apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: event-streamer-streamed
  namespace: ${cell_name}
spec:
  dependsOn:
    - name: ${cell_name}
  interval: 1m
  chart:
    spec:
      chart: se-deployment
      version: 0.4.1
      sourceRef:
        kind: HelmRepository
        name: chartmuseum-steeleye
        namespace: flux-system
      interval: 1m
  values:
    resources:
      requests:
        cpu: 25m
        memory: 100Mi
      limits:
        memory: 100Mi
    livenessProbe:
      enabled: true
      path: /health
      failureThreshold: 3
      initialDelaySeconds: 30
      periodSeconds: 60
      successThreshold: 1
      timeoutSeconds: 1
    service:
      port: 5000
    nodeSelector:
      kubernetes.io/arch: arm64
      role: app
    tolerations:
      - key: "nodepool"
        operator: "Equal"
        value: "${cell_name}-app"
        effect: "NoSchedule"
    serviceMonitor:
      enabled: true
      path: /metrics
      additionalLabels:
        release: prometheus-stack

    owner: sre
    stack: ${cell_name}
    environment: ${environment}
    cloud: ${cloud}
    image:
      repository: ${image_repo}/event-streamer
      tag: 0.0.64
    env:
      SERVER_ADDRESS: ":5000"
      WORKERS: "1"
      CLOUD: ${cloud}
      MAX_POLLING_INTERVAL: "60s"
      POLLING_STEP_INCREASE: "5s"
      KAFKA_PRODUCER_BROKERS: "kafka-cluster-strimzi-cluster-kafka-bootstrap.infra:9092"
      KAFKA_DLQ_TOPIC: "event.streamer.${cell_name}.failed.validations"
      KAFKA_EVENTS_TOPIC: "${events_kafka_topic}"
      QUEUE_URL: "${cell_name}-streamed"
    serviceAccount:
      create: false
      name: event-streamer-service
