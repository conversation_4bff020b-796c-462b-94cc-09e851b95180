import pytest
from unittest.mock import patch, MagicMock
from prefect.engine.signals import SKIP
from swarm_tasks.order.feed.fidessa.controller.fidessa_controller import <PERSON>dessa<PERSON><PERSON>roller, Params

def test_invalid_file_name_skips_gracefully():
    """Test that an invalid file name (without ARC_EVENTS or MARKET_ORDER) 
    raises SKIP instead of FAIL."""
    
    # Setup
    task = FidessaController(name="test-fidessa-controller")
    task.logger = MagicMock()
    params = Params(
        arc_events_file_name_pattern=r"ARC_EVENTS-Exchange_Order-(?P<src_date>\d{8})-\w+.csv",
        source_s3_key_prefix="flows/order-feed-fidessa-controller",
        target_s3_key_prefix="flows/order-feed-fidessa",
    )
    
    # Test with invalid file name
    invalid_file_url = "s3://test.dev.steeleye.co/flows/order-feed-fidessa-controller/INVALID_FILE_NAME.csv"
    
    # Execute and verify
    with pytest.raises(SKIP) as skip_info:
        task.execute(file_url=invalid_file_url, params=params)
    
    # Verify the error message
    assert "File name does not contain either ARC_EVENTS or MARKET_ORDER" in str(skip_info.value)
    
    # Verify that warning was logged
    task.logger.warning.assert_called_once()
    assert "File name does not contain either ARC_EVENTS or MARKET_ORDER" in task.logger.warning.call_args[0][0]

def test_client_error_skips_gracefully():
    """Test that a ClientError raises SKIP instead of FAIL."""
    
    # Setup
    task = FidessaController(name="test-fidessa-controller")
    task.logger = MagicMock()
    params = Params(
        arc_events_file_name_pattern=r"ARC_EVENTS-Exchange_Order-(?P<src_date>\d{8})-\w+.csv",
        source_s3_key_prefix="flows/order-feed-fidessa-controller",
        target_s3_key_prefix="flows/order-feed-fidessa",
    )
    
    # Mock _get_pair_file_key to return a value
    task._get_pair_file_key = MagicMock(return_value="test_key")
    
    # Mock boto3 resource and ClientError
    with patch("boto3.resource") as mock_resource:
        # Set up the mock to raise ClientError when check_pair_file_on_s3 is called
        with patch("swarm_tasks.order.feed.fidessa.controller.fidessa_controller.check_pair_file_on_s3") as mock_check:
            from botocore.exceptions import ClientError
            mock_check.side_effect = ClientError({"Error": {"Code": "NoSuchKey", "Message": "Not found"}}, "GetObject")
            
            # Test with valid file name but ClientError during processing
            valid_file_url = "s3://test.dev.steeleye.co/flows/order-feed-fidessa-controller/ARC_EVENTS-Exchange_Order-20210617-part0001.csv"
            
            # Execute and verify
            with pytest.raises(SKIP) as skip_info:
                task.execute(file_url=valid_file_url, params=params)
            
            # Verify the error message
            assert "Pair file not found" in str(skip_info.value)
            
            # Verify that warning was logged
            task.logger.warning.assert_called_once()
            assert "Pair file not found" in task.logger.warning.call_args[0][0]

def test_general_exception_skips_gracefully():
    """Test that a general exception raises SKIP instead of FAIL."""
    
    # Setup
    task = FidessaController(name="test-fidessa-controller")
    task.logger = MagicMock()
    params = Params(
        arc_events_file_name_pattern=r"ARC_EVENTS-Exchange_Order-(?P<src_date>\d{8})-\w+.csv",
        source_s3_key_prefix="flows/order-feed-fidessa-controller",
        target_s3_key_prefix="flows/order-feed-fidessa",
    )
    
    # Mock _get_pair_file_key to raise an exception
    task._get_pair_file_key = MagicMock(side_effect=ValueError("Test error"))
    
    # Test with valid file name but exception during processing
    valid_file_url = "s3://test.dev.steeleye.co/flows/order-feed-fidessa-controller/ARC_EVENTS-Exchange_Order-20210617-part0001.csv"
    
    # Execute and verify
    with pytest.raises(SKIP) as skip_info:
        task.execute(file_url=valid_file_url, params=params)
    
    # Verify the error message
    assert "Error processing file" in str(skip_info.value)
    
    # Verify that error was logged
    task.logger.error.assert_called_once()
    assert "Error fetching pair file" in task.logger.error.call_args[0][0]

if __name__ == "__main__":
    print("Running tests...")
    try:
        test_invalid_file_name_skips_gracefully()
        print("✅ test_invalid_file_name_skips_gracefully passed")
        
        test_client_error_skips_gracefully()
        print("✅ test_client_error_skips_gracefully passed")
        
        test_general_exception_skips_gracefully()
        print("✅ test_general_exception_skips_gracefully passed")
        
        print("All tests passed!")
    except Exception as e:
        print(f"❌ Test failed: {e}")
