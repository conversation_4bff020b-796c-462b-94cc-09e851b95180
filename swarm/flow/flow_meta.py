from typing import Optional

from pydantic import BaseModel
from pydantic import Field


class FlowMeta(BaseModel):
    s3_bucket: Optional[str] = None
    s3_key: Optional[str] = None
    file_url: Optional[str] = None
    stack: str
    flow_id: str
    bundle_id: str
    flow: dict
    client: dict
    flow_args: Optional[dict] = dict()
    origin: Optional[str] = Field("NA", description="Traces where the job originated.")

    @property
    def env(self) -> str:
        return self.stack.split("-")[0] if self.stack != "srp" else "prod"
