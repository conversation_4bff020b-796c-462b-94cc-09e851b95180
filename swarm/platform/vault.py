import abc
from typing import Any
from typing import Dict
from typing import Union

import boto3
import hvac.exceptions
from pydantic import Field
from pydantic.env_settings import BaseSettings


class VaultConfig(BaseSettings):
    STACK: str = Field(..., description="Stack where the job is being run")
    VAULT_ELASTIC_PATH: Union[str, None] = Field(
        None, description="Vault path under which elastic secrets are present"
    )
    VAULT_URL: str = Field(..., description="Vault mount point")


class AbstractVault(abc.ABC):
    """
    The Singleton abstract class that all our Vault Secrets Stores should inherit from.
    This helps us centralize the instantiation logic.

    Pass reinitialize as True if you want to force instantiate the object
    """

    _instance = None
    VAULT_CONFIG = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None or kwargs.get("reinitialize", False):
            # Creating the instance for the AbstractVault
            cls._instance = super(AbstractVault, cls).__new__(cls)
            cls.VAULT_CONFIG = VaultConfig()
            cls._instance._initialized = False
        return cls._instance

    @abc.abstractmethod
    def get_secret(self, path: str, field: str) -> str:
        """Fetches singular secret.

        :param path: path to the secret in vault, e.g. "/{role}/secrets/test_secret"
        :param field: the key of secret
        :return: the secret value in string format
        """
        raise Exception("Abstract class method")

    @abc.abstractmethod
    def get_secrets(self, path: str, mount_point: str = None) -> dict:
        """
        :param path: vault's path to the secret
        :param mount_point: vault's mount point
        :return: the secret values
        """
        raise Exception("Abstract class method")


class SeVaultKV2(AbstractVault):
    """
    Initialises and fetches secrets from vault kv2 storage engine.
    Requires the secrets provided in `VaultConfig` BaseSettings class
    """

    def __init__(self, **kwargs):
        if self._initialized:
            return

        self._client = hvac.Client(url=self.VAULT_CONFIG.VAULT_URL)

        # Fetch principal credentials
        session = boto3.Session()
        credentials = session.get_credentials()

        # Vault access in AWS Batch Jobs is tied in with AWS IAM Authentication
        self._client.auth.aws.iam_login(
            access_key=credentials.access_key,
            secret_key=credentials.secret_key,
            session_token=credentials.token,
            role=kwargs.get("role") or f"{self.VAULT_CONFIG.STACK}-batch",
        )

        if not self._client.is_authenticated():
            raise hvac.exceptions.Unauthorized(
                "Unable to authenticate to the Vault service"
            )

        # set the attribute to `True` to not initialize again
        self._initialized: bool = True

    def get_secret(self, path: str, field: str) -> Any:
        """Get the value of secret field in the path.

        If no secret is found at the path, raises
        hvac.exceptions.InvalidPath. If field does not exist in the
        secrets at the path, a ValueError is raised
        """
        secrets = self.get_secrets(path)
        if field not in secrets:
            raise ValueError(f"Secret with {field} does not exist at path {path}")
        return secrets[field]

    def get_secrets(self, path: str, mount_point: str = None) -> dict:
        """Get all secrets under the specified path.

        If no secret is found at the path, raises

        hvac.exceptions.InvalidPath
        """
        mount_point = self.VAULT_CONFIG.STACK if mount_point is None else mount_point
        response = self._client.secrets.kv.v2.read_secret_version(
            path=path, mount_point=mount_point
        )
        return response["data"]["data"]


class _SeVaultLocalSecretsStore(AbstractVault):
    """This class implements the fetching of secrets from the data dictionary
    Useful for local testing.

    Accepts a dictionary of all secret paths with values
    """

    def __init__(self, data: Dict[Any, Any]):
        self._data = data

    def get_secret(self, path: str, field: str) -> Any:
        """Get the value of secret `field` in `path`"""
        return self._data[path][field]

    def get_secrets(self, path: str, mount_point: str = None) -> Any:
        """Get all secrets in the path."""
        return self._data[path]


def fetch_elastic_secrets_from_vault() -> dict:
    vault_obj = SeVaultKV2()
    elastic_secrets = vault_obj.get_secrets(
        path=vault_obj.VAULT_CONFIG.VAULT_ELASTIC_PATH
    )

    # We need to add this explicitly as it's not part of the vault secret
    # but is a mandatory field in our pydantic Resource model
    elastic_secrets["id"] = "tenant-data"

    return elastic_secrets
