import abc

import boto3


class AbsSecretsStore(abc.ABC):
    """
    The abstract class that all our Secrets Stores should inherit from.

    Singleton containing all secrets needed for a given Flow. This class was created to ensure
    that a given secret is only retrieved once from a given secret store. Any Task can override its constructor
    to instantiate/access the AbsSecretsStore with the necessary secrets:

    Example:
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            SeSecretsStore("pinafore.dev.steeleye.co", "test.dev.steeleye.co")

    The constructor override's behavior is different on these two scenarios:
    1 - First time the AbsSecretsStore singleton is being instantiated:
        - Instantiate the singleton and fetch the secrets passed as *args
    2 - AbsSecretsStore is being instantiated, but there is already an existing instance:
        - Iterate over each of secrets passed as *args, and if they have not been fetched before,
        retrieve them from a given secret store and add them to the existing singleton instance
        - Return existing instance of the singleton
    """

    _instance = None
    secrets = {}

    def __new__(cls, *secret_names):
        if cls._instance is None:
            # Creating the instance for the AbsSecretsStore
            cls._instance = super(AbsSecretsStore, cls).__new__(cls)
            cls.secrets = {
                name: cls._instance.get_secret(name) for name in secret_names
            }
        # Instance of AbsSecretsStore already exists
        else:
            for secret_name in secret_names:
                if secret_name not in cls._instance.secrets:
                    cls._instance.secrets.update(
                        {name: cls._instance.get_secret(name) for name in secret_names}
                    )

        return cls._instance

    def get_secret(self, name: str, decrypt: bool = True, force: bool = False) -> str:
        """
        :param force: don't rely on cached values
        :param name: the string reference to the secret
        :param decrypt: bool to decrypt the secret in the store
        :return: the secret value in string format
        """
        if name in self.secrets and not force:
            return self.secrets[name]
        return self._fetch_secret(name, decrypt=decrypt)

    @abc.abstractmethod
    def _fetch_secret(self, name: str, decrypt: bool = True) -> str:
        raise Exception("Abstract class method")


class SeSecretsStore(AbsSecretsStore):
    """
    This class implements the fetching of secrets from AWS SSM
    """

    def __new__(cls, *secret_names):
        cls.client = boto3.client("ssm")
        return super().__new__(cls, *secret_names)

    def _fetch_secret(self, name: str, decrypt: bool = True) -> str:
        """
        :param name: the string reference to the secret
        :param decrypt: bool to decrypt the secret in the store
        :return: the secret value in string format
        """
        parameter = self.client.get_parameter(Name=name, WithDecryption=decrypt)
        secret = parameter["Parameter"]["Value"]
        self.secrets[name] = secret
        return secret


class _SeLocalSecretsStore(AbsSecretsStore):
    """
    This class implements the fetching of secrets from the data dictionary
    Useful for local testing
    """

    data = {}

    def __new__(cls, *secret_names):
        return super().__new__(cls, *secret_names)

    def _fetch_secret(self, name: str, decrypt: bool = True) -> str:
        """
        :param name: the string reference to the secret
        :param decrypt: bool to decrypt the secret in the store
        :return: the secret value in string format
        """
        secret = self.data[name]
        self.secrets[name] = secret
        return secret
