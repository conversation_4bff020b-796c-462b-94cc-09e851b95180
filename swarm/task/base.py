from abc import abstractmethod
from typing import Any
from typing import Dict
from typing import List
from typing import Optional

from prefect import Task
from prefect.engine import signals

from swarm.conf import SwarmConnections
from swarm.schema.base import AbstractComponent
from swarm.schema.flow.bundle.components import ResourceConfig
from swarm.schema.task.components import PrefectParams
from swarm.schema.task.static import ResultCategory
from swarm.task.auditor import Auditor
from swarm.task.utilities import pre_run


class BaseParams(AbstractComponent):
    pass  # TODO: do we need to add default fields here?


class BaseResources(AbstractComponent):
    pass


class BaseTask(Task):
    params_class = BaseParams
    resources_class = BaseResources
    result_category: ResultCategory = ResultCategory.NA

    def __init__(
        self,
        name: str,
        params: Optional[BaseParams] = None,
        params_list: Optional[List[BaseParams]] = None,
        config: PrefectParams = None,
        resources: Optional[BaseResources] = None,
        resource_configs: Optional[Dict[str, ResourceConfig]] = None,
        platform: Optional[bool] = False,
    ):
        prefect_config = {} if config is None else config.dict()
        # meta derives from abstract component lineage and not a prefect param
        prefect_config.pop("meta", None)
        prefect_config["name"] = name

        if params and params_list:
            raise ValueError("params and params_list are mutually exclusive.")

        super().__init__(**prefect_config)
        self._auditor: Optional[Auditor] = None
        self._params: Optional[BaseParams] = params
        self._params_list: Optional[List[BaseParams]] = params_list
        self._resources: resources = resources
        self._resource_configs = resource_configs
        self._clients: Optional[SwarmConnections] = None
        self._platform = platform

        if not params and not params_list:
            self._params = self.params_class()

    @property
    def auditor(self) -> Auditor:
        return self._auditor

    @property
    def params(self) -> BaseParams:
        return self._params

    @property
    def params_list(self) -> List[BaseParams]:
        return self._params_list

    @property
    def resource_config(self) -> Optional[Dict[str, ResourceConfig]]:
        return self._resource_configs

    @property
    def clients(self) -> SwarmConnections:
        return self._clients

    @clients.setter
    def clients(self, clients: SwarmConnections):
        self._clients = clients

    @property
    def resources(self) -> Optional[BaseResources]:
        return self._resources

    @pre_run
    def run(self, **kwargs) -> Any:
        try:
            response = self.execute(
                params=self.params, resources=self.resources, **kwargs
            )
        except (signals.SKIP, signals.RETRY, signals.ENDRUN):
            raise
        except Exception as e:
            raise e
        return response

    @abstractmethod
    def execute(
        self,
        params: Optional[BaseParams] = None,
        resources: Optional[BaseResources] = None,
        **kwargs,
    ) -> Any:
        raise NotImplementedError
