from typing import List
from typing import Optional
from typing import Union

from se_elasticsearch.repository import ResourceConfig as SeResourceConfig
from se_elasticsearch.repository.static import MetaPrefix
from typing_extensions import Literal

from swarm.schema.base import AbstractComponent


class ResourceConfig(SeResourceConfig):
    meta_prefix: str = MetaPrefix.AMPERSAND


class Resource(AbstractComponent):
    config: Optional[Union[ResourceConfig, List[ResourceConfig]]]
    name: str
    type: Literal["ELASTICSEARCH", "SFTP"]
