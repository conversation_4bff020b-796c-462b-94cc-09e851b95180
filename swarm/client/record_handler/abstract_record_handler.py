import logging
import re
import uuid
from abc import ABC
from abc import abstractmethod
from collections import namedtuple
from hashlib import sha512
from typing import Dict
from typing import List
from typing import Optional
from typing import Union

import addict
import pandas as pd
from elastic_transport import ObjectApiResponse
from indict import Indict
from schema_sdk.steeleye_model.gamma.helpers import nested_read

from swarm.client.meta_helper import Meta
from swarm.schema.reader import Schema<PERSON>eader
from swarm.schema.static import DocStatus
from swarm.schema.static import STATUS_MAP
from swarm.schema.static import WriteAction
from swarm.schema.utils import sanitize
from swarm.schema.utils import validate_doc_id

log = logging.getLogger(__name__)

RecordResponse = namedtuple(
    "RecordResponse", ["status", "index", "id", "key", "version", "timestamp"]
)
META_PREFIX_FILTER = ["&", "_meta"]


class RecordHandlerError(Exception):
    def __init__(self, message: str):
        super().__init__(message)


class AbstractRecordHandler(ABC):

    """
    Data Store class to Save/Search/Delete records.

    Provides straight dict I/O into the ElasticSearch store, ensuring
    versioning and model consistency.

    You must instantiate your own ``Elasticsearch`` client and provide it as
    parameter during the RecordHandler instantiation. If a ``tenant`` is given
    to the class, it will be used in subsequent method calls, otherwise the
    user will have to provide it for each method call::

        es = Elasticsearch()
        rh = RecordHandler(es, tenant="foobar")
        rh.get(index="foo", doc_id="abc-123")

    """

    def __init__(
        self,
        client,
        tenant: Optional[str] = None,
    ):
        self.client = client
        self._meta = Meta(prefix=self.get_meta_prefix())
        self.tenant = tenant

        log.info(f"Successfully instantiated the {self.__class__.__name__} class")

    @property
    def meta(self):
        return self._meta

    @abstractmethod
    def get_meta_prefix(self) -> str:
        raise NotImplementedError

    def get(
        self,
        index: str,
        doc_id: str,
        version: Optional[int] = None,
        source_only: bool = True,
        model: Optional[str] = None,
    ) -> dict:

        validate_doc_id(doc_id)

        params = dict(index=index, id=doc_id)
        self.set_request_doc_type(params=params, model=model)

        if version is not None:
            params["version"] = version

        result = self.client.get(**params)
        return result.get("_source") if source_only else result

    def get_by_meta_version(
        self,
        index: str,
        doc_id: str,
        meta_version: int,
        source_only: bool = True,
        model: Optional[str] = None,
    ) -> dict:

        validate_doc_id(doc_id)

        result = self.client.search(
            index=index,
            doc_type=self.get_request_doc_type(model=model),
            body=dict(
                query=dict(
                    bool=dict(
                        must=[
                            dict(term={self.meta.id: doc_id}),
                            dict(term={self.meta.version: meta_version}),
                        ]
                    )
                )
            ),
        )
        if result["hits"]["total"] == 0:
            raise self.get_es_not_found_error()()

        hit = result["hits"]["hits"][0]
        return hit.get("_source") if source_only else hit

    @abstractmethod
    def set_request_doc_type(self, params: Dict[str, str], model: str):
        raise NotImplementedError

    @abstractmethod
    def get_request_doc_type(self, model: Optional[str]) -> str:
        raise NotImplementedError

    def search(self, query: dict, index: str, **kwargs) -> addict.Dict:

        response = self.client.search(index=index, body=query, **kwargs)

        response = (
            response.body if isinstance(response, ObjectApiResponse) else response
        )

        return addict.Dict(response)

    def create(
        self,
        doc: Union[dict, pd.Series],
        alias: str,
        model: str,
        retain_meta_version: bool = False,
        user: str = "swarm",
        refresh: bool = True,
        parent: str = None,
    ) -> RecordResponse:

        doc = self._add_meta(
            doc=doc,
            action=WriteAction.CREATE,
            alias=alias,
            model=model,
            user=user,
            retain_meta_version=retain_meta_version,
        )

        params = dict(refresh="true" if refresh else "false")
        if parent:
            params["parent"] = parent

        index_for = (
            SchemaReader.index_for(model_name=model, doc=doc, tenant=self.tenant)
            if self.tenant
            else alias
        )

        response = self.client.create(
            index=index_for,
            doc_type=self.get_request_doc_type(model=model),
            id=doc[self.meta.id],
            body=Indict(obj=doc).remove_empty().unflatten().obj,
            params=params,
        )

        if response.get("result") != "created":
            raise RecordHandlerError(
                f"expected result to be 'created' but was " f"{response.get('result')}"
            )

        return RecordResponse(
            status=STATUS_MAP.get(WriteAction.CREATE),
            index=response.get("_index"),
            id=doc[self.meta.id],
            key=doc[self.meta.key],
            version=response.get("_version"),
            timestamp=doc.get(self.meta.timestamp),
        )

    def update(
        self,
        doc: Union[dict, pd.Series],
        alias: str,
        model: str,
        version: Optional[int] = None,
        retain_meta_version: bool = False,
        perform_archive: bool = True,
        user: str = "swarm",
        refresh: bool = True,
        parent: str = None,
        merge_previous_version: bool = True,
        strip_meta_values_only: bool = False,
    ):

        doc = self._add_meta(
            doc=doc,
            action=WriteAction.UPDATE,
            alias=alias,
            model=model,
            user=user,
            retain_meta_version=retain_meta_version,
        )

        original_index, original_doc, original_es_version = self._get_original(
            alias=alias, model=model, doc_id=doc[self.meta.id], version=version
        )

        # if the original record does not contain a meta hash prop...
        # we can't really attempt to do the comparison here...
        if (
            self.meta.hash in original_doc
            and original_doc[self.meta.hash] == doc[self.meta.hash]
        ):
            return RecordResponse(
                status=DocStatus.DUPLICATE,
                index=original_index,
                id=original_doc[self.meta.id],
                key=original_doc[self.meta.key],
                version=original_doc[self.meta.version],
                timestamp=original_doc.get(self.meta.timestamp),
            )

        if not retain_meta_version:
            doc[self.meta.version] = self._get_incremented_version(
                original_doc=original_doc, original_es_version=original_es_version
            )

        doc[self.meta.ancestor] = original_doc[self.meta.key]

        params = dict(refresh="true" if refresh else "false")

        if version is not None:
            # something to consider...
            # do we want to use the `original_es_version` here, or just
            # let ES do its thing...
            # Going to let ES do its thing for now and NOT specify a version param
            params["version"] = version

        if parent:
            params["parent"] = parent

        update_response = (
            self.client.update(
                index=original_index,
                doc_type=self.get_request_doc_type(model=model),
                id=doc[self.meta.id],
                body=dict(
                    doc=Indict(obj=doc)
                    .remove_empty(
                        match_prefixes=META_PREFIX_FILTER
                        if strip_meta_values_only
                        else None
                    )
                    .unflatten()
                    .obj
                ),
                params=params,
            )
            if merge_previous_version
            else self.client.index(
                index=original_index,
                doc_type=self.get_request_doc_type(model=model),
                id=doc[self.meta.id],
                body=Indict(obj=doc)
                .remove_empty(
                    match_prefixes=META_PREFIX_FILTER
                    if strip_meta_values_only
                    else None
                )
                .unflatten()
                .obj,
                params=params,
            )
        )

        if update_response.get("result") != "updated":
            raise RecordHandlerError(
                f"expected update result to be 'updated' but was "
                f"{update_response.get('result')}"
            )

        if perform_archive:
            self._do_archival(
                original_index=original_index,
                model=model,
                original_doc=original_doc,
                params=params,
                doc_status=DocStatus.EXPIRED,
            )

        return RecordResponse(
            status=STATUS_MAP.get(WriteAction.UPDATE),
            index=update_response.get("_index"),
            id=doc[self.meta.id],
            key=doc[self.meta.key],
            version=update_response.get("_version"),
            timestamp=doc.get(self.meta.timestamp),
        )

    def update_in_place(
        self,
        doc: Union[dict, pd.Series],
        alias: str,
        model: str,
        doc_id: str,
        version: Optional[int] = None,
        refresh: bool = True,
        parent: str = None,
        strip_meta_values_only: bool = False,
        to_flatten: bool = True,
    ):

        validate_doc_id(doc_id)

        original_index, original_doc, original_es_version = self._get_original(
            alias=alias, model=model, doc_id=doc_id, version=version
        )

        doc[self.meta.version] = self._get_incremented_version(
            original_doc=original_doc, original_es_version=original_es_version
        )

        doc = pd.Series(Indict(obj=doc).flatten().obj) if to_flatten else doc
        params = dict(refresh="true" if refresh else "false")

        if version is not None:
            params["version"] = version

        if parent:
            params["parent"] = parent

        update_response = self.client.update(
            index=original_index,
            doc_type=self.get_request_doc_type(model=model),
            id=doc_id,
            body=dict(
                doc=Indict(obj=doc)
                .remove_empty(
                    match_prefixes=META_PREFIX_FILTER
                    if strip_meta_values_only
                    else None
                )
                .unflatten()
                .obj
            ),
            params=params,
        )

        if update_response.get("result") != "updated":
            raise RecordHandlerError(
                f"expected update result to be 'updated' but was "
                f"{update_response.get('result')}"
            )

        return RecordResponse(
            status=STATUS_MAP.get(WriteAction.UPDATE),
            index=update_response.get("_index"),
            id=doc_id,
            key=original_doc[self.meta.key],
            version=update_response.get("_version"),
            timestamp=doc.get(self.meta.timestamp),
        )

    def delete(
        self,
        alias: str,
        model: str,
        doc_id: str,
        version: Optional[int] = None,
        perform_archive: bool = True,
        refresh: bool = True,
        parent: str = None,
    ):
        validate_doc_id(doc_id)
        original_index, original_doc, original_es_version = self._get_original(
            alias=alias, model=model, doc_id=doc_id, version=version
        )

        params = dict(
            refresh="true" if refresh else "false",
            version=version if version is not None else original_es_version,
        )

        if parent:
            params["parent"] = parent

        delete_response = self.client.delete(
            index=original_index,
            doc_type=self.get_request_doc_type(model=model),
            id=doc_id,
            params=params,
        )

        if delete_response.get("result") != "deleted":
            raise RecordHandlerError(
                f"expected update result to be 'deleted' but was "
                f"{delete_response.get('result')}"
            )

        if perform_archive:
            self._do_archival(
                original_index=original_index,
                model=model,
                original_doc=original_doc,
                params=params,
                doc_status=DocStatus.DELETED,
            )

        return RecordResponse(
            status=STATUS_MAP.get(WriteAction.DELETE),
            index=original_index,
            id=doc_id,
            key=original_doc[self.meta.key],
            version=delete_response.get("_version"),
            timestamp=original_doc.get(self.meta.timestamp),
        )

    def _get_original(
        self, alias: str, model: str, doc_id: str, version: Optional[int] = None
    ) -> [str, pd.Series]:

        try:
            query_filters = [
                {"term": {"_id": doc_id}},
                {"term": {self.meta.model: model}},
            ]

            if version is not None:
                query_filters.append({"term": {self.meta.version: version}})

            response = self.client.search(
                index=alias,
                body={
                    "sort": {self.meta.timestamp: {"order": "desc"}},
                    "query": {"bool": {"filter": query_filters}},
                    "version": True,
                },
                size=1,
            )

            hits = response["hits"]["hits"]
            if len(hits) == 0:
                raise RecordHandlerError(f"unable to find {model}:{doc_id} for editing")

            # we are explicitly checking if version is None here
            # because some legacy records will not have an `&version`
            if version is not None and hits[0].get("_version") != version:
                log.warning(
                    "Record %s (%s) meta version: %s differs from ES _version",
                    model,
                    doc_id,
                    version,
                )

            result = hits[0]
        except self.get_es_not_found_error():
            raise RecordHandlerError(f"unable to find {model}:{doc_id} for editing")

        original_index = result.get("_index")
        original_doc = pd.Series(result.get("_source"))
        # for cases where the Version param is None
        original_es_version = result.get("_version") or result.get(self.meta.version)

        # expand meta column dict if needed
        if self.meta.prefix.replace(".", "") in original_doc.index:
            meta = pd.Series(
                original_doc[self.meta.prefix.replace(".", "")]
            ).add_prefix(f"{self.meta.prefix}")

            original_doc = pd.concat([original_doc, meta]).drop(
                index=[self.meta.prefix.replace(".", "")]
            )

        return original_index, original_doc, original_es_version

    def _do_archival(
        self,
        original_index: str,
        model: str,
        original_doc: Union[dict, pd.Series],
        params: dict,
        doc_status: str,
    ):
        # remove version pre archive
        params.pop("version", None)

        # set meta status of original doc to expired
        original_doc[self.meta.status] = doc_status

        # set meta expiry of original doc to now
        # noinspection PyUnresolvedReferences
        original_doc[self.meta.expiry] = pd.Timestamp.utcnow().value // int(1e6)

        archive_response = self.client.index(
            index=original_index,
            doc_type=self.get_request_doc_type(model=model),
            body=Indict(obj=original_doc).remove_empty().unflatten().obj,
            params=params,
        )

        if archive_response.get("result") != "created":
            raise RecordHandlerError(
                f"expected archive result to be 'created' but was "
                f"{archive_response.get('result')}"
            )

    def _add_meta(
        self,
        doc: Dict,
        action: str,
        alias: str,
        model: str,
        retain_meta_version: bool = False,
        user: str = None,
    ) -> Dict:

        # apply hash if applicable
        if action != WriteAction.DELETE:

            hash_properties = self.get_hash_props(model=model)

            if not hash_properties:
                raise RecordHandlerError(
                    f"missing hash_properties for model "
                    f"{model} in write alias {alias}"
                )

            doc[self.meta.hash] = Indict(obj=doc).hash(fields=hash_properties)

        if retain_meta_version and self.meta.version not in doc:
            raise RecordHandlerError(
                f"retain_meta_version option requires {self.meta.version}"
            )

        # define model
        doc[self.meta.model] = model

        # define user
        if user:
            doc[self.meta.user] = user

        # define version
        if action == WriteAction.CREATE and not retain_meta_version:
            doc[self.meta.version] = 1

        # define timestamp
        # noinspection PyUnresolvedReferences
        doc[self.meta.timestamp] = pd.Timestamp.utcnow().value // int(1e6)

        # define id if id prop map provided
        if self.meta.id not in doc or doc[self.meta.id] is None:

            id_properties = self.get_id_props(model=model)
            should_hash_id_for_model = self.model_id_must_be_hash(model=model)

            if id_properties:

                flattened_doc = Indict(obj=doc).flatten().obj
                doc[self.meta.id] = ":".join(
                    sanitize(flattened_doc.get(p)) for p in id_properties
                )
                if should_hash_id_for_model or (
                    len(doc[self.meta.id].encode("utf-8")) > 512
                ):
                    # ST-217: Max limit of ES for id field is 512 bytes
                    doc[self.meta.id] = sha512(
                        doc[self.meta.id].encode("utf-8")
                    ).hexdigest()

            else:
                log.debug(f"missing id_properties for model {model}; using uuid4")
                doc[self.meta.id] = str(uuid.uuid4())

        # assign doc meta unique props if not defined.
        if self.meta.unique_props not in doc or doc[self.meta.unique_props] is None:

            log.debug("Populating unique props in doc.")
            unique_props = self._get_unique_props(doc=doc, model=model)
            if unique_props:
                doc[self.meta.unique_props] = unique_props

        # define key
        key_cols = [self.meta.model, self.meta.id, self.meta.timestamp]
        key_vals = [str(doc.get(k)) for k in key_cols]
        doc[self.meta.key] = ":".join(key_vals)
        doc.pop(self.meta.status, None)
        doc.pop(self.meta.expiry, None)

        return doc

    def _get_unique_props(self, doc: Dict, model: str) -> Optional[List[str]]:
        """
        Returns list of unique props values

        :param doc: Record flatten or unflatten
        :param model: Model name
        :return: List of unique props values.
        """
        unique_fields = self.get_unique_fields(model=model)

        if not unique_fields:
            return None

        unique_props = set()

        for unique_field in unique_fields:
            # in case doc is unflatten
            values = nested_read(doc, *unique_field.split("."))

            if values:
                if isinstance(values, str):
                    unique_props.add(values)
                elif isinstance(values, list):
                    unique_props.update(set(values))
            # in case do is flatten
            elif unique_field in doc:
                field_value = doc[unique_field]
                if isinstance(field_value, list):
                    unique_props.update([str(v) for v in field_value if v])
                elif field_value:
                    unique_props.add(str(field_value))
            else:
                nested_fields = [
                    i for i in doc.keys() if re.match(rf"^{unique_field}.\d+$", i)
                ]
                field_value = [doc[field] for field in nested_fields if doc[field]]
                unique_props.update(field_value)

        # we do NOT want to end up with `&uniqueProps: []` when we write records
        return sorted(unique_props) if any(unique_props) else None

    def _get_incremented_version(
        self, original_doc: pd.DataFrame, original_es_version: int
    ) -> int:
        """
        Return the incremented version of a given ES document.
        If the input `original_doc` does not have a version, it is considered as version = 1

        :param original_doc: Original document retrieved from ES
        :param original_es_version: Original `_version` of the ES document
        :return: Incremented version of the document
        """

        original_version = (
            original_doc.get(self.meta.version) or original_es_version or 1
        )
        return original_version + 1

    def get_hash_props(self, model: str) -> List[str]:
        return SchemaReader.hash_props_for(model_name=model)

    def get_id_props(self, model: str) -> List[str]:
        return SchemaReader.id_props_for(model_name=model)

    def get_unique_fields(self, model: str) -> List[str]:
        return SchemaReader.unique_fields_props_for(model_name=model)

    def model_id_must_be_hash(self, model: str) -> bool:
        return SchemaReader.should_hash_id_for(model_name=model)

    @abstractmethod
    def get_es_not_found_error(self, *args, **kwargs):
        """NOT IMPLEMENTED"""
