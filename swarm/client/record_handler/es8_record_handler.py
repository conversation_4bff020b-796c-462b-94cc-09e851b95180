import logging
from abc import ABC
from typing import Dict
from typing import List
from typing import Optional
from typing import Union

import elasticsearch8
import pandas as pd
from indict import Indict
from se_elastic_schema.models import find_model
from se_elasticsearch.repository.static import MetaPrefix

from swarm.client.record_handler.abstract_record_handler import AbstractR<PERSON><PERSON>Handler
from swarm.client.record_handler.abstract_record_handler import META_PREFIX_FILTER
from swarm.client.record_handler.abstract_record_handler import RecordHandlerError
from swarm.client.record_handler.abstract_record_handler import RecordResponse
from swarm.schema.static import DocStatus
from swarm.schema.static import STATUS_MAP
from swarm.schema.static import WriteAction
from swarm.schema.utils import validate_doc_id


log = logging.getLogger(__name__)


class Es8RecordHandler(AbstractRecordHandler, ABC):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def get_meta_prefix(self) -> str:
        return MetaPrefix.AMPERSAND

    def get(
        self,
        index: str,
        doc_id: str,
        version: Optional[int] = None,
        source_only: bool = True,
        model: Optional[str] = None,
    ) -> dict:

        return super().get(
            index=index, doc_id=doc_id, version=version, source_only=source_only
        )

    def set_request_doc_type(self, params: Dict[str, str], model: str):
        """NOT IMPLEMENTED"""

    def get_request_doc_type(self, model: Optional[str] = None):
        """NOT IMPLEMENTED"""

    def get_by_meta_version(
        self,
        index: str,
        doc_id: str,
        meta_version: int,
        source_only: bool = True,
        model: Optional[str] = None,
    ) -> dict:

        validate_doc_id(doc_id)

        result = self.client.search(
            index=index,
            body=dict(
                query=dict(
                    bool=dict(
                        must=[
                            dict(term={self.meta.id: doc_id}),
                            dict(term={self.meta.version: meta_version}),
                        ]
                    )
                )
            ),
        )
        if result["hits"]["total"].get("value") == 0:
            raise self.get_es_not_found_error()(
                message=f"Document `{doc_id}` was not found for version `{meta_version}`",
                body=result.body,
                meta=result.meta,
            )

        hit = result["hits"]["hits"][0]
        return hit.get("_source") if source_only else hit

    def get_hash_props(self, model: str) -> List[str]:
        return find_model(model).hash_props()

    def get_id_props(self, model: str) -> List[str]:
        return find_model(model).id_props()

    def get_unique_fields(self, model: str) -> List[str]:
        return find_model(model).unique_fields()

    def model_id_must_be_hash(self, model: str) -> bool:
        return find_model(model).convert_meta_id_to_sha512()

    def create(
        self,
        doc: Union[dict, pd.Series],
        alias: str,
        model: str,
        retain_meta_version: bool = False,
        user: str = "swarm",
        refresh: bool = True,
        parent: str = None,
    ) -> RecordResponse:

        doc = self._add_meta(
            doc=doc,
            action=WriteAction.CREATE,
            alias=alias,
            model=model,
            user=user,
            retain_meta_version=retain_meta_version,
        )

        params = dict(refresh="true" if refresh else "false")
        index_for = (
            find_model(model).get_elastic_index_alias(tenant=self.tenant)
            if self.tenant
            else alias
        )

        response = self.client.create(
            index=index_for,
            id=doc[self.meta.id],
            body=Indict(obj=doc).remove_empty().unflatten().obj,
            params=params,
        )

        if response.get("result") != "created":
            raise RecordHandlerError(
                f"expected result to be 'created' but was " f"{response.get('result')}"
            )

        return RecordResponse(
            status=STATUS_MAP.get(WriteAction.CREATE),
            index=response.get("_index"),
            id=doc[self.meta.id],
            key=doc[self.meta.key],
            version=response.get("_version"),
            timestamp=doc.get(self.meta.timestamp),
        )

    def update(
        self,
        doc: Union[dict, pd.Series],
        alias: str,
        model: str,
        version: Optional[int] = None,
        retain_meta_version: bool = False,
        perform_archive: bool = True,
        user: str = "swarm",
        refresh: bool = True,
        parent: str = None,
        merge_previous_version: bool = True,
        strip_meta_values_only: bool = False,
    ):

        doc = self._add_meta(
            doc=doc,
            action=WriteAction.UPDATE,
            alias=alias,
            model=model,
            user=user,
            retain_meta_version=retain_meta_version,
        )

        original_index, original_doc, original_es_version = self._get_original(
            alias=alias, model=model, doc_id=doc[self.meta.id], version=version
        )

        # if the original record does not contain a meta hash prop...
        # we can't really attempt to do the comparison here...
        if (
            self.meta.hash in original_doc
            and original_doc[self.meta.hash] == doc[self.meta.hash]
        ):
            return RecordResponse(
                status=DocStatus.DUPLICATE,
                index=original_index,
                id=original_doc[self.meta.id],
                key=original_doc[self.meta.key],
                version=original_doc[self.meta.version],
                timestamp=original_doc.get(self.meta.timestamp),
            )

        if not retain_meta_version:
            doc[self.meta.version] = self._get_incremented_version(
                original_doc=original_doc, original_es_version=original_es_version
            )

        doc[self.meta.ancestor] = original_doc[self.meta.key]

        params = dict(refresh="true" if refresh else "false")

        update_response = (
            self.client.update(
                index=original_index,
                id=doc[self.meta.id],
                body=dict(
                    doc=Indict(obj=doc)
                    .remove_empty(
                        match_prefixes=META_PREFIX_FILTER
                        if strip_meta_values_only
                        else None
                    )
                    .unflatten()
                    .obj
                ),
                params=params,
            )
            if merge_previous_version
            else self.client.index(
                index=original_index,
                id=doc[self.meta.id],
                body=Indict(obj=doc)
                .remove_empty(
                    match_prefixes=META_PREFIX_FILTER
                    if strip_meta_values_only
                    else None
                )
                .unflatten()
                .obj,
                params=params,
            )
        )

        if update_response.get("result") != "updated":
            raise RecordHandlerError(
                f"expected update result to be 'updated' but was "
                f"{update_response.get('result')}"
            )

        if perform_archive:
            self._do_archival(
                original_index=original_index,
                model=model,
                original_doc=original_doc,
                params=params,
                doc_status=DocStatus.EXPIRED,
            )

        return RecordResponse(
            status=STATUS_MAP.get(WriteAction.UPDATE),
            index=update_response.get("_index"),
            id=doc[self.meta.id],
            key=doc[self.meta.key],
            version=update_response.get("_version"),
            timestamp=doc.get(self.meta.timestamp),
        )

    def get_es_not_found_error(self, *args, **kwargs):

        return elasticsearch8.exceptions.NotFoundError

    def _do_archival(
        self,
        original_index: str,
        model: str,
        original_doc: Union[dict, pd.Series],
        params: dict,
        doc_status: str,
    ):
        # remove version pre archive
        params.pop("version", None)

        # set meta status of original doc to expired
        original_doc[self.meta.status] = doc_status

        # set meta expiry of original doc to now
        # noinspection PyUnresolvedReferences
        original_doc[self.meta.expiry] = pd.Timestamp.utcnow().value // int(1e6)

        archive_response = self.client.index(
            index=original_index,
            body=Indict(obj=original_doc).remove_empty().unflatten().obj,
            params=params,
        )

        if archive_response.get("result") != "created":
            raise RecordHandlerError(
                f"expected archive result to be 'created' but was "
                f"{archive_response.get('result')}"
            )

    def update_in_place(
        self,
        doc: Union[dict, pd.Series],
        alias: str,
        model: str,
        doc_id: str,
        version: Optional[int] = None,
        refresh: bool = True,
        parent: str = None,
        strip_meta_values_only: bool = False,
        to_flatten: bool = True,
    ):

        validate_doc_id(doc_id)

        original_index, original_doc, original_es_version = self._get_original(
            alias=alias, model=model, doc_id=doc_id, version=version
        )

        doc[self.meta.version] = self._get_incremented_version(
            original_doc=original_doc, original_es_version=original_es_version
        )

        doc = pd.Series(Indict(obj=doc).flatten().obj) if to_flatten else doc
        params = dict(refresh="true" if refresh else "false")

        update_response = self.client.update(
            index=original_index,
            id=doc_id,
            body=dict(
                doc=Indict(obj=doc)
                .remove_empty(
                    match_prefixes=META_PREFIX_FILTER
                    if strip_meta_values_only
                    else None
                )
                .unflatten()
                .obj
            ),
            params=params,
        )

        if update_response.get("result") != "updated":
            raise RecordHandlerError(
                f"expected update result to be 'updated' but was "
                f"{update_response.get('result')}"
            )

        return RecordResponse(
            status=STATUS_MAP.get(WriteAction.UPDATE),
            index=update_response.get("_index"),
            id=doc_id,
            key=original_doc[self.meta.key],
            version=update_response.get("_version"),
            timestamp=doc.get(self.meta.timestamp),
        )

    def delete(
        self,
        alias: str,
        model: str,
        doc_id: str,
        version: Optional[int] = None,
        perform_archive: bool = True,
        refresh: bool = True,
        parent: str = None,
    ):
        validate_doc_id(doc_id)
        original_index, original_doc, original_es_version = self._get_original(
            alias=alias, model=model, doc_id=doc_id, version=version
        )

        params = dict(
            refresh="true" if refresh else "false",
        )

        delete_response = self.client.delete(
            index=original_index,
            id=doc_id,
            params=params,
        )

        if delete_response.get("result") != "deleted":
            raise RecordHandlerError(
                f"expected update result to be 'deleted' but was "
                f"{delete_response.get('result')}"
            )

        if perform_archive:
            self._do_archival(
                original_index=original_index,
                model=model,
                original_doc=original_doc,
                params=params,
                doc_status=DocStatus.DELETED,
            )

        return RecordResponse(
            status=STATUS_MAP.get(WriteAction.DELETE),
            index=original_index,
            id=doc_id,
            key=original_doc[self.meta.key],
            version=delete_response.get("_version"),
            timestamp=original_doc.get(self.meta.timestamp),
        )
