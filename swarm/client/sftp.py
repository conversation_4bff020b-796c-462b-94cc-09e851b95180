import logging
import os
import random
import time
from pathlib import Path
from tempfile import mkstemp
from typing import Union

import paramiko
from paramiko import SFTP<PERSON>lient
from paramiko import SSHException
from paramiko.proxy import ProxyCommand
from paramiko.transport import Transport as ParamikoTransport
from pysftp import CnOpts
from pysftp import Connection

from swarm.client.base import ClientBase
from swarm.schema.flow.bundle.components import ResourceConfig

logger = logging.getLogger(__name__)

sftp_proxy_host = os.environ.get("SFTP_PROXY_HOST")
sftp_proxy_port = os.environ.get("SFTP_PROXY_PORT")


class SftpClient(ClientBase):
    def __init__(self, config: ResourceConfig):
        self._config = config
        self.conn_opts = CnOpts()
        self.conn_opts.hostkeys = None

    def connect(self, retry_count=3) -> Union[Connection, SFTPClient]:

        private_key_path = (
            self._get_key_from_store(self._config.private_key_param)
            if self._config.private_key_param
            else None
        )
        retry_count = abs(int(retry_count))  # make sure it's valid
        counter = 1
        backoff_delay = 10
        retry = True
        while retry:
            try:
                if sftp_proxy_host and sftp_proxy_port:
                    logger.info(
                        "Using SFTP proxy. ProxyHost %s ProxyPort %s",
                        sftp_proxy_host,
                        sftp_proxy_port,
                    )
                    proxy_command = (
                        f"socat - PROXY:{sftp_proxy_host}:%s:%s,proxyport={sftp_proxy_port}"
                        % (self._config.host, self._config.port)
                    )
                    proxy = ProxyCommand(proxy_command)
                    transport = ParamikoTransport(sock=proxy)
                    transport.connect(
                        username=self._config.username,
                        password=None if private_key_path else self._config.password,
                        pkey=paramiko.RSAKey.from_private_key_file(private_key_path)
                        if private_key_path
                        else None,
                    )
                    return paramiko.SFTPClient.from_transport(transport)

                return Connection(
                    host=self._config.host,
                    username=self._config.username,
                    # if we have a private key, don't use password
                    # preferred authentication is password if present
                    password=None if private_key_path else self._config.password,
                    port=self._config.port,
                    cnopts=self.conn_opts,
                    private_key=private_key_path,
                )
            except SSHException as e:
                if counter >= retry_count:
                    retry = False
                    raise
                delay = backoff_delay * counter + random.randint(0, 1000) / 1000.0
                logger.warning(
                    f"Failed {counter} with {e}, retrying in {delay} seconds"
                )
                time.sleep(delay)
            finally:
                retry = counter < retry_count
                counter += 1

    @staticmethod
    def _get_key_from_store(name: str, decrypt: bool = True) -> str:
        from swarm.conf import Settings

        value = Settings.secrets_store.get_secret(name=name, decrypt=decrypt)
        _, path = mkstemp()
        Path(path).open("w").write(value)
        return path
