import logging
import os
from dataclasses import dataclass
from pathlib import Path
from typing import List
from typing import Optional
from typing import Union

import humanize
import yaml
from prefect.engine.results import LocalResult
from prefect.utilities.collections import DotDict

from swarm.schema.task.static import ResultCategory
from swarm.task.io.write.sftp.result import SftpAction
from swarm.task.io.write.sftp.result import SftpFile
from swarm.task.io.write.sftp.result import SftpTargetResult

logger = logging.getLogger(__name__)


@dataclass
class SftpResult:
    uploaded: Optional[List[SftpFile]] = None
    downloaded: Optional[List[SftpFile]] = None
    total_bytes: Optional[int] = 0
    empty: bool = False


class SftpResultAggregator:
    @staticmethod
    def execute(context: DotDict) -> SftpResult:
        # sftp results dir
        sftp_results_dir = Path(context.get("results_dir")).joinpath(
            ResultCategory.SFTP.value
        )

        # consolidate sftp results
        sftp_result = SftpResultAggregator.consolidate_results(
            output_path=sftp_results_dir
        )

        # short-circuit if no results
        if not sftp_result:
            return SftpResult(empty=True)

        # compose sftp result logging message
        message = dict(
            payload=humanize.naturalsize(sftp_result.total_bytes),
            uploaded=len(sftp_result.uploaded),
            downloaded=len(sftp_result.downloaded),
        )
        logger.info(f"sftp results:\n{yaml.dump(message)}")

        return sftp_result

    @staticmethod
    def consolidate_results(output_path: str) -> Union[SftpResult, None]:
        results: List[SftpTargetResult] = list()
        for root, directory_names, file_names in os.walk(output_path):
            local_result = LocalResult(dir=root)
            for f in file_names:
                result = local_result.read(location=f)
                results.append(result.value)

        if not results:
            logger.info(f"no sftp results found in path: {output_path}")
            return None

        total_bytes = 0
        uploaded = list()
        downloaded = list()
        for result in results:
            total_bytes += sum([target.bytes for target in result.targets])
            uploaded.extend([t for t in result.targets if t.action == SftpAction.PUT])
            downloaded.extend([t for t in result.targets if t.action == SftpAction.GET])

        return SftpResult(
            uploaded=uploaded, downloaded=downloaded, total_bytes=total_bytes
        )
