# Swarm Tasks

Documentation can be found [here](https://supreme-umbrella-3807537a.pages.github.io/)

# Swarm-tasks known issues

<ul>
<li> 3.18.7 - Accidental deployment from a non-master branch (use 3.18.8) ⛔️.</li>
<li> 3.28.11 - Failed build because of bad poetry version (fixed in 3.28.12 onwards) ⛔️.</li>
<li> 3.30.14 - Failed build because of bad dependent package <i>(catalogue)</i> version (fixed in 3.30.15 onwards) ⛔️.</li>
    <li>Note: Always update to latest swarm-tasks unless you are redeploying an 
        existing version of a flow that has already beeen tested in DEV and UAT</li>
<li> 3.31.11 - Failed deployment - investigation in progress) ⛔️.</li>
<li> 3.31.54 - Failed deployment - Dependency issues causing poetry install to fail ⛔️.</li> 
<li> 4.1.1   - Failed deployment - bad swarm-sdk changes causing all flows to fail </li>
</ul>

