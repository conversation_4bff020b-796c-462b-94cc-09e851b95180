FROM amazonlinux:2023
MAINTAINER <EMAIL>
LABEL org.label-schema.description=swarm-tasks \
      org.label-schema.schema-version=1.0 \
      org.label-schema.version=0.1.0 \
      org.label-schema.build-date="@buildDate@" \
      org.label-schema.name=swarm-tasks \
      org.label-schema.url=https://www.steel-eye.com \
      org.label-schema.vendor="SteelEye Limited" \
      org.label-schema.vcs-url="https://github.com/swarmhub/swarm-tasks"

ARG PYTHON_VERSION=3.9.19
ARG POETRY_VERSION=1.5.1
ARG APPUSER=app
ARG PYPI_HOST=https://steeleye.jfrog.io/steeleye/api/pypi/pypi-local
ARG PYPI_USERNAME
ARG PYPI_PASSWORD

COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh && \
    dnf -y update && \
    dnf install -y gcc python3 python3-pip python3-devel shadow-utils && \
    dnf install -y gnupg2 --allowerasing && \
    groupadd ${APPUSER} && useradd ${APPUSER} -g ${APPUSER} && \
    dnf clean all && rm -rf /var/cache/dnf


USER ${APPUSER}
RUN python3 -m venv /home/<USER>/venv && \
    source /home/<USER>/venv/bin/activate && \
    python3 -m pip install poetry==${POETRY_VERSION} && \
    poetry config repositories.steeleye-pypi ${PYPI_HOST} && \
    poetry config http-basic.steeleye-pypi ${PYPI_USERNAME} ${PYPI_PASSWORD} && \
    mkdir -p /home/<USER>/project

WORKDIR /home/<USER>/project
ENTRYPOINT ["/entrypoint.sh"]

# Install socat for SFTP proxy
USER root
RUN yum -y update && \
    yum -y install openssh-clients socat && \
    yum clean all

# Switch back to non-root user
USER app

RUN source ${HOME}/venv/bin/activate && \
    pip install --upgrade pip poetry==1.5.1

COPY config.toml $HOME/.prefect
COPY README.md pyproject.toml poetry.lock ./
COPY swarm_tasks swarm_tasks

RUN source ${HOME}/venv/bin/activate && \
    poetry install --no-dev
