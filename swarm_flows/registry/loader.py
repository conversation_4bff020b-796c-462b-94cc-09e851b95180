import pandas as pd
from elasticsearch6 import ConflictError
from elasticsearch6 import Elasticsearch


def load_resources():
    elastic_resource = pd.read_csv("content/ElasticsearchResource.csv")
    elastic_resource["_meta.id"] = elastic_resource.apply(
        lambda x: ":".join([x.get("id"), x.get("stack")]), axis=1
    )
    registry_config = elastic_resource.loc[elastic_resource.id == "registry"].iloc[0]
    registry = Elasticsearch(
        hosts=[registry_config.host],
        scheme=registry_config.scheme,
        port=9701,
    )

    sftp = pd.read_csv("content/SFTPResource.csv")
    sftp["_meta.id"] = sftp.apply(lambda x: ":".join([x["id"], x["env"]]), axis=1)
    create(es=registry, df=sftp, index="sftp_resource")

    clients = pd.read_json("content/Client.json")
    clients["_meta.id"] = clients["id"]
    create(es=registry, df=clients, index="client")

    create(es=registry, df=elastic_resource, index="elasticsearch_resource")


def create(es: Elasticsearch, df: pd.DataFrame, index: str):
    for idx, record in df.iterrows():
        body = dict(record.to_dict())
        body = dict([(k, v) for k, v in body.items() if not pd.isnull(v)])
        create_record(es=es, record=body, index=index)


def create_record(es: Elasticsearch, record: dict, index: str):
    try:
        es.create(
            index=index,
            doc_type="_doc",
            id=record.get("_meta.id"),
            body=record,
            refresh="true",
        )
    except ConflictError:
        pass


if __name__ == "__main__":
    load_resources()
