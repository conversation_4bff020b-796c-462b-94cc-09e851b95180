from typing import List

import click
from se_elastic_schema.models import QuarantinedRTS22Transaction
from se_elastic_schema.models import RTS22Transaction
from se_elasticsearch.repository import get_repository_by_cluster_version
from swarm.flow.static import RegistryCluster
from swarm.platform.registry import Registry
from swarm.schema.flow.bundle.components import ResourceConfig


REGISTRY_CONFIG = ResourceConfig(
    host=RegistryCluster.HOST,
    scheme=RegistryCluster.SCHEME,
    port=9701,
)


@click.command("apply_quarantine_hash")
@click.option(
    "--trn",
    multiple=True,
    required=True,
    type=str,
    prompt="Name of tenant to apply change",
)
@click.option(
    "--tenant",
    required=True,
    type=str,
    prompt="Name of tenant to apply change",
)
@click.option(
    "--stack",
    required=True,
    type=str,
    prompt="Name of stack where this should be applied for tenant",
)
def apply_quarantine_hash(trn: List[str], tenant: str, stack: str):

    registry = Registry(config=REGISTRY_CONFIG)

    config = registry.get_resource(
        resource_id="tenant-data", resource_type="ELASTICSEARCH", stack=stack
    )
    config = ResourceConfig.validate(config)

    es = get_repository_by_cluster_version(resource_config=config)

    hash_query = quarantine_hash_query(transaction_ref_numbers=list(trn))

    alias = QuarantinedRTS22Transaction.get_elastic_index_alias(tenant=tenant)
    quarantined = es.scroll(query=hash_query, index=alias)

    ids = quarantined["&id"].values.tolist()

    match_query = rts22_match_query(ids=ids)

    matched = es.scroll(
        query=match_query,
        index=RTS22Transaction.get_elastic_index_alias(tenant=tenant),
        include_elastic_meta=True,
    )

    if len(matched.index) != len(quarantined.index):
        raise ValueError("failed match check")

    matched = matched.rename(columns={"&hash": "stored_hash"})

    df = matched.merge(
        quarantined[[es.meta.id, es.meta.hash]],
        left_on=es.meta.id,
        right_on=es.meta.id,
        how="left",
    )

    duplicates_mask = df[es.meta.hash] == df["stored_hash"]
    df.loc[duplicates_mask, "status"] = "duplicate"
    df.loc[~duplicates_mask, "status"] = "needs_update"

    for i in range(len(df)):
        series = df.loc[i]
        body = {"doc": {es.meta.hash: series[es.meta.hash]}}
        index = series["__elastic__._index"]
        doc_id = series["__elastic__._id"]
        result = es.client.update(index=index, id=doc_id, body=body)
        print(result)


def quarantine_hash_query(transaction_ref_numbers: List[str]):
    body = {
        "size": len(transaction_ref_numbers),
        "_source": ["&hash", "&id"],
        "query": {
            "bool": {
                "must": [
                    {"term": {"&status": "deleted"}},
                    {
                        "terms": {
                            "reportDetails.transactionRefNo": transaction_ref_numbers
                        }
                    },
                ]
            }
        },
    }
    return body


def rts22_match_query(ids: List[str]):
    body = {
        "size": len(ids),
        "_source": ["&hash", "&id"],
        "query": {
            "bool": {
                "must": [
                    {"term": {"&model": "RTS22Transaction"}},
                    {"terms": {"&id": ids}},
                ]
            }
        },
    }
    return body


if __name__ == "__main__":
    apply_quarantine_hash()
