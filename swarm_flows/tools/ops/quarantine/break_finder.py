import click
from se_elastic_schema.models import QuarantinedRTS22Transaction
from se_elastic_schema.models import RTS22Transaction
from se_elasticsearch.repository import get_repository_by_cluster_version
from se_elasticsearch.repository import ResourceConfig
from swarm.flow.static import RegistryCluster
from swarm.platform.registry import Registry

from swarm_flows.tools.ops.quarantine.missing_elements import missing_elements


REGISTRY_CONFIG = ResourceConfig(
    host=RegistryCluster.HOST,
    scheme=RegistryCluster.SCHEME,
    port=9701,
)


@click.command("break_finder")
@click.option("--source_key", required=True)
@click.option("--tenant", required=True)
@click.option("--stack", required=True)
def break_finder(source_key: str, tenant: str, stack: str):

    registry = Registry(config=REGISTRY_CONFIG)

    config = registry.get_resource(
        resource_id="tenant-data", resource_type="ELASTICSEARCH", stack=stack
    )
    config = ResourceConfig.validate(config)

    es = get_repository_by_cluster_version(resource_config=config)

    body = {"_source": ["sourceIndex"], "query": {"term": {"sourceKey": source_key}}}

    df = es.scroll(
        query=body,
        index=",".join(
            [
                RTS22Transaction.get_elastic_index_alias(tenant=tenant),
                QuarantinedRTS22Transaction.get_elastic_index_alias(tenant=tenant),
            ]
        ),
    )

    indices = sorted(df["sourceIndex"].astype(int).tolist())

    breaks = sorted(list(missing_elements(indices, 0, len(indices) - 1)))

    print(breaks)


if __name__ == "__main__":
    break_finder()
