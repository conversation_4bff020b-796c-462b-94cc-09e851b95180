def missing_elements(iterable, start, end):
    if end - start <= 1:
        if iterable[end] - iterable[start] > 1:
            yield from range(iterable[start] + 1, iterable[end])
        return

    index = start + (end - start) // 2

    # is the lower half consecutive?
    consecutive_low = iterable[index] == iterable[start] + (index - start)
    if not consecutive_low:
        yield from missing_elements(iterable, start, index)

    # is the upper part consecutive?
    consecutive_high = iterable[index] == iterable[end] - (end - index)
    if not consecutive_high:
        yield from missing_elements(iterable, index, end)
