from se_elastic_schema.elastic_schema.core.base import BaseStrEnum


class Environments(BaseStrEnum):
    BENCHMARK = "benchmark"
    DEV = "dev"
    POC = "poc"
    PROD = "prod"
    SIT = "sit"
    UAT = "uat"


class FlowId:
    REALM = "realm"
    FLOW_ID = "flow_id"
    NON_PROD_REALM_TEMPLATE = "{tenant}.{env}.steeleye.co"
    PROD_REALM_TEMPLATE = "{tenant}.steeleye.co"


class UpdatedBundle:
    BUNDLE_PATH = "bundle_path"
    VERSION = "version"
    TENANTS = "tenants"
    BUNDLE_ID = "bundle_id"
    BUNDLE = "bundle"
    BUNDLE_DOC = "bundle_doc"


class UpdatedFlow:
    REALM = "realm"
    BUNDLE = "bundle"


class TaskOverrides:
    TASK_OVERRIDES = "taskOverrides"
    ALL_ENVIRONMENTS = "all_environments"
    FLOW_OVERRIDES_FOLDER = "flow_overrides"


class BundleFileName:
    BUNDLE = "bundle.yaml"


class TenantBundle:
    TENANT_YAML_TEMPLATE = "{tenant}.yaml"


class Schema:
    SCHEMA = "schema"
    PYTHON_TYPE = "python type"
    SCHEMA_CSV = "schema.csv"


class ESBundleFields:
    INDEX = "bundle_000"
    ALIAS = "bundle"
    MODEL = "Bundle"
    BUNDLE_FIELD = "bundle"
    BUNDLE_IMAGE = "image"


class ESFlowFields:
    FLOW_ALIAS = "flow"
    FLOW_INDEX = "flow_000"
    MODEL = "Flow"


class ESFields:
    META = "_meta"
    VERSION = "version"
    DUPLICATE = "DUPLICATE"
    COMMENT = "comment"


class GitFields:
    TO_BE_CHECKED_UPSTREAM = "origin/master"
