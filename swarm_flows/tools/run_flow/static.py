from pathlib import Path


class Realm:
    DEV = "dev"
    POC = "poc"
    PROD = "prod"
    STEELEYE = "steeleye"
    REALM = "realm"
    UAT = "uat"
    NONPROD_ENVS = ["dev", "uat", "benchmark", "sit", "poc"]
    SEPARATOR = "."


class TaskOverrides:
    ALL_ENVS = "all_environments"
    FLOW_OVERRIDES_FOLDER = "flow_overrides"
    TASK_OVERRIDES = "taskOverrides"
    TENANT_TEMPLATE = "{tenant}.yaml"


class LocalFlow:
    BUNDLE = "bundle"
    REALM = "realm"
    FLOW_ID_SEPARATOR = ":"


class Bundle:
    BUNDLE_FILE = "bundle.yaml"
    ID = "id"
    SCHEMA = "schema"
    COLUMN = "column"
    PYTHON_TYPE = "python type"
    SCHEMA_CSV = "schema.csv"
    BUNDLES_PATH = Path(__file__).parents[2].joinpath("bundles")
