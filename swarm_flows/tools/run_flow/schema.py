import os
from pathlib import Path
from typing import Dict
from typing import Op<PERSON>
from typing import Tuple

import pandas as pd
import yaml
from addict import addict
from pydantic import BaseModel
from pydantic import Field
from pydantic import root_validator
from pydantic import validator
from swarm.flow.static import FlowEnvVar

from swarm_flows.tools.run_flow.static import Bundle
from swarm_flows.tools.run_flow.static import LocalFlow
from swarm_flows.tools.run_flow.static import Realm
from swarm_flows.tools.run_flow.static import TaskOverrides
from swarm_flows.tools.static import BUNDLES_PATH


class FlowRunnerConfig(BaseModel):
    audit_id: Optional[str] = Field(None, description="The id used for auditing ")
    email_notification: Optional[bool] = Field(
        False, description="Boolean value to enabled email notifications"
    )
    file_url: Optional[str] = Field(
        None, description="The file url to be processed by the flow"
    )
    flow_id: Optional[str] = Field(None, description="Id of the flow to run")
    local_flow_id: Optional[str] = Field(
        None, description="Id of the flow when running locally"
    )
    local_flow: Optional[dict] = Field(
        None, description="Dictionary with the complete flow compiled from local files"
    )
    local_ports: Optional[Dict[str, int]] = Field(
        None, description="Dict containing alternative ports for resources"
    )
    use_dask_executor: Optional[bool] = Field(
        True, description="Enable/disable local task executor"
    )

    @validator("flow_id", always=True)
    def set_flow_id(cls, value):
        if value is None:
            value = os.environ.get(FlowEnvVar.SWARM_FLOW_ID)
        return value

    @validator("file_url", always=True)
    def set_file_url(cls, value):
        if value is None:
            value = os.environ.get(FlowEnvVar.SWARM_FILE_URL)
        return value

    @validator("local_flow_id", always=True)
    def set_local_flow_id(cls, value):
        if value is None:
            value = os.environ.get(FlowEnvVar.SWARM_LOCAL_FLOW_ID)

        if not value:
            return value

        split_colon_value = value.split(":")

        if len(split_colon_value) != 2:
            raise AttributeError(
                "SWARM_LOCAL_FLOW_ID is incorrect, please follow the following "
                "<<realm:bundle_id>>"
            )
        split_realm = split_colon_value[0].split(".")

        if not 3 <= len(split_realm) <= 4:
            raise AttributeError(f"Realm: {split_realm} is in an incorrect format")

        if not (
            split_realm[1] in Realm.NONPROD_ENVS or split_realm[1] == Realm.STEELEYE
        ):
            raise AttributeError(
                "SWARM_LOCAL_FLOW_ID's `realm` not in the correct format"
            )

        return value

    @root_validator
    def local_flow_id_or_flow_id(cls, values):
        local_flow_id = values.get("local_flow_id")
        flow_id = values.get("flow_id")
        if not (local_flow_id or flow_id):
            raise AttributeError("require either flow_id or local_flow_id")

        if not flow_id:
            values.update(local_args(local_flow_id=local_flow_id))
            del values["local_flow_id"]

        return values


def local_args(local_flow_id: str) -> Dict[str, dict]:
    """
    Creates args to run a flow locally with given flow_id
    :param local_flow_id:
    :return: Dict {flow_id: bundle}
    """
    split_flow_id = local_flow_id.split(LocalFlow.FLOW_ID_SEPARATOR)
    bundle_id = split_flow_id[-1]

    bundle_path, bundle_config = local_bundle_paths(bundle_id=bundle_id)

    # as we no longer have the "image" key on bundle.yaml
    # we still need to define the bundle image to run flows locally
    # although this value is not being used
    bundle_config["image"] = ""

    schema_path = bundle_path.parent.joinpath(Bundle.SCHEMA_CSV)

    schema = pd.read_csv(schema_path) if schema_path.exists() else None

    if schema is not None and not schema.empty:
        schema = dict(zip(schema[Bundle.COLUMN], schema[Bundle.PYTHON_TYPE]))

    bundle_config[Bundle.SCHEMA] = schema

    # compose flow based on flow and bundle configs
    realm = split_flow_id[0]
    split_realm = realm.split(Realm.SEPARATOR)

    tenant = split_realm[0]

    env = split_realm[1] if split_realm[1] in Realm.NONPROD_ENVS else Realm.PROD

    local_flow = {
        LocalFlow.REALM: realm,
        LocalFlow.BUNDLE: bundle_config,
    }

    # Getting taskOverrides for tenant, if any
    tenant_config = Path(
        bundle_path.parent.joinpath(
            TaskOverrides.FLOW_OVERRIDES_FOLDER,
            TaskOverrides.TENANT_TEMPLATE.format(tenant=tenant),
        )
    )

    try:
        if os.path.exists(tenant_config):
            flow_config = yaml.load(
                tenant_config.read_text(), Loader=yaml.SafeLoader
            ).get(TaskOverrides.TASK_OVERRIDES, {})
            task_overrides = list()
            task_overrides += flow_config.get(
                TaskOverrides.ALL_ENVS, []
            ) + flow_config.get(env, [])
            if task_overrides:
                local_flow[TaskOverrides.TASK_OVERRIDES] = task_overrides

    except (FileNotFoundError, AttributeError):
        # pass on platform flows proxying for tenants
        pass

    # gather args for flow runner
    args = dict(local_flow=local_flow, flow_id=local_flow_id)

    return args


def local_bundle_paths(bundle_id: str) -> Optional[Tuple[Path, addict.Dict]]:
    """
    Finds & reads local bundle and its path with given bundle id
    :param bundle_id:
    :return:
    """
    for root, dirs, files in os.walk(BUNDLES_PATH):
        root_path = Path(root)
        bundle_paths = [root_path.joinpath(f) for f in files if f == Bundle.BUNDLE_FILE]
        for bundle_path in bundle_paths:
            bundle = yaml.load(
                bundle_path.read_text(encoding="utf-8"), Loader=yaml.SafeLoader
            )
            if bundle.get(Bundle.ID) == bundle_id:
                return bundle_path, bundle
    raise AttributeError(f"Local bundle: {bundle_id} not found")


class RunFlowConfig(BaseModel):
    flow_runner: FlowRunnerConfig
