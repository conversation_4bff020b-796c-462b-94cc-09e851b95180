import logging
from typing import Any
from typing import Dict
from typing import Optional

import boto3
from colors import blue
from colors import green
from colors import yellow

from swarm_flows.tools.custom_logger import CustomFormatter
from swarm_flows.tools.qa.trigger_nonprod_flows_by_bundle_id.bundle_ids import (
    BUNDLE_IDS,
)
from swarm_flows.tools.qa.trigger_nonprod_flows_by_bundle_id.tenant_blacklist import (
    TENANT_BLACKLIST,
)
from swarm_flows.tools.qa.trigger_nonprod_flows_by_bundle_id.tenant_whitelist import (
    TENANT_WHITELIST,
)

# Use custom logger for colored logs based on level -> makes it easier to process visually what is going on

logger = logging.getLogger("UAT Flow Tester")
logger.setLevel(logging.DEBUG)
ch = logging.StreamHandler()
ch.setLevel(logging.DEBUG)
ch.setFormatter(CustomFormatter())
logger.addHandler(ch)

REQUEST_USER_INPUT = True  # Disable this if you know what you are doing and want to skip requesting user input
STACK = "uat"

if <PERSON><PERSON><PERSON> not in ["uat", "dev"]:
    raise RuntimeError("This script only supports non-prod environments")


def get_most_recent_s3_object(s3: Any, bucket_name: str, prefix: str) -> Optional[Dict]:
    """
    For a given bucket and file path prefix, return the last modified file.
    We can easily change this logic to include the latest X files,
    and process all of them or let the user choose from a set

    :param s3: S3 Client
    :param bucket_name: Name of the bucket i.e. "integration.uat.steeleye.co"
    :param prefix: Flow file path prefix i.e. "flows/order-universal-steeleye-trade-blotter/"
    :return: The last file that was executed for a given Flow
    """
    paginator = s3.get_paginator("list_objects_v2")
    page_iterator = paginator.paginate(Bucket=bucket_name, Prefix=prefix)
    latest = None
    for page in page_iterator:
        if "Contents" in page:
            latest2 = max(page["Contents"], key=lambda x: x["LastModified"])
            if latest is None or latest2["LastModified"] > latest["LastModified"]:
                latest = latest2
    return latest


if __name__ == "__main__":
    """
    1) Connect to AWS account and access all provided S3 Buckets.
    Iterate over each one and exclude any bucket that does not contain the `.uat` environment

    2) Search for the flow paths that match the bundle-ids and fetch the latest modified file. Download it to temp dir
    and re-upload it to S3. Log the entire process

    NOTE: pip install ansicolors (colors alias) if you haven't before (it is also a dev dependency on pyproject.toml)
    """

    s3_client = boto3.client("s3")

    if TENANT_WHITELIST:
        buckets = [
            f"{x}.{STACK}.steeleye.co"
            for x in TENANT_WHITELIST
            if x not in TENANT_BLACKLIST
        ]
    else:
        buckets = [
            x["Name"]
            for x in s3_client.list_buckets()["Buckets"]
            if f".{STACK}.steeleye.co" in x["Name"]
            and all(y not in x["Name"] for y in TENANT_BLACKLIST)
        ]

    # Realistically, this condition should never be met, but I added it just to be safe
    if (
        any(
            x in buckets
            for x in ["iris.steeleye.co", "mar.steeleye.co", "pinafore.steeleye.co"]
        )
        or not buckets
    ):
        raise RuntimeError(
            "Current AWS Profile is for Production - this script can only be used for Dev and UAT"
        )

    for bucket in buckets:
        logger.info(f"Iterating over bucket: {bucket}")

        for flow in BUNDLE_IDS:
            logger.info(f"Iterating over flow {flow}")

            try:
                latest_flow_file = get_most_recent_s3_object(
                    s3=s3_client, bucket_name=bucket, prefix=f"flows/{flow}/"
                ).get("Key")
            except AttributeError:
                logger.warning(
                    f"Bucket {bucket} does not contain any files for the flow {flow}"
                )
                continue

            if not REQUEST_USER_INPUT:
                user_authorized = "y"
            else:
                user_authorized = ""

            while user_authorized.strip().lower() not in ["y", "n"]:
                user_authorized = input(
                    f"Found file {blue(latest_flow_file)} for bucket {yellow(bucket)} and flow {green(flow)}.\n"
                    f"Do you want to run this Flow for this file? Enter `y` or `n`:"
                )

            if user_authorized == "y":
                with open("latest_file", "wb") as f:
                    s3_client.download_fileobj(bucket, latest_flow_file, f)

                response = s3_client.upload_file(
                    "latest_file", bucket, latest_flow_file
                )

                logger.info(
                    "Successfully re-uploaded file. Flow will be triggered shortly"
                )
            else:
                logger.warning("Skipping file as per user input")
