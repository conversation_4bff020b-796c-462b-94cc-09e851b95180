id: csv-to-record
name: Imports Model from CSV
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: UploadOrIngestCondition
  conditionTaskName: S3OrEsWriteController
  trueTaskName: Csv2ModelS3UploadConfig
  falseTaskName: PutIfAbsent
  mapped: true
  merge: false
- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  mapped: true
  merge: false
tasks:
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url

- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url

- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url

- path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
  name: CsvFileSplitter
  params:
    chunksize: 2000
    detect_encoding: true
    audit_input_rows: true
  upstreamTasks:
    - taskName: S3OrLocalFile
      mapped: false
      key: extractor_result

- path: swarm_tasks.io.read.batch_producer:BatchProducer
  name: BatchProducer
  upstreamTasks:
  - taskName: CsvFileSplitter
    mapped: true
    flatten: true
    key: file_splitter_result

- path: swarm_tasks.generic.csv_to_model:CsvToModel
  name: CsvToModel
  params:
    populate_meta_fields: false
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result

- path: swarm_tasks.generic.extract_batch_from_frame_producer_result:ExtractBatchFromFrameProducerResult
  name: ExtractBatchFromFrameProducerResult
  params:
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: frame_producer_result

- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: create
    # Comment out this param to write to ES. If the following is enabled we upload to S3.
    dump_raw_ndjson: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: CsvToModel
      mapped: true
      key: transform_result
    - taskName: ExtractBatchFromFrameProducerResult
      mapped: true
      key: batch_index

- path: swarm_tasks.io.read.s3_upload_or_es_write_controller:S3OrEsWriteController
  name: S3OrEsWriteController
  params:
  upstreamTasks:
    - taskName: ElasticBulkTransformer
      mapped: true
      key: ndjson_dir

- path: swarm_tasks.transform.extract_path.generate_csv2model_s3_upload_config:GenerateCsv2ModelS3UploadConfig
  name: Csv2ModelS3UploadConfig
  params:
    s3_key_prefix: "internal/Csv2Model"
  upstreamTasks:
    - taskName: ElasticBulkTransformer
      mapped: true
      key: extract_path_result_list
    - taskName: file_url
      mapped: false
      key: file_url

- path: swarm_tasks.io.write.aws.s3_upload_file:S3UploadFile
  name: S3UploadFile
  upstreamTasks:
    - taskName: Csv2ModelS3UploadConfig
      mapped: true
      key: upload_target

- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsent
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: ElasticBulkTransformer
      mapped: true
      key: result
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineCondition
  upstreamTasks:
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformer
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: CsvToModel
      mapped: true
      key: transform_result
    - taskName: ExtractBatchFromFrameProducerResult
      mapped: true
      key: batch_index
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsent
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: QuarantinedElasticBulkTransformer
      mapped: true
      key: result
- path: swarm_tasks.control_flow.noop:Noop
  name: Noop
