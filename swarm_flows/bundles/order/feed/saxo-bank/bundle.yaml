# Bundle path -> order/feed/saxo-bank
# Confluence page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/**********/Order+Saxo+Bank
# GitHub docs: docs/bundles/order/feed/order-feed-saxo-bank.md
id: order-feed-saxo-bank
name: Order Feed Saxo Bank
infra:
  - name: tenant-data
    type: ELASTICSEARCH
  - name: reference-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process
controlFlows:
  - name: S3OrLocalFile
    conditionTaskName: ParametersFlowController
    trueTaskName: S3DownloadFile
    falseTaskName: LocalFile
    merge: true
  - name: NeedsQuarantine
    conditionTaskName: QuarantineCondition
    trueTaskName: QuarantinedElasticBulkTransformer
    falseTaskName: Noop
    mapped: true
    merge: false
tasks:
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvFileSplitter
    params:
      chunksize: 5000
      detect_encoding: true
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result
  # Batch Producer
  - path: swarm_tasks.io.read.batch_producer:BatchProducer
    name: BatchProducer
    params:
      source_schema:
        AccountCurrency: string
        BuySell: string
        CallPut: string
        CounterpartID: string
        DirtyPrice: float
        ExecutingEntityIdentificationCode: string
        ExpiryDate: string
        FXType: string
        ISIN: string
        ISINCode: string
        ISOMic: string
        InstrumentCode: string
        InstrumentCurrency: string
        InstrumentISINCode: string
        InstrumentType: string
        NetAmount: float
        OrderNumber: string
        OrderType: string
        Price: float
        Strike: float
        TermOfUnderlyingIndex: string
        TradeExecutionTime: string
        TradeNumber: string
        TradeTime: string
        TradedAmount: float
        UnderlyingInstrumentCode: string
        UnderlyingInstrumentISINCode: string
        UserIdOnTrade: string
        Venue: string
    upstreamTasks:
    - taskName: CsvFileSplitter
      mapped: true
      flatten: true
      key: file_splitter_result
  # Saxo Bank Primary Transformations
  - path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
    name: SaxoBankOrderTransformations
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  # Link Parties
  - path: swarm_tasks.transform.steeleye.link.parties:LinkParties
    name: LinkParties
    params:
      identifiers_path: marketIdentifiers.parties
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: SaxoBankOrderTransformations
        mapped: true
        key: result
  # Link Instrument
  - path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
    name: LinkInstrument
    params:
      identifiers_path: marketIdentifiers.instrument
      venue_attribute: transactionDetails.ultimateVenue
      currency_attribute: transactionDetails.priceCurrency
    resources:
      ref_data_key: reference-data
      tenant_data_key: tenant-data
    upstreamTasks:
      - taskName: SaxoBankOrderTransformations
        mapped: true
        key: result
  # Link each OrderState record to the parent Order record
  - path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
    name: AssignMetaParent
    params:
      parent_model_attribute: _order.__meta_model__
      parent_attributes_prefix: _order.
      target_attribute: _orderState.__meta_parent__
    upstreamTasks:
    - taskName: SaxoBankOrderTransformations
      mapped: true
      key: result
  # Primary frame concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PrimaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - marketIdentifiers.instrument
        - marketIdentifiers.parties
    upstreamTasks:
      - taskName: SaxoBankOrderTransformations
        mapped: true
        key: primary_transformations
      - taskName: LinkParties
        mapped: true
        key: link_parties
      - taskName: LinkInstrument
        mapped: true
        key: link_instruments
      - taskName: AssignMetaParent
        mapped: true
        key: parent_id
 # Filter only Order records into a data frame
  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: OrderRecords
    params:
      except_prefix: _orderState.
      strip_prefix: true
    upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: result
  # Filter only OrderState records into a data frame
  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: OrderStateRecords
    params:
      except_prefix: _order.
      strip_prefix: true
    upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: result
  # Strip prefix `_order.` from the column names of OrderRecords frame.
  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixOrder
    params:
      action: strip
      prefix: _order.
    upstreamTasks:
    - taskName: OrderRecords
      mapped: true
      key: result
  # Strip prefix `_orderState.` from the column names of OrderState Records frame.
  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixOrderState
    params:
      action: strip
      prefix: _orderState.
    upstreamTasks:
    - taskName: OrderStateRecords
      mapped: true
      key: result
  # Vertically concatenate Order and OrderState Record frames created above.
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: VerticalConcatenator
    params:
      orient: vertical
      reset_index: true
      drop_index: true
    upstreamTasks:
    - taskName: StripPrefixOrder
      mapped: true
      key: order_records
    - taskName: StripPrefixOrderState
      mapped: true
      key: order_state_records
  # Best-execution tasks
  - path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
    name: BestExecution
    resources:
      es_client_key: reference-data
    upstreamTasks:
    - taskName: VerticalConcatenator
      mapped: true
      key: result
  # Best-Ex results concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: BestExecutionConcatenator
    params:
      orient: horizontal
    upstreamTasks:
    - taskName: BestExecution
      mapped: true
      key: best_ex_result
    - taskName: VerticalConcatenator
      mapped: true
      key: orders_and_orderstates_final
  # Assign Meta
  - path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
    name: AssignMeta
    params:
      model_attribute: __meta_model__
      parent_attribute: __meta_parent__
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result
   # Post-meta concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PostMetaConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __meta_model__
        - __meta_parent__
    upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result
    - taskName: AssignMeta
      mapped: true
      key: meta
  # Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformer
    params:
      action_type: create
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: BatchProducer
      mapped: true
      key: producer_result
  # Elastic Bulk Writer
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: ElasticBulkWriter
    params:
      payload_size: 10000000
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: ElasticBulkTransformer
      mapped: true
      key: result
  # Instrument Mapper
  - path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
    name: InstrumentMapper
    upstreamTasks:
      - taskName: PostMetaConcatenator
        mapped: true
        key: source_frame
      - taskName: ElasticBulkWriter
        mapped: true
        key: bulk_writer_result
  # Quarantine Condition
  - path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
    name: QuarantineCondition
    upstreamTasks:
    - taskName: ElasticBulkWriter
      mapped: true
      key: bulk_writer_result
  # Quarantined Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: QuarantinedElasticBulkTransformer
    params:
      action_type: create
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: BatchProducer
      mapped: true
      key: producer_result
    - taskName: ElasticBulkWriter
      mapped: true
      key: bulk_writer_result
  # Elastic Bulk Writer for Quarantined Records
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: QuarantinedElasticBulkWriter
    params:
      payload_size: 10000000
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: QuarantinedElasticBulkTransformer
      mapped: true
      key: result
  # NoOp
  - path: swarm_tasks.control_flow.noop:Noop
    name: Noop