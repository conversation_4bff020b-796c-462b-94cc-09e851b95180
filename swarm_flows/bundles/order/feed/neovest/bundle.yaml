# Confluence documentation: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2929295361/Order+Neovest+FIX+Drop+Copy
id: order-feed-neovest-fix
name: Order Feed Neovest FIX

infra:
  - name: tenant-data
    type: ELASTICSEARCH
  - name: reference-data
    type: ELASTICSEARCH

audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data

parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process

controlFlows:
  - name: S3OrLocalFile
    conditionTaskName: ParametersFlowController
    trueTaskName: S3DownloadFile
    falseTaskName: LocalFile

  - name: NeedsQuarantine
    conditionTaskName: QuarantineCondition
    trueTaskName: QuarantinedElasticBulkTransformer
    falseTaskName: Noop
    merge: false

tasks:
  # ParametersFlowController
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
    - taskName: file_url
      key: file_url

    # S3DownloadFile
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
    - taskName: file_url
      key: file_url

    # LocalFile
  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
    - taskName: file_url
      key: file_url

    # Download fix files and put their content in a data frame column
  - path: swarm_tasks.io.read.fix.fix_batch_csv_downloader:FixBatchCsvDownloader
    name: FixBatchCsvDownloader
    upstreamTasks:
      - taskName: S3OrLocalFile
        key: extractor_result

    # Read .fix file, parse, validate and convert to FixParserResult dataclass
    # Note: the key has to be fix_dataframe as that's the argument name in execute()
  - path: swarm_tasks.io.read.fix.fix_parser:FixParser
    name: FixParser
    upstreamTasks:
      - taskName: FixBatchCsvDownloader
        key: fix_dataframe

    # Convert FixParserResult to Pandas DataFrame
    # Note: the key has to be fix_parsed_data as that's the argument name in execute()
  - path: swarm_tasks.io.read.fix.fix_parser_result_to_frame:FixParserResultToFrame
    name: FixParserResultToFrame
    params:
      dataframe_columns:
        - Account
        - AvgPx
        - ClientID
        - ClOrdID
        - Currency
        - ExecID
        - ExecType
        - LastCapacity
        - LastLiquidityInd
        - LastQty
        - LastMkt
        - LastPx
        - LeavesQty
        - LegRefID
        - MaturityDate
        - MaturityDay
        - MaturityMonthYear
        - MaxFloor
        - MsgType
        - MultiLegReportingType
        - OrderCapacity
        - OrderID
        - OrderQty
        - OrdStatus
        - OrdType
        - PartyID
        - PossResend
        - Price
        - PriceType  # Does not exist on tested file
        - PutOrCall
        - SendingTime
        - SecurityExchange
        - SecurityID
        - SecurityType
        - Side
        - StopPx
        - StrikePrice
        - Symbol
        - Text
        - TimeInForce
        - TransactTime
        - ff_20001
        - ff_20073
    upstreamTasks:
      - taskName: FixParser
        key: fix_parsed_data

  - path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
    name: FilterRowsInSourceFile
    params:
      query: "~((`MsgType` == 'B' & `ExecType` == 'D' & `PossResend` == 'Y')|(`ExecType`=='6' & `OrdStatus`=='E'))"
      skip_on_empty: true
    upstreamTasks:
    - taskName: FixParserResultToFrame
      key: result

    # Tenant Input for PrimaryTransformations
  - path: swarm_tasks.primary_transformations.input_tasks.tenant_input:TenantInput
    name: TenantInput

    # ES Client Input for PrimaryTransformations
  - path: swarm_tasks.primary_transformations.input_tasks.es_client_input:EsClientInput
    name: EsClientInput
    resources:
      es_client_key: tenant-data

  # Primary transformations
  - path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
    name: PrimaryTransformations
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        key: result
      - taskName: TenantInput
        key: tenant
      - taskName: EsClientInput
        key: es_client

  # Link Parties
  - path: swarm_tasks.transform.steeleye.link.parties:LinkParties
    name: LinkParties
    params:
      identifiers_path: marketIdentifiers.parties
      add_investment_firm_covered_directive: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryTransformations
        key: result

  # Party Fallback with LEI lookup
  - path: swarm_tasks.order.generic.party_fallback_with_lei_lookup:PartyFallbackWithLeiLookup
    name: PartyFallbackWithLeiLookup
    upstreamTasks:
      - taskName: PrimaryTransformations
        key: result
      - taskName: LinkParties
        key: link_parties_result

  # Link Instrument
  - path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
    name: LinkInstrument
    params:
      identifiers_path: marketIdentifiers.instrument
      venue_attribute: transactionDetails.venue
      currency_attribute: transactionDetails.priceCurrency
    resources:
      ref_data_key: reference-data
      tenant_data_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryTransformations
        key: result

  # Instrument Fallback
  - path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
    name: InstrumentFallback
    params:
      instrument_fields_map:
        - source_field: __fb_created_through_fallback__
          target_field: isCreatedThroughFallback
        - source_field: __fb_instrument_fullname__
          target_field: instrumentFullName
        - source_field: __fb_ext_instrument_unique_identifier__
          target_field: ext.instrumentUniqueIdentifier
        - source_field: __fb_ext_best_ex_asset_class_main__
          target_field: ext.bestExAssetClassMain
        - source_field: __fb_ext_best_ex_asset_class_sub__
          target_field: ext.bestExAssetClassSub
        - source_field: __fb_asset_class_detection__
          target_field: instrumentClassification
          # ext fields
        - source_field: __fb_mone__
          target_field: ext.priceNotation
        - source_field: __fb_unit__
          target_field: ext.quantityNotation
      str_to_bool_dict:
        "true": True
        "false": False
    upstreamTasks:
      - taskName: PrimaryTransformations
        key: result
      - taskName: LinkInstrument
        key: link_instrument

  # Link each OrderState record to the parent Order record
  - path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
    name: AssignMetaParent
    params:
        parent_model_attribute: _order.__meta_model__
        parent_attributes_prefix: _order.
        target_attribute: _orderState.__meta_parent__
    upstreamTasks:
      - taskName: PrimaryTransformations
        key: result

  # Frame Concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PrimaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - asset_class_attribute
        - bbg_figi_id_attribute
        - currency_attribute
        - eurex_id_attribute
        - exchange_symbol_attribute
        - expiry_date_attribute
        - interest_rate_start_date_attribute
        - isin_attribute
        - notional_currency_1_attribute
        - notional_currency_2_attribute
        - option_strike_price_attribute
        - option_type_attribute
        - swap_near_leg_date_attribute
        - underlying_index_name_attribute
        - underlying_index_name_leg_2_attribute
        - underlying_index_series_attribute
        - underlying_index_term_attribute
        - underlying_index_term_value_attribute
        - underlying_index_version_attribute
        - underlying_isin_attribute
        - underlying_symbol_attribute
        - underlying_symbol_expiry_code_attribute
        - underlying_index_term_leg_2_attribute
        - underlying_index_term_value_leg_2_attribute
        - venue_attribute
        - venue_financial_instrument_short_name_attribute
        - instrument_classification_attribute
        - __fb_asset_class_detection__
        - __fb_created_through_fallback__
        - __fb_mone__
        - __fb_unit__
        - __fb_instrument_fullname__
        - __fb_ext_instrument_unique_identifier__
        - __fb_ext_best_ex_asset_class_main__
        - __fb_ext_best_ex_asset_class_sub__
        - __fallback_buyer__
        - __fallback_buyer_dec_maker__
        - __fallback_client__
        - __fallback_counterparty__
        - __fallback_executing_entity__
        - __fallback_exec_within_firm__
        - __fallback_inv_dec_in_firm__
        - __fallback_seller__
        - __fallback_seller_dec_maker__
        - __fallback_trader__
    upstreamTasks:
      - taskName: AssignMetaParent
        key: result
      - taskName: PrimaryTransformations
        key: primary_transformations
      - taskName: InstrumentFallback
        key: instrument_fallback
      - taskName: PartyFallbackWithLeiLookup
        key: party_fallback

  # Convert all FILLS/PARF into a single record if fill_by_fill_flag is FALSE
  - path: swarm_tasks.order.generic.fill_by_fill_check:FillByFillCheck
    name: FillByFillCheck
    params:
      fill_by_fill_flag: true
    upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      key: result

  # Filter only Order records columns into a data frame
  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: OrderRecords
    params:
      except_prefix: _orderState.
    upstreamTasks:
    - taskName: FillByFillCheck
      key: result

  # Filter only OrderState records columns into a data frame
  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: OrderStateRecords
    params:
      except_prefix: _order.
    upstreamTasks:
    - taskName: FillByFillCheck
      key: result

  # Strip prefix `_order.` from the column names of OrderRecords frame.
  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixOrder
    params:
      action: strip
      prefix: _order.
    upstreamTasks:
    - taskName: OrderRecords
      key: result

  # Strip prefix `_orderState.` from the column names of OrderState Records frame.
  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixOrderState
    params:
      action: strip
      prefix: _orderState.
    upstreamTasks:
      - taskName: OrderStateRecords
        key: result

  # Vertically concatenate Order and OrderState Record frames created above.
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: VerticalConcatenator
    params:
      orient: vertical
      reset_index: true
      drop_index: true
    upstreamTasks:
    - taskName: StripPrefixOrder
      key: order_records
    - taskName: StripPrefixOrderState
      key: order_state_records

  # Remove InvalidOrderStates
  - path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
    name: RemoveInvalidOrderStates
    params:
      query: "`executionDetails.orderStatus`.notnull()"
    upstreamTasks:
    - taskName: VerticalConcatenator
      key: result

  # Remove duplicate new orders, remove synthetic new orders already in Elastic
  - path: swarm_tasks.order.generic.remove_duplicate_newo:RemoveDuplicateNEWO
    name: RemoveDupNEWO
    params:
      newo_in_file_col: __newo_in_file__
      drop_newo_in_file_col: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: RemoveInvalidOrderStates
      key: result

  #Best Execution
  - path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
    name: BestExecution
    resources:
      es_client_key: reference-data
    upstreamTasks:
      - taskName: RemoveDupNEWO
        key: result

  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: BestExecutionConcatenator
    params:
      orient: horizontal
      drop_columns:
      - marketIdentifiers.instrument
      - marketIdentifiers.parties
      - __newo_in_file__
    upstreamTasks:
      - taskName: RemoveDupNEWO
        key: result
      - taskName: BestExecution
        key: best_ex_result

  # Assign Meta
  - path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
    name: AssignMeta
    params:
      model_attribute: __meta_model__
      parent_attribute: __meta_parent__
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: BestExecutionConcatenator
        key: result

  # Post-meta concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: FinalConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __meta_model__
        - __meta_parent__
    upstreamTasks:
      - taskName: BestExecutionConcatenator
        key: result
      - taskName: AssignMeta
        key: meta

  # Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformer
    params:
      action_type: create
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: FinalConcatenator
      key: transform_result
    - taskName: FilterRowsInSourceFile
      key: producer_result

  # Elastic Bulk Writer
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: PutIfAbsent
    params:
      payload_size: 10000000
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: ElasticBulkTransformer
      key: result

  # Instrument Mapper
  - path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
    name: InstrumentMapper
    upstreamTasks:
      - taskName: FinalConcatenator
        key: source_frame
      - taskName: PutIfAbsent
        key: bulk_writer_result

  # Quarantine Condition
  - path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
    name: QuarantineCondition
    upstreamTasks:
    - taskName: PutIfAbsent
      key: bulk_writer_result

  # Quarantine Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: QuarantinedElasticBulkTransformer
    params:
      action_type: create
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: FinalConcatenator
      key: transform_result
    - taskName: FilterRowsInSourceFile
      key: producer_result
    - taskName: PutIfAbsent
      key: bulk_writer_result

  # Quarantine Elastic Bulk Writer
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: QuarantinedPutIfAbsent
    params:
      payload_size: 10000000
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: QuarantinedElasticBulkTransformer
      key: result

  - path: swarm_tasks.control_flow.noop:Noop
    name: Noop