# Fill by Fill is turned off for caxton. We aggregate all FILLs and PARFs to a single record.
taskOverrides:
  all_environments:
    - name: FillByFillCheck
      params:
        fill_by_fill_flag: false
        initial_quantity: priceFormingData.initialQuantity
        remaining_quantity: priceFormingData.remainingQuantity
        fill_quantity: transactionDetails.quantity
        order_id: _order.id
        execution_price: transactionDetails.price
        order_status: _orderState.executionDetails.orderStatus
        trading_date_time: transactionDetails.tradingDateTime
        route_id: _order.id
