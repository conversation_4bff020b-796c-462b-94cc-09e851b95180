column,description,python type
ActionDateTime,,string
ActionType,,string
AlgoName,,string
AlgoParams,,string
ArrivalTime_QuoteTime,,string
AssetSubType,,string
AssetType,,string
BasketID,,string
BenchmarkType,,string
BenchmarkVenues,,string
ClientCategory,,string
CounterpartyCode,,string
Currency,,string
DecisionTime,,string
DirectedFlow,,string
ExecutionType,,string
FeeAmount1,,string
FeeAmount2,,string
FeeBasis1,,string
FeeBasis2,,string
FirstFillTime_TradeTime,,string
FlowType,,string
Index,,string
IsAnOrder,,string
ISIN,,string
LastCapacity,,string
LastFillTime,,string
LimitPrice,,string
MarketOrderId,,string
MessageType,,string
MiFIDWaiverFlag,,string
Note,,string
OrderRef,,string
OrderType,,string
ParentOrderRef,,string
ParticipantCode,,string
ParticipationRate,,string
PMID,,string
PreTradeImpactEstimate,,string
PreTradeRiskEstimate,,string
Price,,float
PrimaryExchangeLine,,string
PublicTradeID,,string
Quantity,,float
RTS27TradeAttributes,,string
Sector,,string
SettlementDate,,string
SettlementPeriod,,string
Side,,string
TargetCurrency,,string
TimeInForce,,string
TradeFlag,,string
TraderID,,string
TradingNetworkID,,string
Urgency,,string
UserBenchmarks,,string
UserDefinedFilter,,string
Venue,,string