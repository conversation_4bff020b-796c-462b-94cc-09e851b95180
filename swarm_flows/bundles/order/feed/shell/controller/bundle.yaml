# Confluence Page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2642083893/DRAFT+2+Order+Bloomberg+AUDT+SHELL-SAMCo
# Bundle path -> order/feed/shell/controller
id: order-feed-samco-bbg-audt-controller
name: Order Feed SAMCo BBG AUDT Controller
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: S3CopyOverride
  conditionTaskName: S3CopyController
  mapped: false
  merge: true
  cases:
    Noop: Noop # Default case
    S3CopyFile: S3CopyFile # Case specific
tasks:
  - path: swarm_tasks.generic.value_proxy:ValueProxy
    name: S3CopyController
    params:
      value: Noop
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
    - taskName: file_url
      mapped: false
      key: file_url
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
    - taskName: file_url
      mapped: false
      key: file_url
  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
    - taskName: file_url
      mapped: false
      key: file_url
  - path: swarm_tasks.io.read.aws.copy.s3_to_s3_copy:S3ToS3Copy
    name: S3CopyFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  # GPG Decryptor which decrypts PGP and GPG files and returns an ExtractPathResult containing the
  # decrypted file. If the file is not a PGP/GPG file, it returns the ExtractPathResult from S3OrLocalFile.
  - path: swarm_tasks.io.read.gpg_decryptor:GpgDecryptor
    name: GpgDecryptor
    params:
      num_private_key_params: 2
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result

  - path: swarm_tasks.order.feed.bbg.audt.bbg_audt_csv_file_batcher:BBGAudtCSVFileBatcher
    name: ShellCSVFileBatcher
    params:
      max_batch_size: 20000
    upstreamTasks:
      - taskName: GpgDecryptor
        mapped: false
        key: extractor_result

  - path: swarm_tasks.order.feed.shell.record_transformer:ShellFrameTransformer
    name: RecordTransformer
    upstreamTasks:
      - taskName: ShellCSVFileBatcher
        mapped: false
        key: file_splitter_result_list
  - path: swarm_tasks.transform.extract_path.s3_file_list_from_frame_splitter_result_list:S3FileListFileSplitterResultList
    name: S3FileListFileSplitterResultList
    params:
      datetime_field_in_file_path: 1
      cloud_key_prefix: "flows/order-feed-samco-bbg-audt-processor"
    upstreamTasks:
      - taskName: RecordTransformer
        mapped: false
        key: file_splitter_result_list
  - path: swarm_tasks.io.write.aws.s3_upload_file:S3UploadFile
    name: S3UploadFile
    upstreamTasks:
      - taskName: S3FileListFileSplitterResultList
        mapped: true
        key: upload_target
  # Noop
  - path: swarm_tasks.control_flow.noop:Noop
    name: Noop