# Confluence Page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2642083893/DRAFT+2+Order+Bloomberg+AUDT+SHELL-SAMCo
# Bundle path -> order/feed/shell/processor
id: order-feed-samco-bbg-audt-processor
name: Order Feed SAMCo BBG AUDT Processor
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  mapped: true
  merge: false
tasks:
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url

# GPG Decryptor which decrypts PGP and GPG files and returns an ExtractPathResult containing the
# decrypted file. If the file is not a PGP/GPG file, it returns the ExtractPathResult from S3OrLocalFile.
- path: swarm_tasks.io.read.gpg_decryptor:GpgDecryptor
  name: GpgDecryptor
  params:
    num_private_key_params: 2
  upstreamTasks:
    - taskName: S3OrLocalFile
      mapped: false
      key: extractor_result

- path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
  name: CsvFileSplitter
  params:
    chunksize: 20000
    detect_encoding: true
  upstreamTasks:
    - taskName: GpgDecryptor
      mapped: false
      key: extractor_result
# Normalize input data
- path: swarm_tasks.io.read.batch_producer:BatchProducer
  name: BatchProducer
  params:
    source_schema:
      "C_ACCOUNT(8)": string
      "C_ASSET_CLASS(32)": string
      "C_AUDITIDTKT(10)": string
      "C_BASKETNAME(36)": string
      "C_BBID(32)": string
      "C_BKRTRADCAP(30)": string
      "C_BLOOMBERGID(20)": string
      "C_BROKER(10)": string
      "C_BROKER_LEI(21)": string
      "C_CLIENT_ORDER_ID(80)": string
      "C_CURRENCY(3)": string
      "C_DELIVTYPE(2)": string
      "C_EVENT(30)": string
      "C_EXCHCODE(32)": string
      "C_EXECBROKER(10)": string
      "C_EXECINSTR(80)": string
      "C_EXTERNAL_ORDER_ID(40)": string
      "C_FIGI(32)": string
      "C_FUNCTION(4)": string
      "C_IDENTIFIER(12)": string
      "C_INSTR(44)": string
      "C_ISIN(12)": string
      "C_LASTMARKET(4)": string
      "C_OLDEXECINSTR(80)": string
      "C_OLDINSTR(44)": string
      "C_ORDERNAME(36)": string
      "C_ORDSTATUS(30)": string
      "C_ORDTYPE(4)": string
      "C_PARSEKEY(30)": string
      "C_REASONCODE(2)": string
      "C_RECORDTYPE(4)": string
      "C_ROUTE_INSTRUCTION(44)": string
      "C_SECDESC(32)": string
      "C_SECURITY(30)": string
      "C_SIDE(5)": string
      "C_STRATNAME(12)": string
      "C_SYSUTCDATETIME(80)": string
      "C_TICKER(10)": string
      "C_TIF(3)": string
      "C_TRADDESK(8)": string
      "C_TRADEDCUR(4)": string
      "C_TRN(80)": string
      "C_UPFEE(50)": string
      "C_UPFEE_CURR(3)": string
      "C_USER(12)": string
      "C_WAIVERINDICATORS(80)": string
      "D_DATE(8)": string
      "D_OLDMATURDATE(8)": string
      "D_SETTLEDT(8)": string
      "D_TERMDATE(8)": string
      "F_CONTRACT_SIZE(20)": float
      "F_EXECFILLAMT(20)": float
      "F_EXECLFILLAMT(20)": float
      "F_EXECLFILLPRC(20)": float
      "F_FILL(20)": float
      "F_FULLQUANTITY(20)": float
      "F_OLDQUANTITY(20)": float
      "F_PRICE(20)": float
      "F_PRINCIPAL(20)": float
      "F_QUANTITY(20)": float
      "I_AGGRFROM(10)": string
      "I_AGGRTO(10)": string
      "I_AUDITID(16)": string
      "I_BASKETNO(10)": string
      "I_IDENTTYPE(3)": string
      "I_MTKTNUM(10)": string
      "I_PMUUID(16)": string
      "I_TKTNUM(10)": string
      "I_TRADERUUID(32)": string
      "I_TSORDNUM(10)": string
      "I_USERUUID(16)": string
      "T_TIME(8)": string
      "__order_status__": string
    remove_unknown_columns: true
  upstreamTasks:
  - taskName: CsvFileSplitter
    mapped: true
    flatten: true
    key: file_splitter_result
# Skip Logic for extra lines in file
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: SkipRowsErroneousRecords
  params:
    query: "~`I_AUDITID(16)`.str.contains('AUDT|AUDIT|09END')"
    skip_on_empty: true
    audit_skipped_rows: true
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
# Skip Logic for keeping only certain Event Types
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: SkipRowsEvent
  params:
    query: "~(`C_EVENT(30)`.str.fullmatch('PRICE CHANGE', case=False, na=False) & `I_TSORDNUM(10)`.isnull()) & (`C_EVENT(30)`.notnull()) & ~(`C_EVENT(30)`.str.fullmatch('AIM TO RHUB|TRADE TICKET CREATE|TRADE APPROVED|CORPORATE ACTION|CPN TICKET CREATION|COMPLIANCE STATUS: APPROVED|COMPLIANCE STATUS: PASSED|COMPLIANCE STATUS: PENDING|FACTOR/COUPON UPDATE|FACTOR UPDATE|ALLOCATION SENT|ORDER TIF CHANGED|MANUAL ALLOCATION',case=False, na=False))"
    skip_on_empty: true
    audit_skipped_rows: true
  upstreamTasks:
    - taskName: SkipRowsErroneousRecords
      mapped: true
      key: result

# Searches the tenants order index for buy sell information for some orders
- path: swarm_tasks.order.feed.shell.fetch_order_sides:FetchOrderSides
  name: FetchOrderSides
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: SkipRowsEvent
      mapped: true
      key: result

# Searches the tenants order index for information for aggregate orders with null values
- path: swarm_tasks.order.feed.shell.fetch_aggregated_order_info:FetchAggregatedOrderInfo
  name: FetchAggregatedOrderInfo
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: SkipRowsEvent
      mapped: true
      key: result

# Searches the tenants order index for client information for some orders
- path: swarm_tasks.order.feed.shell.fetch_aggregated_order_clients:FetchAggregatedOrderClients
  name: FetchAggregatedOrderClients
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: SkipRowsEvent
      mapped: true
      key: result

# Primary Transformations
- path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
  name: PrimaryTransformation
  upstreamTasks:
    - taskName: SkipRowsEvent
      mapped: true
      key: result
    - taskName: FetchOrderSides
      mapped: true
      key: order_sides
    - taskName: FetchAggregatedOrderInfo
      mapped: true
      key: aggregate_order_info
    - taskName: FetchAggregatedOrderClients
      mapped: true
      key: aggregated_order_clients
# Link Parties
- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkParties
  params:
    identifiers_path: marketIdentifiers.parties
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryTransformation
      mapped: true
      key: result
# Instrument Fallback
- path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
  name: InstrumentFallback
  params:
    instrument_fields_map:
      # Bond
      - source_field: __bond_maturity_date__
        target_field: bond.maturityDate
      # Derivative
      - source_field: __delivery_type__
        target_field: derivative.deliveryType
      - source_field: "F_CONTRACT_SIZE(20)"
        target_field: derivative.priceMultiplier
      - source_field: __expiry_date_fallback__
        target_field: derivative.expiryDate
      # Ext
      - source_field: "C_BBID(32)"
        target_field: ext.alternativeInstrumentIdentifier
      - source_field: "C_ASSET_CLASS(32)"
        target_field: ext.bestExAssetClassMain
      - source_field: "C_FIGI(32)"
        target_field: ext.figi
      - source_field: "C_PARSEKEY(30)"
        target_field: ext.instrumentUniqueIdentifier
      - source_field: "C_TICKER(10)"
        target_field: ext.exchangeSymbolBbg
      # Venue
      - source_field: "C_IDENTIFIER(12)"
        target_field: venue.financialInstrumentShortName
      # Top-level fields
      - source_field: __instr_full_name__
        target_field: instrumentFullName
      - source_field: __isin__
        target_field: instrumentIdCode
      - source_field: __is_created_through_fallback__
        target_field: isCreatedThroughFallback
    str_to_bool_dict:
      "on": True
      "off": False
  upstreamTasks:
    - taskName: PrimaryTransformation
      mapped: true
      key: result
# Party Fallback with LEI lookup
- path: swarm_tasks.order.generic.party_fallback_with_lei_lookup:PartyFallbackWithLeiLookup
  name: PartyFallbackWithLeiLookup
  upstreamTasks:
    - taskName: PrimaryTransformation
      mapped: true
      key: result
    - taskName: LinkParties
      mapped: true
      key: parties
# Link each OrderState record to the parent Order record
- path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
  name: AssignMetaParent
  params:
      parent_model_attribute: _order.__meta_model__
      parent_attributes_prefix: _order.
      target_attribute: _orderState.__meta_parent__
  upstreamTasks:
    - taskName: PrimaryTransformation
      mapped: true
      key: result
# Auxiliary frame concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: AuxiliaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
      - marketIdentifiers.instrument
      - marketIdentifiers.parties
      - __is_created_through_fallback__
      - __option_strike_price__
      - __from_currency_code__
      - __option_type_mapped__
      - __instr_quantity_notation__
      - __notional_currency_2__
      - __expiry_date__
      - __expiry_date_fallback__
      - __delivery_type__
      - __bond_maturity_date__
      - "C_TICKER(10)"
      - "C_IDENTIFIER(12)"
      - "C_ASSET_CLASS(32)"
      - "C_BBID(32)"
      - "F_CONTRACT_SIZE(20)"
      - "C_FIGI(32)"
      - "__isin__"
      - "C_PARSEKEY(30)"
      - __instr_full_name__
      - __fallback_buyer__
      - __fallback_buyer_dec_maker__
      - __fallback_client__
      - __fallback_counterparty__
      - __fallback_executing_entity__
      - __fallback_exec_within_firm__
      - __fallback_inv_dec_in_firm__
      - __fallback_seller__
      - __fallback_seller_dec_maker__
      - __fallback_trader__
  upstreamTasks:
    - taskName: PrimaryTransformation
      mapped: true
      key: primary_transformations
    - taskName: PartyFallbackWithLeiLookup
      mapped: true
      key: parties
    - taskName: InstrumentFallback
      mapped: true
      key: instrument_fallback
    - taskName: AssignMetaParent
      mapped: true
      key: parent_id
# Filter only Order records into a data frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderRecords
  params:
    except_prefix: _orderState.
    strip_prefix: true
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenator
    mapped: true
    key: result
# Filter only OrderState records into a data frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderStateRecords
  params:
    except_prefix: _order.
    strip_prefix: true
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenator
    mapped: true
    key: result
# Strip prefix `_order.` from the column names of OrderRecords frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrder
  params:
    action: strip
    prefix: _order.
  upstreamTasks:
  - taskName: OrderRecords
    mapped: true
    key: result
# Strip prefix `_orderState.` from the column names of OrderState Records frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrderState
  params:
    action: strip
    prefix: _orderState.
  upstreamTasks:
  - taskName: OrderStateRecords
    mapped: true
    key: result
# Vertically concatenate Order and OrderState Record frames created above.
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: VerticalConcatenator
  params:
    orient: vertical
    reset_index: true
    drop_index: true
  upstreamTasks:
  - taskName: StripPrefixOrder
    mapped: true
    key: order_records
  - taskName: StripPrefixOrderState
    mapped: true
    key: order_state_records
# Remove InvalidOrderStates and Synthetic NEWOs
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: RemoveInvalidOrderStates
  params:
    query: "`executionDetails.orderStatus`.notnull() & ~((`executionDetails.orderStatus`.isin(['NEWO']) & `__synth_order__` == False))"
  upstreamTasks:
  - taskName: VerticalConcatenator
    mapped: true
    key: result
# Remove duplicate new orders, remove synthetic new orders already in Elasticsearh
- path: swarm_tasks.order.generic.remove_duplicate_newo:RemoveDuplicateNEWO
  name: RemoveDupNEWO
  params:
    newo_in_file_col: __newo_in_file__
    drop_newo_in_file_col: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: RemoveInvalidOrderStates
      mapped: true
      key: result
#Best Execution
- path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
  name: BestExecution
  resources:
    es_client_key: reference-data
  upstreamTasks:
    - taskName: RemoveDupNEWO
      mapped: true
      key: result
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: BestExecutionConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __synth_order__
  upstreamTasks:
    - taskName: RemoveDupNEWO
      mapped: true
      key: result
    - taskName: BestExecution
      mapped: true
      key: best_ex_result
# Assign Meta
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: AssignMeta
  params:
    model_attribute: __meta_model__
    parent_attribute: __meta_parent__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result
# Post-meta concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PostMetaConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
      - __meta_parent__
  upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result
    - taskName: AssignMeta
      mapped: true
      key: meta
- path: swarm_tasks.generic.extract_batch_from_frame_producer_result:ExtractBatchFromFrameProducerResult
  name: ExtractBatchFromFrameProducerResult
  params:
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: frame_producer_result
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: ExtractBatchFromFrameProducerResult
      mapped: true
      key: batch_index
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsent
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: ElasticBulkTransformer
      mapped: true
      key: result
# Instrument Mapper
- path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
  name: InstrumentMapper
  upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: source_frame
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineCondition
  upstreamTasks:
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformer
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: ExtractBatchFromFrameProducerResult
      mapped: true
      key: batch_index
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsent
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: QuarantinedElasticBulkTransformer
      mapped: true
      key: result
- path: swarm_tasks.control_flow.noop:Noop
  name: Noop
