# This flow checks for the corresponding pair file and creates a file for
# 'order-feed-fidessa' flow if the pair file is present and raise SKIP if not found.
# Confluence page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2101215233/Order+Fidessa+Controller
id: order-feed-fidessa-controller
name: Order Feed Fidessa Controller
# infra not needed for this flow. But exists as flow runner fails if infra not provided.
infra:
  - name: tenant-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process
tasks:
  - path: swarm_tasks.order.feed.fidessa.controller.fidessa_controller:FidessaController
    name: FidessaController
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.write.aws.s3_upload_file:S3UploadFile
    name: S3UploadFile
    upstreamTasks:
      - taskName: FidessaController
        mapped: false
        key: upload_target