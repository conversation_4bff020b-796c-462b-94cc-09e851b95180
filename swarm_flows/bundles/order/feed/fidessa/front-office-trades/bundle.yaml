# Confluence Page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2173599812/Order+Fidessa+Front+Office+Trades
# Bundle path -> order/feed/fidessa/front-office-trades
id: order-feed-fidessa-front-office-trades
name: Fidessa Front Office Trades
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  mapped: true
  merge: false
tasks:
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
    - taskName: file_url
      mapped: false
      key: file_url
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
    - taskName: file_url
      mapped: false
      key: file_url
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
    - taskName: file_url
      mapped: false
      key: file_url

# CSV File splitter
- path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
  name: CsvFileSplitter
  params:
    chunksize: 10000
    detect_encoding: true
    header_file_in_s3: mapping_tables/order-feed-fidessa-processor/fidessa-front-office-trades-header.csv
  upstreamTasks:
    - taskName: S3OrLocalFile
      mapped: false
      key: extractor_result

# Batch Producer
- path: swarm_tasks.io.read.batch_producer:BatchProducer
  name: BatchProducer
  params:
    remove_unknown_columns: true
    source_schema:
      Aggregation_Id: string
      Amended_Datetime: string
      Book_Id: string
      Business_Transaction_Description: string
      Buy_Sell: string
      CFI_Code: string
      Client_LEI: string
      Counterparty: string
      Counterparty_Description: string
      Counterparty_Type: string
      Dealing_Capacity: string
      Dealt_Ccy: string
      Discretionary_Order: string
      Entered_By: string
      Entered_By_User_Group: string
      Entered_Date: string
      Epic_Code: string
      Exchange_Contract_Code: string
      Exchange_Trade_Code: string
      Executing_Book_Id: string
      Execution_Decision_Value: string
      Execution_Venue: string
      Expiry_Date: string
      Expiry_Type: string
      Gross_Price: float
      Instrument_Code: string
      Instrument_Description: string
      Instrument_Type: string
      Investment_Decision_Value: string
      Isin_Code: string
      Official_Place_Of_Listing: string
      Option_Type: string
      Order_Id: string
      Order_Price_Type: string
      Originator_Order_Id: string
      Quantity: float
      Report_To_Exchange: string
      Sedol_Code: string
      Settlement_Ccy: string
      Settlement_Consideration: float
      Settlement_Date: string
      State: string
      Strike_Price: float
      Trade_Datetime: string
      Trade_Id: string
      Trade_Type: string
      Trader: string
      Trading_Entity_Id: string
      Trading_Venue_Transaction_Id: string
      Underlying_ISIN: string
      Version_Number: string
  upstreamTasks:
    - taskName: CsvFileSplitter
      mapped: true
      flatten: true
      key: file_splitter_result

- path: swarm_tasks.generic.extract_batch_from_frame_producer_result:ExtractBatchFromFrameProducerResult
  name: ExtractBatchFromFrameProducerResult
  params:
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: frame_producer_result
# Filter Rows: Skip Logic
- path: swarm_tasks.order.feed.fidessa.front_office_trades.front_office_skip_logic:FrontOfficeSkipLogic
  name: FrontOfficeSkipLogic
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
  # Primary transformations
- path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
  name: FidessaFrontOfficeTransformations
  upstreamTasks:
    - taskName: FrontOfficeSkipLogic
      mapped: true
      key: result

# Assign Meta Parent
- path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
  name: MetaParent
  params:
    parent_model_attribute: _order.__meta_model__
    parent_attributes_prefix: _order.
    target_attribute: _orderState.__meta_parent__
  upstreamTasks:
    - taskName: FidessaFrontOfficeTransformations
      mapped: true
      key: result

# Link Parties
- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkParties
  params:
    identifiers_path: marketIdentifiers.parties
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: FidessaFrontOfficeTransformations
      mapped: true
      key: result

  # Parties Fallback
- path: swarm_tasks.order.generic.parties_fallback:PartiesFallback
  name: PartiesFallback
  resources:
    es_client_key: tenant-data
  params:
    executing_entity_attribute: __trading_entity_without_prefix__
    trader_attribute: __trader_without_prefix__
    client_attribute: __client_without_prefix__
    counterparty_attribute: __client_without_prefix__
    investment_decision_maker_attribute: __investment_decision_without_prefix__
    execution_within_firm_attribute: __execution_decision_without_prefix__
    use_buy_mask_for_buyer_seller: true
    buy_sell_side_attribute: transactionDetails.buySellIndicator
    buyer_attribute: __trading_entity_without_prefix__
    seller_attribute: __client_without_prefix__
  upstreamTasks:
    - taskName: FidessaFrontOfficeTransformations
      mapped: true
      key: result
    - taskName: LinkParties
      mapped: true
      key: link_parties_result

# Link Instruments
- path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
  name: LinkInstrument
  params:
    identifiers_path: marketIdentifiers.instrument
    venue_attribute: transactionDetails.ultimateVenue
    currency_attribute: transactionDetails.priceCurrency
  resources:
    ref_data_key: reference-data
    tenant_data_key: tenant-data
  upstreamTasks:
    - taskName: FidessaFrontOfficeTransformations
      mapped: true
      key: result

- path: swarm_tasks.transform.map.map_to_nested:MapToNested
  name: OverrideInstrumentName
  params:
    source_attribute: __override_instrument_description__
    nested_path: instrumentFullName
    target_attribute: instrumentDetails.instrument
  upstreamTasks:
    - taskName: LinkInstrument
      mapped: true
      key: result
    - taskName: FidessaFrontOfficeTransformations
      mapped: true
      key: transformations

# Instrument Fallback
- path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
  name: InstrumentFallback
  params:
    instrument_fields_map:
      # Derivative
      - source_field: __fallback_exercise_style__
        target_field: derivative.optionExerciseStyle
      - source_field: __fallback_option_type__
        target_field: derivative.optionType
      - source_field: __fallback_price_multiplier__
        target_field: derivative.priceMultiplier
      - source_field: __fallback_strike_price__
        target_field: derivative.strikePrice
      - source_field: __fallback_strike_price_ccy__
        target_field: derivative.strikePriceCurrency
      # Ext
      - source_field: __fallback_strike_price_type__
        target_field: ext.strikePriceType
      - source_field: __fallback_id_code__
        target_field: ext.instrumentIdCodeType
      # Top-level fields
      - source_field: __fallback_instr_class__
        target_field: instrumentClassification
      - source_field: __fallback_instr_full_name__
        target_field: instrumentFullName
      - source_field: __fallback_is_created_through_fb__
        target_field: isCreatedThroughFallback
    str_to_bool_dict:
      "on": True
      "off": False
  upstreamTasks:
    - taskName: FidessaFrontOfficeTransformations
      mapped: true
      key: result
    - taskName: OverrideInstrumentName
      mapped: true
      key: override_instrument_name

  # Primary Frame Conatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PrimaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
      - marketIdentifiers.instrument
      - marketIdentifiers.parties
      - __fallback_currency__
      - __fallback_exercise_style__
      - __fallback_id_code__
      - __fallback_instr_class__
      - __fallback_instr_full_name__
      - __fallback_instr_id_code__
      - __fallback_is_created_through_fb__
      - __fallback_option_type__
      - __fallback_price_multiplier__
      - __fallback_strike_price__
      - __fallback_strike_price_ccy__
      - __fallback_strike_price_type__
      - __client_without_prefix__
      - __execution_decision_without_prefix__
      - __investment_decision_without_prefix__
      - __counterparty_desc_without_prefix__
      - __fallback_expiry_date__
      - __trader_without_prefix__
      - __trading_entity_without_prefix__
      - __override_instrument_description__
      - asset_class_attribute # Instrument Identifiers columns
      - bbg_figi_id_attribute
      - currency_attribute
      - eurex_id_attribute
      - exchange_symbol_attribute
      - expiry_date_attribute
      - interest_rate_start_date_attribute
      - isin_attribute
      - notional_currency_1_attribute
      - notional_currency_2_attribute
      - option_strike_price_attribute
      - option_type_attribute
      - swap_near_leg_date_attribute
      - underlying_index_name_attribute
      - underlying_index_name_leg_2_attribute
      - underlying_index_series_attribute
      - underlying_index_term_attribute
      - underlying_index_term_value_attribute
      - underlying_index_version_attribute
      - underlying_isin_attribute
      - underlying_symbol_attribute
      - underlying_symbol_expiry_code_attribute
      - underlying_index_term_leg_2_attribute
      - underlying_index_term_value_leg_2_attribute
      - venue_attribute
      - venue_financial_instrument_short_name_attribute
      - instrument_classification_attribute
  upstreamTasks:
    - taskName: FidessaFrontOfficeTransformations
      mapped: true
      key: result
    - taskName: MetaParent
      mapped: true
      key: meta_parent
    - taskName: PartiesFallback
      mapped: true
      key: party_fallback
    - taskName: InstrumentFallback
      mapped: true
      key: instrument_fallback

# Filter only OrderRecords into a frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderRecords
  params:
    except_prefix: _orderState.
    strip_prefix: true
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    mapped: true
    key: result
# Filter only OrderStateRecords into a frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderStateRecords
  params:
    except_prefix: _order.
    strip_prefix: true
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: result
# Strip prefix `_order.` from the column names of OrderRecords frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrder
  params:
    action: strip
    prefix: _order.
  upstreamTasks:
    - taskName: OrderRecords
      mapped: true
      key: result
# Strip prefix `orderState.` from the column names of OrderState Records frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrderState
  params:
    action: strip
    prefix: _orderState.
  upstreamTasks:
    - taskName: OrderStateRecords
      mapped: true
      key: result
# Vertically concatenate Order and OrderState Record frames created above.
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: VerticalConcatenator
  params:
    orient: vertical
    reset_index: true
    drop_index: true
  upstreamTasks:
    - taskName: StripPrefixOrder
      mapped: true
      key: order_records
    - taskName: StripPrefixOrderState
      mapped: true
      key: order_state_records

# We shouldn't create synthetic NEWOs for PARFs. So any synthetic NEWOs created should
# be dropped
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: RemoveSyntheticNEWOsForPARFs
  params:
    query: "~(`executionDetails.orderStatus` == 'NEWO' & `__newo_in_file__` == False)"
  upstreamTasks:
    - taskName: VerticalConcatenator
      mapped: true
      key: result
# Remove Invalid Order States
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: RemoveInvalidOrderStates
  params:
    query: "`executionDetails.orderStatus`.notnull()"
  upstreamTasks:
    - taskName: RemoveSyntheticNEWOsForPARFs
      mapped: true
      key: result

# Best-execution tasks
- path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
  name: BestExecution
  resources:
    es_client_key: reference-data
  upstreamTasks:
    - taskName: RemoveInvalidOrderStates
      mapped: true
      key: result
# Best-Ex results concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: BestExecutionConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __newo_in_file__
  upstreamTasks:
    - taskName: BestExecution
      mapped: true
      key: best_ex_result
    - taskName: RemoveInvalidOrderStates
      mapped: true
      key: orders_and_orderstates_final
# Assign Meta
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: AssignMeta
  params:
    model_attribute: __meta_model__
    parent_attribute: __meta_parent__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result
# Post-meta concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PostMetaConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
      - __meta_parent__
  upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result
    - taskName: AssignMeta
      mapped: true
      key: meta

- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: ExtractBatchFromFrameProducerResult
      mapped: true
      key: batch_index
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsent
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: ElasticBulkTransformer
      mapped: true
      key: result
# Instrument Mapper
- path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
  name: InstrumentMapper
  upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: source_frame
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineCondition
  upstreamTasks:
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformer
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: ExtractBatchFromFrameProducerResult
      mapped: true
      key: batch_index
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsent
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: QuarantinedElasticBulkTransformer
      mapped: true
      key: result
- path: swarm_tasks.control_flow.noop:Noop
  name: Noop
