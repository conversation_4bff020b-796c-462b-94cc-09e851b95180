# Confluence Page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2989785089/Order+Bloomberg+AUDT+V2+w+RIMES+-+TOM
# Bundle path -> order/feed/bbg/audt/processor
id: order-feed-bbg-audt-processor
name: Order Feed BBG AUDT Processor
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  mapped: true
  merge: false
tasks:
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url

- path: swarm_tasks.order.feed.bbg.audt.data_source_name_mapper:DataSourceNameMapper
  name: DataSourceNameMapper
  upstreamTasks:
    - taskName: S3OrLocalFile
      mapped: false
      key: extractor_result

- path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
  name: CsvFileSplitter
  params:
    chunksize: 20000
    detect_encoding: true
  upstreamTasks:
    - taskName: S3OrLocalFile
      mapped: false
      key: extractor_result
    - taskName: DataSourceNameMapper
      mapped: false
      key: data_source_name_map

# Normalize input data
- path: swarm_tasks.io.read.batch_producer:BatchProducer
  name: BatchProducer
  params:
    source_schema:
      "C_ACCOUNT(8)": string
      "C_ASOFDATEUTC(20)": string
      "C_ASOFTIMEUTC(20)": string
      "C_ASSET_CLASS(32)": string
      "C_AUDITIDTKT(10)": string
      "C_BASKETNAME(36)": string
      "C_BBID(32)": string
      "C_BKRTRADCAP(30)": string
      "C_BLOOMBERGID(20)": string
      "C_BROKER(10)": string
      "C_BROKER_LEI(21)": string
      "C_CLIENT_ORDER_ID(80)": string
      "C_CURRENCY(3)": string
      "C_DELIVTYPE(2)": string
      "C_EVENT(30)": string
      "C_EXCHCODE(32)": string
      "C_EXECASOFDATE(10)": string
      "C_EXECASOFTIME(8)": string
      "C_EXECBROKER(10)": string
      "C_EXECINSTR(80)": string
      "C_EXECPLATFORM(20)": string
      "C_EXTERNAL_ORDER_ID(40)": string
      "C_FIGI(32)": string
      "C_FILLEXECVENUE(11)": string
      "C_FUNCTION(4)": string
      "C_IDENTIFIER(12)": string
      "C_ID_MIC_PRIM_EXCH(4)": string
      "C_INSTR(44)": string
      "C_ISIN(12)": string
      "C_LASTMARKET(4)": string
      "C_LAST_CAPACITY(10)": string
      "C_OLDEXECINSTR(80)": string
      "C_OLDINSTR(44)": string
      "C_ORDERNAME(36)": string
      "C_ORDSTATUS(30)": string
      "C_ORDTYPE(4)": string
      "C_PARSEKEY(30)": string
      "C_PM(12)": string
      "C_QUOTETYPE(10)": string
      "C_REASONCODE(2)": string
      "C_RECORDTYPE(4)": string
      "C_ROUTE_INSTRUCTION(44)": string
      "C_SECDESC(32)": string
      "C_SECURITY(30)": string
      "C_SIDE(5)": string
      "C_STRATNAME(12)": string
      "C_SW_TYPE(30)": string
      "C_SYSUTCDATETIME(80)": string
      "C_TICKER(10)": string
      "C_TIF(3)": string
      "C_TRADDESK(8)": string
      "C_TRADEDCUR(4)": string
      "C_TRADER(12)": string
      "C_TRN(80)": string
      "C_UPFEE(50)": string
      "C_UPFEE_CURR(3)": string
      "C_USER(12)": string
      "C_WAIVERINDICATORS(80)": string
      "D_ASOFDATE(8)": string
      "D_DATE(8)": string
      "D_OLDMATURDATE(8)": string
      "D_SETTLEDT(8)": string
      "D_TERMDATE(8)": string
      "F_CONTRACT_SIZE(20)": float
      "F_EXECFILLAMT(20)": float
      "F_EXECLFILLAMT(20)": float
      "F_EXECLFILLPRC(20)": float
      "F_FACTOR(20)": float
      "F_FILL(20)": float
      "F_FULLQUANTITY(20)": float
      "F_OLDQUANTITY(20)": float
      "F_PRICE(20)": float
      "F_PRINCIPAL(20)": float
      "F_QUANTITY(20)": float
      "I_AGGRFROM(10)": string
      "I_AGGRTO(10)": string
      "I_AUDITID(16)": string
      "I_BASKETNO(10)": string
      "I_FLAG(2)": string
      "I_IDENTTYPE(3)": string
      "I_MTKTNUM(10)": string
      "I_PMUUID(16)": string
      "I_TIME_MS(3)": string
      "I_TKTNUM(10)": string
      "I_TRADERUUID(32)": string
      "I_TSORDNUM(10)": string
      "I_USERUUID(16)": string
      "T_ASOFTIME(8)": string
      "T_TIME(8)": string
    remove_unknown_columns: true
  upstreamTasks:
    - taskName: CsvFileSplitter
      mapped: true
      flatten: true
      key: file_splitter_result

  # Tenant Input for PrimaryTransformations
- path: swarm_tasks.primary_transformations.input_tasks.tenant_input:TenantInput
  name: TenantInput

  # ES Client Input for PrimaryTransformations
- path: swarm_tasks.primary_transformations.input_tasks.es_client_input:EsClientInput
  name: EsClientInput
  resources:
    es_client_key: tenant-data

# Skip Logic for extra lines in file
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: SkipRowsErroneousRecords
  params:
    query: "~`I_AUDITID(16)`.str.contains('AUDT|AUDIT|09END')"
    skip_on_empty: true
    audit_skipped_rows: true
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result

# ES Lookup for previously ingested NEWO data
- path: swarm_tasks.order.feed.bbg.audt.es_data_lookup:BBGAudtESDataLookup
  name: BBGAudtESDataLookup
  upstreamTasks:
    - taskName: SkipRowsErroneousRecords
      mapped: true
      key: result
    - taskName: TenantInput
      mapped: false
      key: tenant
    - taskName: EsClientInput
      mapped: false
      key: es_client

# Primary Transformations
- path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
  name: PrimaryTransformations
  upstreamTasks:
    - taskName: SkipRowsErroneousRecords
      mapped: true
      key: result
    - taskName: BBGAudtESDataLookup
      mapped: true
      key: ESLookup
    - taskName: file_url
      mapped: false
      key: source_file_uri
    - taskName: TenantInput
      mapped: false
      key: tenant
    - taskName: EsClientInput
      mapped: false
      key: es_client
    - taskName: DataSourceNameMapper
      mapped: false
      key: data_source_name_map

# Remove Null Order Status
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: RemoveInvalidOrderStates
  params:
    query: "`executionDetails.orderStatus`.notnull()"
  upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: result

# Keep only meta parent columns
- path: swarm_tasks.transform.frame.filter_columns:FilterColumns
  name: KeepOnlyMetaParentColumns
  params:
    action: keep
    columns:
      - __meta_parent__id
      - __meta_parent__buySell
      - __meta_parent__executionDetails.orderStatus
      - ___parent_meta_model__
  upstreamTasks:
    - taskName: RemoveInvalidOrderStates
      mapped: true
      key: result

# Link each OrderState record to the parent Order record
- path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
  name: AssignMetaParent
  params:
      parent_model_attribute: ___parent_meta_model__
      parent_attributes_prefix: __meta_parent__
      target_attribute: __meta_parent__
  upstreamTasks:
    - taskName: KeepOnlyMetaParentColumns
      mapped: true
      key: result

# Remove meta parent from NEWO orders
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: RemoveNEWOMetaParent
  params:
    target_attribute: __parent_meta_model_clean__
    cases:
      - query: "`__meta_model__`.str.fullmatch('OrderState',case=False,na=False)"
        attribute: __meta_parent__
  upstreamTasks:
    - taskName: RemoveInvalidOrderStates
      mapped: true
      key: result
    - taskName: AssignMetaParent
      mapped: true
      key: meta_parent

# Link Parties
- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkParties
  params:
    identifiers_path: marketIdentifiers.parties
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: RemoveInvalidOrderStates
      mapped: true
      key: result

# Party Fallback with LEI lookup
- path: swarm_tasks.order.generic.party_fallback_with_lei_lookup:PartyFallbackWithLeiLookup
  name: PartyFallbackWithLeiLookup
  upstreamTasks:
    - taskName: RemoveInvalidOrderStates
      mapped: true
      key: result
    - taskName: LinkParties
      mapped: true
      key: link_parties_result

# Link Instrument
- path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
  name: LinkInstrument
  params:
    identifiers_path: marketIdentifiers.instrument
    venue_attribute: transactionDetails.venue
    currency_attribute: transactionDetails.priceCurrency
  resources:
    ref_data_key: reference-data
    tenant_data_key: tenant-data
  upstreamTasks:
    - taskName: RemoveInvalidOrderStates
      mapped: true
      key: result

# Instrument Fallback
- path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
  name: InstrumentFallback
  params:
    cfi_and_bestex_from_instrument_classification: true
    instrument_fields_map:
      - source_field: __instr_fb_is_created_through_fb__
        target_field: isCreatedThroughFallback
      - source_field: __instr_fb_instrument_classification__
        target_field: instrumentClassification
      - source_field: __instr_fb_instrument_full_name__
        target_field: instrumentFullName
      - source_field: isin_attribute
        target_field: instrumentIdCode
      - source_field: currency_attribute
        target_field: notionalCurrency1
        # bond fields
      - source_field: __instr_fb_maturity_date__
        target_field: bond.maturityDate
        # derivative fields
      - source_field: expiry_date_attribute
        target_field: derivative.expiryDate
        # ext fields
      - source_field: __instr_fb_ext_instrument_unique_identifier__
        target_field: ext.instrumentUniqueIdentifier
      - source_field: transactionDetails.priceNotation
        target_field: ext.priceNotation
      - source_field: transactionDetails.quantityNotation
        target_field: ext.quantityNotation
      - source_field: venue_attribute
        target_field: ext.venueName
      - source_field: __best_ex_asset_class_main__
        target_field: ext.bestExAssetClassMain
      - source_field: __best_ex_asset_class_sub__
        target_field: ext.bestExAssetClassSub
        # fxDerivatives fields
      - source_field: transactionDetails.quantityCurrency
        target_field: fxDerivatives.notionalCurrency2
        # venue fields
      - source_field: venue_attribute
        target_field: venue.tradingVenue
    str_to_bool_dict:
      "true": True
      "false": False
  upstreamTasks:
    - taskName: RemoveInvalidOrderStates
      mapped: true
      key: result
    - taskName: LinkInstrument
      mapped: true
      key: link_instrument

# Frame Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PrimaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
      - asset_class_attribute
      - bbg_figi_id_attribute
      - currency_attribute
      - expiry_date_attribute
      - isin_attribute
      - notional_currency_2_attribute
      - underlying_symbol_attribute
      - venue_attribute
      - instrument_classification_attribute
      - underlying_symbol_expiry_code_attribute
      - __best_ex_asset_class_main__
      - __best_ex_asset_class_sub__
      - __meta_parent__id
      - __meta_parent__buySell
      - __meta_parent__executionDetails.orderStatus
      - ___parent_meta_model__
      - __instr_fb_ext_instrument_unique_identifier__
      - __instr_fb_instrument_classification__
      - __instr_fb_instrument_full_name__
      - __instr_fb_is_created_through_fb__
      - __instr_fb_maturity_date__
      - __fallback_buyer__
      - __fallback_buyer_dec_maker__
      - __fallback_client__
      - __fallback_counterparty__
      - __fallback_executing_entity__
      - __fallback_exec_within_firm__
      - __fallback_inv_dec_in_firm__
      - __fallback_seller__
      - __fallback_seller_dec_maker__
      - __fallback_trader__
  upstreamTasks:
    - taskName: RemoveInvalidOrderStates
      mapped: true
      key: result
    - taskName: InstrumentFallback
      mapped: true
      key: instrument_fallback
    - taskName: PartyFallbackWithLeiLookup
      mapped: true
      key: party_fallback
    - taskName: RemoveNEWOMetaParent
      mapped: true
      key: meta_parent

### update newo parties section START ###
# Inherit Parties
- path: swarm_tasks.order.feed.bbg.audt.inherit_parties:InheritParties
  name: InheritParties
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: result
    - taskName: EsClientInput
      mapped: false
      key: es_client
    - taskName: TenantInput
      mapped: false
      key: tenant

# Assign meta fields
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: AssignMetaUpdate
  params:
    model_attribute: __meta_model__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: InheritParties
      mapped: true
      key: result

# Auxiliary Frame Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: FrameConcatenatorUpdate
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
  upstreamTasks:
    - taskName: InheritParties
      mapped: true
      key: result
    - taskName: AssignMetaUpdate
      mapped: true
      key: meta_assignment_result

#   Elastic Bulk Transformer for updates
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformerUpdate
  params:
    action_type: index
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: FrameConcatenatorUpdate
      mapped: true
      key: transform_result
    - taskName: InheritParties
      mapped: true
      key: producer_result

# Elastic Bulk Writer for Updates
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: ElasticBulkWriterUpdate
  params:
    payload_size: 10000000
    action_type: index
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: ElasticBulkTransformerUpdate
      mapped: true
      key: result
### update newo parties section END ###

#Best Execution
- path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
  name: BestExecution
  resources:
    es_client_key: reference-data
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: result

# Concatenate Best Ex
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: BestExecutionConcatenator
  params:
    orient: horizontal
    drop_columns:
    - marketIdentifiers.instrument
    - marketIdentifiers.parties
    - es_&id
    - es_marketIdentifiers
    - es_reportDetails.executingEntity.fileIdentifier
    - es_buyerFileIdentifier
    - es_buyerDecisionMakerFileIdentifier
    - es_sellerFileIdentifier
    - es_sellerDecisionMakerFileIdentifier
    - es_clientFileIdentifier
    - es_counterpartyFileIdentifier
    - es_traderFileIdentifier
    - es_tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier
    - es_tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: result
    - taskName: BestExecution
      mapped: true
      key: best_ex_result

# Assign Meta
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: AssignMeta
  params:
    model_attribute: __meta_model__
    parent_attribute: __parent_meta_model_clean__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result

# Post-meta concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: FinalConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
      - __parent_meta_model_clean__
  upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result
    - taskName: AssignMeta
      mapped: true
      key: meta

# Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: FinalConcatenator
      mapped: true
      key: transform_result
    - taskName: BatchProducer
      mapped: true
      key: producer_result

# Elastic Bulk Writer
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsent
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: ElasticBulkTransformer
      mapped: true
      key: result

  # Instrument Mapper
- path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
  name: InstrumentMapper
  upstreamTasks:
    - taskName: FinalConcatenator
      mapped: true
      key: source_frame
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result

# Quarantine Condition
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineCondition
  upstreamTasks:
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result

# Quarantine Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformer
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: FinalConcatenator
      mapped: true
      key: transform_result
    - taskName: BatchProducer
      mapped: true
      key: producer_result
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result

# Quarantine Elastic Bulk Writer
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsent
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: QuarantinedElasticBulkTransformer
      mapped: true
      key: result

- path: swarm_tasks.control_flow.noop:Noop
  name: Noop