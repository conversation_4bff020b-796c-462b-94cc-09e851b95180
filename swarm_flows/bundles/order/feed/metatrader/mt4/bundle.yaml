# Confluence page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2296119495/Order+rts22Transaction+MetaTrader+4+MT4
id: order-metatrader-mt4-trades
name: Order MetaTrader MT4 Trades
infra:
  - name: tenant-data
    type: ELASTICSEARCH
  - name: reference-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process
controlFlows:
  - name: S3OrLocalFile
    conditionTaskName: ParametersFlowController
    trueTaskName: S3DownloadFile
    falseTaskName: LocalFile
    merge: true
  - name: NeedsQuarantine
    conditionTaskName: QuarantineCondition
    trueTaskName: QuarantinedElasticBulkTransformer
    falseTaskName: Noop
    mapped: true
    merge: false
tasks:
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvFileSplitter
    params:
      chunksize: 20000
      normalise_columns: true
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result
  # Batch Producer
  - path: swarm_tasks.io.read.batch_producer:BatchProducer
    name: BatchProducer
    params:
      source_schema:
        "CLOSE_PRICE": float
        "CLOSE_TIME": string
        "CMD": string
        "LOGIN": string
        "MAGIC": string
        "MARGIN_RATE": string
        "OANDA_USER_ID": string
        "OPEN_PRICE": float
        "OPEN_TIME": string
        "SL": string
        "SYMBOL": string
        "TICKET": string
        "TP": string
        "VOLUME": float
    upstreamTasks:
      - taskName: CsvFileSplitter
        mapped: true
        key: file_splitter_result
  # Filter out invalid rows
  - path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
    name: FilterRowsInSourceFile
    params:
      query: "((`CMD`.isin(['0', '1', '2', '3', '4', '5', '6'])) & (`SYMBOL`.notnull()))"
      skip_on_empty: true
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  # Primary transformations
  - path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
    name: PrimaryTransformations
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
  # Link Parties
  - path: swarm_tasks.transform.steeleye.link.parties:LinkParties
    name: LinkParties
    params:
      identifiers_path: marketIdentifiers.parties
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryTransformations
        mapped: true
        key: result
  # Parties Fallback
  - path: swarm_tasks.order.generic.parties_fallback:PartiesFallback
    name: PartiesFallback
    resources:
      es_client_key: tenant-data
    params:
      executing_entity_attribute: __EXECUTING_ENTITY__
      client_attribute: __CLIENT__
      counterparty_attribute: __EXECUTING_ENTITY__
      investment_decision_maker_attribute: __INVESTMENT_DEC_WITHIN_FIRM__
      execution_within_firm_attribute: __INVESTMENT_DEC_WITHIN_FIRM__
      use_buy_mask_for_buyer_seller: true
      buy_sell_side_attribute: executionDetails.buySellIndicator
      buyer_attribute: __CLIENT__
      seller_attribute: __EXECUTING_ENTITY__
      buyer_decision_maker_attribute: __EXECUTING_ENTITY__
      seller_decision_maker_attribute: __CLIENT__
    upstreamTasks:
      - taskName: PrimaryTransformations
        mapped: true
        key: result
      - taskName: LinkParties
        mapped: true
        key: link_parties_result
  # Important Note: This bundle does not use InstrumentIdentifiers and LinkInstrument task
  # Instrument data is directly fetched from static s3 file(in primary_transformation) and
  # mapped using below task
  - path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
    name: PopulateInstruments
    params:
      instrument_fields_map:
         # Ext
        - source_field: __TEMP_INST_ID_INSTRUMENT_UNIQUE_ID__
          target_field: ext.instrumentUniqueIdentifier
        - source_field: __TEMP_INST_ID_EXT_EXCHANGE_SYMBOL__
          target_field: ext.exchangeSymbol
        - source_field: __TEMP_INST_ID_INSTRUMENT_ID_CODE_TYPE__
          target_field: ext.instrumentIdCodeType
        - source_field: __TEMP_INST_ID_PRICE_NOTATION__
          target_field: ext.priceNotation
        - source_field: transactionDetails.quantityNotation
          target_field: ext.quantityNotation
        - source_field: transactionDetails.venue
          target_field: ext.venueName
        # Derivative
        - source_field: __TEMP_INST_ID_DELIVERY_TYPE__
          target_field: derivative.deliveryType
        - source_field: __TEMP_INST_ID_PRICE_MULTIPLIER__
          target_field: derivative.priceMultiplier
        # Venue
        - source_field: transactionDetails.venue
          target_field: venue.tradingVenue
        # fxDerivatives
        - source_field: transactionDetails.quantityCurrency
          target_field: fxDerivatives.notionalCurrency2
        # commodityAndEmissionAllowances
        - source_field: __TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_BASE_PRODUCT__
          target_field: commodityAndEmissionAllowances.baseProduct
        - source_field: __TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_FURTHER_SUB_PRODUCT__
          target_field: commodityAndEmissionAllowances.furtherSubProduct
        # Top-level fields
        - source_field: __TEMP_INST_ID_INSTRUMENT_CLASSIFICATION__
          target_field: instrumentClassification
        - source_field: __TEMP_INST_ID_INSTRUMENT_FULL_NAME__
          target_field: instrumentFullName
        - source_field: transactionDetails.priceCurrency
          target_field: notionalCurrency1
        - source_field: __TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE__
          target_field: instrumentClassificationEMIRContractType
        - source_field: __TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_TYPE__
          target_field: instrumentClassificationEMIRProductType
        - source_field: __TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS__
          target_field: instrumentClassificationEMIRAssetClass
      underlying_instrument_fields_map:
        - source_field: __TEMP_INST_ID_INSTRUMENT_FULL_NAME__
          target_level: ext.underlyingInstruments
          target_field: derivative.underlyingIndexName
        - source_field: __TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID__
          target_level: ext.underlyingInstruments
          target_field: instrumentIdCode
        - source_field: __TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID__
          target_level: derivative.underlyingInstruments
          target_field: underlyingInstrumentCode
      cfi_and_bestex_from_instrument_classification: True
      str_to_bool_dict:
        "true": True
        "y": True
        "yes": True
        "t": True
        "on": True
        "false": False
        "n": False
        "no": False
        "f": False
        "off": False
    upstreamTasks:
      - taskName: PrimaryTransformations
        mapped: true
        key: result
  # Link each OrderState record to the parent Order record
  - path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
    name: AssignMetaParent
    params:
      parent_model_attribute: _order.__meta_model__
      parent_attributes_prefix: _order.
      target_attribute: _orderState.__meta_parent__
    upstreamTasks:
      - taskName: PrimaryTransformations
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_static:MapStatic
    name: NEWO_IN_FILE
    paramsList:
      - target_attribute: __NEWO_IN_FILE__
        target_value: false
    upstreamTasks:
      - taskName: PrimaryTransformations
        mapped: true
        key: result
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PrimaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - marketIdentifiers.instrument
        - marketIdentifiers.parties
        - __CLIENT__
        - __EXECUTING_ENTITY__
        - __COUNTERPARTY__
        - __TRADER__
        - __INVESTMENT_DEC_WITHIN_FIRM__
        - __EXECUTION_WITHIN_FIRM__
        - __BUYER__
        - __SELLER__
        - __BUYER_DECISION_MAKER__
        - __SELLER_DECISION_MAKER__
        - __TEMP_BUY_SELL_INDICATOR__
        - __TEMP_INST_ID_INSTRUMENT_ID_CODE__
        - __TEMP_INST_ID_DELIVERY_TYPE__
        - __TEMP_INST_ID_EXT_EXCHANGE_SYMBOL__
        - __TEMP_INST_ID_INSTRUMENT_CLASSIFICATION__
        - __TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS__
        - __TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE__
        - __TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_TYPE__
        - __TEMP_INST_ID_INSTRUMENT_FULL_NAME__
        - __TEMP_INST_ID_INSTRUMENT_ID_CODE_TYPE__
        - __TEMP_INST_ID_PRICE_MULTIPLIER__
        - __TEMP_INST_ID_PRICE_NOTATION__
        - __TEMP_INST_ID_UNDERLYING_INDEX_NAME__
        - __TEMP_INST_ID_QUANTITY_NOTATION__
        - __TEMP_INST_ID_BESTEX_ASSET_CLASS_MAIN__
        - __TEMP_INST_ID_BESTEX_ASSET_CLASS_SUB__
        - __TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID__
        - __TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_BASE_PRODUCT__
        - __TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_FURTHER_SUB_PRODUCT__
        - __TEMP_INST_ID_INSTRUMENT_UNIQUE_ID__
        - __TEMP_INST_ASSET_CLASS__
        - __TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED__
        - __TEMP_INST_UNIQUE_ID_ISIN_POPULATED__
        - __TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED__
        - __TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED__
        - __TEMP_IS_CREATED_THROUGH_FALLBACK__
        - __TEMP_PARENT_META_MODEL__
        - __TEMP_ASSET_CLASS__
    upstreamTasks:
      - taskName: PrimaryTransformations
        mapped: true
        key: primary_transformations
      - taskName: PartiesFallback
        mapped: true
        key: parties
      - taskName: PopulateInstruments
        mapped: true
        key: instruments
      - taskName: AssignMetaParent
        mapped: true
        key: assign_meta_parent
      - taskName: NEWO_IN_FILE
        mapped: true
        key: newo_in_file
  # Filter only OrderRecords into a frame
  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: OrderRecords
    params:
      except_prefix: _orderState.
      strip_prefix: true
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  # Filter only OrderStateRecords into a frame
  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: OrderStateRecords
    params:
      except_prefix: _order.
      strip_prefix: true
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  # Strip prefix `_order.` from the column names of OrderRecords frame.
  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixOrder
    params:
      action: strip
      prefix: _order.
    upstreamTasks:
      - taskName: OrderRecords
        mapped: true
        key: result
  # Strip prefix `orderState.` from the column names of OrderState Records frame.
  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixOrderState
    params:
      action: strip
      prefix: _orderState.
    upstreamTasks:
      - taskName: OrderStateRecords
        mapped: true
        key: result
  # Vertically concatenate Order and OrderState Record frames created above.
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: VerticalConcatenator
    params:
      orient: vertical
      reset_index: true
      drop_index: true
    upstreamTasks:
      - taskName: StripPrefixOrder
        mapped: true
        key: order_records
      - taskName: StripPrefixOrderState
        mapped: true
        key: order_state_records
  - path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
    name: RemoveInvalidOrderStates
    params:
      query: "`executionDetails.orderStatus`.notnull()"
    upstreamTasks:
      - taskName: VerticalConcatenator
        mapped: true
        key: result
  # Remove duplicate new orders, remove synthetic new orders already in Elasticsearh
  - path: swarm_tasks.order.generic.remove_duplicate_newo:RemoveDuplicateNEWO
    name: RemoveDupNEWO
    params:
      newo_in_file_col: __NEWO_IN_FILE__
      drop_newo_in_file_col: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: RemoveInvalidOrderStates
        mapped: true
        key: result
  # Best-execution tasks
  - path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
    name: BestExecution
    resources:
      es_client_key: reference-data
    upstreamTasks:
      - taskName: RemoveDupNEWO
        mapped: true
        key: result
  # Best-Ex results concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: BestExecutionConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __NEWO_IN_FILE__
        - __TEMP_PARENT_META_MODEL__
    upstreamTasks:
      - taskName: BestExecution
        mapped: true
        key: best_ex_result
      - taskName: RemoveDupNEWO
        mapped: true
        key: remove_dup_newo
  # Assign Meta
  - path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
    name: AssignMeta
    params:
      model_attribute: __meta_model__
      parent_attribute: __meta_parent__
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result
   # Post-meta concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PostMetaConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __meta_model__
        - __meta_parent__
    upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result
    - taskName: AssignMeta
      mapped: true
      key: meta
  # Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformer
    params:
      action_type: create
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: FilterRowsInSourceFile
      mapped: true
      key: producer_result
  # Elastic Bulk Writer
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: ElasticBulkWriter
    params:
      payload_size: 10000000
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: ElasticBulkTransformer
      mapped: true
      key: result
  # Instrument Mapper
  - path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
    name: InstrumentMapper
    upstreamTasks:
      - taskName: PostMetaConcatenator
        mapped: true
        key: source_frame
      - taskName: ElasticBulkWriter
        mapped: true
        key: bulk_writer_result
  # Quarantine Condition
  - path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
    name: QuarantineCondition
    upstreamTasks:
    - taskName: ElasticBulkWriter
      mapped: true
      key: bulk_writer_result
  # Quarantined Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: QuarantinedElasticBulkTransformer
    params:
      action_type: create
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: FilterRowsInSourceFile
      mapped: true
      key: producer_result
    - taskName: ElasticBulkWriter
      mapped: true
      key: bulk_writer_result
  # Elastic Bulk Writer for Quarantined Records
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: QuarantinedElasticBulkWriter
    params:
      payload_size: 10000000
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: QuarantinedElasticBulkTransformer
      mapped: true
      key: result
  - path: swarm_tasks.control_flow.noop:Noop
    name: Noop