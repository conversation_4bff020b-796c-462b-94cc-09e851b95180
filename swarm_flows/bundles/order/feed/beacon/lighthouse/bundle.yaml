# Confluence Page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/1691255070/Order+Lighthouse+Beacon
# Bundle path -> order/feed/beacon/lighthouse
# LEI for Lighthouse (Executing Entity): 549300IL8TQT0JMDJJ80
# LEI in Gleif: https://search.gleif.org/#/record/549300IL8TQT0JMDJJ80
id: order-feed-beacon-lighthouse
name: Order Lighthouse Beacon
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: ConditionalCsvFileSplitter
  conditionTaskName: ParamIsBatchedFile
  trueTaskName: BatchedFileCsvSplitter
  falseTaskName: NormalFileCsvSplitter
  merge: true
- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  mapped: true
  merge: false
- name: FileTooBig
  conditionTaskName: HorizontalBatchController
  trueTaskName: BatchProducer
  falseTaskName: MergeAndChunkCsvFiles
  mapped: false
  merge: false
tasks:
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
    - taskName: file_url
      mapped: false
      key: file_url
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
    - taskName: file_url
      mapped: false
      key: file_url
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParamIsBatchedFile
  params:
    arg_name: file_url
    contains: batch 
    ignore_case: true
  upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
    - taskName: file_url
      mapped: false
      key: file_url
- path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
  name: NormalFileCsvSplitter  # NormalFile->ends with .dat extension
  params:
    chunksize: 10000
    delimiter: "|"
  upstreamTasks:
    - taskName: S3OrLocalFile
      mapped: false
      key: extractor_result
- path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
  name: BatchedFileCsvSplitter
  params:
      chunksize: 10000
  upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result
- path: swarm_tasks.io.read.horizontal_batch_controller:HorizontalBatchController
  name: HorizontalBatchController
  params:
    max_chunk_size: 1
  upstreamTasks:
      - taskName: ConditionalCsvFileSplitter
        mapped: false
        key: list_of_batches
- path: swarm_tasks.io.read.merge_and_chunk_csv_files:MergeAndChunkCsvFiles
  name: MergeAndChunkCsvFiles
  params:
    max_chunk_size: 1
  upstreamTasks:
    - taskName: ConditionalCsvFileSplitter
      mapped: false
      key: file_splitter_result_list
    - taskName: file_url
      mapped: false
      key: file_url
- path: swarm_tasks.transform.extract_path.s3_file_list_from_frame_splitter_result_list:S3FileListFileSplitterResultList
  name: S3FileListFileSplitterResultList
  params:
    cloud_key_prefix: "flows/order-feed-beacon-lighthouse/batches"
    datetime_field_in_file_path: false
  upstreamTasks:
    - taskName: MergeAndChunkCsvFiles
      mapped: false
      key: file_splitter_result_list
    - taskName: file_url
      mapped: false
      key: file_url
- path: swarm_tasks.io.write.aws.s3_upload_file:S3UploadFile
  name: S3UploadOrderFile
  upstreamTasks:
    - taskName: S3FileListFileSplitterResultList
      mapped: false
      key: upload_target
- path: swarm_tasks.io.read.batch_producer:BatchProducer
  name: BatchProducer
  params:
    source_schema:
      "ACCOUNT": string
      "BBSYMBOL": string
      "BROKER_CODE": string
      "CLIENTORDERID": string
      "CUMQTY": float
      "CURRENCY": string
      "EFFECTIVE_TIME": string
      "EXCHANGE_NAME": string
      "EXPIRE_DATE": string
      "EXECUTION_TIME": string
      "ISCFD": string
      "ISIN": string
      "LAST_MARKET": string
      "LEAVES_QTY": float
      "LIMIT_PRICE": float
      "LOCATION": string
      "MANAGER": string
      "MATURITY_DAY": string
      "MATURITY_MONTH_YEAR": string
      "MIC": string
      "ORDERQTY": float
      "ORDER_STATUS": string
      "ORDER_TYPE": string
      "ORDER_TIME": string
      "PRICE": float
      "PRICE_NOTATION": string
      "PUTORCALL": string
      "QUANTITY_NOTATION": string
      "SECURITY_NAME": string
      "SECURITY_TYPE": string
      "SIDE": string
      "STRIKE_PRICE": float
      "TIME_IN_FORCE": string
      "TRADE_DATE": string
      "TRADE_ID": string
      "TRADER": string
      "TRANSACT_TIME": string
      "UNDERLYING_ISIN": string
      "UNDERYLING_TICKER": string
  upstreamTasks:
    - taskName: ConditionalCsvFileSplitter
      mapped: true
      flatten: true
      key: file_splitter_result
# Remove/skip rows which have ORDER_STATUS in ['PendingNew','PendingCancel','PendingReplace']
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: FilterRowsInSourceFile
  params:
    query: "~`ORDER_STATUS`.isin(['PendingNew','PendingCancel','PendingReplace'])"
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result

# Primary Transformations
- path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
  name: PrimaryTransformations
  upstreamTasks:
    - taskName: FilterRowsInSourceFile
      mapped: true
      key: result
- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkParties
  params:
    identifiers_path: marketIdentifiers.parties
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: result
- path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
  name: LinkInstrument
  params:
    identifiers_path: marketIdentifiers.instrument
    asset_class_attribute: __input_asset_class__
    venue_attribute: __input_venue__
    currency_attribute: __currency__
  resources:
    ref_data_key: reference-data
    tenant_data_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: result
- path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
  name: InstrumentFallback
  params:
    instrument_fields_map:
      # Bond
      - source_field: __expiry__
        target_field: bond.maturityDate
      # Derivative
      - source_field: __expiry_date__
        target_field: derivative.expiryDate
      - source_field: __currency__
        target_field: derivative.strikePriceCurrency
      # Ext
      - source_field: __strike_price_type__
        target_field: ext.strikePriceType
      # Top-level fields
      - source_field: SECURITY_NAME
        target_field: instrumentFullName
      - source_field: isCreatedThroughFallback
        target_field: true
    str_to_bool_dict:
      "true": True
      "y": True
      "yes": True
      "t": True
      "on": True
      "false": False
      "n": False
      "no": False
      "f": False
      "off": False
  upstreamTasks:
    - taskName: LinkInstrument
      mapped: true
      key: link_instrument
    - taskName: PrimaryTransformations
      mapped: true
      key: result
- path: swarm_tasks.transform.map.map_to_nested:MapToNested
  name: InstrumentOverridesStrikePrice
  params:
    source_attribute: __option_strike_price__
    nested_path: derivative.strikePrice
    target_attribute: instrumentDetails.instrument
  upstreamTasks:
    - taskName: InstrumentFallback
      mapped: true
      key: instrument_fallback
    - taskName: PrimaryTransformations
      mapped: true
      key: result
- path: swarm_tasks.transform.map.map_to_nested:MapToNested
  name: InstrumentOverridesStrikePriceCurrency
  params:
    source_attribute: __currency__
    nested_path: derivative.strikePriceCurrency
    target_attribute: instrumentDetails.instrument
  upstreamTasks:
    - taskName: InstrumentOverridesStrikePrice
      mapped: true
      key: instrument_override_strike_price
    - taskName: PrimaryTransformations
      mapped: true
      key: result
- path: swarm_tasks.transform.map.map_to_nested:MapToNested
  name: InstrumentExtStrikePriceType
  params:
    source_attribute: __strike_price_type__
    nested_path: ext.strikePriceType
    target_attribute: instrumentDetails.instrument
    query: "`__asset_class__` == 'option'"
  upstreamTasks:
    - taskName: InstrumentOverridesStrikePriceCurrency
      mapped: true
      key: instrument_override_strike_price_currency
    - taskName: PrimaryTransformations
      mapped: true
      key: result
- path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
  name: MetaParent
  params:
    parent_model_attribute: _order.__meta_model__
    parent_attributes_prefix: _order.
    target_attribute: _orderState.__meta_parent__
  upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: result
  # Primary Frame Conatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PrimaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
    - marketIdentifiers.instrument
    - marketIdentifiers.parties
    - __asset_class__
    - __currency__
    - __expiry__
    - __expiry_date__
    - __input_asset_class__
    - __input_venue__
    - __option_strike_price__
    - __option_type__
    - __strike_price_type__
    - asset_class_attribute # Instrument Identifiers columns
    - bbg_figi_id_attribute
    - currency_attribute
    - eurex_id_attribute
    - exchange_symbol_attribute
    - expiry_date_attribute
    - interest_rate_start_date_attribute
    - isin_attribute
    - notional_currency_1_attribute
    - notional_currency_2_attribute
    - option_strike_price_attribute
    - option_type_attribute
    - swap_near_leg_date_attribute
    - underlying_index_name_attribute
    - underlying_index_name_leg_2_attribute
    - underlying_index_series_attribute
    - underlying_index_term_attribute
    - underlying_index_term_value_attribute
    - underlying_index_version_attribute
    - underlying_isin_attribute
    - underlying_symbol_attribute
    - underlying_symbol_expiry_code_attribute
    - underlying_index_term_leg_2_attribute
    - underlying_index_term_value_leg_2_attribute
    - venue_attribute
    - venue_financial_instrument_short_name_attribute
    - instrument_classification_attribute
  upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: primary_transformations
    - taskName: MetaParent
      mapped: true
      key: parent_id
    - taskName: LinkParties
      mapped: true
      key: link_parties
    - taskName: InstrumentExtStrikePriceType
      mapped: true
      key: instrument_ext_strike_price_type
# Filter only OrderRecords into a frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderRecords
  params:
    except_prefix: _orderState.
    strip_prefix: true
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    mapped: true
    key: result
# Filter only OrderStateRecords into a frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderStateRecords
  params:
    except_prefix: _order.
    strip_prefix: true
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: result
# Strip prefix `_order.` from the column names of OrderRecords frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrder
  params:
    action: strip
    prefix: _order.
  upstreamTasks:
    - taskName: OrderRecords
      mapped: true
      key: result
# Strip prefix `orderState.` from the column names of OrderState Records frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrderState
  params:
    action: strip
    prefix: _orderState.
  upstreamTasks:
    - taskName: OrderStateRecords
      mapped: true
      key: result
# Vertically concatenate Order and OrderState Record frames created above.
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: VerticalConcatenator
  params:
    orient: vertical
    reset_index: true
    drop_index: true
  upstreamTasks:
    - taskName: StripPrefixOrder
      mapped: true
      key: order_records
    - taskName: StripPrefixOrderState
      mapped: true
      key: order_state_records
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: RemoveInvalidOrderStates
  params:
    query: "`executionDetails.orderStatus`.notnull()"
  upstreamTasks:
    - taskName: VerticalConcatenator
      mapped: true
      key: result
# Remove duplicate new orders, remove synthetic new orders already in Elasticsearh
- path: swarm_tasks.order.generic.remove_duplicate_newo:RemoveDuplicateNEWO
  name: RemoveDupNEWO
  params:
    newo_in_file_col: __newo_in_file__
    drop_newo_in_file_col: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: RemoveInvalidOrderStates
      mapped: true
      key: result
# Best-execution tasks
- path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
  name: BestExecution
  resources:
    es_client_key: reference-data
  upstreamTasks:
    - taskName: RemoveDupNEWO
      mapped: true
      key: result
# Best-Ex results concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: BestExecutionConcatenator
  params:
    orient: horizontal
  upstreamTasks:
    - taskName: BestExecution
      mapped: true
      key: best_ex_result
    - taskName: RemoveDupNEWO
      mapped: true
      key: orders_and_orderstates_final
# Assign Meta
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: AssignMeta
  params:
    model_attribute: __meta_model__
    parent_attribute: __meta_parent__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result
# Post-meta concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PostMetaConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
      - __meta_parent__
  upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result
    - taskName: AssignMeta
      mapped: true
      key: meta
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: FilterRowsInSourceFile
      mapped: true
      key: producer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsent
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: ElasticBulkTransformer
      mapped: true
      key: result
  # Instrument Mapper
- path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
  name: InstrumentMapper
  upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: source_frame
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineCondition
  upstreamTasks:
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformer
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: FilterRowsInSourceFile
      mapped: true
      key: producer_result
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsent
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: QuarantinedElasticBulkTransformer
      mapped: true
      key: result
- path: swarm_tasks.control_flow.noop:Noop
  name: Noop
