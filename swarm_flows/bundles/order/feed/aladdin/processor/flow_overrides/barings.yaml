taskOverrides:
  prod:
    - name: LinkParties
      params:
        parties_matching_condition:
            - path: "buyer"
              conditions:
                - target_field: "&key"
                  matching_value: "AccountFirm"
                - target_field: "details.firmType"
                  matching_value: "Counterparty"
            - path: "seller"
              conditions:
                - target_field: "&key"
                  matching_value: "AccountFirm"
                - target_field: "details.firmType"
                  matching_value: "Counterparty"
            - path: "clientIdentifiers.client"
              conditions:
                - target_field: "details.firmType"
                  matching_value: "Client"
            - path: "counterparty"
              conditions:
                - target_field: "details.firmType"
                  matching_value: "Counterparty"
    - name: FilterRows
      params:
        # Skip rows with transaction.[execCptyType] = "PORTFOLIO", transaction.[secGroup] = "CASH", transaction.[secTicker] = "TNOTE|TBOND|TIPS", or order.[dealingCapacity] or transaction.[dealingCapacity] == I|L
        query: "(~(`transaction.execCptyType`.astype('str').str.fullmatch('PORTFOLIO',case=False,na=False))) & (~(`transaction.secGroup`.astype('str').str.fullmatch('CASH',case=False,na=False))) & (~(`transaction.secTicker`.astype('str').str.fullmatch('TNOTE|TBOND|TIPS',case=False,na=False))) & (~(`order.dealingCapacity`.astype('str').str.fullmatch('I|L',case=False,na=False)) | ~(`transaction.dealingCapacity`.astype('str').str.fullmatch('I|L',case=False,na=False)))"
