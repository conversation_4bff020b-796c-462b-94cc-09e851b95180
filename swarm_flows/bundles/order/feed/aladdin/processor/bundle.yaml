id: order-feed-aladdin-processor
name: Order Feed Aladdin Processor
infra:
  - name: tenant-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process
controlFlows:
  - name: S3OrLocalFile
    conditionTaskName: ParametersFlowController
    trueTaskName: S3DownloadFile
    falseTaskName: LocalFile
    merge: true
  - name: NeedsQuarantine
    conditionTaskName: QuarantineCondition
    trueTaskName: QuarantinedElasticBulkTransformer
    falseTaskName: Noop
    mapped: true
    merge: false
tasks:
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url

  # CSV File flow branch
  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvFileSplitter
    params:
      chunksize: 25000
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result

  # Normalize input data
  - path: swarm_tasks.io.read.batch_producer:BatchProducer
    name: BatchProducer
    params:
      source_schema:
        "__aggregate_portfolio_id__": string
        "__order_type__": string
        "fill.dealingCapacity": string
        "fill.exchange": string
        "fill.executedPrice": float
        "fill.executedQuantity": float
        "fill.executedTimestampUtc": string
        "fill.fillId": string
        "fill.placementId": string
        "orderdetail.orderId": string
        "orderdetail.orderDetailId": string
        "orderdetail.origOrderId": string
        "orderdetail.portfolioId": string
        "order.activatedTimestampUtc": string
        "order.avgPrice": float
        "order.basketId": string
        "order.createdTimestampUtc": string
        "order.dealingCapacity": string
        "order.isin": string
        "order.modifiedTimestampUtc": string
        "order.orderId": string
        "order.orderQuantity": float
        "order.orderStatus": string
        "order.orderType": string
        "order.pmInitials": string
        "order.priceCcy": string
        "order.ric": string
        "order.timeInForce": string
        "order.trader": string
        "order.tranType": string
        "order.genComments": string
        "placement.limitValue": float
        "placement.stopValue": float
        "placement.orderId": string
        "placement.placementId": string
        "placement.sendTimeUtc": string
        "transaction.assetId": string
        "transaction.cptyId": string
        "transaction.dealingCapacity": string
        "transaction.execCptyId": string
        "transaction.execCptyType": string
        "transaction.isin": string
        "transaction.maturity": string
        "transaction.orderId": string
        "transaction.placementId": string
        "transaction.portfolioId": string
        "transaction.portfolioTicker": string
        "transaction.ric": string
        "transaction.secDesc1": string
        "transaction.secGroup": string
        "transaction.secTicker": string
        "transaction.secType": string
        "transaction.tradeCoupon": float
        "transaction.tradeNum": string
        "transaction.tradePurpose": string
        "transaction.tradeQuantity": float
        "transaction.trader": string
        "transaction.underlyingSnpCusip": string
      remove_unknown_columns: true
    upstreamTasks:
      - taskName: CsvFileSplitter
        mapped: true
        flatten: true
        key: file_splitter_result

  # this is used to skip rows during an override
  - path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
    name: FilterRows
    params:
      query: "index==index"
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result

  # Primary transformations
  - path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
    name: AladdinOrderTransformation
    upstreamTasks:
      - taskName: FilterRows
        mapped: true
        key: result

  - path: swarm_tasks.transform.steeleye.link.parties:LinkParties
    name: LinkParties
    params:
      identifiers_path: marketIdentifiers.parties
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: AladdinOrderTransformation
        mapped: true
        key: result
  - path: swarm_tasks.order.generic.party_fallback_with_lei_lookup:PartyFallbackWithLeiLookup
    name: PartyFallbackWithLeiLookup
    upstreamTasks:
      - taskName: AladdinOrderTransformation
        mapped: true
        key: result
      - taskName: LinkParties
        mapped: true
        key: link_parties_result
  - path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
    name: LinkInstrument
    params:
      identifiers_path: marketIdentifiers.instrument
      venue_attribute: transactionDetails.venue
      currency_attribute: transactionDetails.priceCurrency
      asset_class_attribute: __asset_class__
    resources:
      ref_data_key: reference-data
      tenant_data_key: tenant-data
    upstreamTasks:
      - taskName: AladdinOrderTransformation
        mapped: true
        key: result
  # Instrument Fallback
  - path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
    name: InstrumentFallback
    params:
      instrument_fields_map:
        - source_field: transactionDetails.priceCurrency
          target_field: derivative.strikePriceCurrency
        - source_field: __ric__
          target_field: ext.pricingReferences.RIC
        - source_field: __pricing_reference_lxid__
          target_field: ext.pricingReferences.LXID
        - source_field: __pricing_reference_redcode__
          target_field: ext.pricingReferences.REDCode
        - source_field: __instrument_full_name__
          target_field: instrumentFullName
        - source_field: __instrument_classification__
          target_field: instrumentClassification
        - source_field: __instrument_unique_identifier__
          target_field: ext.instrumentUniqueIdentifier
        - source_field: __instrument_created_through_fb__
          target_field: isCreatedThroughFallback
        - source_field: __best_ex_asset_class_main__
          target_field: ext.bestExAssetClassMain
        - source_field: __cfi_category__
          target_field: cfiCategory
        - source_field: __cfi_group__
          target_field: cfiGroup
      str_to_bool_dict:
        "true": True
        "false": False
    upstreamTasks:
      - taskName: AladdinOrderTransformation
        mapped: true
        key: result
      - taskName: LinkInstrument
        mapped: true
        key: link_instrument
  - path: swarm_tasks.transform.map.map_to_nested:MapToNested
    name: InstrumentFullNameOverride
    params:
      source_attribute: __instrument_full_name__
      nested_path: instrumentFullName
      target_attribute: instrumentDetails.instrument
      skip_target_nan: False
      query: "`__file_type_asset_class__`!='FX'"   # For FX, only instruments via fallback should have this override which is already done in fallback task.
    upstreamTasks:
      - taskName: InstrumentFallback
        mapped: true
        key: result
      - taskName: AladdinOrderTransformation
        mapped: true
        key: primary_transformation
  - path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
    name: AssignMetaParent
    params:
      parent_model_attribute: _order.__meta_model__
      parent_attributes_prefix: _order.
      target_attribute: _orderState.__meta_parent__
    upstreamTasks:
    - taskName: AladdinOrderTransformation
      mapped: true
      key: result
  # Auxiliary frame concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: AuxiliaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __asset_class__
        - __best_ex_asset_class_main__
        - __cfi_category__
        - __cfi_group__
        - __expiry_date__
        - __file_type_asset_class__
        - __instrument_classification__
        - __instrument_created_through_fb__
        - __instrument_full_name__
        - __instrument_unique_identifier__
        - __isin__
        - __option_type__
        - __notional_currency_2__
        - __pricing_reference_lxid__
        - __pricing_reference_redcode__
        - __ric__
        - __strike_price__
        - __fallback_buyer__
        - __fallback_client__
        - __fallback_counterparty__
        - __fallback_exec_within_firm__
        - __fallback_executing_entity__
        - __fallback_inv_dec_in_firm__
        - __fallback_seller__
        - __fallback_trader__
        - __fallback_buyer_dec_maker__
        - __fallback_seller_dec_maker__
        - marketIdentifiers.instrument
        - marketIdentifiers.parties
        - asset_class_attribute # Instrument Identifiers columns
        - bbg_figi_id_attribute
        - currency_attribute
        - eurex_id_attribute
        - exchange_symbol_attribute
        - expiry_date_attribute
        - interest_rate_start_date_attribute
        - isin_attribute
        - notional_currency_1_attribute
        - notional_currency_2_attribute
        - option_strike_price_attribute
        - option_type_attribute
        - swap_near_leg_date_attribute
        - underlying_index_name_attribute
        - underlying_index_name_leg_2_attribute
        - underlying_index_series_attribute
        - underlying_index_term_attribute
        - underlying_index_term_value_attribute
        - underlying_index_version_attribute
        - underlying_isin_attribute
        - underlying_symbol_attribute
        - underlying_symbol_expiry_code_attribute
        - underlying_index_term_leg_2_attribute
        - underlying_index_term_value_leg_2_attribute
        - venue_attribute
        - venue_financial_instrument_short_name_attribute
        - instrument_classification_attribute
    upstreamTasks:
      - taskName: AladdinOrderTransformation
        mapped: true
        key: primary_frame_concatenator
      - taskName: PartyFallbackWithLeiLookup
        mapped: true
        key: link_parties
      - taskName: InstrumentFullNameOverride
        mapped: true
        key: link_instruments
      - taskName: AssignMetaParent
        mapped: true
        key: assign_meta_parent
  # Filter only OrderRecords into a frame
  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: OrderRecords
    params:
      except_prefix: _orderState.
      strip_prefix: true
    upstreamTasks:
    - taskName: AuxiliaryFrameConcatenator
      mapped: true
      key: result
  # Filter only OrderStateRecords into a frame
  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: OrderStateRecords
    params:
      except_prefix: _order.
      strip_prefix: true
    upstreamTasks:
    - taskName: AuxiliaryFrameConcatenator
      mapped: true
      key: result
  # Strip prefix `_order.` from the column names of OrderRecords frame.
  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixOrder
    params:
      action: strip
      prefix: _order.
    upstreamTasks:
    - taskName: OrderRecords
      mapped: true
      key: result
  # Strip prefix `_orderState.` from the column names of OrderState Records frame.
  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixOrderState
    params:
      action: strip
      prefix: _orderState.
    upstreamTasks:
    - taskName: OrderStateRecords
      mapped: true
      key: result
  # Vertically concatenate Order and OrderState Record frames created above.
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: VerticalConcatenator
    params:
      orient: vertical
      reset_index: true
      drop_index: true
    upstreamTasks:
    - taskName: StripPrefixOrder
      mapped: true
      key: order_records
    - taskName: StripPrefixOrderState
      mapped: true
      key: order_state_records
  - path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
    name: RemoveInvalidOrderStates
    params:
      query: "`executionDetails.orderStatus`.notnull()"
    upstreamTasks:
    - taskName: VerticalConcatenator
      mapped: true
      key: result
  # Remove duplicate new orders, remove synthetic new orders already in Elasticsearh
  - path: swarm_tasks.order.generic.remove_duplicate_newo:RemoveDuplicateNEWO
    name: RemoveDupNEWO
    params:
      newo_in_file_col: __newo_in_file__
      drop_newo_in_file_col: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: RemoveInvalidOrderStates
        mapped: true
        key: result
  - path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
    name: BestExecution
    resources:
      es_client_key: reference-data
    upstreamTasks:
    - taskName: RemoveDupNEWO
      mapped: true
      key: result
  # Best-Ex results concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: BestExecutionConcatenator
    params:
      orient: horizontal
    upstreamTasks:
    - taskName: BestExecution
      mapped: true
      key: best_ex_result
    - taskName: RemoveDupNEWO
      mapped: true
      key: orders_and_orderstates_final
  # Assign Meta
  - path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
    name: AssignMeta
    params:
      model_attribute: __meta_model__
      parent_attribute: __meta_parent__
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result
   # Post-meta concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PostMetaConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __meta_model__
        - __meta_parent__
    upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result
    - taskName: AssignMeta
      mapped: true
      key: meta
  # Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformer
    params:
      action_type: create
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
  # Elastic Bulk Writer
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: ElasticBulkWriter
    params:
      payload_size: 10000000
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: ElasticBulkTransformer
      mapped: true
      key: result
  # Instrument Mapper
  - path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
    name: InstrumentMapper
    upstreamTasks:
      - taskName: PostMetaConcatenator
        mapped: true
        key: source_frame
      - taskName: ElasticBulkWriter
        mapped: true
        key: bulk_writer_result
  # Quarantine Condition
  - path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
    name: QuarantineCondition
    upstreamTasks:
    - taskName: ElasticBulkWriter
      mapped: true
      key: bulk_writer_result
  # Quarantined Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: QuarantinedElasticBulkTransformer
    params:
      action_type: create
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: ElasticBulkWriter
      mapped: true
      key: bulk_writer_result
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: QuarantinedElasticBulkWriter
    params:
      payload_size: 10000000
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: QuarantinedElasticBulkTransformer
      mapped: true
      key: result
  - path: swarm_tasks.control_flow.noop:Noop
    name: Noop