# Confluence Page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2753593546/Order+Thinkfolio
# Bundle path -> order/feed/thinkfolio/equities
id: order-feed-thinkfolio
name: Order Feed Thinkfolio
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  mapped: true
  merge: false
tasks:
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url

# Merges Fixed Income files into one
- path: swarm_tasks.order.feed.thinkfolio.fixed_income_file_merger:FixedIncomeFileMerger
  name: FixedIncomeFileMerger
  upstreamTasks:
    - taskName: S3OrLocalFile
      mapped: false
      key: extractor_result
    - taskName: file_url
      mapped: false
      key: file_url

# Split CSV file into chunks
- path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
  name: CsvFileSplitter
  params:
    chunksize: 20000
    detect_encoding: true
  upstreamTasks:
    - taskName: FixedIncomeFileMerger
      mapped: false
      key: extractor_result

# Normalize input data
- path: swarm_tasks.io.read.batch_producer:BatchProducer
  name: BatchProducer
  params:
    source_schema:
      # Shared
      "Asset Class": string
      "Capacity": string
      "Order Type": string
      "Parent Order ID": string
      "Placement ID": string
      "Side": string
      "Trade Date": string
      "Broker": string
      "Dealing Desk": string


      # Equity
      "Broker Pickup Time": string
      "Commission": string
      "Currency": string
      "Execution Decision Time": string
      "Execution Limit Price": float
      "Execution Time": string
      "Last Capacity": string
      "Last Market": string
      "Last Px": float
      "Last Qty": float
      "Liquidity Flag": string
      "Order Limit Price": float
      "Order Quantity": string
      "Ordered Shares": float
      "Placement Shares": float
      "PM Entry Time": string
      "PM": string
      "Security ID Type": string
      "Security ID": string
      "sISIN": string
      "Strategy Param 1": string
      "Strategy Param 2": string
      "Strategy Param 3": string
      "Strategy": string
      "Time in Force": string
      "Trader Pickup Time": string
      "Trader Sent Time": string
      "Trader": string
      "via FIX": string

      # Fixed Income
      "Algo": string
      "Algo Name": string
      "Asset Sub Type": string
      "Asset Sub-Class": string
      "Asset Sub-Type": string
      "Asset Type": string
      "Authorised TS": string
      "Authorising PM": string
      "Broker Date": string
      "Broker Fill TS": string
      "Broker ID": string
      "Broker Name": string
      "Cancelled Indicator": string
      "Comment Fields": string
      "Commission Fee Code": string
      "Commission Over-Ride Code": string
      "Cusip": string
      "Dealer": string
      "DMA": string
      "Exec Broker Spread": string
      "Exec Broker Yield": string
      "ExtSys Ack TS": string
      "FI Dealing Classification": string
      "ISIN": string
      "Last Basket": string
      "LoanX": string
      "Local Commission Amount": string
      "Local Currency": string
      "Local Currency Code": string
      "Local Unit Price": float
      "Market Fees/Taxes": string
      "Market Order Type": string
      "MI Desk Level 2": string
      "Modifiers": string
      "Narrative Fields": string
      "Order ID": string
      "Other Expenses": string
      "Parent Order Size": float
      "ParentOrderTimestamp": string
      "PM Order Creation TS": string
      "PM Order Input TS": string
      "Portfolio Code": string
      "Quantity Traded": float
      "Reason": string
      "Security Name": string
      "SEDOL": string
      "Sent to ExtSys TS": string
      "Settlement Date": string
      "Settlement Value Local CCY": float
      "Time In Force": string
      "TraderID": string
      "Underlying SEDOL": string
      "Unfunded": string
      "Venue": string
      "Working TS": string

      # FX
      "Account Fund": string
      "AssetClass": string
      "Timestamp (GMT)": string
      "Trade Id": string
      "Trader ID": string
      "Outright Rate": string
      "Buy Ccy": string
      "Buy Amount": string
      "Sell Amount": string
      "Rate Direction": string
      "Execution Type": string
      "Custodian": string
      "Ccy Intent": string
      "Value Date Fwd": string
      "Value Date": string
      "CFI code": string

    remove_unknown_columns: true
  upstreamTasks:
  - taskName: CsvFileSplitter
    mapped: true
    flatten: true
    key: file_splitter_result

# Primary Transformations
- path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
  name: PrimaryTransformations
  upstreamTasks:
  - taskName: BatchProducer
    mapped: true
    key: result

# Link Parties identifiers
- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkParties
  params:
    identifiers_path: marketIdentifiers.parties
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: PrimaryTransformations
    mapped: true
    key: result

# Link Instrument identifiers
- path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
  name: LinkInstruments
  params:
    identifiers_path: marketIdentifiers.instrument
    venue_attribute: transactionDetails.venue
    currency_attribute: transactionDetails.priceCurrency
    asset_class_attribute: asset_class_attribute
  resources:
    ref_data_key: reference-data
    tenant_data_key: tenant-data
  upstreamTasks:
  - taskName: PrimaryTransformations
    mapped: true
    key: result

# Instrument Fallback
- path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
  name: InstrumentFallback
  params:
    instrument_fields_map:
      - source_field: __full_name__
        target_field: instrumentFullName
      - source_field: __classification__
        target_field: instrumentClassification
      - source_field: __is_created_through_fallback__
        target_field: isCreatedThroughFallback
    str_to_bool_dict:
      "true": True
      "false": False
  upstreamTasks:
  - taskName: PrimaryTransformations
    mapped: true
    key: result
  - taskName: LinkInstruments
    mapped: true
    key: link_instruments

# Assign Meta Parent
- path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
  name: AssignMetaParent
  params:
    parent_model_attribute: _order.__meta_model__
    parent_attributes_prefix: _order.
    target_attribute: _orderState.__meta_parent__
  upstreamTasks:
  - taskName: PrimaryTransformations
    mapped: true
    key: result

# Auxiliary frame concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PrimaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __is_created_through_fallback__
      - marketIdentifiers.instrument
      - marketIdentifiers.parties
      - asset_class_attribute # Instrument Identifiers columns
      - bbg_figi_id_attribute
      - currency_attribute
      - eurex_id_attribute
      - exchange_symbol_attribute
      - expiry_date_attribute
      - interest_rate_start_date_attribute
      - isin_attribute
      - notional_currency_1_attribute
      - notional_currency_2_attribute
      - option_strike_price_attribute
      - option_type_attribute
      - swap_near_leg_date_attribute
      - underlying_index_name_attribute
      - underlying_index_name_leg_2_attribute
      - underlying_index_series_attribute
      - underlying_index_term_attribute
      - underlying_index_term_value_attribute
      - underlying_index_version_attribute
      - underlying_isin_attribute
      - underlying_symbol_attribute
      - underlying_symbol_expiry_code_attribute
      - underlying_index_term_leg_2_attribute
      - underlying_index_term_value_leg_2_attribute
      - venue_attribute
      - venue_financial_instrument_short_name_attribute
      - instrument_classification_attribute
      - __full_name__
      - __classification__
  upstreamTasks:
  - taskName: PrimaryTransformations
    mapped: true
    key: result
  - taskName: LinkParties
    mapped: true
    key: link_parties
  - taskName: InstrumentFallback
    mapped: true
    key: link_instruments
  - taskName: AssignMetaParent
    mapped: true
    key: assign_meta_parent

# Filter only OrderRecords into a frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderRecords
  params:
    except_prefix: _orderState.
    strip_prefix: true
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    mapped: true
    key: result

# Filter only OrderStateRecords into a frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderStateRecords
  params:
    except_prefix: _order.
    strip_prefix: true
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    mapped: true
    key: result

# Strip prefix `_order.` from the column names of OrderRecords frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrder
  params:
    action: strip
    prefix: _order.
  upstreamTasks:
  - taskName: OrderRecords
    mapped: true
    key: result

# Strip prefix `_orderState.` from the column names of OrderState Records frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrderState
  params:
    action: strip
    prefix: _orderState.
  upstreamTasks:
  - taskName: OrderStateRecords
    mapped: true
    key: result

# Vertically concatenate Order and OrderState Record frames created above.
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: VerticalConcatenator
  params:
    orient: vertical
    reset_index: true
    drop_index: true
  upstreamTasks:
  - taskName: StripPrefixOrder
    mapped: true
    key: order_records
  - taskName: StripPrefixOrderState
    mapped: true
    key: order_state_records

# Remove duplicate new orders, remove synthetic new orders already in Elasticsearch
- path: swarm_tasks.order.generic.remove_duplicate_newo:RemoveDuplicateNEWO
  name: RemoveDupNEWO
  params:
    newo_in_file_col: __newo_in_file__
    drop_newo_in_file_col: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: VerticalConcatenator
    mapped: true
    key: result

# Best Execution values calculations
- path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
  name: BestExecution
  resources:
    es_client_key: reference-data
  upstreamTasks:
  - taskName: RemoveDupNEWO
    mapped: true
    key: result

# Best-Ex results concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: BestExecutionConcatenator
  params:
    orient: horizontal
  upstreamTasks:
  - taskName: BestExecution
    mapped: true
    key: best_ex_result
  - taskName: RemoveDupNEWO
    mapped: true
    key: orders_and_orderstates_final

# Assign Meta
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: AssignMeta
  params:
    model_attribute: __meta_model__
    parent_attribute: __meta_parent__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: BestExecutionConcatenator
    mapped: true
    key: result

 # Post-meta concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PostMetaConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
      - __meta_parent__
  upstreamTasks:
  - taskName: BestExecutionConcatenator
    mapped: true
    key: result
  - taskName: AssignMeta
    mapped: true
    key: meta

# Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: PostMetaConcatenator
    mapped: true
    key: transform_result

# Elastic Bulk Writer
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsent
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: ElasticBulkTransformer
    mapped: true
    key: result

# Instrument Mapper
- path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
  name: InstrumentMapper
  upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: source_frame
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result

# Quarantine Condition
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineCondition
  upstreamTasks:
  - taskName: PutIfAbsent
    mapped: true
    key: bulk_writer_result

# Quarantined Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformer
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: PostMetaConcatenator
    mapped: true
    key: transform_result
  - taskName: PutIfAbsent
    mapped: true
    key: bulk_writer_result

# Quarantined Elastic Bulk Writer
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsent
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: QuarantinedElasticBulkTransformer
    mapped: true
    key: result

# Noop
- path: swarm_tasks.control_flow.noop:Noop
  name: Noop