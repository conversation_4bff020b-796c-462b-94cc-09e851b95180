# Input: Csv file which contains S3 links to a batch of LME Select fix files.
# All the fix files are downloaded locally, and processed together in one flow run.

# Methodology - Parse FIX message; build Pandas DataFrame with relevant data;
# Map data to fit Order and OrderState models;
# Output - Write Order and OrderState trades to the associated tenant's Elastic index
# Confluence documentation:
# https://steeleye.atlassian.net/wiki/spaces/IN/pages/2959212545/Order+LME+FIX+Drop+Copy+New+Platform
id: order-feed-lme-select-fix
name: Order Feed LME Select FIX
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  merge: false

tasks:
  # ParametersFlowController
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url

  # S3DownloadFile
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url

  # LocalFile
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url

  # Download fix files and put their content in a data frame column
- path: swarm_tasks.io.read.fix.fix_batch_csv_downloader:FixBatchCsvDownloader
  name: FixBatchCsvDownloader
  upstreamTasks:
    - taskName: S3OrLocalFile
      key: extractor_result

  # Read .fix file, parse, validate and convert to FixParserResult dataclass
  # Note: the key has to be fix_dataframe as that's the argument name in execute()
- path: swarm_tasks.io.read.fix.fix_parser:FixParser
  name: FixParser
  upstreamTasks:
    - taskName: FixBatchCsvDownloader
      key: fix_dataframe

# Convert FixParserResult to Pandas DataFrame
# Note: the key has to be fix_parsed_data as that's the argument name in execute()
- path: swarm_tasks.io.read.fix.fix_parser_result_to_frame:FixParserResultToFrame
  name: FixParserResultToFrame
  params:
    dataframe_columns:
      - AvgPx
      - ClOrdID
      - ExecID
      - ExecType
      - ff_10010
      - ff_1057
      - LastPx
      - LastQty
      - LeavesQty
      - LegLastPx
      - LegRefID
      - LegSide
      - MultiLegReportingType
      - NoLegs
      - OrderCapacity
      - OrderID
      - OrderQty
      - OrdStatus
      - OrdType
      - OrderRestrictions
      - PartyID
      - PartyRole
      - Price
      - SecurityID
      - Side
      - StopPx
      - Symbol
      - Text
      - TimeInForce
      - TransactTime
      - TrdMatchID
  upstreamTasks:
    - taskName: FixParser
      key: fix_parsed_data
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: FilterRowsInSourceFile
  params:
    skip_on_empty: true
    query: "`MsgType`.notnull() and `MsgType` != 'B'"
  upstreamTasks:
  - taskName: FixParserResultToFrame
    key: result
# Transformations
- path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
  name: LmeSelectOrderTransformations
  upstreamTasks:
    - taskName: FilterRowsInSourceFile
      key: result
# Fetch instruments for all the trades and split the multileg trades into single legs
# Note: the values of fields like order id, buysell, order qty might change if the
# mutlileg are split into single leg
- path: swarm_tasks.order.generic.vdi_link_instrument.fetch_vdi_and_split_multileg_trades:FetchVDIAndSplitMultiLegTrades
  name: FetchVDIAndSplitMultiLegTrades
  params:
    security_id_col: __security_id__
    trade_date: date
    instrument: instrumentDetails.instrument
    exchange: xlme
    audit_and_remove_missing_instruments: false
    convert_buysell_from_enum_to_number: true
  upstreamTasks:
    - taskName: LmeSelectOrderTransformations
      key: result

#  Mappings for Venue and price fields
# /Users/<USER>/Github/se-prefect-dir/swarm-tasks/swarm_tasks/order/feed/lme/select/fix/lme_select_fix_prices_currencies.py
- path: swarm_tasks.order.feed.lme.select.fix.lme_select_fix_prices_currencies:LmeSelectFixPricesCurrencies
  name: LmeSelectFixPricesCurrencies
  params:
    instrument: instrumentDetails.instrument
  upstreamTasks:
    - taskName: FetchVDIAndSplitMultiLegTrades
      key: result
# Link Parties
- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkParties
  params:
    identifiers_path: marketIdentifiers.parties
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: LmeSelectFixPricesCurrencies
      key: result
# Instrument Fallback
- path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
  name: InstrumentFallback
  params:
    instrument_fields_map:
      - source_field: __security_id__
        target_field: instrumentFullName
      - source_field: __is_created_through_fallback__
        target_field: isCreatedThroughFallback
    str_to_bool_dict:
      "on": True
      "off": False
  upstreamTasks:
    - taskName: LmeSelectFixPricesCurrencies
      key: result
# Link each OrderState record to the parent Order record
- path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
  name: AssignMetaParent
  params:
      parent_model_attribute: _order.__meta_model__
      parent_attributes_prefix: _order.
      target_attribute: _orderState.__meta_parent__
  upstreamTasks:
    - taskName: LmeSelectFixPricesCurrencies
      key: result
# Remove duplicate column instrumentDetails.instrument from LmeSelectFixPricesCurrencies
# before the frame concatenator. The latest version of the instrument should be present in
# the fallback anyway.
- path: swarm_tasks.transform.frame.filter_columns:FilterColumns
  name: LmeDataWithoutDuplicateInstrument
  params:
    action: drop
    columns:
      - "instrumentDetails.instrument"
  upstreamTasks:
    - taskName: LmeSelectFixPricesCurrencies
      key: result
# Frame Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PrimaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
      - __leg_side_1__
      - __leg_side_2__
      - __leg_last_px_1__
      - __leg_last_px_2__
      - __is_created_through_fallback__
      - __num_legs__
      - __security_id__
      - __raw_side__
      - __is_multileg_trade__
  upstreamTasks:
    - taskName: AssignMetaParent
      key: result
    - taskName: LmeDataWithoutDuplicateInstrument
      key: lme_all_mappings
    - taskName: InstrumentFallback
      key: instrument_fallback
    - taskName: LinkParties
      key: link_parties
# Filter only Order records into a data frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderRecords
  params:
    except_prefix: _orderState.
    strip_prefix: true
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    key: result
# Filter only OrderState records into a data frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderStateRecords
  params:
    except_prefix: _order.
    strip_prefix: true
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    key: result
# Strip prefix `_order.` from the column names of OrderRecords frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrder
  params:
    action: strip
    prefix: _order.
  upstreamTasks:
  - taskName: OrderRecords
    key: result
# Strip prefix `_orderState.` from the column names of OrderState Records frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrderState
  params:
    action: strip
    prefix: _orderState.
  upstreamTasks:
  - taskName: OrderStateRecords
    key: result
# Vertically concatenate Order and OrderState Record frames created above.
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: VerticalConcatenator
  params:
    orient: vertical
    reset_index: true
    drop_index: true
  upstreamTasks:
  - taskName: StripPrefixOrder
    key: order_records
  - taskName: StripPrefixOrderState
    key: order_state_records
# Remove InvalidOrderStates
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: RemoveInvalidOrderStates
  params:
    query: "`executionDetails.orderStatus`.notnull()"
  upstreamTasks:
  - taskName: VerticalConcatenator
    key: result
# Remove duplicate new orders, remove synthetic new orders already in Elastic
- path: swarm_tasks.order.generic.remove_duplicate_newo:RemoveDuplicateNEWO
  name: RemoveDupNEWO
  params:
    newo_in_file_col: __newo_in_file__
    drop_newo_in_file_col: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: RemoveInvalidOrderStates
    key: result
#Best Execution
- path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
  name: BestExecution
  resources:
    es_client_key: reference-data
  upstreamTasks:
    - taskName: RemoveDupNEWO
      key: result
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: BestExecutionConcatenator
  params:
    orient: horizontal
    drop_columns:
    - marketIdentifiers.instrument
    - marketIdentifiers.parties
  upstreamTasks:
    - taskName: RemoveDupNEWO
      key: result
    - taskName: BestExecution
      key: best_ex_result

# Assign Meta
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: AssignMeta
  params:
    model_attribute: __meta_model__
    parent_attribute: __meta_parent__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: BestExecutionConcatenator
      key: result

# Post-meta concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: FinalConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
      - __meta_parent__
  upstreamTasks:
    - taskName: BestExecutionConcatenator
      key: result
    - taskName: AssignMeta
      key: meta
# Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: FinalConcatenator
    key: transform_result
  - taskName: FilterRowsInSourceFile
    key: producer_result
# Elastic Bulk Writer
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: ElasticBulkWriter
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: ElasticBulkTransformer
    key: result
# Instrument Mapper
- path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
  name: InstrumentMapper
  upstreamTasks:
    - taskName: FinalConcatenator
      key: source_frame
    - taskName: ElasticBulkWriter
      key: bulk_writer_result
# Quarantine Condition
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineCondition
  upstreamTasks:
  - taskName: ElasticBulkWriter
    key: bulk_writer_result
# Quarantine Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformer
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: FinalConcatenator
    key: transform_result
  - taskName: FilterRowsInSourceFile
    key: producer_result
  - taskName: ElasticBulkWriter
    key: bulk_writer_result
# Quarantine Elastic Bulk Writer
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedElasticBulkWriter
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: QuarantinedElasticBulkTransformer
    key: result
- path: swarm_tasks.control_flow.noop:Noop
  name: Noop
