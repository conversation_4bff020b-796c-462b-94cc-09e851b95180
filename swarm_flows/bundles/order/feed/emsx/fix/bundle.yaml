# Input: Csv file which contains S3 links to a batch of IBP fix files.
# All the fix files are downloaded locally, and processed together in one flow run.

# Methodology - Parse FIX message; build Pandas DataFrame with relevant data;
# Map data to fit Order and OrderState models; Follow order-universal-steeleye-trade-blotter pipeline
# Output - Write Order and OrderState trades to the associated tenant's Elastic index if new data,
# handle duplicate and quarantine data otherwise
# NOTE: If the "OrdStatus" tag of the .fix message IS NOT "1" nor "2" - flow creates Order record only
# If the "OrdStatus" tag of the .fix message IS NOT "0" - flow creates OrderState record only

# Confluence documentation: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2671345703/Order+EMSX+-+Bloomberg+-+FIX
id: order-feed-emsx-fix
name: Order Feed BBG EMSX FIX
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: flow_args
  envVar: SWARM_FLOW_ARGS
  description: "flow arguments"
controlFlows:
- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  merge: false
tasks:
# For a given directory in S3, i.e. ingress/raw/order-feed-emsx-fix/20220701,
# Download the existing FIX files and store its contents and location into a Pandas DataFrame
- path: swarm_tasks.io.read.aws.fetch_and_merge_text_files:FetchAndMergeTextFiles
  name: FetchAndMergeTextFiles
  upstreamTasks:
    - taskName: flow_args
      key: flow_args
# Read .fix file, parse, validate and convert to FixParserResult dataclass
# Note: the key has to be fix_dataframe as that's the argument name in execute()
- path: swarm_tasks.io.read.fix.fix_parser:FixParser
  name: FixParser
  upstreamTasks:
    - taskName: FetchAndMergeTextFiles
      key: fix_dataframe
# Convert FixParserResult to Pandas DataFrame
# Note: the key has to be fix_parsed_data as that's the argument name in execute()
- path: swarm_tasks.io.read.fix.fix_parser_result_to_frame:FixParserResultToFrame
  name: FixParserResultToFrame
  params:
    dataframe_columns:
      - Account
      - AvgPx
      - ClOrdID
      - Currency
      - ExecBroker
      - ExecID
      - ExecType
      - PartyID
      - PartyRole
      - ff_76
      - LastCapacity
      - LastQty
      - LastPx
      - LastMkt
      - LeavesQty
      - OrderID
      - OrderQty
      - OrdType
      - OrigClOrdID
      - Side
      - SecurityID
      - SettlCurrency
      - SettlDate
      - TimeInForce
      - TransactTime
  upstreamTasks:
    - taskName: FixParser
      key: fix_parsed_data
# Primary transformations
- path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
  name: PrimaryTransformations
  upstreamTasks:
    - taskName: FixParserResultToFrame
      key: result
# Link Parties
- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkParties
  params:
    identifiers_path: marketIdentifiers.parties
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryTransformations
      key: result
# Link Instruments
- path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
  name: LinkInstrument
  params:
    identifiers_path: marketIdentifiers.instrument
    currency_attribute: transactionDetails.priceCurrency
    venue_attribute: transactionDetails.ultimateVenue
  resources:
    ref_data_key: reference-data
    tenant_data_key: tenant-data
  upstreamTasks:
  - taskName: PrimaryTransformations
    key: result
# Instrument Fallback
- path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
  name: InstrumentFallback
  params:
    instrument_fields_map:
      - source_field: transactionDetails.priceCurrency
        target_field: notionalCurrency1
      - source_field: transactionDetails.quantityCurrency
        target_field: fxDerivatives.notionalCurrency2
      - source_field: transactionDetails.ultimateVenue
        target_field: ext.venueName
      - source_field: transactionDetails.ultimateVenue
        target_field: venue.tradingVenue
      - source_field: __instrument_created_through_fb__
        target_field: isCreatedThroughFallback
    str_to_bool_dict:
      "on": True
      "off": False
  upstreamTasks:
    - taskName: PrimaryTransformations
      key: result
    - taskName: LinkInstrument
      key: link_instrument
# Link each OrderState record to the parent Order record
- path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
  name: AssignMetaParent
  params:
      parent_model_attribute: _order.__meta_model__
      parent_attributes_prefix: _order.
      target_attribute: _orderState.__meta_parent__
  upstreamTasks:
    - taskName: PrimaryTransformations
      key: result
# Frame Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PrimaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
      - __instrument_created_through_fb__
      - __security_id__
      - asset_class_attribute # Instrument Identifiers columns
      - bbg_figi_id_attribute
      - currency_attribute
      - eurex_id_attribute
      - exchange_symbol_attribute
      - expiry_date_attribute
      - interest_rate_start_date_attribute
      - isin_attribute
      - notional_currency_1_attribute
      - notional_currency_2_attribute
      - option_strike_price_attribute
      - option_type_attribute
      - swap_near_leg_date_attribute
      - underlying_index_name_attribute
      - underlying_index_name_leg_2_attribute
      - underlying_index_series_attribute
      - underlying_index_term_attribute
      - underlying_index_term_value_attribute
      - underlying_index_version_attribute
      - underlying_isin_attribute
      - underlying_symbol_attribute
      - underlying_symbol_expiry_code_attribute
      - underlying_index_term_leg_2_attribute
      - underlying_index_term_value_leg_2_attribute
      - venue_attribute
      - venue_financial_instrument_short_name_attribute
      - instrument_classification_attribute
  upstreamTasks:
    - taskName: AssignMetaParent
      key: result
    - taskName: PrimaryTransformations
      key: primary_transformations
    - taskName: InstrumentFallback
      key: instrument_fallback
    - taskName: LinkParties
      key: link_parties
# Filter only Order records into a data frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderRecords
  params:
    except_prefix: _orderState.
    strip_prefix: true
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    key: result
# Filter only OrderState records into a data frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderStateRecords
  params:
    except_prefix: _order.
    strip_prefix: true
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    key: result
# Strip prefix `_order.` from the column names of OrderRecords frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrder
  params:
    action: strip
    prefix: _order.
  upstreamTasks:
  - taskName: OrderRecords
    key: result
# Strip prefix `_orderState.` from the column names of OrderState Records frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrderState
  params:
    action: strip
    prefix: _orderState.
  upstreamTasks:
  - taskName: OrderStateRecords
    key: result
# Vertically concatenate Order and OrderState Record frames created above.
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: VerticalConcatenator
  params:
    orient: vertical
    reset_index: true
    drop_index: true
  upstreamTasks:
  - taskName: StripPrefixOrder
    key: order_records
  - taskName: StripPrefixOrderState
    key: order_state_records
# Remove InvalidOrderStates
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: RemoveInvalidOrderStates
  params:
    query: "`executionDetails.orderStatus`.notnull()"
  upstreamTasks:
  - taskName: VerticalConcatenator
    key: result
# Remove duplicate new orders, remove synthetic new orders already in Elastic
- path: swarm_tasks.order.generic.remove_duplicate_newo:RemoveDuplicateNEWO
  name: RemoveDupNEWO
  params:
    newo_in_file_col: __newo_in_file__
    drop_newo_in_file_col: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: RemoveInvalidOrderStates
    key: result
#Best Execution
- path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
  name: BestExecution
  resources:
    es_client_key: reference-data
  upstreamTasks:
    - taskName: RemoveDupNEWO
      key: result
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: BestExecutionConcatenator
  params:
    orient: horizontal
    drop_columns:
    - marketIdentifiers.instrument
    - marketIdentifiers.parties
    - __newo_in_file__
  upstreamTasks:
    - taskName: RemoveDupNEWO
      key: result
    - taskName: BestExecution
      key: best_ex_result

# Assign Meta
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: AssignMeta
  params:
    model_attribute: __meta_model__
    parent_attribute: __meta_parent__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: BestExecutionConcatenator
      key: result

# Post-meta concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: FinalConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
      - __meta_parent__
  upstreamTasks:
    - taskName: BestExecutionConcatenator
      key: result
    - taskName: AssignMeta
      key: meta
# Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: FinalConcatenator
    key: transform_result
  - taskName: FixParserResultToFrame
    key: producer_result
# Elastic Bulk Writer
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsent
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: ElasticBulkTransformer
    key: result
# Instrument Mapper
- path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
  name: InstrumentMapper
  upstreamTasks:
    - taskName: FinalConcatenator
      key: source_frame
    - taskName: PutIfAbsent
      key: bulk_writer_result
# Quarantine Condition
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineCondition
  upstreamTasks:
  - taskName: PutIfAbsent
    key: bulk_writer_result
# Quarantine Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformer
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: FinalConcatenator
    key: transform_result
  - taskName: FixParserResultToFrame
    key: producer_result
  - taskName: PutIfAbsent
    key: bulk_writer_result
# Quarantine Elastic Bulk Writer
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsent
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: QuarantinedElasticBulkTransformer
    key: result
- path: swarm_tasks.control_flow.noop:Noop
  name: Noop
