# Confluence Page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2267938859/Order+Enfusion+new
# Bundle path -> order/feed/enfusion/v2
id: order-feed-enfusion-v2
name: Order Enfusion V2
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: ConditionalCsvFileSplitter
  conditionTaskName: ParamIsBatchedFile
  trueTaskName: BatchedFileCsvSplitter
  falseTaskName: NormalFileCsvSplitter
  merge: true
- name: AllocationOrExecution
  conditionTaskName: AllocationOrExecutionController
  trueTaskName: SkipNullOrderIdAndOrderSide
  falseTaskName: PrimaryTransformationExecutions
  mapped: true
  merge: false
- name: NeedsQuarantineAllo
  conditionTaskName: QuarantineConditionAllo
  trueTaskName: QuarantinedElasticBulkTransformerAllo
  falseTaskName: NoopAllo
  mapped: true
  merge: false
- name: NeedsQuarantineExe
  conditionTaskName: QuarantineConditionExe
  trueTaskName: QuarantinedElasticBulkTransformerExe
  falseTaskName: NoopExe
  mapped: true
  merge: false
- name: FileTooBig
  conditionTaskName: HorizontalBatchController
  trueTaskName: BatchProducer
  falseTaskName: MergeAndChunkCsvFiles
  mapped: false
  merge: false
tasks:
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
    - taskName: file_url
      mapped: false
      key: file_url
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
    - taskName: file_url
      mapped: false
      key: file_url
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParamIsBatchedFile
  params:
    arg_name: file_url
    contains: batch # batched file will always contain 'batch' in name
    ignore_case: true
  upstreamTasks:
    - taskName: file_url
      mapped: false
      key: file_url
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
    - taskName: file_url
      mapped: false
      key: file_url
- path: swarm_tasks.order.feed.enfusion.v2.pre_process_skip_rows:PreProcessSkipRows
  name: PreProcessSkipRows
  upstreamTasks:
    - taskName: S3OrLocalFile
      mapped: false
      key: extractor_result
- path: swarm_tasks.order.feed.enfusion.v2.allocation_record_mapper:AllocationRecordMapper
  name: AllocationRecordMapper
  params:
    allocation_aggregation_flag: false
  upstreamTasks:
    - taskName: S3OrLocalFile
      mapped: false
      key: file_url
- path: swarm_tasks.order.feed.enfusion.v2.execution_record_mapper:ExecutionRecordMapper
  name: ExecutionRecordMapper
  params:
    fill_by_fill_flag: true
  upstreamTasks:
    - taskName: S3OrLocalFile
      mapped: false
      key: file_url
- path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
  name: NormalFileCsvSplitter
  params:
    chunksize: &batches_rowsize 10000
    normalise_columns: true
  upstreamTasks:
    - taskName: S3OrLocalFile
      mapped: false
      key: extractor_result
    - taskName: PreProcessSkipRows
      mapped: false
      key: skiprows
- path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
  name: BatchedFileCsvSplitter
  params:
    chunksize: *batches_rowsize
  upstreamTasks:
    - taskName: S3OrLocalFile
      mapped: false
      key: extractor_result
- path: swarm_tasks.io.read.horizontal_batch_controller:HorizontalBatchController
  name: HorizontalBatchController
  params:
    max_chunk_size: &batches_chunksize 5
  upstreamTasks:
    - taskName: ConditionalCsvFileSplitter
      mapped: false
      key: list_of_batches
- path: swarm_tasks.io.read.merge_and_chunk_csv_files:MergeAndChunkCsvFiles
  name: MergeAndChunkCsvFiles
  params:
    max_chunk_size: *batches_chunksize
  upstreamTasks:
    - taskName: ConditionalCsvFileSplitter
      mapped: false
      key: file_splitter_result_list
    - taskName: file_url
      mapped: false
      key: file_url
- path: swarm_tasks.transform.extract_path.s3_file_list_from_frame_splitter_result_list:S3FileListFileSplitterResultList
  name: S3FileListFileSplitterResultList
  params:
    cloud_key_prefix: "flows/order-feed-enfusion-v2/batches"
    datetime_field_in_file_path: false
  upstreamTasks:
    - taskName: MergeAndChunkCsvFiles
      mapped: false
      key: file_splitter_result_list
    - taskName: file_url
      mapped: false
      key: file_url
- path: swarm_tasks.io.write.aws.s3_upload_file:S3UploadFile
  name: S3UploadOrderFile
  upstreamTasks:
    - taskName: S3FileListFileSplitterResultList
      mapped: false
      key: upload_target
- path: swarm_tasks.io.read.batch_producer:BatchProducer
  name: BatchProducer
  params:
    source_schema:
      "BBYELLOW": string
      "BRANCHLOCATION": string
      "BUYER-DATEOFBIRTH": string
      "BUYER-FIRSTNAME(S)": string
      "BUYER-SURNAME(S)": string
      "BUYERDECISIONMAKER-DATEOFBIRTH": string
      "BUYERDECISIONMAKER-FIRSTNAME(S)": string
      "BUYERDECISIONMAKER-SURNAME(S)": string
      "BUYERDECISIONMAKERCODE": string
      "BUYERDECISIONMAKERCODETYPE": string
      "BUYERDECISIONMAKERNPCODE": string
      "BUYERIDENTIFICATIONCODE": string
      "BUYERIDENTIFICATIONCODETYPE": string
      "BUYERNPCODE": string
      "COMMISSIONTYPE": string
      "COMMISSIONS": float
      "COMMODITYDERIVATIVEINDICATOR": string
      "COMPLEXTRADECOMPONENTID": string
      "COUNTERPARTY": string
      "COUNTRYOFBRANCHMEMBERSHIP": string
      "COUNTRYOFTHEBRANCHFORTHEBUYER": string
      "COUNTRYOFTHEBRANCHFORTHESELLER": string
      "COUNTRYOFTHEBRANCHRESPONSIBLEFORTHEPERSONMAKINGTHEINVESTMENTDECISION": string
      "COUNTRYOFTHEBRANCHSUPERVISINGTHEPERSONRESPONSIBLEFORTHEEXECUTION": string
      "CURRENCY": string
      "CURRENCY2": string
      "CUSIP": string
      "DELIVERYTYPE": string
      "DERIVATIVENOTIONALINCREASE/DECREASE": string
      "DESCRIPTION": string
      "EVENTTYPE": string
      "EXCHANGEMICCODE": string
      "EXCHANGESHORTNAME": string
      "EXECID": string
      "EXECREFID": string
      "EXECUTIONDATE": string
      "EXECUTIONENTITYIDENTIFICATIONCODE": string
      "EXECUTIONORDERTYPE": string
      "EXECUTIONTIME": string
      "EXECUTIONWITHINFIRM": string
      "EXECUTIONWITHINFIRM-TYPE": string
      "EXECUTIONWITHINFIRMNPCODE": string
      "EXPIRYDATE": string
      "FILLER/NETAMOUNT": float
      "FUTUREBLOOMBERGROOT": string
      "FUTUREEXPIRATIONDATE": string
      "INSTRUCTIONS": string
      "INSTRUMENTCLASSIFICATION": string
      "INSTRUMENTFULLNAME": string
      "INSTRUMENTIDENTIFICATIONCODE": string
      "INVESTMENTDECISIONWITHFIRM": string
      "INVESTMENTDECISIONWITHFIRM-TYPE": string
      "INVESTMENTDECISIONWITHINFIRMNPCODE": string
      "INVESTMENTFIRMCONVEREDBY2014/65/EU": string
      "ISIN": string
      "LASTFILLTIME": string
      "LASTPX": float
      "LASTQTY": float
      "LENAME": string
      "LIFECYCLEEVENT": string
      "MATURITYDATE": string
      "NETAMOUNT": string
      "NOTIONALCURRENCY1": string
      "NOTIONALCURRENCY2": string
      "OPTIONCONTRACTBLOOMBERGROOTCODE": string
      "OPTIONEXERCISESTYLE": string
      "OPTIONEXPIRATIONDATE": string
      "OPTIONSTRIKE": float
      "OPTIONTYPE": string
      "ORDERCUMULATIVEQUANTITY": string
      "ORDERDATE": string
      "ORDEREXECUTIONDESTINATION": string
      "ORDERID": string
      "ORDERLIMITPRICE": float
      "ORDERREFERENCE": string
      "ORDERREMAININGQUANTITY": float
      "ORDERSIDE": string
      "ORDERSTATUS": string
      "ORDERSTATUSTEXT": string
      "ORDERSTOPPRICE": float
      "ORDERTIMEINFORCE": string
      "ORDERTIMESTAMP": string
      "ORDERTOTALQUANTITY": float
      "ORDERTYPE": string
      "OSI": string
      "OTCPOST-TRADEINDICATOR": string
      "PARENTORDERID": string
      "PARENTORDERREMAININGQUANTITY": string
      "PORTFOLIOMANAGER": string
      "PRICE": float
      "PRICE-TYPE": string
      "PRICECURRENCY": string
      "PRICEMULTIPLIER": float
      "PROGRAMID": string
      "QUANTITY": float
      "QUANTITYCURRENCY": string
      "QUANTITYTYPE": string
      "REPORTSTATUS": string
      "RIC": string
      "SECURITIESFINANCINGTRANSACTIONINDICATOR": string
      "SEDOL": string
      "SELLER-DATEOFBIRTH": string
      "SELLER-FIRSTNAME(S)": string
      "SELLER-SURNAME(S)": string
      "SELLERDECISIONMAKER-DATEOFBIRTH": string
      "SELLERDECISIONMAKER-FIRSTNAME(S)": string
      "SELLERDECISIONMAKER-SURNAME(S)": string
      "SELLERDECISIONMAKERCODE": string
      "SELLERDECISIONMAKERCODETYPE": string
      "SELLERDECISIONMAKERNPCODE": string
      "SELLERIDENTIFICATIONCODE": string
      "SELLERIDENTIFICATIONCODETYPE": string
      "SELLERNPCODE": string
      "SENDTOSTEELEYE": string
      "SENDTOSTEELEYEELIGIBLE": string
      "SHORTSELLINGINDICATOR": string
      "STRIKEPRICE": float
      "STRIKEPRICECURRENCY": string
      "STRIKEPRICETYPE": string
      "TERMOFUNDERLYINGINDEX-VALUE": string
      "TICKER": string
      "TRADECANCELED": string
      "TRADER": string
      "TRADINGCAPACITY": string
      "TRADINGDATETIME": string
      "TRADINGVENUETRANSACTIONIDENTIFICATIONCODE": string
      "TRANSACTIONREFERENCENUMBER": string
      "TRANSACTIONTYPE": string
      "TRANSMISSIONOFORDERINDICATOR": string
      "TRANSMITTINGFIRMIDENTIFICATIONCODEFORTHEBUYER": string
      "TRANSMITTINGFIRMIDENTIFICATIONCODEFORTHESELLER": string
      "UNDERLYINGINDEXNAME": string
      "UNDERLYINGINSTRUMENTCODE": string
      "UP-FRONTPAYMENT": string
      "UP-FRONTPAYMENTCURRENCY": string
      "VENUE": string
      "WAIVEINDICATOR": string
  upstreamTasks:
    - taskName: ConditionalCsvFileSplitter
      mapped: true
      flatten: true
      key: file_splitter_result

# The flow separates in two branches. One to process Allocation files and other to process Execution files
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: AllocationOrExecutionController
  params:
    arg_name: file_url
    contains: allocations
    ignore_case: true
  upstreamTasks:
    - taskName: file_url
      mapped: false
      key: file_url
    - taskName: BatchProducer
      mapped: true

# ----------------------- Allocations path -----------------------

# Remove rows that don't have both Order Id and Order Side
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: SkipNullOrderIdAndOrderSide
  params:
    query: "((`ORDERID`.notnull() | `ORDERSIDE`.notnull()) & ~(`SENDTOSTEELEYE`.astype('str').str.fullmatch('RESEND',case=False,na=False)))"
    skip_on_empty: true
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result

# Transforming the source file when file type is allocation.
# Note: This increase the shape of the dataframe
- path: swarm_tasks.order.feed.enfusion.v2.allocation_frame_transformer:AllocationFrameTransformer
  name: AllocationFrameTransformer
  upstreamTasks:
    - taskName: SkipNullOrderIdAndOrderSide
      mapped: true
      key: result

# Primary Transformations
- path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
  name: PrimaryTransformationAllocations
  upstreamTasks:
    - taskName: AllocationFrameTransformer
      mapped: true
      key: result
    - taskName: AllocationRecordMapper
      mapped: false
      key: allocation_aggregation_map

# Link Parties
- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkPartiesAllo
  params:
    identifiers_path: marketIdentifiers.parties
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryTransformationAllocations
      mapped: true
      key: result

- path: swarm_tasks.order.generic.party_fallback_with_lei_lookup:PartyFallbackWithLeiLookup
  name: PartyFallbackWithLeiLookupAllocations
  upstreamTasks:
    - taskName: PrimaryTransformationAllocations
      mapped: true
      key: result
    - taskName: LinkPartiesAllo
      mapped: true
      key: link_parties_result

# Link Instrument
- path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
  name: LinkInstrumentAllo
  params:
    identifiers_path: marketIdentifiers.instrument
    asset_class_attribute: __asset_class__
    currency_attribute: __isntr_ids_currency__
    venue_attribute: __venue__
  resources:
    ref_data_key: reference-data
    tenant_data_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryTransformationAllocations
      mapped: true
      key: result

# Instrument Fallback
- path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
  name: InstrumentFallbackAlloc
  params:
    instrument_fields_map:
      - source_field: __price_reference_ric__
        target_field: ext.pricingReferences.RIC
      - source_field: transactionDetails.priceCurrency
        target_field: notionalCurrency1
      - source_field: transactionDetails.quantityCurrency
        target_field: fxDerivatives.notionalCurrency2
      - source_field: transactionDetails.priceCurrency
        target_field: derivative.strikePriceCurrency
      - source_field: __instrument_full_name__
        target_field: instrumentFullName
      - source_field: __instrument_unique_identifier__
        target_field: ext.instrumentUniqueIdentifier
      - source_field: __instrument_classification__
        target_field: instrumentClassification
      - source_field: __created_through_fallback__
        target_field: isCreatedThroughFallback
    str_to_bool_dict:
      "true": True
      "false": False
  upstreamTasks:
    - taskName: PrimaryTransformationAllocations
      mapped: true
      key: result
    - taskName: LinkInstrumentAllo
      mapped: true
      key: link_instrument

# Link each OrderState record to the parent Order record
- path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
  name: AssignMetaParentAllo
  params:
      parent_model_attribute: _order.__meta_model__
      parent_attributes_prefix: _order.
      target_attribute: _orderState.__meta_parent__
  upstreamTasks:
    - taskName: PrimaryTransformationAllocations
      mapped: true
      key: result

# Auxiliary frame concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: AuxiliaryFrameConcatenatorAllo
  params:
    orient: horizontal
    drop_columns:
      - marketIdentifiers.instrument
      - marketIdentifiers.parties
      - __synth_parent.buySell
      - __synth_parent.executionDetails.orderStatus
      - __synth_parent.id
      - __synth_parent.__meta_model__
      - __asset_class__
      - __isntr_ids_currency__
      - __venue__
      - __created_through_fallback__
      - __fallback_buyer__
      - __fallback_seller__
      - __fallback_counterparty__
      - __fallback_client__
      - __fallback_buyer_dec_maker__
      - __fallback_seller_dec_maker__
      - __fallback_inv_dec_in_firm__
      - __fallback_trader__
      - __fallback_exec_within_firm__
      - __fallback_executing_entity__
      - __instrument_full_name__
      - __instrument_classification__
      - __instrument_unique_identifier__
      - __price_reference_ric__
      - asset_class_attribute # Instrument Identifiers columns
      - bbg_figi_id_attribute
      - currency_attribute
      - eurex_id_attribute
      - exchange_symbol_attribute
      - expiry_date_attribute
      - interest_rate_start_date_attribute
      - isin_attribute
      - notional_currency_1_attribute
      - notional_currency_2_attribute
      - option_strike_price_attribute
      - option_type_attribute
      - swap_near_leg_date_attribute
      - underlying_index_name_attribute
      - underlying_index_name_leg_2_attribute
      - underlying_index_series_attribute
      - underlying_index_term_attribute
      - underlying_index_term_value_attribute
      - underlying_index_version_attribute
      - underlying_isin_attribute
      - underlying_symbol_attribute
      - underlying_symbol_expiry_code_attribute
      - underlying_index_term_leg_2_attribute
      - underlying_index_term_value_leg_2_attribute
      - venue_attribute
      - venue_financial_instrument_short_name_attribute
      - instrument_classification_attribute
  upstreamTasks:
    - taskName: PrimaryTransformationAllocations
      mapped: true
      key: primary_transformations
    - taskName: InstrumentFallbackAlloc
      mapped: true
      key: instrument_fallback_alloc
    - taskName: AssignMetaParentAllo
      mapped: true
      key: parent_id
    - taskName: PartyFallbackWithLeiLookupAllocations
      mapped: true
      key: party_fallback

# Filter only Order records into a data frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderRecords
  params:
    except_prefix: _orderState.
    strip_prefix: true
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenatorAllo
    mapped: true
    key: result

# Filter only OrderState records into a data frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderStateRecords
  params:
    except_prefix: _order.
    strip_prefix: true
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenatorAllo
    mapped: true
    key: result

# Strip prefix `_order.` from the column names of OrderRecords frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrder
  params:
    action: strip
    prefix: _order.
  upstreamTasks:
  - taskName: OrderRecords
    mapped: true
    key: result

# Strip prefix `_orderState.` from the column names of OrderState Records frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrderState
  params:
    action: strip
    prefix: _orderState.
  upstreamTasks:
  - taskName: OrderStateRecords
    mapped: true
    key: result

# Vertically concatenate Order and OrderState Record frames created above.
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: VerticalConcatenator
  params:
    orient: vertical
    reset_index: true
    drop_index: true
  upstreamTasks:
  - taskName: StripPrefixOrder
    mapped: true
    key: order_records
  - taskName: StripPrefixOrderState
    mapped: true
    key: order_state_records

# Remove InvalidOrderStates
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: RemoveInvalidOrderStates
  params:
    query: "`executionDetails.orderStatus`.notnull()"
  upstreamTasks:
  - taskName: VerticalConcatenator
    mapped: true
    key: result

# Remove synthetic NEWO records created for Cancels
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: RemoveCancelNewOrders
  params:
    query: "`__newo_cancel_indicator__`.eq(False)"
  upstreamTasks:
    - taskName: RemoveInvalidOrderStates
      mapped: true
      key: result

# Remove duplicate new orders, remove synthetic new orders already in Elastic
- path: swarm_tasks.order.generic.remove_duplicate_newo:RemoveDuplicateNEWO
  name: RemoveDupNEWO
  params:
    newo_in_file_col: __newo_in_file__
    drop_newo_in_file_col: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: RemoveCancelNewOrders
    mapped: true
    key: result

#Best Execution
- path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
  name: BestExecutionAllo
  resources:
    es_client_key: reference-data
  upstreamTasks:
    - taskName: RemoveDupNEWO
      mapped: true
      key: result
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: BestExecutionConcatenatorAllo
  params:
    orient: horizontal
    drop_columns:
      - __newo_cancel_indicator__
  upstreamTasks:
    - taskName: RemoveDupNEWO
      mapped: true
      key: result
    - taskName: BestExecutionAllo
      mapped: true
      key: best_ex_result

# Assign Meta
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: AssignMetaAllo
  params:
    model_attribute: __meta_model__
    parent_attribute: __meta_parent__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: BestExecutionConcatenatorAllo
      mapped: true
      key: result

# Post-meta concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PostMetaConcatenatorAllo
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
      - __meta_parent__
  upstreamTasks:
    - taskName: BestExecutionConcatenatorAllo
      mapped: true
      key: result
    - taskName: AssignMetaAllo
      mapped: true
      key: meta

- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformerAllo
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PostMetaConcatenatorAllo
      mapped: true
      key: transform_result
    - taskName: BatchProducer
      mapped: true
      key: producer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsentAllo
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: ElasticBulkTransformerAllo
      mapped: true
      key: result

# Instrument Mapper
- path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
  name: InstrumentMapperAllo
  upstreamTasks:
    - taskName: PostMetaConcatenatorAllo
      mapped: true
      key: source_frame
    - taskName: PutIfAbsentAllo
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineConditionAllo
  upstreamTasks:
    - taskName: PutIfAbsentAllo
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformerAllo
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PostMetaConcatenatorAllo
      mapped: true
      key: transform_result
    - taskName: BatchProducer
      mapped: true
      key: producer_result
    - taskName: PutIfAbsentAllo
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsentAllo
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: QuarantinedElasticBulkTransformerAllo
      mapped: true
      key: result
- path: swarm_tasks.control_flow.noop:Noop
  name: NoopAllo

# ----------------------- Executions path -----------------------
# Primary transformations
- path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
  name: PrimaryTransformationExecutions
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
    - taskName: ExecutionRecordMapper
      mapped: false
      key: fill_by_fill_map

# Link Parties
- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkPartiesExe
  params:
    identifiers_path: marketIdentifiers.parties
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryTransformationExecutions
      mapped: true
      key: result

- path: swarm_tasks.order.generic.party_fallback_with_lei_lookup:PartyFallbackWithLeiLookup
  name: PartyFallbackWithLeiLookupExecutions
  upstreamTasks:
    - taskName: PrimaryTransformationExecutions
      mapped: true
      key: result
    - taskName: LinkPartiesExe
      mapped: true
      key: link_parties_result

# Link Instrument
- path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
  name: LinkInstrumentExe
  params:
    identifiers_path: marketIdentifiers.instrument
    asset_class_attribute: __asset_class__
    currency_attribute: __isntr_ids_currency__
    venue_attribute: __venue__
  resources:
    ref_data_key: reference-data
    tenant_data_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryTransformationExecutions
      mapped: true
      key: result

# Instrument Fallback
- path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
  name: InstrumentFallbackExe
  params:
    instrument_fields_map:
      - source_field: __price_reference_ric__
        target_field: ext.pricingReferences.RIC
      - source_field: transactionDetails.priceCurrency
        target_field: notionalCurrency1
      - source_field: transactionDetails.quantityCurrency
        target_field: fxDerivatives.notionalCurrency2
      - source_field: transactionDetails.priceCurrency
        target_field: derivative.strikePriceCurrency
      - source_field: __instrument_unique_identifier__
        target_field: ext.instrumentUniqueIdentifier
      - source_field: __instrument_full_name__
        target_field: instrumentFullName
      - source_field: __instrument_classification__
        target_field: instrumentClassification
      - source_field: __created_through_fallback__
        target_field: isCreatedThroughFallback
    str_to_bool_dict:
      "true": True
      "false": False
  upstreamTasks:
    - taskName: PrimaryTransformationExecutions
      mapped: true
      key: result
    - taskName: LinkInstrumentExe
      mapped: true
      key: link_instrument

# Link each OrderState record to the parent Order record
- path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
  name: AssignMetaParentExe
  params:
    parent_model_attribute: __synth_parent.__meta_model__
    parent_attributes_prefix: __synth_parent.
    target_attribute: __meta_parent__
  upstreamTasks:
    - taskName: PrimaryTransformationExecutions
      mapped: true
      key: result

# Removes __meta_parent__ from NEWO rows
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: RemoveNewoMetaParentExe
  paramsList:
    - target_attribute: __meta_parent_filtered__
      cases:
        - query: "`executionDetails.orderStatus` != 'NEWO'"
          attribute: __meta_parent__
  upstreamTasks:
    - taskName: PrimaryTransformationExecutions
      mapped: true
      key: result
    - taskName: AssignMetaParentExe
      mapped: true
      key: meta_parent_res

# Auxiliary frame concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: AuxiliaryFrameConcatenatorExe
  params:
    orient: horizontal
    drop_columns:
      - marketIdentifiers.instrument
      - marketIdentifiers.parties
      - __synth_parent.buySell
      - __synth_parent.executionDetails.orderStatus
      - __synth_parent.id
      - __synth_parent.__meta_model__
      - __asset_class__
      - __isntr_ids_currency__
      - __venue__
      - __created_through_fallback__
      - __fallback_buyer__
      - __fallback_seller__
      - __fallback_counterparty__
      - __fallback_client__
      - __fallback_buyer_dec_maker__
      - __fallback_seller_dec_maker__
      - __fallback_inv_dec_in_firm__
      - __fallback_trader__
      - __fallback_exec_within_firm__
      - __fallback_executing_entity__
      - __instrument_full_name__
      - __instrument_classification__
      - __instrument_unique_identifier__
      - __price_reference_ric__
      - asset_class_attribute # Instrument Identifiers columns
      - bbg_figi_id_attribute
      - currency_attribute
      - eurex_id_attribute
      - exchange_symbol_attribute
      - expiry_date_attribute
      - interest_rate_start_date_attribute
      - isin_attribute
      - notional_currency_1_attribute
      - notional_currency_2_attribute
      - option_strike_price_attribute
      - option_type_attribute
      - swap_near_leg_date_attribute
      - underlying_index_name_attribute
      - underlying_index_name_leg_2_attribute
      - underlying_index_series_attribute
      - underlying_index_term_attribute
      - underlying_index_term_value_attribute
      - underlying_index_version_attribute
      - underlying_isin_attribute
      - underlying_symbol_attribute
      - underlying_symbol_expiry_code_attribute
      - underlying_index_term_leg_2_attribute
      - underlying_index_term_value_leg_2_attribute
      - venue_attribute
      - venue_financial_instrument_short_name_attribute
      - instrument_classification_attribute
  upstreamTasks:
    - taskName: PrimaryTransformationExecutions
      mapped: true
      key: primary_transformations
    - taskName: InstrumentFallbackExe
      mapped: true
      key: instrument_fallback_exe
    - taskName: RemoveNewoMetaParentExe
      mapped: true
      key: parent_id
    - taskName: PartyFallbackWithLeiLookupExecutions
      mapped: true
      key: party_fallback

- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: RemoveInvalidOrderStatesExe
  params:
    query: "`executionDetails.orderStatus`.notnull()"
  upstreamTasks:
    - taskName: AuxiliaryFrameConcatenatorExe
      mapped: true
      key: result

#Best Execution
- path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
  name: BestExecutionExe
  resources:
    es_client_key: reference-data
  upstreamTasks:
    - taskName: RemoveInvalidOrderStatesExe
      mapped: true
      key: result
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: BestExecutionConcatenatorExe
  params:
    orient: horizontal
  upstreamTasks:
    - taskName: RemoveInvalidOrderStatesExe
      mapped: true
      key: result
    - taskName: BestExecutionExe
      mapped: true
      key: best_ex_result

# Assign Meta
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: AssignMetaExe
  params:
    model_attribute: __meta_model__
    parent_attribute: __meta_parent_filtered__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: BestExecutionConcatenatorExe
      mapped: true
      key: result

# Post-meta concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PostMetaConcatenatorExe
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
      - __meta_parent_filtered__
  upstreamTasks:
    - taskName: BestExecutionConcatenatorExe
      mapped: true
      key: result
    - taskName: AssignMetaExe
      mapped: true
      key: meta

- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformerExe
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PostMetaConcatenatorExe
      mapped: true
      key: transform_result
    - taskName: BatchProducer
      mapped: true
      key: producer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsentExe
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: ElasticBulkTransformerExe
      mapped: true
      key: result

# Instrument Mapper
- path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
  name: InstrumentMapperExe
  upstreamTasks:
    - taskName: PostMetaConcatenatorExe
      mapped: true
      key: source_frame
    - taskName: PutIfAbsentExe
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineConditionExe
  upstreamTasks:
    - taskName: PutIfAbsentExe
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformerExe
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PostMetaConcatenatorExe
      mapped: true
      key: transform_result
    - taskName: BatchProducer
      mapped: true
      key: producer_result
    - taskName: PutIfAbsentExe
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsentExe
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: QuarantinedElasticBulkTransformerExe
      mapped: true
      key: result
- path: swarm_tasks.control_flow.noop:Noop
  name: NoopExe
