# Confluence page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/1501790412/Order+Enfusion
id: order-feed-enfusion
name: Order Enfusion
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  mapped: true
  merge: false
tasks:
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.xls_to_csv_converter:XlsToCsvConverter
  name: XlsToCsvConverter
  params:
    source_date_columns:
      - Expiry Date
      - Order Date
      - Last Fill Time
  upstreamTasks:
    - taskName: S3OrLocalFile
      mapped: false
      key: extractor_result
- path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
  name: CsvFileSplitter
  params:
    chunksize: 50000
  upstreamTasks:
    - taskName: XlsToCsvConverter
      mapped: false
      key: extractor_result
- path: swarm_tasks.io.read.batch_producer:BatchProducer
  name: BatchProducer
  params:
    source_schema:
      "Additional Information": string
      "Buyer Decision Maker Code": string
      "Buyer Identification Code": string
      "Commissions": string
      "Commission Type": string
      "Counterparty": string
      "Exchange MIC Code": string
      "Execution Order Type": string
      "Expiry Date": string
      "Instrument Identification Code": string
      "Last Fill Time": string
      "Notional Currency 2": string
      "Option Contract Bloomberg Root Code": string
      "Option Strike": float
      "Option Type": string
      "Order Date": string
      "Order Id": string
      "Order Limit Price": float
      "Order Remaining Quantity": float
      "Order Status": string
      "Order Stop Price": float
      "Order Time In Force": string
      "Order Total Quantity": float
      "Portfolio Manager": string
      "Price": float
      "Price Currency": string
      "Price Multiplier": float
      "Price - Type": string
      "Quantity": float
      "Quantity Type": string
      "Seller Decision Maker Code": string
      "Seller Identification Code": string
      "Term of Underlying Index - Value": float
      "Trading Capacity": string
      "Transaction Reference Number": string
      "Transaction Type": string
      "Transmission of Order Indicator": string
      "Underlying Index Name": string
      "Underlying Instrument Code": string
      "Venue": string
  upstreamTasks:
  - taskName: CsvFileSplitter
    mapped: true
    flatten: true
    key: file_splitter_result
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: FilterRowsInSourceFile
  params:
    query: "`Order Id`.notnull()"
  upstreamTasks:
  - taskName: BatchProducer
    mapped: true
    key: result
  # primary transformations
- path: swarm_tasks.transform.datetime.convert_datetime:ConvertDatetime
  name: ConvertDatetime
  paramsList:
    - source_attribute: Order Date
      target_attribute: date
      source_attribute_format: "%Y-%m-%dT%H:%M:%S.%fZ"
      convert_to: date
    - source_attribute: Order Date
      target_attribute: timestamps.orderReceived
      source_attribute_format: "%Y-%m-%dT%H:%M:%S.%fZ"
      convert_to: datetime
    - source_attribute: Last Fill Time
      target_attribute: timestamps.tradingDateTime
      source_attribute_format: "%Y-%m-%dT%H:%M:%S.%fZ"
      convert_to: datetime
    - source_attribute: Expiry Date
      target_attribute: __expiry_date__
      source_attribute_format: "%Y-%m-%dT%H:%M:%S.%fZ"
      convert_to: date
  upstreamTasks:
    - taskName: FilterRowsInSourceFile
      mapped: true
      key: result
- path: swarm_tasks.generic.currency.convert_minor_to_major:ConvertMinorToMajor
  name: ConvertMinorToMajor
  paramsList:
    - source_ccy_attribute: Price Currency
      target_ccy_attribute: __price_currency__
    - source_ccy_attribute: Price Currency
      source_price_attribute: Price
      target_price_attribute: priceFormingData.price
      cast_to: abs
    - source_ccy_attribute: Price Currency
      source_price_attribute: Price
      target_price_attribute: transactionDetails.price
      cast_to: abs
    - source_ccy_attribute: Price Currency
      source_price_attribute: Order Stop Price
      target_price_attribute: executionDetails.stopPrice
      cast_to: abs
    - source_ccy_attribute: Price Currency
      source_price_attribute: Order Limit Price
      target_price_attribute: executionDetails.limitPrice
      cast_to: abs
    - source_ccy_attribute: Price Currency
      source_price_attribute: Option Strike Price
      target_price_attribute: __option_strike_price__
      cast_to: abs
  upstreamTasks:
    - taskName: FilterRowsInSourceFile
      mapped: true
      key: result
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: CastToInteger
  paramsList:
  - source_attribute: Order Id
    target_attribute: __order_id__
    cast_to: integer
  upstreamTasks:
    - taskName: FilterRowsInSourceFile
      mapped: true
      key: result
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: MapAttributes
  paramsList:
  - source_attribute: Transaction Reference Number
    target_attribute: orderIdentifiers.internalOrderIdCode
  - source_attribute: Transaction Reference Number
    target_attribute: reportDetails.transactionRefNo
  - source_attribute: __order_id__
    target_attribute: _order.id
    cast_to: string
  - source_attribute: Execution Order Type
    target_attribute: executionDetails.orderType
  - source_attribute: Trading Capacity
    target_attribute: executionDetails.tradingCapacity
  - source_attribute: Trading Capacity
    target_attribute: transactionDetails.tradingCapacity
  - source_attribute: Price - Type
    target_attribute: transactionDetails.priceNotation
  - source_attribute: Quantity Type
    target_attribute: transactionDetails.quantityNotation
  - source_attribute: Order Total Quantity
    target_attribute: transactionDetails.quantity
  - source_attribute: Order Total Quantity
    target_attribute: priceFormingData.initialQuantity
  - source_attribute: Order Remaining Quantity
    target_attribute: priceFormingData.remainingQuantity
  - source_attribute: Quantity
    target_attribute: priceFormingData.tradedQuantity
  - source_attribute: __price_currency__
    target_attribute: transactionDetails.settlementAmountCurrency
  - source_attribute: __price_currency__
    target_attribute: transactionDetails.priceCurrency
  - source_attribute: __price_currency__
    target_attribute: _order.transactionDetails.commissionAmountCurrency
  - source_attribute: Commissions
    target_attribute: _order.transactionDetails.commissionAmount
  - source_attribute: Exchange MIC Code
    target_attribute: _order.transactionDetails.ultimateVenue
  - source_attribute: Venue
    target_attribute: transactionDetails.venue
  - source_attribute: Additional Information
    target_attribute: executionDetails.outgoingOrderAddlInfo
    # fields for populating party identifiers
  - source_attribute: Execution Entity Identification Code
    target_attribute: __exec_entity_with_lei__
    prefix: "lei:"
  - source_attribute: Trader
    target_attribute: __trader_id__
    prefix: "id:"
  - source_attribute: Buyer Identification Code
    target_attribute: __buyer_id_code__
    prefix: "lei:"
  - source_attribute: Buyer Decision Maker Code
    target_attribute: __buyer_dm_id_code__
    prefix: "lei:"
  - source_attribute: Seller Identification Code
    target_attribute: __seller_id_code__
    prefix: "lei:"
  - source_attribute: Seller Decision Maker Code
    target_attribute: __seller_dm_id_code__
    prefix: "lei:"
  - source_attribute: Portfolio Manager
    target_attribute: __inv_dec_in_firm_id__
    prefix: "id:"
  - source_attribute: Counterparty
    target_attribute: __counterparty_id__
    prefix: "id:"
  upstreamTasks:
  - taskName: FilterRowsInSourceFile
    mapped: true
    key: result
  - taskName: ConvertMinorToMajor
    mapped: true
    key: currency_conversion
  - taskName: CastToInteger
    mapped: true
    key: map_attribute
- path: swarm_tasks.transform.map.map_value:MapValue
  name: MapValue
  paramsList:
    - source_attribute: Transaction Type
      target_attribute: __buysell__
      case_insensitive: true
      value_map:
        buy: BUYI
        buy to cover: BUYI
        buytocover: BUYI
        sell: SELL
        sellshort: SELL
        sell short: SELL
    - source_attribute: Commission Type
      target_attribute: _order.transactionDetails.commissionAmountType
      case_insensitive: true
      value_map:
        Commission (Basis Points): Basis points
        Commission (Cents Per Share): Amount per unit
    - source_attribute: Order Status
      target_attribute: orderState.executionDetails.orderStatus
      case_insensitive: true
      value_map:
        filled: FILL
        fill: FILL
        cancel: CAME
        cancelled: CAME
        canceled: CAME
        done for day: DNFD
        doneforday: DNFD
        expire: CAME
        expired: CAME
    - source_attribute: Order Time In Force
      target_attribute: __validity_period__
      case_insensitive: true
      value_map:
        day: DAVY
        gtc: GTCV
  upstreamTasks:
    - taskName: FilterRowsInSourceFile
      mapped: true
      key: result
- path: swarm_tasks.transform.map.map_static:MapStatic
  name: MapStatic
  paramsList:
  - target_attribute: _order.__meta_model__
    target_value: Order
  - target_attribute: orderState.__meta_model__
    target_value: OrderState
  - target_attribute: _order.executionDetails.orderStatus
    target_value: NEWO
  - target_attribute: __newo_in_file__
    target_value: false
  - target_attribute: orderState.sourceKey
    from_env_var: SWARM_FILE_URL
  - target_attribute: orderState.sourceIndex
    from_index: true
  - target_attribute: _order.sourceKey
    from_env_var: SWARM_FILE_URL
  - target_attribute: _order.sourceIndex
    from_index: true
  - target_attribute: dataSourceName
    target_value: Enfusion
  upstreamTasks:
  - taskName: FilterRowsInSourceFile
    mapped: true
    key: result
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: MapConditional
  paramsList:
    - target_attribute: transactionDetails.quantityCurrency
      cases:
        - query: "`Quantity Type` == 'MONE'"
          attribute: __price_currency__
        - query: "`Quantity Type` != 'MONE'"
          as_empty: true
    - target_attribute: __final_counterparty__
      cases:
        - query: "`__counterparty_id__`.notnull()"
          attribute: __counterparty_id__
        - query: "(`__buysell__`=='BUYI') & `__counterparty_id__`.isnull() & `__buyer_id_code__`.notnull()"
          attribute: __buyer_id_code__
        - query: "(`__buysell__`=='BUYI') & `__counterparty_id__`.isnull() & `__buyer_id_code__`.isnull() & `__buyer_dm_id_code__`.notnull()"
          attribute: __buyer_dm_id_code__
        - query: "(`__buysell__`=='SELL') & `__counterparty_id__`.isnull() & `__seller_id_code__`.notnull()"
          attribute: __seller_id_code__
        - query: "(`__buysell__`=='SELL') & `__counterparty_id__`.isnull() & `__seller_id_code__`.isnull() & `__seller_dm_id_code__`.notnull()"
          attribute: __seller_dm_id_code__
    # client_id has been created for the BUAM override, and is not used by the bundle.yaml anywhere else
    - target_attribute: __client_id__
      cases:
        - query: "__buysell__=='BUYI'"
          attribute: __buyer_id_code__
        - query: "__buysell__=='SELL'"
          attribute: __seller_id_code__
  upstreamTasks:
  - taskName: FilterRowsInSourceFile
    mapped: true
    key: result
  - taskName: ConvertMinorToMajor
    mapped: true
    key: currency_conversion
  - taskName: MapAttributes
    mapped: true
    key: map_attributes
  - taskName: MapValue
    mapped: true
    key: map_value
- path: swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers:GenericOrderPartyIdentifiers
  name: PartyIdentifiers
  params:
    target_attribute: marketIdentifiers.parties
    executing_entity_identifier: __exec_entity_with_lei__
    counterparty_identifier: __final_counterparty__
    client_identifier: __final_counterparty__
    buyer_identifier: __exec_entity_with_lei__
    seller_identifier: __final_counterparty__
    trader_identifier: __trader_id__
    execution_within_firm_identifier: __trader_id__
    investment_decision_within_firm_identifier: __inv_dec_in_firm_id__
    buy_sell_side_attribute: __buysell__
    use_buy_mask_for_buyer_seller: true
  upstreamTasks:
    - taskName: MapAttributes
      mapped: true
      key: result
    - taskName: MapValue
      mapped: true
      key: map_value
    - taskName: MapConditional
      mapped: true
      key: map_conditional
- path: swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers:InstrumentIdentifiers
  name: InstrumentIdentifiers
  params:
    currency_attribute: __price_currency__
    expiry_date_attribute: __expiry_date__
    isin_attribute: Instrument Identification Code
    notional_currency_2_attribute: Notional Currency 2
    option_strike_price_attribute: __option_strike_price__
    option_type_attribute: Option Type
    underlying_index_name_attribute: Underlying Index Name
    underlying_index_term_value_attribute: Term of Underlying Index - Value
    underlying_isin_attribute: Underlying Instrument Code
    underlying_symbol_attribute: Option Contract Bloomberg Root Code
    venue_attribute: Exchange MIC Code
  upstreamTasks:
    - taskName: FilterRowsInSourceFile
      mapped: true
      key: result
    - taskName: ConvertMinorToMajor
      mapped: true
      key: convert_currencies
    - taskName: ConvertDatetime
      mapped: true
      key: convert_date_time
- path: swarm_tasks.transform.steeleye.link.merge_market_identifiers:MergeMarketIdentifiers
  name: MergeMarketIdentifiers
  params:
    identifiers_path: marketIdentifiers
    instrument_path: marketIdentifiers.instrument
    parties_path: marketIdentifiers.parties
  upstreamTasks:
    - taskName: InstrumentIdentifiers
      mapped: true
      key: result
    - taskName: PartyIdentifiers
      mapped: true
      key: party_identifiers
# primary frame concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PrimaryFrameConcatenator
  params:
    orient: horizontal
  upstreamTasks:
  - taskName: ConvertDatetime
    mapped: true
    key: convert_date_time
  - taskName: ConvertMinorToMajor
    mapped: true
    key: convert_minor_major
  - taskName: MapAttributes
    mapped: true
    key: map_attribute
  - taskName: MapStatic
    mapped: true
    key: map_static
  - taskName: MapValue
    mapped: true
    key: map_value
  - taskName: MapConditional
    mapped: true
    key: map_conditional
  - taskName: PartyIdentifiers
    mapped: true
    key: party_identifiers
  - taskName: InstrumentIdentifiers
    mapped: true
    key: instrument_identifiers
  - taskName: MergeMarketIdentifiers
    mapped: true
    key: merge_market_ids
- path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
  name: LinkInstrument
  params:
    identifiers_path: marketIdentifiers.instrument
    currency_attribute: __price_currency__
    venue_attribute: _order.transactionDetails.ultimateVenue
  resources:
    ref_data_key: reference-data
    tenant_data_key: tenant-data
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    mapped: true
    key: result
- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkParties
  params:
    identifiers_path: marketIdentifiers.parties
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    mapped: true
    key: result
- path: swarm_tasks.transform.map.map_to_nested:MapToNested
  name: InstrumentOverridesStrikePrice
  params:
    source_attribute: __option_strike_price__
    nested_path: derivative.strikePrice
    target_attribute: instrumentDetails.instrument
  upstreamTasks:
    - taskName: LinkInstrument
      mapped: true
      key: link_instrument
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: result
- path: swarm_tasks.transform.map.map_to_nested:MapToNested
  name: InstrumentOverridesExpiryDate
  params:
    source_attribute: __expiry_date__
    nested_path: derivative.expiryDate
    target_attribute: instrumentDetails.instrument
  upstreamTasks:
    - taskName: InstrumentOverridesStrikePrice
      mapped: true
      key: override_strike_price
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: result
- path: swarm_tasks.transform.map.map_to_nested:MapToNested
  name: InstrumentOverridesPriceMultiplier
  params:
    source_attribute: Price Multiplier
    nested_path: derivative.priceMultiplier
    target_attribute: instrumentDetails.instrument
  upstreamTasks:
    - taskName: FilterRowsInSourceFile
      mapped: true
      key: result
    - taskName: InstrumentOverridesExpiryDate
      mapped: true
      key: override_expiry_date
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: AuxiliaryMapAttributes
  paramsList:
  - source_attribute: reportDetails.transactionRefNo
    target_attribute: orderState.orderIdentifiers.transactionRefNo
  - source_attribute: _order.id
    target_attribute: orderIdentifiers.orderIdCode
  - source_attribute: _order.id
    target_attribute: orderState.id
  - source_attribute: __buysell__
    target_attribute: transactionDetails.buySellIndicator
  - source_attribute: __buysell__
    target_attribute: _order.buySell
  - source_attribute: __buysell__
    target_attribute: orderState.buySell
  - source_attribute: __buysell__
    target_attribute: executionDetails.buySellIndicator
  - source_attribute: timestamps.tradingDateTime
    target_attribute: transactionDetails.tradingDateTime
  - source_attribute: timestamps.tradingDateTime
    target_attribute: _order.timestamps.orderStatusUpdated
  - source_attribute: timestamps.tradingDateTime
    target_attribute: orderState.timestamps.orderStatusUpdated
  - source_attribute: timestamps.orderReceived
    target_attribute: timestamps.orderSubmitted
  - source_attribute: __validity_period__
    target_attribute: executionDetails.validityPeriod
    cast_to: string.list
    list_delimiter: ;
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: result
- path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
  name: ParentId
  params:
    parent_model_attribute: _order.__meta_model__
    parent_attributes_prefix: _order.
    target_attribute: orderState.__meta_parent__
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    mapped: true
    key: result
  - taskName: AuxiliaryMapAttributes
    mapped: true
    key: auxiliary_map_attributes
   # Auxiliary frame conatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: AuxiliaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
    - marketIdentifiers.instrument
    - marketIdentifiers.parties
    - __price_currency__
    - __buysell__
    - __buyer_id_code__
    - __buyer_dm_id_code__
    - __client_id__
    - __counterparty_id__
    - __exec_entity_with_lei__
    - __inv_dec_in_firm_id__
    - __seller_id_code__
    - __seller_dm_id_code__
    - __trader_id__
    - __final_counterparty__
    - __expiry_date__
    - __option_strike_price__
    - __validity_period__
  upstreamTasks:
  - taskName: AuxiliaryMapAttributes
    mapped: true
    key: auxiliary_map_attributes
  - taskName: PrimaryFrameConcatenator
    mapped: true
    key: primary_frame_concatenator
  - taskName: ParentId
    mapped: true
    key: parent_id
  - taskName: LinkParties
    mapped: true
    key: link_parties
  - taskName: InstrumentOverridesPriceMultiplier
    mapped: true
    key: instrument_overrides_price_multiplier
# Filter only OrderRecords into a frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderRecords
  params:
    except_prefix: orderState.
    strip_prefix: true
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenator
    mapped: true
    key: result
# Filter only OrderStateRecords into a frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderStateRecords
  params:
    except_prefix: _order.
    strip_prefix: true
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenator
    mapped: true
    key: result
# Strip prefix `_order.` from the column names of OrderRecords frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrder
  params:
    action: strip
    prefix: _order.
  upstreamTasks:
  - taskName: OrderRecords
    mapped: true
    key: result
# Strip prefix `orderState.` from the column names of OrderState Records frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrderState
  params:
    action: strip
    prefix: orderState.
  upstreamTasks:
  - taskName: OrderStateRecords
    mapped: true
    key: result
# Vertically concatenate Order and OrderState Record frames created above.
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: VerticalConcatenator
  params:
    orient: vertical
    reset_index: true
    drop_index: true
  upstreamTasks:
  - taskName: StripPrefixOrder
    mapped: true
    key: order_records
  - taskName: StripPrefixOrderState
    mapped: true
    key: order_state_records
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: RemoveInvalidOrderStates
  params:
    query: "`executionDetails.orderStatus`.notnull()"
  upstreamTasks:
  - taskName: VerticalConcatenator
    mapped: true
    key: result
# Remove duplicate new orders, remove synthetic new orders already in Elastic
- path: swarm_tasks.order.generic.remove_duplicate_newo:RemoveDuplicateNEWO
  name: RemoveDupNEWO
  params:
    newo_in_file_col: __newo_in_file__
    drop_newo_in_file_col: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: RemoveInvalidOrderStates
    mapped: true
    key: result
# Best-execution tasks
- path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
  name: BestExecution
  resources:
    es_client_key: reference-data
  upstreamTasks:
  - taskName: RemoveDupNEWO
    mapped: true
    key: result
# Best-Ex results concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: BestExecutionConcatenator
  params:
    orient: horizontal
  upstreamTasks:
  - taskName: BestExecution
    mapped: true
    key: best_ex_result
  - taskName: RemoveDupNEWO
    mapped: true
    key: orders_and_orderstates_final
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: AssignMeta
  params:
    model_attribute: __meta_model__
    parent_attribute: __meta_parent__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: BestExecutionConcatenator
    mapped: true
    key: result
 # Post-meta concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PostMetaConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
      - __meta_parent__
  upstreamTasks:
  - taskName: BestExecutionConcatenator
    mapped: true
    key: result
  - taskName: AssignMeta
    mapped: true
    key: meta
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: PostMetaConcatenator
    mapped: true
    key: transform_result
  - taskName: FilterRowsInSourceFile
    mapped: true
    key: producer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsent
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: ElasticBulkTransformer
    mapped: true
    key: result
# Instrument Mapper
- path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
  name: InstrumentMapper
  upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: source_frame
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineCondition
  upstreamTasks:
  - taskName: PutIfAbsent
    mapped: true
    key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformer
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: PostMetaConcatenator
    mapped: true
    key: transform_result
  - taskName: FilterRowsInSourceFile
    mapped: true
    key: producer_result
  - taskName: PutIfAbsent
    mapped: true
    key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsent
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: QuarantinedElasticBulkTransformer
    mapped: true
    key: result
- path: swarm_tasks.control_flow.noop:Noop
  name: Noop