column,description,python type
Account,,string
ContraOrderID,,string
Description,,string
Destination,,string
Expiration,,string
LegAmount,,float
LegCount,,float
LegNumber,,string
LegPrice,,float
LegQuantity,,float
LegSide,,string
OrderID,,string
OrderQuantity,,float
OrderRefPrice,,string
OrderSide,,string
Ratio,,float
Route,,string
Strike,,float
Symbol,,string
Timestamp,,string
Type,,string
Username,,string