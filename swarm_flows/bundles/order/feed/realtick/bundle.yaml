# Bundle path -> order/feed/realtick
# Confluence page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/1629586004/Order+Realtick
# Note: The first client using this bundle is Monsas.
# If this bundle has to be used for a different client, the LEIs below should be passed as flow overrides.
# LEI for Monsas: 254900Q2VI6VYIGQM972 (https://search.gleif.org/#/record/254900Q2VI6VYIGQM972)
# LEI for Argon (counterparty): 213800AOJXFQAQM9Y991 (https://search.gleif.org/#/record/213800AOJXFQAQM9Y991)
id: order-feed-realtick
name: Order Feed Realtick
infra:
  - name: tenant-data
    type: ELASTICSEARCH
  - name: reference-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process
controlFlows:
  - name: S3OrLocalFile
    conditionTaskName: ParametersFlowController
    trueTaskName: S3DownloadFile
    falseTaskName: LocalFile
    merge: true
  - name: NeedsQuarantine
    conditionTaskName: QuarantineCondition
    trueTaskName: QuarantinedElasticBulkTransformer
    falseTaskName: Noop
    mapped: true
    merge: false
tasks:
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvFileSplitter
    params:
      chunksize: 25000
      skiprows: 1
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result
  - path: swarm_tasks.io.read.batch_producer:BatchProducer
    name: BatchProducer
    params:
      source_schema:
        "Bank": string
        "Broker": string
        "Commission": float
        "Contra": string
        "Currency": string
        "CUSIP": string
        "Customer": string
        "Deposit": string
        "Domain": string
        "Exchange": string
        "Expiration": string
        "Good Until": string
        "ISIN": string
        "Option Root": string
        "Order ID": string
        "Original Order ID": string
        "Original Order Volume": float
        "Price": float
        "Put / Call Indicator": string
        "Residual Volume": float
        "Sec Type": string
        "Settlement Date": string
        "Side": string
        "Stop Price": float
        "Strike Price": float
        "Symbol": string
        "Ticket Id": string
        "Trade Date": string
        "Trade Time": string
        "User": string
        "Volume": float
    upstreamTasks:
      - taskName: CsvFileSplitter
        mapped: true
        key: file_splitter_result
  # The file contains a trailer record containing the word 'Trailer' in the 1st column
  # (Original Order ID) and the number of rows in the second column (Order ID)
  - path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
    name: FilterRowsInSourceFile
    params:
      query: "`Original Order ID`.str.lower() != 'trailer'"
      skip_on_empty: true
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  # Primary Transformations
  - path: swarm_tasks.transform.datetime.convert_datetime:ConvertDatetime
    name: ConvertDatetime
    paramsList:
      - source_attribute: Settlement Date
        target_attribute: transactionDetails.settlementDate
        source_attribute_format: "%Y%m%d"
        convert_to: date
      - source_attribute: Expiration
        target_attribute: __expiry_date__
        source_attribute_format: "%Y%m%d"
        convert_to: date
      - source_attribute: Trade Date
        target_attribute: date
        source_attribute_format: "%Y%m%d"
        convert_to: date
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
  - path: swarm_tasks.transform.datetime.join_date_and_time:JoinDateAndTimeFormat
    name: JoinDateAndTimeFormat
    paramsList:
      - source_date_attribute: Trade Date
        source_time_attribute: Trade Time
        target_attribute: transactionDetails.tradingDateTime
        source_format: "%Y%m%d%H:%M:%S"
        target_format: "%Y-%m-%dT%H:%M:%SZ"
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_attribute:MapAttribute
    name: OutgoingOrderInfoFields
    paramsList:
    - source_attribute: Symbol
      target_attribute: __symbol_with_prefix__
      prefix: "Symbol: "
    - source_attribute: CUSIP
      target_attribute: __cusip_with_prefix__
      prefix: "CUSIP: "
    - source_attribute: Domain
      target_attribute: __domain_with_prefix__
      prefix: "Domain: "
    - source_attribute: Bank
      target_attribute: __bank_with_prefix__
      prefix: "Bank: "
    - source_attribute: Customer
      target_attribute: __customer_with_prefix__
      prefix: "Customer: "
    - source_attribute: Deposit
      target_attribute: __deposit_with_prefix__
      prefix: "Deposit: "
    - source_attribute: Broker
      target_attribute: __broker_with_prefix__
      prefix: "Broker: "
    - source_attribute: Contra
      target_attribute: __contra_with_prefix__
      prefix: "Contra: "
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
  - path: swarm_tasks.transform.concat.concat_attributes:ConcatAttributes
    name: ConcatAttributes
    paramsList:
      - source_attributes:
          - __symbol_with_prefix__
          - __cusip_with_prefix__
          - __domain_with_prefix__
          - __bank_with_prefix__
          - __customer_with_prefix__
          - __deposit_with_prefix__
          - __broker_with_prefix__
          - __contra_with_prefix__
        target_attribute: executionDetails.outgoingOrderAddlInfo
        delimiter: ", "
    upstreamTasks:
      - taskName: OutgoingOrderInfoFields
        mapped: true
        key: result
  - path: swarm_tasks.generic.currency.convert_minor_to_major:ConvertMinorToMajor
    name: ConvertMinorToMajor
    paramsList:
      # Currencies
      - source_ccy_attribute: Currency
        target_ccy_attribute: transactionDetails.priceCurrency
      - source_ccy_attribute: Currency
        target_ccy_attribute: transactionDetails.commissionAmountCurrency
      # Prices
      - source_price_attribute: Price
        source_ccy_attribute: Currency
        target_price_attribute: priceFormingData.price
        cast_to: abs
      - source_price_attribute: Strike Price
        source_ccy_attribute: Currency
        target_price_attribute: __option_strike_price__
        cast_to: abs
      - source_price_attribute: Stop Price
        source_ccy_attribute: Currency
        target_price_attribute: _order.executionDetails.stopPrice
        cast_to: abs
      - source_price_attribute: Commission
        source_ccy_attribute: Currency
        target_price_attribute: transactionDetails.commissionAmount
        cast_to: abs
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_static:MapStatic
    name: MapStatic
    paramsList:
      # Metadata Fields
      - target_attribute: sourceKey
        from_env_var: SWARM_FILE_URL
      - target_attribute: sourceIndex
        from_index: true
        cast_to_str: true
      - target_attribute: dataSourceName
        target_value: Ezesoft Realtick
      - target_attribute: _orderState.__meta_model__
        target_value: OrderState
      - target_attribute: _order.__meta_model__
        target_value: Order
      - target_attribute: _order.executionDetails.orderStatus
        target_value: NEWO
      - target_attribute: __newo_in_file__
        target_value: false
      # Transaction Details fields
      - target_attribute: transactionDetails.quantityNotation
        target_value: UNIT
      - target_attribute: transactionDetails.priceNotation
        target_value: MONE
      - target_attribute: transactionDetails.tradingCapacity
        target_value: AOTC
      # Execution Details fields
      - target_attribute: executionDetails.orderType
        target_value: Market
      - target_attribute: executionDetails.tradingCapacity
        target_value: AOTC
      # Price Forming Data fields
      - target_attribute: _order.priceFormingData.tradedQuantity
        target_value: 0.0
      # Fields related to Parties
      - target_attribute: __executing_entity__
        target_value: 254900Q2VI6VYIGQM972
      - target_attribute: __counterparty__
        target_value: 213800AOJXFQAQM9Y991
      # Fields related to Instruments
      - target_attribute: transactionDetails.venue
        target_value: XOFF
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_value:MapValue
    name: MapValue
    paramsList:
      - source_attribute: Side
        target_attribute: __buysell__
        case_insensitive: true
        value_map:
          b: BUYI
          bc: BUYI
          s: SELL
          ss: SELL
      # Set to pd.NA if Sec Type = 1 (equity).
      - source_attribute: Sec Type
        target_attribute: __asset_class__
        value_map: "{'2': 'option', '3': 'future'}"
      - source_attribute: Good Until
        target_attribute: __validity_period__
        case_insensitive: true
        value_map:
          day: DAVY
          dayplus: DAVY
          gtc: GTCV
          clo: GTDV
      - source_attribute: Exchange
        target_attribute: transactionDetails.ultimateVenue
        case_insensitive: true
        value_map:
          ads: XADS
          amm: XAMM
          ams: XAMS
          arc: ARCX
          ase: XASE
          ath: ASEX
          aus: XASX
          bae: CBOE
          bah: XBAH
          bas: XBAS
          bat: XBAT
          ber: XBER
          bom: XBOM
          bot: XBOT
          bov: XBOV
          brn: XBRN
          brt: XBRT
          bru: XBRU
          brv: XBRV
          bse: XBSE
          buc: XBUC
          bud: XBUD
          bul: XBUL
          cai: XCAI
          car: XCAR
          cas: XCAS
          cbo: CBOE
          cha: XCHA
          chi: XCHI
          cnx: XCNX
          col: XCOL
          cph: XCPH
          cse: XCSE
          cys: XCYS
          dbo: XDBO
          dfm: XDFM
          doh: XDOH
          dus: XDUS
          edg: XEDG
          eno: XENO
          eti: XETI
          etr: XETR
          eud: XEUD
          eun: XEUN
          eur: XEUR
          fra: XFRA
          fse: XFSE
          fuk: XFUK
          ham: XHAM
          han: XHAN
          hel: XHEL
          hks: XHKS
          hnx: XHNX
          hsx: XHSX
          icx: XICX
          iex: XIEX
          irs: XIRS
          ist: XIST
          jak: XJAK
          jas: XJAS
          jnb: XJNB
          kar: XKAR
          koc: XKOC
          kon: XKON
          kor: XKOR
          kos: XKOS
          kuw: XKUW
          ldn: XLDN
          lim: XLIM
          lis: XLIS
          lju: XLJU
          lse: XLSE
          lux: XLUX
          mau: XMAU
          mce: XMCE
          mcs: XMCS
          mcu: XMCU
          mcx: XMCX
          mex: XMEX
          mil: XMIL
          mun: XMUN
          mus: XMUS
          mys: XMYS
          nag: XNAG
          nam: XNAM
          nap: XNAP
          nas: XNAS
          nax: XNAX
          ndu: XNDU
          neo: XNEO
          nex: XNEX
          ngm: XNGM
          ngx: XNGX
          nin: XNIN
          nor: XNOR
          nxs: XNXS
          nys: XNYS
          nzs: XNZS
          obb: XOBB
          osl: XOSL
          par: XPAR
          phl: XPHL
          pnk: XPNK
          prg: XPRG
          rts: XRTS
          sap: XSAP
          sau: XSAU
          ses: XSES
          set: XSET
          shh: XSHH
          shn: XSHN
          shz: XSHZ
          sq1: XSQ1
          sri: XSRI
          sse: XSSE
          sto: XSTO
          stu: XSTU
          swx: XSWX
          szn: XSZN
          taa: XTAA
          tad: XTAD
          tae: XTAE
          tai: XTAI
          tao: XTAO
          tas: XTAS
          tav: XTAV
          tde: XTDE
          tor: XTOR
          tqe: XTQE
          trq: XTRQ
          trv: XTRV
          tun: XTUN
          tys: XTYS
          ukx: XUKX
          van: XVAN
          vie: XVIE
          vto: XVTO
          wnc: XWNC
          wse: XWSE
          xes: XXES
          xus: XXUS
          zag: XZAG
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_conditional:MapConditional
    name: MapConditional
    paramsList:
    - target_attribute: _orderState.executionDetails.orderStatus
      cases:
        - query: "`Volume`>0 & `Residual Volume`==0"
          value: "FILL"
        - query: "`Residual Volume`>0"
          value: "PARF"
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_attribute:MapAttribute
    name: MapAttributes
    paramsList:
    # Transaction Details
    - source_attribute: Ticket Id
      target_attribute: transactionDetails.trailId
    - source_attribute: Volume
      target_attribute: transactionDetails.quantity
    # Price Forming Data
    - source_attribute: Original Order Volume
      target_attribute: priceFormingData.initialQuantity
    - source_attribute: Residual Volume
      target_attribute: _order.priceFormingData.remainingQuantity
    - source_attribute: Volume
      target_attribute: _orderState.priceFormingData.tradedQuantity
    # Buy/Sell Indicator
    - source_attribute: __buysell__
      target_attribute: _orderState.buySell
    - source_attribute: __buysell__
      target_attribute: _order.buySell
    - source_attribute: __buysell__
      target_attribute: executionDetails.buySellIndicator
    - source_attribute: __buysell__
      target_attribute: transactionDetails.buySellIndicator
    # Order ID and Transaction Ref No. fields
    - source_attribute: Original Order ID
      target_attribute: orderIdentifiers.orderIdCode
    - source_attribute: Original Order ID
      target_attribute: orderIdentifiers.aggregatedOrderId
    - source_attribute: Original Order ID
      target_attribute: _order.id
    - source_attribute: Original Order ID
      target_attribute: _orderState.id
    - source_attribute: Order ID
      target_attribute: reportDetails.transactionRefNo
    - source_attribute: Order ID
      target_attribute: _orderState.orderIdentifiers.transactionRefNo
    # Dates and times
    - source_attribute: transactionDetails.tradingDateTime
      target_attribute: timestamps.tradingDateTime
    - source_attribute: transactionDetails.tradingDateTime
      target_attribute: timestamps.orderReceived
    - source_attribute: transactionDetails.tradingDateTime
      target_attribute: timestamps.orderSubmitted
    - source_attribute: transactionDetails.tradingDateTime
      target_attribute: _order.timestamps.orderStatusUpdated
    - source_attribute: transactionDetails.tradingDateTime
      target_attribute: _orderState.timestamps.orderStatusUpdated
    # Fields for populating Party Identifiers
    - source_attribute: __executing_entity__
      target_attribute: __executing_entity_with_lei__
      prefix: "lei:"
    - source_attribute: __counterparty__
      target_attribute: __counterparty_with_lei__
      prefix: "lei:"
    - source_attribute: Deposit
      target_attribute: __trader_with_id__
      prefix: "id:"
    - source_attribute: User
      target_attribute: __exec_within_firm_with_id__
      prefix: "id:"
    - source_attribute: Deposit
      target_attribute: __buyer_with_id__
      prefix: "id:"
    - source_attribute: __counterparty__
      target_attribute: __seller_with_lei__
      prefix: "lei:"
    # Fields for populating Instrument Identifiers
    - source_attribute: Put / Call Indicator
      target_attribute: __option_type__
      start_index: 0
      end_index: 1
    # Validity period
    - source_attribute: __validity_period__
      target_attribute: executionDetails.validityPeriod
      cast_to: string.list
      list_delimiter: ;
    upstreamTasks:
    - taskName: FilterRowsInSourceFile
      mapped: true
      key: result
    - taskName: JoinDateAndTimeFormat
      mapped: true
      key: join_datetime
    - taskName: MapStatic
      mapped: true
      key: map_static
    - taskName: MapValue
      mapped: true
      key: map_value
    - taskName: ConcatAttributes
      mapped: true
      key: concat_attributes
  - path: swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers:GenericOrderPartyIdentifiers
    name: PartyIdentifiers
    params:
      target_attribute: marketIdentifiers.parties
      executing_entity_identifier: __executing_entity_with_lei__
      counterparty_identifier: __counterparty_with_lei__
      buyer_identifier: __buyer_with_id__
      seller_identifier: __seller_with_lei__
      trader_identifier: __trader_with_id__
      execution_within_firm_identifier: __exec_within_firm_with_id__
      buy_sell_side_attribute: __buysell__
      use_buy_mask_for_buyer_seller: true
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
      - taskName: MapValue
        mapped: true
        key: map_value
      - taskName: MapAttributes
        mapped: true
        key: map_attributes
  - path: swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers:InstrumentIdentifiers
    name: InstrumentIdentifiers
    params:
      asset_class_attribute: __asset_class__
      currency_attribute: transactionDetails.priceCurrency
      isin_attribute: ISIN
      venue_attribute: transactionDetails.ultimateVenue
      expiry_date_attribute: __expiry_date__
      option_strike_price_attribute: __option_strike_price__
      option_type_attribute: __option_type__
      underlying_symbol_attribute: Option Root
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
      - taskName: ConvertMinorToMajor
        mapped: true
        key: convert_minor_to_major
      - taskName: ConvertDatetime
        mapped: true
        key: convert_datetime
      - taskName: MapStatic
        mapped: true
        key: map_static
      - taskName: MapValue
        mapped: true
        key: map_value
  - path: swarm_tasks.transform.steeleye.link.merge_market_identifiers:MergeMarketIdentifiers
    name: MergeMarketIdentifiers
    params:
      identifiers_path: marketIdentifiers
      instrument_path: marketIdentifiers.instrument
      parties_path: marketIdentifiers.parties
    upstreamTasks:
      - taskName: InstrumentIdentifiers
        mapped: true
        key: result
      - taskName: PartyIdentifiers
        mapped: true
        key: party_identifiers
  # Primary Frame Concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PrimaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __executing_entity__
        - __counterparty__
        - __executing_entity_with_lei__
        - __counterparty_with_lei__
        - __trader_with_id__
        - __exec_within_firm_with_id__
        - __buyer_with_id__
        - __seller_with_lei__
        - __buysell__
        - __option_type__
        - __validity_period__
    upstreamTasks:
      - taskName: ConvertMinorToMajor
        mapped: true
        key: convert_minor_to_major
      - taskName: ConvertDatetime
        mapped: true
        key: convert_datetime
      - taskName: JoinDateAndTimeFormat
        mapped: true
        key: join_datetime
      - taskName: MapAttributes
        mapped: true
        key: map_attributes
      - taskName: MapStatic
        mapped: true
        key: map_static
      - taskName: MapConditional
        mapped: true
        key: map_conditional
      - taskName: MapValue
        mapped: true
        key: map_value
      - taskName: ConcatAttributes
        mapped: true
        key: concat_attributes
      - taskName: PartyIdentifiers
        mapped: true
        key: party_identifiers
      - taskName: InstrumentIdentifiers
        mapped: true
        key: instrument_identifiers
      - taskName: MergeMarketIdentifiers
        mapped: true
        key: merge_market_identifiers
  - path: swarm_tasks.transform.steeleye.link.parties:LinkParties
    name: LinkParties
    params:
      identifiers_path: marketIdentifiers.parties
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
    name: LinkInstrument
    params:
      identifiers_path: marketIdentifiers.instrument
      asset_class_attribute: __asset_class__
      currency_attribute: transactionDetails.priceCurrency
      venue_attribute: transactionDetails.ultimateVenue
    resources:
      ref_data_key: reference-data
      tenant_data_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_to_nested:MapToNested
    name: InstrumentStrikePriceOverride
    params:
      source_attribute: __option_strike_price__
      nested_path: derivative.strikePrice
      target_attribute: instrumentDetails.instrument
      skip_target_nan: True
      skip_source_nan: True
    upstreamTasks:
      - taskName: LinkInstrument
        mapped: true
        key: result
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: primary_frame_concatenator
  - path: swarm_tasks.transform.map.map_to_nested:MapToNested
    name: InstrumentExpiryDateOverride
    params:
      source_attribute: __expiry_date__
      nested_path: derivative.expiryDate
      target_attribute: instrumentDetails.instrument
      skip_target_nan: True
      skip_source_nan: True
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
      - taskName: InstrumentStrikePriceOverride
        mapped: true
        key: strikeprice_override
  - path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
    name: ParentId
    params:
      parent_model_attribute: _order.__meta_model__
      parent_attributes_prefix: _order.
      target_attribute: _orderState.__meta_parent__
    upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: result
  # Auxiliary frame concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: AuxiliaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - marketIdentifiers.instrument
        - marketIdentifiers.parties
        - __expiry_date__
        - __option_strike_price__
        - __asset_class__
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: primary_frame_concatenator
      - taskName: LinkParties
        mapped: true
        key: link_parties
      - taskName: InstrumentExpiryDateOverride
        mapped: true
        key: link_instruments
      - taskName: ParentId
        mapped: true
        key: parent_id
  # Filter only OrderRecords into a frame
  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: OrderRecords
    params:
      except_prefix: _orderState.
      strip_prefix: true
    upstreamTasks:
    - taskName: AuxiliaryFrameConcatenator
      mapped: true
      key: result
  # Filter only OrderStateRecords into a frame
  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: OrderStateRecords
    params:
      except_prefix: _order.
      strip_prefix: true
    upstreamTasks:
    - taskName: AuxiliaryFrameConcatenator
      mapped: true
      key: result
  # Strip prefix `_order.` from the column names of OrderRecords frame.
  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixOrder
    params:
      action: strip
      prefix: _order.
    upstreamTasks:
    - taskName: OrderRecords
      mapped: true
      key: result
  # Strip prefix `_orderState.` from the column names of OrderState Records frame.
  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixOrderState
    params:
      action: strip
      prefix: _orderState.
    upstreamTasks:
    - taskName: OrderStateRecords
      mapped: true
      key: result
  # Vertically concatenate Order and OrderState Record frames created above.
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: VerticalConcatenator
    params:
      orient: vertical
      reset_index: true
      drop_index: true
    upstreamTasks:
    - taskName: StripPrefixOrder
      mapped: true
      key: order_records
    - taskName: StripPrefixOrderState
      mapped: true
      key: order_state_records
  - path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
    name: RemoveInvalidOrderStates
    params:
      query: "`executionDetails.orderStatus`.notnull()"
    upstreamTasks:
    - taskName: VerticalConcatenator
      mapped: true
      key: result
# Remove duplicate new orders, remove synthetic new orders already in Elastic
  - path: swarm_tasks.order.generic.remove_duplicate_newo:RemoveDuplicateNEWO
    name: RemoveDupNEWO
    params:
      newo_in_file_col: __newo_in_file__
      drop_newo_in_file_col: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: RemoveInvalidOrderStates
      mapped: true
      key: result
  # Best-execution tasks
  - path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
    name: BestExecution
    resources:
      es_client_key: reference-data
    upstreamTasks:
    - taskName: RemoveDupNEWO
      mapped: true
      key: result
  # Best-Ex results concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: BestExecutionConcatenator
    params:
      orient: horizontal
    upstreamTasks:
    - taskName: BestExecution
      mapped: true
      key: best_ex_result
    - taskName: RemoveDupNEWO
      mapped: true
      key: orders_and_orderstates_final
  # Assign Meta
  - path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
    name: AssignMeta
    params:
      model_attribute: __meta_model__
      parent_attribute: __meta_parent__
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result
   # Post-meta concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PostMetaConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __meta_model__
        - __meta_parent__
    upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result
    - taskName: AssignMeta
      mapped: true
      key: meta
  # Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformer
    params:
      action_type: create
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: FilterRowsInSourceFile
      mapped: true
      key: producer_result
  # Elastic Bulk Writer
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: PutIfAbsent
    params:
      payload_size: 10000000
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: ElasticBulkTransformer
      mapped: true
      key: result
  # Instrument Mapper
  - path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
    name: InstrumentMapper
    upstreamTasks:
      - taskName: PostMetaConcatenator
        mapped: true
        key: source_frame
      - taskName: PutIfAbsent
        mapped: true
        key: bulk_writer_result
  # Quarantine Condition
  - path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
    name: QuarantineCondition
    upstreamTasks:
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
  # Quarantined Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: QuarantinedElasticBulkTransformer
    params:
      action_type: create
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: FilterRowsInSourceFile
      mapped: true
      key: producer_result
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: QuarantinedPutIfAbsent
    params:
      payload_size: 10000000
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: QuarantinedElasticBulkTransformer
      mapped: true
      key: result
  - path: swarm_tasks.control_flow.noop:Noop
    name: Noop