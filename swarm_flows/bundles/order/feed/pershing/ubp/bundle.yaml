# Confluence page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2078736482/Order+Pershing+-UBP
#
id: order-feed-pershing-ubp
name: Order Pershing
infra:
  - name: tenant-data
    type: ELASTICSEARCH
  - name: reference-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process
controlFlows:
  - name: S3OrLocalFile
    conditionTaskName: ParametersFlowController
    trueTaskName: S3DownloadFile
    falseTaskName: LocalFile
    merge: true
  - name: NeedsQuarantine
    conditionTaskName: QuarantineCondition
    trueTaskName: QuarantinedElasticBulkTransformer
    falseTaskName: Noop
    mapped: true
    merge: false
tasks:
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  #source file has 1st column in format:ddmmyyyyHHMMClt/Mkt; renaming it using regex
  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvFileSplitter
    params:
      chunksize: 10000
      delimiter: '^'
      normalise_columns: true
      list_of_col_regex:
        - '^[0-9]{12}(Clt/Mkt)'
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result

  # Batch Producer
  - path: swarm_tasks.io.read.batch_producer:BatchProducer
    name: BatchProducer
    params:
      strip_whitespace: true
      source_schema:
        "AGT/PPL": string
        "BGN_REF": string
        "BGT/SLD": string
        "CLT/MKT": string
        "CLT_CONS": float
        "CLT_REF": string
        "CLT_STL_CCY": string
        "CMN_REF": string
        "COMPANY": string
        "DATE": string
        "EXCH_CCY1": string
        "EXCH_CCY2": string
        "EXCH_RATE2": float
        "MKT_CONS": float
        "PRICE": float
        "SETTLE": string
        "STK_QTY": float
        "STL_EXCH": string
        "STOCK": string
        "TIME": string
        "TRADE": string
    upstreamTasks:
      - taskName: CsvFileSplitter
        mapped: true
        key: file_splitter_result

  # Filter out invalid rows
  - path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
    name: FilterRowsInSourceFile
    params:
      query: "`DEL`!='Y'"
      skip_on_empty: true
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result

  # Primary transformations
  - path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
    name: PrimaryTransformations
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result

  # Link Parties
  - path: swarm_tasks.transform.steeleye.link.parties:LinkParties
    name: LinkParties
    params:
      identifiers_path: marketIdentifiers.parties
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryTransformations
        mapped: true
        key: result

  # Link Instrument
  - path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
    name: LinkInstrument
    params:
      identifiers_path: marketIdentifiers.instrument
      currency_attribute: transactionDetails.priceCurrency
      asset_class_attribute: ASSET_CLASS
      venue_attribute: transactionDetails.ultimateVenue
    resources:
      ref_data_key: reference-data
      tenant_data_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryTransformations
        mapped: true
        key: result

  # Instrument Fallback
  - path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
    name: InstrumentFallback
    params:
      instrument_fields_map:
        - source_field: __fallback_id_code__
          target_field: ext.instrumentIdCodeType
        - source_field: __fallback_price_notation__
          target_field: ext.priceNotation
        - source_field: __fallback_quantity_notation__
          target_field: ext.quantityNotation
        - source_field: __fallback_is_created_through_fb__
          target_field: isCreatedThroughFallback
        - source_field: __fallback_instrument_fullname__
          target_field: instrumentFullName
        - source_field: __fallback_instrument_national_currency__
          target_field: notionalCurrency1
      str_to_bool_dict:
        "true": True
        "false": False
    upstreamTasks:
      - taskName: PrimaryTransformations
        mapped: true
        key: result
      - taskName: LinkInstrument
        mapped: true
        key: link_instrument

  # Link each OrderState record to the parent Order record
  - path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
    name: AssignMetaParent
    params:
      parent_model_attribute: _order.__meta_model__
      parent_attributes_prefix: _order.
      target_attribute: _orderState.__meta_parent__
    upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: result

  # Auxiliary frame concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: AuxiliaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - marketIdentifiers.instrument
        - marketIdentifiers.parties
        - __fallback_id_code__
        - __fallback_price_notation__
        - __fallback_quantity_notation__
        - __fallback_is_created_through_fb__
        - __fallback_instrument_fullname__
        - __fallback_instrument_national_currency__
        - __isin__
        - ASSET_CLASS
        - asset_class_attribute # Instrument Identifiers columns
        - bbg_figi_id_attribute
        - currency_attribute
        - eurex_id_attribute
        - exchange_symbol_attribute
        - expiry_date_attribute
        - interest_rate_start_date_attribute
        - isin_attribute
        - notional_currency_1_attribute
        - notional_currency_2_attribute
        - option_strike_price_attribute
        - option_type_attribute
        - swap_near_leg_date_attribute
        - underlying_index_name_attribute
        - underlying_index_name_leg_2_attribute
        - underlying_index_series_attribute
        - underlying_index_term_attribute
        - underlying_index_term_value_attribute
        - underlying_index_version_attribute
        - underlying_isin_attribute
        - underlying_symbol_attribute
        - underlying_symbol_expiry_code_attribute
        - underlying_index_term_leg_2_attribute
        - underlying_index_term_value_leg_2_attribute
        - venue_attribute
        - venue_financial_instrument_short_name_attribute
        - instrument_classification_attribute
    upstreamTasks:
      - taskName: PrimaryTransformations
        mapped: true
        key: primary_transformations
      - taskName: LinkParties
        mapped: true
        key: link_parties
      - taskName: AssignMetaParent
        mapped: true
        key: parent_id
      - taskName: InstrumentFallback
        mapped: true
        key: instruments

  # Filter only Order records into a data frame
  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: OrderRecords
    params:
      except_prefix: _orderState.
      strip_prefix: true
    upstreamTasks:
    - taskName: AuxiliaryFrameConcatenator
      mapped: true
      key: result

  # Filter only OrderState records into a data frame
  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: OrderStateRecords
    params:
      except_prefix: _order.
      strip_prefix: true
    upstreamTasks:
    - taskName: AuxiliaryFrameConcatenator
      mapped: true
      key: result

  # Strip prefix `_order.` from the column names of OrderRecords frame.
  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixOrder
    params:
      action: strip
      prefix: _order.
    upstreamTasks:
    - taskName: OrderRecords
      mapped: true
      key: result

  # Strip prefix `_orderState.` from the column names of OrderState Records frame.
  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixOrderState
    params:
      action: strip
      prefix: _orderState.
    upstreamTasks:
    - taskName: OrderStateRecords
      mapped: true
      key: result

  # Vertically concatenate Order and OrderState Record frames created above.
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: VerticalConcatenator
    params:
      orient: vertical
      reset_index: true
      drop_index: true
    upstreamTasks:
    - taskName: StripPrefixOrder
      mapped: true
      key: order_records
    - taskName: StripPrefixOrderState
      mapped: true
      key: order_state_records

  - path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
    name: RemoveInvalidOrderStates
    params:
      query: "`executionDetails.orderStatus`.notnull()"
    upstreamTasks:
      - taskName: VerticalConcatenator
        mapped: true
        key: result

  - path: swarm_tasks.order.generic.remove_duplicate_newo:RemoveDuplicateNEWO
    name: RemoveDupNEWO
    params:
      newo_in_file_col: __newo_in_file_col__
      drop_newo_in_file_col: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: RemoveInvalidOrderStates
        mapped: true
        key: result

  # Best-execution tasks
  - path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
    name: BestExecution
    resources:
      es_client_key: reference-data
    upstreamTasks:
    - taskName: RemoveDupNEWO
      mapped: true
      key: result

  # Best-Ex results concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: BestExecutionConcatenator
    params:
      orient: horizontal
    upstreamTasks:
    - taskName: BestExecution
      mapped: true
      key: best_ex_result
    - taskName: RemoveDupNEWO
      mapped: true
      key: remove_dup_newo

  # Assign Meta
  - path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
    name: AssignMeta
    params:
      model_attribute: __meta_model__
      parent_attribute: __meta_parent__
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result

   # Post-meta concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PostMetaConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __meta_model__
        - __meta_parent__
    upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result
    - taskName: AssignMeta
      mapped: true
      key: meta

  # Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformer
    params:
      action_type: create
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: FilterRowsInSourceFile
      mapped: true
      key: producer_result

  # Elastic Bulk Writer
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: ElasticBulkWriter
    params:
      payload_size: 10000000
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: ElasticBulkTransformer
      mapped: true
      key: result

  # Instrument Mapper
  - path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
    name: InstrumentMapper
    upstreamTasks:
      - taskName: PostMetaConcatenator
        mapped: true
        key: source_frame
      - taskName: ElasticBulkWriter
        mapped: true
        key: bulk_writer_result

  # Quarantine Condition
  - path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
    name: QuarantineCondition
    upstreamTasks:
    - taskName: ElasticBulkWriter
      mapped: true
      key: bulk_writer_result

  # Quarantined Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: QuarantinedElasticBulkTransformer
    params:
      action_type: create
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: FilterRowsInSourceFile
      mapped: true
      key: producer_result
    - taskName: ElasticBulkWriter
      mapped: true
      key: bulk_writer_result

  # Elastic Bulk Writer for Quarantined Records
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: QuarantinedElasticBulkWriter
    params:
      payload_size: 10000000
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: QuarantinedElasticBulkTransformer
      mapped: true
      key: result

  - path: swarm_tasks.control_flow.noop:Noop
    name: Noop