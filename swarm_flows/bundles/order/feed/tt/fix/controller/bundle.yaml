# Input: FlowArgs to define where to download FIX files from in S3, and for what time window if any

# "delta_in_days" -> int; if 0 -> download FIX files from day of Flow execution (T);
#  if 1 -> download FIX files from day before Flow Execution (T-1), etc...

# "prefix" -> str; path where to look for FIX files in the tenant's S3 bucket

# Then it groups the fix files by `OrderID` within the same batch.

# At the end uploads the batches to s3, ready to be handled by `order-feed-tt-fix` feed.

id: order-feed-tt-fix-controller
name: Order Feed TT FIX Controller

infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH

audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data

parameters:
- name: flow_args
  envVar: SWARM_FLOW_ARGS
  description: "flow arguments"

tasks:

# For a given directory in S3, i.e. ingress/raw/order-feed-tt-fix/20220701,
# Download the existing FIX files and create csv files
# This csv files groups fix files for the same `OrderID` in the same batch
- path: swarm_tasks.order.feed.tt.fix.s3_fix_files_batcher:S3FixFilesBatcher
  name: S3FixFilesBatcher
  params:
    feed: 'order-feed-tt-fix'
    chunk_size: 25000
  upstreamTasks:
    - taskName: flow_args
      key: flow_args

- path: swarm_tasks.io.write.aws.s3_upload_file:S3UploadFile
  name: S3UploadFile
  upstreamTasks:
  - taskName: S3FixFilesBatcher
    mapped: false
    key: upload_target