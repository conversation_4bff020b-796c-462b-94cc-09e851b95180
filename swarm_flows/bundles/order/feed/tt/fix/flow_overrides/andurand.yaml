taskOverrides:
  all_environments:
    - name: AuxiliaryFinalMapConditional
      params:
        target_attribute: transactionDetails.tradingCapacity
        cases:
          - query: "index == index"
            value: AOTC
    - name: LinkInstrument
      params:
        identifiers_path: marketIdentifiers.instrument
        instrument_model_preference_rank:
          - FcaFirdsInstrument
          - VenueDirectInstrument
          - VenueInstrument
          - FirdsInstrument
          - AnnaDsbInstrument
          - SedolInstrument
          - CfdInstrument
          - SteelEyeInstrument
          - Instrument
        asset_class_attribute: __asset_class__
        currency_attribute: transactionDetails.priceCurrency
        venue_attribute: transactionDetails.ultimateVenue
    - name: DefaultExecutionWithinFirm
      params:
        target_attribute: EXECUTION_WITHIN_FIRM
        cases:
          - query: "`__trader__`.notnull()"
            attribute: __trader__
          - query: "`__trader__`.isnull()"
            attribute: __trader_37__