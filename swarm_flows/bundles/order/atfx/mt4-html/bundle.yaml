id: order-atfx-mt4-html
name: Order ATFX MT4 HTML

infra:
  - name: tenant-data
    type: ELASTICSEARCH
  - name: reference-data
    type: ELASTICSEARCH


audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data


parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process


controlFlows:
  - name: S3OrLocalFile
    conditionTaskName: ParametersFlowController
    trueTaskName: S3DownloadFile
    falseTaskName: LocalFile
    merge: true
  - name: NeedsQuarantine
    conditionTaskName: QuarantineCondition
    trueTaskName: QuarantinedElasticBulkTransformer
    falseTaskName: Noop
    mapped: true
    merge: false


tasks:
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.unzip:Unzip
    name: Unzip
    params:
      file_match_pattern: "**/*.htm"
      multi_file: true
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extract_result
  - path: swarm_tasks.io.read.html_file_splitter:HtmlFileSplitter
    name: HtmlFileSplitter
    params:
      chunksize: 25000
      skip_rows: 0
      drop_tail: 1
      header: 1
      drop_na: Deal
      encoding: latin-1
      column_to_string:
        - Deal
        - Login
    upstreamTasks:
      - taskName: Unzip
        mapped: true
        key: extractor_result
  - path: swarm_tasks.io.read.batch_producer:BatchProducer
    name: BatchProducer
    upstreamTasks:
      - taskName: HtmlFileSplitter
        mapped: true
        flatten: true
        key: file_splitter_result
  - path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
    name: OpenTrades
    params:
      query: "`Close Time`.isnull()"
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
    name: CloseTrades
    params:
      query: "`Close Time`.notnull()"
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_static:MapStatic
    name: MapStaticInitial
    paramsList:
      - target_attribute: __duplicated_row_flag__
        target_value: true
    upstreamTasks:
      - taskName: CloseTrades
        mapped: true
        key: result
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: CloseTradesWithFlag
    params:
      orient: horizontal
    upstreamTasks:
      - taskName: CloseTrades
        mapped: true
        key: result
      - taskName: MapStaticInitial
        mapped: true
        key: map_static_initial
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: GenerateAdditionalRowsFinal
    params:
      orient: vertical
      reset_index: true
      drop_index: true
    upstreamTasks:
      - taskName: OpenTrades
        mapped: true
        key: open_trades
      - taskName: CloseTrades
        mapped: true
        key: close_trades
      - taskName: CloseTradesWithFlag
        mapped: true
        key: close_trades_with_flag
  # ON-2233 ignore all rows where `Type` is other than Buy/Sell
  - path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
    name: FilterRows
    params:
      query: "`Type`.str.match('^buy$|^sell$',case=False)"
    upstreamTasks:
      - taskName: GenerateAdditionalRowsFinal
        mapped: true
        key: result
  - path: swarm_tasks.transform.datetime.convert_datetime:ConvertDatetime
    name: ConvertDatetime
    paramsList:
      - source_attribute: Open Time
        target_attribute: __open_time__
        convert_to: datetime
      - source_attribute: Close Time
        target_attribute: __close_time__
        convert_to: datetime
      - source_attribute: Open Time
        target_attribute: date
        convert_to: date
    upstreamTasks:
      - taskName: FilterRows
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_attribute:MapAttribute
    name: MapAttribute
    paramsList:
      - source_attribute: Close Price
        target_attribute: temp.price
      - source_attribute: Deal
        target_attribute: __order_id_with_suffix__
        suffix: _2
    upstreamTasks:
      - taskName: FilterRows
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_static:MapStatic
    name: MapStatic
    paramsList:
      - target_attribute: _order.__meta_model__
        target_value: Order
      - target_attribute: __NEWO_IN_FILE__
        target_value: false
      - target_attribute: orderState.__meta_model__
        target_value: OrderState
      - target_attribute: executionDetails.orderType
        target_value: Market
      - target_attribute: _order.executionDetails.orderStatus
        target_value: NEWO
      - target_attribute: temp.comment
        target_value: FILL
      - target_attribute: orderState.sourceKey
        from_env_var: SWARM_FILE_URL
      - target_attribute: orderState.sourceIndex
        from_index: true
      - target_attribute: executionDetails.tradingCapacity
        target_value: AOTC
      - target_attribute: transactionDetails.tradingCapacity
        target_value: AOTC
      - target_attribute: _order.sourceKey
        from_env_var: SWARM_FILE_URL
      - target_attribute: _order.sourceIndex
        from_index: true
      - target_value: XOFF
        target_attribute: transactionDetails.venue
      - target_value: MONE
        target_attribute: transactionDetails.quantityNotation
      - target_value: MONE
        target_attribute: transactionDetails.priceNotation
      - target_attribute: dataSourceName
        target_value: MT4-HTML
      - target_attribute: __decrement_one_hour__
        target_value: 3600
    upstreamTasks:
      - taskName: FilterRows
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_conditional:MapConditional
    name: MapConditional
    paramsList:
      - target_attribute: temp.orderState
        cases:
          - query: "`Comment`.isnull()"
            attribute: temp.comment
          - query: "`Comment`.notnull()"
            attribute: Comment
      - target_attribute: temp.initialQuantity
        cases:
          - query: "`Symbol`.str.contains(r'(?i)(AFN|ALL|DZD|USD|EUR|AOA|XCD|XCD|ARS|AMD|AWG|AUD|EUR|AZN|BSD|BHD|BDT|BBD|BYN|EUR|BZD|XOF|BMD|BTN|INR|BOB|BOV|USD|BAM|BWP|NOK|BRL|USD|BND|BGN|XOF|BIF|CVE|KHR|XAF|CAD|KYD|XAF|XAF|CLF|CLP|CNY|AUD|AUD|COP|COU|KMF|CDF|XAF|NZD|CRC|HRK|CUC|CUP|ANG|EUR|CZK|XOF|DKK|DJF|XCD|DOP|USD|EGP|SVC|USD|XAF|ERN|EUR|ETB|EUR|FKP|DKK|FJD|EUR|EUR|EUR|XPF|EUR|XAF|GMD|GEL|EUR|GHS|GIP|EUR|DKK|XCD|EUR|USD|GTQ|GBP|GNF|XOF|GYD|HTG|USD|AUD|EUR|HNL|HKD|HUF|ISK|INR|IDR|XDR|IRR|IQD|EUR|GBP|ILS|EUR|JMD|JPY|GBP|JOD|KZT|KES|AUD|KPW|KRW|KWD|KGS|LAK|EUR|LBP|LSL|ZAR|LRD|LYD|CHF|EUR|EUR|MOP|MGA|MWK|MYR|MVR|XOF|EUR|USD|EUR|MRU|MUR|EUR|XUA|MXN|MXV|USD|MDL|EUR|MNT|EUR|XCD|MAD|MZN|MMK|NAD|ZAR|AUD|NPR|EUR|XPF|NZD|NIO|XOF|NGN|NZD|AUD|USD|NOK|OMR|PKR|USD|PAB|USD|PGK|PYG|PEN|PHP|NZD|PLN|EUR|USD|QAR|MKD|RON|RUB|RWF|EUR|EUR|SHP|XCD|XCD|EUR|EUR|XCD|WST|EUR|STN|SAR|XOF|RSD|SCR|SLL|SGD|ANG|XSU|EUR|EUR|SBD|SOS|ZAR|SSP|EUR|LKR|SDG|SRD|NOK|SZL|SEK|CHE|CHF|CHW|SYP|TWD|TJS|TZS|THB|USD|XOF|NZD|TOP|TTD|TND|TRY|TMT|USD|AUD|UGX|UAH|AED|GBP|USD|USD|USN|UYI|UYU|UZS|VUV|VEF|VND|USD|USD|XPF|MAD|YER|ZMW|ZWL|EUR)')"
            value: 100000
          - query: "~`Symbol`.str.contains(r'(?i)(AFN|ALL|DZD|USD|EUR|AOA|XCD|XCD|ARS|AMD|AWG|AUD|EUR|AZN|BSD|BHD|BDT|BBD|BYN|EUR|BZD|XOF|BMD|BTN|INR|BOB|BOV|USD|BAM|BWP|NOK|BRL|USD|BND|BGN|XOF|BIF|CVE|KHR|XAF|CAD|KYD|XAF|XAF|CLF|CLP|CNY|AUD|AUD|COP|COU|KMF|CDF|XAF|NZD|CRC|HRK|CUC|CUP|ANG|EUR|CZK|XOF|DKK|DJF|XCD|DOP|USD|EGP|SVC|USD|XAF|ERN|EUR|ETB|EUR|FKP|DKK|FJD|EUR|EUR|EUR|XPF|EUR|XAF|GMD|GEL|EUR|GHS|GIP|EUR|DKK|XCD|EUR|USD|GTQ|GBP|GNF|XOF|GYD|HTG|USD|AUD|EUR|HNL|HKD|HUF|ISK|INR|IDR|XDR|IRR|IQD|EUR|GBP|ILS|EUR|JMD|JPY|GBP|JOD|KZT|KES|AUD|KPW|KRW|KWD|KGS|LAK|EUR|LBP|LSL|ZAR|LRD|LYD|CHF|EUR|EUR|MOP|MGA|MWK|MYR|MVR|XOF|EUR|USD|EUR|MRU|MUR|EUR|XUA|MXN|MXV|USD|MDL|EUR|MNT|EUR|XCD|MAD|MZN|MMK|NAD|ZAR|AUD|NPR|EUR|XPF|NZD|NIO|XOF|NGN|NZD|AUD|USD|NOK|OMR|PKR|USD|PAB|USD|PGK|PYG|PEN|PHP|NZD|PLN|EUR|USD|QAR|MKD|RON|RUB|RWF|EUR|EUR|SHP|XCD|XCD|EUR|EUR|XCD|WST|EUR|STN|SAR|XOF|RSD|SCR|SLL|SGD|ANG|XSU|EUR|EUR|SBD|SOS|ZAR|SSP|EUR|LKR|SDG|SRD|NOK|SZL|SEK|CHE|CHF|CHW|SYP|TWD|TJS|TZS|THB|USD|XOF|NZD|TOP|TTD|TND|TRY|TMT|USD|AUD|UGX|UAH|AED|GBP|USD|USD|USN|UYI|UYU|UZS|VUV|VEF|VND|USD|USD|XPF|MAD|YER|ZMW|ZWL|EUR)')"
            value: 0.0
      - target_attribute: _order.id
        cases:
          - query: "`__duplicated_row_flag__`.isnull()"
            attribute: Deal
          - query: "`__duplicated_row_flag__`.notnull()"
            attribute: __order_id_with_suffix__
      - target_attribute: transactionDetails.buySellIndicator
        cases:
          - query: "`__duplicated_row_flag__`.isnull() & `Type`.str.upper() == 'BUY'"
            value: BUYI
          - query: "`__duplicated_row_flag__`.isnull() & `Type`.str.upper() == 'SELL'"
            value: SELL
          - query: "`__duplicated_row_flag__`.notnull() & `Type`.str.upper() == 'BUY'"
            value: SELL
          - query: "`__duplicated_row_flag__`.notnull() & `Type`.str.upper() == 'SELL'"
            value: BUYI
      - target_attribute: _order.buySell
        cases:
          - query: "`__duplicated_row_flag__`.isnull() & `Type`.str.upper() == 'BUY'"
            value: "1"
          - query: "`__duplicated_row_flag__`.isnull() & `Type`.str.upper() == 'SELL'"
            value: "2"
          - query: "`__duplicated_row_flag__`.notnull() & `Type`.str.upper() == 'BUY'"
            value: "2"
          - query: "`__duplicated_row_flag__`.notnull() & `Type`.str.upper() == 'SELL'"
            value: "1"
    upstreamTasks:
      - taskName: FilterRows
        mapped: true
        key: result
      - taskName: MapStatic
        mapped: true
        key: map_static
      - taskName: MapAttribute
        mapped: true
        key: map_attribute
  - path: swarm_tasks.transform.map.map_attribute:MapAttribute
    name: AuxiliaryMapAttribute
    paramsList:
      - source_attribute: transactionDetails.buySellIndicator
        target_attribute: executionDetails.buySellIndicator
      - source_attribute: _order.buySell
        target_attribute: orderState.buySell
      - source_attribute: _order.id
        target_attribute: orderIdentifiers.orderIdCode
      - source_attribute: _order.id
        target_attribute: orderState.orderIdentifiers.transactionRefNo
      - source_attribute: _order.id
        target_attribute: orderState.id
    upstreamTasks:
      - taskName: FilterRows
        mapped: true
        key: result
      - taskName: MapConditional
        mapped: true
        key: buy_sell
  - path: swarm_tasks.steeleye.generic.get_tenant_lei:GetTenantLEI
    name: GetTenantLEI
    params:
      target_lei_column: __lei__
      target_column_prefix: "lei:"
    upstreamTasks:
      - taskName: FilterRows
        mapped: true
        key: result
  - path: swarm_tasks.transform.steeleye.orders.data_source.atfx.mt4.html.identifiers.party_identifiers:PartyIdentifiers
    name: PartyIdentifiers
    params:
      lei_col: __lei__
    upstreamTasks:
      - taskName: FilterRows
        mapped: true
        key: result
      - taskName: MapConditional
        mapped: true
        key: map_conditional
      - taskName: GetTenantLEI
        mapped: true
        key: tenant_lei
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3TenantCache
    params:
      s3_key: feeds/router_ignore/resources/cache/client-static-instruments.json
  - path: swarm_tasks.transform.steeleye.orders.data_source.atfx.mt4.html.identifiers.instrument_identifiers:InstrumentIdentifiers
    name: InstrumentIdentifiers
    params:
      asset_class_identifier: FXSPOTCFD
      venue_identifier: XXXX
    upstreamTasks:
      - taskName: FilterRows
        mapped: true
        key: result
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PrimaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - temp.orderState
        - temp.comment
        - temp.currency_1
        - temp.currency_2
    upstreamTasks:
      - taskName: ConvertDatetime
        mapped: true
        key: convert_date_time
      - taskName: MapAttribute
        mapped: true
        key: map_attribute
      - taskName: MapStatic
        mapped: true
        key: map_static
      - taskName: MapConditional
        mapped: true
        key: buy_sell
      - taskName: PartyIdentifiers
        mapped: true
        key: party_identifiers
      - taskName: InstrumentIdentifiers
        mapped: true
        key: instrument_identifier
      - taskName: ResolveTempConditions
        mapped: true
        key: resolve_temp_condition
      - taskName: AuxiliaryMapAttribute
        mapped: true
        key: auxiliary_map_attribute
  - path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
    name: ParentId
    params:
      parent_model_attribute: _order.__meta_model__
      parent_attributes_prefix: _order.
      target_attribute: orderState.__meta_parent__
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
    name: LinkInstrument
    params:
      identifiers_path: marketIdentifiers.instrument
    resources:
      ref_data_key: reference-data
      tenant_data_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.steeleye.link.parties:LinkParties
    name: LinkParties
    params:
      identifiers_path: marketIdentifiers.parties
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.steeleye.orders.data_source.atfx.mt4.html.identifiers.instrument_fallback:InstrumentFallBack
    name: InstrumentFallback
    upstreamTasks:
      - taskName: S3TenantCache
        mapped: false
        key: extractor_result
      - taskName: FilterRows
        mapped: true
        key: result
      - taskName: LinkInstrument
        mapped: true
        key: link_instrument
  - path: swarm_tasks.transform.map.map_from_nested:MapFromNested
    name: InstrumentCurrency
    paramsList:
      - source_attribute: instrumentDetails.instrument
        nested_path: notionalCurrency1
        target_attribute: transactionDetails.priceCurrency
      - source_attribute: instrumentDetails.instrument
        nested_path: notionalCurrency1
        target_attribute: transactionDetails.quantityCurrency
    upstreamTasks:
      - taskName: InstrumentFallback
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_conditional:MapConditional
    name: ResolveTempConditions
    paramsList:
      - target_attribute: __trading_datetime__
        cases:
          - query: "`Close Time`.isnull()"
            attribute: __open_time__
          - query: "`Close Time`.notnull() & `__duplicated_row_flag__`.notnull()"
            attribute: __close_time__
          - query: "`Close Time`.notnull() & `__duplicated_row_flag__`.isnull()"
            attribute: __open_time__
      - target_attribute: priceFormingData.price
        cases:
          - query: "`Close Time`.isnull()"
            attribute: Open Price
          - query: "`Close Time`.notnull() & `__duplicated_row_flag__`.notnull()"
            attribute: Close Price
          - query: "`Close Time`.notnull() & `__duplicated_row_flag__`.isnull()"
            attribute: Open Price
      - target_attribute: orderState.executionDetails.orderStatus
        cases:
          - query: "`temp.orderState`.str.contains(r'(?i)(cancelled.*)|(deleted.*)')"
            value: CAME
          - query: "~`temp.orderState`.str.contains(r'(?i)(cancelled.*)|(deleted.*)')"
            value: FILL
    upstreamTasks:
      - taskName: FilterRows
        mapped: true
        key: result
      - taskName: MapAttribute
        mapped: true
        key: map_attribute
      - taskName: MapConditional
        mapped: true
        key: map_conditional
      - taskName: ConvertDatetime
        mapped: true
        key: convert_datetime
  - path: swarm_tasks.transform.datetime.arrow_datetime_arithmetic:ArrowDatetimeArithmetic
    name: SubtractOneHourFromTradingTime
    params:
      source_attribute: __trading_datetime__
      source_attribute_format: 'YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]'
      source_manipulator_attribute: __decrement_one_hour__
      source_manipulator_unit: 'seconds'
      target_attribute: timestamps.tradingDateTime
      target_attribute_format: 'YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]'
      decrement: true
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_attribute:MapAttribute
    name: MapTimestamps
    paramsList:
      - source_attribute: timestamps.tradingDateTime
        target_attribute: transactionDetails.tradingDateTime
      - source_attribute: timestamps.tradingDateTime
        target_attribute: timestamps.orderSubmitted
      - source_attribute: timestamps.tradingDateTime
        target_attribute: timestamps.orderReceived
    upstreamTasks:
      - taskName: SubtractOneHourFromTradingTime
        mapped: true
        key: result
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: LotSizeCache
    params:
      s3_key: feeds/router_ignore/resources/cache/Lot Sizes.xlsx
  - path: swarm_tasks.transform.steeleye.orders.data_source.atfx.mt4.html.identifiers.lot_size:LotSize
    name: LotSize
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
      - taskName: FilterRows
        mapped: true
        key: result_2
      - taskName: LotSizeCache
        mapped: false
        key: extractor_result
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: AuxiliaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - marketIdentifiers.instrument
        - marketIdentifiers.parties
        - temp.initialQuantity
        - __trading_datetime__
        - __open_time__
        - __close_time__
        - temp.price
        - __order_id_with_suffix__
        - __decrement_one_hour__
    upstreamTasks:
      - taskName: InstrumentCurrency
        mapped: true
        key: instrument_currency
      - taskName: InstrumentFallback
        mapped: true
        key: instrument_fallback
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: primary_frame_concatenator
      - taskName: ParentId
        mapped: true
        key: parent_id
      - taskName: MapTimestamps
        mapped: true
        key: map_timestamps
      - taskName: LinkParties
        mapped: true
        key: parties_result
      - taskName: LotSize
        mapped: true
        key: lot_size
      - taskName: SubtractOneHourFromTradingTime
        mapped: true
        key: subtract_1_hour_from_trading_time
  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: OrderRecords
    params:
      except_prefix: orderState.
      strip_prefix: true
    upstreamTasks:
      - taskName: AuxiliaryFrameConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: OrderStateRecords
    params:
      except_prefix: _order.
      strip_prefix: true
    upstreamTasks:
      - taskName: AuxiliaryFrameConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixOrder
    params:
      action: strip
      prefix: _order.
    upstreamTasks:
      - taskName: OrderRecords
        mapped: true
        key: result
  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixOrderState
    params:
      action: strip
      prefix: orderState.
    upstreamTasks:
      - taskName: OrderStateRecords
        mapped: true
        key: result
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: VerticalConcatenator
    params:
      orient: vertical
      reset_index: true
      drop_index: true
    upstreamTasks:
      - taskName: StripPrefixOrder
        mapped: true
        key: order_records
      - taskName: StripPrefixOrderState
        mapped: true
        key: order_state_records
  - path: swarm_tasks.order.generic.remove_duplicate_newo:RemoveDuplicateNEWO
    name: RemoveDupNEWO
    params:
      newo_in_file_col: __NEWO_IN_FILE__
      drop_newo_in_file_col: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: VerticalConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
    name: Meta
    params:
      model_attribute: __meta_model__
      parent_attribute: __meta_parent__
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: RemoveDupNEWO
        mapped: true
        key: result
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: OrdersAndOrderStatesFinalConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __meta_model__
        - __meta_parent__
    upstreamTasks:
      - taskName: Meta
        mapped: true
        key: result
      - taskName: RemoveDupNEWO
        mapped: true
        key: remove_duplicate_newo
  # Compute Best-Execution
  - path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
    name: BestExecution
    resources:
      es_client_key: reference-data
    upstreamTasks:
      - taskName: OrdersAndOrderStatesFinalConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: BestExecutionConcatenator
    params:
      orient: horizontal
    upstreamTasks:
      - taskName: BestExecution
        mapped: true
        key: best_ex_result
      - taskName: OrdersAndOrderStatesFinalConcatenator
        mapped: true
        key: orders_and_orderstates_final
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformer
    params:
      action_type: create
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: BestExecutionConcatenator
        mapped: true
        key: transform_result
      - taskName: FilterRows
        mapped: true
        key: producer_result
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: PutIfAbsent
    params:
      payload_size: 10000000
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: ElasticBulkTransformer
        mapped: true
        key: result
  # Instrument Mapper
  - path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
    name: InstrumentMapper
    upstreamTasks:
      - taskName: BestExecutionConcatenator
        mapped: true
        key: source_frame
      - taskName: PutIfAbsent
        mapped: true
        key: bulk_writer_result
  - path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
    name: QuarantineCondition
    upstreamTasks:
      - taskName: PutIfAbsent
        mapped: true
        key: bulk_writer_result
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: QuarantinedElasticBulkTransformer
    params:
      action_type: create
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: BestExecutionConcatenator
        mapped: true
        key: transform_result
      - taskName: FilterRows
        mapped: true
        key: producer_result
      - taskName: PutIfAbsent
        mapped: true
        key: bulk_writer_result
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: QuarantinedPutIfAbsent
    params:
      payload_size: 10000000
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: QuarantinedElasticBulkTransformer
        mapped: true
        key: result
  - path: swarm_tasks.control_flow.noop:Noop
    name: Noop
