id: order-thornbridge-luna-third-financial
name: Order Thornbridge Luna Third Financial
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  mapped: true
  merge: false
tasks:
  # ParametersFlowController
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
  # Process file in S3
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
  # Or process local file
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
  # Read XLS file and convert to CSV
- path: swarm_tasks.io.read.xls_to_csv_converter:XlsToCsvConverter
  name: XlsToCsvConverter
  upstreamTasks:
    - taskName: S3OrLocalFile
      mapped: false
      key: extractor_result
  # Read CSV file in chunks
- path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
  name: CsvFileSplitter
  params:
    chunksize: 25000
  upstreamTasks:
    - taskName: XlsToCsvConverter
      mapped: false
      key: extractor_result
  # Produce chunk batches for downstream tasks
- path: swarm_tasks.io.read.batch_producer:BatchProducer
  name: BatchProducer
  params:
    source_schema:
      Order Reference: string
      Execution Date: string
      Status: string
      ISIN: string
      Dealer: string
      Limit Price: float
      Counterparty: string
      Security Class: string
      CCY: string
      Buy/Sell: string
      Trade Value: float
      Price: float
      Quantity: float
  upstreamTasks:
  - taskName: CsvFileSplitter
    mapped: true
    flatten: true
    key: file_splitter_result
  # Filter rows that do not meet requirements
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: GetRowsByCondition
  params:
    query: ~(Status == "Cancelled")
  upstreamTasks:
  - taskName: BatchProducer
    mapped: true
    key: result
  # Date and Datetime transformations (timestamps.orderReceived is mapped to other columns in MapAttributes)
- path: swarm_tasks.transform.datetime.convert_datetime:ConvertDatetime
  name: ConvertDatetime
  paramsList:
    - source_attribute: Instructed On
      target_attribute: date
      convert_to: date
    - source_attribute: Instructed On
      target_attribute: timestamps.orderReceived
      convert_to: datetime
    - source_attribute: Execution Date
      target_attribute: __execution_date__
      convert_to: datetime
  upstreamTasks:
    - taskName: GetRowsByCondition
      mapped: true
      key: result
  # Currency transformation
- path: swarm_tasks.generic.currency.convert_minor_to_major:ConvertMinorToMajor
  name: ConvertMinorToMajor
  paramsList:
    - source_price_attribute: Trade Value
      source_ccy_attribute: CCY
      target_price_attribute: executionDetails.settlementAmount
      cast_to: abs
    - source_price_attribute: Price
      source_ccy_attribute: CCY
      target_price_attribute: transactionDetails.priceAverage
      cast_to: abs
    - source_price_attribute: Price
      source_ccy_attribute: CCY
      target_price_attribute: priceFormingData.price
      cast_to: abs
    - source_price_attribute: Limit Price
      source_ccy_attribute: CCY
      target_price_attribute: executionDetails.limitPrice
      cast_to: abs
    - source_ccy_attribute: CCY
      target_ccy_attribute: transactionDetails.priceCurrency
  upstreamTasks:
    - taskName: GetRowsByCondition
      mapped: true
      key: result
# Map static values
- path: swarm_tasks.transform.map.map_static:MapStatic
  name: MapStatic
  paramsList:
    - target_attribute: _order.__meta_model__
      target_value: Order
    - target_attribute: orderState.__meta_model__
      target_value: OrderState
    - target_attribute: _order.executionDetails.orderStatus
      target_value: NEWO
    - target_attribute: orderState.executionDetails.orderStatus
      target_value: FILL
    - target_attribute: _order.sourceKey
      from_env_var: SWARM_FILE_URL
    - target_attribute: orderState.sourceKey
      from_env_var: SWARM_FILE_URL
    - target_attribute: _order.sourceIndex
      from_index: true
      cast_to_str: true
    - target_attribute: orderState.sourceIndex
      from_index: true
      cast_to_str: true
    - target_attribute: executionDetails.tradingCapacity
      target_value: AOTC
    - target_attribute: transactionDetails.tradingCapacity
      target_value: AOTC
    - target_attribute: transactionDetails.priceNotation
      target_value: MONE
    - target_attribute: priceFormingData.remainingQuantity
      target_value: 0
    - target_attribute: transactionDetails.quantityNotation
      target_value: "UNIT"
    - target_attribute: transactionDetails.venue
      target_value: "XOFF"
    - target_attribute: "&traitFqn"
      target_value: "mifid2/order"
    - target_attribute: tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator
      target_value: false
    - target_attribute: dataSourceName
      target_value: Luna Third Financial
  upstreamTasks:
  - taskName: GetRowsByCondition
    mapped: true
    key: result
# Map values by condition
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: MapConditional
  paramsList:
    - target_attribute: executionDetails.orderType
      cases:
        - query: "~`Limit Price`.notnull()"
          value: Market
        - query: "`Limit Price`.notnull()"
          value: Limit
  upstreamTasks:
  - taskName: GetRowsByCondition
    mapped: true
    key: result
# Map values
- path: swarm_tasks.transform.map.map_value:MapValue
  name: MapValue
  paramsList:
    - source_attribute: Buy/Sell
      target_attribute: transactionDetails.buySellIndicator
      case_insensitive: true
      value_map:
        buy: BUYI
        sell: SELL
    - source_attribute: Buy/Sell
      target_attribute: executionDetails.buySellIndicator
      case_insensitive: true
      value_map:
        buy: BUYI
        sell: SELL
    - source_attribute: Order Reference
      target_attribute: __transaction_ref_no__
      regex_replace_map:
        - regex: \/
          drop: true
      regex_replace_only: true
    - source_attribute: Buy/Sell
      target_attribute: _order.buySell
      case_insensitive: true
      value_map:
        buy: "1"
        sell: "2"
    - source_attribute: Buy/Sell
      target_attribute: orderState.buySell
      case_insensitive: true
      value_map:
        buy: "1"
        sell: "2"
  upstreamTasks:
    - taskName: GetRowsByCondition
      mapped: true
      key: result
# Concat Order Reference and ISIN to create order id
- path: swarm_tasks.transform.concat.concat_attributes:ConcatAttributes
  name: ConcatAttributes
  params:
    source_attributes:
      - __transaction_ref_no__
      - ISIN
    target_attribute: _order.id
    delimiter: ;
  upstreamTasks:
    - taskName: GetRowsByCondition
      mapped: true
      key: result
    - taskName: MapValue
      mapped: true
      key: map_value_result
#  Primary frame concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PrimaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
      - Status
      - Buy/Sell
      - Security Class
  upstreamTasks:
  - taskName: ConvertDatetime
    mapped: true
    key: convert_date_time
  - taskName: ConvertMinorToMajor
    mapped: true
    key: minor_to_major_result
  - taskName: MapConditional
    mapped: true
    key: map_conditional_result
  - taskName: MapStatic
    mapped: true
    key: map_static_result
  - taskName: MapValue
    mapped: true
    key: map_value_result
  - taskName: ConcatAttributes
    mapped: true
    key: concat_attributes_result
  - taskName: GetRowsByCondition
    mapped: true
    key: result
# Map attributes
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: MapAttribute
  paramsList:
    - source_attribute: timestamps.orderReceived
      target_attribute: timestamps.orderSubmitted
    - source_attribute: __execution_date__
      target_attribute: transactionDetails.tradingDateTime
    - source_attribute: __execution_date__
      target_attribute: timestamps.tradingDateTime
    - source_attribute: __execution_date__
      target_attribute: timestamps.orderStatusUpdated
    - source_attribute: transactionDetails.priceCurrency
      target_attribute: transactionDetails.settlementAmountCurrency
    - source_attribute: __transaction_ref_no__
      target_attribute: reportDetails.transactionRefNo
    - source_attribute: Quantity
      target_attribute: priceFormingData.initialQuantity
    - source_attribute: Quantity
      target_attribute: priceFormingData.tradedQuantity
    - source_attribute: Quantity
      target_attribute: transactionDetails.quantity
    - source_attribute: CCY
      target_attribute: transactionDetails.quantityCurrency
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: result
# Create Party identifiers
- path: swarm_tasks.transform.steeleye.orders.data_source.thornbridge.luna-third-financial.identifiers.party_identifiers:PartyIdentifiers
  name: PartyIdentifiers
  params:
    executing_entity_id: 2138008NEORY2DQV4R24
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: result
# Create Instrument identifiers
- path: swarm_tasks.transform.steeleye.orders.data_source.thornbridge.luna-third-financial.identifiers.instrument_identifiers:InstrumentIdentifiers
  name: InstrumentIdentifiers
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: result
# Merge Party and Instrument identifiers
- path: swarm_tasks.transform.steeleye.link.merge_market_identifiers:MergeMarketIdentifiers
  name: MergeMarketIdentifiers
  params:
    identifiers_path: marketIdentifiers
    instrument_path: marketIdentifiers.instrument
    parties_path: marketIdentifiers.parties
  upstreamTasks:
    - taskName: InstrumentIdentifiers
      mapped: true
      key: result
    - taskName: PartyIdentifiers
      mapped: true
      key: party_identifiers_result
#  Auxiliary frame concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: AuxiliaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
      - Order Reference
      - Instructed On
      - Execution Date
      - Trade Value
      - Price
      - Quantity
      - Limit Price
      - CCY
      - Counterparty
      - Dealer
      - ISIN
      - __transaction_ref_no__
      - __execution_date__
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: result
    - taskName: MapAttribute
      mapped: true
      key: map_attribute
    - taskName: PartyIdentifiers
      mapped: true
      key: party_identifiers
    - taskName: InstrumentIdentifiers
      mapped: true
      key: instrument_identifiers
    - taskName: MergeMarketIdentifiers
      mapped: true
      key: merged_market_identifiers
# Auxiliary map attributes
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: AuxiliaryMapAttributes
  paramsList:
  - source_attribute: reportDetails.transactionRefNo
    target_attribute: orderState.orderIdentifiers.transactionRefNo
  - source_attribute: _order.id
    target_attribute: orderState.id
  - source_attribute: _order.id
    target_attribute: orderIdentifiers.orderIdCode

  upstreamTasks:
    - taskName: AuxiliaryFrameConcatenator
      mapped: true
      key: result
# Assign Meta Parent (OrderState is a child model of Order)
- path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
  name: ParentId
  params:
    parent_model_attribute: _order.__meta_model__
    parent_attributes_prefix: _order.
    target_attribute: orderState.__meta_parent__
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenator
    mapped: true
    key: result
# Link Instruments created in InstrumentIdentifiers with the ones in SRP
- path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
  name: LinkInstrument
  params:
    identifiers_path: marketIdentifiers.instrument
    venue_attribute: transactionDetails.venue
    currency_attribute: transactionDetails.priceCurrency
  resources:
    ref_data_key: reference-data
    tenant_data_key: tenant-data
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenator
    mapped: true
    key: result
# Link Parties created in PartyIdentifiers with the ones in SRP
- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkParties
  params:
    identifiers_path: marketIdentifiers.parties
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenator
    mapped: true
    key: result
# Market data concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: MarketFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
    - marketIdentifiers.instrument
    - marketIdentifiers.parties
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenator
    mapped: true
    key: auxiliary_frame_concatenator
  - taskName: ParentId
    mapped: true
    key: parent_id
  - taskName: LinkParties
    mapped: true
    key: link_parties
  - taskName: LinkInstrument
    mapped: true
    key: link_instrument
  - taskName: AuxiliaryMapAttributes
    mapped: true
    key: aux_map_attribute
# Split Order dataframe
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderRecords
  params:
    except_prefix: orderState.
    strip_prefix: true
  upstreamTasks:
  - taskName: MarketFrameConcatenator
    mapped: true
    key: result
# Split OrderState dataframe
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderStateRecords
  params:
    except_prefix: _order.
    strip_prefix: true
  upstreamTasks:
  - taskName: MarketFrameConcatenator
    mapped: true
    key: result
# Strip _order. columns
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrder
  params:
    action: strip
    prefix: _order.
  upstreamTasks:
  - taskName: OrderRecords
    mapped: true
    key: result
# Strip orderState columns
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrderState
  params:
    action: strip
    prefix: orderState.
  upstreamTasks:
  - taskName: OrderStateRecords
    mapped: true
    key: result
# Vertical concatenator of order and orderState data
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: VerticalConcatenator
  params:
    orient: vertical
    reset_index: true
    drop_index: true
  upstreamTasks:
  - taskName: StripPrefixOrder
    mapped: true
    key: order_records
  - taskName: StripPrefixOrderState
    mapped: true
    key: order_state_records
# Compute Best-Execution
- path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
  name: BestExecution
  resources:
    es_client_key: reference-data
  upstreamTasks:
    - taskName: VerticalConcatenator
      mapped: true
      key: result
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: BestExecutionConcatenator
  params:
    orient: horizontal
  upstreamTasks:
    - taskName: BestExecution
      mapped: true
      key: best_ex_result
    - taskName: VerticalConcatenator
      mapped: true
      key: orders_and_orderstates_final
# Assign meta fields
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: Meta
  params:
    model_attribute: __meta_model__
    parent_attribute: __meta_parent__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: BestExecutionConcatenator
    mapped: true
    key: result
 # Post meta concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PostMetaConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
      - __meta_parent__
  upstreamTasks:
  - taskName: BestExecutionConcatenator
    mapped: true
    key: BestExecutionConcatenator
  - taskName: Meta
    mapped: true
    key: result
# Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: PostMetaConcatenator
    mapped: true
    key: transform_result
  - taskName: GetRowsByCondition
    mapped: true
    key: producer_result
# Elastic Bulk Writer
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsent
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: ElasticBulkTransformer
    mapped: true
    key: result
# Instrument Mapper
- path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
  name: InstrumentMapper
  upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: source_frame
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
# Quarantine Condition
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineCondition
  upstreamTasks:
  - taskName: PutIfAbsent
    mapped: true
    key: bulk_writer_result
# Quarantined Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformer
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: PostMetaConcatenator
    mapped: true
    key: transform_result
  - taskName: GetRowsByCondition
    mapped: true
    key: producer_result
  - taskName: PutIfAbsent
    mapped: true
    key: bulk_writer_result
# Quarantined Elastic Bulk Writer
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsent
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: QuarantinedElasticBulkTransformer
    mapped: true
    key: result
# Noop
- path: swarm_tasks.control_flow.noop:Noop
  name: Noop
