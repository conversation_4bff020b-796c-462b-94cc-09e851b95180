# Confluence Page:
# Bundle path -> order/bbg/audt
id: order-bbg-emsi-processor
name: Order Feed BBG EMSI Processor
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  mapped: true
  merge: false
tasks:
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
  name: CsvFileSplitter
  params:
    chunksize: 40000
    detect_encoding: true
    audit_input_rows: true
  upstreamTasks:
    - taskName: S3OrLocalFile
      mapped: false
      key: extractor_result
# Normalize input data
- path: swarm_tasks.io.read.batch_producer:BatchProducer
  name: BatchProducer
  params:
    source_schema:
      "Account (Tag 1)": string
      "Activity": string
      "As Of Date Time (Tag 60)": string
      "Average Price (Tag 6)": float
      "Broker": string
      "Buyside LEI": string
      "Contract Expiration": string
      "Execution Price (Tag 31)": float
      "FIGI": string
      "Fill ID": string
      "Fill Quantity (Tag 32)": float
      "Full Exch Symbol": string
      "GTD Date (Tag 126)": string
      "Instruction (Tag 58)": string
      "ISIN": string
      "Last Capacity (Tag 29)": string
      "Last Market": string
      "Last Market (Tag 30)": string
      "Limit Price (Tag 44)": string
      "Liquidity (Tag 851)": string
      "Local Exch Symbol": string
      "OCC_Symbol": string
      "Order #": string
      "Order ID": string
      "Order Type (Tag 40)": string
      "Originator LEI": string
      "Parsekey": string
      "PM Name": string
      "Price Currency": string
      "Product": string
      "Quantity (Tag 53)": float
      "Receive Date Time": string
      "Route ID": string
      "Security Name": string
      "Side (Tag 54)": string
      "Status (Tag 39)": string
      "Stop Price (Tag 99)": float
      "Strategy Name": string
      "Ticker (Tag 55)": string
      "TIF (Tag59)": string
      "Trader Name": string
      "Trader UUID": string
      "Tran Account": string
      "Transaction Reporting MIC": string
      "Type": string
      "__last_fill_filled_quantity__": float
      "__last_fill_fill_id__": string
      "__last_fill_limit_price__": string
      "__last_fill_price__": float
      "__last_fill_quantity__": float
      "__last_fill_receive_date_time__": string
      "__quantity_newo__": string
      "__order_status__": string
      "__external_order_received__": string
      "__internal_order_submitted__": string
      "__order_received__": string
      "__order_submitted__": string
      "__order_status_updated__": string
      "__trading_date_time__": string
    remove_unknown_columns: true
  upstreamTasks:
  - taskName: CsvFileSplitter
    mapped: true
    flatten: true
    key: file_splitter_result

# Filter out unnecessary records. The bundle doesn't do any filtering, but there
# are flow overrides which do filter records
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: PrimarySkipLogic
  params:
    query: "index==index"
    skip_on_empty: true
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
# Primary Transformations
- path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
  name: PrimaryTransformation
  upstreamTasks:
    - taskName: PrimarySkipLogic
      mapped: true
      key: result
# Link Parties
- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkParties
  params:
    identifiers_path: marketIdentifiers.parties
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryTransformation
      mapped: true
      key: result
# Link Instrument
- path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
  name: LinkInstrument
  params:
    identifiers_path: marketIdentifiers.instrument
    venue_attribute: __link_instrument_venue_attribute__
    currency_attribute: transactionDetails.priceCurrency
    instrument_id_fields:
      - "ext.exchangeSymbol"
      - "venue.financialInstrumentShortName"
      - "ext.additionalIdentifiers.compositeFigi"
      - "ext.additionalIdentifiers.figi"
      - "ext.aii.daily"
      - "ext.alternativeInstrumentIdentifier"
      - "ext.instrumentUniqueIdentifier"
      - "instrumentIdCode"
  resources:
    ref_data_key: reference-data
    tenant_data_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryTransformation
      mapped: true
      key: result
# Instrument Fallback
- path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
  name: InstrumentFallback
  params:
    instrument_fields_map:
      # Ext
      - source_field: "__asset_class__"
        target_field: ext.bestExAssetClassMain
      - source_field: __instr_unique_identifier__
        target_field: ext.instrumentUniqueIdentifier
      # Top-level fields
      - source_field: __instrument_full_name__
        target_field: instrumentFullName
      - source_field: __is_created_through_fallback__
        target_field: isCreatedThroughFallback
      # Venue
      - source_field: __instrument_short_name__
        target_field: venue.financialInstrumentShortName
    str_to_bool_dict:
      "on": True
      "off": False
  upstreamTasks:
    - taskName: PrimaryTransformation
      mapped: true
      key: result
    - taskName: LinkInstrument
      mapped: true
      key: link_instrument
# Overwrite the instrument full name and short name in the instrument
- path: swarm_tasks.transform.map.map_to_nested:MapToNested
  name: InstrumentOverwriteInstrumentFullName
  params:
    source_attribute: __instrument_full_name__
    nested_path: instrumentFullName
    target_attribute: instrumentDetails.instrument
  upstreamTasks:
    - taskName: PrimaryTransformation
      mapped: true
      key: result
    - taskName: InstrumentFallback
      mapped: true
      key: instrument
- path: swarm_tasks.transform.map.map_to_nested:MapToNested
  name: InstrumentOverwriteInstrumentShortName
  params:
    source_attribute: __instrument_short_name__
    nested_path: venue.financialInstrumentShortName
    target_attribute: instrumentDetails.instrument
  upstreamTasks:
    - taskName: PrimaryTransformation
      mapped: true
      key: result
    - taskName: InstrumentOverwriteInstrumentFullName
      mapped: true
      key: instrument
# Party Fallback with LEI lookup
- path: swarm_tasks.order.generic.party_fallback_with_lei_lookup:PartyFallbackWithLeiLookup
  name: PartyFallbackWithLeiLookup
  upstreamTasks:
    - taskName: PrimaryTransformation
      mapped: true
      key: result
    - taskName: LinkParties
      mapped: true
      key: link_parties_result
# Link each OrderState record to the parent Order record
- path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
  name: AssignMetaParent
  params:
      parent_model_attribute: _order.__meta_model__
      parent_attributes_prefix: _order.
      target_attribute: _orderState.__meta_parent__
  upstreamTasks:
    - taskName: PrimaryTransformation
      mapped: true
      key: result
# Auxiliary frame concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: AuxiliaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
      - marketIdentifiers.instrument
      - marketIdentifiers.parties
      - __is_created_through_fallback__
      - __option_strike_price__
      - __from_currency_code__
      - __option_type_mapped__
      - __instr_quantity_notation__
      - __instr_unique_identifier__
      - __notional_currency_2__
      - __currency_code__
      - __expiry_date__
      - __delivery_type__
      - __bond_maturity_date__
      - "Ticker (Tag 55)"
      - "__asset_class__"
      - "FIGI"
      - "ISIN"
      - "Last Market"
      - __instrument_full_name__
      - __instrument_short_name__
      - __fallback_buyer__
      - __fallback_seller__
      - __fallback_counterparty__
      - __fallback_client__
      - __fallback_buyer_dec_maker__
      - __fallback_seller_dec_maker__
      - __fallback_inv_dec_in_firm__
      - __fallback_trader__
      - __fallback_exec_within_firm__
      - __fallback_executing_entity__
      - __link_instrument_venue_attribute__
      - asset_class_attribute # Instrument Identifiers columns
      - bbg_figi_id_attribute
      - currency_attribute
      - eurex_id_attribute
      - exchange_symbol_attribute
      - expiry_date_attribute
      - interest_rate_start_date_attribute
      - isin_attribute
      - notional_currency_1_attribute
      - notional_currency_2_attribute
      - option_strike_price_attribute
      - option_type_attribute
      - swap_near_leg_date_attribute
      - underlying_index_name_attribute
      - underlying_index_name_leg_2_attribute
      - underlying_index_series_attribute
      - underlying_index_term_attribute
      - underlying_index_term_value_attribute
      - underlying_index_version_attribute
      - underlying_isin_attribute
      - underlying_symbol_attribute
      - underlying_symbol_expiry_code_attribute
      - underlying_index_term_leg_2_attribute
      - underlying_index_term_value_leg_2_attribute
      - venue_attribute
      - venue_financial_instrument_short_name_attribute
      - instrument_classification_attribute
  upstreamTasks:
    - taskName: PrimaryTransformation
      mapped: true
      key: primary_transformations
    - taskName: PartyFallbackWithLeiLookup
      mapped: true
      key: parties
    - taskName: InstrumentOverwriteInstrumentShortName
      mapped: true
      key: instrument
    - taskName: AssignMetaParent
      mapped: true
      key: parent_id
# Filter only Order records into a data frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderRecords
  params:
    except_prefix: _orderState.
    strip_prefix: true
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenator
    mapped: true
    key: result
# Filter only OrderState records into a data frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderStateRecords
  params:
    except_prefix: _order.
    strip_prefix: true
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenator
    mapped: true
    key: result
# Strip prefix `_order.` from the column names of OrderRecords frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrder
  params:
    action: strip
    prefix: _order.
  upstreamTasks:
  - taskName: OrderRecords
    mapped: true
    key: result
# Strip prefix `_orderState.` from the column names of OrderState Records frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrderState
  params:
    action: strip
    prefix: _orderState.
  upstreamTasks:
  - taskName: OrderStateRecords
    mapped: true
    key: result
# Vertically concatenate Order and OrderState Record frames created above.
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: VerticalConcatenator
  params:
    orient: vertical
    reset_index: true
    drop_index: true
  upstreamTasks:
  - taskName: StripPrefixOrder
    mapped: true
    key: order_records
  - taskName: StripPrefixOrderState
    mapped: true
    key: order_state_records
# Remove InvalidOrderStates
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: RemoveInvalidOrderStates
  params:
    query: "(`executionDetails.orderStatus`.notnull())"
  upstreamTasks:
  - taskName: VerticalConcatenator
    mapped: true
    key: result
# Remove duplicate new orders, remove synthetic new orders already in Elastic
- path: swarm_tasks.order.generic.remove_duplicate_newo:RemoveDuplicateNEWO
  name: RemoveDupNEWO
  params:
    newo_in_file_col: __newo_in_file__
    drop_newo_in_file_col: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: RemoveInvalidOrderStates
    mapped: true
    key: result
#Best Execution
- path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
  name: BestExecution
  resources:
    es_client_key: reference-data
  upstreamTasks:
    - taskName: RemoveDupNEWO
      mapped: true
      key: result
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: BestExecutionConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __newo_in_file__
  upstreamTasks:
    - taskName: RemoveDupNEWO
      mapped: true
      key: result
    - taskName: BestExecution
      mapped: true
      key: best_ex_result
# Assign Meta
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: AssignMeta
  params:
    model_attribute: __meta_model__
    parent_attribute: __meta_parent__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result
# Post-meta concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PostMetaConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
      - __meta_parent__
  upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: result
    - taskName: AssignMeta
      mapped: true
      key: meta
- path: swarm_tasks.generic.extract_batch_from_frame_producer_result:ExtractBatchFromFrameProducerResult
  name: ExtractBatchFromFrameProducerResult
  params:
  upstreamTasks:
    - taskName: PrimarySkipLogic
      mapped: true
      key: frame_producer_result
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: ExtractBatchFromFrameProducerResult
      mapped: true
      key: batch_index
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsent
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: ElasticBulkTransformer
      mapped: true
      key: result
  # Instrument Mapper
- path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
  name: InstrumentMapper
  upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: source_frame
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineCondition
  upstreamTasks:
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformer
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: ExtractBatchFromFrameProducerResult
      mapped: true
      key: batch_index
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsent
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: QuarantinedElasticBulkTransformer
      mapped: true
      key: result
- path: swarm_tasks.control_flow.noop:Noop
  name: Noop
