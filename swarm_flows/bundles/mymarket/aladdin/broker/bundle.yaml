# This flow takes as input a 'Broker' CSV file which is dropped into the path
# of the flow 'order-feed-aladdin'. There is an S3toS3Copy task in that flow
# which copies across broker files to trigger this flow.
id: mymarket-aladdin-broker-firm
name: Mymarket Aladdin Broker
infra:
  - name: tenant-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: URL of the CSV file
controlFlows:
  - name: S3OrLocalFile
    conditionTaskName: ParametersFlowController
    trueTaskName: S3DownloadFile
    falseTaskName: LocalFile
    merge: true
tasks:
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  # Download S3 file
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
      - taskName: file_url
        key: file_url
  # Process local file
  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
      - taskName: file_url
        key: file_url
  # Create chunks
  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvFileSplitter
    params:
      chunksize: 100000
      delimiter: ','
      detect_encoding: true
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result
  # Cast to correct datatype and remove unknown columns
  - path: swarm_tasks.io.read.batch_producer:BatchProducer
    name: BatchProducer
    params:
      remove_unknown_columns: true
      source_schema:
        "cptyDeskId": string
        "cptyId": string
        "issuerLongName": string
    upstreamTasks:
      - taskName: CsvFileSplitter
        mapped: true
        key: file_splitter_result
  # Extract the batch number to use downstream in ElasticBulkTransformer
  - path: swarm_tasks.generic.extract_batch_from_frame_producer_result:ExtractBatchFromFrameProducerResult
    name: ExtractBatchFromFrameProducerResult
    params:
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: frame_producer_result
  # Group together issuerLongName and concat cptyDeskIds, split into create and update frames
  - path: swarm_tasks.steeleye.mymarket.firm.aladdin.split_and_update_trade_file_ids:SplitAndUpdateTradeFileIds
    name: SplitAndUpdateTradeFileIds
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: source_frame
# ------------------------------------------------ Update Branch ------------------------------------------------
  # Get Update Data Frame
  - path: swarm_tasks.generic.get_key_value_from_dict:GetKeyValueFromDict
    name: GetUpdateDataFrame
    params:
      key: "update"
      skip_if_missing: true
    upstreamTasks:
      - taskName: SplitAndUpdateTradeFileIds
        mapped: true
        key: value
  # Elastic Bulk Transformer: we skip AssignMeta and let the Bulk Transformer do
  # the validation as we have only updated the trade file identifiers and the
  # uniqueIds. So we can skip the overhead and unnecessary duplicate fields from
  # AssignMeta (as we already have all the meta fields).
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformerUpdate
    params:
      action_type: index
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: GetUpdateDataFrame
        mapped: true
        key: transform_result
      - taskName: ExtractBatchFromFrameProducerResult
        mapped: true
        key: producer_result
  # Elastic Bulk Writer
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: ElasticBulkWriterUpdate
    params:
      payload_size: 10000000
      action_type: index
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: ElasticBulkTransformerUpdate
        mapped: true
        key: result
# ------------------------------------------------ Create Branch ------------------------------------------------
  # Get Create Data Frame
  - path: swarm_tasks.generic.get_key_value_from_dict:GetKeyValueFromDict
    name: GetCreateDataFrame
    params:
      key: "create"
      skip_if_missing: true
    upstreamTasks:
      - taskName: SplitAndUpdateTradeFileIds
        mapped: true
        key: value
  # Transformations
  - path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
    name: AladdinBrokerFirmTransformations
    upstreamTasks:
      - taskName: GetCreateDataFrame
        mapped: true
        key: result
  - path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
    name: AssignMetaCreate
    params:
      model_attribute: __meta_model__
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: AladdinBrokerFirmTransformations
        mapped: true
        key: result
  # Primary Frame Concatenator.
  # Note: AssignMeta populates &uniqueProps=null for models which don't have
  # unique fields (AccountPerson, MarketPerson). We drop the &uniqueProps value
  # from AssignMeta here, and re-create it from uniqueIds downstream
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PrimaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __meta_model__
        - "&uniqueProps"
    upstreamTasks:
    - taskName: AssignMetaCreate
      mapped: true
      key: meta_assignment_result
    - taskName: AladdinBrokerFirmTransformations
      mapped: true
      key: all_records
  # Populate &uniqueProps from uniqueIds
  - path: swarm_tasks.transform.map.map_attribute:MapAttribute
    name: PopulateUniqueProps
    paramsList:
      - source_attribute: uniqueIds
        target_attribute: "&uniqueProps"
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  # Final Frame Concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: FinalFrameConcatenator
    params:
      orient: horizontal
    upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: primary_frame_concat
    - taskName: PopulateUniqueProps
      mapped: true
      key: unique_props
  # Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformerCreate
    params:
      action_type: create
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: FinalFrameConcatenator
        mapped: true
        key: transform_result
      - taskName: ExtractBatchFromFrameProducerResult
        mapped: true
        key: producer_result
  # Elastic Bulk Writer
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: ElasticBulkWriterCreate
    params:
      payload_size: 10000000
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: ElasticBulkTransformerCreate
        mapped: true
        key: result