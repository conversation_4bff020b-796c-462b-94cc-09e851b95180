# Confluence page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2460811265/BP+-+Market+Person+Feed
# GitHub docs: swarm-flows/docs/bundles/mymarket/bp/mymarket-bp-person-controller.md
# This flow checks for the presence of both pair files BPISTMRL* and KPMGrmrl*. If both are
# present, it triggers the mymarket-bp-person-processor flow. If one of them is not present,
# a FAIL signal is raised.
id: mymarket-bp-person-controller
name: MyMarket BP Person Controller
infra:
  - name: tenant-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process
tasks:
  # This task polls for 2 files, a file starting with 'BPISTMRL' and a file starting with
  # KPMGrmrl. If both are present in S3, it triggers the flow 'mymarket-bp-person'. If only one
  # of the files is present, a SKIP signal is raised.
  - path: swarm_tasks.control_flow.multiple_files_input_controller:MultipleFilesInputController
    name: MultipleFileController
    params:
      list_of_files_regex:
        - "(BPISTMRL)"
        - "(KPMGrmrl)"
      unique_identifier_regex:
        - "\\d{4}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])" # i.e "20220226"
      file_links_in_output: true
      target_cloud_key_prefix: "/flows/mymarket-bp-person-processor"
    upstreamTasks:
      - taskName: file_url
        key: file_url