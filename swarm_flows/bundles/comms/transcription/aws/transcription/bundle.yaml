
id: comms-voice-transcription
name: Transcription and Translation
infra:
  - name: tenant-data
    type: ELASTICSEARCH
tasks:
  - path: swarm_tasks.transform.steeleye.comms.voice.transcription.aws_add_transcription:AwsAddTranscription
    name: AwsAddTranscription
    params:
      skip_transcribed: false
    resources:
      es_client_key: tenant-data
  - path: swarm_tasks.io.read.batch_producer:BatchProducer
    name: BatchProducer
    params:
      source_schema:
        "&hash": string
        "&id": string
        "&key": string
        "&model": object
        language: string
        transcribed: bool
        transcription: string
    upstreamTasks:
      - taskName: AwsAddTranscription
        mapped: true
        key: file_splitter_result
  - path: swarm_tasks.transform.steeleye.comms.voice.translation.translate:AddTranslation
    name: AddTranslation
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_attribute:MapAttribute
    name: MapAttribute
    paramsList:
      - source_attribute: transcription
        target_attribute: body.text
      - source_attribute: transcription
        target_attribute: body.displayText
    upstreamTasks:
      - taskName: AddTranslation
        mapped: true
        key: result
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PrimaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
      - language
      - transcription
    upstreamTasks:
      - taskName: AddTranslation
        mapped: true
        key: result
      - taskName: MapAttribute
        mapped: true
        key: map_attribute
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformer
    params:
      action_type: update
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: transform_result
      - taskName: BatchProducer
        mapped: true
        key: producer_result
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: UpdateBody
    params:
      payload_size: 10000000
      action_type: update
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: ElasticBulkTransformer
        mapped: true
        key: result
