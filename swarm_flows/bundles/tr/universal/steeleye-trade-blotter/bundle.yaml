# bundle path -> tr/universal/steeleye-trade-blotter
id: tr-universal-steeleye-trade-blotter
name: TR Universal SteelEye Trade Blotter
infra:
  - name: tenant-data
    type: ELASTICSEARCH
  - name: reference-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process
controlFlows:
  - name: S3OrLocalFile
    conditionTaskName: ParametersFlowController
    trueTaskName: S3DownloadFile
    falseTaskName: LocalFile
    merge: true
  - name: MapTradingVenue
    conditionTaskName: TradingVenueController
    merge: true
    mapped: true
    cases:
      UNIVERSAL: MapVenueConditional
      MG: TradingVenue
  - name: NeedsQuarantine
    conditionTaskName: QuarantineCondition
    trueTaskName: QuarantinedElasticBulkTransformer
    falseTaskName: Noop
    mapped: true
    merge: false
  - name: S3CopyOverride
    conditionTaskName: S3CopyController
    mapped: false
    merge: true
    cases:
      Noop: Noop # Default case
      S3CopyFile: S3CopyFile # Case specific
tasks:
  - path: swarm_tasks.generic.value_proxy:ValueProxy
    name: S3CopyController
    params:
      value: Noop
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.aws.copy.s3_to_s3_copy:S3ToS3Copy
    name: S3CopyFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvFileSplitter
    params:
      chunksize: 10000
      detect_encoding: true
      normalise_columns: true
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result
  - path: swarm_tasks.io.read.rename_csv_header:RenameCsvHeader
    name: RenameCsvHeader
    params:
      detect_encoding: true
      columns_map:
        "BUYSELL": "BUY/SELL"
        "CFICODE": "INSTRUMENTCLASSIFICATION"
        "DISCRETIONARYNONDISCRETIONARY": "DISCRETIONARY/NONDISCRETIONARY"
        "EXPIRYDATE": "EXPIRYDATE/MATURITYDATE(YYYYMMDD)"
        "EXPIRYDATE/MATURITYDATE": "EXPIRYDATE/MATURITYDATE(YYYYMMDD)"
        "EXPIRYDATEMATURITYDATE": "EXPIRYDATE/MATURITYDATE(YYYYMMDD)"
        "EXPIRYDATEMATURITYDATEYYYYMMDD": "EXPIRYDATE/MATURITYDATE(YYYYMMDD)"
        "ISCFD": "ISCFD?"
        "ISPAD": "ISPAD?"
        "ISSPREADBET": "ISSPREADBET?"
        "ORDERTIMEAPPROVEDBYPM": "ORDERTIME-APPROVEDBYPM"
        "SETTLEDATEYYYYMMDD": "SETTLEDATE"
        "TRADEDATE": "TRADEDATE(YYYYMMDD)"
        "TRADEDATEYYYYMMDD": "TRADEDATE(YYYYMMDD)"
        "TRADETIME": "TRADETIME(HH:MM:SS)"
        "TRADETIME(HHMMSS)": "TRADETIME(HH:MM:SS)"
        "TRADETIMEHH:MM:SS": "TRADETIME(HH:MM:SS)"
        "TRADETIMEHHMMSS": "TRADETIME(HH:MM:SS)"
        "UNDERLYINGINSTRUMENTISIN": "UNDERLYINGINSTRUMENTISIN/S"
        "UNDERLYINGINSTRUMENTISINS": "UNDERLYINGINSTRUMENTISIN/S"
        "UNDERLYINGINSTRUMENTSYMBOL": "UNDERLYINGINSTRUMENTSYMBOL/S"
        "UNDERLYINGINSTRUMENTSYMBOLS": "UNDERLYINGINSTRUMENTSYMBOL/S"
    upstreamTasks:
      - taskName: CsvFileSplitter
        mapped: true
        key: file_path
  - path: swarm_tasks.io.read.batch_producer:BatchProducer
    name: BatchProducer
    params:
      source_schema:
        "AVERAGEPRICE": float
        "BLOOMBERGFIGIID": string
        "BUY/SELL": string
        "CLIENTID": string
        "COUNTERPARTY": string
        "COUNTERPARTYID": string
        "COUNTRYOFTHEBRANCHMEMBERSHIP": string
        "CUSIP": string
        "DATASOURCENAME": string
        "DELIVERYTYPE": string
        "DISCRETIONARY/NONDISCRETIONARY": string
        "EXCHANGEMIC": string
        "EXECUTINGENTITYID": string
        "EXECUTIONWITHINFIRM": string
        "EXPIRYDATE/MATURITYDATE(YYYYMMDD)": string
        "INITIALQUANTITY": string
        "INSTRUMENTASSETCLASS": string
        "INSTRUMENTCLASSIFICATION": string
        "INSTRUMENTNAME": string
        "INTERESTRATESTARTDATE": string
        "INVESTMENTDECISIONMAKER": string
        "ISIN": string
        "ISALLOCATION?": string
        "ISCFD?": string
        "ISPAD?": string
        "ISSPREADBET?": string
        "LIMITPRICE": float
        "MONTHLYCONTRACTCODE": string
        "NETAMOUNT": float
        "NOTIONALCURRENCY1": string
        "NOTIONALCURRENCY2": string
        "OPTIONSTRIKEPRICE": float
        "OPTIONSTYLE": string
        "OPTIONTYPE": string
        "ORDERID": string
        "ORDERSTATUS": string
        "ORDERTYPE": string
        "POSITIONEFFECT": string
        "POSITIONID": string
        "PRICE": float
        "PRICECURRENCY": string
        "PRICEMULTIPLIER": float
        "PRICENOTATION": string
        "QUANTITYCURRENCY": string
        "QUANTITYNOTATION": string
        "REMAININGQUANTITY": string
        "RECORDTYPE": string
        "REPORTSTATUS": string
        "SETTLEAMOUNT": float
        "SETTLEDATE": string
        "STOPPRICE": float
        "SYMBOL": string
        "TIMEINFORCEFORANORDER": string
        "TRADEDATE(YYYYMMDD)": string
        "TRADEID": string
        "TRADETIME(HH:MM:SS)": string
        "TRADEDQUANTITY": float
        "TRADERID": string
        "TRADINGCAPACITY": string
        "TRANSMISSIONOFORDER": string
        "TRANSMITTINGFOR": string
        "ULTIMATEVENUE": string
        "UNDERLYINGINDEXNAME": string
        "UNDERLYINGINDEXNAMELEG2": string
        "UNDERLYINGINDEXSERIES": string
        "UNDERLYINGINDEXTERM": string
        "UNDERLYINGINDEXTERMLEG2": string
        "UNDERLYINGINDEXVERSION": string
        "UNDERLYINGINSTRUMENTISIN/S": string
        "UNDERLYINGINSTRUMENTSYMBOL/S": string
        "UP-FRONTPAYMENT": float
        "UP-FRONTPAYMENTCURRENCY": string
    upstreamTasks:
      - taskName: RenameCsvHeader
        mapped: true
        key: file_splitter_result
  - path: swarm_tasks.transform.datetime.convert_datetime:ConvertDatetime
    name: ConvertDatetime
    paramsList:
      - source_attribute: transactionDetails.tradingDateTime
        target_attribute: date
        convert_to: date
      - source_attribute: EXPIRYDATE/MATURITYDATE(YYYYMMDD)
        target_attribute: __fb_expiry_date__
        convert_to: date
        filter_dates: true
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
      - taskName: TradingDateTime
        mapped: true
        key: trading_date_time
  - path: swarm_tasks.generic.currency.convert_minor_to_major:ConvertMinorToMajor
    name: ConvertMinorToMajor
    paramsList:
      - source_ccy_attributes:
          - QUANTITYCURRENCY
          - NOTIONALCURRENCY2
          - PRICECURRENCY
        target_ccy_attribute: __quantity_currency__
      - source_ccy_attribute: PRICECURRENCY
        target_ccy_attribute: transactionDetails.priceCurrency
      - source_ccy_attribute: PRICECURRENCY
        target_ccy_attribute: transactionDetails.settlementAmountCurrency
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.generic.currency.convert_minor_to_major:ConvertMinorToMajor
    name: ConvertMinorToMajorPrice
    params:
      source_price_attribute: PRICE
      source_ccy_attribute: PRICECURRENCY
      target_price_attribute: transactionDetails.price
      cast_to: abs
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.generic.currency.convert_minor_to_major:ConvertMinorToMajor
    name: ConvertMinorToMajorStrikePrice
    params:
      source_price_attribute: OPTIONSTRIKEPRICE
      source_ccy_attribute: PRICECURRENCY
      target_price_attribute: __option_strike_price__
      cast_to: abs
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: MinorToMajorConcatenator
    params:
      orient: horizontal
    upstreamTasks:
      - taskName: ConvertMinorToMajor
        mapped: true
        key: minor_to_major
      - taskName: ConvertMinorToMajorPrice
        mapped: true
        key: minor_to_major_price
      - taskName: ConvertMinorToMajorStrikePrice
        mapped: true
        key: result
  - path: swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.map_asset_class:MapAssetClass
    name: MapAssetClass
    params:
      fx_spot_asset_classes:
        - FX Forward
        - FX Future
        - Foreign Exchange Forward
        - Foreign Exchange Future
        - Currency Forward
        - Currency Future
        - Currency Derivatives Forwards
      spot_currency_pairs:
        - USDCAD
        - CADUSD
        - USDTRY
        - TRYUSD
        - USDPHP
        - PHPUSD
        - USDRUB
        - RUBUSD
      str_to_bool_dict:
        "true": True
        "y": True
        "yes": True
        "t": True
        "on": True
        "false": False
        "n": False
        "no": False
        "f": False
        "off": False
      asset_class_map:
        BOND: bond
        CREDIT DEFAULT SWAP INDEX: cds index
        CREDIT DEFAULT SWAP INDEX TRANCHE: cds index
        CREDIT DEFAULT SWAPS SINGLE NAME: cds single stock
        CDS INDEX SWAP: cds index
        CDX INDEX SWAP: cds index
        CDS INDEX: cds index
        CDS INDEX TRANCHE: cds index
        CDS SINGLE STOCK: cds single stock
        CDS SINGLE: cds single stock
        CDX: cds index
        CREDIT DEFAULT SWAP: cds single stock
        CREDIT DEFAULT SWAPS SINGLE STOCK: cds single stock
        CURRENCY DERIVATIVES FORWARDS: fx forward
        CURRENCY DERIVATIVES FUTURES: fx forward
        CURRENCY DERIVATIVES OPTIONS: fx option
        CURRENCY OPTION: fx option
        CURRENCY SPOT: fx spot
        CURRENCY SWAP: fx swap
        EQUITY FORWARD: equity forward
        EQUITY OPTION: option
        EQUITY SWAP: equity swap
        FOREIGN EXCHANGE FORWARD: fx forward
        FOREIGN EXCHANGE NDF: fx forward
        FOREIGN EXCHANGE OPTION: fx option
        FOREIGN EXCHANGE SPOT: fx spot
        FOREIGN EXCHANGE SWAP: fx swap
        FUTURE: future
        FUTURES: future
        FX CFD: fx cfd
        FX FORWARD: fx forward
        FX NDF: fx forward
        FX NONDELIVERABLE FORWARD: fx forward
        FX OPTION: fx option
        FX OPTION BARRIER: fx option barrier
        FX OPTION BARRIER DIGITAL: fx option barrier digital
        FX OPTION LOOKBACK: fx option lookback
        FX OPTION OTHER: fx option other
        FX SB: fx sb
        FX SPOT: fx spot
        FX SWAP: fx swap
        OPTION: option
        SB: sb
        SPOT FX: fx spot
        SPOTFX: fx spot
        TOTAL RETURN SWAP: trs single stock
        TOTAL RETURN SWAP SINGLE STOCK: trs single stock
        TOTAL RETURN SWAP SINGLE: trs single stock
        TOTAL RETURN SWAP NAME: trs single stock
        TRS SINGLE NAME: trs single stock
        TRS SINGLE STOCK: trs single stock
      target_attribute: __asset_class__
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.transform.steeleye.orders.data_source.universal.steeleye_trade_blotter.convert_cusip_to_isin:ConvertCusipToIsin
    name: ConvertCusipToISIN
    params:
      target_attribute: __isin__
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers:InstrumentIdentifiers
    name: InstrumentIdentifiers
    params:
      asset_class_attribute: __asset_class__
      currency_attribute: transactionDetails.priceCurrency
      expiry_date_attribute: __expiry_date_filtered__
      isin_attribute: __isin__
      instrument_classification_attribute: INSTRUMENTCLASSIFICATION
      interest_rate_start_date_attribute: INTERESTRATESTARTDATE
      interest_rate_start_date_format: "%Y%m%d"
      notional_currency_1_attribute: NOTIONALCURRENCY1
      notional_currency_2_attribute: __instrument_notional_currency_2__
      option_strike_price_attribute: __option_strike_price__
      option_type_attribute: OPTIONTYPE
      swap_near_leg_date_attribute: TRADEDATE(YYYYMMDD)
      underlying_index_name_attribute: UNDERLYINGINDEXNAME
      underlying_index_name_leg_2_attribute: UNDERLYINGINDEXNAMELEG2
      underlying_index_series_attribute: UNDERLYINGINDEXSERIES
      underlying_index_term_attribute: UNDERLYINGINDEXTERM
      underlying_index_term_leg_2_attribute: UNDERLYINGINDEXTERMLEG2
      underlying_index_term_value_attribute: UNDERLYINGINDEXTERM
      underlying_index_term_value_leg_2_attribute: UNDERLYINGINDEXTERMLEG2
      underlying_index_version_attribute: UNDERLYINGINDEXVERSION
      underlying_isin_attribute: UNDERLYINGINSTRUMENTISIN/S
      underlying_symbol_attribute: __instrument_symbol__
      underlying_symbol_expiry_code_attribute: MONTHLYCONTRACTCODE
      venue_attribute: __instrument_venue__
      retain_task_inputs: true
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
      - taskName: MapAssetClass
        mapped: true
        key: asset_class
      - taskName: MinorToMajorConcatenator
        mapped: true
        key: convert_to_major
      - taskName: MapConditional
        mapped: true
        key: map_conditional
      - taskName: ConvertCusipToISIN
        mapped: true
        key: convert_cusip
  - path: swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.party_identifiers:PartyIdentifiers
    name: PartyIdentifiers
    params:
      override_discretionary: false
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
      - taskName: MapValue
        mapped: true
        key: map_value
  - path: swarm_tasks.transform.map.map_static:MapStatic
    name: MapStatic
    paramsList:
      - target_attribute: tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator
        target_value: false
      - target_attribute: sourceIndex
        from_index: true
      - target_attribute: sourceKey
        from_env_var: SWARM_FILE_URL
      - target_attribute: __meta_model__
        target_value: RTS22Transaction
      - target_attribute: __fb_is_created_through_fallback__
        target_value: true
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_attribute:MapAttribute
    name: MapAttribute
    paramsList:
      - source_attribute: NETAMOUNT
        target_attribute: transactionDetails.netAmount
        cast_to: numeric.absolute
      - source_attribute: TRADEDQUANTITY
        target_attribute: transactionDetails.quantity
        cast_to: numeric.absolute
      - source_attribute: COUNTRYOFTHEBRANCHMEMBERSHIP
        target_attribute: __branch_membership_country__
      # Auxiliary columns for MapVenueConditional
      - source_attribute: ULTIMATEVENUE
        target_attribute: __ultimate_venue
      - source_attribute: EXCHANGEMIC
        target_attribute: __exchange_mic
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.transform.steeleye.link.merge_market_identifiers:MergeMarketIdentifiers
    name: MergeMarketIdentifiers
    params:
      identifiers_path: marketIdentifiers
      instrument_path: marketIdentifiers.instrument
      parties_path: marketIdentifiers.parties
    upstreamTasks:
      - taskName: InstrumentIdentifiers
        mapped: true
        key: result
      - taskName: PartyIdentifiers
        mapped: true
        key: party_identifiers
  - path: swarm_tasks.transform.datetime.join_date_and_time:JoinDateAndTimeFormat
    name: TradingDateTime
    params:
      source_date_attribute: TRADEDATE(YYYYMMDD)
      source_time_attribute: TRADETIME(HH:MM:SS)
      target_attribute: transactionDetails.tradingDateTime
      source_formats: ['%Y%m%d%H:%M:%S.%fZ', '%Y%m%d%H:%M:%S.%f', '%Y%m%d%H:%M:%S']
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_value:MapValue
    name: MapValue
    paramsList:
      - source_attribute: TRADINGCAPACITY
        target_attribute: transactionDetails.tradingCapacity
        case_insensitive: true
        value_map:
          agency: AOTC
          aotc: AOTC
          deal: DEAL
          match: MTCH
          mtch: MTCH
          pro: DEAL
          proprietary: DEAL
      - source_attribute: BUY/SELL
        target_attribute: transactionDetails.buySellIndicator
        case_insensitive: true
        value_map:
          buy: BUYI
          b: BUYI
          cover: BUYI
          buy to cover: BUYI
          buytocover: BUYI
          sell: SELL
          s: SELL
          short: SELL
          sell short: SELL
          short sell: SELL
      - source_attribute: POSITIONEFFECT
        target_attribute: transactionDetails.positionEffect
        case_insensitive: true
        value_map:
          C: Close
          D: Default
          F: FIFO
          N: Closed but notify on open
          O: Open
          R: Rolled
      - source_attribute: QUANTITYNOTATION
        target_attribute: transactionDetails.quantityNotation
        case_insensitive: true
        value_map: "{'M': 'MONE', 'MONE': 'MONE', 'MONETARY': 'MONE', 'MONEY': 'MONE', 'N': 'NOML', 'NOMI': 'NOML', 'NOMINAL': 'NOML', 'NOMINALVALUE': 'NOML', 'NOML': 'NOML', 'NOTIONAL': 'NOML', 0: 'UNIT', 1: 'UNIT', 2: 'UNIT', 'U': 'UNIT', 'UNIT': 'UNIT'}"
      - source_attribute: ISALLOCATION?
        target_attribute: __record_type_from_is_allocation__
        case_insensitive: true
        value_map:
          y: Allocation
          true: Allocation
          t: Allocation
      - source_attribute: RECORDTYPE
        target_attribute: __record_type_from_record_type__
        case_insensitive: true
        value_map:
          Client Side: Client Side
          Market Side: Market Side
          Allocation: Allocation
      - source_attribute: PRICENOTATION
        target_attribute: transactionDetails.priceNotation
        case_insensitive: true
        value_map: "{'%': 'PERC', 1: 'PERC', 25: 'PERC', 'P': 'PERC', 'PERC': 'PERC', 'PERCENTAGE': 'PERC', 12: 'MONE', 23: 'MONE', 2: 'MONE', 'M': 'MONE', 'MONE': 'MONE', 'MONETARY': 'MONE', 'MONEY': 'MONE', 22: 'BAPO', 6: 'BAPO', 'B': 'BAPO', 'BAPO': 'BAPO', 'BASE POINTS': 'BAPO', 'BASIS POINTS': 'BAPO', 'BP': 'BAPO', 9: 'YIEL', 'Y': 'YIEL', 'YIEL': 'YIEL', 'YIELD': 'YIEL'}"
      - source_attribute: TRANSMISSIONOFORDER
        target_attribute: transmissionDetails.orderTransmissionIndicator
        case_insensitive: true
        default_value: false
        value_map: "{'true': 'true', 1: 'true', 't': 'true', 'y': 'true', 'false': 'false', 0: 'false', 'f': 'false', 'n': 'false'}"
      - source_attribute: OPTIONTYPE
        target_attribute: __option_type__
        case_insensitive: true
        value_map: "{0: 'PUTO', 'P': 'PUTO', 'PUT': 'PUTO', 'PUTO': 'PUTO', 1: 'CALL', 'C': 'CALL', 'CALL': 'CALL', 2: 'OTHR', 'OTHER': 'OTHR', 'OTHR': 'OTHR'}"
      - source_attribute: OPTIONSTYLE
        target_attribute: __option_style__
        case_insensitive: true
        value_map:
          AMER: AMER
          AMERICA: AMER
          AMERICAN: AMER
          EURO: EURO
          EUROPE: EURO
          EUROPEAN: EURO
          ASIA: ASIA
          ASIAN: ASIA
          BERM: BERM
          BERMUDA: BERM
          OTHER: OTHR
          OTHR: OTHR
      - source_attribute: PRICENOTATION
        target_attribute: __ext_strikePriceType__
        case_insensitive: true
        value_map: "{'%': 'Pctg', 1: 'Pctg', 25: 'Pctg', 'P': 'Pctg', 'PERC': 'Pctg', 'PERCENTAGE': 'Pctg', 12: 'MntryVal', 23: 'MntryVal', 2: 'MntryVal', 'M': 'MntryVal', 'MONE': 'MntryVal', 'MONETARY': 'MntryVal', 'MONEY': 'MntryVal', 22: 'BsisPts', 6: 'BsisPts', 'B': 'BsisPts', 'BAPO': 'BsisPts', 'BASE POINTS': 'BsisPts', 'BASIS POINTS': 'BsisPts', 'BP': 'BsisPts', 9: 'Yld', 'Y': 'Yld', 'YIEL': 'Yld', 'YIELD': 'Yld', 'NA': 'PNDG', 'NOAP': 'PNDG', 'NOT AVAILABLE': 'PNDG', 'PEND': 'PNDG', 'PENDING': 'PNDG', 'PND': 'PNDG', 'PNDG': 'PNDG'}"
      - source_attribute: REPORTSTATUS
        target_attribute: reportDetails.reportStatus
        case_insensitive: true
        default_value: NEWT
        value_map:
          NEW: NEWT
          NEWT: NEWT
          N: NEWT
          CANC: CANC
          CANCEL: CANC
          C: CANC
      - source_attribute: DELIVERYTYPE
        target_attribute: __delivery_type__
        case_insensitive: true
        value_map:
          P: PHYS
          PHYSICAL: PHYS
          PHYS: PHYS
          C: CASH
          CASH: CASH
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_conditional:MapConditional
    name: MapConditional
    paramsList:
      - target_attribute: transactionDetails.pricePending
        cases:
          - query: "`transactionDetails.price`.isnull()"
            value: true
          - query: "`transactionDetails.price`.notnull()"
            value: false
      - target_attribute: __instrument_venue__
        cases:
          - query: "index == index"
            attribute: EXCHANGEMIC
          - query: "`ULTIMATEVENUE`.notnull() & (`ULTIMATEVENUE` != 'XOFF') & (`ULTIMATEVENUE`.str.len() == 4)"
            attribute: ULTIMATEVENUE
      - target_attribute: instrumentDetails.instrument.ext.strikePriceType
        cases:
          - query: "`OPTIONSTRIKEPRICE`.notnull()"
            attribute: __ext_strikePriceType__
      - target_attribute: __instrument_symbol__
        cases:
          - query: "index == index"
            attribute: UNDERLYINGINSTRUMENTSYMBOL/S
          - query: "`UNDERLYINGINSTRUMENTSYMBOL/S`.isnull()"
            attribute: SYMBOL
      - target_attribute: __instrument_notional_currency_2__
        cases:
          - query: "index == index"
            attribute: NOTIONALCURRENCY2
          - query: "`NOTIONALCURRENCY2`.isnull()"
            attribute: QUANTITYCURRENCY
      - target_attribute: transactionDetails.recordType
        cases:
          - query: "`__record_type_from_record_type__`.notnull()"
            attribute: __record_type_from_record_type__
          - query: "`__record_type_from_record_type__`.isnull()"
            attribute: __record_type_from_is_allocation__
      - target_attribute: __fb_ext_strikePriceCurrency__
        cases:
          - query: "`OPTIONSTRIKEPRICE`.notnull()"
            attribute: PRICECURRENCY
      - target_attribute: __fb_ext_strikePriceType__
        cases:
          - query: "`OPTIONSTRIKEPRICE`.notnull()"
            attribute: __ext_strikePriceType__
      - target_attribute: dataSourceName
        cases:
          - query: "`DATASOURCENAME`.isnull()"
            value: "SteeleyeTradeBlotter"
          - query: "`DATASOURCENAME`.notnull()"
            attribute: DATASOURCENAME
      - target_attribute: __expiry_date_filtered__
        cases:
          - query: "`__fb_expiry_date__`.notnull()"
            attribute: "EXPIRYDATE/MATURITYDATE(YYYYMMDD)"
      - target_attribute: transactionDetails.quantityCurrency
        cases:
          - query: "`transactionDetails.quantityNotation`.str.fullmatch(r'(MONE|NOML)', case=False, na=False)"
            attribute: __quantity_currency__
          - query: "~(`transactionDetails.quantityNotation`.str.fullmatch(r'(MONE|NOML)', case=False, na=False))"
            as_empty: true
      - target_attribute: __null_col__
        cases:
          - query: "index == index"
            as_empty: true
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
      - taskName: MinorToMajorConcatenator
        mapped: true
        key: convert_minor_to_major
      - taskName: MapValue
        mapped: true
        key: map_value
      - taskName: ConvertDatetime
        mapped: true
        key: convert_datetime
  - path: swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.transaction_ref_no:TransactionRefNo
    name: TransactionRefNo
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
      - taskName: TradingDateTime
        mapped: true
        key: trading_date_time
      - taskName: MapValue
        mapped: true
        key: map_value_result
  - path: swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.up_front:TrxDtlUpFront
    name: TrxDtlUpFront
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
      - taskName: MapAssetClass
        mapped: true
        key: asset_class_map
  # Primary Frame Concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PrimaryFrameConcatenator
    params:
      orient: horizontal
    upstreamTasks:
      - taskName: ConvertDatetime
        mapped: true
        key: convert_date_time
      - taskName: MinorToMajorConcatenator
        mapped: true
        key: convert_minor_to_major
      - taskName: InstrumentIdentifiers
        mapped: true
        key: instrument_identifiers
      - taskName: TradingDateTime
        mapped: true
        key: trading_date_time
      - taskName: MapAttribute
        mapped: true
        key: map_attribute
      - taskName: MapStatic
        mapped: true
        key: map_static
      - taskName: MapValue
        mapped: true
        key: map_value
      - taskName: MergeMarketIdentifiers
        mapped: true
        key: merge_market_identifiers
      - taskName: PartyIdentifiers
        mapped: true
        key: party_identifiers
      - taskName: MapConditional
        mapped: true
        key: map_conditional
      - taskName: TransactionRefNo
        mapped: true
        key: transaction_ref_no
      - taskName: TrxDtlUpFront
        mapped: true
        key: trx_dtl_up_front
  - path: swarm_tasks.transform.steeleye.link.parties:LinkParties
    name: LinkParties
    params:
      identifiers_path: marketIdentifiers.parties
      add_investment_firm_covered_directive: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  # Trading Venue Controller
  - path: swarm_tasks.generic.value_proxy:ValueProxy
    name: TradingVenueController
    params:
      value: UNIVERSAL
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
      - taskName: LinkParties
        mapped: true
        key: link_parties
  # MapVenueConditional (executed if Trading Venue Controller value == UNIVERSAL)
  - path: swarm_tasks.transform.map.map_conditional:MapConditional
    name: MapVenueConditional
    paramsList:
      - target_attribute: transactionDetails.venue
        cases:
          - query: "index == index"
            value: XOFF
          - query: "`__ultimate_venue`.notnull()"
            attribute: __ultimate_venue
          - query: "`__exchange_mic`.notnull()"
            attribute: __exchange_mic
      - target_attribute: transactionDetails.ultimateVenue
        cases:
          - query: "`__exchange_mic`.notnull() & ~`__exchange_mic`.isin(('XXXX', 'XOFF'))"
            attribute: __exchange_mic
          - query: "`__ultimate_venue`.notnull() & ~`__ultimate_venue`.isin(('XXXX', 'XOFF'))"
            attribute: __ultimate_venue
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  # TradingVenue (executed if Trading Venue Controller value == MG)
  - path: swarm_tasks.transform.steeleye.tr.data_source.mg.trading_venue:TradingVenue
    name: TradingVenue
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
      - taskName: LinkParties
        mapped: true
        key: link_parties
  - path: swarm_tasks.transform.steeleye.tr.mifir.eligibility_assessor:EligibilityAssessor
    name: EligibilityAssessor
    resources:
      es_client_key: tenant-data
      srp_client_key: reference-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
      - taskName: InstrumentFallback
        mapped: true
        key: instrument_fallback
      - taskName: MapTradingVenue
        mapped: true
        key: trading_venue
  - path: swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.branch_membership_country:BranchMembershipCountry
    name: BranchMembershipCountry
    params:
      branch_membership_country_column: __branch_membership_country__
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
      - taskName: MapTradingVenue
        mapped: true
        key: trading_venue
      - taskName: LinkParties
        mapped: true
        key: parties

  - path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
    name: LinkInstrument
    params:
      identifiers_path: marketIdentifiers.instrument
      venue_attribute: __instrument_venue__
      currency_attribute: transactionDetails.priceCurrency
      asset_class_attribute: __asset_class__
    resources:
      ref_data_key: reference-data
      tenant_data_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
      - taskName: MapTradingVenue
        mapped: true
        key: trading_venue
      - taskName: MapAssetClass
        mapped: true
        key: asset_class
  - path: swarm_tasks.transform.map.map_to_nested:MapToNested
    name: InstrumentOverrides
    params:
      source_attribute: OPTIONSTRIKEPRICE
      nested_path: derivative.strikePrice
      target_attribute: instrumentDetails.instrument
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
      - taskName: LinkInstrument
        mapped: true
        key: link_instrument
  - path: swarm_tasks.transform.map.map_to_nested:MapToNested
    name: InstrumentDeliveryTypeOverride
    params:
      source_attribute: __delivery_type__
      nested_path: derivative.deliveryType
      target_attribute: instrumentDetails.instrument
      skip_source_nan: True
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
      - taskName: InstrumentOverrides
        mapped: true
        key: link_instrument
  # Override StrikePriceType and StrikePriceCurrency if override_strike_price_currency_and_type is True
  # (logic should be used in flow overrides). Default behaviour: just return the source_frame
  - path: swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.override_strike_price_currency_and_type:OverrideStrikePriceCurrencyAndType
    name: OverrideStrikePriceCurrencyAndType
    params:
      override_strike_price_currency_and_type: false
    upstreamTasks:
      - taskName: InstrumentDeliveryTypeOverride
        mapped: true
        key: result
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: primary_frame_concat
  # Override StrikePriceType in instrumentDetails.instrument for FX instruments
  - path: swarm_tasks.transform.steeleye.orders.data_source.universal.steeleye_trade_blotter.strike_price_type_override:StrikePriceTypeOverride
    name: InstrumentOverridesStrikePriceType
    params:
      override_strike_price_type: true
    upstreamTasks:
      - taskName: OverrideStrikePriceCurrencyAndType
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_to_nested:MapToNested
    name: InstrumentFullNameOverride
    params:
      source_attribute: INSTRUMENTNAME
      nested_path: instrumentFullName
      target_attribute: instrumentDetails.instrument
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
      - taskName: InstrumentOverridesStrikePriceType
        mapped: true
        key: link_instrument
  - path: swarm_tasks.transform.map.map_to_nested:MapToNested
    name: InstrumentClassificationOverride
    params:
      source_attribute: INSTRUMENTCLASSIFICATION
      nested_path: instrumentClassification
      target_attribute: instrumentDetails.instrument
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
      - taskName: InstrumentFullNameOverride
        mapped: true
        key: link_instrument
  - path: swarm_tasks.transform.map.map_conditional:MapConditional
    name: InstrumentFallbackConditional
    paramsList:
      - target_attribute: __fb_instrumentIdCodeType__
        cases:
          - query: "`ISIN`.str.len() == 12"
            value: ID
      - target_attribute: __fb_venue_tradingVenue__
        cases:
          - query: "index == index"
            value: XXXX
          - query: "`EXCHANGEMIC`.notnull()"
            attribute: EXCHANGEMIC
          - query: "`EXCHANGEMIC`.isnull() & `ULTIMATEVENUE`.notnull()"
            attribute: ULTIMATEVENUE
          - query: "`ULTIMATEVENUE`.notnull() & `ULTIMATEVENUE`.str.len() == 4 & `ULTIMATEVENUE`.str.upper() != 'XOFF'"
            attribute: ULTIMATEVENUE
      - target_attribute: __fb_ext_notionalCurrency2Type__
        cases:
          - query: "`NOTIONALCURRENCY2`.notnull()"
            value: MntryVal
      - target_attribute: __IS_CFD__
        cases:
          - query: "`ISCFD?`.str.match('TRUE|T|Y|YES', case=False, na=False)"
            value: True
      - target_attribute: __fb_bond_maturity_date__
        cases:
          - query: "`INSTRUMENTCLASSIFICATION`.str.upper().str.startswith('D') | `INSTRUMENTASSETCLASS`.str.contains('BOND|FIXED INCOME|MONEY MARKET', case=False, na=False)"
            attribute: __fb_expiry_date__
      - target_attribute: __fb_exchange_symbol_root__
        cases:
          - query: "`UNDERLYINGINSTRUMENTSYMBOL/S`.notnull()"
            attribute: UNDERLYINGINSTRUMENTSYMBOL/S
          - query: "`UNDERLYINGINSTRUMENTSYMBOL/S`.isnull() & `SYMBOL`.notnull()"
            attribute: SYMBOL
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: primary_frame_concat_result
  - path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
    name: InstrumentFallback
    params:
      is_se_blotter: True
      derived_asset_class_attribute: __asset_class__
      market_instrument_identifiers_attribute: marketIdentifiers.instrument
      instrument_fields_map:
        - source_field: __fb_bond_maturity_date__
          target_field: bond.maturityDate
        - source_field: __fb_is_created_through_fallback__
          target_field: isCreatedThroughFallback
        - source_field: __delivery_type__
          target_field: derivative.deliveryType
        - source_field: __fb_expiry_date__
          target_field: derivative.expiryDate
        - source_field: __option_style__
          target_field: derivative.optionExerciseStyle
        - source_field: __option_type__
          target_field: derivative.optionType
        - source_field: PRICEMULTIPLIER
          target_field: derivative.priceMultiplier
        - source_field: BLOOMBERGFIGIID
          target_field: ext.figi
        - source_field: __fb_instrumentIdCodeType__
          target_field: ext.instrumentIdCodeType
        - source_field: __fb_ext_strikePriceType__
          target_field: ext.strikePriceType
        - source_field: __fb_ext_strikePriceCurrency__
          target_field: derivative.strikePriceCurrency
        - source_field: __fb_ext_notionalCurrency2Type__
          target_field: ext.notionalCurrency2Type
      str_to_bool_dict:
        "true": True
        "y": True
        "yes": True
        "t": True
        "on": True
        "false": False
        "n": False
        "no": False
        "f": False
        "off": False
    upstreamTasks:
      - taskName: InstrumentClassificationOverride
        mapped: true
        key: instrument_overrides
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: primary_frame
      - taskName: BatchProducer
        mapped: true
        key: result
      - taskName: InstrumentFallbackConditional
        mapped: true
        key: inst_fb_conditional
      - taskName: MapAssetClass
        mapped: true
        key: map_asset_class
  - path: swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.transmitting_for:TransmittingFor
    name: TransmittingFor
    params: null
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: primary_frame_concatenator
      - taskName: LinkParties
        mapped: true
        key: link_parties
  - path: swarm_tasks.transform.steeleye.tr.generic.short_selling_indicator:ShortSellingIndicator
    name: ShortSellingIndicator
    params:
      buy_sell_attribute: BUY/SELL
      buy_sell_indicator_attribute: transactionDetails.buySellIndicator
      client_id_attribute: CLIENTID
      instrument_attribute: instrumentDetails.instrument
      instrument_classification_nested_path: instrumentClassification
      target_attribute: tradersAlgosWaiversIndicators.shortSellingIndicator
      issuer_or_operator_of_trading_venue_id_nested_path: issuerOrOperatorOfTradingVenueId
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
      - taskName: LinkInstrument
        mapped: true
        key: link_instrument
      - taskName: BatchProducer
        mapped: true
        key: batch_producer
  # Assign Trading Venue Transaction ID Code
  - path: swarm_tasks.steeleye.tr.data_source.steeleye_trade_blotter.trading_venue_transaction_id_code:TradingVenueTransactionIdCode
    name: TradingVenueTransactionIdCode
    params:
      venue_type: UK_VENUES
    upstreamTasks:
      - taskName: MapTradingVenue
        mapped: true
        key: result
      - taskName: TransactionRefNo
        mapped: true
        key: transaction_ref_no
  # Auxiliary Frame Concatenator
  - path: swarm_tasks.transform.steeleye.orders.common_utils.commodity_derivative_indicators:CommodityDerivativeIndicators
    name: CommodityDerivativeIndicators
    params:
      target_attribute: tradersAlgosWaiversIndicators.commodityDerivativeIndicator
      instrument_nested_path: commoditiesOrEmissionAllowanceDerivativeInd
      instrument_attribute: instrumentDetails.instrument
    upstreamTasks:
      - taskName: InstrumentFallback
        mapped: true
        key: instrument_fallback
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: AuxiliaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __asset_class__
        - __branch_membership_country__
        - __delivery_type__
        - __expiry_date_filtered__
        - __instrument_venue__
        - marketIdentifiers.instrument
        - marketIdentifiers.parties
        - __instrument_symbol__
        - __instrument_notional_currency_2__
        - __isin__
        - __null_col__
        - __ultimate_venue
        - __exchange_mic
        - __option_strike_price__
        - __option_style__
        - __option_type__
        - __ext_strikePriceType__
        - __fb_instrumentIdCodeType__
        - __fb_venue_tradingVenue__
        - __fb_ext_notionalCurrency2Type__
        - __fb_ext_strikePriceCurrency__
        - __fb_ext_strikePriceType__
        - __fb_is_created_through_fallback__
        - __fb_expiry_date__
        - __fb_bond_maturity_date__
        - __record_type_from_record_type__
        - __record_type_from_is_allocation__
        - __quantity_currency__
        - asset_class_attribute # Instrument Identifiers columns
        - bbg_figi_id_attribute
        - currency_attribute
        - eurex_id_attribute
        - exchange_symbol_attribute
        - expiry_date_attribute
        - interest_rate_start_date_attribute
        - isin_attribute
        - notional_currency_1_attribute
        - notional_currency_2_attribute
        - option_strike_price_attribute
        - option_type_attribute
        - swap_near_leg_date_attribute
        - underlying_index_name_attribute
        - underlying_index_name_leg_2_attribute
        - underlying_index_series_attribute
        - underlying_index_term_attribute
        - underlying_index_term_value_attribute
        - underlying_index_version_attribute
        - underlying_isin_attribute
        - underlying_symbol_attribute
        - underlying_symbol_expiry_code_attribute
        - underlying_index_term_leg_2_attribute
        - underlying_index_term_value_leg_2_attribute
        - venue_attribute
        - venue_financial_instrument_short_name_attribute
        - instrument_classification_attribute
    upstreamTasks:
      - taskName: BranchMembershipCountry
        mapped: true
        key: branch_membership_country
      - taskName: CommodityDerivativeIndicators
        mapped: true
        key: commodity_derivative_indicators
      - taskName: InstrumentFallback
        mapped: true
        key: instrument_fallback
      - taskName: EligibilityAssessor
        mapped: true
        key: eligibility_assessor
      - taskName: LinkParties
        mapped: true
        key: link_parties
      - taskName: TransmittingFor
        mapped: true
        key: transmitting_for
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: primary_frame_concatenator
      - taskName: ShortSellingIndicator
        mapped: true
        key: short_selling_indicator
      - taskName: MapTradingVenue
        mapped: true
        key: trading_venue
      - taskName: TradingVenueTransactionIdCode
        mapped: true
        key: trd_venue_trx_id_code
  - path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
    name: Meta
    params:
      model_attribute: __meta_model__
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: AuxiliaryFrameConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_conditional:MapConditional
    name: WorkflowStatus
    params:
      source_attribute: '&validationErrors'
      target_attribute: workflow.validationStatus
      condition: isnull
      true_value: PASSED
      false_value: FAILED
    upstreamTasks:
      - taskName: Meta
        mapped: true
        key: result
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: FinalFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __meta_model__
    upstreamTasks:
      - taskName: AuxiliaryFrameConcatenator
        mapped: true
        key: result
      - taskName: Meta
        mapped: true
        key: meta_result
      - taskName: WorkflowStatus
        mapped: true
        key: workflow_status_result
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformer
    params:
      action_type: create
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: FinalFrameConcatenator
        mapped: true
        key: transform_result
      - taskName: BatchProducer
        mapped: true
        key: producer_result
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: PutIfAbsent
    params:
      payload_size: 10000000
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: ElasticBulkTransformer
        mapped: true
        key: result
  # quarantine
  - path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
    name: QuarantineCondition
    upstreamTasks:
      - taskName: PutIfAbsent
        mapped: true
        key: bulk_writer_result
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: QuarantinedElasticBulkTransformer
    params:
      action_type: create
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: FinalFrameConcatenator
        mapped: true
        key: transform_result
      - taskName: BatchProducer
        mapped: true
        key: producer_result
      - taskName: PutIfAbsent
        mapped: true
        key: bulk_writer_result
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: QuarantinedPutIfAbsent
    params:
      payload_size: 10000000
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: QuarantinedElasticBulkTransformer
        mapped: true
        key: result
  - path: swarm_tasks.control_flow.noop:Noop
    name: Noop
