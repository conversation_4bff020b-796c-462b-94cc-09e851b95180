taskOverrides:
  all_environments:
    - name: S3CopyController
      params:
        value: S3CopyFile
    - name: S3CopyFile
      params:
        s3_target_folder: "aries/ingress/nonstreamed/evented/order_blotter/"
    - name: TradingDateTime
      params:
        timezone_info: "US/Eastern"
    # Override strike price type and strike price currency if they are null/empty
    - name: OverrideStrikePriceCurrencyAndType
      params:
        override_strike_price_currency_and_type: true
        source_strike_price_type_column: __fb_ext_strikePriceType__
        source_strike_price_currency_column: __fb_ext_strikePriceCurrency__
    # Disable the 'normal' strike price type override (which uses a static value MntryVal)
    # and only overrides for FX instruments
    - name: InstrumentOverridesStrikePriceType
      params:
        override_strike_price_type: false
    # Overriding the following columns to null so that the override does not work
    - name: InstrumentFullNameOverride
      params:
        source_attribute: __null_col__
    - name: InstrumentClassificationOverride
      params:
        source_attribute: __null_col__
