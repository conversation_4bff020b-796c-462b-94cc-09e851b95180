id: tr-workflow-unavista-submit-report
name: TR Workflow Unavista Submit Report
platform: true
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: unavista-sftp
  type: SFTP
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
tasks:
- path: swarm_tasks.transform.steeleye.tr.unavista.submit_report.submit_driver:SubmitDriver
  name: SubmitDriver
  params:
    chunksize: 100000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
  - taskName: SubmitDriver
    mapped: false
    key: audit_result
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.write.sftp_uploader:SftpUploader
  name: SftpUploader
  resources:
    sftp_client_key: unavista-sftp
  params:
    sftp_file_path: To_LSEG
  upstreamTasks:
  - taskName: S3OrLocalFile
    mapped: false
    key: result
- path: swarm_tasks.transform.steeleye.tr.unavista.submit_report.link_transaction:LinkTransaction
  name: LinkTransaction
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: SftpUploader
    mapped: false
    key: uploader_result
  - taskName: SubmitDriver
    mapped: true
    key: result
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: update
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: LinkTransaction
    mapped: true
    key: transform_result
  - taskName: SubmitDriver
    mapped: true
    key: producer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: ElasticBulkWriter
  params:
    action_type: update
    payload_size: 10000000
    refresh: "true"
    model_index_to_flush: "RTS22Transaction"
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: ElasticBulkTransformer
    mapped: true
    key: result
- path: swarm_tasks.io.read.poll_elastic_records:PollElasticRecords
  name: PollUpdatedTransactions
  params:
    model_name: "RTS22Transaction"
    columns_to_use:
      - "&id"
      - "workflow.isReported"
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: LinkTransaction
      mapped: true
      key: result
    - taskName: ElasticBulkWriter
      mapped: true
      key: writer_result
- path: swarm_tasks.transform.steeleye.tr.unavista.submit_report.update_report:UpdateReport
  name: UpdateReport
  params:
    email_from: <EMAIL>
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: SubmitDriver
    mapped: false
    key: result
  - taskName: ElasticBulkWriter
    mapped: false
  - taskName: PollUpdatedTransactions
    mapped: false
