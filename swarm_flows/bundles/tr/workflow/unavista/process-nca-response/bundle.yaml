id: tr-workflow-unavista-process-nca-response
name: TR Workflow Unavista Process NCA Response
platform: true
infra:
- name: tenant-data
  type: ELASTICSEARCH
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
tasks:
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.transform.steeleye.tr.unavista.response.response_handler:UnavistaResponseHandler
  name: UnavistaResponseHandler
  params:
    chunksize: 1000
    response_type: NCA
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: S3OrLocalFile
    mapped: false
    key: extractor_result
- path: swarm_tasks.io.read.batch_producer:BatchProducer
  name: BatchProducer
  upstreamTasks:
  - taskName: UnavistaResponseHandler
    mapped: true
    key: file_splitter_result
- path: swarm_tasks.transform.steeleye.tr.unavista.response.link_transaction_response:LinkTransactionResponse
  name: LinkTransactionResponse
  params:
    response_type: NCA
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: BatchProducer
    mapped: true
    key: result
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: update
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: LinkTransactionResponse
    mapped: true
    key: transform_result
  - taskName: BatchProducer
    mapped: true
    key: producer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: ElasticBulkWriter
  params:
    action_type: update
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: ElasticBulkTransformer
    mapped: true
    key: result
- path: swarm_tasks.transform.steeleye.tr.unavista.response.update_nca_producer:UpdateNcaProducer
  name: UpdateNcaProducer
  params:
    chunksize: 1000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: ElasticBulkWriter
    mapped: false
    key: results
- path: swarm_tasks.transform.steeleye.tr.unavista.response.link_nca_response:LinkNcaResponse
  name: LinkNcaResponse
  upstreamTasks:
  - taskName: UpdateNcaProducer
    mapped: true
    key: result
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: AuxillaryTransformer
  params:
    action_type: update
    allow_empty: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: LinkNcaResponse
    mapped: true
    key: transform_result
  - taskName: UpdateNcaProducer
    mapped: true
    key: producer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: AuxillaryWriter
  params:
    action_type: update
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: AuxillaryTransformer
    mapped: true
    key: result
