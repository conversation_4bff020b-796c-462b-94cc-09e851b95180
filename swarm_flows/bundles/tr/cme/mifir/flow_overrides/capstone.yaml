taskOverrides:
  all_environments:
    # Always use instrument fallback IF INSTRUMENTCLASSIFICATION is one of the values in the list and
    # INSTRUMENTIDENTIFIC<PERSON>IONCODE is null. If INSTRUMENTCLASSIFICATION is NOT one of the values in the
    # list or if INSTRUMENTIDENT<PERSON>ICATIONCODE is not null, use the instrument from SRP
    - name: SetCFIInstrumentClassificationNull
      params:
        target_attribute: marketIdentifiers.instrument
        cases:
          - query: "(~`INSTRUMENTCLASSIFICATION`.isin(['OPEICX', 'OCEICX', 'OCEFCX', 'OPEFCX', 'OPASPX', 'OCAFPX', 'OPAFPX', 'OCASPX', 'OCASPS', 'OCESPS', 'OPASPS', 'OPESPS', 'OCAICS', 'OCEICS', 'OPAICS', 'OPEICS', 'OCAFPS', 'OCAFCS', 'OCEFPS', 'OCEFCS', 'OPAFPS', 'OPAFCS', 'OPEFPS', 'OPEFCS'])|(`INSTRUMENTIDENTIFICATIONCODE`.notnull()))"
            attribute: __instrument__
    - name: VenueOverride
      params:
        override_venue: true
    - name: InstrumentFallback
      params:
        instrument_classification_regex: ^ES[a-zA-Z0-9_]*$
