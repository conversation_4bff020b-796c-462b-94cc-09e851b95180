# Bundle path -> tr/feed/lme/controller
# Confluence page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2709159950/RTS22+LME
# Docs: docs/bundles/tr/feed/tr-feed-lme-controller.md
id: tr-feed-lme-controller
name: TR Feed LME Controller
infra:
  - name: tenant-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process
controlFlows:
  - name: InputFileOverride
    conditionTaskName: FileControllerCondition
    merge: false
    cases:
      S3DownloadFile: S3DownloadFile
      MultipleFilesController: MultipleFilesController
tasks:
  - path: swarm_tasks.generic.value_proxy:ValueProxy
    name: FileControllerCondition
    params:
      value: MultipleFilesController
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: result

  # ----------------------- Multiple File path -----------------------
  - path: swarm_tasks.control_flow.multiple_files_input_controller:MultipleFilesInputController
    name: MultipleFilesController
    params:
      list_of_files_regex:
        - "(EODM)"
        - "(additional_details|ADDITIONAL_DETAILS)"
      unique_identifier_regex:
        - "\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])" # i.e "20220226 - YYYYMMDD"
      file_links_in_output: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url

  # Pre Process data by reading all the files present in S3 for the flow run
  - path: swarm_tasks.io.read.aws.s3_download_multiple_files:S3DownloadMultipleFiles
    name: PreProcessData
    params:
      file_columns:
        - s3_eodm_file_url
        - s3_additional_details_file_url
      suffix_identifier_regex: s3_(.*)_file_url
    upstreamTasks:
      - taskName: MultipleFilesController
        mapped: false
        key: producer_result

  # Merge the allocations and executions data
  - path: swarm_tasks.steeleye.tr.feed.lme.controller.transaction_record_transformer:LmeTransactionFrameTransformer
    name: FrameTransformer
    upstreamTasks:
      - taskName: PreProcessData
        mapped: false
        key: pre_process_result
      - taskName: MultipleFilesController
        mapped: false
        key: s3_file_url_df

  # Chunk into batches
  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvFileSplitterMultiple
    params:
      chunksize: 20000
      detect_encoding: true
    upstreamTasks:
      - taskName: FrameTransformer
        mapped: false
        key: extractor_result

  # Create config to upload to S3
  - path: swarm_tasks.transform.extract_path.s3_file_list_from_frame_splitter_result_list:S3FileListFileSplitterResultList
    name: S3FileListFileSplitterResultListMultiple
    params:
      datetime_field_in_file_path: 1
      cloud_key_prefix: "flows/tr-feed-lme-processor"
    upstreamTasks:
      - taskName: CsvFileSplitterMultiple
        mapped: false
        key: file_splitter_result_list

  # Upload to S3
  - path: swarm_tasks.io.write.aws.s3_upload_file:S3UploadFile
    name: S3UploadFileMultiple
    upstreamTasks:
      - taskName: S3FileListFileSplitterResultListMultiple
        mapped: true
        key: upload_target

  # ----------------------- Single File path -----------------------
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url

  # Chunk into batches
  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvFileSplitterSingle
    params:
      chunksize: 20000
      detect_encoding: true
      include_file_name_in_batches: true
    upstreamTasks:
      - taskName: S3DownloadFile
        mapped: false
        key: extractor_result

  # Create config to upload to S3
  - path: swarm_tasks.transform.extract_path.s3_file_list_from_frame_splitter_result_list:S3FileListFileSplitterResultList
    name: S3FileListFileSplitterResultListSingle
    params:
      datetime_field_in_file_path: 1
      cloud_key_prefix: "flows/tr-feed-lme-processor"
    upstreamTasks:
      - taskName: CsvFileSplitterSingle
        mapped: false
        key: file_splitter_result_list

  # Upload to S3
  - path: swarm_tasks.io.write.aws.s3_upload_file:S3UploadFile
    name: S3UploadFileSingle
    upstreamTasks:
      - taskName: S3FileListFileSplitterResultListSingle
        mapped: true
        key: upload_target