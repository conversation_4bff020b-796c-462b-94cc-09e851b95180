id: tr-feed-pdm-trades
name: TR Feed PDM Trades
infra:
  - name: tenant-data
    type: ELASTICSEARCH
  - name: reference-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process
controlFlows:
  - name: S3OrLocalFile
    conditionTaskName: ParametersFlowController
    trueTaskName: S3DownloadFile
    falseTaskName: LocalFile
    merge: true
  - name: NeedsQuarantine
    conditionTaskName: QuarantineCondition
    trueTaskName: QuarantinedElasticBulkTransformer
    falseTaskName: Noop
    mapped: true
    merge: false
tasks:
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvFileSplitter
    params:
      chunksize: 10000
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result
  - path: swarm_tasks.io.read.batch_producer:BatchProducer
    name: BatchProducer
    params:
      source_schema:
        Asset_Name: string
        Asset_Rate1: float
        Trade_SettleDate: string
        Position_ID: string
        TotalAmount: float
        Trade_ID: string
        Trade_ParAmount: float
        Trade_TradeGroup_ID: string
        Issuer_Name: string
        TradeTypeDescription: string
        Trade_Price: float
        Trader_Notes: string
        CounterBank_AbbrevName: string
        Asset_MaturityDate: string
        Asset_SecurityID: string
        CurrencyType_Identifier: string
        Trade_mifirtimestamp: string
        TradeGroup_mifirtimestamp: string
    upstreamTasks:
      - taskName: CsvFileSplitter
        mapped: true
        key: file_splitter_result
  - path: swarm_tasks.steeleye.tr.data_source.pdm_trades.pre_process_pdm_data:PreProcessPDMData
    name: PreProcessPDMData
    params:
      group_by_columns:
        - Trade_TradeGroup_ID
      group_by_aggregate_dict:
        Trade_SettleDate: first
        Position_ID: first
        TotalAmount: sum
        Trade_ID: first
        Trade_ParAmount: sum
        Trade_TradeGroup_ID: first
        Issuer_Name: first
        TradeTypeDescription: first
        Trade_Price: first
        Trader_Notes: first
        CounterBank_AbbrevName: first
        Asset_MaturityDate: first
        Asset_SecurityID: first
        CurrencyType_Identifier: first
        CounterBank_EntityId: first
        Trade_mifirtimestamp: first
        TradeGroup_mifirtimestamp: first
        Asset_Name: first
        Asset_Rate1: first
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.generic.currency.convert_minor_to_major:ConvertMinorToMajor
    name: ConvertMinorToMajor
    paramsList:
      - source_ccy_attribute: CurrencyType_Identifier
        target_ccy_attribute: transactionDetails.priceCurrency
      - source_ccy_attribute: CurrencyType_Identifier
        target_ccy_attribute: transactionDetails.quantityCurrency
      - source_price_attribute: Trade_Price
        source_ccy_attribute: CurrencyType_Identifier
        target_price_attribute: transactionDetails.price
        cast_to: abs
    upstreamTasks:
      - taskName: PreProcessPDMData
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_static:MapStatic
    name: MapStatic
    paramsList:
      - target_attribute: dataSourceName
        target_value: Permira Debt Trades
      - target_attribute: sourceIndex
        from_index: true
      - target_attribute: sourceKey
        from_env_var: SWARM_FILE_URL
      - target_value: NEWT
        target_attribute: reportDetails.reportStatus
      - target_attribute: __meta_model__
        target_value: RTS22Transaction
      - target_attribute: __true__
        target_value: true
      - target_attribute: __false__
        target_value: false
      - target_attribute: transactionDetails.tradingCapacity
        target_value: AOTC
      - target_attribute: transactionDetails.priceNotation
        target_value: PERC
      - target_attribute: transactionDetails.quantityNotation
        target_value: NOML
      - target_attribute: transmissionDetails.orderTransmissionIndicator
        target_value: false
      - target_attribute: tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator
        target_value: false
      - target_attribute: __execution_within_firm_id__
        target_value: "clnt:nore"
      - target_attribute: __asset_class__
        target_value: bond
      - target_attribute: __ID__
        target_value: ID
      - target_attribute: __PERC__
        target_value: PERC
      - target_attribute: __NOML__
        target_value: NOML
      - target_attribute: __DXXXXX__
        target_value: DXXXXX
      - target_attribute: __Issuer_Name__
        target_value: "Issuer Name: "
    upstreamTasks:
      - taskName: PreProcessPDMData
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_attribute:MapAttribute
    name: MapAttribute
    paramsList:
      - source_attribute: TotalAmount
        target_attribute: transactionDetails.netAmount
      - source_attribute: Trade_TradeGroup_ID
        target_attribute: reportDetails.transactionRefNo
      - source_attribute: Trade_ParAmount
        target_attribute: transactionDetails.quantity
      - source_attribute: Position_ID
        target_attribute: transactionDetails.positionId
      - source_attribute: CurrencyType_Identifier
        target_attribute: transactionDetails.settlementAmountCurrency
      - source_attribute: __settlement_date__
        target_attribute: transactionDetails.settlementDate
      # parties
      - source_attribute: Trader_Notes
        target_attribute: __trader_id__
        prefix: "id:"
      - source_attribute: CounterBank_AbbrevName
        target_attribute: __counterparty_id__
        prefix: "lei:"
    upstreamTasks:
      - taskName: PreProcessPDMData
        mapped: true
        key: result
      - taskName: ConvertDatetime
        mapped: true
        key: convert_date_time
  - path: swarm_tasks.transform.map.map_value:MapValue
    name: MapValue
    paramsList:
      - source_attribute: TradeTypeDescription
        target_attribute: transactionDetails.buySellIndicator
        case_insensitive: true
        value_map:
          buy: BUYI
          b: BUYI
          cover: BUYI
          buy to cover: BUYI
          buytocover: BUYI
          sell: SELL
          s: SELL
          short: SELL
          sell short: SELL
          short sell: SELL
    upstreamTasks:
      - taskName: PreProcessPDMData
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_conditional:MapConditional
    name: MapConditional
    paramsList:
      - target_attribute: transactionDetails.venue
        cases:
          - query: "`CounterBank_EntityId`.notnull()"
            attribute: CounterBank_EntityId
          - query: "`CounterBank_EntityId`.isnull()"
            value: XOFF
      - target_attribute: transactionDetails.ultimateVenue
        cases:
          - query: "`CounterBank_EntityId`.notnull()"
            attribute: CounterBank_EntityId
          - query: "`CounterBank_EntityId`.isnull()"
            value: XOFF
      - target_attribute: __trading_datetime__
        cases:
          - query: "`Trade_mifirtimestamp`.notnull()"
            attribute: Trade_mifirtimestamp
          - query: "`Trade_mifirtimestamp`.isnull()"
            attribute: TradeGroup_mifirtimestamp
    upstreamTasks:
      - taskName: PreProcessPDMData
        mapped: true
        key: result
  - path: swarm_tasks.transform.datetime.convert_datetime:ConvertDatetime
    name: ConvertDatetime
    paramsList:
      - source_attribute: __trading_datetime__
        target_attribute: date
        convert_to: date
      - source_attribute: __trading_datetime__
        source_attribute_format: '%d/%m/%Y %H:%M:%S'
        target_attribute: transactionDetails.tradingDateTime
        convert_to: datetime
        timezone_info: Europe/London
      - source_attribute: Asset_MaturityDate
        target_attribute: __fb_bond_maturity_date__
        convert_to: date
      - source_attribute: Trade_SettleDate
        source_attribute_format: '%d/%m/%Y'
        target_attribute: __settlement_date__
        convert_to: date
    upstreamTasks:
      - taskName: PreProcessPDMData
        mapped: true
        key: result
      - taskName: MapConditional
        mapped: true
        key: map_conditional
  - path: swarm_tasks.transform.concat.concat_attributes:ConcatAttributes
    name: ConcatAttributes
    paramsList:
      - source_attributes:
          - __Issuer_Name__
          - Issuer_Name
        target_attribute: transactionDetails.outgoingOrderAddlInfo
    upstreamTasks:
      - taskName: PreProcessPDMData
        mapped: true
        key: result
      - taskName: MapStatic
        mapped: true
        key: map_static
  - path: swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers:InstrumentIdentifiers
    name: InstrumentIdentifiers
    params:
      asset_class_attribute: __asset_class__
      currency_attribute: transactionDetails.priceCurrency
      expiry_date_attribute: __fb_bond_maturity_date__
      isin_attribute: Asset_SecurityID
      venue_attribute: transactionDetails.venue
      retain_task_inputs: true
    upstreamTasks:
      - taskName: PreProcessPDMData
        mapped: true
        key: result
      - taskName: MapStatic
        mapped: true
        key: map_static
      - taskName: ConvertMinorToMajor
        mapped: true
        key: convert_to_major
      - taskName: MapConditional
        mapped: true
        key: map_conditional
      - taskName: ConvertDatetime
        mapped: true
        key: convert_date_time
  # Get Tenant LEI
  - path: swarm_tasks.steeleye.generic.get_tenant_lei:GetTenantLEI
    name: GetTenantLEI
    params:
      target_lei_column: __executing_entity_with_lei__
      target_column_prefix: "lei:"
    upstreamTasks:
      - taskName: PreProcessPDMData
        mapped: true
        key: result
  - path: swarm_tasks.transform.steeleye.tr.identifiers.party_identifiers:PartyIdentifiers
    name: PartyIdentifiers
    params:
      target_attribute: marketIdentifiers.parties
      buy_sell_side_attribute: transactionDetails.buySellIndicator
      use_buy_mask_for_buyer_seller: true
      buyer_identifier: __executing_entity_with_lei__
      seller_identifier: __counterparty_id__
      trader_identifier: __trader_id__
      counterparty_identifier: __counterparty_id__
      executing_entity_identifier: __executing_entity_with_lei__
      execution_within_firm_identifier: __execution_within_firm_id__
    upstreamTasks:
      - taskName: PreProcessPDMData
        mapped: true
        key: result
      - taskName: MapAttribute
        mapped: true
        key: map_attribute
      - taskName: MapValue
        mapped: true
        key: map_value
      - taskName: MapStatic
        mapped: true
        key: map_static
      - taskName: GetTenantLEI
        mapped: true
        key: get_tenant_lei
  - path: swarm_tasks.transform.steeleye.link.merge_market_identifiers:MergeMarketIdentifiers
    name: MergeMarketIdentifiers
    params:
      identifiers_path: marketIdentifiers
      instrument_path: marketIdentifiers.instrument
      parties_path: marketIdentifiers.parties
    upstreamTasks:
      - taskName: InstrumentIdentifiers
        mapped: true
        key: result
      - taskName: PartyIdentifiers
        mapped: true
        key: party_identifiers
  # Primary Frame Concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PrimaryFrameConcatenator
    params:
      orient: horizontal
    upstreamTasks:
      - taskName: ConvertDatetime
        mapped: true
        key: convert_date_time
      - taskName: ConvertMinorToMajor
        mapped: true
        key: convert_minor_to_major
      - taskName: InstrumentIdentifiers
        mapped: true
        key: instrument_identifiers
      - taskName: MapAttribute
        mapped: true
        key: map_attribute
      - taskName: MapStatic
        mapped: true
        key: map_static
      - taskName: MapValue
        mapped: true
        key: map_value
      - taskName: MapConditional
        mapped: true
        key: map_conditional
      - taskName: MergeMarketIdentifiers
        mapped: true
        key: merge_market_identifiers
      - taskName: PartyIdentifiers
        mapped: true
        key: party_identifiers
      - taskName: ConcatAttributes
        mapped: true
        key: concat_attributes
  - path: swarm_tasks.transform.steeleye.link.parties:LinkParties
    name: LinkParties
    params:
      identifiers_path: marketIdentifiers.parties
      add_investment_firm_covered_directive: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.steeleye.tr.mifir.eligibility_assessor:EligibilityAssessor
    name: EligibilityAssessor
    resources:
      es_client_key: tenant-data
      srp_client_key: reference-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
      - taskName: LinkInstrument
        mapped: true
        key: link_instrument
  - path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
    name: LinkInstrument
    params:
      identifiers_path: marketIdentifiers.instrument
      asset_class_attribute: __asset_class__
      currency_attribute: transactionDetails.priceCurrency
      venue_attribute: transactionDetails.venue
    resources:
      ref_data_key: reference-data
      tenant_data_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
    name: InstrumentFallback
    params:
      instrument_fields_map:
        - source_field: __fb_bond_maturity_date__
          target_field: bond.maturityDate
        - source_field: __ID__
          target_field: ext.instrumentIdCodeType
        - source_field: __PERC__
          target_field: ext.priceNotation
        - source_field: __NOML__
          target_field: ext.quantityNotation
        - source_field: __DXXXXX__
          target_field: instrumentClassification
        - source_field: Asset_Name
          target_field: instrumentFullName
        - source_field: __true__
          target_field: isCreatedThroughFallback
        - source_field: Asset_Name
          target_field: venue.financialInstrumentShortName
        - source_field: Asset_Rate1
          target_field: bond.fixedRate
        - source_field: __true__
          target_field: ext.mifirEligible
        - source_field: __true__
          target_field: ext.venueInEEA
        - source_field: __false__
          target_field: ext.onFIRDS
      populate_workflow_fields: true
      workflow_fields_map:
        - source_field: __false__
          target_field: eligible
        - source_field: __true__
          target_field: totv
      workflow_status: NON_REPORTABLE
      str_to_bool_dict:
        "true": True
        "y": True
        "yes": True
        "t": True
        "on": True
        "false": False
        "n": False
        "no": False
        "f": False
        "off": False
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: primary_frame
      - taskName: PreProcessPDMData
        mapped: true
        key: result
      - taskName: EligibilityAssessor
        mapped: true
        key: eligibility_assessor
      - taskName: LinkInstrument
        mapped: true
        key: link_instrument
  # Auxiliary Frame Concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: AuxiliaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __asset_class__
        - marketIdentifiers.instrument
        - marketIdentifiers.parties
        - __fb_bond_maturity_date__
        - __ID__
        - __PERC__
        - __NOML__
        - __true__
        - __false__
        - __DXXXXX__
        - __counterparty_id__
        - __execution_within_firm_id__
        - __trader_id__
        - __trading_datetime__
        - __Issuer_Name__
        - __settlement_date__
        - asset_class_attribute # Instrument Identifiers columns
        - bbg_figi_id_attribute
        - currency_attribute
        - eurex_id_attribute
        - exchange_symbol_attribute
        - expiry_date_attribute
        - interest_rate_start_date_attribute
        - isin_attribute
        - notional_currency_1_attribute
        - notional_currency_2_attribute
        - option_strike_price_attribute
        - option_type_attribute
        - swap_near_leg_date_attribute
        - underlying_index_name_attribute
        - underlying_index_name_leg_2_attribute
        - underlying_index_series_attribute
        - underlying_index_term_attribute
        - underlying_index_term_value_attribute
        - underlying_index_version_attribute
        - underlying_isin_attribute
        - underlying_symbol_attribute
        - underlying_symbol_expiry_code_attribute
        - underlying_index_term_leg_2_attribute
        - underlying_index_term_value_leg_2_attribute
        - venue_attribute
        - venue_financial_instrument_short_name_attribute
        - instrument_classification_attribute
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: primary_frame_concatenator
      - taskName: InstrumentFallback
        mapped: true
        key: instrument_fallback
      - taskName: LinkParties
        mapped: true
        key: link_parties
  - path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
    name: Meta
    params:
      model_attribute: __meta_model__
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: AuxiliaryFrameConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_conditional:MapConditional
    name: WorkflowStatus
    params:
      source_attribute: '&validationErrors'
      target_attribute: workflow.validationStatus
      condition: isnull
      true_value: PASSED
      false_value: FAILED
    upstreamTasks:
      - taskName: Meta
        mapped: true
        key: result
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: FinalFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __meta_model__
    upstreamTasks:
      - taskName: AuxiliaryFrameConcatenator
        mapped: true
        key: result
      - taskName: Meta
        mapped: true
        key: meta_result
      - taskName: WorkflowStatus
        mapped: true
        key: workflow_status_result
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformer
    params:
      action_type: create
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: FinalFrameConcatenator
        mapped: true
        key: transform_result
      - taskName: PreProcessPDMData
        mapped: true
        key: producer_result
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: PutIfAbsent
    params:
      payload_size: 10000000
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: ElasticBulkTransformer
        mapped: true
        key: result
  # quarantine
  - path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
    name: QuarantineCondition
    upstreamTasks:
      - taskName: PutIfAbsent
        mapped: true
        key: bulk_writer_result
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: QuarantinedElasticBulkTransformer
    params:
      action_type: create
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: FinalFrameConcatenator
        mapped: true
        key: transform_result
      - taskName: PreProcessPDMData
        mapped: true
        key: producer_result
      - taskName: PutIfAbsent
        mapped: true
        key: bulk_writer_result
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: QuarantinedPutIfAbsent
    params:
      payload_size: 10000000
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: QuarantinedElasticBulkTransformer
        mapped: true
        key: result
  - path: swarm_tasks.control_flow.noop:Noop
    name: Noop
