id: tr-feed-thornbridge-interactive-brokers

name: TR Feed Thornbridge Interactive Brokers

infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH

audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data

parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process

controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  mapped: true
  merge: false
- name: S3CopyOverride
  conditionTaskName: S3CopyController
  mapped: false
  merge: true
  cases:
    Noop: Noop # Case specific
    S3CopyFile: S3CopyFile # Default case
tasks:
  # Copy Controller
- path: swarm_tasks.generic.value_proxy:ValueProxy
  name: S3CopyController
  params:
    value: S3CopyFile
  upstreamTasks:
    - taskName: file_url
      mapped: false
      key: file_url

  # Copies file to order version of flow
- path: swarm_tasks.io.read.aws.copy.s3_to_s3_copy:S3ToS3Copy
  name: S3CopyFile
  params:
    s3_target_folder: "aries/ingress/nonstreamed/evented/trade_sink_ns/interactive_brokers_activity/"
  upstreamTasks:
    - taskName: file_url
      mapped: false
      key: file_url

  # ParametersFlowController
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
    - taskName: file_url
      mapped: false
      key: file_url

  # Process file in S3
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
    - taskName: file_url
      mapped: false
      key: file_url

  # Or process local file
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
    - taskName: file_url
      mapped: false
      key: file_url

# GPG Decryptor which decrypts PGP and GPG files and returns an ExtractPathResult containing the
# decrypted file. If the file is not a PGP/GPG file, it returns the ExtractPathResult from S3OrLocalFile.
- path: swarm_tasks.io.read.gpg_decryptor:GpgDecryptor
  name: GpgDecryptor
  params:
    num_private_key_params: 1
  upstreamTasks:
    - taskName: S3OrLocalFile
      mapped: false
      key: extractor_result

  # Read CSV file in chunks
- path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
  name: CsvFileSplitter
  params:
    chunksize: 25000
  upstreamTasks:
    - taskName: GpgDecryptor
      mapped: false
      key: extractor_result

  # Produce chunk batches for downstream tasks
- path: swarm_tasks.io.read.batch_producer:BatchProducer
  name: BatchProducer
  params:
    source_schema:
      "AccountID": string
      "AssetType": string
      "AwayBrokerCommission": string
      "BaseCurrency": string
      "BBGlobalID": string
      "BBTicker": string
      "BBTickerAndExchangeCode": string
      "ClientReference": string
      "ClrFirmID": string
      "Commission": string
      "ConID": string
      "ContraPartyName": string
      "CostBasis": string
      "Currency": string
      "CUSIP": string
      "DeliveryMonth": string
      "Description": string
      "Exchange": string
      "ExecutionID": string
      "ExpirationDate": string
      "Flag": string
      "FxRateToBase": string
      "GrossAmount": string
      "IssueDate": string
      "Issuer": string
      "MasterAccountID": string
      "MaturityDate": string
      "Multiplier": string
      "Net": string
      "NetInBase": string
      "OptionStrike": string
      "OptionType": string
      "OrderID": string
      "OrderTime": string
      "PrimaryExchange": string
      "Quantity": float
      "SECFee": string
      "SecurityDescription": string
      "SecurityID": string
      "SettleDate": string
      "SubCategory": string
      "Symbol": string
      "Tax": string
      "TaxBasisElection": string
      "TradeDate": string
      "TradeID": string
      "TradeTime": string
      "TransactionID": string
      "TransactionType": string
      "Type": string
      "UnderlyingCategory": string
      "UnderlyingConID": string
      "UnderlyingPrimaryExchange": string
      "UnderlyingSecurityId": string
      "UnderlyingSymbol": string
      "UnitPrice": float
      "Van": string
  upstreamTasks:
    - taskName: CsvFileSplitter
      mapped: true
      key: file_splitter_result

  # Exclude records that do not have TransactionType as one of ["BUY", "SELL", "CA", "COVER", "SHORT"]
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: GetRowsByCondition
  params:
    query: "`TransactionType`.astype('str').str.upper().str.fullmatch('(BUY|SELL|CA|COVER|SHORT)',case=False,na=False)"
    skip_on_empty: true
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result

  # Tenant Input for PrimaryTransformations
- path: swarm_tasks.primary_transformations.input_tasks.tenant_input:TenantInput
  name: TenantInput

  # ES Client Input for PrimaryTransformations
- path: swarm_tasks.primary_transformations.input_tasks.es_client_input:EsClientInput
  name: EsClientInput
  resources:
    es_client_key: tenant-data

  # Primary Transformations
- path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
  name: PrimaryTransformations
  upstreamTasks:
    - taskName: GetRowsByCondition
      mapped: true
      key: result
    - taskName: file_url
      mapped: false
      key: source_file_uri
    - taskName: TenantInput
      mapped: false
      key: tenant
    - taskName: EsClientInput
      mapped: false
      key: es_client

  # Link Parties
- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkParties
  params:
    identifiers_path: marketIdentifiers.parties
    add_investment_firm_covered_directive: True
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: result

  # Link Instrument
- path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
  name: LinkInstrument
  params:
    identifiers_path: marketIdentifiers.instrument
    currency_attribute: transactionDetails.quantityCurrency
    venue_attribute: transactionDetails.venue
    instrument_id_fields:
      - "ext.additionalIdentifiers.compositeFigi"
      - "ext.additionalIdentifiers.figi"
      - "ext.aii.daily"
      - "ext.aii.dailyWithoutStrike"
      - "ext.aii.withoutStrike"
      - "ext.alternativeInstrumentIdentifier"
      - "ext.compositeFigi"
      - "ext.figi"
      - "ext.instrumentUniqueIdentifier"
      - "instrumentIdCode"
  resources:
    ref_data_key: reference-data
    tenant_data_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: result

  # Instrument Fallback
- path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
  name: InstrumentFallback
  params:
    instrument_fields_map:
      - source_field: __instr_fb_is_created_through_fb__
        target_field: isCreatedThroughFallback
      - source_field: isin_attribute
        target_field: instrumentIdCode
      - source_field: currency_attribute
        target_field: notionalCurrency1
    str_to_bool_dict:
      "true": True
      "false": False
  upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: result
    - taskName: LinkInstrument
      mapped: true
      key: link_instrument

  # MapFromNested
- path: swarm_tasks.transform.map.map_from_nested:MapFromNested
  name: MapNestedFromInstrumentDirectMapping
  paramsList:
    - source_attribute: instrumentDetails.instrument
      nested_path: fxDerivatives.notionalCurrency2
      target_attribute: transactionDetails.quantityCurrency
  upstreamTasks:
    - taskName: LinkInstrument
      mapped: true
      key: result

  # MapFromNested
- path: swarm_tasks.transform.map.map_from_nested:MapFromNested
  name: MapNestedFromInstrument
  paramsList:
    - source_attribute: instrumentDetails.instrument
      nested_path: ext.priceNotation
      target_attribute: __price_notation__
    - source_attribute: instrumentDetails.instrument
      nested_path: ext.quantityNotation
      target_attribute: __quantity_notation__
    - source_attribute: instrumentDetails.instrument
      nested_path: notionalCurrency1
      target_attribute: __price_currency_fallback__
    - source_attribute: instrumentDetails.instrument
      nested_path: instrumentIdCode
      target_attribute: __instrument_id_code__
    - source_attribute: instrumentDetails.instrument
      nested_path: instrumentClassification
      target_attribute: __instrument_classification__
    - source_attribute: instrumentDetails.instrument
      nested_path: commoditiesOrEmissionAllowanceDerivativeInd
      target_attribute: __commodities_or_emission_allowance_derivative_ind__
  upstreamTasks:
    - taskName: LinkInstrument
      mapped: true
      key: result

  # Map tradersAlgosWaiversIndicators.commodityDerivativeIndicator from __instrument_classification__
- path: swarm_tasks.transform.map.map_value:MapValue
  name: MapCommodityDerivativeIndicator
  paramsList:
  - source_attribute: __instrument_classification__
    target_attribute: __commodity_derivative_indicator__
    case_insensitive: true
    regex_replace_map:
      - regex: "^JT[A-Z]{2}C[A-Z]{1}$"
        replace_value: F
      - regex: "^JT[A-Z]{2}F[A-Z]{1}$"
        replace_value: F
      - regex: "^FC[A-Z]{4}$"
        replace_value: F
      - regex: "^HT[A-Z]{4}$"
        replace_value: F
      - regex: "^O[A-Z]{2}T[A-Z]{2}$"
        replace_value: F
      - regex: "^ST[A-Z]{1}T[A-Z]{2}$"
        replace_value: F
      - regex: "^ST[A-Z]{1}C[A-Z]{2}$"
        replace_value: F
    value_map:
      F: false
  upstreamTasks:
  - taskName: MapNestedFromInstrument
    mapped: true
    key: result

  # Check if Instrument Code is in Exempt Shares
- path: swarm_tasks.order.generic.exempt_shares:ExemptShares
  name: ExemptShares
  paramsList:
    - source_attribute: __instrument_id_code__
      target_attribute: __instrument_id_code_in_list__
  upstreamTasks:
    - taskName: MapNestedFromInstrument
      mapped: true
      key: result

  # Map missing currencies to linked instruments' currency
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: MapConditional
  paramsList:
    - target_attribute: tradersAlgosWaiversIndicators.commodityDerivativeIndicator
      cases:
        - query: "~`__commodity_derivative_indicator__` | `__commodities_or_emission_allowance_derivative_ind__`"
          value: false
    - target_attribute: transactionDetails.priceCurrency
      cases:
        - query: "`__price_currency__`.notnull()"
          attribute: __price_currency__
        - query: "`__price_currency__`.isnull()"
          attribute: __price_currency_fallback__
    - target_attribute: transactionDetails.priceNotation
      cases:
        - query: "`__price_notation__`.notnull()"
          attribute: __price_notation__
        - query: "`__price_notation__`.isnull()"
          value: "MONE"
    - target_attribute: transactionDetails.quantityNotation
      cases:
        - query: "`__quantity_notation__`.notnull()"
          attribute: __quantity_notation__
        - query: "`__quantity_notation__`.isnull()"
          value: "UNIT"
    - target_attribute: tradersAlgosWaiversIndicators.shortSellingIndicator
      cases:
        - query: "index == index"
          attribute: __short_sell_indicator__
        - query: "(`__instrument_id_code_in_list__`) & (`__short_sell_indicator__`.astype('str').str.fullmatch('SESH',case=False,na=False))"
          value: "SSEX"
        - query: "~((`__instrument_classification__`.astype('str').str.fullmatch('^[C|E|D].*',case=False,na=False)) | (`__instrument_classification__`.astype('str').str.fullmatch('^[O].{2}[B|D|S].*',case=False,na=False)))"
          as_empty: true
  upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: result
    - taskName: MapNestedFromInstrument
      mapped: true
      key: map_neste_conditional
    - taskName: MapCommodityDerivativeIndicator
      mapped: true
      key: commodity_derivative
    - taskName: ExemptShares
      mapped: true
      key: exempt_shares

  # Eligibility Assessor
- path: swarm_tasks.transform.steeleye.tr.mifir.eligibility_assessor:EligibilityAssessor
  name: EligibilityAssessor
  resources:
    es_client_key: tenant-data
    srp_client_key: reference-data
  upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: result
    - taskName: InstrumentFallback
      mapped: true
      key: instrument_fallback

  # Frame Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: AuxiliaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __instr_fb_is_created_through_fb__
      - marketIdentifiers.instrument
      - marketIdentifiers.parties
      - __price_currency__
      - __short_sell_indicator__
      - asset_class_attribute # Instrument Identifiers columns
      - bbg_figi_id_attribute
      - currency_attribute
      - eurex_id_attribute
      - exchange_symbol_attribute
      - expiry_date_attribute
      - interest_rate_start_date_attribute
      - isin_attribute
      - notional_currency_1_attribute
      - notional_currency_2_attribute
      - option_strike_price_attribute
      - option_type_attribute
      - swap_near_leg_date_attribute
      - underlying_index_name_attribute
      - underlying_index_name_leg_2_attribute
      - underlying_index_series_attribute
      - underlying_index_term_attribute
      - underlying_index_term_value_attribute
      - underlying_index_version_attribute
      - underlying_isin_attribute
      - underlying_symbol_attribute
      - underlying_symbol_expiry_code_attribute
      - underlying_index_term_leg_2_attribute
      - underlying_index_term_value_leg_2_attribute
      - venue_attribute
      - venue_financial_instrument_short_name_attribute
      - instrument_classification_attribute
  upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: result
    - taskName: InstrumentFallback
      mapped: true
      key: instrument_fallback
    - taskName: LinkParties
      mapped: true
      key: parties
    - taskName: MapNestedFromInstrumentDirectMapping
      mapped: true
      key: map_nested_direct
    - taskName: MapConditional
      mapped: true
      key: map_conditional
    - taskName: EligibilityAssessor
      mapped: true
      key: eligibility_assessor

  # Assign Meta
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: AssignMeta
  params:
    model_attribute: __meta_model__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: AuxiliaryFrameConcatenator
      mapped: true
      key: result

  # Workflow Status
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: WorkflowStatus
  params:
    source_attribute: '&validationErrors'
    target_attribute: workflow.validationStatus
    condition: isnull
    true_value: PASSED
    false_value: FAILED
  upstreamTasks:
    - taskName: AssignMeta
      mapped: true
      key: result

  # Final Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: FinalConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
  upstreamTasks:
    - taskName: AssignMeta
      mapped: true
      key: result
    - taskName: AuxiliaryFrameConcatenator
      mapped: true
      key: aux_frame_concatenator
    - taskName: WorkflowStatus
      mapped: true
      key: workflow_status_result

  # Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: FinalConcatenator
      mapped: true
      key: transform_result
    - taskName: BatchProducer
      mapped: true
      key: producer_result

  # Elastic Bulk Writer
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsent
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: ElasticBulkTransformer
      mapped: true
      key: result

  # Quarantine Condition
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineCondition
  upstreamTasks:
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result

  # Quarantined Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformer
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: FinalConcatenator
      mapped: true
      key: transform_result
    - taskName: BatchProducer
      mapped: true
      key: producer_result
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result

  # Quarantined Elastic Bulk Writer
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsent
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: QuarantinedElasticBulkTransformer
      mapped: true
      key: result

  # Noop
- path: swarm_tasks.control_flow.noop:Noop
  name: Noop
