# Bundle path -> tr/feed/unavista
# Confluence page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2703294465/RTS22+UnaVista+Handler
id: tr-feed-unavista
name: TR Feed UnaVista
infra:
  - name: tenant-data
    type: ELASTICSEARCH
  - name: reference-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process
controlFlows:
  - name: S3OrLocalFile
    conditionTaskName: ParametersFlowController
    trueTaskName: S3DownloadFile
    falseTaskName: LocalFile
    merge: true
  - name: PartiesFallback
    conditionTaskName: PartiesFallbackController
    merge: true
    mapped: true
    cases:
      UNIVERSAL: PassThroughFrameConcatenator
      APOLLO: ApolloPartiesFallback
  - name: NeedsQuarantine
    conditionTaskName: QuarantineCondition
    trueTaskName: QuarantinedElasticBulkTransformer
    falseTaskName: Noop
    mapped: true
    merge: false
tasks:
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvFileSplitter
    params:
      audit_input_rows: true
      chunksize: 5000
      detect_encoding: true
      normalise_columns: true
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result
  - path: swarm_tasks.io.read.batch_producer:BatchProducer
    name: BatchProducer
    params:
      source_schema:
        "BUYERCOUNTRYOFBRANCH": string
        "BUYERDECISIONMAKERID": string
        "BUYERID": string
        "BUYERIDTYPE": string
        "BUYERTRANSMITTERID": string
        "COMMODITYDERIVATIVEINDICATOR": string
        "COMPLEXTRADECOMPONENTID": string
        "COUNTRYOFBRANCH": string
        "DATACATEGORY": string
        "DELIVERYTYPE": string
        "DERIVATIVENOTIONALCHANGE": string
        "EXECUTINGENTITYID": string
        "EXPIRYDATE": string
        "FIRMEXECUTIONCOUNTRYOFBRANCH": string
        "FIRMEXECUTIONID": string
        "FIRMEXECUTIONIDSUBTYPE": string
        "FIRMEXECUTIONIDTYPE": string
        "INSTRUMENTCLASSIFICATION": string
        "INSTRUMENTID": string
        "INSTRUMENTIDTYPE": string
        "INSTRUMENTNAME": string
        "INTERNALCLIENTIDENTIFICATION": string
        "INVESTMENTDECISIONCOUNTRYOFBRANCH": string
        "INVESTMENTDECISIONID": string
        "INVESTMENTDECISIONIDSUBTYPE": string
        "INVESTMENTDECISIONIDTYPE": string
        "MATURITYDATE": string
        "NETAMOUNT": string
        "NOTIONALCURRENCY1": string
        "NOTIONALCURRENCY2": string
        "NOTIONALCURRENCY2TYPE": string
        "OPTIONSTYLE": string
        "OPTIONTYPE": string
        "ORDERTRANSMISSIONINDICATOR": string
        "OTCPOSTTRADEINDICATOR": string
        "PRICE": float
        "PRICECURRENCY": string
        "PRICEMULTIPLIER": string
        "PRICETYPE": string
        "QUANTITY": string
        "QUANTITYCURRENCY": string
        "QUANTITYTYPE": string
        "REPORTSTATUS": string
        "SELLERCOUNTRYOFBRANCH": string
        "SELLERDECISIONMAKERID": string
        "SELLERID": string
        "SELLERIDTYPE": string
        "SELLERTRANSMITTERID": string
        "SFTINDICATOR": string
        "SHORTSELLINGINDICATOR": string
        "STRIKEPRICE": float
        "STRIKEPRICECURRENCY": string
        "STRIKEPRICETYPE": string
        "TRADINGCAPACITY": string
        "TRADINGDATETIME": string
        "TRANSACTIONREFERENCENUMBER": string
        "UNDERLYINGINDEXID": string
        "UNDERLYINGINDEXNAME": string
        "UNDERLYINGINDEXTERM": string
        "UNDERLYINGINSTRUMENTID": string
        "UP-FRONTPAYMENT": string
        "UP-FRONTPAYMENTCURRENCY": string
        "UVINDEXCLASSIFICATION": string
        "UVINSTRUMENTCLASSIFICATION": string
        "VENUE": string
        "VENUETRANSACTIONID": string
        "WAIVERINDICATOR": string
    upstreamTasks:
      - taskName: CsvFileSplitter
        mapped: true
        flatten: true
        key: file_splitter_result

  # UnaVista Primary Transformations
  - path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
    name: UnaVistaTransformations
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result

  # Link Parties
  - path: swarm_tasks.transform.steeleye.link.parties:LinkParties
    name: LinkParties
    params:
      identifiers_path: marketIdentifiers.parties
      add_investment_firm_covered_directive: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: UnaVistaTransformations
        mapped: true
        key: result

  # Party Fallback Controller
  - path: swarm_tasks.generic.value_proxy:ValueProxy
    name: PartiesFallbackController
    params:
      value: UNIVERSAL
    upstreamTasks:
      - taskName: LinkParties
        mapped: true
        key: result

  # PassThroughFrameConcatenator (executed if PartiesFallbackController value == UNIVERSAL)
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PassThroughFrameConcatenator
    params:
      orient: horizontal
    upstreamTasks:
      - taskName: LinkParties
        mapped: true
        key: result

  # ApolloPartiesFallback (executed if PartiesFallbackController value == APOLLO)
  - path: swarm_tasks.tr.feed.unavista.apollo_parties_fallback:ApolloPartiesFallback
    name: ApolloPartiesFallback
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
      - taskName: LinkParties
        mapped: true
        key: link_parties

  # Link Instrument
  - path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
    name: LinkInstrument
    params:
      identifiers_path: marketIdentifiers.instrument
      venue_attribute: transactionDetails.venue
      currency_attribute: transactionDetails.priceCurrency
    resources:
      ref_data_key: reference-data
      tenant_data_key: tenant-data
    upstreamTasks:
      - taskName: UnaVistaTransformations
        mapped: true
        key: result
  # Instrument Fallback
  - path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
    name: InstrumentFallback
    params:
      instrument_fields_map:
        - source_field: __instrument_classification__
          target_field: instrumentClassification
        - source_field: __instrument_full_name__
          target_field: instrumentFullName
        - source_field: __is_created_through_fallback__
          target_field: isCreatedThroughFallback
        - source_field: __notional_currency_1__
          target_field: notionalCurrency1

        #bond
        - source_field: __maturity_date__
          target_field: bond.maturityDate

        # derivative
        - source_field: __delivery_type__
          target_field: derivative.deliveryType
        - source_field: __option_exercise_style__
          target_field: derivative.optionExerciseStyle
        - source_field: __price_multiplier__
          target_field: derivative.priceMultiplier
        - source_field: __strike_price_currency__
          target_field: derivative.strikePriceCurrency
        - source_field: __strike_price__
          target_field: derivative.strikePrice
        - source_field: __underlying_index_name__
          target_field: derivative.underlyingIndexName
        - source_field: __underlying_index_term__
          target_field: derivative.underlyingIndexTerm

        # ext
        - source_field: __instrument_id_code_type__
          target_field: ext.instrumentIdCodeType
        - source_field: __notional_currency_2_type__
          target_field: ext.notionalCurrency2Type
        - source_field: __strike_price_type__
          target_field: ext.strikePriceType

        # fxDerivatives
        - source_field: __notional_currency_2__
          target_field: fxDerivatives.notionalCurrency2
      underlying_instrument_fields_map:
        - source_field: __instrument_id_code__
          target_level: ext.underlyingInstruments
          target_field: instrumentIdCode
        - source_field: __instrument_id_code__
          target_level: derivative.underlyingInstruments
          target_field: underlyingInstrumentCode
        - source_field: __under_instr_underlying_index_name__
          target_level: ext.underlyingInstruments
          target_field: derivative.underlyingIndexName
        - source_field: __under_instr_underlying_index_term__
          target_level: ext.underlyingInstruments
          target_field: derivative.underlyingIndexTerm
      str_to_bool_dict:
        "on": True
        "off": False
    upstreamTasks:
      - taskName: UnaVistaTransformations
        mapped: true
        key: result
      - taskName: LinkInstrument
        mapped: true
        key: link_instrument
  # Eligibility Assessor
  - path: swarm_tasks.transform.steeleye.tr.mifir.eligibility_assessor:EligibilityAssessor
    name: EligibilityAssessor
    resources:
      es_client_key: tenant-data
      srp_client_key: reference-data
    upstreamTasks:
      - taskName: UnaVistaTransformations
        mapped: true
        key: result
      - taskName: InstrumentFallback
        mapped: true
        key: instrument_fallback

  # Auxiliary Frame Concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: AuxiliaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - marketIdentifiers.instrument
        - marketIdentifiers.parties
        - __data_category__
        - __delivery_type__
        - __is_created_through_fallback__
        - __instrument_classification__
        - __instrument_full_name__
        - __instrument_id_code__
        - __instrument_id_code_type__
        - __notional_currency_1__
        - __notional_currency_2__
        - __notional_currency_2_type__
        - __maturity_date__
        - __option_exercise_style__
        - __price_multiplier__
        - __price_notation__
        - __quantity_type__
        - __quantity_notation__
        - __strike_price__
        - __strike_price_currency__
        - __strike_price_type__
        - __underlying_index_name__
        - __underlying_index_term__
        - __under_instr_underlying_index_name__
        - __under_instr_underlying_index_term__
        - asset_class_attribute # Instrument Identifiers columns
        - bbg_figi_id_attribute
        - currency_attribute
        - eurex_id_attribute
        - exchange_symbol_attribute
        - expiry_date_attribute
        - interest_rate_start_date_attribute
        - isin_attribute
        - notional_currency_1_attribute
        - notional_currency_2_attribute
        - option_strike_price_attribute
        - option_type_attribute
        - swap_near_leg_date_attribute
        - underlying_index_name_attribute
        - underlying_index_name_leg_2_attribute
        - underlying_index_series_attribute
        - underlying_index_term_attribute
        - underlying_index_term_value_attribute
        - underlying_index_version_attribute
        - underlying_isin_attribute
        - underlying_symbol_attribute
        - underlying_symbol_expiry_code_attribute
        - underlying_index_term_leg_2_attribute
        - underlying_index_term_value_leg_2_attribute
        - venue_attribute
        - venue_financial_instrument_short_name_attribute
        - instrument_classification_attribute
    upstreamTasks:
      - taskName: UnaVistaTransformations
        mapped: true
        key: result
      - taskName: PartiesFallback
        mapped: true
        key: link_parties_or_fallback
      - taskName: EligibilityAssessor
        mapped: true
        key: eligibility_assessor
      - taskName: InstrumentFallback
        mapped: true
        key: instrument_fallback
  # Assign Meta
  - path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
    name: AssignMeta
    params:
      model_attribute: __meta_model__
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: AuxiliaryFrameConcatenator
        mapped: true
        key: result

  # Workflow Status
  - path: swarm_tasks.transform.map.map_conditional:MapConditional
    name: WorkflowStatus
    params:
      source_attribute: '&validationErrors'
      target_attribute: workflow.validationStatus
      condition: isnull
      true_value: PASSED
      false_value: FAILED
    upstreamTasks:
      - taskName: AssignMeta
        mapped: true
        key: result

  # Final Concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: FinalConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __meta_model__
    upstreamTasks:
      - taskName: AssignMeta
        mapped: true
        key: result
      - taskName: AuxiliaryFrameConcatenator
        mapped: true
        key: aux_frame_concatenator
      - taskName: WorkflowStatus
        mapped: true
        key: workflow_status_result

  # Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformer
    params:
      action_type: create
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: FinalConcatenator
        mapped: true
        key: transform_result
      - taskName: BatchProducer
        mapped: true
        key: producer_result

  # Elastic Bulk Writer
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: PutIfAbsent
    params:
      payload_size: 10000000
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: ElasticBulkTransformer
        mapped: true
        key: result

  # Quarantine Condition
  - path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
    name: QuarantineCondition
    upstreamTasks:
      - taskName: PutIfAbsent
        mapped: true
        key: bulk_writer_result

  # Quarantined Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: QuarantinedElasticBulkTransformer
    params:
      action_type: create
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: FinalConcatenator
        mapped: true
        key: transform_result
      - taskName: BatchProducer
        mapped: true
        key: producer_result
      - taskName: PutIfAbsent
        mapped: true
        key: bulk_writer_result

  # Quarantined Elastic Bulk Writer
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: QuarantinedPutIfAbsent
    params:
      payload_size: 10000000
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: QuarantinedElasticBulkTransformer
        mapped: true
        key: result
  # Noop
  - path: swarm_tasks.control_flow.noop:Noop
    name: Noop
