# Input: Csv file which contains S3 links to a batch of ICE fix files.
# All the fix files are downloaded locally, and processed together in one flow run.

# Methodology - Parse FIX message; build Pandas DataFrame with relevant data
# Map data to fit Rts22Transaction model; Follow tr-universal-steeleye-trade-blotter pipeline
# Output - Write TR trades to the associated tenant's Elastic index if new data,
# handle duplicate and quarantine data otherwise
id: tr-feed-ice-fix
name: TR Feed ICE FIX
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  merge: false
- name: MapAttributeOrStaticTrader
  conditionTaskName: FlagControllerTrader
  trueTaskName: MapAttributeTrader
  falseTaskName: MapStaticTrader
  merge: true
- name: MapAttributeOrStaticInvestmentDecisionMaker
  conditionTaskName: FlagControllerInvestmentDecisionMaker
  trueTaskName: MapAttributeInvestmentDecisionMaker
  falseTaskName: MapStaticInvestmentDecisionMaker
  merge: true
- name: MapAttributeOrStaticCounterparty
  conditionTaskName: FlagControllerCounterparty
  trueTaskName: MapAttributeCounterparty
  falseTaskName: MapStaticCounterparty
  merge: true
- name: MapAttributeOrStaticClient
  conditionTaskName: FlagControllerClient
  trueTaskName: MapAttributeClient
  falseTaskName: MapStaticClient
  merge: true
- name: MapAttributeOrStaticExecutingEntity
  conditionTaskName: FlagControllerExecutingEntity
  trueTaskName: MapAttributeExecutingEntity
  falseTaskName: MapStaticExecutingEntity
  merge: true
- name: MapAttributeOrStaticExecutionWithinFirm
  conditionTaskName: FlagControllerExecutionWithinFirm
  trueTaskName: MapAttributeExecutionWithinFirm
  falseTaskName: MapStaticExecutionWithinFirm
  merge: true
tasks:
  # ParametersFlowController
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
  - taskName: file_url
    key: file_url
  # S3DownloadFile
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
  - taskName: file_url
    key: file_url
  # LocalFile
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
  - taskName: file_url
    key: file_url
  # Download fix files and put their content in a data frame column
- path: swarm_tasks.io.read.fix.fix_batch_csv_downloader:FixBatchCsvDownloader
  name: FixBatchCsvDownloader
  upstreamTasks:
    - taskName: S3OrLocalFile
      key: extractor_result
  # Read .fix file, parse, validate and convert to dict data type
  # Note: the key has to be fix_dataframe as that's the argument name in execute()
- path: swarm_tasks.io.read.fix.fix_parser:FixParser
  name: FixParser
  upstreamTasks:
    - taskName: FixBatchCsvDownloader
      key: fix_dataframe
  # Create Rts22Transaction dataframe from FIX data
  # Note: the key has to be fix_parser_data as that's the argument name in execute()
- path: swarm_tasks.tr.feed.ice.fix.ice_fix_generate_trades:IceFixGenerateTrades
  name: IceFixGenerateTrades
  params:
    transactions_only: true
  upstreamTasks:
    - taskName: FixParser
      key: fix_parser_data
# Convert dates
- path: swarm_tasks.transform.datetime.convert_datetime:ConvertDatetime
  name: ConvertDatetime
  paramsList:
    - source_attribute: TrdTimestamps
      target_attribute: transactionDetails.tradingDateTime
      convert_to: datetime
    - source_attribute: TrdTimestamps
      target_attribute: date
      convert_to: date
    - source_attribute: TrdTimestamps
      target_attribute: Trade Date (YYYYMMDD)
      convert_to: date
    - source_attribute: ExpiryDate
      target_attribute: __expiry_date__
      convert_to: date
  upstreamTasks:
    - taskName: IceFixGenerateTrades
      key: result
 # Map Value
- path: swarm_tasks.transform.map.map_value:MapValue
  name: MapValue
  paramsList:
    - source_attribute: TradeReportOrderDetail.OrderCapacity
      target_attribute: transactionDetails.tradingCapacity
      case_insensitive: true
      value_map:
        agency: AOTC
        aotc: AOTC
        deal: DEAL
        match: MTCH
        mtch: MTCH
        pro: DEAL
        proprietary: DEAL
    - source_attribute: Side
      target_attribute: transactionDetails.buySellIndicator
      case_insensitive: true
      value_map: "{1: 'BUYI', 2: 'SELL'}"
    - source_attribute: PositionEffect
      target_attribute: transactionDetails.positionEffect
      case_insensitive: true
      value_map:
        C: Close
        D: Default
        F: FIFO
        N: Closed but notify on open
        O: Open
        R: Rolled
    - source_attribute: UnderlyingSymbol
      target_attribute: __underlying_symbol_firds__
      case_insensitive: true
      value_map:
        BRN: B
        WBS: T
        ECF: C
        GWM: M
  upstreamTasks:
    - taskName: IceFixGenerateTrades
      key: result
# MapConditional
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: MapConditional
  paramsList:
  # If the ICE underlying symbol was successfully mapped to a FIRDS underlying symbol,
  # use that value. Otherwise, use the original ICE underlying symbol.
  - target_attribute: __underlying_symbol__
    cases:
      - query: "`__underlying_symbol_firds__`.notnull()"
        attribute: __underlying_symbol_firds__
      - query: "`__underlying_symbol_firds__`.isnull()"
        attribute: UnderlyingSymbol
  upstreamTasks:
    - taskName: IceFixGenerateTrades
      key: result
    - taskName: MapValue
      key: map_value
 # Map Static
- path: swarm_tasks.transform.map.map_static:MapStatic
  name: MapStatic
  paramsList:
  - target_attribute: transactionDetails.quantityNotation
    target_value: UNIT
  - target_attribute: transactionDetails.priceNotation
    target_value: MONE
  - target_attribute: __meta_model__
    target_value: RTS22Transaction
  - target_attribute: sourceIndex
    from_index: true
    cast_to_str: true
  - target_attribute: dataSourceName
    target_value: ICE FIX Universal TR Blotter
  - target_attribute: tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator
    target_value: false
  - target_attribute: reportDetails.reportStatus
    target_value: NEWT
  - target_attribute: transmissionDetails.orderTransmissionIndicator
    target_value: false
  upstreamTasks:
    - taskName: IceFixGenerateTrades
      key: result
  # Map Attribute
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: MapAttribute
  paramsList:
  - source_attribute: TradeID
    target_attribute: Trade ID
  - source_attribute: LastQty
    target_attribute: transactionDetails.quantity
    cast_to: numeric.absolute
  - source_attribute: Venue
    target_attribute: transactionDetails.venue
  - source_attribute: Venue
    target_attribute: transactionDetails.ultimateVenue
  - source_attribute: SecurityID
    target_attribute: __SecurityID__
    prefix: 'SecurityID: '
  - source_attribute: Symbol
    target_attribute: __symbol__
    prefix: ', Symbol: '
  - source_attribute: AssetClass
    target_attribute: __asset_class__
  - source_attribute: StrikePrice
    target_attribute: __option_strike_price__
  # Add the sourceKey from the S3FileURL column created in IceFixGenerateTrades
  - source_attribute: S3FileURL
    target_attribute: sourceKey
  upstreamTasks:
    - taskName: IceFixGenerateTrades
      key: result
 # Flag Controller Trader
- path: swarm_tasks.boolean.flag_controller:FlagController
  name: FlagControllerTrader
  params:
    flag: true
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  # Map Attribute Trader
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: MapAttributeTrader
  params:
    source_attribute: PartyRole_11
    target_attribute: Trader ID
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  # Map Static Trader
- path: swarm_tasks.transform.map.map_static:MapStatic
  name: MapStaticTrader
  params:
    target_attribute: Trader ID
    target_value: __placeholder__
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  # Flag Controller Investment Decision Maker
- path: swarm_tasks.boolean.flag_controller:FlagController
  name: FlagControllerInvestmentDecisionMaker
  params:
    flag: true
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  # Map Attribute Investment Decision Maker
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: MapAttributeInvestmentDecisionMaker
  params:
    source_attribute: PartyRole_11
    target_attribute: Investment Decision Maker
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  # Map Static Investment Decision Maker
- path: swarm_tasks.transform.map.map_static:MapStatic
  name: MapStaticInvestmentDecisionMaker
  params:
    target_attribute: Investment Decision Maker
    target_value: __placeholder__
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  # Flag Controller Counterparty
- path: swarm_tasks.boolean.flag_controller:FlagController
  name: FlagControllerCounterparty
  params:
    flag: true
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  # Map Attribute Counterparty
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: MapAttributeCounterparty
  params:
    source_attribute: PartyRole_60
    target_attribute: Counterparty ID
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  # Map Static Counterparty
- path: swarm_tasks.transform.map.map_static:MapStatic
  name: MapStaticCounterparty
  params:
    target_attribute: Counterparty ID
    target_value: __placeholder__
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  # Flag Controller Client
- path: swarm_tasks.boolean.flag_controller:FlagController
  name: FlagControllerClient
  params:
    flag: true
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  # Map Attribute Client
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: MapAttributeClient
  params:
    source_attribute: PartyRole_51
    target_attribute: Client ID
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  # Map Static Client
- path: swarm_tasks.transform.map.map_static:MapStatic
  name: MapStaticClient
  params:
    target_attribute: Client ID
    target_value: __placeholder__
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  # Flag Controller Executing Entity
- path: swarm_tasks.boolean.flag_controller:FlagController
  name: FlagControllerExecutingEntity
  params:
    flag: true
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  # Map Attribute Executing Entity
  # Description of PartyRole fields scattered throughout the bundle:
  # https://btobits.com/fixopaedia/fixdic50/index.html?tag_452_PartyRole.html
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: MapAttributeExecutingEntity
  params:
    source_attribute: PartyRole_13
    target_attribute: Executing Entity ID
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  # Map Static Executing Entity
- path: swarm_tasks.transform.map.map_static:MapStatic
  name: MapStaticExecutingEntity
  params:
    target_attribute: Executing Entity ID
    target_value: __placeholder__
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  # Flag Controller Execution Within Firm
- path: swarm_tasks.boolean.flag_controller:FlagController
  name: FlagControllerExecutionWithinFirm
  params:
    flag: true
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  # Map Attribute Execution Within Firm
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: MapAttributeExecutionWithinFirm
  params:
    source_attribute: PartyRole_11
    target_attribute: EXECUTION_WITHIN_FIRM
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  # Map Static Execution Within Firm
- path: swarm_tasks.transform.map.map_static:MapStatic
  name: MapStaticExecutionWithinFirm
  params:
    target_attribute: EXECUTION_WITHIN_FIRM
    target_value: __placeholder__
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  # Concat Attributes
- path: swarm_tasks.transform.concat.concat_attributes:ConcatAttributes
  name: ConcatAttributes
  paramsList:
    - source_attributes:
        - __SecurityID__
        - __symbol__
      target_attribute: transactionDetails.outgoingOrderAddlInfo
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  - taskName: MapAttribute
    key: map_attribute
  # Party Identifiers
- path: swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.party_identifiers:PartyIdentifiers
  name: PartyIdentifiers
  params:
    override_discretionary: false
    override_non_lei_prefix: id
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  - taskName: MapValue
    key: map_value
  - taskName: MapAttribute
    key: map_attribute
  - taskName: MapAttributeOrStaticTrader
    key: trader_result
  - taskName: MapAttributeOrStaticInvestmentDecisionMaker
    key: investment_decision_maker_result
  - taskName: MapAttributeOrStaticCounterparty
    key: counterparty_result
  - taskName: MapAttributeOrStaticClient
    key: client_result
  - taskName: MapAttributeOrStaticExecutingEntity
    key: executing_entity_result
  - taskName: MapAttributeOrStaticExecutionWithinFirm
    key: execution_within_firm_result
  # Instrument Identifiers
- path: swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers:InstrumentIdentifiers
  name: InstrumentIdentifiers
  params:
    asset_class_attribute: __asset_class__
    expiry_date_attribute: __expiry_date__
    option_strike_price_attribute: __option_strike_price__
    option_type_attribute: OptionType
    swap_near_leg_date_attribute: date
    underlying_symbol_attribute: __underlying_symbol__
    venue_attribute: Venue
  upstreamTasks:
    - taskName: IceFixGenerateTrades
      key: result
    - taskName: MapAttribute
      key: map_attribute
    - taskName: MapConditional
      key: map_conditional
    - taskName: ConvertDatetime
      key: convert_datetime
  # Merge Market Identifiers
- path: swarm_tasks.transform.steeleye.link.merge_market_identifiers:MergeMarketIdentifiers
  name: MergeMarketIdentifiers
  params:
    identifiers_path: marketIdentifiers
    instrument_path: marketIdentifiers.instrument
    parties_path: marketIdentifiers.parties
  upstreamTasks:
    - taskName: InstrumentIdentifiers
      key: result
    - taskName: PartyIdentifiers
      key: party_identifiers
 # Primary Frame Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PrimaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
    - __SecurityID__
    - __symbol__
    - TradeID
    - TrdTimestamps
    - PositionEffect
    - PriceType
    - QtyType
    - LastQty
    - LastMkt
    - CumQty
    - model
    - OrdType
    - LeavesQty
    - Side
    - TradeReportOrderDetail.OrderCapacity
    - SecurityExchange
    - Venue
    - ExpiryDate
    - OrderQty
    - MsgSeqNum
    - AssetClass
    - S3FileURL
  upstreamTasks:
  - taskName: IceFixGenerateTrades
    key: result
  - taskName: ConvertDatetime
    key: convert_date_time
  - taskName: MapAttribute
    key: map_attribute
  - taskName: MapStatic
    key: map_static
  - taskName: MapValue
    key: map_value
  - taskName: MapConditional
    key: map_conditional
  - taskName: ConcatAttributes
    key: concat_attributes
  - taskName: PartyIdentifiers
    key: party_identifiers
  - taskName: InstrumentIdentifiers
    key: instrument_identifiers
  - taskName: MergeMarketIdentifiers
    key: merge_market_ids
  - taskName: MapAttributeOrStaticTrader
    key: trader_result
  - taskName: MapAttributeOrStaticInvestmentDecisionMaker
    key: investment_decision_maker_result
  - taskName: MapAttributeOrStaticCounterparty
    key: counterparty_result
  - taskName: MapAttributeOrStaticClient
    key: client_result
  - taskName: MapAttributeOrStaticExecutingEntity
    key: executing_entity_result
  - taskName: MapAttributeOrStaticExecutionWithinFirm
    key: execution_within_firm_result
  # Branch Membership Country
- path: swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.branch_membership_country:BranchMembershipCountry
  name: BranchMembershipCountry
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      key: result
  # Link Instrument
- path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
  name: LinkInstrument
  params:
    identifiers_path: marketIdentifiers.instrument
    venue_attribute: transactionDetails.venue
    asset_class_attribute: __asset_class__
  resources:
    ref_data_key: reference-data
    tenant_data_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      key: result
  # Map Currency from InstrumentDetails.instrument.notionalCurrency1
- path: swarm_tasks.transform.map.map_from_nested:MapFromNested
  name: MapNestedFromInstrument
  paramsList:
  - source_attribute: instrumentDetails.instrument
    nested_path: notionalCurrency1
    target_attribute: Currency
  - source_attribute: instrumentDetails.instrument
    nested_path: instrumentClassification
    target_attribute: __instrument_classification
  upstreamTasks:
  - taskName: LinkInstrument
    key: result
  # Eligibility Assessor
- path: swarm_tasks.transform.steeleye.tr.mifir.eligibility_assessor:EligibilityAssessor
  name: EligibilityAssessor
  resources:
    es_client_key: tenant-data
    srp_client_key: reference-data
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      key: result
    - taskName: LinkInstrument
      key: link_instrument
  # Link Parties
- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkParties
  params:
    identifiers_path: marketIdentifiers.parties
    add_investment_firm_covered_directive: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      key: result
  # Map tradersAlgosWaiversIndicators.commodityDerivativeIndicator from __instrument_classification
- path: swarm_tasks.transform.map.map_value:MapValue
  name: MapCommodityDerivativeIndicator
  paramsList:
  - source_attribute: __instrument_classification
    target_attribute: tradersAlgosWaiversIndicators.commodityDerivativeIndicator
    case_insensitive: true
    regex_replace_map:
      - regex: "^JT[A-Z]{2}C[A-Z]{1}$"
        replace_value: F
      - regex: "^JT[A-Z]{2}F[A-Z]{1}$"
        replace_value: F
      - regex: "^FC[A-Z]{4}$"
        replace_value: F
      - regex: "^HT[A-Z]{4}$"
        replace_value: F
      - regex: "^O[A-Z]{2}T[A-Z]{2}$"
        replace_value: F
      - regex: "^ST[A-Z]{1}T[A-Z]{2}$"
        replace_value: F
      - regex: "^ST[A-Z]{1}C[A-Z]{2}$"
        replace_value: F
    value_map:
      F: false
  upstreamTasks:
  - taskName: MapNestedFromInstrument
    key: result
  # Market Frame Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: MarketFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __instrument_classification
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    key: result
  - taskName: LinkParties
    key: link_parties
  - taskName: LinkInstrument
    key: link_instrument
  - taskName: MapNestedFromInstrument
    key: currency
  - taskName: BranchMembershipCountry
    key: branch_membership_country
  - taskName: EligibilityAssessor
    key: eligibility_assessor
  - taskName: MapCommodityDerivativeIndicator
    key: commodity_derivative_indicator
  # Filter out Party Role columns
- path: swarm_tasks.transform.frame.filter_columns:FilterColumns
  name: FilterPartyRoleColumns
  params:
    column_regex: ^PartyRole
    action: drop
  upstreamTasks:
    - taskName: MarketFrameConcatenator
      key: result
  # Convert currency and price
- path: swarm_tasks.generic.currency.convert_minor_to_major:ConvertMinorToMajor
  name: ConvertMinorToMajor
  paramsList:
    - source_ccy_attribute: Currency
      target_ccy_attribute: transactionDetails.priceCurrency
    - source_ccy_attribute: Currency
      target_ccy_attribute: transactionDetails.settlementAmountCurrency
    - source_price_attribute: AvgPx
      source_ccy_attribute: Currency
      target_price_attribute: transactionDetails.priceAverage
      cast_to: abs
    - source_price_attribute: __option_strike_price__
      source_ccy_attribute: Currency
      target_price_attribute: __option_strike_price_converted__
      cast_to: abs
  upstreamTasks:
    - taskName: FilterPartyRoleColumns
      key: result
  # Price and Currency Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PriceCurrencyFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
      - instrumentDetails.instrument
  upstreamTasks:
  - taskName: FilterPartyRoleColumns
    key: result
  - taskName: ConvertMinorToMajor
    key: minor_to_major
  # Short Selling Indicator
- path: swarm_tasks.transform.steeleye.tr.generic.short_selling_indicator:ShortSellingIndicator
  name: ShortSellingIndicator
  params:
    buy_sell_indicator_attribute: transactionDetails.buySellIndicator
    client_id_attribute: Client ID
    instrument_attribute: instrumentDetails.instrument
    instrument_classification_nested_path: instrumentClassification
    target_attribute: tradersAlgosWaiversIndicators.shortSellingIndicator
    issuer_or_operator_of_trading_venue_id_nested_path: issuerOrOperatorOfTradingVenueId
  upstreamTasks:
    - taskName: PriceCurrencyFrameConcatenator
      key: price_currency_frame
    - taskName: IceFixGenerateTrades
      key: result
    - taskName: LinkInstrument
      key: link_instrument
  # Instrument Override Strike Price
- path: swarm_tasks.transform.map.map_to_nested:MapToNested
  name: InstrumentOverridesStrikePrice
  params:
    source_attribute: __option_strike_price_converted__
    nested_path: derivative.strikePrice
    target_attribute: instrumentDetails.instrument
  upstreamTasks:
    - taskName: PriceCurrencyFrameConcatenator
      key: price_currency_frame
    - taskName: LinkInstrument
      key: result
# TransactionRefNo
- path: swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.transaction_ref_no:TransactionRefNo
  name: TransactionRefNo
  upstreamTasks:
    - taskName: PriceCurrencyFrameConcatenator
      key: result
  # Auxiliary Frame Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: AuxiliaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
    - marketIdentifiers.instrument
    - marketIdentifiers.parties
    - __expiry_date__
    - __asset_class__
    - __option_strike_price__
    - __option_strike_price_converted__
    - __underlying_symbol__
    - __underlying_symbol_firds__
    - Currency
    - SecurityID
    - UnderlyingSymbol
    - Symbol
    - LastPx
    - AvgPx
    - TargetSubId
    - StrikePrice
    - OptionType
    - RegulatoryTradeID
    - Order ID
    - Trade ID
    - Counterparty ID
    - Client ID
    - Executing Entity ID
    - Investment Decision Maker
    - Trader ID
    - OrdStatus
    - Trade Date (YYYYMMDD)
    - EXECUTION_WITHIN_FIRM
  upstreamTasks:
  - taskName: PriceCurrencyFrameConcatenator
    key: result
  - taskName: ShortSellingIndicator
    key: short_selling_indicator
  - taskName: TransactionRefNo
    key: transaction_ref_no
  - taskName: InstrumentOverridesStrikePrice
    key: strike_price
# Auxiliary Map Attribute
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: AuxiliaryMapAttribute
  paramsList:
  - source_attribute: reportDetails.transactionRefNo
    target_attribute: reportDetails.tradingVenueTransactionIdCode
  upstreamTasks:
    - taskName: AuxiliaryFrameConcatenator
      key: result
  # Assign Meta
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: Meta
  params:
    model_attribute: __meta_model__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: AuxiliaryFrameConcatenator
      key: result
    - taskName: AuxiliaryMapAttribute
      key: aux_map_attribute
  # Map Conditional Workflow Status
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: WorkflowStatus
  params:
    source_attribute: '&validationErrors'
    target_attribute: workflow.validationStatus
    condition: isnull
    true_value: PASSED
    false_value: FAILED
  upstreamTasks:
    - taskName: Meta
      key: result
  # Final Frame Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: FinalFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
  upstreamTasks:
    - taskName: AuxiliaryFrameConcatenator
      key: result
    - taskName: Meta
      key: meta_result
    - taskName: WorkflowStatus
      key: workflow_status_result
    - taskName: AuxiliaryMapAttribute
      key: aux_map_attribute
  # Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: FinalFrameConcatenator
      key: transform_result
    - taskName: IceFixGenerateTrades
      key: producer_result
  # Elastic Bulk Writer
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsent
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: ElasticBulkTransformer
      key: result
  # Quarantine Condition
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineCondition
  upstreamTasks:
  - taskName: PutIfAbsent
    key: bulk_writer_result
  # Quarantine Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformer
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: FinalFrameConcatenator
    key: transform_result
  - taskName: IceFixGenerateTrades
    key: producer_result
  - taskName: PutIfAbsent
    key: bulk_writer_result
  # Quarantine Elastic Bulk Writer
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsent
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: QuarantinedElasticBulkTransformer
    key: result
- path: swarm_tasks.control_flow.noop:Noop
  name: Noop