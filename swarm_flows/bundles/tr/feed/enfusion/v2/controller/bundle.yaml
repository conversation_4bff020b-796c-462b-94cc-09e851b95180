# Bundle path -> tr/feed/enfusion/v2/controller
# Confluence page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2063139091/RTS22+Enfusion+V2
# Docs: docs/bundles/tr/feed/tr-feed-enfusion-v2-controller.md
id: tr-feed-enfusion-v2-controller
name: TR Feed Enfusion V2 Controller
infra:
  - name: tenant-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process
controlFlows:
  - name: S3CopyOverride
    conditionTaskName: S3CopyController
    mapped: false
    merge: true
    cases:
      Noop: Noop # Case specific
      S3CopyFile: S3CopyFile # Default case
tasks:
  - path: swarm_tasks.generic.value_proxy:ValueProxy
    name: S3CopyController
    params:
      value: Noop
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.aws.copy.s3_to_s3_copy:S3ToS3Copy
    name: S3CopyFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.control_flow.multiple_files_input_controller:MultipleFilesInputController
    name: FileController
    params:
      list_of_files_regex:
        - "(Allocations)"
        - "(Executions)"
      unique_identifier_regex:
        - "^[a-zA-Z]+\\_"   # i.e file_name - RaynarPortfolioManagement_ or GalvanizeClimate_
        - "(\\d{4}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01]))"  # i.e "20220226 - YYYYMMDD"
      file_links_in_output: true
    upstreamTasks:
      - taskName: file_url
        key: file_url

  # Pre Process data by reading all the files present in S3 for the flow run
  - path: swarm_tasks.io.read.aws.s3_download_multiple_files:S3DownloadMultipleFiles
    name: PreProcessData
    params:
      file_columns:
        - s3_executions_file_url
        - s3_allocations_file_url
      suffix_identifier_regex: s3_(.*)_file_url
    upstreamTasks:
      - taskName: FileController
        mapped: false
        key: producer_result

  # Merge the allocations and executions data
  - path: swarm_tasks.steeleye.tr.feed.enfusion.v2.processor.transaction_record_transformer:EnfusionTransactionFrameTransformer
    name: FrameTransformer
    upstreamTasks:
      - taskName: PreProcessData
        mapped: false
        key: pre_process_result
      - taskName: FileController
        mapped: false
        key: s3_file_url_df

  # Chunk into batches
  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvFileSplitter
    params:
      chunksize: 20000
      detect_encoding: true
    upstreamTasks:
      - taskName: FrameTransformer
        mapped: false
        key: extractor_result

  # Create config to upload to S3
  - path: swarm_tasks.transform.extract_path.s3_file_list_from_frame_splitter_result_list:S3FileListFileSplitterResultList
    name: S3FileListFileSplitterResultList
    params:
      datetime_field_in_file_path: 1
      cloud_key_prefix: "flows/tr-feed-enfusion-v2-processor"
    upstreamTasks:
      - taskName: CsvFileSplitter
        mapped: false
        key: file_splitter_result_list

  # Upload to S3
  - path: swarm_tasks.io.write.aws.s3_upload_file:S3UploadFile
    name: S3UploadFile
    upstreamTasks:
      - taskName: S3FileListFileSplitterResultList
        mapped: true
        key: upload_target
  - path: swarm_tasks.control_flow.noop:Noop
    name: Noop