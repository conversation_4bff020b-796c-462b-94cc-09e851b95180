# Bundle path -> tr/feed/beacon/lighthouse
# Confluence page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/1691255110/RTS22+Lighthouse+Beacon
# Lighthouse lei from mapping: lei:549300IL8TQT0JMDJJ80
id: tr-feed-beacon-lighthouse
name: TR Feed Lighthouse Beacon
infra:
  - name: tenant-data
    type: ELASTICSEARCH
  - name: reference-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process
controlFlows:
  - name: S3OrLocalFile
    conditionTaskName: ParametersFlowController
    trueTaskName: S3DownloadFile
    falseTaskName: LocalFile
    merge: true
  - name: ConditionalCsvFileSplitter
    conditionTaskName: ParamIsBatchedFile
    trueTaskName: BatchedFileCsvSplitter
    falseTaskName: NormalFileCsvSplitter
    merge: true
  - name: NeedsQuarantine
    conditionTaskName: QuarantineCondition
    trueTaskName: QuarantinedElasticBulkTransformer
    falseTaskName: Noop
    mapped: true
    merge: false
  # Depending on chunksize, if the input file is greater than X rows,
  # it will be split into different files of X rows and uploaded to S3
  - name: FileTooBig
    conditionTaskName: HorizontalBatchController
    trueTaskName: BatchProducer
    falseTaskName: MergeAndChunkCsvFiles
    mapped: false
    merge: false
tasks:
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParamIsBatchedFile
    params:
      arg_name: file_url
      contains: batch # batched file will always contain 'batch' in name
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: NormalFileCsvSplitter
    params:
      chunksize: 10000
      delimiter: "|"
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result
  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: BatchedFileCsvSplitter
    params:
      chunksize: 10000
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result
  - path: swarm_tasks.io.read.horizontal_batch_controller:HorizontalBatchController
    name: HorizontalBatchController
    params:
      max_chunk_size: 1
    upstreamTasks:
      - taskName: ConditionalCsvFileSplitter
        mapped: false
        key: list_of_batches
  - path: swarm_tasks.io.read.merge_and_chunk_csv_files:MergeAndChunkCsvFiles
    name: MergeAndChunkCsvFiles
    params:
      max_chunk_size: 1
    upstreamTasks:
      - taskName: ConditionalCsvFileSplitter
        mapped: false
        key: file_splitter_result_list
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.transform.extract_path.s3_file_list_from_frame_splitter_result_list:S3FileListFileSplitterResultList
    name: S3FileListFileSplitterResultList
    params:
      cloud_key_prefix: "flows/tr-feed-beacon-lighthouse/batches"
      datetime_field_in_file_path: false
    upstreamTasks:
      - taskName: MergeAndChunkCsvFiles
        mapped: false
        key: file_splitter_result_list
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.write.aws.s3_upload_file:S3UploadFile
    name: S3UploadOrderFile
    upstreamTasks:
      - taskName: S3FileListFileSplitterResultList
        mapped: false
        key: upload_target
  - path: swarm_tasks.io.read.batch_producer:BatchProducer
    name: BatchProducer
    params:
      source_schema:
        "ACCOUNT": "string"
        "BBSYMBOL": "string"
        "BROKER_CODE": "string"
        "CLIENTID": "string"
        "CURRENCY": "string"
        "EXCHANGE_NAME": "string"
        "EXPIRE_DATE": "string"
        "ISCFD": "string"
        "ISIN": "string"
        "LAST_MARKET": "string"
        "LOCATION": "string"
        "MANAGER": "string"
        "MIC": "string"
        "ORDER_STATUS": "string"
        "PRICE": "float"
        "PRICE_NOTATION": "string"
        "PUTORCALL": "string"
        "QUANTITY_NOTATION": "string"
        "SECURITY_NAME": "string"
        "SECURITY_TYPE": "string"
        "SIDE": "string"
        "STRIKE_PRICE": "float"
        "TRADE_ID": "string"
        "TRADED_QTY": "float"
        "TRADER": "string"
        "TRADE_DATE": "string"
        "TRADING_CAPACITY": "string"
        "TRANSACT_TIME": "string"
        "UNDERLYING_ISIN": "string"
        "UNDERLYING_TICKER": "string"
    upstreamTasks:
    - taskName: ConditionalCsvFileSplitter
      mapped: true
      flatten: true
      key: file_splitter_result
  # Remove/skip rows which have OrderStatus in ['PendingNew','PendingCancel','PendingReplace']
  - path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
    name: FilterRowsInSourceFile
    params:
      query: "~`ORDER_STATUS`.isin(['New', 'PendingNew', 'PendingCancel', 'PendingReplace', 'Rejected', 'Replaced'])"
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  # Convert Minor Currencies to Major
  - path: swarm_tasks.generic.currency.convert_minor_to_major:ConvertMinorToMajor
    name: ConvertMinorToMajor
    paramsList:
      - source_ccy_attribute: CURRENCY
        target_ccy_attribute: __currency__
      - source_price_attribute: PRICE
        source_ccy_attribute: CURRENCY
        target_price_attribute: transactionDetails.price
        cast_to: abs
      - source_price_attribute: TRADED_QTY
        source_ccy_attribute: CURRENCY
        target_price_attribute: __traded_qty__
        cast_to: abs
      - source_price_attribute: STRIKE_PRICE
        source_ccy_attribute: CURRENCY
        target_price_attribute: __option_strike_price__
        cast_to: abs
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
  # Convert Date and Datetimes
  - path: swarm_tasks.transform.datetime.convert_datetime:ConvertDatetime
    name: ConvertDatetime
    paramsList:
      - source_attribute: TRADE_DATE
        target_attribute: date
        source_attribute_format: "%Y-%m-%dT"
        convert_to: date
      - source_attribute: TRANSACT_TIME
        target_attribute: __transact_time__
        source_attribute_format: "%Y-%m-%dT%H:%M:%S.%f"
        convert_to: datetime
      - source_attribute: EXPIRE_DATE
        target_attribute: __expire_date__
        source_attribute_format: "%Y-%m-%d"
        convert_to: date
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_value:MapValue
    name: MapValues
    paramsList:
      - source_attribute: SIDE
        target_attribute: __buysell__
        case_insensitive: true
        value_map:
          Buy: BUYI
          Cover: BUYI
          Short: SELL
          Sell: SELL
      - source_attribute: PRICE_NOTATION
        target_attribute: transactionDetails.priceNotation
        case_insensitive: true
        value_map:
          PERCENTAGE: PERC
          PERC: PERC
          MONETARY: MONE
          MONE: MONE
          MONEY: MONE
          BASE POINTS: BAPO
          BAPO: BAPO
          YIELD: YIEL
          YIEL: YIEL
      - source_attribute: QUANTITY_NOTATION
        target_attribute: transactionDetails.quantityNotation
        case_insensitive: true
        value_map:
          MONE: MONE
          MONETARY: MONE
          MONEY: MONE
          NOMI: NOML
          NOMINAL: NOML
          NOMINALVALUE: NOML
          NOML: NOML
          UNIT: UNIT
      - source_attribute: ORDER_STATUS
        target_attribute: reportDetails.reportStatus
        case_insensitive: true
        value_map:
          Canceled: CANC
          DoneForDay: NEWT
          Filled: NEWT
          PartFilled: NEWT
      - source_attribute: SECURITY_TYPE
        target_attribute: __asset_class__
        case_insensitive: true
        value_map:
          common stock: cds single stock
          fut: future
          index: cds index
          opt: option
          stock-etf: equity forward
          stock-subtype:93: cds single stock
          stock: cds single stock
          swap: equity swap
      - source_attribute: TRADE_ID
        target_attribute: __trade_id__
        case_insensitive: true
        regex_replace_map:
          - regex: '[^a-zA-Z\d]'
            drop: true
        regex_replace_only: true
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
  # MapStatic
  - path: swarm_tasks.transform.map.map_static:MapStatic
    name: MapStatic
    paramsList:
      # Metadata fields
      - target_attribute: sourceKey
        from_env_var: SWARM_FILE_URL
      - target_attribute: dataSourceName
        target_value: Beacon OMS
      - target_attribute: __meta_model__
        target_value: RTS22Transaction
      - target_attribute: sourceIndex
        from_index: true
        cast_to_str: true
      # LEI for Lighthouse: 549300IL8TQT0JMDJJ80
      - target_attribute: __executing_entity__
        target_value: 549300IL8TQT0JMDJJ80
      # Instrument fallback fields
      - target_attribute: __fb_is_created_through_fallback__
        target_value: true
      - target_attribute: __strike_price_type__
        target_value: MntryVal
      # Indicators
      - target_attribute: tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator
        target_value: False
      - target_attribute: transmissionDetails.orderTransmissionIndicator
        target_value: False
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
  # Venue and asset-class
  - path: swarm_tasks.transform.map.map_conditional:MapConditional
    name: MapConditionalCFDInstruments
    paramsList:
      - target_attribute: __input_asset_class__
        cases:
          - query: "`ISCFD` == '1'"
            value: cfd
          - query: "`ISCFD` == '0'"
            attribute: __asset_class__
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
      - taskName: MapValues
        mapped: true
        key: map_values
  # Map Attributes
  - path: swarm_tasks.transform.map.map_attribute:MapAttribute
    name: MapAttributes
    paramsList:
      - source_attribute: TRADING_CAPACITY
        target_attribute: transactionDetails.tradingCapacity
      - source_attribute: __trade_id__
        target_attribute: reportDetails.transactionRefNo
      - source_attribute: SECURITY_NAME
        target_attribute: __security_name__
      # Date/Times
      - source_attribute: __transact_time__
        target_attribute: transactionDetails.tradingDateTime
      - source_attribute: __expire_date__
        target_attribute: instrumentDetails.instrument.derivative.expiryDate
      # BuySell
      - source_attribute: __buysell__
        target_attribute: transactionDetails.buySellIndicator
      # Currencies/Prices
      - source_attribute: __currency__
        target_attribute: transactionDetails.priceCurrency
      - source_attribute: __currency__
        target_attribute: transactionDetails.quantityCurrency
      - source_attribute: __traded_qty__
        target_attribute: transactionDetails.quantity
      # Party Identifiers Fields
      - source_attribute: __executing_entity__
        target_attribute: __executing_entity_with_lei__
        prefix: "lei:"
      - source_attribute: TRADER
        target_attribute: __trader_id__
        prefix: "id:"
      - source_attribute: BROKER_CODE
        target_attribute: __counterparty_id__
        prefix: "id:"
      - source_attribute: MANAGER
        target_attribute: __inv_dec_in_firm_id__
        prefix: "id:"
      - source_attribute: ACCOUNT
        target_attribute: __buyer_id__
        prefix: "id:"
      - source_attribute: __executing_entity__
        target_attribute: __buyer_dm_with_lei__
        prefix: "lei:"
      - source_attribute: BROKER_CODE
        target_attribute: __seller_id__
        prefix: "id:"
      # Venues
      - source_attribute: LOCATION
        target_attribute: transactionDetails.venue
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
      - taskName: MapValues
        mapped: true
        key: map_values
      - taskName: MapStatic
        mapped: true
        key: map_static
      - taskName: ConvertDatetime
        mapped: true
        key: convert_date_time
      - taskName: ConvertMinorToMajor
        mapped: true
        key: convert_minor_major
  # Map Conditional for short sell indicator
  - path: swarm_tasks.transform.map.map_conditional:MapConditional
    name: MapConditional
    paramsList:
      - target_attribute: tradersAlgosWaiversIndicators.shortSellingIndicator
        cases:
          - query: "SIDE == 'Short'"
            value: SESH
      - target_attribute: __put_or_call__
        cases:
          - query: "`PUTORCALL` == '0'"
            value: PUTO
          - query: "`PUTORCALL` == '1'"
            value: CALL
      - target_attribute: transactionDetails.ultimateVenue
        cases:
          - query: "`LAST_MARKET`.notnull() & `LAST_MARKET`.str.len() == 4"
            attribute: LAST_MARKET
          - query: "`LAST_MARKET`.isnull() | `LAST_MARKET`.str.len() != 4"
            attribute: MIC
          - query: "`ISIN`.isnull()"
            attribute: LOCATION
      - target_attribute: __input_venue__
        cases:
          - query: "`LAST_MARKET`.notnull() & `LAST_MARKET`.str.len() == 4"
            attribute: LAST_MARKET
          - query: "`LAST_MARKET`.isnull() | `LAST_MARKET`.str.len() != 4"
            attribute: MIC
          - query: "`ISIN`.isnull()"
            attribute: LOCATION
          - query: "(`ISIN`.isnull()) & (`UNDERLYING_ISIN`.isnull())"
            attribute: LAST_MARKET
      - target_attribute: __inv_dec_in_firm_id_if_client__
        cases:
          - query: "`CLIENTID` == '549300IL8TQT0JMDJJ80'"
            attribute: __inv_dec_in_firm_id__
      - target_attribute: __buyer_dm_with_lei_if_client__
        cases:
          - query: "`CLIENTID` == '549300IL8TQT0JMDJJ80'"
            attribute: __buyer_dm_with_lei__
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
      - taskName: MapAttributes
        mapped: true
        key: map_attributes
  # Party Identifiers
  - path: swarm_tasks.transform.steeleye.tr.identifiers.party_identifiers:PartyIdentifiers
    name: PartyIdentifiers
    params:
      target_attribute: marketIdentifiers.parties
      executing_entity_identifier: __executing_entity_with_lei__
      trader_identifier: __trader_id__
      counterparty_identifier: __counterparty_id__
      investment_decision_within_firm_identifier: __inv_dec_in_firm_id_if_client__
      execution_within_firm_identifier: __trader_id__
      buy_sell_side_attribute: transactionDetails.buySellIndicator
      buyer_identifier: __buyer_id__
      buyer_decision_maker_identifier: __buyer_dm_with_lei_if_client__
      seller_identifier: __seller_id__
      use_buy_mask_for_buyer_seller: true
      use_buy_mask_for_buyer_seller_decision_maker: true
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
      - taskName: MapAttributes
        mapped: true
        key: map_attributes
      - taskName: MapConditional
        mapped: true
        key: map_conditional
  # Instrument Identifiers
  - path: swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers:InstrumentIdentifiers
    name: InstrumentIdentifiers
    params:
      asset_class_attribute: __input_asset_class__
      bbg_figi_id_attribute: BBSYMBOL
      currency_attribute: __currency__
      expiry_date_attribute: __expire_date__
      isin_attribute: ISIN
      option_strike_price_attribute: __option_strike_price__
      option_type_attribute: __put_or_call__
      underlying_symbol_attribute: UNDERLYING_TICKER
      underlying_isin_attribute: UNDERLYING_ISIN
      venue_attribute: __input_venue__
      retain_task_inputs: true
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
      - taskName: MapValues
        mapped: true
        key: map_values
      - taskName: MapConditional
        mapped: true
        key: map_conditional
      - taskName: ConvertMinorToMajor
        mapped: true
        key: convert_minor_major
      - taskName: ConvertDatetime
        mapped: true
        key: convert_date_time
      - taskName: MapConditionalCFDInstruments
        mapped: true
        key: asset_class
  # Merge Identifiers
  - path: swarm_tasks.transform.steeleye.link.merge_market_identifiers:MergeMarketIdentifiers
    name: MergeMarketIdentifiers
    params:
      identifiers_path: marketIdentifiers
      instrument_path: marketIdentifiers.instrument
      parties_path: marketIdentifiers.parties
    upstreamTasks:
      - taskName: InstrumentIdentifiers
        mapped: true
        key: result
      - taskName: PartyIdentifiers
        mapped: true
        key: party_identifiers
  # Primary Frame Concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PrimaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __buyer_dm_with_lei__
        - __buyer_dm_with_lei_if_client__
        - __buyer_id__
        - __buysell__
        - __counterparty_id__
        - __traded_qty__
        - __executing_entity__
        - __executing_entity_with_lei__
        - __inv_dec_in_firm_id__
        - __inv_dec_in_firm_id_if_client__
        - __put_or_call__
        - __seller_id__
        - __trade_id__
        - __trader_id__
        - __transact_time__
    upstreamTasks:
      - taskName: MapAttributes
        mapped: true
        key: result
      - taskName: MapValues
        mapped: true
        key: map_values
      - taskName: MapConditional
        mapped: true
        key: map_conditional
      - taskName: MapStatic
        mapped: true
        key: map_static
      - taskName: MapConditionalCFDInstruments
        mapped: true
        key: map_conditional_cfd_instruments
      - taskName: ConvertDatetime
        mapped: true
        key: convert_date_time
      - taskName: ConvertMinorToMajor
        mapped: true
        key: convert_minor_major
      - taskName: PartyIdentifiers
        mapped: true
        key: party_identifiers
      - taskName: InstrumentIdentifiers
        mapped: true
        key: instrument_identifiers
      - taskName: MergeMarketIdentifiers
        mapped: true
        key: merge_market_identifiers
  # Link Parties
  - path: swarm_tasks.transform.steeleye.link.parties:LinkParties
    name: LinkParties
    params:
      identifiers_path: marketIdentifiers.parties
      add_investment_firm_covered_directive: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  # Link Instrument
  - path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
    name: LinkInstrument
    params:
      identifiers_path: marketIdentifiers.instrument
      asset_class_attribute: __input_asset_class__
      currency_attribute: __currency__
      venue_attribute: __input_venue__
    resources:
      ref_data_key: reference-data
      tenant_data_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  # Instrument Fallback
  - path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
    name: InstrumentFallback
    params:
      instrument_fields_map:
        - source_field: __expire_date__
          target_field: derivative.expiryDate
        - source_field: __currency__
          target_field: derivative.strikePriceCurrency
        - source_field: __strike_price_type__
          target_field: ext.strikePriceType
        - source_field: __security_name__
          target_field: instrumentFullName
        - source_field: __fb_is_created_through_fallback__
          target_field: isCreatedThroughFallback
      str_to_bool_dict:
        "true": True
        "false": False
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
      - taskName: LinkInstrument
        mapped: true
        key: link_instrument
  # Method to override the notional_currency_1 to the client provided currency
  - path: swarm_tasks.transform.map.map_to_nested:MapToNested
    name: InstrumentOverrideNotionalCurrency1
    params:
      source_attribute: __currency__
      nested_path: notionalCurrency1
      target_attribute: instrumentDetails.instrument
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
      - taskName: InstrumentFallback
        mapped: true
        key: instrument_fallback
  - path: swarm_tasks.transform.map.map_to_nested:MapToNested
    name: InstrumentOverridesStrikePrice
    params:
      source_attribute: __option_strike_price__
      nested_path: derivative.strikePrice
      target_attribute: instrumentDetails.instrument
    upstreamTasks:
      - taskName: InstrumentOverrideNotionalCurrency1
        mapped: true
        key: instrument_override_notional_currency_1
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_to_nested:MapToNested
    name: InstrumentOverridesStrikePriceCurrency
    params:
      source_attribute: __currency__
      nested_path: derivative.strikePriceCurrency
      target_attribute: instrumentDetails.instrument
    upstreamTasks:
      - taskName: InstrumentOverridesStrikePrice
        mapped: true
        key: instrument_override_strike_price
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_to_nested:MapToNested
    name: InstrumentExtStrikePriceType
    params:
      source_attribute: __strike_price_type__
      nested_path: ext.strikePriceType
      target_attribute: instrumentDetails.instrument
      query: "`__asset_class__` == 'option'"
    upstreamTasks:
      - taskName: InstrumentOverridesStrikePriceCurrency
        mapped: true
        key: instrument_override_strike_price_currency
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  # Eligibility Assessor
  - path: swarm_tasks.transform.steeleye.tr.mifir.eligibility_assessor:EligibilityAssessor
    name: EligibilityAssessor
    resources:
      es_client_key: tenant-data
      srp_client_key: reference-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
      - taskName: InstrumentExtStrikePriceType
        mapped: true
        key: instrument_ext_strike_price_type
  # Auxiliary Frame Concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: AuxiliaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - marketIdentifiers.instrument
        - marketIdentifiers.parties
        - __currency__
        - __expire_date__
        - __fb_is_created_through_fallback__
        - __maturity_date__
        - __option_strike_price__
        - __security_name__
        - __strike_price_type__
        - __asset_class__
        - __input_asset_class__
        - __input_venue__
        - asset_class_attribute # Instrument Identifiers columns
        - bbg_figi_id_attribute
        - currency_attribute
        - eurex_id_attribute
        - exchange_symbol_attribute
        - expiry_date_attribute
        - interest_rate_start_date_attribute
        - isin_attribute
        - notional_currency_1_attribute
        - notional_currency_2_attribute
        - option_strike_price_attribute
        - option_type_attribute
        - swap_near_leg_date_attribute
        - underlying_index_name_attribute
        - underlying_index_name_leg_2_attribute
        - underlying_index_series_attribute
        - underlying_index_term_attribute
        - underlying_index_term_value_attribute
        - underlying_index_version_attribute
        - underlying_isin_attribute
        - underlying_symbol_attribute
        - underlying_symbol_expiry_code_attribute
        - underlying_index_term_leg_2_attribute
        - underlying_index_term_value_leg_2_attribute
        - venue_attribute
        - venue_financial_instrument_short_name_attribute
        - instrument_classification_attribute
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
      - taskName: LinkParties
        mapped: true
        key: link_parties
      - taskName: EligibilityAssessor
        mapped: true
        key: eligibility_assessor
      - taskName: InstrumentExtStrikePriceType
        mapped: true
        key: instrument_ext_strike_price_type
  - path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
    name: AssignMeta
    params:
      model_attribute: __meta_model__
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: AuxiliaryFrameConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_conditional:MapConditional
    name: WorkflowStatus
    params:
      source_attribute: '&validationErrors'
      target_attribute: workflow.validationStatus
      condition: isnull
      true_value: PASSED
      false_value: FAILED
    upstreamTasks:
      - taskName: AssignMeta
        mapped: true
        key: result
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: FinalConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __meta_model__
    upstreamTasks:
      - taskName: AssignMeta
        mapped: true
        key: result
      - taskName: AuxiliaryFrameConcatenator
        mapped: true
        key: aux_frame_concatenator
      - taskName: WorkflowStatus
        mapped: true
        key: workflow_status_result
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformer
    params:
      action_type: create
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: FinalConcatenator
        mapped: true
        key: transform_result
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: producer_result
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: PutIfAbsent
    params:
      payload_size: 10000000
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: ElasticBulkTransformer
        mapped: true
        key: result
  - path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
    name: QuarantineCondition
    upstreamTasks:
      - taskName: PutIfAbsent
        mapped: true
        key: bulk_writer_result
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: QuarantinedElasticBulkTransformer
    params:
      action_type: create
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: FinalConcatenator
        mapped: true
        key: transform_result
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: producer_result
      - taskName: PutIfAbsent
        mapped: true
        key: bulk_writer_result
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: QuarantinedPutIfAbsent
    params:
      payload_size: 10000000
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: QuarantinedElasticBulkTransformer
        mapped: true
        key: result
  - path: swarm_tasks.control_flow.noop:Noop
    name: Noop
