# Bundle path -> tr/feed/bbg/emsi-routes
# ---------------------------------------
# Confluence page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2876473345/RTS22+Bloomberg+EMSI+routes
# ---------------------------------------
# FLOW Summary:
# 1. It downloads a given csv that contains 3 headers (Order, Fill, Route)
# 2. Split this csv file into 3 different ones (order.csv, fill.csv, route.csv) given the defined patterns
# 3. This 3 files are then loaded into memory (with only the required columns) and fed to `GetPrimaryTransformations`
# 4. `GetPrimaryTransformations` will align this 3 dataframes, please check `EmsiRoutesTransformations._pre_init`
#    | to see how they are aligned together.
#
# After primary transformation will run the default RST22Transction required tasks.
# ---------------------------------------
id: tr-feed-bbg-emsi-routes
name: TR Feed Bloomberg Emsi Routes

infra:
  - name: tenant-data
    type: ELASTICSEARCH
  - name: reference-data
    type: ELASTICSEARCH

audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data

parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process

controlFlows:
  - name: S3OrLocalFile
    conditionTaskName: ParametersFlowController
    trueTaskName: S3DownloadFile
    falseTaskName: LocalFile
    merge: true

  - name: NeedsQuarantine
    conditionTaskName: QuarantineCondition
    trueTaskName: QuarantinedElasticBulkTransformer
    falseTaskName: Noop
    merge: false

tasks:
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url

  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url

  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url

  - path: swarm_tasks.io.read.csv_file_filter:CsvFileFilter
    name: CsvFileFilter
    params:
      split_pattern:
        order: '^ORDER,'
        fill: '^FILL,'
        route: '^ROUTE,'
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: local_file_path

  - path: swarm_tasks.generic.get_key_value_from_dict:GetKeyValueFromDict
    name: OrderLocalFilePath
    params:
      key: "order"
    upstreamTasks:
      - taskName: CsvFileFilter
        mapped: false
        key: value

  - path: swarm_tasks.io.read.csv_file_to_dataframe_converter:CsvFileToDataframeConverter
    name: OrdersDataframe
    params:
      detect_encoding: true
      columns:
        - "Order Number"
        - "Trader UUID"
        - "Trader Notes"
    upstreamTasks:
      - taskName: OrderLocalFilePath
        mapped: false
        key: path

  - path: swarm_tasks.generic.get_key_value_from_dict:GetKeyValueFromDict
    name: FillLocalFilePath
    params:
      key: "fill"
    upstreamTasks:
      - taskName: CsvFileFilter
        mapped: false
        key: value

  - path: swarm_tasks.io.read.csv_file_to_dataframe_converter:CsvFileToDataframeConverter
    name: FillsDataframe
    params:
      detect_encoding: true
      return_transform_result: false
      columns:
        - "Order Number"
        - "Exec Seq Num"
        - "Fill As Of Date"
        - "Fill As Of Time"
        - "Account"
    upstreamTasks:
      - taskName: FillLocalFilePath
        mapped: false
        key: path

  - path: swarm_tasks.generic.get_key_value_from_dict:GetKeyValueFromDict
    name: RouteLocalFilePath
    params:
      key: "route"
    upstreamTasks:
      - taskName: CsvFileFilter
        mapped: false
        key: value

  - path: swarm_tasks.io.read.csv_file_to_dataframe_converter:CsvFileToDataframeConverter
    name: RoutesDataframe
    params:
      detect_encoding: true
      return_transform_result: false
      columns:
        - "Order Number"
        - "Route Number"
        - "Currency"
        - "Route Avg Price"
        - "Route Filled Amount"
        - "Broker"
        - "ISIN"
        - "Side"
    upstreamTasks:
      - taskName: RouteLocalFilePath
        mapped: false
        key: path

    # Tenant Input for PrimaryTransformations
  - path: swarm_tasks.primary_transformations.input_tasks.tenant_input:TenantInput
    name: TenantInput

    # ES Client Input for PrimaryTransformations
  - path: swarm_tasks.primary_transformations.input_tasks.es_client_input:EsClientInput
    name: EsClientInput
    resources:
      es_client_key: tenant-data

  # Transaction Primary Transformations
  # This part is a little tricky. It will pass 3 dataframes to
  # the primary transformations.
  # `OrdersDataframe` is a `TransformResult` because `GetPrimaryTransformations` expect
  # the first kwarg to be a `TransformResult`
  # The other two, `RoutesDataframe` and `FillsDataframe`, are normal dataframes
  - path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
    name: GetPrimaryTransformations
    upstreamTasks:
      - taskName: OrdersDataframe
        mapped: false
        key: result
      - taskName: RoutesDataframe
        mapped: false
        key: routes_dataframe
      - taskName: FillsDataframe
        mapped: false
        key: fills_dataframe
      - taskName: file_url
        mapped: false
        key: source_file_uri
      - taskName: TenantInput
        key: tenant
      - taskName: EsClientInput
        key: es_client

    # Link Parties
  - path: swarm_tasks.transform.steeleye.link.parties:LinkParties
    name: LinkParties
    params:
      identifiers_path: marketIdentifiers.parties
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: GetPrimaryTransformations
        mapped: false
        key: result

    # Link Instrument
  - path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
    name: LinkInstrument
    params:
      identifiers_path: marketIdentifiers.instrument
      venue_attribute: transactionDetails.venue
      currency_attribute: transactionDetails.priceCurrency
    resources:
      ref_data_key: reference-data
      tenant_data_key: tenant-data
    upstreamTasks:
      - taskName: GetPrimaryTransformations
        key: result

  # Instrument Fallback
  - path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
    name: InstrumentFallback
    params:
      instrument_fields_map:
        - source_field: __fb_is_created_through_fallback__
          target_field: isCreatedThroughFallback
      str_to_bool_dict:
        "true": True
        "false": False
    upstreamTasks:
      - taskName: GetPrimaryTransformations
        key: result
      - taskName: LinkInstrument
        key: link_instrument

  # MapNestedFromInstrument
  - path: swarm_tasks.transform.map.map_from_nested:MapFromNested
    name: MapNestedFromInstrument
    paramsList:
      - source_attribute: instrumentDetails.instrument
        nested_path: instrumentClassification
        target_attribute: __instrument_classification__
      - source_attribute: instrumentDetails.instrument
        nested_path: commoditiesOrEmissionAllowanceDerivativeInd
        target_attribute: __commodities_or_emission_allowance_derivative_ind__
    upstreamTasks:
    - taskName: InstrumentFallback
      key: result

  # Map tradersAlgosWaiversIndicators.commodityDerivativeIndicator from __instrument_classification__
  - path: swarm_tasks.transform.map.map_value:MapValue
    name: MapCommodityDerivativeIndicator
    paramsList:
    - source_attribute: __instrument_classification__
      target_attribute: __commodity_derivative_indicator__
      case_insensitive: true
      regex_replace_map:
        - regex: "^JT[A-Z]{2}C[A-Z]{1}$"
          replace_value: F
        - regex: "^JT[A-Z]{2}F[A-Z]{1}$"
          replace_value: F
        - regex: "^FC[A-Z]{4}$"
          replace_value: F
        - regex: "^HT[A-Z]{4}$"
          replace_value: F
        - regex: "^O[A-Z]{2}T[A-Z]{2}$"
          replace_value: F
        - regex: "^ST[A-Z]{1}T[A-Z]{2}$"
          replace_value: F
        - regex: "^ST[A-Z]{1}C[A-Z]{2}$"
          replace_value: F
      value_map:
        F: false
    upstreamTasks:
    - taskName: MapNestedFromInstrument
      key: result

  # MapConditional
  - path: swarm_tasks.transform.map.map_conditional:MapConditional
    name: MapConditional
    params:
      target_attribute: tradersAlgosWaiversIndicators.commodityDerivativeIndicator
      cases:
        - query: "~`__commodity_derivative_indicator__` | `__commodities_or_emission_allowance_derivative_ind__`"
          value: false
    upstreamTasks:
      - taskName: MapNestedFromInstrument
        key: result
      - taskName: MapCommodityDerivativeIndicator
        key: map_commodity_derivative_indicator

  # Eligibility Assessor
  - path: swarm_tasks.transform.steeleye.tr.mifir.eligibility_assessor:EligibilityAssessor
    name: EligibilityAssessor
    resources:
      es_client_key: tenant-data
      srp_client_key: reference-data
    upstreamTasks:
      - taskName: GetPrimaryTransformations
        key: result
      - taskName: InstrumentFallback
        key: link_instrument

  # Auxiliary Frame Concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: AuxiliaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - marketIdentifiers.instrument
        - marketIdentifiers.parties
        - __buyer__
        - __fb_is_created_through_fallback__
        - __tenant_lei__
        - currency_attribute
        - isin_attribute
    upstreamTasks:
      - taskName: GetPrimaryTransformations
        key: result
      - taskName: LinkParties
        key: link_parties
      - taskName: EligibilityAssessor
        key: eligibility_assessor
      - taskName: MapConditional
        key: map_conditional
      - taskName: InstrumentFallback
        key: instrument_fallback

  # Assign Meta
  - path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
    name: AssignMeta
    params:
      model_attribute: __meta_model__
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: AuxiliaryFrameConcatenator
        key: result

  # Workflow Status
  - path: swarm_tasks.transform.map.map_conditional:MapConditional
    name: WorkflowStatus
    params:
      source_attribute: '&validationErrors'
      target_attribute: workflow.validationStatus
      condition: isnull
      true_value: PASSED
      false_value: FAILED
    upstreamTasks:
      - taskName: AssignMeta
        key: result

  # Final Concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: FinalConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __meta_model__
    upstreamTasks:
      - taskName: AssignMeta
        key: result
      - taskName: AuxiliaryFrameConcatenator
        key: aux_frame_concatenator
      - taskName: WorkflowStatus
        key: workflow_status_result

  # Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformer
    params:
      action_type: create
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: FinalConcatenator
        key: transform_result
      - taskName: BatchProducer
        key: producer_result

  # Elastic Bulk Writer
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: PutIfAbsent
    params:
      payload_size: 10000000
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: ElasticBulkTransformer
        key: result

  # Quarantine Condition
  - path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
    name: QuarantineCondition
    upstreamTasks:
      - taskName: PutIfAbsent
        key: bulk_writer_result

  # Quarantined Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: QuarantinedElasticBulkTransformer
    params:
      action_type: create
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: FinalConcatenator
        key: transform_result
      - taskName: BatchProducer
        key: producer_result
      - taskName: PutIfAbsent
        key: bulk_writer_result

  # Quarantined Elastic Bulk Writer
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: QuarantinedPutIfAbsent
    params:
      payload_size: 10000000
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: QuarantinedElasticBulkTransformer
        key: result

  # Noop
  - path: swarm_tasks.control_flow.noop:Noop
    name: Noop
