# Bundle path: tr/fix/universal
# Confluence page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2866774068/RTS22+FIX+Universal
# Input: CSV file which contains S3 links to a batch of fix files.
# All the fix files are downloaded locally, and processed together in one flow run.

# Methodology - Parse FIX messages, build Pandas DataFrame from them with relevant data.
# Models populated: Order, OrderState, QuarantinedOrder

id: tr-fix-universal
name: TR FIX Universal

infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH

audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data

parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process

controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  merge: false

tasks:
  # ParametersFlowController
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
  - taskName: file_url
    key: file_url

  # S3DownloadFile
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
  - taskName: file_url
    key: file_url

  # LocalFile
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
  - taskName: file_url
    key: file_url

  # Download fix files and put their content in a data frame column
- path: swarm_tasks.io.read.fix.fix_batch_csv_downloader:FixBatchCsvDownloader
  name: FixBatchCsvDownloader
  upstreamTasks:
    - taskName: S3OrLocalFile
      key: extractor_result

  # Read .fix file, parse, validate and convert to FixParserResult dataclass
  # Note: the key has to be fix_dataframe as that's the argument name in execute()
- path: swarm_tasks.io.read.fix.fix_parser:FixParser
  name: FixParser
  upstreamTasks:
    - taskName: FixBatchCsvDownloader
      key: fix_dataframe

  # Convert FixParserResult to Pandas DataFrame
  # Note: the key has to be fix_parsed_data as that's the argument name in execute()
  # Note: be careful when using this dataframe, because PrimaryTransformations will remove cancelled orders from it
  # and reset its index
- path: swarm_tasks.io.read.fix.fix_parser_result_to_frame:FixParserResultToFrame
  name: FixParserResultToFrame
  params:
    dataframe_columns:
      - BookingType  # ff_775
      - ClientID  # ff_109
      - Currency  # ff_15
      - ExDestination  # ff_100
      - ExecID  # ff_17
      - ExecRefID  # ff_19
      - ExecType  # ff_150
      - LastCapacity  # ff_29
      - LastQty  # ff_32
      - OrdStatus  # ff_39
      - Price  # ff_44
      - SecurityDesc  # ff_107
      - SecurityID  # ff_48
      - SecurityType  # ff_167
      - SettlCurrAmt  # ff_119
      - SettlCurrency  # ff_120
      - SettlDate  # ff_64
      - Side  # ff_54
      - TransactTime  # ff_60
      # ff_ columns not in the fix protocol
      - ff_1906
      - ff_2670
      - ff_76
      - ff_8059
      - ff_8151
      - ff_8152
      - ff_8156
      - ff_8161
  upstreamTasks:
    - taskName: FixParser
      key: fix_parsed_data

  # Filter records where ExecType != 'D'
  # It also allow, any canceled order to go through ( ExecType == 'H' )
  # this orders will later be filtered out by PrimaryTransformations
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: FilterFixMessagesByExecType
  params:
    query: "`ExecType` == 'H' | (~(`ExecType`.isin(['D'])) & (`OrdStatus`.isin(['1', '2'])))"
    skip_on_empty: true
  upstreamTasks:
    - taskName: FixParserResultToFrame
      key: result

  # Tenant Input for PrimaryTransformations
- path: swarm_tasks.primary_transformations.input_tasks.tenant_input:TenantInput
  name: TenantInput

  # ES Client Input for PrimaryTransformations
- path: swarm_tasks.primary_transformations.input_tasks.es_client_input:EsClientInput
  name: EsClientInput
  resources:
    es_client_key: tenant-data

  # Primary transformations
  # Note: the keys has to be `tenant` and `es_client` as those values are expected within the flow transformations
  # Note: the primary transformations will remove cancelled orders from the dataframe and reset its index
  #      | need to be careful with frame concatenation after this task
- path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
  name: PrimaryTransformations
  upstreamTasks:
    - taskName: FilterFixMessagesByExecType
      key: result
    - taskName: TenantInput
      key: tenant
    - taskName: EsClientInput
      key: es_client

  # Link Parties
- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkParties
  params:
    identifiers_path: marketIdentifiers.parties
    add_investment_firm_covered_directive: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryTransformations
      key: result

  # Link Instrument
- path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
  name: LinkInstrument
  params:
    identifiers_path: marketIdentifiers.instrument
    venue_attribute: transactionDetails.venue
    currency_attribute: transactionDetails.priceCurrency
  resources:
    ref_data_key: reference-data
    tenant_data_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryTransformations
      key: result

  # Instrument Fallback
- path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
  name: InstrumentFallback
  params:
    instrument_fields_map:
      - source_field: __fb_securitydesc__
        target_field: instrumentFullName
      - source_field: __fb_securityid__
        target_field: instrumentIdCode
      - source_field: __fb_currency__
        target_field: notionalCurrency1
      - source_field: __fb_is_created_through_fallback__
        target_field: isCreatedThroughFallback
        # derivative fields
      - source_field: __fb_expiry_date__
        target_field: derivative.expiryDate
      - source_field: __fb_8152__
        target_field: derivative.optionType
      - source_field: __fb_strike_price__
        target_field: derivative.strikePrice
      - source_field: __fb_currency__
        target_field: derivative.strikePriceCurrency
        # ext fields
      - source_field: __fb_mone__
        target_field: ext.priceNotation
      - source_field: __fb_unit__
        target_field: ext.quantityNotation
    str_to_bool_dict:
      "true": True
      "false": False
  upstreamTasks:
    - taskName: PrimaryTransformations
      key: result
    - taskName: LinkInstrument
      key: link_instrument

  # MapNestedFromInstrument
- path: swarm_tasks.transform.map.map_from_nested:MapFromNested
  name: MapNestedFromInstrument
  paramsList:
    - source_attribute: instrumentDetails.instrument
      nested_path: instrumentClassification
      target_attribute: __instrument_classification__
    - source_attribute: instrumentDetails.instrument
      nested_path: commoditiesOrEmissionAllowanceDerivativeInd
      target_attribute: __commodities_or_emission_allowance_derivative_ind__
  upstreamTasks:
  - taskName: InstrumentFallback
    key: result

  # Map tradersAlgosWaiversIndicators.commodityDerivativeIndicator from __instrument_classification__
- path: swarm_tasks.transform.map.map_value:MapValue
  name: MapCommodityDerivativeIndicator
  paramsList:
  - source_attribute: __instrument_classification__
    target_attribute: __commodity_derivative_indicator__
    case_insensitive: true
    regex_replace_map:
      - regex: "^JT[A-Z]{2}C[A-Z]{1}$"
        replace_value: F
      - regex: "^JT[A-Z]{2}F[A-Z]{1}$"
        replace_value: F
      - regex: "^FC[A-Z]{4}$"
        replace_value: F
      - regex: "^HT[A-Z]{4}$"
        replace_value: F
      - regex: "^O[A-Z]{2}T[A-Z]{2}$"
        replace_value: F
      - regex: "^ST[A-Z]{1}T[A-Z]{2}$"
        replace_value: F
      - regex: "^ST[A-Z]{1}C[A-Z]{2}$"
        replace_value: F
    value_map:
      F: false
  upstreamTasks:
  - taskName: MapNestedFromInstrument
    key: result

  # MapConditional
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: MapConditional
  params:
    target_attribute: tradersAlgosWaiversIndicators.commodityDerivativeIndicator
    cases:
      - query: "~`__commodity_derivative_indicator__` | `__commodities_or_emission_allowance_derivative_ind__`"
        value: false
  upstreamTasks:
    - taskName: MapNestedFromInstrument
      key: result
    - taskName: MapCommodityDerivativeIndicator
      key: map_commodity_derivative_indicator

  # Eligibility Assessor
- path: swarm_tasks.transform.steeleye.tr.mifir.eligibility_assessor:EligibilityAssessor
  name: EligibilityAssessor
  resources:
    es_client_key: tenant-data
    srp_client_key: reference-data
  upstreamTasks:
    - taskName: PrimaryTransformations
      key: result
    - taskName: InstrumentFallback
      key: link_instrument

  # Auxiliary Frame Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: AuxiliaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
      - marketIdentifiers.instrument
      - marketIdentifiers.parties
      - __fb_is_created_through_fallback__
      - __fb_currency__
      - __fb_expiry_date__
      - __fb_ff_8152__
      - __fb_mone__
      - __fb_securitydesc__
      - __fb_securityid__
      - __fb_strike_price__
      - __fb_unit__
  upstreamTasks:
    - taskName: PrimaryTransformations
      key: result
    - taskName: LinkParties
      key: link_parties
    - taskName: InstrumentFallback
      key: instrument_fallback
    - taskName: EligibilityAssessor
      key: eligibility_assessor
    - taskName: MapConditional
      key: map_conditional

  # Assign Meta
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: AssignMeta
  params:
    model_attribute: __meta_model__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: AuxiliaryFrameConcatenator
      key: result

  # Workflow Status
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: WorkflowStatus
  params:
    source_attribute: '&validationErrors'
    target_attribute: workflow.validationStatus
    condition: isnull
    true_value: PASSED
    false_value: FAILED
  upstreamTasks:
    - taskName: AssignMeta
      key: result

  # Final Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: FinalConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
  upstreamTasks:
    - taskName: AssignMeta
      key: result
    - taskName: AuxiliaryFrameConcatenator
      key: aux_frame_concatenator
    - taskName: WorkflowStatus
      key: workflow_status_result

  # Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: FinalConcatenator
      key: transform_result

  # Elastic Bulk Writer
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsent
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: ElasticBulkTransformer
      key: result

  # Quarantine Condition
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineCondition
  upstreamTasks:
    - taskName: PutIfAbsent
      key: bulk_writer_result

  # Quarantined Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformer
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: FinalConcatenator
      key: transform_result
    - taskName: PutIfAbsent
      key: bulk_writer_result

  # Quarantined Elastic Bulk Writer
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsent
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: QuarantinedElasticBulkTransformer
      key: result

  # Noop
- path: swarm_tasks.control_flow.noop:Noop
  name: Noop
