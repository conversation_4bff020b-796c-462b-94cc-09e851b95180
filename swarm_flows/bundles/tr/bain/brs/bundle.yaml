# Bundle path -> tr/bain/brs
id: tr-bain-brs
name: TR Bain BRS
infra:
  - name: tenant-data
    type: ELASTICSEARCH
  - name: reference-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process
controlFlows:
  - name: S3OrLocalFile
    conditionTaskName: ParametersFlowController
    trueTaskName: S3DownloadFile
    falseTaskName: LocalFile
    merge: true
  - name: NeedsQuarantine
    conditionTaskName: QuarantineCondition
    trueTaskName: QuarantinedElasticBulkTransformer
    falseTaskName: Noop
    mapped: true
    merge: false
tasks:
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvFileSplitter
    params:
      chunksize: 10000
      delimiter: ','
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result
  - path: swarm_tasks.io.read.batch_producer:BatchProducer
    name: BatchProducer
    params:
      source_schema:
        "Buyer Country of Branch": string
        "Buyer Decision Maker DOB": string
        "Buyer Decision Maker First Name": string
        "Buyer Decision Maker ID Sub Type": string
        "Buyer Decision Maker ID Type": string
        "Buyer Decision Maker ID": string
        "Buyer Decision Maker Surname": string
        "Buyer DOB": string
        "Buyer First Name": string
        "Buyer ID Sub Type": string
        "Buyer ID Type": string
        "Buyer ID": string
        "Buyer Surname": string
        "Buyer Transmitter ID": string
        "Commodity Derivative Indicator": string
        "Complex Trade Component ID": string
        "Country of Branch": string
        "Data Category": string
        "Delivery Type": string
        "Derivative Notional Change": string
        "Executing Entity ID": string
        "Expiry Date": string
        "Firm Execution Country of Branch": string
        "Firm Execution ID Sub Type": string
        "Firm Execution ID Type": string
        "Firm Execution ID": string
        "Instrument Classification": string
        "Instrument ID type": string
        "Instrument ID": string
        "Instrument Name": string
        "Internal client identification": string
        "Investment Decision Country of Branch": string
        "Investment Decision ID Sub Type": string
        "Investment Decision ID Type": string
        "Investment Decision ID": string
        "Investment Firm Indicator": string
        "Maturity Date": string
        "Net Amount": float
        "Notional Currency 1": string
        "Notional Currency 2 type": string
        "Notional Currency 2": string
        "Option Style": string
        "Option Type": string
        "Order Transmission Indicator": string
        "OTC Post Trade Indicator": string
        "Price Currency": string
        "Price Multiplier": float
        "Price type": string
        "Price": float
        "Quantity Currency": string
        "Quantity type": string
        "Quantity": float
        "Report Status": string
        "Seller Country of Branch": string
        "Seller Decision Maker DOB": string
        "Seller Decision Maker First Name": string
        "Seller Decision Maker ID Sub Type": string
        "Seller Decision Maker ID Type": string
        "Seller Decision Maker ID": string
        "Seller Decision Maker Surname": string
        "Seller DOB": string
        "Seller First Name": string
        "Seller ID Sub Type": string
        "Seller ID Type": string
        "Seller ID": string
        "Seller Surname": string
        "Seller Transmitter ID": string
        "SFT Indicator": string
        "Short Selling Indicator": string
        "Strike Price Currency": string
        "Strike price Type": string
        "Strike Price": string
        "Submitting Entity ID": string
        "Trading Capacity": string
        "Trading Date Time": string
        "Transaction Reference Number": string
        "Underlying Index ID": string
        "Underlying Index Name": string
        "Underlying Index Term": string
        "Underlying Instrument ID": string
        "Up-Front Payment Currency": string
        "Up-Front Payment": float
        "UV Index Classification": string
        "UV Instrument Classification": string
        "Venue Transaction ID": string
        "Venue": string
        "Waiver Indicator": string

    upstreamTasks:
      - taskName: CsvFileSplitter
        mapped: true
        key: file_splitter_result
  - path: swarm_tasks.generic.currency.convert_minor_to_major:ConvertMinorToMajor
    name: ConvertCurrency
    paramsList:
      - source_ccy_attribute: Price Currency
        target_ccy_attribute: transactionDetails.priceCurrency
      - source_ccy_attribute: Quantity Currency
        target_ccy_attribute: transactionDetails.quantityCurrency
      - source_ccy_attribute: Up-Front Payment Currency
        target_ccy_attribute: transactionDetails.upFrontPaymentCurrency
      - source_price_attribute: Price
        source_ccy_attribute: Price Currency
        target_price_attribute: transactionDetails.price
      - source_price_attribute: Up-Front Payment
        source_ccy_attribute: Up-Front Payment Currency
        target_price_attribute: transactionDetails.upFrontPayment
      - source_price_attribute: Strike Price
        source_ccy_attribute: Strike Price Currency
        target_price_attribute: __strike_price__
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers:InstrumentIdentifiers
    name: InstrumentIdentifiers
    params:
      currency_attribute:
        - Price Currency
        - Notional Currency 1
      expiry_date_attribute: Expiry Date
      expiry_date_format: '%Y-%m-%d'
      instrument_classification_attribute: Instrument Classification
      isin_attribute: Instrument ID
      notional_currency_2_attribute:
        - Notional Currency 2
        - Quantity Currency
      option_strike_price_attribute: Strike Price
      option_type_attribute: Option Type
      underlying_index_name_attribute: Underlying Index Name
      underlying_index_term_attribute: Underlying Index Term
      underlying_isin_attribute: Underlying Instrument ID
      underlying_symbol_attribute: Underlying Index Name
      venue_attribute: Venue
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_attribute:MapAttribute
    name: MapAttribute
    paramsList:
      - source_attribute: Complex Trade Component ID
        target_attribute: transactionDetails.complexTradeComponentId
        strip_whitespace: true
      - source_attribute: Net Amount
        target_attribute: transactionDetails.netAmount
        cast_to: numeric.absolute
      - source_attribute: OTC Post Trade Indicator
        target_attribute: tradersAlgosWaiversIndicators.otcPostTradeIndicator
        strip_whitespace: true
        cast_to: string.list
        list_delimiter: ;
      - source_attribute: Quantity
        target_attribute: transactionDetails.quantity
        cast_to: numeric.absolute
      - source_attribute: Report Status
        target_attribute: reportDetails.reportStatus
      - source_attribute: Trading Capacity
        target_attribute: transactionDetails.tradingCapacity
        strip_whitespace: true
      - source_attribute: Venue
        target_attribute: __trading_venue__
        strip_whitespace: true
      - source_attribute: Venue Transaction ID
        target_attribute: reportDetails.tradingVenueTransactionIdCode
        strip_whitespace: true
      - source_attribute: Waiver Indicator
        target_attribute: tradersAlgosWaiversIndicators.waiverIndicator
        strip_whitespace: true
        cast_to: string.list
        list_delimiter: ;
      - source_attribute: Venue
        target_attribute: transactionDetails.venue
      - source_attribute: Country of Branch
        target_attribute: transactionDetails.branchMembershipCountry
        strip_whitespace: true
        cast_to: string.upper
        # parties file identifiers
      - source_attribute: Executing Entity ID
        target_attribute: __executing_entity_id__
        prefix: 'lei:'
      - source_attribute: Firm Execution ID
        target_attribute: __exec_within_firm__
        prefix: 'id:'
      - source_attribute: Investment Decision ID
        target_attribute: __investment_dec_within_firm__
        prefix: 'id:'
      - source_attribute: Buyer ID
        target_attribute: __buyer_id__
        prefix: 'lei:'
      - source_attribute: Seller ID
        target_attribute: __seller_id__
        prefix: 'lei:'
      - source_attribute: Buyer Decision Maker ID
        target_attribute: __buyer_decision_maker__
        prefix: 'lei:'
      - source_attribute: Seller Decision Maker ID
        target_attribute: __seller_decision_maker__
        prefix: 'lei:'
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_attribute:MapAttribute
    name: MapTransactionRefNo
    params:
      source_attribute: Transaction Reference Number
      target_attribute: reportDetails.transactionRefNo
      strip_whitespace: true
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_conditional:MapConditional
    name: MapConditional
    params:
        target_attribute: transactionDetails.ultimateVenue
        cases:
         - query: "`Venue` != ['XXXX', 'XOFF']"
           attribute: Venue
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_static:MapStatic
    name: MapStatic
    paramsList:
      - target_attribute: sourceIndex
        from_index: true
      - target_attribute: sourceKey
        from_env_var: SWARM_FILE_URL
      - target_attribute: __meta_model__
        target_value: RTS22Transaction
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_value:MapValue
    name: MapValue
    paramsList:
      - source_attribute: Short Selling Indicator
        target_attribute: tradersAlgosWaiversIndicators.shortSellingIndicator
        case_insensitive: true
        value_map:
          SESH: SESH
          SSEX: SSEX
          SELL: SELL
          NTAV: NTAV
          UNDI: NTAV
      - source_attribute: Buyer Decision Maker ID
        target_attribute: transactionDetails.buySellIndicator
        case_insensitive: true
        value_map:
          B: BUYI
          S: SELL
          Z9B60TNDM8U1G327S712: BUYI
          F5UBQYY43K0L53B9SF55: BUYI
        default_value: SELL
      - source_attribute: Price type
        target_attribute: transactionDetails.priceNotation
        case_insensitive: true
        value_map:
          BsisPts: BAPO
          MntryValAmt: MONE
          Pctg: PERC
          Yld: YIEL
      - source_attribute: Quantity type
        target_attribute: transactionDetails.quantityNotation
        case_insensitive: true
        value_map:
          MonetaryValue: MONE
          NominalValue: NOML
          Unit: UNIT
      - source_attribute: Commodity Derivative Indicator
        target_attribute: tradersAlgosWaiversIndicators.commodityDerivativeIndicator
        case_insensitive: true
        value_map:
          true: true
          false: false
      - source_attribute: SFT Indicator
        target_attribute: tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator
        case_insensitive: true
        value_map:
          true: true
          false: false
      - source_attribute: Order Transmission Indicator
        target_attribute: transmissionDetails.orderTransmissionIndicator
        case_insensitive: true
        value_map:
          true: true
          false: false
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.transform.steeleye.link.merge_market_identifiers:MergeMarketIdentifiers
    name: MergeMarketIdentifiers
    params:
      identifiers_path: marketIdentifiers
      instrument_path: marketIdentifiers.instrument
      parties_path: marketIdentifiers.parties
    upstreamTasks:
      - taskName: InstrumentIdentifiers
        mapped: true
        key: result
      - taskName: PartyIdentifiers
        mapped: true
        key: party_identifiers
  - path: swarm_tasks.transform.steeleye.tr.identifiers.party_identifiers:PartyIdentifiers
    name: PartyIdentifiers
    params:
      target_attribute: marketIdentifiers.parties
      buy_sell_side_attribute: transactionDetails.buySellIndicator
      counterparty_identifier: __counterparty_identifier__
      executing_entity_identifier: __executing_entity_id__
      execution_within_firm_identifier: __exec_within_firm__
      trader_identifier: __trader_identifier__
      investment_decision_within_firm_identifier: __investment_dec_within_firm__
      buyer_identifier: __buyer_id__
      seller_identifier: __seller_id__
      buyer_decision_maker_identifier: __buyer_decision_maker__
      seller_decision_maker_identifier: __seller_decision_maker__
      use_buy_mask_for_buyer_seller: false
      use_buy_mask_for_buyer_seller_decision_maker: false
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
      - taskName: MapValue
        mapped: true
        key: map_value
      - taskName: MapAttribute
        mapped: true
        key: map_attribute
  - path: swarm_tasks.transform.datetime.convert_datetime:ConvertDatetime
    name: TradingDateTime
    params:
      source_attribute: Trading Date Time
      target_attribute: transactionDetails.tradingDateTime
      source_attribute_format: '%Y-%m-%dT%H:%M:%S'
      convert_to: datetime
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  - path: swarm_tasks.transform.datetime.convert_datetime:ConvertDatetime
    name: Date
    params:
      source_attribute: transactionDetails.tradingDateTime
      target_attribute: date
      source_attribute_format: '%Y-%m-%dT%H:%M:%S'
      convert_to: date
    upstreamTasks:
      - taskName: TradingDateTime
        mapped: true
        key: result
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PrimaryFrameConcatenator
    params:
      orient: horizontal
    upstreamTasks:
      - taskName: Date
        mapped: true
        key: date
      - taskName: ConvertCurrency
        mapped: true
        key: convert_currency
      - taskName: InstrumentIdentifiers
        mapped: true
        key: instrument_identifiers
      - taskName: MapAttribute
        mapped: true
        key: map_attribute
      - taskName: MapConditional
        mapped: true
        key: map_conditional
      - taskName: MapStatic
        mapped: true
        key: map_static
      - taskName: MapTransactionRefNo
        mapped: true
        key: map_transaction_ref_no
      - taskName: MapValue
        mapped: true
        key: map_value
      - taskName: MergeMarketIdentifiers
        mapped: true
        key: MergeMarketIdentifiers
      - taskName: PartyIdentifiers
        mapped: true
        key: party_identifiers
      - taskName: TradingDateTime
        mapped: true
        key: trading_date_time
  - path: swarm_tasks.transform.steeleye.tr.mifir.eligibility_assessor:EligibilityAssessor
    name: EligibilityAssessor
    resources:
      es_client_key: tenant-data
      srp_client_key: reference-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
      - taskName: InstrumentPriceMultiplierOverride
        mapped: true
        key: instrument_overrides
  - path: swarm_tasks.transform.steeleye.tr.data_source.bain.brs.instrument_fallback:InstrumentFallback
    name: InstrumentFallback
    params:
       price_notation_map:
          BSISPTS: BAPO
          MNTRYVALAMT: MONE
          PCTG: PERC
          YLD: YIEL
       quantity_notation_map:
          MONETARYVALUE: MONE
          NOMINALVALUE: NOML
          UNIT: UNIT
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: primary_frame_concatenator
      - taskName: InstrumentPriceMultiplierOverride
        mapped: true
        key: instrument_overrides
      - taskName: EligibilityAssessor
        mapped: true
        key: eligibility_assessor
  - path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
    name: LinkInstrument
    params:
      identifiers_path: marketIdentifiers.instrument
      venue_attribute: __trading_venue__
      currency_attribute: transactionDetails.priceCurrency
    resources:
      ref_data_key: reference-data
      tenant_data_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_to_nested:MapToNested
    name: InstrumentStrikePriceOverride
    params:
      source_attribute: __strike_price__
      nested_path: derivative.strikePrice
      target_attribute: instrumentDetails.instrument
    upstreamTasks:
      - taskName: LinkInstrument
        mapped: true
        key: result
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: primary_frame_concatenator
  - path: swarm_tasks.transform.map.map_to_nested:MapToNested
    name: InstrumentPriceMultiplierOverride
    params:
      source_attribute: Price Multiplier
      nested_path: derivative.priceMultiplier
      target_attribute: instrumentDetails.instrument
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
      - taskName: InstrumentStrikePriceOverride
        mapped: true
        key: strikeprice_override
  - path: swarm_tasks.transform.steeleye.link.parties:LinkParties
    name: LinkParties
    params:
      identifiers_path: marketIdentifiers.parties
      add_investment_firm_covered_directive: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: AuxiliaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - marketIdentifiers.instrument
        - marketIdentifiers.parties
        - __strike_price__
        - __trading_venue__
        - __executing_entity_id__
        - __exec_within_firm__
        - __investment_dec_within_firm__
        - __buyer_id__
        - __seller_id__
        - __buyer_decision_maker__
        - __seller_decision_maker__
    upstreamTasks:
      - taskName: InstrumentFallback
        mapped: true
        key: instrument_fallback
      - taskName: LinkParties
        mapped: true
        key: link_parties
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: primary_frame_concatenator
  - path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
    name: Meta
    params:
      model_attribute: __meta_model__
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: AuxiliaryFrameConcatenator
        mapped: true
        key: result
  - path: swarm_tasks.transform.map.map_conditional:MapConditional
    name: WorkflowStatus
    params:
      source_attribute: '&validationErrors'
      target_attribute: workflow.validationStatus
      condition: isnull
      true_value: PASSED
      false_value: FAILED
    upstreamTasks:
      - taskName: Meta
        mapped: true
        key: result
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: FinalFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __meta_model__
    upstreamTasks:
      - taskName: AuxiliaryFrameConcatenator
        mapped: true
        key: auxiliary_frame_concatenator
      - taskName: Meta
        mapped: true
        key: meta
      - taskName: WorkflowStatus
        mapped: true
        key: workflow_status_result
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformer
    params:
      action_type: create
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: FinalFrameConcatenator
        mapped: true
        key: transform_result
      - taskName: BatchProducer
        mapped: true
        key: producer_result
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: PutIfAbsent
    params:
      payload_size: 10000000
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: ElasticBulkTransformer
        mapped: true
        key: result
  - path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
    name: QuarantineCondition
    upstreamTasks:
      - taskName: PutIfAbsent
        mapped: true
        key: bulk_writer_result
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: QuarantinedElasticBulkTransformer
    params:
      action_type: create
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: FinalFrameConcatenator
        mapped: true
        key: transform_result
      - taskName: BatchProducer
        mapped: true
        key: producer_result
      - taskName: PutIfAbsent
        mapped: true
        key: bulk_writer_result
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: QuarantinedPutIfAbsent
    params:
      payload_size: 10000000
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: QuarantinedElasticBulkTransformer
        mapped: true
        key: result
  - path: swarm_tasks.control_flow.noop:Noop
    name: Noop
