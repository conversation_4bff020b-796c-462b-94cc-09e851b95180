## TODO

## Assumptions
- Report Status source column maps directly to reportDetails.reportStatus

## Questions

### Outstanding to integration team
1st round:
- Should net amount be absolute? Yes
- Up Front Payment/CCY: does this depend on credit default swaps as in steeleye trade blotter?
    It is not dependent. Apply minor to major. DONE
- What is the internalOrderIdCode? 
    Not in the model, so don't use
- executionDetails.outgoingOrderAddlInfo to discuss with Storey (make task generic)
    Apply new line. Generic task Column name: column value
- record type
        No allocation
- shouldn't trading capacity be a map?
    Their values align with our model. Validation will validate this assumption.

2st round
- securitiesFinancingTxnIndicator: is not a bool?
- priceFormingData priceFormingData.tradedQuantity not in model 
- executionDetails.tradingCapacity -> transactionDetails.tradingCapacity
- executionDetails.outgoingOrderAddlInfo -> transactionDetails.outgoingOrderAddlInfo