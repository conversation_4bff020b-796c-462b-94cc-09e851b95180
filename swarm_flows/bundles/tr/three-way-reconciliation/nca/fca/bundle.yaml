# Mapping instructions here: https://steeleye.atlassian.net/wiki/spaces/IN/pages/1597472769/Mapping%2B-%2BRTS22%2B-%2BThree-Way-Reconcilliation
# Summary:
# - This flow performs Three-Way-Reconciliation against an input FCA XML file, which contains a set of transactions
#   which should have been loaded previously into SteelEye, and have been reported to ARM_UNAVISTA and then to FCA.
# Workflow:
# 1 - Fetch, read, parse, and split into chunks, the contents of the XML report
# 2 - Try to link all report transactions to the ones in the tenant's ES data;
#     - Linked transactions will be reconciled (if they were not created through 3-Way-Rec) in step 3
#     - Report transactions which did not link up, will be ingested in step 4
# 3 - Reconcile report transactions against SteelEye transactions
#     - The reconciliation process consists of filtering, parsing and comparing every report field with the
#       associated SteelEye field. for every transaction. If the fields do not match, we will add a new entry to the
#       `reconciliation.fieldBreaks` field of the RTS22Transaction. If we reconcile a transaction which was already
#       previously reconciled, any `fieldBreaks` which are not detected in the current execution, will be removed.
# 4 - Filter report transactions which are not in SteelEye, build the associated RTS22Transaction fields and load them
#     into ElasticSearch
# 5 - Trigger an email notification with the results of the 3-Way-Rec flow. The email recipients are
#     the tenant's realm's users with ADMIN and TRANSACTION_REPORTING_ADMIN permissions
# Development:
# Before running this flow please make sure that the TRADES in report are already ingested in the ENVIRONMENT you are
# going to run it on.
id: three-way-rec-nca-fca
name: Three Way Reconciliation FCA
infra:
  - name: tenant-data
    type: ELASTICSEARCH
  - name: reference-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process
controlFlows:
  - name: S3OrLocalFile
    conditionTaskName: ParametersFlowController
    trueTaskName: S3DownloadFile
    falseTaskName: LocalFile
    merge: true
  - name: IsFileTypeCSV
    conditionTaskName: IsFileTypeCSVController
    trueTaskName: CsvFileSplitterBatchCsv
    falseTaskName: XMLFileSplitter
    merge: true
  - name: FileTooBig # Depending on chunksize, if the input file is greater than X rows, it will be split into different files of X rows and uploaded to S3
    conditionTaskName: HorizontalBatchController
    trueTaskName: BatchProducer
    falseTaskName: MergeAndChunkCsvFiles
    mapped: false
    merge: false
tasks:
  # ParametersFlowController to detect input file from S3 or local machine
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  # Retrieve input file from S3
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  # Or read local file
  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url

  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: IsFileTypeCSVController
    params:
      arg_name: file_url
      ends_with: .csv
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url

  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvFileSplitterBatchCsv
    params:
      chunksize: 10000
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result
  # Read input XML file, parse it into a Pandas DataFrame and split it in batches
  # Duplicate report transactions are dropped as per mapping instructions
  - path: swarm_tasks.io.read.xml_file_splitter:XMLFileSplitter
    name: XMLFileSplitter
    params:
      chunksize: 10000
      nested_data_path: Document.FinInstrmRptgTxRpt.Tx
      drop_duplicates_subset:
        - Document.FinInstrmRptgTxRpt.Tx.New.TxId
        - Document.FinInstrmRptgTxRpt.Tx.Cxl.TxId
      keep_first_occurrence_from_sorted_column: Document.FinInstrmRptgTxRpt.Tx.SubmDt
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result

  - path: swarm_tasks.io.read.horizontal_batch_controller:HorizontalBatchController
    name: HorizontalBatchController
    params:
      max_chunk_size: &batches_chunksize 1  # max number of batches (Split files into chunks of 1 * 10000 each)
    upstreamTasks:
      - taskName: IsFileTypeCSV
        mapped: false
        key: list_of_batches
  - path: swarm_tasks.io.read.merge_and_chunk_csv_files:MergeAndChunkCsvFiles
    name: MergeAndChunkCsvFiles
    params:
      max_chunk_size: *batches_chunksize
    upstreamTasks:
      - taskName: IsFileTypeCSV
        mapped: false
        key: file_splitter_result_list
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.transform.extract_path.s3_file_list_from_frame_splitter_result_list:S3FileListFileSplitterResultList
    name: S3FileListFileSplitterResultList
    params:
      cloud_key_prefix: "flows/three-way-rec-nca-fca/batches"
      datetime_field_in_file_path: false
    upstreamTasks:
      - taskName: MergeAndChunkCsvFiles
        mapped: false
        key: file_splitter_result_list
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.write.aws.s3_upload_file:S3UploadFile
    name: S3UploadNCAResponseBatchFile
    upstreamTasks:
      - taskName: S3FileListFileSplitterResultList
        mapped: false
        key: upload_target
  # Read CSV batches and dispatch the associated dataframes to downstream tasks
  - path: swarm_tasks.io.read.batch_producer:BatchProducer
    name: BatchProducer
    upstreamTasks:
      - taskName: IsFileTypeCSV
        mapped: true
        key: file_splitter_result
  # Map SourceKey
  - path: swarm_tasks.transform.map.map_static:MapStatic
    name: MapReconciliatonSourceKey
    paramsList:
      - target_attribute: __reconciliation.nca.sourceKey
        from_env_var: SWARM_FILE_URL
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  # Link the Transactions in the report to the tenant's transactions in SteelEye
  # Output is a DataFrame where each row represents a transaction from the input XML report file
  # and the columns represent both the multiple transaction tags of the XML report and the RTS22Transaction fields
  # of the linked transaction in SteelEye
  - path: swarm_tasks.steeleye.tr.workflow.three_way_rec.link_report_transactions_to_steeleye:LinkReportTransactionsToSteelEye
    name: LinkReportTransactionsToSteelEye
    resources:
      es_client_key: tenant-data
    params:
      report_type: nca_fca
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
      - taskName: MapReconciliatonSourceKey
        mapped: true
        key: source_file_name

  # Report Transactions found in SteelEye which must be reconciled and updated
  # - Note 1: `&id` is the identifier of RTS22Transactions linked in the upstream task,
  #   and if it is not populated, it means we did not find a link between the report transaction and ES
  # - Note 2: We do not want to reconcile transactions which were created through 3-Way-Rec
  - path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
    name: ReportTransactionsInSteeleye
    params:
      query: "(`&id`.notnull() & `isCreatedThroughReconciliation.flag`.isnull())"
    upstreamTasks:
      - taskName: LinkReportTransactionsToSteelEye
        mapped: true
        key: result

  # Reconciliation
  ##############################################################################################
  # Reconcile Report Transactions which were found in SteelEye
  - path: swarm_tasks.steeleye.tr.workflow.three_way_rec.reconcile_report_transactions:ReconcileReportTransactions
    name: ReconcileReportTransactions
    resources:
      es_client_key: tenant-data
    params:
      report_type: nca_fca
    upstreamTasks:
      - taskName: ReportTransactionsInSteeleye
        mapped: true
        key: result
  # ES Transformer - Update Report Transactions which were found in SteelEye with reconciliation results
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: UpdateElasticBulkTransformer
    params:
      action_type: update
      allow_empty: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: ReconcileReportTransactions
        mapped: true
        key: transform_result
      - taskName: BatchProducer
        mapped: true
        key: producer_result
  # ES Writer - Update Report Transactions which were found in SteelEye with reconciliation results
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: UpdateReconciledTransactions
    params:
      payload_size: 5000000
      action_type: update
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: UpdateElasticBulkTransformer
      mapped: true
      key: result
  ##############################################################################################
  # Ingestion of Report Transactions not in SteelEye

  # Report Transactions that were not found in SteelEye and must be ingested
  # Except for "Cancel" transactions
  - path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
    name: ReportTransactionsAbsentFromSteeleye
    params:
      query: "`&id`.isnull() & `Document.FinInstrmRptgTxRpt.Tx.Cxl.TxId`.isnull()"
    upstreamTasks:
      - taskName: LinkReportTransactionsToSteelEye
        mapped: true
        key: result
  # Map __meta_model__ temp column for AssignMeta
  - path: swarm_tasks.transform.map.map_static:MapStatic
    name: MapStatic
    paramsList:
      - target_attribute: __meta_model__
        target_value: RTS22Transaction
      - target_attribute: reportDetails.reportStatus
        target_value: NEWT
      - target_attribute: sourceKey
        from_env_var: SWARM_FILE_URL
      - target_attribute: sourceIndex
        from_index: true
    upstreamTasks:
      - taskName: ReportTransactionsAbsentFromSteeleye
        mapped: true
        key: result
  # Build RTS22Transactions from report data which are missing from SteelEye
  - path: swarm_tasks.steeleye.tr.workflow.three_way_rec.build_transactions_from_report:BuildTransactionsFromReport
    name: BuildTransactionsFromReport
    resources:
      es_client_key: tenant-data
    params:
      report_type: nca_fca
    upstreamTasks:
      - taskName: ReportTransactionsAbsentFromSteeleye
        mapped: true
        key: result
  # Populate `date`
  - path: swarm_tasks.transform.datetime.convert_datetime:ConvertDatetime
    name: PopulateDate
    paramsList:
      - source_attribute: transactionDetails.tradingDateTime
        target_attribute: date
        convert_to: date
    upstreamTasks:
      - taskName: BuildTransactionsFromReport
        mapped: true
        key: result
  # Primary Frame Concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PrimaryFrameConcatenator
    params:
      orient: horizontal
    upstreamTasks:
      - taskName: BuildTransactionsFromReport
        mapped: true
        key: result
      - taskName: PopulateDate
        mapped: true
        key: populate_date
      - taskName: MapStatic
        mapped: true
        key: map_static
  # Assign the RTS22Transactions ES meta fields
  - path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
    name: RTS22AssignMeta
    params:
      model_attribute: __meta_model__
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  # Final Frame Concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: FinalFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __meta_model__
    upstreamTasks:
      - taskName: RTS22AssignMeta
        mapped: true
        key: result
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: frame_data
  # ES Transformer - Create Report Transactions which were NOT found in SteelEye
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: CreateElasticBulkTransformer
    params:
      action_type: create
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: FinalFrameConcatenator
        mapped: true
        key: transform_result
      - taskName: BatchProducer
        mapped: true
        key: producer_result
  # ES Writer - Create Report Transactions which were NOT found in SteelEye
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: CreateTransactions
    params:
      payload_size: 5000000
      action_type: create
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: CreateElasticBulkTransformer
      mapped: true
      key: result
  ############## Email notification ########################################

  # Fetch Tenant Configuration
  - path: swarm_tasks.steeleye.utilities.fetch_tenant_configuration:FetchTenantConfiguration
    name: FetchTenantConfiguration
    resources:
      es_client_key: tenant-data

  # Send an email notification with a summary of the 3-Way-Rec results
  - path: swarm_tasks.steeleye.tr.workflow.three_way_rec.notification_email:NotificationEmail
    name: SendResultsNotificationEmail
    params:
      report_type: nca_fca
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: FetchTenantConfiguration
        mapped: false
        key: tenant_configuration
      - taskName: LinkReportTransactionsToSteelEye
        mapped: false
        key: deduped_input_result
      - taskName: ReconcileReportTransactions
        mapped: false
        key: reconciliation_result
      - taskName: BuildTransactionsFromReport
        mapped: false
        key: ingestion_result
      - taskName: UpdateReconciledTransactions
        mapped: false
      - taskName: CreateTransactions
        mapped: false