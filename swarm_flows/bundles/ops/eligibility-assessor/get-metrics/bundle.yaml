id: tr-eligibility-assessor-get-metrics
name: TR Eligibility Assessor Get Metrics
infra:
  - name: tenant-data
    type: ELASTICSEARCH
  - name: reference-data
    type: ELASTICSEARCH
parameters:
- name: flow_args
  envVar: SWARM_FLOW_ARGS
  description: flow args

tasks:
- path: swarm_tasks.ops.eligibility_assessor.eligibility_assessor_get_metrics:EligibilityAssessorGetMetrics
  name: EligibilityAssessorMetrics
  resources:
    es_client_key: reference-data
    es_tenant_key: tenant-data
  upstreamTasks:
  - taskName: flow_args
    mapped: false
    key: flow_args
- path: swarm_tasks.io.write.aws.s3_upload_file:S3UploadFile
  name: UploadEligibilityAssessorMetricsCSV
  upstreamTasks:
    - taskName: EligibilityAssessorMetrics
      mapped: false
      key: upload_target