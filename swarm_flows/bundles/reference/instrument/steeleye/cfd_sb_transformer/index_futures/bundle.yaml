# Specs: https://steeleye.atlassian.net/wiki/spaces/PRODUCT/pages/2170454019/SteelEye+Instruments+-+CFD+SB+Transformer+-+Index+Futures

id: reference-instrument-se-cfdsb-index-transformer
name: SE Instrument CFD/SB-Index Futures Transformer
platform: true
infra:
- name: reference-data
  type: ELASTICSEARCH
parameters:
- name: flow_args
  envVar: SWARM_FLOW_ARGS
  description: "flow arguments"
tasks:
  # FLow Args Validator
  - path: swarm_tasks.generic.flow_args_validator:FlowArgsValidator
    name: FlowArgsValidator
    params:
      flow_args_model: swarm_tasks.utilities.es.fetch_record_time_window.flow_args_model.FlowArgsModel
    upstreamTasks:
      - taskName: flow_args
        key: flow_args

  # Fetch Instruments
  - path: swarm_tasks.utilities.es.fetch_record_time_window.fetch_record_time_window:FetchRecordTimeWindow
    name: FetchInstruments
    params:
      index: ".firdsInstrument,.fcaFirdsInstrument"
      body: "{'sort': [{'&timestamp': {'order': 'asc'}}], 'query': {'bool': {'must': [{'wildcard': {'instrumentClassification.text': 'ffi*'}}], 'filter': {'range': {'&timestamp': {'gte': '%gte%', 'lte': '%lte%', 'format': 'epoch_millis'}}}}}}"
    resources:
      es_key: reference-data
    upstreamTasks:
      - taskName: FlowArgsValidator
        key: flow_args

  # Batch Producer
  - path: swarm_tasks.io.read.batch_producer:BatchProducer
    name: BatchProducer
    params:
      source_schema:
        "&id": "string"
        "&key": "string"
        "&model": "string"
        "&timestamp": "string"
        "&user": "string"
        "_meta.id": "string"
        "_meta.key": "string"
        "_meta.model": "string"
        "_meta.timestamp": "string"
        "_meta.user": "string"
        "cfiAttribute1": "string"
        "cfiAttribute2": "string"
        "cfiAttribute3": "string"
        "cfiAttribute4": "string"
        "cfiCategory": "string"
        "cfiGroup": "string"
        "commoditiesOrEmissionAllowanceDerivativeInd": "string"
        "derivative.deliveryType": "string"
        "derivative.expiryDate": "string"
        "derivative.priceMultiplier": "string"
        "derivative.underlyingIndexName": "string"
        "derivative.underlyingInstruments": "string"
        "ext.aii.daily": "string"
        "ext.aii.mic": "string"
        "ext.alternativeInstrumentIdentifier": "string"
        "ext.bestExAssetClassMain": "string"
        "ext.bestExAssetClassSub": "string"
        "ext.emirEligible": "string"
        "ext.instrumentIdCodeType": "string"
        "ext.instrumentUniqueIdentifier": "string"
        "ext.mifirEligible": "string"
        "ext.onFIRDS": "string"
        "ext.pricingReferences.ICE": "string"
        "ext.pricingReferences.RIC": "string"
        "ext.venueInEEA": "string"
        "instrumentClassification": "string"
        "instrumentClassificationEMIRAssetClass": "string"
        "instrumentClassificationEMIRContractType": "string"
        "instrumentClassificationEMIRProductType": "string"
        "instrumentFullName": "string"
        "instrumentIdCode": "string"
        "issuerOrOperatorOfTradingVenueId": "string"
        "notionalCurrency1": "string"
        "sourceKey": "string"
        "venue.admissionToTradingOrFirstTradeDate": "string"
        "venue.financialInstrumentShortName": "string"
        "venue.issuerRequestForAdmissionToTrading": "string"
        "venue.terminationDate": "string"
        "venue.tradingVenue": "string"
    upstreamTasks:
      - taskName: FetchInstruments
        mapped: true
        key: file_splitter_result

  # Primary transformations
  - path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
    name: PrimaryTransformations
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result

  # CFD Records, strips SB columns
  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: CFDRecords
    params:
      except_prefix: __sb.
      strip_prefix: true
    upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: result

  # SB Records, strips CFD columns
  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: SBRecords
    params:
      except_prefix: __cfd.
      strip_prefix: true
    upstreamTasks:
    - taskName: PrimaryTransformations
      mapped: true
      key: result

  # CFD Records, removes prefix
  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixCFD
    params:
      action: strip
      prefix: __cfd.
    upstreamTasks:
    - taskName: CFDRecords
      mapped: true
      key: result

  # SB Records, removes prefix
  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixSB
    params:
      action: strip
      prefix: __sb.
    upstreamTasks:
    - taskName: SBRecords
      mapped: true
      key: result

  # Concatenates CFD and SB Records
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: VerticalConcatenator
    params:
      orient: vertical
      reset_index: true
      drop_index: true
    upstreamTasks:
    - taskName: StripPrefixCFD
      mapped: true
      key: cfd_records
    - taskName: StripPrefixSB
      mapped: true
      key: sb_records


  # Assign Meta
  - path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
    name: AssignMeta
    params:
      model_attribute: __meta_model__
    resources:
      es_client_key: reference-data
    upstreamTasks:
      - taskName: VerticalConcatenator
        mapped: true
        key: result

 # Post-meta concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PostMetaConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __meta_model__
    upstreamTasks:
    - taskName: VerticalConcatenator
      mapped: true
      key: result
    - taskName: AssignMeta
      mapped: true
      key: meta

  # ElasticBulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformer
    params:
      action_type: create
    resources:
      es_client_key: reference-data
    upstreamTasks:
      - taskName: PostMetaConcatenator
        mapped: true
        key: transform_result
      - taskName: BatchProducer
        mapped: true
        key: producer_result

  # ElasticBulk Writer
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: PutIfAbsent
    params:
      payload_size: 10000000
    resources:
      es_client_key: reference-data
    upstreamTasks:
      - taskName: ElasticBulkTransformer
        mapped: true
        key: result