id: reference-instrument-firds-fca
name: Reference Instrument Firds - FCA
infra:
- name: reference-data
  type: ELASTICSEARCH
parameters:
- name: flow_args
  envVar: SWARM_FLOW_ARGS
  description: flow args with download_date

tasks:
- path: swarm_tasks.steeleye.reference.instrument.data_source.firds.fca.get_fca_files:GetFCAFiles
  name: GetFCAFiles
  upstreamTasks:
  - taskName: flow_args
    mapped: false
    key: flow_args

- path: swarm_tasks.steeleye.reference.instrument.data_source.firds.download_file:DownloadFile
  name: DownloadFile
  params:
    type: fca
  upstreamTasks:
  - taskName: GetFCAFiles
    mapped: true
    key: url

- path: swarm_tasks.io.write.aws.s3_upload_file:S3UploadFile
  name: S3UploadFile
  upstreamTasks:
  - taskName: DownloadFile
    mapped: true
    key: upload_target
