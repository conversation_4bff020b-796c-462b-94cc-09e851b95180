���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�workflow.isReported��workflow.status��workflow.eligibility��#workflow.eligibility.executionVenue��)workflow.eligibility.totvOnExecutionVenue�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(h+N�start�K �stop�K�step�Ku��R�e]�(hhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C     �t�bhhK ��h��R�(KKK��h!�]�(�se_schema.static.mifid2��RTS22TransactionStatus����NON_REPORTABLE���R�hLhLhI�
REPORTABLE���R�hOet�bhhK ��h��R�(KKK��h!�]�(}�(�onFirds���eligible���applicableVenues�]��discountedVenues�]��firstAdmissionToTrading��pandas._libs.tslibs.nattype��__nat_unpickle���N��R��terminationDate�hb�totv���underlyingOnFirds���utotv��u}�(hW�hX�hY]�h[]�h]hbhchbhd�he�hf�u}�(hW�hX�hY]�h[]�h]hbhchbhd�he�hf�u}�(hW�hX��reason��1Non-EEA traded Derivative with uToTV constituents�hchbhd�he�hf�hY]�h[]�h]hbuhmet�bhhK ��h��R�(KKK��h!�]�(�XXXX�hxhx�pandas._libs.missing��NA���h{et�bhhK ��h��R�(KKK��h!�]�(h{h{h{h{h{et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bh+Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bh+Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bh+Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bh+Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bh+Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h:�mgr_locs��builtins��slice���K KK��R�u}�(h�hDh�h�KKK��R�u}�(h�hSh�h�KKK��R�u}�(h�huh�h�KKK��R�u}�(h�hh�h�KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.