��-      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK"��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�workflow.isReported��workflow.status��workflow.eligibility��#workflow.eligibility.executionVenue��)workflow.eligibility.totvOnExecutionVenue��&id��&key��&model��&version��
&cascadeId��&hash��&validationErrors.0.fieldPath��&validationErrors.0.message��&validationErrors.0.fieldName��&validationErrors.0.code��&validationErrors.0.category��%&validationErrors.0.modulesAffected.0��%&validationErrors.0.modulesAffected.1��%&validationErrors.0.modulesAffected.2��&validationErrors.0.action��&validationErrors.0.severity��&validationErrors.0.source��&validationErrors.1.fieldPath��&validationErrors.1.message��&validationErrors.1.fieldName��&validationErrors.1.code��&validationErrors.1.category��%&validationErrors.1.modulesAffected.0��&validationErrors.1.action��&validationErrors.1.severity��&validationErrors.1.source��
&timestamp��&user��&validationErrors�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(hHN�start�K �stop�K�step�Ku��R�e]�(hhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C  �t�bhhK ��h��R�(KKK��h!�]�(�se_schema.static.mifid2��RTS22TransactionStatus����
REPORTABLE���R�hi}�(�onFirds���eligible���reason��1Non-EEA traded Derivative with uToTV constituents��terminationDate��pandas._libs.tslibs.nattype��__nat_unpickle���N��R��totv���underlyingOnFirds���utotv���applicableVenues�]��discountedVenues�]��firstAdmissionToTrading�htuhj�pandas._libs.missing��NA���hhh�10159110:2021-11-24:NEWT��10159114:2021-11-24:NEWT��7RTS22Transaction:10159110:2021-11-24:NEWT:1640284238340��7RTS22Transaction:10159114:2021-11-24:NEWT:1640284238340��RTS22Transaction�h�et�bhhK ��h��R�(KKK��h�i8�����R�(K�<�NNNJ����J����K t�b�C              �t�bhhK ��h��R�(KKK��h!�]�(�$93929af3-23b7-48df-8f3b-2da95369622f�h��@ba025bf45c9dfc3baf86267ce21afc97b61fbe1edaebe5a6e1468aa670ebf6f5��@ef6d126b9e99a92fde8cfc4bafa6e27f069c0ec64d0b8c362a520ca0b80c3a5f�G�      G�      G�      G�      et�bhhK ��h��R�(KKK��h�f8�����R�(Kh�NNNJ����J����K t�b�C      �      ��t�bhhK ��h��R�(KKK��h!�]�(G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      et�bhhK ��h��R�(KKK��h��C      �      ��t�bhhK ��h��R�(KKK��h!�]�(G�      G�      G�      G�      G�      G�      G�      G�      et�bhhK ��h��R�(KKK��h��C      �      ��t�bhhK ��h��R�(KKK��h!�]�(G�      G�      G�      G�      G�      G�      et�bhhK ��h��R�(KKK��h��C      �      ��t�bhhK ��h��R�(KKK��h!�]�(G�      G�      G�      G�      et�bhhK ��h��R�(KKK��h��C2��}  2��}  �t�bhhK ��h��R�(KKK��h!�]�(�system�h�et�bhhK ��h��R�(KKK��h��C      �      ��t�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h&h'h(h)h*h+h,et�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h.h/h0h1et�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h3h4h5h6h7et�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h9h:h;h<et�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h>h?h@et�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hBhCet�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bhHNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hW�mgr_locs��builtins��slice���K KK��R�u}�(jp  hajq  jt  KKK��R�u}�(jp  h�jq  jt  KK	K��R�u}�(jp  h�jq  jt  K	K
K��R�u}�(jp  h�jq  jt  K
KK��R�u}�(jp  h�jq  jt  KKK��R�u}�(jp  h�jq  jt  KKK��R�u}�(jp  h�jq  jt  KKK��R�u}�(jp  h�jq  jt  KKK��R�u}�(jp  h�jq  jt  KKK��R�u}�(jp  h�jq  jt  KKK��R�u}�(jp  h�jq  jt  KKK��R�u}�(jp  h�jq  jt  KK K��R�u}�(jp  h�jq  jt  K K!K��R�u}�(jp  h�jq  jt  K!K"K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.