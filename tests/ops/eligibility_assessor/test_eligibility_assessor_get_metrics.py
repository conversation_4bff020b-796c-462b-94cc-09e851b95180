import pandas as pd
import pytest
from prefect.engine import signals
from swarm.conf import SettingsCls

from swarm_tasks.ops.eligibility_assessor.eligibility_assessor_get_metrics import (
    EligibilityAssessorGetMetrics,
)
from swarm_tasks.ops.eligibility_assessor.eligibility_assessor_get_metrics import (
    Params,
)
from swarm_tasks.ops.eligibility_assessor.eligibility_assessor_get_metrics import (
    Resources,
)


def mock_prepare_s3_upload(final_df: pd.DataFrame) -> [pd.DataFrame, None]:
    """
    returns the input dataframe to be compared in the testing function
    :param final_df: input dataframe to be returned
    :return: input dataframe
    """
    return final_df, None


class TestEligibilityAssessorGetMetrics:
    @pytest.mark.parametrize("return_dataframe", [False, True])
    def test_end_to_end(
        self,
        mocker,
        es_results,
        eligibility_assessor_result_df,
        final_df,
        final_update_df,
        return_dataframe,
    ):
        """
        Tests the task end to end, mocking any connection to ES
        and changing the upload function to return the dataframe instead of the S2File object
        """

        task = EligibilityAssessorGetMetrics(name="EligibilityAssessorGetMetrics")

        mocker.patch.object(
            SettingsCls,
            "realm",
            new_callable=mocker.PropertyMock,
            return_value="dummy.steeleye.co",
        )
        mocker.patch.object(
            SettingsCls,
            "connections",
            new_callable=mocker.PropertyMock,
            return_value={},
        )
        mocker.patch.object(
            EligibilityAssessorGetMetrics,
            "_get_non_reportable_trades",
            return_value=(es_results, "&"),
        )
        mocker.patch(
            "swarm_tasks.ops.eligibility_assessor.eligibility_assessor_get_metrics.run_eligibility_assessor",
            return_value=eligibility_assessor_result_df,
        )
        mocker.patch.object(
            EligibilityAssessorGetMetrics,
            "prepare_s3_upload",
            side_effect=mock_prepare_s3_upload,
        )

        result = task.process(
            params=Params(return_dataframe=return_dataframe), resources=Resources()
        )

        if return_dataframe:
            assert not pd.testing.assert_frame_equal(result, final_update_df)
        elif not return_dataframe:
            assert not pd.testing.assert_frame_equal(result[0], final_df)

    def test_no_trades_found(self, mocker):
        task = EligibilityAssessorGetMetrics(name="EligibilityAssessorGetMetrics")

        mocker.patch.object(
            SettingsCls,
            "realm",
            new_callable=mocker.PropertyMock,
            return_value="dummy.steeleye.co",
        )
        mocker.patch.object(
            EligibilityAssessorGetMetrics,
            "_get_non_reportable_trades",
            return_value=(
                {
                    "took": 1,
                    "timed_out": False,
                    "_shards": {"total": 1, "successful": 1, "skipped": 0, "failed": 0},
                    "hits": {"total": 0, "max_score": None, "hits": []},
                },
                "&",
            ),
        )
        with pytest.raises(signals.SKIP) as e:
            task.process(params=Params(), resources=Resources())

        assert e.match("No trades to upload.")

    def test_generate_non_reportable_trades_query(self, search_query):
        """
        Tests the query to search ES
        """
        chunk_size = 50

        task = EligibilityAssessorGetMetrics(name="EligibilityAssessorGetMetrics")
        no_last_sort_query = task.generate_non_reportable_trades_query(
            last_sort=None, chunk_size=chunk_size
        )
        last_sort_query = task.generate_non_reportable_trades_query(
            last_sort=[123456, "dummy_id"], chunk_size=chunk_size
        )

        assert last_sort_query == search_query
        search_query.pop("search_after")
        assert no_last_sort_query == search_query

    def test_get_df_from_es_results(self, es_results, es_results_df):
        """
        Tests the transformation from ES results to dataframe
        """
        task = EligibilityAssessorGetMetrics(name="EligibilityAssessorGetMetrics")
        results_df = task.get_df_from_es_results(es_results=es_results)
        assert not pd.testing.assert_frame_equal(results_df, es_results_df)
