from unittest.mock import patch

from swarm.conf import SettingsCls

from swarm_tasks.primary_transformations.input_tasks.tenant_input import TenantInput


class TestTenantInput:
    @patch.object(target=SettingsCls, attribute="realm", new="dummy.steeleye.co")
    def test_tenant_input(self):

        task = TenantInput(name="TenantInput")
        result = task.execute()

        assert result == "dummy"
