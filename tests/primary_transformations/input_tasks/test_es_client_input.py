from unittest.mock import patch

from addict import addict
from swarm.conf import SettingsCls

from swarm_tasks.primary_transformations.input_tasks.es_client_input import (
    EsClientInput,
)
from swarm_tasks.primary_transformations.input_tasks.es_client_input import Resources

EXPECTED_DICT = {"dummy_key": "dummy:value"}


class TestEsClientInput:
    @patch.object(
        target=SettingsCls,
        attribute="connections",
        new=addict.Dict({"tenant-data": EXPECTED_DICT}),
    )
    def test_es_client_input(self):

        resources = Resources(es_client_key="tenant-data")

        task = EsClientInput(name="EsClientInput", resources=resources)
        result = task.execute(resources=resources)

        assert result == EXPECTED_DICT
