import datetime
import io
import logging
from pathlib import Path
from unittest.mock import patch

import asynctest
import chardet
import pandas as pd
import pytest
from aiobotocore.response import StreamingBody
from aiobotocore.session import get_session
from botocore.exceptions import BotoCoreError
from botocore.exceptions import ClientError
from botocore.stub import Stubber
from swarm.conf import SettingsCls
from swarm.task import auditor

from swarm_tasks.cloud.aws.s3 import utils
from swarm_tasks.cloud.aws.s3.utils import async_read_csv_from_s3
from swarm_tasks.cloud.aws.s3.utils import download_file_and_convert_to_frame
from swarm_tasks.cloud.aws.s3.utils import get_csv_header_from_s3
from swarm_tasks.cloud.aws.s3.utils import s3_list_files

logger = logging.getLogger()

TEST_FILE_PATH = Path(__file__).parent.joinpath("data")
ONLY_HEADER_PATH = Path.joinpath(TEST_FILE_PATH, "only_header.csv")


def sample_df(*args, **kwargs):
    """
    Sample dataframe which is returned
    """
    return pd.DataFrame({"a": [1, 2, 3]})


def sample_df_with_non_utf_chars(*args, **kwargs):
    """
    Sample dataframe which is returned
    """
    return pd.DataFrame({"a": [1, 2, 3], "b": ["平", "仮", "名"]})


@pytest.fixture
def mock_async_read_and_convert(monkeypatch):
    """
    Mocking download_file_and_convert_to_frame coroutine
    """
    mocked_coroutine = asynctest.CoroutineMock(
        utils.download_file_and_convert_to_frame, side_effect=sample_df
    )
    monkeypatch.setattr(utils, "download_file_and_convert_to_frame", mocked_coroutine)
    return mocked_coroutine


class RawStream(io.BytesIO):
    """
    Wrapper around RawStream with overridden read function which
    can be used in an async context
    """

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass

    async def read(self, n):
        return super().read(n)


class ResponseWrapper:
    def __init__(self, content):
        self.content = content


class MockPaginator:
    def __init__(self, page_response, raise_botocore_error):
        self.paginate_response = page_response
        self.raise_botocore_error = raise_botocore_error

    def paginate(self, *args, **kwargs):

        if self.raise_botocore_error:
            raise BotoCoreError

        return self.paginate_response


class MockS3Client:
    def __init__(self, page_response, raise_botocore_error=False):
        self.page_response = page_response
        self.raise_botocore_error = raise_botocore_error

    def get_paginator(self, *args, **kwargs):
        return MockPaginator(self.page_response, self.raise_botocore_error)


class TestS3Utils(object):
    """
    Test cases for S3 utilities
    """

    def test_s3_list_files(self, mocker):
        """Test s3_list_files"""

        bucket = "onefinancial.uat.steeleye.co"
        folder_path = "mapping_tables/one-zero/mt5_user_list"

        response = [
            {
                "IsTruncated": False,
                "Name": "onefinancial.uat.steeleye.co",
                "MaxKeys": 1000,
                "Prefix": "",
                "Contents": [
                    {
                        "Key": "mapping_tables/one-zero/mt5_user_list/",
                        "LastModified": datetime.datetime(2021, 7, 5, 14, 48, 1),
                        "ETag": '"d41d8cd98f00b204e9800998ecf8427e"',
                        "Size": 0,
                        "StorageClass": "STANDARD",
                    },
                    {
                        "Key": "mapping_tables/one-zero/mt5_user_list/2020-09-28 210212 - MT5.csv",
                        "LastModified": datetime.datetime(2021, 7, 5, 14, 49, 10),
                        "ETag": '"9deccffb3f52de8e0f1a146b3e53c0d5"',
                        "Size": 81628,
                        "StorageClass": "STANDARD",
                    },
                    {
                        "Key": "mapping_tables/one-zero/mt5_user_list/2020-10-12 210159 - MT5.csv",
                        "LastModified": datetime.datetime(2021, 7, 5, 14, 49, 10),
                        "ETag": '"d7f62f6c450a7d0fffe4e26ee42a43f4"',
                        "Size": 83294,
                        "StorageClass": "STANDARD",
                    },
                ],
                "EncodingType": "url",
                "ResponseMetadata": {
                    "RequestId": "abc123",
                    "HTTPStatusCode": 200,
                    "HostId": "abc123",
                },
            }
        ]

        expected_file_list = [
            "2020-09-28 210212 - MT5.csv",
            "2020-10-12 210159 - MT5.csv",
        ]
        mocker.patch(
            "swarm_tasks.cloud.aws.s3.utils.get_s3_client"
        ).return_value = MockS3Client(response)

        files_list = s3_list_files(
            bucket=bucket, folder_path=folder_path, logger=logger
        )
        assert files_list == expected_file_list

    def test_s3_list_files_throws_exception(self, mocker):
        """Test s3_list_files"""

        bucket = "onefinancial.uat.steeleye.co"
        folder_path = "mapping_tables/one-zero/mt5_user_list"

        mocker.patch(
            "swarm_tasks.cloud.aws.s3.utils.get_s3_client"
        ).return_value = MockS3Client(None, True)

        assert (
            s3_list_files(bucket=bucket, folder_path=folder_path, logger=logger) is None
        )

    @patch.object(utils, "s3_download_file")
    def test_get_csv_header_with_header_missing(self, mock_download_csv):
        mock_download_csv.return_value = ONLY_HEADER_PATH
        result = get_csv_header_from_s3(
            s3_bucket="dummy.steeleye.co",
            s3_key="dummy_key",
            logger=logger,
        )
        assert result == ["number", "year", "quantity", "price"]

    @patch.object(utils, "s3_download_file")
    def test_get_csv_header_when_s3_download_file_fails(self, mock_download_csv):
        mock_download_csv.side_effect = ClientError(
            error_response={"Error": {"Code": "NoSuchFile"}},
            operation_name="HeadObjects",
        )
        result = get_csv_header_from_s3(
            s3_bucket="dummy.steeleye.co",
            s3_key="dummy_key",
            logger=logger,
        )
        assert result is None

    @pytest.mark.asyncio
    async def test_async_s3_read(self, mock_async_read_and_convert):
        with asynctest.mock.patch(
            "swarm_tasks.cloud.aws.s3.utils.download_file_and_convert_to_frame",
            side_effect=mock_async_read_and_convert,
        ):
            download_config = [
                {"bucket": "bucket", "key": "key_", "index": "col_name"},
                {"bucket": "bucket_1", "key": "key_1", "index": "col_name_1"},
            ]
            expected_result = [sample_df(), sample_df()]
            result = await async_read_csv_from_s3(
                download_config=download_config,
                logger=logger,
                auditor=auditor,
                file_sep="\t",
            )

            assert len(expected_result) == len(result)

            for index in range(len(expected_result)):
                assert not pd.testing.assert_frame_equal(
                    expected_result[index], result[index]
                )

    @pytest.mark.asyncio
    async def test_async_download_file_and_convert_to_frame(self):
        aio_session = get_session()
        client = await aio_session.create_client("s3").__aenter__()

        stubber = Stubber(client)
        df_bytes_object = sample_df().to_csv(index=False).encode()
        stubber.add_response(
            "get_object",
            {
                "Body": StreamingBody(
                    raw_stream=ResponseWrapper(RawStream(df_bytes_object)),
                    content_length=str(len(df_bytes_object)),
                )
            },
            expected_params={"Bucket": "bucket", "Key": "key_"},
        )
        stubber.activate()

        result = await download_file_and_convert_to_frame(
            client=client,
            bucket="bucket",
            file_name="key_",
            index="sample_index",
            sep="\t",
            detect_encoding=False,
            column_names=None,
            logger=logger,
            auditor=auditor,
        )

        await client.__aexit__(None, None, None)
        pd.testing.assert_frame_equal(
            sample_df(), result.get("sample_index", pd.DataFrame())
        )

    @pytest.mark.asyncio
    async def test_async_download_file_and_convert_to_frame_with_non_ascii_chars(
        self, mocker
    ):
        aio_session = get_session()
        client = await aio_session.create_client("s3").__aenter__()

        stubber = Stubber(client)
        df_bytes_object = (
            sample_df_with_non_utf_chars().to_csv(index=False, sep="\t").encode("cp932")
        )
        mock_chardet = mocker.patch.object(chardet, "detect")
        mock_chardet.return_value = {"encoding": "cp932"}
        stubber.add_response(
            "get_object",
            {
                "Body": StreamingBody(
                    raw_stream=ResponseWrapper(RawStream(df_bytes_object)),
                    content_length=str(len(df_bytes_object)),
                )
            },
            expected_params={"Bucket": "bucket", "Key": "key_"},
        )
        stubber.activate()
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "test.dev.steeleye.co"
        mock_snif_csv_delimiter = mocker.patch.object(utils, "sniff_delimiter")
        mock_snif_csv_delimiter.return_value = "\t"

        result = await download_file_and_convert_to_frame(
            client=client,
            bucket="bucket",
            file_name="key_",
            index="sample_index",
            sep="\t",
            detect_encoding=True,
            detect_file_sep=True,
            column_names=None,
            logger=logger,
            auditor=auditor,
        )

        await client.__aexit__(None, None, None)
        pd.testing.assert_frame_equal(
            sample_df_with_non_utf_chars(), result.get("sample_index", pd.DataFrame())
        )
