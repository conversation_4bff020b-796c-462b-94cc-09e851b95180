���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�es_col_buySell��es_col_traderFileIdentifier��es_col_sellerFileIdentifier��	es_col_id��'es_col_transactionDetails.priceCurrency��)es_col_transactionDetails.tradingCapacity��*es_col_transactionDetails.buySellIndicator��'es_col_transactionDetails.priceNotation��Oes_col_tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��es_col___isin__��'es_col_priceFormingData.initialQuantity��!es_col_executionDetails.orderType��'es_col_executionDetails.tradingCapacity��(es_col_executionDetails.buySellIndicator��*es_col_transactionDetails.quantityNotation��(es_col_transactionDetails.settlementDate��es_col___instr_full_name__��)es_col_priceFormingData.remainingQuantity��es_col_priceFormingData.price��&es_col_executionDetails.validityPeriod��es_col_transactionDetails.price��Fes_col_tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��!es_col_counterpartyFileIdentifier��es_col_buyerFileIdentifier�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C       �t�bh>Nu��R�e]�(hhK ��h��R�(KKK��h!�]�(�2��
id:8082391�G�      �1245728��EUR��DEAL��SELL��MONE��
id:7292237��DE000A2GS5D8��MKT��DEAL��SELL��UNIT��
2022-02-09��DERMAPHARM HOLDING SE(DMP GR)�]��GTCV�aG�      G�      G�      �id:12�et�bhhK ��h��R�(KKK��h�f8�����R�(KhLNNNJ����J����K t�b�C     4�@     4�@�����iQ@�t�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(h)h*h+h,h-h.h0h1h2h3h4h5h8h9h:h;h<et�bh>Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h/h6h7et�bh>Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hU�mgr_locs�hhK ��h��R�(KK��h�i8�����R�(KhLNNNJ����J����K t�b�C�                                                                	                     
                                                               �t�bu}�(h�hmh�hhK ��h��R�(KK��h��C
                     �t�bueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.