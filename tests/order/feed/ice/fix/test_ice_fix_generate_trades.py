from pathlib import Path

import pandas as pd
import pytest
from prefect.engine.signals import SKIP
from se_trades_tasks.order_and_tr.fix.dataclasses.fix_parser_dataclasses import (
    FixParserResult,
)
from se_trades_tasks.order_and_tr.fix.dataclasses.fix_parser_dataclasses import FixTag

from swarm_tasks.tr.feed.ice.fix.ice_fix_generate_trades import IceFixGenerateTrades
from swarm_tasks.tr.feed.ice.fix.ice_fix_generate_trades import Params

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
EXPECTED_FILE_PATH_SINGLE = TEST_FILES_DIR.joinpath(r"expected_data_single.csv")
EXPECTED_FILE_PATH_MULTI_LEG = TEST_FILES_DIR.joinpath(r"expected_data_multi_leg.csv")
EXPECTED_FILE_CREATE_CANCELS = TEST_FILES_DIR.joinpath(
    r"expected_data_create_cancels.csv"
)


class TestIceFixGenerateTrades:
    """
    Test Ice Fix Generate Trades logic.
    """

    def test_empty_data_scenario(self):
        params = Params(transactions_only=False)
        with pytest.raises(SKIP):
            task = IceFixGenerateTrades(name="test_task", params=params)
            task.execute(fix_parser_data=list(), params=params)

    def test_order_single_leg_record_generation(self):
        params = Params(transactions_only=False)
        task = IceFixGenerateTrades(name="test_task", params=params)
        fix_parser_data = [
            FixParserResult(
                message_string="8=FIX.4.49=64935=AE34=163849=ICE52=********-13:33:23.93056=1462157=SteelEye_SIGMA11=236427417=236427522=831=64.0032=237=236427439=248=EGD SMX0022!54=255=625553960=********-13:28:29.14675=********77=O150=F207=IFED453=11448=swillems2452=11447=D448=Glencore Commodities Ltd.452=13447=D448=207452=56447=D448=Sigma Broking LTD-Broker452=1447=D448=14621452=61447=D448=aholmes3452=12447=D448=2172452=4447=D448=GCMZ3STW452=51447=D448=Mizuho Securities452=60447=D448=MZF452=63447=D448=W452=54447=D461=FXXXXX487=0552=1570=N571=309019828=K856=0916=20221101917=202211301031=W9018=29022=19028=09064=09413=310=184",
                s3_file_url="s3://puneeth.uat.steeleye.co/fix/order-feed-ice-fix/05cb82f0c52dd4b5de45137e996e5a1c11b4288bb6938aa121009ae8351775c8_1638.fix",
                tags={
                    8: [
                        FixTag(
                            tag=8,
                            name="BeginString",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="FIX.4.4",
                        )
                    ],
                    9: [
                        FixTag(
                            tag=9,
                            name="BodyLength",
                            description="",
                            type={
                                "Name": "Length",
                                "BaseType": "int",
                                "Description": "int field representing the length in bytes. Value must be positive.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:nonNegativeInteger",
                                    "Description": "int field representing the length in bytes. Value must be positive.",
                                },
                                "Added": "FIX.4.3",
                            },
                            value=649,
                        )
                    ],
                    35: [
                        FixTag(
                            tag=35,
                            name="MsgType",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="AE",
                        )
                    ],
                    34: [
                        FixTag(
                            tag=34,
                            name="MsgSeqNum",
                            description="",
                            type={
                                "Name": "SeqNum",
                                "BaseType": "int",
                                "Description": "int field representing a message sequence number. Value must be positive.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:positiveInteger",
                                    "Description": "int field representing a message sequence number. Value must be positive.",
                                },
                                "Added": "FIX.4.3",
                            },
                            value=1638,
                        )
                    ],
                    49: [
                        FixTag(
                            tag=49,
                            name="SenderCompID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="ICE",
                        )
                    ],
                    52: [
                        FixTag(
                            tag=52,
                            name="SendingTime",
                            description="",
                            type={
                                "Name": "UTCTimestamp",
                                "BaseType": "String",
                                "Description": "string field representing Time/date combination represented in UTC (Universal Time Coordinated, also known as 'GMT') in either YYYYMMDD-HH:MM:SS (whole seconds) or YYYYMMDD-HH:MM:SS.sss (milliseconds) format, colons, dash, and period required.\nValid values:\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second) (without milliseconds).\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second), sss=000-999 (indicating milliseconds).\nLeap Seconds: Note that UTC includes corrections for leap seconds, which are inserted to account for slowing of the rotation of the earth. Leap second insertion is declared by the International Earth Rotation Service (IERS) and has, since 1972, only occurred on the night of Dec. 31 or Jun 30. The IERS considers March 31 and September 30 as secondary dates for leap second insertion, but has never utilized these dates. During a leap second insertion, a UTCTimestamp field may read '********-23:59:59', '********-23:59:60', '********-00:00:00'. (see http://tycho.usno.navy.mil/leapsec.html)",
                                "Example": "TransactTm='********-09:30:47'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:dateTime",
                                    "Description": "string field representing Time/date combination represented in UTC (Universal Time Coordinated, also known as 'GMT') in either YYYY-MM-DDTHH:MM:SS (whole seconds) or YYYY-MM-DDTHH:MM:SS.sss (milliseconds) format as specified in ISO 8601.\nValid values:\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second) (without milliseconds).\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second), sss=000-999 (indicating milliseconds).\nLeap Seconds: Note that UTC includes corrections for leap seconds, which are inserted to account for slowing of the rotation of the earth. Leap second insertion is declared by the International Earth Rotation Service (IERS) and has, since 1972, only occurred on the night of Dec. 31 or Jun 30. The IERS considers March 31 and September 30 as secondary dates for leap second insertion, but has never utilized these dates. During a leap second insertion, a UTCTimestamp field may read '1998-12-31T23:59:59', '1998-12-31T23:59:60', '1999-01-01T00:00:00'. (see http://tycho.usno.navy.mil/leapsec.html)",
                                    "Example": "TransactTm='2001-12-17T09:30:47-05:00'",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="********-13:33:23.930",
                        )
                    ],
                    56: [
                        FixTag(
                            tag=56,
                            name="TargetCompID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="14621",
                        )
                    ],
                    57: [
                        FixTag(
                            tag=57,
                            name="TargetSubID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="SteelEye_SIGMA",
                        )
                    ],
                    11: [
                        FixTag(
                            tag=11,
                            name="ClOrdID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="2364274",
                        )
                    ],
                    17: [
                        FixTag(
                            tag=17,
                            name="ExecID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="2364275",
                        )
                    ],
                    22: [
                        FixTag(
                            tag=22,
                            name="SecurityIDSource",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="8",
                        )
                    ],
                    31: [
                        FixTag(
                            tag=31,
                            name="LastPx",
                            description="",
                            type={
                                "Name": "Price",
                                "BaseType": "float",
                                "Description": "float field representing a price. Note the number of decimal places may vary. For certain asset classes prices may be negative values. For example, prices for options strategies can be negative under certain market conditions. Refer to Volume 7: FIX Usage by Product for asset classes that support negative price values.",
                                "Example": "Strk='47.50'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:decimal",
                                    "Description": "float field representing a price. Note the number of decimal places may vary. For certain asset classes prices may be negative values. For example, prices for options strategies can be negative under certain market conditions. Refer to Volume 7: FIX Usage by Product for asset classes that support negative price values.",
                                },
                                "Added": "FIX.4.2",
                            },
                            value=64.0,
                        )
                    ],
                    32: [
                        FixTag(
                            tag=32,
                            name="LastQty",
                            description="",
                            type={
                                "Name": "Qty",
                                "BaseType": "float",
                                "Description": "float field capable of storing either a whole number (no decimal places) of 'shares' (securities denominated in whole units) or a decimal value containing decimal places for non-share quantity asset classes (securities denominated in fractional units).",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:decimal",
                                    "Description": "float field capable of storing either a whole number (no decimal places) of 'shares' (securities denominated in whole units) or a decimal value containing decimal places for non-share quantity asset classes (securities denominated in fractional units).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value=2.0,
                        )
                    ],
                    37: [
                        FixTag(
                            tag=37,
                            name="OrderID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="2364274",
                        )
                    ],
                    39: [
                        FixTag(
                            tag=39,
                            name="OrdStatus",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="2",
                        )
                    ],
                    48: [
                        FixTag(
                            tag=48,
                            name="SecurityID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="EGD SMX0022!",
                        )
                    ],
                    54: [
                        FixTag(
                            tag=54,
                            name="Side",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="2",
                        )
                    ],
                    55: [
                        FixTag(
                            tag=55,
                            name="Symbol",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="6255539",
                        )
                    ],
                    60: [
                        FixTag(
                            tag=60,
                            name="TransactTime",
                            description="",
                            type={
                                "Name": "UTCTimestamp",
                                "BaseType": "String",
                                "Description": "string field representing Time/date combination represented in UTC (Universal Time Coordinated, also known as 'GMT') in either YYYYMMDD-HH:MM:SS (whole seconds) or YYYYMMDD-HH:MM:SS.sss (milliseconds) format, colons, dash, and period required.\nValid values:\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second) (without milliseconds).\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second), sss=000-999 (indicating milliseconds).\nLeap Seconds: Note that UTC includes corrections for leap seconds, which are inserted to account for slowing of the rotation of the earth. Leap second insertion is declared by the International Earth Rotation Service (IERS) and has, since 1972, only occurred on the night of Dec. 31 or Jun 30. The IERS considers March 31 and September 30 as secondary dates for leap second insertion, but has never utilized these dates. During a leap second insertion, a UTCTimestamp field may read '********-23:59:59', '********-23:59:60', '********-00:00:00'. (see http://tycho.usno.navy.mil/leapsec.html)",
                                "Example": "TransactTm='********-09:30:47'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:dateTime",
                                    "Description": "string field representing Time/date combination represented in UTC (Universal Time Coordinated, also known as 'GMT') in either YYYY-MM-DDTHH:MM:SS (whole seconds) or YYYY-MM-DDTHH:MM:SS.sss (milliseconds) format as specified in ISO 8601.\nValid values:\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second) (without milliseconds).\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second), sss=000-999 (indicating milliseconds).\nLeap Seconds: Note that UTC includes corrections for leap seconds, which are inserted to account for slowing of the rotation of the earth. Leap second insertion is declared by the International Earth Rotation Service (IERS) and has, since 1972, only occurred on the night of Dec. 31 or Jun 30. The IERS considers March 31 and September 30 as secondary dates for leap second insertion, but has never utilized these dates. During a leap second insertion, a UTCTimestamp field may read '1998-12-31T23:59:59', '1998-12-31T23:59:60', '1999-01-01T00:00:00'. (see http://tycho.usno.navy.mil/leapsec.html)",
                                    "Example": "TransactTm='2001-12-17T09:30:47-05:00'",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="********-13:28:29.146",
                        )
                    ],
                    75: [
                        FixTag(
                            tag=75,
                            name="TradeDate",
                            description="",
                            type={
                                "Name": "LocalMktDate",
                                "BaseType": "String",
                                "Description": "string field represening a Date of Local Market (as oppose to UTC) in YYYYMMDD format. This is the 'normal' date field used by the FIX Protocol.\nValid values:\nYYYY = 0000-9999, MM = 01-12, DD = 01-31.",
                                "Example": "BizDate='2003-09-10'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:date",
                                    "Description": "string field represening a Date of Local Market (as oppose to UTC) in YYYY-MM-DD format per the ISO 8601 standard. This is the 'normal' date field used by the FIX Protocol.\nValid values:\nYYYY = 0000-9999, MM = 01-12, DD = 01-31.",
                                    "Example": "BizDate='2003-09-10'",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="********",
                        )
                    ],
                    77: [
                        FixTag(
                            tag=77,
                            name="PositionEffect",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="O",
                        )
                    ],
                    150: [
                        FixTag(
                            tag=150,
                            name="ExecType",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="F",
                        )
                    ],
                    207: [
                        FixTag(
                            tag=207,
                            name="SecurityExchange",
                            description="",
                            type={
                                "Name": "Exchange",
                                "BaseType": "String",
                                "Description": "string field representing a market or exchange using ISO 10383 Market Identifier Code (MIC) values (see'Appendix 6-C).",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".*",
                                    "Description": "string field representing a market or exchange using ISO 10383 Market Identifier Code (MIC) values (see'Appendix 6-C).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="IFED",
                        )
                    ],
                    453: [
                        FixTag(
                            tag=453,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="11",
                        )
                    ],
                    448: [
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="swillems2",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="Glencore Commodities Ltd.",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="207",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="Sigma Broking LTD-Broker",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="14621",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="aholmes3",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="2172",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="GCMZ3STW",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="Mizuho Securities",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="MZF",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="W",
                        ),
                    ],
                    452: [
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=11,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=13,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=56,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=1,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=61,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=12,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=4,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=51,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=60,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=63,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=54,
                        ),
                    ],
                    447: [
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                    ],
                    461: [
                        FixTag(
                            tag=461,
                            name="CFICode",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="FXXXXX",
                        )
                    ],
                    487: [
                        FixTag(
                            tag=487,
                            name="TradeReportTransType",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=0,
                        )
                    ],
                    552: [
                        FixTag(
                            tag=552,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="1",
                        )
                    ],
                    570: [
                        FixTag(
                            tag=570,
                            name="PreviouslyReported",
                            description="",
                            type={
                                "Name": "Boolean",
                                "BaseType": "char",
                                "Description": "char field containing one of two values:\n'Y' = True/Yes\n'N' = False/No",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": "[YN]{1}",
                                    "Description": "char field containing one of two values:\n'Y' = True/Yes\n'N' = False/No",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="N",
                        )
                    ],
                    571: [
                        FixTag(
                            tag=571,
                            name="TradeReportID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="309019",
                        )
                    ],
                    828: [
                        FixTag(
                            tag=828,
                            name="TrdType",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value="K",
                        )
                    ],
                    856: [
                        FixTag(
                            tag=856,
                            name="TradeReportType",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=0,
                        )
                    ],
                    916: [
                        FixTag(
                            tag=916,
                            name="StartDate",
                            description="",
                            type={
                                "Name": "LocalMktDate",
                                "BaseType": "String",
                                "Description": "string field represening a Date of Local Market (as oppose to UTC) in YYYYMMDD format. This is the 'normal' date field used by the FIX Protocol.\nValid values:\nYYYY = 0000-9999, MM = 01-12, DD = 01-31.",
                                "Example": "BizDate='2003-09-10'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:date",
                                    "Description": "string field represening a Date of Local Market (as oppose to UTC) in YYYY-MM-DD format per the ISO 8601 standard. This is the 'normal' date field used by the FIX Protocol.\nValid values:\nYYYY = 0000-9999, MM = 01-12, DD = 01-31.",
                                    "Example": "BizDate='2003-09-10'",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="20221101",
                        )
                    ],
                    917: [
                        FixTag(
                            tag=917,
                            name="EndDate",
                            description="",
                            type={
                                "Name": "LocalMktDate",
                                "BaseType": "String",
                                "Description": "string field represening a Date of Local Market (as oppose to UTC) in YYYYMMDD format. This is the 'normal' date field used by the FIX Protocol.\nValid values:\nYYYY = 0000-9999, MM = 01-12, DD = 01-31.",
                                "Example": "BizDate='2003-09-10'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:date",
                                    "Description": "string field represening a Date of Local Market (as oppose to UTC) in YYYY-MM-DD format per the ISO 8601 standard. This is the 'normal' date field used by the FIX Protocol.\nValid values:\nYYYY = 0000-9999, MM = 01-12, DD = 01-31.",
                                    "Example": "BizDate='2003-09-10'",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="20221130",
                        )
                    ],
                    1031: [
                        FixTag(
                            tag=1031,
                            name="CustOrderHandlingInst",
                            description="",
                            type={
                                "Name": "MultipleStringValue",
                                "BaseType": "String",
                                "Description": "string field containing one or more space delimited multiple character values (e.g. |277=AV AN A| ).",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".+(\\s.+)*",
                                    "Description": "string field containing one or more space delimited multiple character values (e.g. |277=AV AN A| ).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="W",
                        )
                    ],
                    9018: [
                        FixTag(
                            tag=9018,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="2",
                        )
                    ],
                    9022: [
                        FixTag(
                            tag=9022,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="1",
                        )
                    ],
                    9028: [
                        FixTag(
                            tag=9028,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="0",
                        )
                    ],
                    9064: [
                        FixTag(
                            tag=9064,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="0",
                        )
                    ],
                    9413: [
                        FixTag(
                            tag=9413,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="3",
                        )
                    ],
                    10: [
                        FixTag(
                            tag=10,
                            name="CheckSum",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="184",
                        )
                    ],
                },
            )
        ]
        result = task.execute(fix_parser_data=fix_parser_data, params=params)
        result_df = result.frame.fillna(pd.NA).astype(str)
        expected_df = pd.read_csv(EXPECTED_FILE_PATH_SINGLE).fillna(pd.NA).astype(str)

        assert not pd.testing.assert_frame_equal(result_df, expected_df)

    def test_order_multi_leg_record_generation(self):
        params = Params(transactions_only=False)
        task = IceFixGenerateTrades(name="test_task", params=params)
        fix_parser_data = [
            FixParserResult(
                message_string="8=FIX.4.49=199835=AE34=173549=ICE52=********-14:21:03.53456=1462157=SteelEye_SIGMA11=7581581917=7581582122=831=1.8132=10037=7581581939=248=BRN FMU0023_OMXA000000000207262354=155=543085460=********-14:19:24.09375=********77=O150=F207=IFEU453=12448=emifsud1452=11447=D448=Goldman Sachs International452=13447=D448=664452=56447=D448=Sigma Broking LTD-Broker452=1447=D448=14621452=61447=D448=dwilson34452=12447=D448=664452=35447=D448=662452=4447=D448=EUCRP01452=51447=D448=Goldman Sachs452=60447=D448=GSF452=63447=D448=F452=54447=D461=OMXXXX487=0552=1555=4600=5430854602=BRN FMU0023_OMCA0000083502072623603=8608=OCXXXX612=83.509404=106835518616=IFEU624=1637=14.61687=1001017=65654=758158239019=1009023=19020=202309019021=20230930539=6524=662525=D538=4524=EUCRP01525=D538=51524=Goldman Sachs525=D538=60524=GSF525=D538=63524=F525=D538=54524=664525=D538=359426=W600=5430845602=BRN FMM0023!603=8608=FXXXXX616=IFEU624=1637=85.50687=631017=63654=758158249019=639023=19020=202306019021=20230630539=6524=662525=D538=4524=EUCRP01525=D538=51524=Goldman Sachs525=D538=60524=GSF525=D538=63524=F525=D538=54524=664525=D538=359426=W600=5430845602=BRN FMM0023_OMCA0000085502042523603=8608=OCXXXX612=85.509404=105769975616=IFEU624=2637=12.80687=1001017=63654=758158229019=1009023=19020=202306019021=20230630539=6524=662525=D538=4524=EUCRP01525=D538=51524=Goldman Sachs525=D538=60524=GSF525=D538=63524=F525=D538=54524=664525=D538=359426=W600=5430854602=BRN FMU0023!603=8608=FXXXXX616=IFEU624=2637=83.50687=651017=65654=758158259019=659023=19020=202309019021=20230930539=6524=662525=D538=4524=EUCRP01525=D538=51524=Goldman Sachs525=D538=60524=GSF525=D538=63524=F525=D538=54524=664525=D538=359426=W570=N571=349866762=63828=K856=0916=20230901917=202309301031=W8013=LRGS9018=1009022=19028=09064=09403=305012579413=39707=110=065",
                s3_file_url="s3://puneeth.uat.steeleye.co/fix/order-feed-ice-fix/499bd892a02b8498d72073a1018c7c260f66bc8328ce0bdfd076f28fcda57f75_1735.fix",
                tags={
                    8: [
                        FixTag(
                            tag=8,
                            name="BeginString",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="FIX.4.4",
                        )
                    ],
                    9: [
                        FixTag(
                            tag=9,
                            name="BodyLength",
                            description="",
                            type={
                                "Name": "Length",
                                "BaseType": "int",
                                "Description": "int field representing the length in bytes. Value must be positive.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:nonNegativeInteger",
                                    "Description": "int field representing the length in bytes. Value must be positive.",
                                },
                                "Added": "FIX.4.3",
                            },
                            value=1998,
                        )
                    ],
                    35: [
                        FixTag(
                            tag=35,
                            name="MsgType",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="AE",
                        )
                    ],
                    34: [
                        FixTag(
                            tag=34,
                            name="MsgSeqNum",
                            description="",
                            type={
                                "Name": "SeqNum",
                                "BaseType": "int",
                                "Description": "int field representing a message sequence number. Value must be positive.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:positiveInteger",
                                    "Description": "int field representing a message sequence number. Value must be positive.",
                                },
                                "Added": "FIX.4.3",
                            },
                            value=1735,
                        )
                    ],
                    49: [
                        FixTag(
                            tag=49,
                            name="SenderCompID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="ICE",
                        )
                    ],
                    52: [
                        FixTag(
                            tag=52,
                            name="SendingTime",
                            description="",
                            type={
                                "Name": "UTCTimestamp",
                                "BaseType": "String",
                                "Description": "string field representing Time/date combination represented in UTC (Universal Time Coordinated, also known as 'GMT') in either YYYYMMDD-HH:MM:SS (whole seconds) or YYYYMMDD-HH:MM:SS.sss (milliseconds) format, colons, dash, and period required.\nValid values:\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second) (without milliseconds).\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second), sss=000-999 (indicating milliseconds).\nLeap Seconds: Note that UTC includes corrections for leap seconds, which are inserted to account for slowing of the rotation of the earth. Leap second insertion is declared by the International Earth Rotation Service (IERS) and has, since 1972, only occurred on the night of Dec. 31 or Jun 30. The IERS considers March 31 and September 30 as secondary dates for leap second insertion, but has never utilized these dates. During a leap second insertion, a UTCTimestamp field may read '********-23:59:59', '********-23:59:60', '********-00:00:00'. (see http://tycho.usno.navy.mil/leapsec.html)",
                                "Example": "TransactTm='********-09:30:47'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:dateTime",
                                    "Description": "string field representing Time/date combination represented in UTC (Universal Time Coordinated, also known as 'GMT') in either YYYY-MM-DDTHH:MM:SS (whole seconds) or YYYY-MM-DDTHH:MM:SS.sss (milliseconds) format as specified in ISO 8601.\nValid values:\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second) (without milliseconds).\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second), sss=000-999 (indicating milliseconds).\nLeap Seconds: Note that UTC includes corrections for leap seconds, which are inserted to account for slowing of the rotation of the earth. Leap second insertion is declared by the International Earth Rotation Service (IERS) and has, since 1972, only occurred on the night of Dec. 31 or Jun 30. The IERS considers March 31 and September 30 as secondary dates for leap second insertion, but has never utilized these dates. During a leap second insertion, a UTCTimestamp field may read '1998-12-31T23:59:59', '1998-12-31T23:59:60', '1999-01-01T00:00:00'. (see http://tycho.usno.navy.mil/leapsec.html)",
                                    "Example": "TransactTm='2001-12-17T09:30:47-05:00'",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="********-14:21:03.534",
                        )
                    ],
                    56: [
                        FixTag(
                            tag=56,
                            name="TargetCompID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="14621",
                        )
                    ],
                    57: [
                        FixTag(
                            tag=57,
                            name="TargetSubID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="SteelEye_SIGMA",
                        )
                    ],
                    11: [
                        FixTag(
                            tag=11,
                            name="ClOrdID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="75815819",
                        )
                    ],
                    17: [
                        FixTag(
                            tag=17,
                            name="ExecID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="75815821",
                        )
                    ],
                    22: [
                        FixTag(
                            tag=22,
                            name="SecurityIDSource",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="8",
                        )
                    ],
                    31: [
                        FixTag(
                            tag=31,
                            name="LastPx",
                            description="",
                            type={
                                "Name": "Price",
                                "BaseType": "float",
                                "Description": "float field representing a price. Note the number of decimal places may vary. For certain asset classes prices may be negative values. For example, prices for options strategies can be negative under certain market conditions. Refer to Volume 7: FIX Usage by Product for asset classes that support negative price values.",
                                "Example": "Strk='47.50'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:decimal",
                                    "Description": "float field representing a price. Note the number of decimal places may vary. For certain asset classes prices may be negative values. For example, prices for options strategies can be negative under certain market conditions. Refer to Volume 7: FIX Usage by Product for asset classes that support negative price values.",
                                },
                                "Added": "FIX.4.2",
                            },
                            value=1.81,
                        )
                    ],
                    32: [
                        FixTag(
                            tag=32,
                            name="LastQty",
                            description="",
                            type={
                                "Name": "Qty",
                                "BaseType": "float",
                                "Description": "float field capable of storing either a whole number (no decimal places) of 'shares' (securities denominated in whole units) or a decimal value containing decimal places for non-share quantity asset classes (securities denominated in fractional units).",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:decimal",
                                    "Description": "float field capable of storing either a whole number (no decimal places) of 'shares' (securities denominated in whole units) or a decimal value containing decimal places for non-share quantity asset classes (securities denominated in fractional units).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value=100.0,
                        )
                    ],
                    37: [
                        FixTag(
                            tag=37,
                            name="OrderID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="75815819",
                        )
                    ],
                    39: [
                        FixTag(
                            tag=39,
                            name="OrdStatus",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="2",
                        )
                    ],
                    48: [
                        FixTag(
                            tag=48,
                            name="SecurityID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="BRN FMU0023_OMXA0000000002072623",
                        )
                    ],
                    54: [
                        FixTag(
                            tag=54,
                            name="Side",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="1",
                        )
                    ],
                    55: [
                        FixTag(
                            tag=55,
                            name="Symbol",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="5430854",
                        )
                    ],
                    60: [
                        FixTag(
                            tag=60,
                            name="TransactTime",
                            description="",
                            type={
                                "Name": "UTCTimestamp",
                                "BaseType": "String",
                                "Description": "string field representing Time/date combination represented in UTC (Universal Time Coordinated, also known as 'GMT') in either YYYYMMDD-HH:MM:SS (whole seconds) or YYYYMMDD-HH:MM:SS.sss (milliseconds) format, colons, dash, and period required.\nValid values:\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second) (without milliseconds).\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second), sss=000-999 (indicating milliseconds).\nLeap Seconds: Note that UTC includes corrections for leap seconds, which are inserted to account for slowing of the rotation of the earth. Leap second insertion is declared by the International Earth Rotation Service (IERS) and has, since 1972, only occurred on the night of Dec. 31 or Jun 30. The IERS considers March 31 and September 30 as secondary dates for leap second insertion, but has never utilized these dates. During a leap second insertion, a UTCTimestamp field may read '********-23:59:59', '********-23:59:60', '********-00:00:00'. (see http://tycho.usno.navy.mil/leapsec.html)",
                                "Example": "TransactTm='********-09:30:47'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:dateTime",
                                    "Description": "string field representing Time/date combination represented in UTC (Universal Time Coordinated, also known as 'GMT') in either YYYY-MM-DDTHH:MM:SS (whole seconds) or YYYY-MM-DDTHH:MM:SS.sss (milliseconds) format as specified in ISO 8601.\nValid values:\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second) (without milliseconds).\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second), sss=000-999 (indicating milliseconds).\nLeap Seconds: Note that UTC includes corrections for leap seconds, which are inserted to account for slowing of the rotation of the earth. Leap second insertion is declared by the International Earth Rotation Service (IERS) and has, since 1972, only occurred on the night of Dec. 31 or Jun 30. The IERS considers March 31 and September 30 as secondary dates for leap second insertion, but has never utilized these dates. During a leap second insertion, a UTCTimestamp field may read '1998-12-31T23:59:59', '1998-12-31T23:59:60', '1999-01-01T00:00:00'. (see http://tycho.usno.navy.mil/leapsec.html)",
                                    "Example": "TransactTm='2001-12-17T09:30:47-05:00'",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="********-14:19:24.093",
                        )
                    ],
                    75: [
                        FixTag(
                            tag=75,
                            name="TradeDate",
                            description="",
                            type={
                                "Name": "LocalMktDate",
                                "BaseType": "String",
                                "Description": "string field represening a Date of Local Market (as oppose to UTC) in YYYYMMDD format. This is the 'normal' date field used by the FIX Protocol.\nValid values:\nYYYY = 0000-9999, MM = 01-12, DD = 01-31.",
                                "Example": "BizDate='2003-09-10'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:date",
                                    "Description": "string field represening a Date of Local Market (as oppose to UTC) in YYYY-MM-DD format per the ISO 8601 standard. This is the 'normal' date field used by the FIX Protocol.\nValid values:\nYYYY = 0000-9999, MM = 01-12, DD = 01-31.",
                                    "Example": "BizDate='2003-09-10'",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="********",
                        )
                    ],
                    77: [
                        FixTag(
                            tag=77,
                            name="PositionEffect",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="O",
                        )
                    ],
                    150: [
                        FixTag(
                            tag=150,
                            name="ExecType",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="F",
                        )
                    ],
                    207: [
                        FixTag(
                            tag=207,
                            name="SecurityExchange",
                            description="",
                            type={
                                "Name": "Exchange",
                                "BaseType": "String",
                                "Description": "string field representing a market or exchange using ISO 10383 Market Identifier Code (MIC) values (see'Appendix 6-C).",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".*",
                                    "Description": "string field representing a market or exchange using ISO 10383 Market Identifier Code (MIC) values (see'Appendix 6-C).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="IFEU",
                        )
                    ],
                    453: [
                        FixTag(
                            tag=453,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="12",
                        )
                    ],
                    448: [
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="emifsud1",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="Goldman Sachs International",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="664",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="Sigma Broking LTD-Broker",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="14621",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="dwilson34",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="664",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="662",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="EUCRP01",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="Goldman Sachs",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="GSF",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="F",
                        ),
                    ],
                    452: [
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=11,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=13,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=56,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=1,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=61,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=12,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=35,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=4,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=51,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=60,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=63,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=54,
                        ),
                    ],
                    447: [
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                    ],
                    461: [
                        FixTag(
                            tag=461,
                            name="CFICode",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="OMXXXX",
                        )
                    ],
                    487: [
                        FixTag(
                            tag=487,
                            name="TradeReportTransType",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=0,
                        )
                    ],
                    552: [
                        FixTag(
                            tag=552,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="1",
                        )
                    ],
                    555: [
                        FixTag(
                            tag=555,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="4",
                        )
                    ],
                    600: [
                        FixTag(
                            tag=600,
                            name="LegSymbol",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="5430854",
                        ),
                        FixTag(
                            tag=600,
                            name="LegSymbol",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="5430845",
                        ),
                        FixTag(
                            tag=600,
                            name="LegSymbol",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="5430845",
                        ),
                        FixTag(
                            tag=600,
                            name="LegSymbol",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="5430854",
                        ),
                    ],
                    602: [
                        FixTag(
                            tag=602,
                            name="LegSecurityID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="BRN FMU0023_OMCA0000083502072623",
                        ),
                        FixTag(
                            tag=602,
                            name="LegSecurityID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="BRN FMM0023!",
                        ),
                        FixTag(
                            tag=602,
                            name="LegSecurityID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="BRN FMM0023_OMCA0000085502042523",
                        ),
                        FixTag(
                            tag=602,
                            name="LegSecurityID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="BRN FMU0023!",
                        ),
                    ],
                    603: [
                        FixTag(
                            tag=603,
                            name="LegSecurityIDSource",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="8",
                        ),
                        FixTag(
                            tag=603,
                            name="LegSecurityIDSource",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="8",
                        ),
                        FixTag(
                            tag=603,
                            name="LegSecurityIDSource",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="8",
                        ),
                        FixTag(
                            tag=603,
                            name="LegSecurityIDSource",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="8",
                        ),
                    ],
                    608: [
                        FixTag(
                            tag=608,
                            name="LegCFICode",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="OCXXXX",
                        ),
                        FixTag(
                            tag=608,
                            name="LegCFICode",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="FXXXXX",
                        ),
                        FixTag(
                            tag=608,
                            name="LegCFICode",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="OCXXXX",
                        ),
                        FixTag(
                            tag=608,
                            name="LegCFICode",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="FXXXXX",
                        ),
                    ],
                    612: [
                        FixTag(
                            tag=612,
                            name="LegStrikePrice",
                            description="",
                            type={
                                "Name": "Price",
                                "BaseType": "float",
                                "Description": "float field representing a price. Note the number of decimal places may vary. For certain asset classes prices may be negative values. For example, prices for options strategies can be negative under certain market conditions. Refer to Volume 7: FIX Usage by Product for asset classes that support negative price values.",
                                "Example": "Strk='47.50'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:decimal",
                                    "Description": "float field representing a price. Note the number of decimal places may vary. For certain asset classes prices may be negative values. For example, prices for options strategies can be negative under certain market conditions. Refer to Volume 7: FIX Usage by Product for asset classes that support negative price values.",
                                },
                                "Added": "FIX.4.2",
                            },
                            value=83.5,
                        ),
                        FixTag(
                            tag=612,
                            name="LegStrikePrice",
                            description="",
                            type={
                                "Name": "Price",
                                "BaseType": "float",
                                "Description": "float field representing a price. Note the number of decimal places may vary. For certain asset classes prices may be negative values. For example, prices for options strategies can be negative under certain market conditions. Refer to Volume 7: FIX Usage by Product for asset classes that support negative price values.",
                                "Example": "Strk='47.50'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:decimal",
                                    "Description": "float field representing a price. Note the number of decimal places may vary. For certain asset classes prices may be negative values. For example, prices for options strategies can be negative under certain market conditions. Refer to Volume 7: FIX Usage by Product for asset classes that support negative price values.",
                                },
                                "Added": "FIX.4.2",
                            },
                            value=85.5,
                        ),
                    ],
                    9404: [
                        FixTag(
                            tag=9404,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="106835518",
                        ),
                        FixTag(
                            tag=9404,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="105769975",
                        ),
                    ],
                    616: [
                        FixTag(
                            tag=616,
                            name="LegSecurityExchange",
                            description="",
                            type={
                                "Name": "Exchange",
                                "BaseType": "String",
                                "Description": "string field representing a market or exchange using ISO 10383 Market Identifier Code (MIC) values (see'Appendix 6-C).",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".*",
                                    "Description": "string field representing a market or exchange using ISO 10383 Market Identifier Code (MIC) values (see'Appendix 6-C).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="IFEU",
                        ),
                        FixTag(
                            tag=616,
                            name="LegSecurityExchange",
                            description="",
                            type={
                                "Name": "Exchange",
                                "BaseType": "String",
                                "Description": "string field representing a market or exchange using ISO 10383 Market Identifier Code (MIC) values (see'Appendix 6-C).",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".*",
                                    "Description": "string field representing a market or exchange using ISO 10383 Market Identifier Code (MIC) values (see'Appendix 6-C).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="IFEU",
                        ),
                        FixTag(
                            tag=616,
                            name="LegSecurityExchange",
                            description="",
                            type={
                                "Name": "Exchange",
                                "BaseType": "String",
                                "Description": "string field representing a market or exchange using ISO 10383 Market Identifier Code (MIC) values (see'Appendix 6-C).",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".*",
                                    "Description": "string field representing a market or exchange using ISO 10383 Market Identifier Code (MIC) values (see'Appendix 6-C).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="IFEU",
                        ),
                        FixTag(
                            tag=616,
                            name="LegSecurityExchange",
                            description="",
                            type={
                                "Name": "Exchange",
                                "BaseType": "String",
                                "Description": "string field representing a market or exchange using ISO 10383 Market Identifier Code (MIC) values (see'Appendix 6-C).",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".*",
                                    "Description": "string field representing a market or exchange using ISO 10383 Market Identifier Code (MIC) values (see'Appendix 6-C).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="IFEU",
                        ),
                    ],
                    624: [
                        FixTag(
                            tag=624,
                            name="LegSide",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="1",
                        ),
                        FixTag(
                            tag=624,
                            name="LegSide",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="1",
                        ),
                        FixTag(
                            tag=624,
                            name="LegSide",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="2",
                        ),
                        FixTag(
                            tag=624,
                            name="LegSide",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="2",
                        ),
                    ],
                    637: [
                        FixTag(
                            tag=637,
                            name="LegLastPx",
                            description="",
                            type={
                                "Name": "Price",
                                "BaseType": "float",
                                "Description": "float field representing a price. Note the number of decimal places may vary. For certain asset classes prices may be negative values. For example, prices for options strategies can be negative under certain market conditions. Refer to Volume 7: FIX Usage by Product for asset classes that support negative price values.",
                                "Example": "Strk='47.50'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:decimal",
                                    "Description": "float field representing a price. Note the number of decimal places may vary. For certain asset classes prices may be negative values. For example, prices for options strategies can be negative under certain market conditions. Refer to Volume 7: FIX Usage by Product for asset classes that support negative price values.",
                                },
                                "Added": "FIX.4.2",
                            },
                            value=14.61,
                        ),
                        FixTag(
                            tag=637,
                            name="LegLastPx",
                            description="",
                            type={
                                "Name": "Price",
                                "BaseType": "float",
                                "Description": "float field representing a price. Note the number of decimal places may vary. For certain asset classes prices may be negative values. For example, prices for options strategies can be negative under certain market conditions. Refer to Volume 7: FIX Usage by Product for asset classes that support negative price values.",
                                "Example": "Strk='47.50'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:decimal",
                                    "Description": "float field representing a price. Note the number of decimal places may vary. For certain asset classes prices may be negative values. For example, prices for options strategies can be negative under certain market conditions. Refer to Volume 7: FIX Usage by Product for asset classes that support negative price values.",
                                },
                                "Added": "FIX.4.2",
                            },
                            value=85.5,
                        ),
                        FixTag(
                            tag=637,
                            name="LegLastPx",
                            description="",
                            type={
                                "Name": "Price",
                                "BaseType": "float",
                                "Description": "float field representing a price. Note the number of decimal places may vary. For certain asset classes prices may be negative values. For example, prices for options strategies can be negative under certain market conditions. Refer to Volume 7: FIX Usage by Product for asset classes that support negative price values.",
                                "Example": "Strk='47.50'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:decimal",
                                    "Description": "float field representing a price. Note the number of decimal places may vary. For certain asset classes prices may be negative values. For example, prices for options strategies can be negative under certain market conditions. Refer to Volume 7: FIX Usage by Product for asset classes that support negative price values.",
                                },
                                "Added": "FIX.4.2",
                            },
                            value=12.8,
                        ),
                        FixTag(
                            tag=637,
                            name="LegLastPx",
                            description="",
                            type={
                                "Name": "Price",
                                "BaseType": "float",
                                "Description": "float field representing a price. Note the number of decimal places may vary. For certain asset classes prices may be negative values. For example, prices for options strategies can be negative under certain market conditions. Refer to Volume 7: FIX Usage by Product for asset classes that support negative price values.",
                                "Example": "Strk='47.50'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:decimal",
                                    "Description": "float field representing a price. Note the number of decimal places may vary. For certain asset classes prices may be negative values. For example, prices for options strategies can be negative under certain market conditions. Refer to Volume 7: FIX Usage by Product for asset classes that support negative price values.",
                                },
                                "Added": "FIX.4.2",
                            },
                            value=83.5,
                        ),
                    ],
                    687: [
                        FixTag(
                            tag=687,
                            name="LegQty",
                            description="",
                            type={
                                "Name": "Qty",
                                "BaseType": "float",
                                "Description": "float field capable of storing either a whole number (no decimal places) of 'shares' (securities denominated in whole units) or a decimal value containing decimal places for non-share quantity asset classes (securities denominated in fractional units).",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:decimal",
                                    "Description": "float field capable of storing either a whole number (no decimal places) of 'shares' (securities denominated in whole units) or a decimal value containing decimal places for non-share quantity asset classes (securities denominated in fractional units).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value=100.0,
                        ),
                        FixTag(
                            tag=687,
                            name="LegQty",
                            description="",
                            type={
                                "Name": "Qty",
                                "BaseType": "float",
                                "Description": "float field capable of storing either a whole number (no decimal places) of 'shares' (securities denominated in whole units) or a decimal value containing decimal places for non-share quantity asset classes (securities denominated in fractional units).",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:decimal",
                                    "Description": "float field capable of storing either a whole number (no decimal places) of 'shares' (securities denominated in whole units) or a decimal value containing decimal places for non-share quantity asset classes (securities denominated in fractional units).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value=63.0,
                        ),
                        FixTag(
                            tag=687,
                            name="LegQty",
                            description="",
                            type={
                                "Name": "Qty",
                                "BaseType": "float",
                                "Description": "float field capable of storing either a whole number (no decimal places) of 'shares' (securities denominated in whole units) or a decimal value containing decimal places for non-share quantity asset classes (securities denominated in fractional units).",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:decimal",
                                    "Description": "float field capable of storing either a whole number (no decimal places) of 'shares' (securities denominated in whole units) or a decimal value containing decimal places for non-share quantity asset classes (securities denominated in fractional units).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value=100.0,
                        ),
                        FixTag(
                            tag=687,
                            name="LegQty",
                            description="",
                            type={
                                "Name": "Qty",
                                "BaseType": "float",
                                "Description": "float field capable of storing either a whole number (no decimal places) of 'shares' (securities denominated in whole units) or a decimal value containing decimal places for non-share quantity asset classes (securities denominated in fractional units).",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:decimal",
                                    "Description": "float field capable of storing either a whole number (no decimal places) of 'shares' (securities denominated in whole units) or a decimal value containing decimal places for non-share quantity asset classes (securities denominated in fractional units).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value=65.0,
                        ),
                    ],
                    1017: [
                        FixTag(
                            tag=1017,
                            name="LegOptionRatio",
                            description="",
                            type={
                                "Name": "float",
                                "Description": "Sequence of digits with optional decimal point and sign character (ASCII characters '-', '0' - '9' and '.'); the absence of the decimal point within the string will be interpreted as the float representation of an integer value. All float fields must accommodate up to fifteen significant digits. The number of decimal places used should be a factor of business/market needs and mutual agreement between counterparties. Note that float values may contain leading zeros (e.g. '00023.23' = '23.23') and may contain or omit trailing zeros after the decimal point (e.g. '23.0' = '23.0000' = '23' = '23.').\nNote that fields which are derived from float may contain negative values unless explicitly specified otherwise. The following data types are based on float.",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:decimal",
                                    "Description": "Sequence of digits with optional decimal point and sign character (ASCII characters '-', '0' - '9' and '.'); the absence of the decimal point within the string will be interpreted as the float representation of an integer value. All float fields must accommodate up to fifteen significant digits. The number of decimal places used should be a factor of business/market needs and mutual agreement between counterparties. Note that float values may contain leading zeros (e.g. '00023.23' = '23.23') and may contain or omit trailing zeros after the decimal point (e.g. '23.0' = '23.0000' = '23' = '23.').\nNote that fields which are derived from float may contain negative values unless explicitly specified otherwise. The following data types are based on float.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value=65.0,
                        ),
                        FixTag(
                            tag=1017,
                            name="LegOptionRatio",
                            description="",
                            type={
                                "Name": "float",
                                "Description": "Sequence of digits with optional decimal point and sign character (ASCII characters '-', '0' - '9' and '.'); the absence of the decimal point within the string will be interpreted as the float representation of an integer value. All float fields must accommodate up to fifteen significant digits. The number of decimal places used should be a factor of business/market needs and mutual agreement between counterparties. Note that float values may contain leading zeros (e.g. '00023.23' = '23.23') and may contain or omit trailing zeros after the decimal point (e.g. '23.0' = '23.0000' = '23' = '23.').\nNote that fields which are derived from float may contain negative values unless explicitly specified otherwise. The following data types are based on float.",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:decimal",
                                    "Description": "Sequence of digits with optional decimal point and sign character (ASCII characters '-', '0' - '9' and '.'); the absence of the decimal point within the string will be interpreted as the float representation of an integer value. All float fields must accommodate up to fifteen significant digits. The number of decimal places used should be a factor of business/market needs and mutual agreement between counterparties. Note that float values may contain leading zeros (e.g. '00023.23' = '23.23') and may contain or omit trailing zeros after the decimal point (e.g. '23.0' = '23.0000' = '23' = '23.').\nNote that fields which are derived from float may contain negative values unless explicitly specified otherwise. The following data types are based on float.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value=63.0,
                        ),
                        FixTag(
                            tag=1017,
                            name="LegOptionRatio",
                            description="",
                            type={
                                "Name": "float",
                                "Description": "Sequence of digits with optional decimal point and sign character (ASCII characters '-', '0' - '9' and '.'); the absence of the decimal point within the string will be interpreted as the float representation of an integer value. All float fields must accommodate up to fifteen significant digits. The number of decimal places used should be a factor of business/market needs and mutual agreement between counterparties. Note that float values may contain leading zeros (e.g. '00023.23' = '23.23') and may contain or omit trailing zeros after the decimal point (e.g. '23.0' = '23.0000' = '23' = '23.').\nNote that fields which are derived from float may contain negative values unless explicitly specified otherwise. The following data types are based on float.",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:decimal",
                                    "Description": "Sequence of digits with optional decimal point and sign character (ASCII characters '-', '0' - '9' and '.'); the absence of the decimal point within the string will be interpreted as the float representation of an integer value. All float fields must accommodate up to fifteen significant digits. The number of decimal places used should be a factor of business/market needs and mutual agreement between counterparties. Note that float values may contain leading zeros (e.g. '00023.23' = '23.23') and may contain or omit trailing zeros after the decimal point (e.g. '23.0' = '23.0000' = '23' = '23.').\nNote that fields which are derived from float may contain negative values unless explicitly specified otherwise. The following data types are based on float.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value=63.0,
                        ),
                        FixTag(
                            tag=1017,
                            name="LegOptionRatio",
                            description="",
                            type={
                                "Name": "float",
                                "Description": "Sequence of digits with optional decimal point and sign character (ASCII characters '-', '0' - '9' and '.'); the absence of the decimal point within the string will be interpreted as the float representation of an integer value. All float fields must accommodate up to fifteen significant digits. The number of decimal places used should be a factor of business/market needs and mutual agreement between counterparties. Note that float values may contain leading zeros (e.g. '00023.23' = '23.23') and may contain or omit trailing zeros after the decimal point (e.g. '23.0' = '23.0000' = '23' = '23.').\nNote that fields which are derived from float may contain negative values unless explicitly specified otherwise. The following data types are based on float.",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:decimal",
                                    "Description": "Sequence of digits with optional decimal point and sign character (ASCII characters '-', '0' - '9' and '.'); the absence of the decimal point within the string will be interpreted as the float representation of an integer value. All float fields must accommodate up to fifteen significant digits. The number of decimal places used should be a factor of business/market needs and mutual agreement between counterparties. Note that float values may contain leading zeros (e.g. '00023.23' = '23.23') and may contain or omit trailing zeros after the decimal point (e.g. '23.0' = '23.0000' = '23' = '23.').\nNote that fields which are derived from float may contain negative values unless explicitly specified otherwise. The following data types are based on float.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value=65.0,
                        ),
                    ],
                    654: [
                        FixTag(
                            tag=654,
                            name="LegRefID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="75815823",
                        ),
                        FixTag(
                            tag=654,
                            name="LegRefID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="75815824",
                        ),
                        FixTag(
                            tag=654,
                            name="LegRefID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="75815822",
                        ),
                        FixTag(
                            tag=654,
                            name="LegRefID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="75815825",
                        ),
                    ],
                    9019: [
                        FixTag(
                            tag=9019,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="100",
                        ),
                        FixTag(
                            tag=9019,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="63",
                        ),
                        FixTag(
                            tag=9019,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="100",
                        ),
                        FixTag(
                            tag=9019,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="65",
                        ),
                    ],
                    9023: [
                        FixTag(
                            tag=9023,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="1",
                        ),
                        FixTag(
                            tag=9023,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="1",
                        ),
                        FixTag(
                            tag=9023,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="1",
                        ),
                        FixTag(
                            tag=9023,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="1",
                        ),
                    ],
                    9020: [
                        FixTag(
                            tag=9020,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="20230901",
                        ),
                        FixTag(
                            tag=9020,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="20230601",
                        ),
                        FixTag(
                            tag=9020,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="20230601",
                        ),
                        FixTag(
                            tag=9020,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="20230901",
                        ),
                    ],
                    9021: [
                        FixTag(
                            tag=9021,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="20230930",
                        ),
                        FixTag(
                            tag=9021,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="20230630",
                        ),
                        FixTag(
                            tag=9021,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="20230630",
                        ),
                        FixTag(
                            tag=9021,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="20230930",
                        ),
                    ],
                    539: [
                        FixTag(
                            tag=539,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="6",
                        ),
                        FixTag(
                            tag=539,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="6",
                        ),
                        FixTag(
                            tag=539,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="6",
                        ),
                        FixTag(
                            tag=539,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="6",
                        ),
                    ],
                    524: [
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="662",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="EUCRP01",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="Goldman Sachs",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="GSF",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="F",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="664",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="662",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="EUCRP01",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="Goldman Sachs",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="GSF",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="F",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="664",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="662",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="EUCRP01",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="Goldman Sachs",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="GSF",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="F",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="664",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="662",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="EUCRP01",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="Goldman Sachs",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="GSF",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="F",
                        ),
                        FixTag(
                            tag=524,
                            name="NestedPartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="664",
                        ),
                    ],
                    525: [
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=525,
                            name="NestedPartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                    ],
                    538: [
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=4,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=51,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=60,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=63,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=54,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=35,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=4,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=51,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=60,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=63,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=54,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=35,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=4,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=51,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=60,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=63,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=54,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=35,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=4,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=51,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=60,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=63,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=54,
                        ),
                        FixTag(
                            tag=538,
                            name="NestedPartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=35,
                        ),
                    ],
                    9426: [
                        FixTag(
                            tag=9426,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="W",
                        ),
                        FixTag(
                            tag=9426,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="W",
                        ),
                        FixTag(
                            tag=9426,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="W",
                        ),
                        FixTag(
                            tag=9426,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="W",
                        ),
                    ],
                    570: [
                        FixTag(
                            tag=570,
                            name="PreviouslyReported",
                            description="",
                            type={
                                "Name": "Boolean",
                                "BaseType": "char",
                                "Description": "char field containing one of two values:\n'Y' = True/Yes\n'N' = False/No",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": "[YN]{1}",
                                    "Description": "char field containing one of two values:\n'Y' = True/Yes\n'N' = False/No",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="N",
                        )
                    ],
                    571: [
                        FixTag(
                            tag=571,
                            name="TradeReportID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="349866",
                        )
                    ],
                    762: [
                        FixTag(
                            tag=762,
                            name="SecuritySubType",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="63",
                        )
                    ],
                    828: [
                        FixTag(
                            tag=828,
                            name="TrdType",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value="K",
                        )
                    ],
                    856: [
                        FixTag(
                            tag=856,
                            name="TradeReportType",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=0,
                        )
                    ],
                    916: [
                        FixTag(
                            tag=916,
                            name="StartDate",
                            description="",
                            type={
                                "Name": "LocalMktDate",
                                "BaseType": "String",
                                "Description": "string field represening a Date of Local Market (as oppose to UTC) in YYYYMMDD format. This is the 'normal' date field used by the FIX Protocol.\nValid values:\nYYYY = 0000-9999, MM = 01-12, DD = 01-31.",
                                "Example": "BizDate='2003-09-10'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:date",
                                    "Description": "string field represening a Date of Local Market (as oppose to UTC) in YYYY-MM-DD format per the ISO 8601 standard. This is the 'normal' date field used by the FIX Protocol.\nValid values:\nYYYY = 0000-9999, MM = 01-12, DD = 01-31.",
                                    "Example": "BizDate='2003-09-10'",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="20230901",
                        )
                    ],
                    917: [
                        FixTag(
                            tag=917,
                            name="EndDate",
                            description="",
                            type={
                                "Name": "LocalMktDate",
                                "BaseType": "String",
                                "Description": "string field represening a Date of Local Market (as oppose to UTC) in YYYYMMDD format. This is the 'normal' date field used by the FIX Protocol.\nValid values:\nYYYY = 0000-9999, MM = 01-12, DD = 01-31.",
                                "Example": "BizDate='2003-09-10'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:date",
                                    "Description": "string field represening a Date of Local Market (as oppose to UTC) in YYYY-MM-DD format per the ISO 8601 standard. This is the 'normal' date field used by the FIX Protocol.\nValid values:\nYYYY = 0000-9999, MM = 01-12, DD = 01-31.",
                                    "Example": "BizDate='2003-09-10'",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="20230930",
                        )
                    ],
                    1031: [
                        FixTag(
                            tag=1031,
                            name="CustOrderHandlingInst",
                            description="",
                            type={
                                "Name": "MultipleStringValue",
                                "BaseType": "String",
                                "Description": "string field containing one or more space delimited multiple character values (e.g. |277=AV AN A| ).",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".+(\\s.+)*",
                                    "Description": "string field containing one or more space delimited multiple character values (e.g. |277=AV AN A| ).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="W",
                        )
                    ],
                    8013: [
                        FixTag(
                            tag=8013,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="LRGS",
                        )
                    ],
                    9018: [
                        FixTag(
                            tag=9018,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="100",
                        )
                    ],
                    9022: [
                        FixTag(
                            tag=9022,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="1",
                        )
                    ],
                    9028: [
                        FixTag(
                            tag=9028,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="0",
                        )
                    ],
                    9064: [
                        FixTag(
                            tag=9064,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="0",
                        )
                    ],
                    9403: [
                        FixTag(
                            tag=9403,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="30501257",
                        )
                    ],
                    9413: [
                        FixTag(
                            tag=9413,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="3",
                        )
                    ],
                    9707: [
                        FixTag(
                            tag=9707,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="1",
                        )
                    ],
                    10: [
                        FixTag(
                            tag=10,
                            name="CheckSum",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="065",
                        )
                    ],
                },
            )
        ]
        result = task.execute(fix_parser_data=fix_parser_data, params=params)
        result_df = result.frame.fillna(pd.NA).astype(str)
        expected_df = (
            pd.read_csv(EXPECTED_FILE_PATH_MULTI_LEG).fillna(pd.NA).astype(str)
        )

        assert not pd.testing.assert_frame_equal(result_df, expected_df)

    def test_create_cancels(self):
        params = Params(transactions_only=False, create_cancels=True)
        task = IceFixGenerateTrades(name="test_task", params=params)
        fix_parser_data = [
            FixParserResult(
                message_string="8=FIX.4.49=64935=AE34=163849=ICE52=********-13:33:23.93056=1462157=SteelEye_SIGMA11=236427417=236427522=831=64.0032=237=236427439=448=EGD SMX0022!54=255=625553960=********-13:28:29.14675=********77=O150=H207=IFED453=11448=swillems2452=11447=D448=Glencore Commodities Ltd.452=13447=D448=207452=56447=D448=Sigma Broking LTD-Broker452=1447=D448=14621452=61447=D448=aholmes3452=12447=D448=2172452=4447=D448=GCMZ3STW452=51447=D448=Mizuho Securities452=60447=D448=MZF452=63447=D448=W452=54447=D461=FXXXXX487=0552=1570=N571=309019828=K856=0916=20221101917=202211301031=W9018=29022=19028=09064=09413=310=184",
                s3_file_url="s3://puneeth.uat.steeleye.co/fix/order-feed-ice-fix/05cb82f0c52dd4b5de45137e996e5a1c11b4288bb6938aa121009ae8351775c8_1638.fix",
                tags={
                    8: [
                        FixTag(
                            tag=8,
                            name="BeginString",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="FIX.4.4",
                        )
                    ],
                    9: [
                        FixTag(
                            tag=9,
                            name="BodyLength",
                            description="",
                            type={
                                "Name": "Length",
                                "BaseType": "int",
                                "Description": "int field representing the length in bytes. Value must be positive.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:nonNegativeInteger",
                                    "Description": "int field representing the length in bytes. Value must be positive.",
                                },
                                "Added": "FIX.4.3",
                            },
                            value=649,
                        )
                    ],
                    35: [
                        FixTag(
                            tag=35,
                            name="MsgType",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="AE",
                        )
                    ],
                    34: [
                        FixTag(
                            tag=34,
                            name="MsgSeqNum",
                            description="",
                            type={
                                "Name": "SeqNum",
                                "BaseType": "int",
                                "Description": "int field representing a message sequence number. Value must be positive.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:positiveInteger",
                                    "Description": "int field representing a message sequence number. Value must be positive.",
                                },
                                "Added": "FIX.4.3",
                            },
                            value=1638,
                        )
                    ],
                    49: [
                        FixTag(
                            tag=49,
                            name="SenderCompID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="ICE",
                        )
                    ],
                    52: [
                        FixTag(
                            tag=52,
                            name="SendingTime",
                            description="",
                            type={
                                "Name": "UTCTimestamp",
                                "BaseType": "String",
                                "Description": "string field representing Time/date combination represented in UTC (Universal Time Coordinated, also known as 'GMT') in either YYYYMMDD-HH:MM:SS (whole seconds) or YYYYMMDD-HH:MM:SS.sss (milliseconds) format, colons, dash, and period required.\nValid values:\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second) (without milliseconds).\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second), sss=000-999 (indicating milliseconds).\nLeap Seconds: Note that UTC includes corrections for leap seconds, which are inserted to account for slowing of the rotation of the earth. Leap second insertion is declared by the International Earth Rotation Service (IERS) and has, since 1972, only occurred on the night of Dec. 31 or Jun 30. The IERS considers March 31 and September 30 as secondary dates for leap second insertion, but has never utilized these dates. During a leap second insertion, a UTCTimestamp field may read '********-23:59:59', '********-23:59:60', '********-00:00:00'. (see http://tycho.usno.navy.mil/leapsec.html)",
                                "Example": "TransactTm='********-09:30:47'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:dateTime",
                                    "Description": "string field representing Time/date combination represented in UTC (Universal Time Coordinated, also known as 'GMT') in either YYYY-MM-DDTHH:MM:SS (whole seconds) or YYYY-MM-DDTHH:MM:SS.sss (milliseconds) format as specified in ISO 8601.\nValid values:\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second) (without milliseconds).\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second), sss=000-999 (indicating milliseconds).\nLeap Seconds: Note that UTC includes corrections for leap seconds, which are inserted to account for slowing of the rotation of the earth. Leap second insertion is declared by the International Earth Rotation Service (IERS) and has, since 1972, only occurred on the night of Dec. 31 or Jun 30. The IERS considers March 31 and September 30 as secondary dates for leap second insertion, but has never utilized these dates. During a leap second insertion, a UTCTimestamp field may read '1998-12-31T23:59:59', '1998-12-31T23:59:60', '1999-01-01T00:00:00'. (see http://tycho.usno.navy.mil/leapsec.html)",
                                    "Example": "TransactTm='2001-12-17T09:30:47-05:00'",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="********-13:33:23.930",
                        )
                    ],
                    56: [
                        FixTag(
                            tag=56,
                            name="TargetCompID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="14621",
                        )
                    ],
                    57: [
                        FixTag(
                            tag=57,
                            name="TargetSubID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="SteelEye_SIGMA",
                        )
                    ],
                    11: [
                        FixTag(
                            tag=11,
                            name="ClOrdID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="2364274",
                        )
                    ],
                    17: [
                        FixTag(
                            tag=17,
                            name="ExecID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="2364275",
                        )
                    ],
                    22: [
                        FixTag(
                            tag=22,
                            name="SecurityIDSource",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="8",
                        )
                    ],
                    31: [
                        FixTag(
                            tag=31,
                            name="LastPx",
                            description="",
                            type={
                                "Name": "Price",
                                "BaseType": "float",
                                "Description": "float field representing a price. Note the number of decimal places may vary. For certain asset classes prices may be negative values. For example, prices for options strategies can be negative under certain market conditions. Refer to Volume 7: FIX Usage by Product for asset classes that support negative price values.",
                                "Example": "Strk='47.50'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:decimal",
                                    "Description": "float field representing a price. Note the number of decimal places may vary. For certain asset classes prices may be negative values. For example, prices for options strategies can be negative under certain market conditions. Refer to Volume 7: FIX Usage by Product for asset classes that support negative price values.",
                                },
                                "Added": "FIX.4.2",
                            },
                            value=64.0,
                        )
                    ],
                    32: [
                        FixTag(
                            tag=32,
                            name="LastQty",
                            description="",
                            type={
                                "Name": "Qty",
                                "BaseType": "float",
                                "Description": "float field capable of storing either a whole number (no decimal places) of 'shares' (securities denominated in whole units) or a decimal value containing decimal places for non-share quantity asset classes (securities denominated in fractional units).",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:decimal",
                                    "Description": "float field capable of storing either a whole number (no decimal places) of 'shares' (securities denominated in whole units) or a decimal value containing decimal places for non-share quantity asset classes (securities denominated in fractional units).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value=2.0,
                        )
                    ],
                    37: [
                        FixTag(
                            tag=37,
                            name="OrderID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="2364274",
                        )
                    ],
                    39: [
                        FixTag(
                            tag=39,
                            name="OrdStatus",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="4",
                        )
                    ],
                    48: [
                        FixTag(
                            tag=48,
                            name="SecurityID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="EGD SMX0022!",
                        )
                    ],
                    54: [
                        FixTag(
                            tag=54,
                            name="Side",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="2",
                        )
                    ],
                    55: [
                        FixTag(
                            tag=55,
                            name="Symbol",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="6255539",
                        )
                    ],
                    60: [
                        FixTag(
                            tag=60,
                            name="TransactTime",
                            description="",
                            type={
                                "Name": "UTCTimestamp",
                                "BaseType": "String",
                                "Description": "string field representing Time/date combination represented in UTC (Universal Time Coordinated, also known as 'GMT') in either YYYYMMDD-HH:MM:SS (whole seconds) or YYYYMMDD-HH:MM:SS.sss (milliseconds) format, colons, dash, and period required.\nValid values:\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second) (without milliseconds).\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second), sss=000-999 (indicating milliseconds).\nLeap Seconds: Note that UTC includes corrections for leap seconds, which are inserted to account for slowing of the rotation of the earth. Leap second insertion is declared by the International Earth Rotation Service (IERS) and has, since 1972, only occurred on the night of Dec. 31 or Jun 30. The IERS considers March 31 and September 30 as secondary dates for leap second insertion, but has never utilized these dates. During a leap second insertion, a UTCTimestamp field may read '********-23:59:59', '********-23:59:60', '********-00:00:00'. (see http://tycho.usno.navy.mil/leapsec.html)",
                                "Example": "TransactTm='********-09:30:47'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:dateTime",
                                    "Description": "string field representing Time/date combination represented in UTC (Universal Time Coordinated, also known as 'GMT') in either YYYY-MM-DDTHH:MM:SS (whole seconds) or YYYY-MM-DDTHH:MM:SS.sss (milliseconds) format as specified in ISO 8601.\nValid values:\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second) (without milliseconds).\n* YYYY = 0000-9999, MM = 01-12, DD = 01-31, HH = 00-23, MM = 00-59, SS = 00-60 (60 only if UTC leap second), sss=000-999 (indicating milliseconds).\nLeap Seconds: Note that UTC includes corrections for leap seconds, which are inserted to account for slowing of the rotation of the earth. Leap second insertion is declared by the International Earth Rotation Service (IERS) and has, since 1972, only occurred on the night of Dec. 31 or Jun 30. The IERS considers March 31 and September 30 as secondary dates for leap second insertion, but has never utilized these dates. During a leap second insertion, a UTCTimestamp field may read '1998-12-31T23:59:59', '1998-12-31T23:59:60', '1999-01-01T00:00:00'. (see http://tycho.usno.navy.mil/leapsec.html)",
                                    "Example": "TransactTm='2001-12-17T09:30:47-05:00'",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="********-13:28:29.146",
                        )
                    ],
                    75: [
                        FixTag(
                            tag=75,
                            name="TradeDate",
                            description="",
                            type={
                                "Name": "LocalMktDate",
                                "BaseType": "String",
                                "Description": "string field represening a Date of Local Market (as oppose to UTC) in YYYYMMDD format. This is the 'normal' date field used by the FIX Protocol.\nValid values:\nYYYY = 0000-9999, MM = 01-12, DD = 01-31.",
                                "Example": "BizDate='2003-09-10'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:date",
                                    "Description": "string field represening a Date of Local Market (as oppose to UTC) in YYYY-MM-DD format per the ISO 8601 standard. This is the 'normal' date field used by the FIX Protocol.\nValid values:\nYYYY = 0000-9999, MM = 01-12, DD = 01-31.",
                                    "Example": "BizDate='2003-09-10'",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="********",
                        )
                    ],
                    77: [
                        FixTag(
                            tag=77,
                            name="PositionEffect",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="O",
                        )
                    ],
                    150: [
                        FixTag(
                            tag=150,
                            name="ExecType",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="H",
                        )
                    ],
                    207: [
                        FixTag(
                            tag=207,
                            name="SecurityExchange",
                            description="",
                            type={
                                "Name": "Exchange",
                                "BaseType": "String",
                                "Description": "string field representing a market or exchange using ISO 10383 Market Identifier Code (MIC) values (see'Appendix 6-C).",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".*",
                                    "Description": "string field representing a market or exchange using ISO 10383 Market Identifier Code (MIC) values (see'Appendix 6-C).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="IFED",
                        )
                    ],
                    453: [
                        FixTag(
                            tag=453,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="11",
                        )
                    ],
                    448: [
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="swillems2",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="Glencore Commodities Ltd.",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="207",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="Sigma Broking LTD-Broker",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="14621",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="aholmes3",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="2172",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="GCMZ3STW",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="Mizuho Securities",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="MZF",
                        ),
                        FixTag(
                            tag=448,
                            name="PartyID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="W",
                        ),
                    ],
                    452: [
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=11,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=13,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=56,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=1,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=61,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=12,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=4,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=51,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=60,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=63,
                        ),
                        FixTag(
                            tag=452,
                            name="PartyRole",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=54,
                        ),
                    ],
                    447: [
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                        FixTag(
                            tag=447,
                            name="PartyIDSource",
                            description="",
                            type={
                                "Name": "char",
                                "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".{1}",
                                    "Description": "Single character value, can include any alphanumeric character or punctuation except the delimiter. All char fields are case sensitive (i.e. m != M).\nThe following fields are based on char.",
                                },
                                "Added": "FIX.2.7",
                            },
                            value="D",
                        ),
                    ],
                    461: [
                        FixTag(
                            tag=461,
                            name="CFICode",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="FXXXXX",
                        )
                    ],
                    487: [
                        FixTag(
                            tag=487,
                            name="TradeReportTransType",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=0,
                        )
                    ],
                    552: [
                        FixTag(
                            tag=552,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="1",
                        )
                    ],
                    570: [
                        FixTag(
                            tag=570,
                            name="PreviouslyReported",
                            description="",
                            type={
                                "Name": "Boolean",
                                "BaseType": "char",
                                "Description": "char field containing one of two values:\n'Y' = True/Yes\n'N' = False/No",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": "[YN]{1}",
                                    "Description": "char field containing one of two values:\n'Y' = True/Yes\n'N' = False/No",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="N",
                        )
                    ],
                    571: [
                        FixTag(
                            tag=571,
                            name="TradeReportID",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="309019",
                        )
                    ],
                    828: [
                        FixTag(
                            tag=828,
                            name="TrdType",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value="K",
                        )
                    ],
                    856: [
                        FixTag(
                            tag=856,
                            name="TradeReportType",
                            description="",
                            type={
                                "Name": "int",
                                "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:integer",
                                    "Description": "Sequence of digits without commas or decimals and optional sign character (ASCII characters '-' and '0' - '9' ). The sign character utilizes one byte (i.e. positive int is '99999' while negative int is '-99999'). Note that int values may contain leading zeros (e.g. '00023' = '23').\nExamples:\n723 in field 21 would be mapped int as |21=723|.\n-723 in field 12 would be mapped int as |12=-723|\nThe following data types are based on int.\n",
                                },
                                "Added": "FIX.2.7",
                                "Issue": "SPEC-370",
                            },
                            value=0,
                        )
                    ],
                    916: [
                        FixTag(
                            tag=916,
                            name="StartDate",
                            description="",
                            type={
                                "Name": "LocalMktDate",
                                "BaseType": "String",
                                "Description": "string field represening a Date of Local Market (as oppose to UTC) in YYYYMMDD format. This is the 'normal' date field used by the FIX Protocol.\nValid values:\nYYYY = 0000-9999, MM = 01-12, DD = 01-31.",
                                "Example": "BizDate='2003-09-10'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:date",
                                    "Description": "string field represening a Date of Local Market (as oppose to UTC) in YYYY-MM-DD format per the ISO 8601 standard. This is the 'normal' date field used by the FIX Protocol.\nValid values:\nYYYY = 0000-9999, MM = 01-12, DD = 01-31.",
                                    "Example": "BizDate='2003-09-10'",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="20221101",
                        )
                    ],
                    917: [
                        FixTag(
                            tag=917,
                            name="EndDate",
                            description="",
                            type={
                                "Name": "LocalMktDate",
                                "BaseType": "String",
                                "Description": "string field represening a Date of Local Market (as oppose to UTC) in YYYYMMDD format. This is the 'normal' date field used by the FIX Protocol.\nValid values:\nYYYY = 0000-9999, MM = 01-12, DD = 01-31.",
                                "Example": "BizDate='2003-09-10'",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:date",
                                    "Description": "string field represening a Date of Local Market (as oppose to UTC) in YYYY-MM-DD format per the ISO 8601 standard. This is the 'normal' date field used by the FIX Protocol.\nValid values:\nYYYY = 0000-9999, MM = 01-12, DD = 01-31.",
                                    "Example": "BizDate='2003-09-10'",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="20221130",
                        )
                    ],
                    1031: [
                        FixTag(
                            tag=1031,
                            name="CustOrderHandlingInst",
                            description="",
                            type={
                                "Name": "MultipleStringValue",
                                "BaseType": "String",
                                "Description": "string field containing one or more space delimited multiple character values (e.g. |277=AV AN A| ).",
                                "XML": {
                                    "BuiltIn": "0",
                                    "Base": "xs:string",
                                    "Pattern": ".+(\\s.+)*",
                                    "Description": "string field containing one or more space delimited multiple character values (e.g. |277=AV AN A| ).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="W",
                        )
                    ],
                    9018: [
                        FixTag(
                            tag=9018,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="2",
                        )
                    ],
                    9022: [
                        FixTag(
                            tag=9022,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="1",
                        )
                    ],
                    9028: [
                        FixTag(
                            tag=9028,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="0",
                        )
                    ],
                    9064: [
                        FixTag(
                            tag=9064,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="0",
                        )
                    ],
                    9413: [
                        FixTag(
                            tag=9413,
                            name="Unknown",
                            description="Unknown",
                            type=dict(),
                            value="3",
                        )
                    ],
                    10: [
                        FixTag(
                            tag=10,
                            name="CheckSum",
                            description="",
                            type={
                                "Name": "String",
                                "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                "XML": {
                                    "BuiltIn": "1",
                                    "Base": "xs:string",
                                    "Description": "Alpha-numeric free format strings, can include any character or punctuation except the delimiter. All String fields are case sensitive (i.e. morstatt != Morstatt).",
                                },
                                "Added": "FIX.4.2",
                            },
                            value="184",
                        )
                    ],
                },
            )
        ]

        result = task.execute(fix_parser_data=fix_parser_data, params=params)
        result_df = result.frame.fillna(pd.NA).astype(str)
        expected_df = (
            pd.read_csv(EXPECTED_FILE_CREATE_CANCELS).fillna(pd.NA).astype(str)
        )

        pd.testing.assert_frame_equal(result_df, expected_df)
