from pathlib import Path

import pandas as pd
import pytest
from prefect import context
from prefect.engine.signals import FAIL
from prefect.engine.signals import SKIP
from swarm.task.auditor import Auditor

from swarm_tasks.order.feed.aladdin.aladdin_order_frame_merger import (
    AladdinOrderFrameMerger,
)


SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
ORDER_FILE = TEST_FILES_DIR.joinpath("aladdin_order_file.csv")
ORDER_DETAIL_FILE = TEST_FILES_DIR.joinpath("aladdin_order_detail_file.csv")
FILL_FILE = TEST_FILES_DIR.joinpath("aladdin_fill_file.csv")
TRANSACTION_FILE = TEST_FILES_DIR.joinpath("aladdin_transaction_file.csv")
PLACEMENT_FILE = TEST_FILES_DIR.joinpath("aladdin_placement_file.csv")
EXPECTED_MERGED_ORDER_DF = TEST_FILES_DIR.joinpath("aladdin_merged_order_df.pkl")
EXPECTED_MERGED_ORDER_CSV = TEST_FILES_DIR.joinpath("aladdin_merged_csv_file.csv")


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestAladdinOrderFrameMerger:
    def test_incomplete_pre_process_result(self):
        pre_process_result = {
            "order": pd.read_csv(ORDER_FILE),
            "orderdetail": pd.read_csv(ORDER_DETAIL_FILE),
            "transaction": pd.read_csv(TRANSACTION_FILE),
        }
        task = AladdinOrderFrameMerger(name="TestAladdinOrderFrameMerger")
        with pytest.raises(FAIL):
            task.execute(pre_process_result=pre_process_result)

    def test_aladdin_merge_with_one_empty_file(self, auditor):
        pre_process_result = {
            "order": pd.read_csv(ORDER_FILE),
            "orderdetail": pd.read_csv(ORDER_DETAIL_FILE),
            "fill": pd.DataFrame(),
            "placement": pd.read_csv(PLACEMENT_FILE),
            "transaction": pd.read_csv(TRANSACTION_FILE),
        }
        task = AladdinOrderFrameMerger(name="TestAladdinOrderFrameMerger")
        with pytest.raises(SKIP):
            task.process(
                pre_process_result=pre_process_result,
                file_url="s3://irises8.dev.steeleye.co/flows/order-feed-aladdin/tests/2024/aladdin_order_detail_file.csv",
                logger=context.get("logger"),
                auditor=auditor,
            )

    def test_end_to_end_order_frame_merger(self, auditor):
        pre_process_result = {
            "order": pd.read_csv(ORDER_FILE),
            "orderdetail": pd.read_csv(ORDER_DETAIL_FILE),
            "fill": pd.read_csv(FILL_FILE),
            "placement": pd.read_csv(PLACEMENT_FILE),
            "transaction": pd.read_csv(TRANSACTION_FILE),
        }
        task = AladdinOrderFrameMerger(name="TestAladdinOrderFrameMerger")
        result = task.process(
            pre_process_result=pre_process_result,
            file_url="s3://irises8.dev.steeleye.co/flows/order-feed-aladdin/tests/2024/AladdinTCA.FX.OrderDetail.20241104.csv",
            logger=context.get("logger"),
            auditor=auditor,
        )
        result_df = pd.read_csv(result.path)
        expected = pd.read_csv(EXPECTED_MERGED_ORDER_CSV)
        pd.testing.assert_frame_equal(left=result_df, right=expected)
        assert result.path.name == "AladdinTCA.FX.20241104.csv"
