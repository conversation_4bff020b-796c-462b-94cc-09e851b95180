from pathlib import Path

import pandas as pd
import pytest
from prefect.engine.signals import FAIL
from prefect.engine.signals import SKIP
from swarm.task.io.read.result import FrameProducerResult

from swarm_tasks.order.feed.fidessa.processor.pre_process_fidessa_data import Params
from swarm_tasks.order.feed.fidessa.processor.pre_process_fidessa_data import (
    PreProcessFidessaData,
)
from swarm_tasks.order.feed.fidessa.processor.pre_process_fidessa_data import static

test_file_path = Path(__file__).parent.joinpath("data")


class TestPreprocessFidessaData(object):
    """
    Test cases for "TestPreprocessFidessaData" class
    """

    def test_empty_input_df_without_source_columns(
        self,
        empty_source_df: FrameProducerResult,
        market_order_data: pd.DataFrame,
        mic_code_mapping,
        mocker,
    ):
        task = PreProcessFidessaData(name="test-parties", params=None)
        mock_arc_events_data = mocker.patch.object(
            PreProcessFidessaData, "_read_data_from_s3"
        )
        mock_arc_events_data.side_effect = [
            empty_source_df,
            market_order_data,
            mic_code_mapping,
        ]
        with pytest.raises(FAIL) as e:
            task.execute(empty_source_df, params=None)
        assert e.type == FAIL

    def test_empty_arc_and_market_data(
        self,
        fidessa_controller_output: FrameProducerResult,
        arc_events_empty: pd.DataFrame,
        market_order_empty: pd.DataFrame,
        mocker,
        input_params,
    ):
        task = PreProcessFidessaData(name="test-parties", params=input_params)
        mock_arc_events_data = mocker.patch.object(
            PreProcessFidessaData, "_read_data_from_s3"
        )
        mock_arc_events_data.side_effect = [
            arc_events_empty,
            market_order_empty,
        ]

        with pytest.raises(SKIP) as e:
            task.execute(fidessa_controller_output, params=input_params)

        assert e.match("Empty files")

    def test_all_col_in_source_df(
        self,
        fidessa_controller_output: FrameProducerResult,
        arc_events_data: pd.DataFrame,
        market_order_data: pd.DataFrame,
        expected_result_all_col_in_source_df: pd.DataFrame,
        mic_code_mapping: pd.DataFrame,
        mocker,
        mocked_mic_mapping,
        input_params,
    ):
        task = PreProcessFidessaData(name="test-parties", params=input_params)
        mock_arc_events_data = mocker.patch.object(
            PreProcessFidessaData, "_read_data_from_s3"
        )
        mock_arc_events_data.side_effect = [
            arc_events_data,
            market_order_data,
        ]
        mock_source_dir = mocker.patch.object(PreProcessFidessaData, "_get_source_dir")
        mock_source_dir.return_value = Path(__file__).parent.joinpath("results")
        result = task.execute(fidessa_controller_output, params=input_params)
        result = pd.read_csv(result.path)
        result = result.loc[
            result[static.DerivedColumns.DERIVED_ORDER_ID].isin(
                ["00021939266FOLO1", "00021943843FOLO1|006MYzUVpUht"]
            ),
            [
                static.ArcEventsColumns.PRIMARY_ID,
                static.ArcEventsColumns.EXCHANGE_ORDER_ID,
                static.ArcEventsColumns.EVENT_TYPE,
                static.DerivedColumns.DERIVED_ORDER_ID,
                static.DerivedColumns.DERIVED_HIERARCHY,
                static.ArcEventsColumns.EVENT_TIMESTAMP,
                static.DerivedColumns.DERIVED_ORDER_RECEIVED_DT,
                static.DerivedColumns.DERIVED_ORDER_SUBMITTED_DT,
                static.DerivedColumns.DERIVED_AGGREGATE_ORDER_ID,
                static.DerivedColumns.DERIVED_ISIN_CODE,
                static.DerivedColumns.DERIVED_BUY_SELL,
                static.DerivedColumns.DERIVED_LIMIT_PRICE,
                static.DerivedColumns.DERIVED_STOP_PRICE,
                static.DerivedColumns.DERIVED_PARENT_ORDER_ID,
                static.DerivedColumns.DERIVED_INITIAL_QTY,
                static.DerivedColumns.DERIVED_BOOK_VIEW_CODE,
                static.DerivedColumns.DERIVED_ENTERED_BY,
                static.DerivedColumns.DERIVED_COUNTERPARTY_CODE,
                static.DerivedColumns.DERIVED_MARKET_ID,
                static.DerivedColumns.DERIVED_INVESTMENT_DECISION,
                static.DerivedColumns.DERIVED_EXECUTION_DECISION,
            ],
        ]
        assert result.fillna(pd.NA).equals(expected_result_all_col_in_source_df)

    def test_missing_some_col_in_source_df(
        self,
        fidessa_controller_output: FrameProducerResult,
        arc_events_data_missing_columns: pd.DataFrame,
        market_order_data: pd.DataFrame,
        mic_code_mapping,
        mocker,
        input_params,
    ):
        task = PreProcessFidessaData(name="test-parties", params=input_params)
        mock_arc_events_data = mocker.patch.object(
            PreProcessFidessaData, "_read_data_from_s3"
        )
        mock_arc_events_data.side_effect = [
            arc_events_data_missing_columns,
            market_order_data,
            mic_code_mapping,
        ]
        with pytest.raises(FAIL) as e:
            task.execute(fidessa_controller_output, params=input_params)
        assert e.type == FAIL

    def test_market_order_file_without_header(
        self,
        fidessa_controller_output: FrameProducerResult,
        arc_events_data: pd.DataFrame,
        market_order_data_without_header: pd.DataFrame,
        expected_result_all_col_in_source_df: pd.DataFrame,
        mic_code_mapping: pd.DataFrame,
        mocker,
        mocked_mic_mapping,
        input_params,
    ):
        task = PreProcessFidessaData(name="test-parties", params=input_params)
        mock_arc_events_data = mocker.patch.object(
            PreProcessFidessaData, "_read_data_from_s3"
        )
        mock_arc_events_data.side_effect = [
            arc_events_data,
            market_order_data_without_header,
        ]
        mock_source_dir = mocker.patch.object(PreProcessFidessaData, "_get_source_dir")
        mock_source_dir.return_value = Path(__file__).parent.joinpath("results")
        result = task.execute(fidessa_controller_output, params=input_params)
        result = pd.read_csv(result.path)
        result = result.loc[
            result[static.DerivedColumns.DERIVED_ORDER_ID].isin(
                ["00021939266FOLO1", "00021943843FOLO1|006MYzUVpUht"]
            ),
            [
                static.ArcEventsColumns.PRIMARY_ID,
                static.ArcEventsColumns.EXCHANGE_ORDER_ID,
                static.ArcEventsColumns.EVENT_TYPE,
                static.DerivedColumns.DERIVED_ORDER_ID,
                static.DerivedColumns.DERIVED_HIERARCHY,
                static.ArcEventsColumns.EVENT_TIMESTAMP,
                static.DerivedColumns.DERIVED_ORDER_RECEIVED_DT,
                static.DerivedColumns.DERIVED_ORDER_SUBMITTED_DT,
                static.DerivedColumns.DERIVED_AGGREGATE_ORDER_ID,
                static.DerivedColumns.DERIVED_ISIN_CODE,
                static.DerivedColumns.DERIVED_BUY_SELL,
                static.DerivedColumns.DERIVED_LIMIT_PRICE,
                static.DerivedColumns.DERIVED_STOP_PRICE,
                static.DerivedColumns.DERIVED_PARENT_ORDER_ID,
                static.DerivedColumns.DERIVED_INITIAL_QTY,
                static.DerivedColumns.DERIVED_BOOK_VIEW_CODE,
                static.DerivedColumns.DERIVED_ENTERED_BY,
                static.DerivedColumns.DERIVED_COUNTERPARTY_CODE,
                static.DerivedColumns.DERIVED_MARKET_ID,
                static.DerivedColumns.DERIVED_INVESTMENT_DECISION,
                static.DerivedColumns.DERIVED_EXECUTION_DECISION,
            ],
        ]
        assert result.fillna(pd.NA).equals(expected_result_all_col_in_source_df)

    def test_get_limit_price(
        self, limit_price_source_df, expected_result_get_limit_price
    ):
        """Tests the _get_limit_price static method using the required fixtures.
        The following tests are carried out:
        1. Limit prices for EREQ, EA, FE, FE, CA
        2. Limit prices for EREQ, EA, RA, FE, RA, FE

        For non-FE rows, the limit price should be retrieved from the ArcEventsColumns.LIMIT_PRICE column
        For FE rows, the limit price should be retrieved from the ArcEventsColumns.LIMIT_PRICE column of
        the previous EA or RA rows with the same order id
        """
        result = PreProcessFidessaData._get_limit_price(df=limit_price_source_df)
        assert result.equals(expected_result_get_limit_price)

    def test_get_venue(self, venue_computation_source_df, mocked_mic_mapping):
        """
        Tests the _get_venue static method using the required fixtures.

        For non-FE rows, the venue is computed by taking the first ArcEventsColumns.EXECUTION_VENUE
        with the group of Order ID where event_type is trade
        For FE rows, we take ArcEventsColumns.EXECUTION_VENUE as is.
        For row where we do not get any venue from the above logic we get it from the mic mapping file.
        """

        result = PreProcessFidessaData._get_venue(
            venue_computation_source_df, params=Params()
        )
        expected = pd.Series(
            ["AQXE", "AQXE", "AQXE", "XLON", "AQXE", "XLON"], name="EXECUTION_VENUE"
        )

        assert result.equals(expected)
