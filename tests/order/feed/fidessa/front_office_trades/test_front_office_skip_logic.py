import os
from unittest.mock import patch

import pandas as pd
import pytest
from prefect.engine import signals
from swarm.task.auditor import Auditor

from swarm_tasks.order.feed.fidessa.front_office_trades.front_office_skip_logic import (
    FrontOfficeSkipLogic,
)
from swarm_tasks.order.feed.fidessa.front_office_trades.front_office_skip_logic import (
    SourceColumns,
)


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    Creates an empty dataframe
    """
    df = pd.DataFrame()
    return df


@pytest.fixture()
def source_df() -> pd.DataFrame:
    """
    Creates a data frame for a source file to test all the skip logic
    """
    df = pd.DataFrame(
        [
            # Record 1: skipped: I with a C with a higher version number (skip_condition 1)
            {
                SourceColumns.STATE: "I",
                SourceColumns.TRADE_ID: "00025586591TRDU1-1",
                SourceColumns.VERSION_NUMBER: "1",
                SourceColumns.TRADE_TYPE: "TRADE",
                SourceColumns.EXCHANGE_TRADE_CODE: "OK",
                SourceColumns.DISCRETIONARY_ORDER: pd.NA,
                SourceColumns.BUSINESS_TRANSACTION_DESCRIPTION: "Broker Fill",
                SourceColumns.BOOK_ID: "NOT_NETTNG",
            },
            # Record 2: skipped: I with a C with a higher version number (skip_condition 1)
            {
                SourceColumns.STATE: "I",
                SourceColumns.TRADE_ID: "00025586591TRDU1-1",
                SourceColumns.VERSION_NUMBER: "2",
                SourceColumns.TRADE_TYPE: "TRADE",
                SourceColumns.EXCHANGE_TRADE_CODE: "OK",
                SourceColumns.DISCRETIONARY_ORDER: pd.NA,
                SourceColumns.BUSINESS_TRANSACTION_DESCRIPTION: "Broker Fill",
                SourceColumns.BOOK_ID: "NOT_NETTNG",
            },
            # Record 3: skipped because State=C (skip_condition 2)
            {
                SourceColumns.STATE: "C",
                SourceColumns.TRADE_ID: "00025586591TRDU1-1",
                SourceColumns.VERSION_NUMBER: "3",
                SourceColumns.TRADE_TYPE: "TRADE",
                SourceColumns.EXCHANGE_TRADE_CODE: "OK",
                SourceColumns.DISCRETIONARY_ORDER: pd.NA,
                SourceColumns.BUSINESS_TRANSACTION_DESCRIPTION: "Broker Fill",
                SourceColumns.BOOK_ID: "NOT_NETTNG",
            },
            # Record 4: not skipped. I with a version number > corresponding C
            {
                SourceColumns.STATE: "I",
                SourceColumns.TRADE_ID: "00025586591TRDU1-1",
                SourceColumns.VERSION_NUMBER: "4",
                SourceColumns.TRADE_TYPE: "TRADE",
                SourceColumns.EXCHANGE_TRADE_CODE: "NOTOK",
                SourceColumns.DISCRETIONARY_ORDER: pd.NA,
                SourceColumns.BUSINESS_TRANSACTION_DESCRIPTION: "Broker Fill",
                SourceColumns.BOOK_ID: "NOT_NETTNG",
            },
            # Record 5: skipped because Trade_Type = AGTD (skip_condition 3)
            {
                SourceColumns.STATE: "I",
                SourceColumns.TRADE_ID: "10025586591TRDU1-1",
                SourceColumns.VERSION_NUMBER: "1",
                SourceColumns.TRADE_TYPE: "AGTD",
                SourceColumns.EXCHANGE_TRADE_CODE: "OK",
                SourceColumns.DISCRETIONARY_ORDER: pd.NA,
                SourceColumns.BUSINESS_TRANSACTION_DESCRIPTION: "Broker Fill",
                SourceColumns.BOOK_ID: "NOT_NETTNG",
            },
            # Record 6: skipped because Trade_Type = BLOCK and BUSINESS_TRANSACTION_DESCRIPTION != Client trade (cond 4)
            {
                SourceColumns.STATE: "I",
                SourceColumns.TRADE_ID: "20025586591TRDU1-1",
                SourceColumns.VERSION_NUMBER: "1",
                SourceColumns.TRADE_TYPE: "BLOCK",
                SourceColumns.EXCHANGE_TRADE_CODE: "OK",
                SourceColumns.DISCRETIONARY_ORDER: pd.NA,
                SourceColumns.BUSINESS_TRANSACTION_DESCRIPTION: "Not Client trade",
                SourceColumns.BOOK_ID: "NOT_NETTNG",
            },
            # Record 7: skipped because Business_Transaction_Description = 'Booking'
            {
                SourceColumns.STATE: "I",
                SourceColumns.TRADE_ID: "40025586591TRDU1-1",
                SourceColumns.VERSION_NUMBER: "1",
                SourceColumns.TRADE_TYPE: "TRADE",
                SourceColumns.EXCHANGE_TRADE_CODE: "OK",
                SourceColumns.DISCRETIONARY_ORDER: pd.NA,
                SourceColumns.BUSINESS_TRANSACTION_DESCRIPTION: "Booking",
                SourceColumns.BOOK_ID: "NOT_NETTNG",
            },
            # Record 8: skipped because Business_Transaction_Description = 'Order warehousing transfer'
            {
                SourceColumns.STATE: "I",
                SourceColumns.TRADE_ID: "40025586591TRDU1-1",
                SourceColumns.VERSION_NUMBER: "1",
                SourceColumns.TRADE_TYPE: "TRADE",
                SourceColumns.EXCHANGE_TRADE_CODE: "OK",
                SourceColumns.DISCRETIONARY_ORDER: pd.NA,
                SourceColumns.BUSINESS_TRANSACTION_DESCRIPTION: "Order warehousing transfer",
                SourceColumns.BOOK_ID: "NOT_NETTNG",
            },
            # Record 9: skipeed because Book_Id = 'NETTNG'
            {
                SourceColumns.STATE: "I",
                SourceColumns.TRADE_ID: "00025586591TRDU1-1",
                SourceColumns.VERSION_NUMBER: "4",
                SourceColumns.TRADE_TYPE: "TRADE",
                SourceColumns.EXCHANGE_TRADE_CODE: "OK",
                SourceColumns.DISCRETIONARY_ORDER: pd.NA,
                SourceColumns.BUSINESS_TRANSACTION_DESCRIPTION: "Broker Fill",
                SourceColumns.BOOK_ID: "NETTNG",
            },
        ]
    )
    return df


@pytest.fixture()
def expected_result_for_source_df():
    """Expected result for execution with source_frame"""
    df = pd.DataFrame(
        [
            # Record 4: not skipped. I with a version number > corresponding C
            {
                SourceColumns.STATE: "I",
                SourceColumns.TRADE_ID: "00025586591TRDU1-1",
                SourceColumns.VERSION_NUMBER: "4",
                SourceColumns.TRADE_TYPE: "TRADE",
                SourceColumns.EXCHANGE_TRADE_CODE: "NOTOK",
                SourceColumns.DISCRETIONARY_ORDER: pd.NA,
                SourceColumns.BUSINESS_TRANSACTION_DESCRIPTION: "Broker Fill",
                SourceColumns.BOOK_ID: "NOT_NETTNG",
            },
        ],
        index=[3],
    )
    return df


@pytest.fixture()
def skip_all_rows_df():
    """Source frame for which all rows need to be skipped"""
    df = pd.DataFrame(
        [
            # Record 1: skipped because Business_Transaction_Description = 'Booking'
            {
                SourceColumns.STATE: "I",
                SourceColumns.TRADE_ID: "30025586591TRDU1-1",
                SourceColumns.VERSION_NUMBER: "4",
                SourceColumns.TRADE_TYPE: "TRADE",
                SourceColumns.EXCHANGE_TRADE_CODE: "OK",
                SourceColumns.DISCRETIONARY_ORDER: pd.NA,
                SourceColumns.BUSINESS_TRANSACTION_DESCRIPTION: "Booking",
                SourceColumns.BOOK_ID: "NOT_NETTNG",
            },
            # Record 2: skipped because Book_Id = 'NETTNG'
            {
                SourceColumns.STATE: "I",
                SourceColumns.TRADE_ID: "00025586591TRDU1-1",
                SourceColumns.VERSION_NUMBER: "4",
                SourceColumns.TRADE_TYPE: "TRADE",
                SourceColumns.EXCHANGE_TRADE_CODE: "OK",
                SourceColumns.DISCRETIONARY_ORDER: pd.NA,
                SourceColumns.BUSINESS_TRANSACTION_DESCRIPTION: "Broker Fill",
                SourceColumns.BOOK_ID: "NETTNG",
            },
        ]
    )
    return df


class TestFrontOfficeSkipLogic:
    """Class to hold all the test cases for FrontOfficeSkipLogic"""

    def test_empty_source_frame(self, empty_source_df):
        """Test for empty source frame"""
        os.environ["SWARM_FILE_URL"] = "/path/ORDER_PROGRESS-202202081830.csv"
        with pytest.raises(signals.SKIP):
            task = FrontOfficeSkipLogic(name="Front Office Skip logic")
            task.execute(source_frame=empty_source_df)

    @patch.object(FrontOfficeSkipLogic, "auditor")
    def test_all_cases(
        self,
        mock_auditor,
        source_df,
        expected_result_for_source_df,
    ):
        """Tests all possible cases for a non-empty source frame."""
        mock_auditor.return_value = Auditor(task_name="FrontOfficeSkipLogic")
        task = FrontOfficeSkipLogic(name="Front Office Skip logic")
        result = task.execute(source_frame=source_df)
        assert result.equals(expected_result_for_source_df)

    @patch.object(FrontOfficeSkipLogic, "auditor")
    def test_skip_all_rows(self, mock_auditor, skip_all_rows_df):
        """Test for the case where all the rows in the source frame are skipped"""
        mock_auditor.return_value = Auditor(task_name="FrontOfficeSkipLogic")
        task = FrontOfficeSkipLogic(name="FrontOfficeSkipLogic")
        with pytest.raises(signals.SKIP):
            task.execute(source_frame=skip_all_rows_df)
