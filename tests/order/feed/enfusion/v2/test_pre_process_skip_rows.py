import os
from pathlib import Path

from swarm.task.io.read.result import Extract<PERSON>ath<PERSON>esult

from swarm_tasks.order.feed.enfusion.v2.pre_process_skip_rows import Params
from swarm_tasks.order.feed.enfusion.v2.pre_process_skip_rows import PreProcessSkipRows

TEST_FILE_PATH = Path(__file__).parent
TEST_DATA_DIR_PATH = TEST_FILE_PATH.joinpath("data")
EXAMPLE_DATA = TEST_DATA_DIR_PATH.joinpath("Example_Executions.csv")
EXAMPLE_DATA_WITHOUT_REQD_COLUMNS = TEST_DATA_DIR_PATH.joinpath(
    "Example_Executions_with_out_reqd_columns.csv"
)


class TestPreProcessSkipRows:
    def test_executions_file(self):
        self.set_env_variable("SWARM_FILE_URL", "/test/dir/Executions.csv")
        extract_path_result = ExtractPathResult(path=EXAMPLE_DATA)
        task = PreProcessSkipRows(name="PreProcessSkipRows", params=Params())
        result = task.process(extractor_result=extract_path_result, params=Params())
        assert sorted(result) == [3, 4, 5, 6, 7, 8]

    def test_allocations_file(self):
        self.set_env_variable("SWARM_FILE_URL", "/test/dir/Allocations.csv")
        task = PreProcessSkipRows(name="PreProcessSkipRows", params=Params())
        result = task.process(extractor_result="")
        assert result == list()

    def test_execution_without_reqd_columns(self):
        self.set_env_variable("SWARM_FILE_URL", "/test/dir/Executions.csv")
        extract_path_result = ExtractPathResult(path=EXAMPLE_DATA_WITHOUT_REQD_COLUMNS)
        task = PreProcessSkipRows(name="PreProcessSkipRows", params=Params())
        result = task.process(extractor_result=extract_path_result, params=Params())
        assert result == list()

    @staticmethod
    def set_env_variable(key, value):
        if key in os.environ:
            del os.environ[key]

        os.environ.setdefault(key, value)
