import pandas as pd
import pytest

from swarm_tasks.order.feed.enfusion.v2.allocation_frame_transformer import (
    AllocationFrameTransformer,
)
from swarm_tasks.order.feed.enfusion.v2.allocation_frame_transformer import (
    SourceColumns,
)


@pytest.fixture()
def sample_source_frame_for_partial_fill_condition() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            SourceColumns.ORDER_ID: [1, 2, 3],
            SourceColumns.ORDER_TOTAL_QUANTITY: [1.0, 2.0, 3.0],
            SourceColumns.ORDER_CUMULATIVE_QUANTITY: [2.0, 3.0, 4.0],
            SourceColumns.QUANTITY: [1.0, 2.0, 3.0],
            SourceColumns.REPORT_STATUS: ["NEWT", "NEWT", "NEWT"],
        }
    )
    return df


@pytest.fixture()
def sample_source_frame_for_fill_condition() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            SourceColumns.ORDER_ID: [1, 2, 3],
            SourceColumns.ORDER_TOTAL_QUANTITY: [1.0, 2.0, 3.0],
            SourceColumns.ORDER_CUMULATIVE_QUANTITY: [1.0, 2.0, 3.0],
            SourceColumns.QUANTITY: [1.0, 2.0, 3.0],
            SourceColumns.REPORT_STATUS: ["NEWT", "NEWT", "NEWT"],
        }
    )
    return df


@pytest.fixture()
def sample_source_frame_for_cancel_condition() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            SourceColumns.ORDER_ID: [1],
            SourceColumns.ORDER_TOTAL_QUANTITY: [1.0],
            SourceColumns.ORDER_CUMULATIVE_QUANTITY: [2.0],
            SourceColumns.QUANTITY: [1.0],
            SourceColumns.REPORT_STATUS: ["CANC"],
        }
    )
    return df


class TestEnfusionAllocationFrameTransformer:
    """
    Test Enfusion Record Creation Logic
    """

    def test_for_partial_fill_scenario(
        self, sample_source_frame_for_partial_fill_condition
    ):
        task = AllocationFrameTransformer(name="test_task")
        result = task.execute(
            source_frame=sample_source_frame_for_partial_fill_condition
        )
        expected_frame = pd.DataFrame(
            [
                {
                    "ORDERID": 1,
                    "ORDERTOTALQUANTITY": 1.0,
                    "ORDERCUMULATIVEQUANTITY": 2.0,
                    "QUANTITY": 1.0,
                    "REPORTSTATUS": "NEWT",
                    "__order_status__": "NEWO",
                    "__record_type__": "allocation_record",
                },
                {
                    "ORDERID": 2,
                    "ORDERTOTALQUANTITY": 2.0,
                    "ORDERCUMULATIVEQUANTITY": 3.0,
                    "QUANTITY": 2.0,
                    "REPORTSTATUS": "NEWT",
                    "__order_status__": "NEWO",
                    "__record_type__": "allocation_record",
                },
                {
                    "ORDERID": 3,
                    "ORDERTOTALQUANTITY": 3.0,
                    "ORDERCUMULATIVEQUANTITY": 4.0,
                    "QUANTITY": 3.0,
                    "REPORTSTATUS": "NEWT",
                    "__order_status__": "NEWO",
                    "__record_type__": "allocation_record",
                },
                {
                    "ORDERID": 1,
                    "ORDERTOTALQUANTITY": 1.0,
                    "ORDERCUMULATIVEQUANTITY": 2.0,
                    "QUANTITY": 1.0,
                    "REPORTSTATUS": "NEWT",
                    "__order_status__": "PARF",
                    "__record_type__": "allocation_record",
                },
                {
                    "ORDERID": 2,
                    "ORDERTOTALQUANTITY": 2.0,
                    "ORDERCUMULATIVEQUANTITY": 3.0,
                    "QUANTITY": 2.0,
                    "REPORTSTATUS": "NEWT",
                    "__order_status__": "PARF",
                    "__record_type__": "allocation_record",
                },
                {
                    "ORDERID": 3,
                    "ORDERTOTALQUANTITY": 3.0,
                    "ORDERCUMULATIVEQUANTITY": 4.0,
                    "QUANTITY": 3.0,
                    "REPORTSTATUS": "NEWT",
                    "__order_status__": "PARF",
                    "__record_type__": "allocation_record",
                },
                {
                    "ORDERID": 1,
                    "ORDERTOTALQUANTITY": 1.0,
                    "ORDERCUMULATIVEQUANTITY": 2.0,
                    "QUANTITY": 1.0,
                    "REPORTSTATUS": "NEWT",
                    "__order_status__": "NEWO",
                    "__record_type__": "market_record",
                },
                {
                    "ORDERID": 2,
                    "ORDERTOTALQUANTITY": 2.0,
                    "ORDERCUMULATIVEQUANTITY": 3.0,
                    "QUANTITY": 2.0,
                    "REPORTSTATUS": "NEWT",
                    "__order_status__": "NEWO",
                    "__record_type__": "market_record",
                },
                {
                    "ORDERID": 3,
                    "ORDERTOTALQUANTITY": 3.0,
                    "ORDERCUMULATIVEQUANTITY": 4.0,
                    "QUANTITY": 3.0,
                    "REPORTSTATUS": "NEWT",
                    "__order_status__": "NEWO",
                    "__record_type__": "market_record",
                },
            ]
        )

        pd.testing.assert_frame_equal(left=result, right=expected_frame)

    def test_for_fill_scenario(
        self,
        sample_source_frame_for_fill_condition,
    ):
        task = AllocationFrameTransformer(name="test_task")
        result = task.execute(source_frame=sample_source_frame_for_fill_condition)
        expected = pd.DataFrame(
            [
                {
                    "ORDERID": 1,
                    "ORDERTOTALQUANTITY": 1.0,
                    "ORDERCUMULATIVEQUANTITY": 1.0,
                    "QUANTITY": 1.0,
                    "REPORTSTATUS": "NEWT",
                    "__order_status__": "NEWO",
                    "__record_type__": "allocation_record",
                },
                {
                    "ORDERID": 2,
                    "ORDERTOTALQUANTITY": 2.0,
                    "ORDERCUMULATIVEQUANTITY": 2.0,
                    "QUANTITY": 2.0,
                    "REPORTSTATUS": "NEWT",
                    "__order_status__": "NEWO",
                    "__record_type__": "allocation_record",
                },
                {
                    "ORDERID": 3,
                    "ORDERTOTALQUANTITY": 3.0,
                    "ORDERCUMULATIVEQUANTITY": 3.0,
                    "QUANTITY": 3.0,
                    "REPORTSTATUS": "NEWT",
                    "__order_status__": "NEWO",
                    "__record_type__": "allocation_record",
                },
                {
                    "ORDERID": 1,
                    "ORDERTOTALQUANTITY": 1.0,
                    "ORDERCUMULATIVEQUANTITY": 1.0,
                    "QUANTITY": 1.0,
                    "REPORTSTATUS": "NEWT",
                    "__order_status__": "FILL",
                    "__record_type__": "allocation_record",
                },
                {
                    "ORDERID": 2,
                    "ORDERTOTALQUANTITY": 2.0,
                    "ORDERCUMULATIVEQUANTITY": 2.0,
                    "QUANTITY": 2.0,
                    "REPORTSTATUS": "NEWT",
                    "__order_status__": "FILL",
                    "__record_type__": "allocation_record",
                },
                {
                    "ORDERID": 3,
                    "ORDERTOTALQUANTITY": 3.0,
                    "ORDERCUMULATIVEQUANTITY": 3.0,
                    "QUANTITY": 3.0,
                    "REPORTSTATUS": "NEWT",
                    "__order_status__": "FILL",
                    "__record_type__": "allocation_record",
                },
                {
                    "ORDERID": 1,
                    "ORDERTOTALQUANTITY": 1.0,
                    "ORDERCUMULATIVEQUANTITY": 1.0,
                    "QUANTITY": 1.0,
                    "REPORTSTATUS": "NEWT",
                    "__order_status__": "NEWO",
                    "__record_type__": "market_record",
                },
                {
                    "ORDERID": 2,
                    "ORDERTOTALQUANTITY": 2.0,
                    "ORDERCUMULATIVEQUANTITY": 2.0,
                    "QUANTITY": 2.0,
                    "REPORTSTATUS": "NEWT",
                    "__order_status__": "NEWO",
                    "__record_type__": "market_record",
                },
                {
                    "ORDERID": 3,
                    "ORDERTOTALQUANTITY": 3.0,
                    "ORDERCUMULATIVEQUANTITY": 3.0,
                    "QUANTITY": 3.0,
                    "REPORTSTATUS": "NEWT",
                    "__order_status__": "NEWO",
                    "__record_type__": "market_record",
                },
            ]
        )

        pd.testing.assert_frame_equal(left=result, right=expected)

    def test_for_cancels(self, sample_source_frame_for_cancel_condition):
        task = AllocationFrameTransformer(name="test_task")
        result = task.execute(source_frame=sample_source_frame_for_cancel_condition)
        expected_frame = pd.DataFrame(
            [
                {
                    "ORDERID": 1,
                    "ORDERTOTALQUANTITY": 1.0,
                    "ORDERCUMULATIVEQUANTITY": 2.0,
                    "QUANTITY": 1.0,
                    "REPORTSTATUS": "CANC",
                    "__order_status__": "NEWO",
                    "__record_type__": "allocation_record",
                },
                {
                    "ORDERID": 1,
                    "ORDERTOTALQUANTITY": 1.0,
                    "ORDERCUMULATIVEQUANTITY": 2.0,
                    "QUANTITY": 1.0,
                    "REPORTSTATUS": "CANC",
                    "__order_status__": "CAME",
                    "__record_type__": "allocation_record",
                },
                {
                    "ORDERID": 1,
                    "ORDERTOTALQUANTITY": 1.0,
                    "ORDERCUMULATIVEQUANTITY": 2.0,
                    "QUANTITY": 1.0,
                    "REPORTSTATUS": "CANC",
                    "__order_status__": "NEWO",
                    "__record_type__": "market_record",
                },
            ]
        )

        pd.testing.assert_frame_equal(left=result, right=expected_frame)
