��      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�prim_rpl_symbol��prim_rpl_portfolio��prim_rpl_portfolio-side��prim_rpl_destination��prim_rpl_reference-orderid��prim_rpl_order-id��prim_rpl_ordered-shrs��prim_rpl_limit-price��prim_rpl_order-time��prim_rpl_order-status��prim_rpl_fixtags��prim_rpl_user-initials��(prim_rpl_incoming-fix-orderid/strategyid��exe_exec-shares��
exe_symbol��exe_userfixtag��original_source_key�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(h7N�start�K �stop�K�step�Ku��R�e]�(hhK ��h��R�(KKK��h!�]�(�KED.AU��SLOMS_317049��SEL��MACQUA�G�      �CAA0003-20211025��27757��MKT-DAY��2021-10-24 22:03:34��CXLD��128=2;10001=ASC;15=AUD��RUP��
1444355095OMS��1300��KED.AU��j115=MACQ;15=AUD;20=0;22=2;29=1;30=XASX;48=BMBJWP8;49=MACQUA;60=20211025-03:05:02.712;150=1;151=26457;851=2�et�bhhK ��h��R�(KKK��h!�]��k/Users/<USER>/Github/se-prefect-dir/swarm-tasks/tests/order/feed/flextrade/data/FSI-FT-report-20211024.csv�at�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4et�bh7Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bh7Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hF�mgr_locs��builtins��slice���K KK��R�u}�(hzh[h{h~KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.