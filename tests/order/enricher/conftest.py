import asyncio
import os

import pandas as pd
import pytest

# need to set these environment variables for the tests to run
os.environ["COGNITO_CLIENT_ID"] = "some_id"
os.environ["COGNITO_CLIENT_SECRET"] = "some_secret"
os.environ["COGNITO_AUTH_URL"] = "some_auth_url"

from market_data_sdk.schema.api.components import APIResponse  # noqa: E402
from market_data_sdk.schema.api.components import APIResponseStatus  # noqa: E402
from se_elastic_schema.components.reference.cfi import Cfi  # noqa: E402
from se_elastic_schema.models.efd.ric_lookup import RicLookup  # noqa: E402

from swarm_tasks.order.universal.order_enricher import (  # noqa: E402
    OrderEODStatsEnricher,
)
from swarm_tasks.transform.steeleye.orders.best_execution.static import (  # noqa: E402
    BestExecutionFields,
)


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    return pd.DataFrame()


@pytest.fixture
def source_frame_with_values():
    result = pd.DataFrame(
        {
            BestExecutionFields.INSTRUMENT_DETAILS_INSTRUMENT: [
                {
                    "sourceKey": "FirdsInstrument:US0079031078USDBERB:1622947716000",
                    "instrumentClassificationEMIRAssetClass": "EQ",
                    "&id": "XS1002121454EURFRAB",
                    "cfiGroup": "Equity",
                    "cfiCategory": "Forwards",
                    "commoditiesOrEmissionAllowanceDerivativeInd": pd.NA,
                    "instrumentFullName": "ADVANCED MICRO DEVICES INC.   Registered Shares DL -,01 CFD",
                    "&key": "SteelEyeInstrument:XS1002121454EURFRAB:1623181613",
                    "notionalCurrency1": "USD",
                    "instrumentClassificationEMIRProductType": "CFD",
                    "instrumentClassificationEMIRContractType": "CD",
                    "cfiAttribute1": "Single Stock",
                    "cfiAttribute2": "Undefined",
                    "cfiAttribute3": "Contract for difference",
                    "cfiAttribute4": "Cash",
                    "instrumentClassification": "JESXCC",
                    "ext.aii.mic": pd.NA,
                    "ext.emirEligible": True,
                    "ext.underlyingOnFIRDS": True,
                    "ext.alternativeInstrumentIdentifier": "XXXXUS0079031078CFD",
                    "ext.mifirEligible": True,
                    "ext.otcFlag": True,
                    "ext.bestExAssetClassSub": "Swaps and other equity derivatives",
                    "ext.exchangeSymbol": pd.NA,
                    "ext.bestExAssetClassMain": "Equity Derivatives",
                    "ext.quantityNotation": "UNIT",
                    "ext.underlyingInstruments": pd.NA,
                    "ext.instrumentUniqueIdentifier": "XS1002121454EURFRAB",
                    "ext.onFIRDS": pd.NA,
                    "ext.priceNotation": "MONE",
                    "venue.tradingVenue": "XXXX",
                    "venue.fipd.NAcialInstrumentShortName": pd.NA,
                    "commodityAndEmissionAllowances.subProduct": pd.NA,
                    "commodityAndEmissionAllowances.baseProduct": pd.NA,
                    "derivative.underlyingIndexName": pd.NA,
                    "derivative.underlyingIndexTerm": pd.NA,
                    "derivative.underlyingIndexTermValue": pd.NA,
                    "derivative.underlyingInstruments": [
                        {
                            "underlyingInstrumentCode": "US0079031078",
                            "underlyingInstrumentClassification": "ESVUFR",
                        }
                    ],
                    "derivative.deliveryType": "CASH",
                    "derivative.priceMultiplier": 100.0,
                    "instrumentIdCode": pd.NA,
                    "issuerOrOperatorOfTradingVenueId": pd.NA,
                    "ext.aii.exchangeProductCode": pd.NA,
                    "ext.tradingVenueCountry": pd.NA,
                    "ext.venueType": pd.NA,
                    "ext.strikePriceType": pd.NA,
                    "ext.tradingVenueCountryCode": pd.NA,
                    "ext.venueInEEA": pd.NA,
                    "ext.figi": pd.NA,
                    "ext.pricingReferences.ICE": "isin/US0079031078/USD",
                    "ext.venueName": "OTC",
                    "ext.compositeFigi": pd.NA,
                    "ext.venueOutsideEEA": pd.NA,
                    "ext.localCode": pd.NA,
                    "ext.instrumentIdCodeType": "ID",
                    "derivative.optionType": pd.NA,
                    "derivative.expiryDate": pd.NA,
                    "derivative.optionExerciseStyle": pd.NA,
                    "derivative.strikePriceCurrency": pd.NA,
                    "derivative.strikePrice": pd.NA,
                    "bond.maturityDate": pd.NA,
                    "bond.fixedRate": pd.NA,
                    "ext.underlyingVenueOutsideEEA": False,
                    "ext.underlyingOtcFlag": True,
                    "ext.underlyingVenueInEEA": True,
                },
            ],
        }
    )
    return result


def custom_order_data_side_effect(*args):
    custom_coroutine = asyncio.Future()
    function_args = args[1]
    result = None
    if "instrument_id" in function_args.keys():
        result = (
            RicLookup(
                id__="XS1002121454EURFRAB",
                cfi=Cfi(code="DBVJPR"),
                instrumentUniqueIdentifier="XXXX",
            ),
            "NL100212145=RRPS",
        )

    if "ric" in function_args.keys():
        market_eod_df = pd.DataFrame(
            {"Date": ["2021-06-01"], "Currency": ["USD"], "Close Price": [111]}
        )
        result = APIResponse(
            ric_selected="NL100212145=RRPS",
            data=market_eod_df,
            status=APIResponseStatus.SUCCESS,
        )
    custom_coroutine.set_result(result)
    return custom_coroutine


@pytest.fixture
def mock_no_response_market_data_call(mocker):
    """ """
    custom_coroutine = asyncio.Future()
    custom_coroutine.set_result(None)
    mock_async_func_call = mocker.patch.object(OrderEODStatsEnricher, "call_func")
    mock_async_func_call.return_value = custom_coroutine
    return mock_async_func_call


@pytest.fixture
def mock_market_data_async_call_with_response(mocker):
    mock_async_func_call = mocker.patch.object(OrderEODStatsEnricher, "call_func")
    mock_async_func_call.side_effect = custom_order_data_side_effect
    return mock_async_func_call


@pytest.fixture()
def expected_market_data() -> pd.DataFrame:
    """
    Creates test eod market dataframe
    """
    market_eod_df = pd.DataFrame(
        {"Date": ["2021-06-01"], "Currency": ["USD"], "Close Price": [111]}
    )
    result = pd.DataFrame(
        {
            "instrumentDetails.instrument": ["XS1002121454EURFRAB"],
            "__ric__": ["NL100212145=RRPS"],
            "__eod_data__": [market_eod_df.to_dict(orient="records")],
        }
    )

    return result
