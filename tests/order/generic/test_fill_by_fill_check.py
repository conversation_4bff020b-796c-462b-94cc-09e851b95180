import pandas as pd
import pytest
from pandas.testing import assert_frame_equal

from swarm_tasks.order.generic.fill_by_fill_check import <PERSON>llByFill<PERSON>heck
from swarm_tasks.order.generic.fill_by_fill_check import Params


@pytest.fixture()
def expected_result(expected_flat_average_df):
    expected_result = expected_flat_average_df.drop(columns=["__order_id__"])
    expected_result["_orderState.priceFormingData.remainingQuantity"] = pd.Series(
        [24.0, 402.0, 490.0]
    )
    return expected_result


@pytest.fixture()
def params_data():
    return {
        "fill_by_fill_flag": False,
        "initial_quantity": "priceFormingData.initialQuantity",
        "remaining_quantity": "_orderState.priceFormingData.remainingQuantity",
        "fill_quantity": "_orderState.priceFormingData.tradedQuantity",
        "order_id": "_order.id",
        "execution_price": "__last_px__",
        "order_status": "_orderState.executionDetails.orderStatus",
        "trading_date_time": "_orderState.transactionDetails.tradingDateTime",
        "route_id": "_order.id",
    }


@pytest.fixture()
def params_missing_data(params_data):
    params_list = [{"fill_by_fill_flag": False}]

    temp_params = params_data.copy()
    temp_params["order_id"] = ""
    params_list.append(temp_params)

    temp_params = params_data.copy()
    del temp_params["route_id"]
    params_list.append(temp_params)
    return params_list


class TestFillByFillCheck:
    """Test suite for FillByFillCheck task"""

    def test_fill_by_fill_check(
        self, source_flat_average_df, params_data, expected_result
    ):
        params = Params(**params_data)
        task = FillByFillCheck(name="FillByFillCheck", params=params)
        result = task.execute(source_frame=source_flat_average_df, params=params)
        result = result.reset_index(drop=True)
        assert_frame_equal(result, expected_result)

    def test_fill_by_fill_false(self, source_flat_average_df, expected_result):
        params = Params(fill_by_fill_flag=True)
        task = FillByFillCheck(name="FillByFillCheck", params=params)
        result = task.execute(source_frame=source_flat_average_df, params=params)
        assert_frame_equal(result, source_flat_average_df)


class TestParams:
    """Test suite for Params class"""

    def test_validation_fill_by_fill_flag_true(self, params_missing_data):
        for param in params_missing_data:
            param["fill_by_fill_flag"] = True
            Params(**param)

    def test_params_missing_values(self, params_missing_data):
        for param in params_missing_data:
            with pytest.raises(ValueError):
                Params(**param)
