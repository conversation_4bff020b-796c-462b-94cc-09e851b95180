from pathlib import Path

import pandas as pd
from swarm.task.auditor import Auditor

from swarm_tasks.order.generic.vdi_link_instrument.fetch_vdi_and_split_multileg_trades import (
    FetchInstruments,
)
from swarm_tasks.order.generic.vdi_link_instrument.fetch_vdi_and_split_multileg_trades import (
    FetchVDIAndSplitMultiLegTrades,
)
from swarm_tasks.order.generic.vdi_link_instrument.fetch_vdi_and_split_multileg_trades import (
    Params,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
SOURCE_FRAME = TEST_FILES_DIR.joinpath("source_frame.pkl")
SOURCE_FRAME_WITHOUT_MULTILEG = TEST_FILES_DIR.joinpath(
    "source_frame_without_multileg.pkl"
)
SOURCE_FRAME_MULTILEG_TEST = TEST_FILES_DIR.joinpath("multileg_source_pkl.pkl")
EXPECTED_FRAME_1 = TEST_FILES_DIR.joinpath("expected_data_1.pkl")
EXPECTED_FRAME_2 = TEST_FILES_DIR.joinpath("expected_data_2.pkl")
EXPECTED_FRAME_NO_INSTR_NOT_DISCRDED = TEST_FILES_DIR.joinpath(
    "expected_data_no_discard.pkl"
)
EXPECTED_FRAME_BUY_SELL_NUM = TEST_FILES_DIR.joinpath("expected_data_buysell_num.pkl")
EXPECTED_FRAME_WITHOUT_MULTILEG = TEST_FILES_DIR.joinpath(
    "expected_data_without_multilegs.pkl"
)
EXPECTED_MULTILEG_TEST_FRAME = TEST_FILES_DIR.joinpath(
    "expected_multileg_result_pkl.pkl"
)
EXPECTED_FRAME_WITH_MULTILEG_REPORTING_TYPE = TEST_FILES_DIR.joinpath(
    "expected_result_with_multi_leg_reporting_type"
)


class TestFetchVDIAndSplitMultiLegTrades:
    """Tests end to end fetching of vdi instruments for fix files"""

    def test_with_empty_source_frame(self):
        params = Params(
            security_id_col="__symbol__",
            trade_date="date",
            instrument="instrumentDetails.instrument",
            exchange="CME",
        )
        task = FetchVDIAndSplitMultiLegTrades(
            name="FetchVDIAndSplitMultiLegTrades", params=params
        )

        result = task.execute(source_frame=pd.DataFrame(), params=params)
        assert result.empty

    def test_with_non_empty_source_frame(
        self, mocker, fetch_instruments_1, fetch_instruments_2, fetch_instruments_3
    ):
        params = Params(
            security_id_col="__symbol__",
            trade_date="date",
            instrument="instrumentDetails.instrument",
            exchange="ICE",
        )
        source_frame = pd.read_pickle(SOURCE_FRAME)
        expected_frame = pd.read_pickle(EXPECTED_FRAME_1)
        task = FetchVDIAndSplitMultiLegTrades(
            name="FetchVDIAndSplitMultiLegTrades", params=params
        )
        auditor = Auditor(task_name="Dummy")
        task._auditor = auditor

        mock_fetch_instruments = mocker.patch.object(FetchInstruments, "process")
        mock_fetch_instruments.side_effect = [
            fetch_instruments_1,
            fetch_instruments_2,
            fetch_instruments_3,
        ]
        result = task.execute(source_frame=source_frame, params=params)
        pd.testing.assert_frame_equal(left=result, right=expected_frame)

    def test_with_non_empty_source_frame_missing_ml_instrument(
        self, mocker, fetch_instruments_1, fetch_instruments_2, fetch_instruments_3
    ):
        """Test when at one level, the multileg instrument isn't found in srp"""
        params = Params(
            security_id_col="__symbol__",
            trade_date="date",
            instrument="instrumentDetails.instrument",
            exchange="ICE",
        )
        source_frame = pd.read_pickle(SOURCE_FRAME)
        expected_frame = pd.read_pickle(EXPECTED_FRAME_2)
        task = FetchVDIAndSplitMultiLegTrades(
            name="FetchVDIAndSplitMultiLegTrades", params=params
        )
        auditor = Auditor(task_name="Dummy")
        task._auditor = auditor

        mock_fetch_instruments = mocker.patch.object(FetchInstruments, "process")
        fetch_instruments_2.iloc[1] = pd.NA
        fetch_instruments_3 = fetch_instruments_3.iloc[:3]

        mock_fetch_instruments.side_effect = [
            fetch_instruments_1,
            fetch_instruments_2,
            fetch_instruments_3,
        ]
        result = task.execute(source_frame=source_frame, params=params)
        pd.testing.assert_frame_equal(left=result, right=expected_frame)

    def test_with_no_multilegs_and_few_null_security_ids(
        self, mocker, fetch_instruments_no_ml_and_null_security_ids
    ):
        """Test when at one level, the multileg instrument isn't found in srp"""
        params = Params(
            security_id_col="__symbol__",
            trade_date="date",
            instrument="instrumentDetails.instrument",
            exchange="ICE",
        )
        source_frame = pd.read_pickle(SOURCE_FRAME_WITHOUT_MULTILEG)
        expected_frame = pd.read_pickle(EXPECTED_FRAME_WITHOUT_MULTILEG)
        task = FetchVDIAndSplitMultiLegTrades(
            name="FetchVDIAndSplitMultiLegTrades", params=params
        )
        auditor = Auditor(task_name="Dummy")
        task._auditor = auditor

        mock_fetch_instruments = mocker.patch.object(FetchInstruments, "process")

        mock_fetch_instruments.return_value = (
            fetch_instruments_no_ml_and_null_security_ids
        )
        result = task.execute(source_frame=source_frame, params=params)
        pd.testing.assert_frame_equal(left=result, right=expected_frame)

    def test_with_missing_instruments_not_discarded(
        self,
        mocker,
        fetch_instruments_1: pd.DataFrame,
        fetch_instruments_2: pd.DataFrame,
        fetch_instruments_3: pd.DataFrame,
    ):
        """Test when at one level, the multileg instrument isn't found in srp.
        But due to the presence of the audit_and_remove_missing_instruments
        param (set to False), these records are not discarded.
        """
        params = Params(
            security_id_col="__symbol__",
            trade_date="date",
            instrument="instrumentDetails.instrument",
            exchange="ICE",
            audit_and_remove_missing_instruments=False,
        )
        source_frame = pd.read_pickle(SOURCE_FRAME)
        expected_frame = pd.read_pickle(EXPECTED_FRAME_NO_INSTR_NOT_DISCRDED)
        task = FetchVDIAndSplitMultiLegTrades(
            name="FetchVDIAndSplitMultiLegTrades", params=params
        )
        auditor = Auditor(task_name="Dummy")
        task._auditor = auditor

        mock_fetch_instruments = mocker.patch.object(FetchInstruments, "process")
        fetch_instruments_2.iloc[1] = pd.NA
        fetch_instruments_3 = fetch_instruments_3.iloc[:3]

        mock_fetch_instruments.side_effect = [
            fetch_instruments_1,
            fetch_instruments_2,
            fetch_instruments_3,
        ]
        result = task.execute(source_frame=source_frame, params=params)
        pd.testing.assert_frame_equal(left=result, right=expected_frame)

    def test_buy_sell_changed_to_number(
        self,
        mocker,
        fetch_instruments_1: pd.DataFrame,
        fetch_instruments_2: pd.DataFrame,
        fetch_instruments_3: pd.DataFrame,
    ):
        """Test for the case where there are multi-legs and the buySell
        is converted to a number through a param.
        """
        params = Params(
            security_id_col="__symbol__",
            trade_date="date",
            instrument="instrumentDetails.instrument",
            exchange="ICE",
            convert_buysell_from_enum_to_number=True,
        )
        source_frame = pd.read_pickle(SOURCE_FRAME)
        expected_frame = pd.read_pickle(EXPECTED_FRAME_BUY_SELL_NUM)
        task = FetchVDIAndSplitMultiLegTrades(
            name="FetchVDIAndSplitMultiLegTrades", params=params
        )
        auditor = Auditor(task_name="Dummy")
        task._auditor = auditor

        mock_fetch_instruments = mocker.patch.object(FetchInstruments, "process")
        fetch_instruments_2.iloc[1] = pd.NA
        fetch_instruments_3 = fetch_instruments_3.iloc[:3]

        mock_fetch_instruments.side_effect = [
            fetch_instruments_1,
            fetch_instruments_2,
            fetch_instruments_3,
        ]
        result = task.execute(source_frame=source_frame, params=params)
        pd.testing.assert_frame_equal(left=result, right=expected_frame)

    def test_multileg_trades(
        self,
        mocker,
        fetch_multileg_instruments_1,
        fetch_multileg_instruments_2,
    ):
        params = Params(
            security_id_col="__symbol__",
            trade_date="date",
            instrument="instrumentDetails.instrument",
            exchange="ICE",
        )
        source_frame = pd.read_pickle(SOURCE_FRAME_MULTILEG_TEST)
        expected_frame = pd.read_pickle(EXPECTED_MULTILEG_TEST_FRAME)

        task = FetchVDIAndSplitMultiLegTrades(
            name="FetchVDIAndSplitMultiLegTrades", params=params
        )
        auditor = Auditor(task_name="Dummy")
        task._auditor = auditor

        mock_fetch_instruments = mocker.patch.object(FetchInstruments, "process")
        mock_fetch_instruments.side_effect = [
            fetch_multileg_instruments_1,
            fetch_multileg_instruments_2,
        ]
        result = task.execute(source_frame=source_frame, params=params)
        pd.testing.assert_frame_equal(left=result, right=expected_frame)

    def test_add_multi_leg_reporting_type_column(
        self,
        mocker,
        fetch_instruments_1: pd.DataFrame,
        fetch_instruments_2: pd.DataFrame,
        fetch_instruments_3: pd.DataFrame,
    ):
        """Test for the case where there are multi-legs and single legs
        and we add the schema column MultiLegReportingType
        """
        params = Params(
            security_id_col="__symbol__",
            trade_date="date",
            instrument="instrumentDetails.instrument",
            exchange="ice_pof",
            convert_buysell_from_enum_to_number=True,
            add_multi_leg_reporting_type=True,
        )
        source_frame = pd.read_pickle(SOURCE_FRAME)
        expected_frame = pd.read_pickle(EXPECTED_FRAME_WITH_MULTILEG_REPORTING_TYPE)
        task = FetchVDIAndSplitMultiLegTrades(
            name="FetchVDIAndSplitMultiLegTrades", params=params
        )
        auditor = Auditor(task_name="Dummy")
        task._auditor = auditor

        mock_fetch_instruments = mocker.patch.object(FetchInstruments, "process")

        mock_fetch_instruments.side_effect = [
            fetch_instruments_1,
            fetch_instruments_2,
            fetch_instruments_3,
        ]

        result = task.execute(source_frame=source_frame, params=params)
        pd.testing.assert_frame_equal(left=result, right=expected_frame)
