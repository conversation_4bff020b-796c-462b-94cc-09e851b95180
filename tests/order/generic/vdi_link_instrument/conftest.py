import pandas as pd
import pytest


@pytest.fixture()
def fetch_instruments_1():
    data = {
        "instrumentDetails.instrument": {
            0: pd.NA,
            1: {
                "sourceKey": "iceInstruments.uds.IFEU.FCXXXX.20230330.220006",
                "&id": "IFEU|ULDSQJ0023.M0023-ULDSQN0023.U0023|6814592|20230331|USD",
                "instrumentFullName": "Gasoil Crack Futures Spr (bbl) - LS GO 1st Line/Brent 1st Line - Q2 23/Q3 23 - UDS",
                "&key": "VenueDirectInstrument:IFEU|ULDSQJ0023.M0023-ULDSQN0023.U0023|6814592|20230331|USD:1680300926",
                "notionalCurrency1": "USD",
                "ext.exchangeSymbolLocal": "6814592",
                "ext.instrumentUniqueIdentifier": "IFEU|ULDSQJ0023.M0023-ULDSQN0023.U0023|6814592|20230331|USD",
                "venue.tradingVenue": "IFEU",
                "derivative.isUserDefinedSpread": True,
                "derivative.legs": [
                    {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "5652882"},
                    {"legRatioQty": "1", "legSide": "SELL", "legSecurityId": "5652884"},
                ],
                "derivative.underlyingInstruments": pd.NA,
            },
            3: {
                "sourceKey": "iceInstruments.IFEU.FCXXXX.20230424.034508",
                "&id": "IFEU|ULDSMQ0023!|20230831|USD",
                "instrumentFullName": "IFEU - ULD - Aug-23 - Future",
                "&key": "VenueDirectInstrument:IFEU|ULDSMQ0023!|20230831|USD:1682308667",
                "notionalCurrency1": "USD",
                "ext.exchangeSymbolLocal": "5652862",
                "ext.instrumentUniqueIdentifier": "IFEU|ULDSMQ0023!|20230831|USD",
                "venue.tradingVenue": "IFEU",
                "derivative.isUserDefinedSpread": False,
                "derivative.legs": pd.NA,
                "derivative.underlyingInstruments": [
                    {"underlyingInstrumentCode": "GB00H208M508"}
                ],
            },
            4: {
                "sourceKey": "iceInstruments.IFEU.FCXXXX.20230424.034508",
                "&id": "IFEU|ULDSMK0023!|20230531|USD",
                "instrumentFullName": "IFEU - ULD - May-23 - Future",
                "&key": "VenueDirectInstrument:IFEU|ULDSMK0023!|20230531|USD:1682308667",
                "notionalCurrency1": "USD",
                "ext.exchangeSymbolLocal": "5652856",
                "ext.instrumentUniqueIdentifier": "IFEU|ULDSMK0023!|20230531|USD",
                "venue.tradingVenue": "IFEU",
                "derivative.isUserDefinedSpread": False,
                "derivative.legs": pd.NA,
                "derivative.underlyingInstruments": [
                    {"underlyingInstrumentCode": "GB00H208M276"}
                ],
            },
            5: {
                "sourceKey": "iceInstruments.IFEU.FCXXXX.20230424.034508",
                "&id": "IFEU|ULDSMM0023!|20230630|USD",
                "instrumentFullName": "IFEU - ULD - Jun-23 - Future",
                "&key": "VenueDirectInstrument:IFEU|ULDSMM0023!|20230630|USD:1682308667",
                "notionalCurrency1": "USD",
                "ext.exchangeSymbolLocal": "5652858",
                "ext.instrumentUniqueIdentifier": "IFEU|ULDSMM0023!|20230630|USD",
                "venue.tradingVenue": "IFEU",
                "derivative.isUserDefinedSpread": False,
                "derivative.legs": pd.NA,
                "derivative.underlyingInstruments": [
                    {"underlyingInstrumentCode": "GB00H208M383"}
                ],
            },
            7: {
                "sourceKey": "iceInstruments.IFEU.FCXXXX.20230424.034508",
                "&id": "IFEU|ULDSMJ0023!|20230428|USD",
                "instrumentFullName": "IFEU - ULD - Apr-23 - Future",
                "&key": "VenueDirectInstrument:IFEU|ULDSMJ0023!|20230428|USD:1682308667",
                "notionalCurrency1": "USD",
                "ext.exchangeSymbolLocal": "5652854",
                "ext.instrumentUniqueIdentifier": "IFEU|ULDSMJ0023!|20230428|USD",
                "venue.tradingVenue": "IFEU",
                "derivative.isUserDefinedSpread": False,
                "derivative.legs": pd.NA,
                "derivative.underlyingInstruments": [
                    {"underlyingInstrumentCode": "GB00H208M169"}
                ],
            },
            9: {
                "sourceKey": "iceInstruments.IFEU.FCXXXX.20230424.034508",
                "&id": "IFEU|ULDSMN0023!|20230731|USD",
                "instrumentFullName": "IFEU - ULD - Jul-23 - Future",
                "&key": "VenueDirectInstrument:IFEU|ULDSMN0023!|20230731|USD:1682308667",
                "notionalCurrency1": "USD",
                "ext.exchangeSymbolLocal": "5652860",
                "ext.instrumentUniqueIdentifier": "IFEU|ULDSMN0023!|20230731|USD",
                "venue.tradingVenue": "IFEU",
                "derivative.isUserDefinedSpread": False,
                "derivative.legs": pd.NA,
                "derivative.underlyingInstruments": [
                    {"underlyingInstrumentCode": "GB00H208M490"}
                ],
            },
        },
        "__is_multileg__": {
            0: False,
            1: True,
            3: False,
            4: False,
            5: False,
            7: False,
            9: False,
        },
    }
    return pd.DataFrame(data)


@pytest.fixture()
def fetch_instruments_2():
    data = {
        "instrumentDetails.instrument": {
            1: {
                "sourceKey": "iceInstruments.uds.IFEU.FCXXXX.20230330.220006",
                "&id": "IFEU|ULDSQJ0023.M0023|5652882|20230331|USD",
                "instrumentFullName": "Gasoil Crack Futures (bbl) - LS GO 1st Line/Brent 1st Line - Q2 23 - UDS",
                "&key": "VenueDirectInstrument:IFEU|ULDSQJ0023.M0023|5652882|20230331|USD:1680300820",
                "notionalCurrency1": "USD",
                "ext.exchangeSymbolLocal": "5652882",
                "ext.instrumentUniqueIdentifier": "IFEU|ULDSQJ0023.M0023|5652882|20230331|USD",
                "venue.tradingVenue": "IFEU",
                "derivative.isUserDefinedSpread": True,
                "derivative.priceMultiplier": "1.0",
                "derivative.legs": [
                    {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "5652854"},
                    {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "5652856"},
                    {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "5652858"},
                ],
            },
            2: {
                "sourceKey": "iceInstruments.uds.IFEU.FCXXXX.20230423.033029",
                "&id": "IFEU|ULDSQN0023.U0023|5652884|20230630|USD",
                "instrumentFullName": "Gasoil Crack Futures (bbl) - LS GO 1st Line/Brent 1st Line - Q3 23 - UDS",
                "&key": "VenueDirectInstrument:IFEU|ULDSQN0023.U0023|5652884|20230630|USD:1682307163",
                "notionalCurrency1": "USD",
                "ext.exchangeSymbolLocal": "5652884",
                "ext.instrumentUniqueIdentifier": "IFEU|ULDSQN0023.U0023|5652884|20230630|USD",
                "venue.tradingVenue": "IFEU",
                "derivative.isUserDefinedSpread": True,
                "derivative.priceMultiplier": "1.0",
                "derivative.legs": [
                    {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "5652860"},
                    {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "5652862"},
                    {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "5652864"},
                ],
            },
        },
        "__is_multileg__": {1: True, 2: True},
    }
    return pd.DataFrame(data)


@pytest.fixture()
def fetch_instruments_3():
    data = {
        "instrumentDetails.instrument": {
            1: {
                "sourceKey": "iceInstruments.IFEU.FCXXXX.20230424.034508",
                "&id": "IFEU|ULDSMJ0023!|20230428|USD",
                "instrumentFullName": "IFEU - ULD - Apr-23 - Future",
                "&key": "VenueDirectInstrument:IFEU|ULDSMJ0023!|20230428|USD:1682308667",
                "notionalCurrency1": "USD",
                "ext.exchangeSymbolLocal": "5652854",
                "ext.instrumentUniqueIdentifier": "IFEU|ULDSMJ0023!|20230428|USD",
                "venue.tradingVenue": "IFEU",
                "derivative.priceDisplayFactor": 1,
                "derivative.underlyingInstruments": [
                    {"underlyingInstrumentCode": "GB00H208M169"}
                ],
                "derivative.isUserDefinedSpread": False,
            },
            2: {
                "sourceKey": "iceInstruments.IFEU.FCXXXX.20230424.034508",
                "&id": "IFEU|ULDSMK0023!|20230531|USD",
                "instrumentFullName": "IFEU - ULD - May-23 - Future",
                "&key": "VenueDirectInstrument:IFEU|ULDSMK0023!|20230531|USD:1682308667",
                "notionalCurrency1": "USD",
                "ext.exchangeSymbolLocal": "5652856",
                "ext.instrumentUniqueIdentifier": "IFEU|ULDSMK0023!|20230531|USD",
                "venue.tradingVenue": "IFEU",
                "derivative.priceDisplayFactor": 1,
                "derivative.underlyingInstruments": [
                    {"underlyingInstrumentCode": "GB00H208M276"}
                ],
                "derivative.isUserDefinedSpread": False,
            },
            3: {
                "sourceKey": "iceInstruments.IFEU.FCXXXX.20230424.034508",
                "&id": "IFEU|ULDSMM0023!|20230630|USD",
                "instrumentFullName": "IFEU - ULD - Jun-23 - Future",
                "&key": "VenueDirectInstrument:IFEU|ULDSMM0023!|20230630|USD:1682308667",
                "notionalCurrency1": "USD",
                "ext.exchangeSymbolLocal": "5652858",
                "ext.instrumentUniqueIdentifier": "IFEU|ULDSMM0023!|20230630|USD",
                "venue.tradingVenue": "IFEU",
                "derivative.priceDisplayFactor": 1,
                "derivative.underlyingInstruments": [
                    {"underlyingInstrumentCode": "GB00H208M383"}
                ],
                "derivative.isUserDefinedSpread": False,
            },
            4: {
                "sourceKey": "iceInstruments.IFEU.FCXXXX.20230424.034508",
                "&id": "IFEU|ULDSMN0023!|20230731|USD",
                "instrumentFullName": "IFEU - ULD - Jul-23 - Future",
                "&key": "VenueDirectInstrument:IFEU|ULDSMN0023!|20230731|USD:1682308667",
                "notionalCurrency1": "USD",
                "ext.exchangeSymbolLocal": "5652860",
                "ext.instrumentUniqueIdentifier": "IFEU|ULDSMN0023!|20230731|USD",
                "venue.tradingVenue": "IFEU",
                "derivative.priceDisplayFactor": 1,
                "derivative.underlyingInstruments": [
                    {"underlyingInstrumentCode": "GB00H208M490"}
                ],
                "derivative.isUserDefinedSpread": False,
            },
            5: {
                "sourceKey": "iceInstruments.IFEU.FCXXXX.20230424.034508",
                "&id": "IFEU|ULDSMQ0023!|20230831|USD",
                "instrumentFullName": "IFEU - ULD - Aug-23 - Future",
                "&key": "VenueDirectInstrument:IFEU|ULDSMQ0023!|20230831|USD:1682308667",
                "notionalCurrency1": "USD",
                "ext.exchangeSymbolLocal": "5652862",
                "ext.instrumentUniqueIdentifier": "IFEU|ULDSMQ0023!|20230831|USD",
                "venue.tradingVenue": "IFEU",
                "derivative.priceDisplayFactor": 1,
                "derivative.underlyingInstruments": [
                    {"underlyingInstrumentCode": "GB00H208M508"}
                ],
                "derivative.isUserDefinedSpread": False,
            },
            6: {
                "sourceKey": "iceInstruments.IFEU.FCXXXX.20230424.034508",
                "&id": "IFEU|ULDSMU0023!|20230929|USD",
                "instrumentFullName": "IFEU - ULD - Sep-23 - Future",
                "&key": "VenueDirectInstrument:IFEU|ULDSMU0023!|20230929|USD:1682308667",
                "notionalCurrency1": "USD",
                "ext.exchangeSymbolLocal": "5652864",
                "ext.instrumentUniqueIdentifier": "IFEU|ULDSMU0023!|20230929|USD",
                "venue.tradingVenue": "IFEU",
                "derivative.priceDisplayFactor": 1,
                "derivative.underlyingInstruments": [
                    {"underlyingInstrumentCode": "GB00H208M615"}
                ],
                "derivative.isUserDefinedSpread": False,
            },
        },
        "__is_multileg__": {
            1: False,
            2: False,
            3: False,
            4: False,
            5: False,
            6: False,
        },
    }
    return pd.DataFrame(data)


@pytest.fixture()
def fetch_instruments_no_ml_and_null_security_ids():
    data = [
        {
            "instrumentDetails.instrument": {
                "sourceKey": "iceInstruments.IFLL.FCXXXX.20230426.221540",
                "instrumentClassificationEMIRAssetClass": "CO",
                "&id": "IFLL|IFMM0024!|20240617|EUR",
                "cfiGroup": "Commodities futures",
                "cfiCategory": "Futures",
                "instrumentFullName": "IFLL - I - Jun-24 - Future",
                "&key": "VenueDirectInstrument:IFLL|IFMM0024!|20240617|EUR:1682550716",
                "notionalCurrency1": "EUR",
                "ext.exchangeSymbolLocal": "5792127",
                "ext.instrumentUniqueIdentifier": "IFLL|IFMM0024!|20240617|EUR",
                "venue.tradingVenue": "IFLL",
                "derivative.expiryDate": "2024-06-17",
                "derivative.priceDisplayFactor": 1,
                "derivative.underlyingInstruments": [
                    {"underlyingInstrumentCode": "GB00F71K4T03"}
                ],
                "derivative.isUserDefinedSpread": False,
            },
            "__is_multileg__": False,
        },
        {"instrumentDetails.instrument": pd.NA, "__is_multileg__": False},
        {"instrumentDetails.instrument": pd.NA, "__is_multileg__": False},
    ]
    return pd.DataFrame(data)


@pytest.fixture()
def fetch_multileg_instruments_1():
    data = {
        "instrumentDetails.instrument": {
            0: {
                "sourceKey": "iceInstruments.uds.IFLL.FCXXXX.20231228.220006",
                "instrumentClassificationEMIRAssetClass": "CO",
                "&id": "IFLL|20226488|20240916|EUR",
                "cfiGroup": "Commodities futures",
                "cfiCategory": "Futures",
                "instrumentFullName": "Three Month Euro (Euribor) Future - ICEU - Sep24 - UDS",
                "&key": "VenueDirectInstrument:IFLL|20226488|20240916|EUR:1703898461",
                "notionalCurrency1": "EUR",
                "instrumentClassificationEMIRProductType": "FUT",
                "instrumentClassificationEMIRContractType": "FU",
                "cfiAttribute1": "Not Applicable/Undefined",
                "cfiAttribute2": "Not Applicable/Undefined",
                "cfiAttribute3": "Not Applicable/Undefined",
                "cfiAttribute4": "Not Applicable/Undefined",
                "instrumentClassification": "FCXXXX",
                "ext.exchangeSymbolLocal": "20226488",
                "ext.mifirEligible": True,
                "ext.emirEligible": True,
                "ext.bestExAssetClassSub": "Futures and options admitted to trading on a trading venue",
                "ext.quantityNotation": "UNIT",
                "ext.bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives",
                "ext.instrumentUniqueIdentifier": "IFLL|20226488|20240916|EUR",
                "ext.priceNotation": "MONE",
                "ext.exchangeSymbolRoot": "I",
                "venue.tradingVenue": "IFLL",
                "venue.financialInstrumentShortName": "I    56  20226488",
                "derivative.expiryDate": "2024-09-16",
                "derivative.priceDisplayFactor": 1,
                "derivative.legs": [
                    {
                        "legOptionDelta": "30",
                        "legRatioQty": "1",
                        "legSide": "BUYI",
                        "legSecurityId": "107284169",
                    },
                    {"legSide": "BUYI", "legSecurityId": "5856230"},
                ],
                "derivative.isUserDefinedSpread": True,
                "derivative.priceMultiplier": "1.0",
            },
            1: {
                "sourceKey": "iceInstruments.uds.IFLL.FCXXXX.20240205.220006",
                "instrumentClassificationEMIRAssetClass": "CO",
                "&id": "IFLL|20230948|20240916|EUR",
                "cfiGroup": "Commodities futures",
                "cfiCategory": "Futures",
                "instrumentFullName": "Three Month Euro (Euribor) Future - ICEU - Sep24 - UDS",
                "&key": "VenueDirectInstrument:IFLL|20230948|20240916|EUR:1707257047",
                "notionalCurrency1": "EUR",
                "instrumentClassificationEMIRProductType": "FUT",
                "instrumentClassificationEMIRContractType": "FU",
                "cfiAttribute1": "Not Applicable/Undefined",
                "cfiAttribute2": "Not Applicable/Undefined",
                "cfiAttribute3": "Not Applicable/Undefined",
                "cfiAttribute4": "Not Applicable/Undefined",
                "instrumentClassification": "FCXXXX",
                "ext.exchangeSymbolLocal": "20230948",
                "ext.mifirEligible": True,
                "ext.emirEligible": True,
                "ext.bestExAssetClassSub": "Futures and options admitted to trading on a trading venue",
                "ext.quantityNotation": "UNIT",
                "ext.bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives",
                "ext.instrumentUniqueIdentifier": "IFLL|20230948|20240916|EUR",
                "ext.priceNotation": "MONE",
                "ext.exchangeSymbolRoot": "I",
                "venue.tradingVenue": "IFLL",
                "venue.financialInstrumentShortName": "I    53  20230948",
                "derivative.expiryDate": "2024-09-16",
                "derivative.priceDisplayFactor": 1,
                "derivative.legs": [
                    {
                        "legOptionDelta": "55",
                        "legRatioQty": "1",
                        "legSide": "BUYI",
                        "legSecurityId": "107284160",
                    },
                    {
                        "legRatioQty": "1",
                        "legSide": "BUYI",
                        "legSecurityId": "107284161",
                    },
                    {"legSide": "BUYI", "legSecurityId": "5856230"},
                ],
                "derivative.isUserDefinedSpread": True,
                "derivative.priceMultiplier": "1.0",
            },
        },
        "__is_multileg__": {
            0: True,
            1: True,
        },
    }
    return pd.DataFrame(data)


@pytest.fixture()
def fetch_multileg_instruments_2():
    data = {
        "instrumentDetails.instrument": {
            0: {
                "sourceKey": "iceInstruments.IFLL.OPXXXX.20240221.132826",
                "&id": "IFLL|IFMU0024_OMPA0000970003091624|20240916|EUR",
                "cfiGroup": "Put options",
                "instrumentIdCode": "GB00K8XMN336",
                "cfiCategory": "Listed options",
                "instrumentFullName": "IFLL - I - 16-Sep-24 - 97 - Option - Put",
                "&key": "VenueDirectInstrument:IFLL|IFMU0024_OMPA0000970003091624|20240916|EUR:1708522665",
                "notionalCurrency1": "EUR",
                "cfiAttribute1": "Not Applicable/Undefined",
                "cfiAttribute2": "Not Applicable/Undefined",
                "cfiAttribute3": "Not Applicable/Undefined",
                "cfiAttribute4": "Not Applicable/Undefined",
                "instrumentClassification": "OPXXXX",
                "ext.emirEligible": True,
                "ext.alternativeInstrumentIdentifier": "IFLLIOP2024-09-16 00:00:0097.00000000",
                "ext.exchangeSymbolRoot": "I",
                "ext.exchangeSymbolLocal": "107284169",
                "ext.mifirEligible": True,
                "ext.pricingReferences.ICE": "isin/GB00K8XMN336/EUR",
                "ext.bestExAssetClassSub": pd.NA,
                "ext.exchangeSymbol": "I   FMU0024_OMPA0000970003091624",
                "ext.quantityNotation": "UNIT",
                "ext.bestExAssetClassMain": "Other Instruments",
                "ext.instrumentUniqueIdentifier": "IFLL|IFMU0024_OMPA0000970003091624|20240916|EUR",
                "ext.priceNotation": "MONE",
                "ext.instrumentIdCodeType": "ID",
                "venue.tradingVenue": "IFLL",
                "venue.financialInstrumentShortName": "I   FMU0024_OMPA0000970003091624",
                "derivative.optionType": "PUTO",
                "derivative.expiryDate": "2024-09-16",
                "derivative.optionExerciseStyle": "AMER",
                "derivative.priceDisplayFactor": 1,
                "derivative.strikePriceCurrency": "EUR",
                "derivative.isUserDefinedSpread": False,
                "derivative.strikePrice": "97.00",
                "instrumentClassificationEMIRAssetClass": pd.NA,
                "instrumentClassificationEMIRProductType": pd.NA,
                "instrumentClassificationEMIRContractType": pd.NA,
            },
            1: {
                "sourceKey": "iceInstruments.IFLL.FCXXXX.20240218.221540",
                "&id": "IFLL|IFMU0024!|20240916|EUR",
                "cfiGroup": "Commodities futures",
                "instrumentIdCode": "GB00H3PJDL87",
                "cfiCategory": "Futures",
                "instrumentFullName": "IFLL - I - Sep-24 - Future",
                "&key": "VenueDirectInstrument:IFLL|IFMU0024!|20240916|EUR:1708295092",
                "notionalCurrency1": "EUR",
                "cfiAttribute1": "Not Applicable/Undefined",
                "cfiAttribute2": "Not Applicable/Undefined",
                "cfiAttribute3": "Not Applicable/Undefined",
                "cfiAttribute4": "Not Applicable/Undefined",
                "instrumentClassification": "FCXXXX",
                "ext.emirEligible": True,
                "ext.alternativeInstrumentIdentifier": "IFLLIFF2024-09-16 00:00:00",
                "ext.exchangeSymbolRoot": "I",
                "ext.exchangeSymbolLocal": "5856230",
                "ext.mifirEligible": True,
                "ext.pricingReferences.ICE": "isin/GB00H3PJDL87/EUR",
                "ext.bestExAssetClassSub": "Futures and options admitted to trading on a trading venue",
                "ext.exchangeSymbol": "I   FMU0024!",
                "ext.quantityNotation": "UNIT",
                "ext.bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives",
                "ext.instrumentUniqueIdentifier": "IFLL|IFMU0024!|20240916|EUR",
                "ext.priceNotation": "MONE",
                "ext.instrumentIdCodeType": "ID",
                "venue.tradingVenue": "IFLL",
                "venue.financialInstrumentShortName": "I   FMU0024!",
                "derivative.optionType": pd.NA,
                "derivative.expiryDate": "2024-09-16",
                "derivative.optionExerciseStyle": pd.NA,
                "derivative.priceDisplayFactor": 1,
                "derivative.strikePriceCurrency": pd.NA,
                "derivative.isUserDefinedSpread": False,
                "derivative.strikePrice": pd.NA,
                "instrumentClassificationEMIRAssetClass": "CO",
                "instrumentClassificationEMIRProductType": "FUT",
                "instrumentClassificationEMIRContractType": "FU",
            },
            2: {
                "sourceKey": "iceInstruments.IFLL.OCXXXX.20240221.132826",
                "&id": "IFLL|IFMU0024_OMCA0000965003091624|20240916|EUR",
                "cfiGroup": "Call options",
                "instrumentIdCode": "GB00K8XMM361",
                "cfiCategory": "Listed options",
                "instrumentFullName": "IFLL - I - 16-Sep-24 - 96.5 - Option - Call",
                "&key": "VenueDirectInstrument:IFLL|IFMU0024_OMCA0000965003091624|20240916|EUR:1708522880",
                "notionalCurrency1": "EUR",
                "cfiAttribute1": "Not Applicable/Undefined",
                "cfiAttribute2": "Not Applicable/Undefined",
                "cfiAttribute3": "Not Applicable/Undefined",
                "cfiAttribute4": "Not Applicable/Undefined",
                "instrumentClassification": "OCXXXX",
                "ext.emirEligible": True,
                "ext.alternativeInstrumentIdentifier": "IFLLIOC2024-09-16 00:00:0096.50000000",
                "ext.exchangeSymbolRoot": "I",
                "ext.exchangeSymbolLocal": "107284160",
                "ext.mifirEligible": True,
                "ext.pricingReferences.ICE": "isin/GB00K8XMM361/EUR",
                "ext.bestExAssetClassSub": pd.NA,
                "ext.exchangeSymbol": "I   FMU0024_OMCA0000965003091624",
                "ext.quantityNotation": "UNIT",
                "ext.bestExAssetClassMain": "Other Instruments",
                "ext.instrumentUniqueIdentifier": "IFLL|IFMU0024_OMCA0000965003091624|20240916|EUR",
                "ext.priceNotation": "MONE",
                "ext.instrumentIdCodeType": "ID",
                "venue.tradingVenue": "IFLL",
                "venue.financialInstrumentShortName": "I   FMU0024_OMCA0000965003091624",
                "derivative.optionType": "CALL",
                "derivative.expiryDate": "2024-09-16",
                "derivative.optionExerciseStyle": "AMER",
                "derivative.priceDisplayFactor": 1,
                "derivative.strikePriceCurrency": "EUR",
                "derivative.isUserDefinedSpread": False,
                "derivative.strikePrice": "96.50",
                "instrumentClassificationEMIRAssetClass": pd.NA,
                "instrumentClassificationEMIRProductType": pd.NA,
                "instrumentClassificationEMIRContractType": pd.NA,
            },
            3: {
                "sourceKey": "iceInstruments.IFLL.OPXXXX.20240221.132826",
                "&id": "IFLL|IFMU0024_OMPA0000965003091624|20240916|EUR",
                "cfiGroup": "Put options",
                "instrumentIdCode": "GB00K8XMMZ84",
                "cfiCategory": "Listed options",
                "instrumentFullName": "IFLL - I - 16-Sep-24 - 96.5 - Option - Put",
                "&key": "VenueDirectInstrument:IFLL|IFMU0024_OMPA0000965003091624|20240916|EUR:1708522665",
                "notionalCurrency1": "EUR",
                "cfiAttribute1": "Not Applicable/Undefined",
                "cfiAttribute2": "Not Applicable/Undefined",
                "cfiAttribute3": "Not Applicable/Undefined",
                "cfiAttribute4": "Not Applicable/Undefined",
                "instrumentClassification": "OPXXXX",
                "ext.emirEligible": True,
                "ext.alternativeInstrumentIdentifier": "IFLLIOP2024-09-16 00:00:0096.50000000",
                "ext.exchangeSymbolRoot": "I",
                "ext.exchangeSymbolLocal": "107284161",
                "ext.mifirEligible": True,
                "ext.pricingReferences.ICE": "isin/GB00K8XMMZ84/EUR",
                "ext.bestExAssetClassSub": pd.NA,
                "ext.exchangeSymbol": "I   FMU0024_OMPA0000965003091624",
                "ext.quantityNotation": "UNIT",
                "ext.bestExAssetClassMain": "Other Instruments",
                "ext.instrumentUniqueIdentifier": "IFLL|IFMU0024_OMPA0000965003091624|20240916|EUR",
                "ext.priceNotation": "MONE",
                "ext.instrumentIdCodeType": "ID",
                "venue.tradingVenue": "IFLL",
                "venue.financialInstrumentShortName": "I   FMU0024_OMPA0000965003091624",
                "derivative.optionType": "PUTO",
                "derivative.expiryDate": "2024-09-16",
                "derivative.optionExerciseStyle": "AMER",
                "derivative.priceDisplayFactor": 1,
                "derivative.strikePriceCurrency": "EUR",
                "derivative.isUserDefinedSpread": False,
                "derivative.strikePrice": "96.50",
                "instrumentClassificationEMIRAssetClass": pd.NA,
                "instrumentClassificationEMIRProductType": pd.NA,
                "instrumentClassificationEMIRContractType": pd.NA,
            },
            4: {
                "sourceKey": "iceInstruments.IFLL.FCXXXX.20240218.221540",
                "&id": "IFLL|IFMU0024!|20240916|EUR",
                "cfiGroup": "Commodities futures",
                "instrumentIdCode": "GB00H3PJDL87",
                "cfiCategory": "Futures",
                "instrumentFullName": "IFLL - I - Sep-24 - Future",
                "&key": "VenueDirectInstrument:IFLL|IFMU0024!|20240916|EUR:1708295092",
                "notionalCurrency1": "EUR",
                "cfiAttribute1": "Not Applicable/Undefined",
                "cfiAttribute2": "Not Applicable/Undefined",
                "cfiAttribute3": "Not Applicable/Undefined",
                "cfiAttribute4": "Not Applicable/Undefined",
                "instrumentClassification": "FCXXXX",
                "ext.emirEligible": True,
                "ext.alternativeInstrumentIdentifier": "IFLLIFF2024-09-16 00:00:00",
                "ext.exchangeSymbolRoot": "I",
                "ext.exchangeSymbolLocal": "5856230",
                "ext.mifirEligible": True,
                "ext.pricingReferences.ICE": "isin/GB00H3PJDL87/EUR",
                "ext.bestExAssetClassSub": "Futures and options admitted to trading on a trading venue",
                "ext.exchangeSymbol": "I   FMU0024!",
                "ext.quantityNotation": "UNIT",
                "ext.bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives",
                "ext.instrumentUniqueIdentifier": "IFLL|IFMU0024!|20240916|EUR",
                "ext.priceNotation": "MONE",
                "ext.instrumentIdCodeType": "ID",
                "venue.tradingVenue": "IFLL",
                "venue.financialInstrumentShortName": "I   FMU0024!",
                "derivative.optionType": pd.NA,
                "derivative.expiryDate": "2024-09-16",
                "derivative.optionExerciseStyle": pd.NA,
                "derivative.priceDisplayFactor": 1,
                "derivative.strikePriceCurrency": pd.NA,
                "derivative.isUserDefinedSpread": False,
                "derivative.strikePrice": pd.NA,
                "instrumentClassificationEMIRAssetClass": "CO",
                "instrumentClassificationEMIRProductType": "FUT",
                "instrumentClassificationEMIRContractType": "FU",
            },
        },
        "__is_multileg__": {
            0: False,
            1: False,
            2: False,
            3: False,
            4: False,
        },
    }
    return pd.DataFrame(data)
