���!      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK7��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	hierarchy��	_order.id��_orderState.id��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��_order.__meta_model__��_orderState.__meta_model__��
orderClass��"orderIdentifiers.aggregatedOrderId��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��.orderIdentifiers.tradingVenueTransactionIdCode��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��"priceFormingData.remainingQuantity��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��!transactionDetails.positionEffect��transactionDetails.quantity��$_order.transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��
__symbol__��	__price__��__last_px__��__stop_px__��__newo_in_file__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C8                                           	       �t�bh]Nu��R�e]�(hhK ��h��R�(KK"K��h!�]�(�BUYI��SELL�hwhxhxhxhwhwhxhwhxhxhxhw�ICE POF Exchange�hyhyhyhyhyhy�
2023-03-14��
2023-03-14��
2023-03-14��
2023-03-14��
2023-03-14��
2023-03-14��
2023-03-14�hwhxhwhxhxhxhw�NEWO�h�h�h�h�h�h��FILL��pandas._libs.missing��NA���h�h�h�h�h��Limit�h�h�h�h�h�h��0|805|200SG761��805|200SG761��0|805|200SG761��0|805|200SG761��0|805|200SG761��0|805|200SG761��0|805|200SG761��AOTC�h�h�h�h�h�h�]��GTCV�ah�h�h�h�h�h��
Standalone�h�h�h�h�h�h��303350403|5652864��303350403|6814592��303350403|5652862��303350403|5652856��303350403|5652858��303350403|5652854��303350403|5652860�h�h�h�h�h�h�h�]�(}�(�labelId��id:socar��path��buyer��type��se_schema.static.market��IdentifierType����ARRAY���R�u}�(h��id:socar�h��reportDetails.executingEntity�h�h��OBJECT���R�u}�(h��id:ice�h��seller�h�h�u}�(h��id:ice�h��counterparty�h�h�u}�(h��id:3�h��1tradersAlgosWaiversIndicators.executionWithinFirm�h�h�u}�(h��id:7073�h��clientIdentifiers.client�h�h�u}�(h��
id:ecoutya�h��trader�h�h�ue]�(}�(h��id:socar�h�h�h�h�u}�(h��id:socar�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:3�h�h�h�h�u}�(h��id:7073�h�h�h�h�u}�(h��
id:ecoutya�h�h�h�h�ue]�(}�(h��id:socar�h�h�h�h�u}�(h��id:socar�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:3�h�h�h�h�u}�(h��id:7073�h�h�h�h�u}�(h��
id:ecoutya�h�h�h�h�ue]�(}�(h��id:socar�h�h�h�h�u}�(h��id:socar�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:3�h�h�h�h�u}�(h��id:7073�h�h�h�h�u}�(h��
id:ecoutya�h�h�h�h�ue]�(}�(h��id:socar�h�h�h�h�u}�(h��id:socar�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:3�h�h�h�h�u}�(h��id:7073�h�h�h�h�u}�(h��
id:ecoutya�h�h�h�h�ue]�(}�(h��id:socar�h�h�h�h�u}�(h��id:socar�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:3�h�h�h�h�u}�(h��id:7073�h�h�h�h�u}�(h��
id:ecoutya�h�h�h�h�ue]�(}�(h��id:socar�h�h�h�h�u}�(h��id:socar�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:3�h�h�h�h�u}�(h��id:7073�h�h�h�h�u}�(h��
id:ecoutya�h�h�h�h�ue�id:socar��id:socar��id:socar��id:socar��id:socar��id:socar��id:socar��id:socar��id:socar��id:socar��id:socar��id:socar��id:socar��id:socar��id:ice��id:ice��id:ice��id:ice��id:ice��id:ice��id:ice��id:ice��id:ice��id:ice��id:ice��id:ice��id:ice��id:ice�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��id:3��id:3��id:3��id:3��id:3��id:3��id:3��id:7073��id:7073��id:7073��id:7073��id:7073��id:7073��id:7073��
id:ecoutya��
id:ecoutya��
id:ecoutya��
id:ecoutya��
id:ecoutya��
id:ecoutya��
id:ecoutya�]�(h�h�h�h�h�h�h�e]�(h�h�h�h�h�h�h�e]�(h�h�h�h�h�h�h�e]�(h�h�h�h�h�h�h�e]�(h�h�h�h�h�h�h�e]�(h�h�h�h�j   j  j  e]�(j  j	  j  j
  j  j  j  e�Order�jM  jM  jM  jM  jM  jM  �
OrderState�jN  jN  jN  jN  jN  jN  �
Regular Trade�h�jO  jO  jO  jO  jO  �6021515�h��6021515��6021515��6021515��6021515��6021515��5562119��5562119��5562119��5562119��5562119��5562119��5562119�h�h�h�h�h�h�h�h�h�h�h�h�h�h��6021523��303350403:1:Ack��6021522��6021518��6021519��6021517��6021521�et�bhhK ��h��R�(KKK��h�f8�����R�(KhkNNNJ����J����K t�b�C�      �?      �?      �?      �?      �?      �?      �?              �?                                              �?              �?      �?      �?      �?      �?�t�bhhK ��h��R�(KK	K��h!�]�(j]  j^  j_  j`  ja  jb  jc  ��s3://integration.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/Mleg_on_mleg/0ca427e1d996a788b0e00d46598fa9c2dfb72e37ab4536f61ecebf862e6cf637_156616.fix���s3://integration.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/Mleg_on_mleg/2f7d095672f3b312d0a62fabc3da40aa13baa5823c11d97d68f4a03445942564_155459.fix���s3://integration.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/Mleg_on_mleg/6e155637cf506aff50ff7dfa5cf7e2870d006b8fb4d0c8d84b6e85f73f7964d2_156615.fix���s3://integration.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/Mleg_on_mleg/7eaf29ba590828c41bd2259501c0572b71b22df70dee203698cb404503e15066_156611.fix���s3://integration.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/Mleg_on_mleg/87cf1550c34ae7829d8c6365a3bd7b309cb637107bfca6465b343da15ff5ba7a_156612.fix���s3://integration.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/Mleg_on_mleg/c67e1316b84f7b544cccbefedf074487e19473a51bbef4b9927d6aee707eb5c4_156610.fix���s3://integration.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/Mleg_on_mleg/dcfa75f4b8cf9ec02f618a3bd0f20774aaa665382dfac639326d304eb0c4e3fc_156614.fix��2023-03-14T18:10:40.139200Z��2023-03-14T17:16:24.248318Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T17:16:24.248318Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T17:16:24.248318Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z�j{  j|  j}  j~  j  j�  j�  hwhxhwhxhxhxhwjV  jW  jX  jY  jZ  j[  j\  h�h�h�h�h�h�h�et�bhhK ��h��R�(KKK��jk  �C8      �?              �?      �?      �?      �?      �?�t�bhhK ��h��R�(KKK��h!�]�(�Market Side�j�  j�  j�  j�  j�  j�  h�h�h�h�h�h�h�j{  j|  j}  j~  j  j�  j�  �5652864��6814592��5652862��5652856��5652858��5652854��5652860�et�bhhK ��h��R�(KKK��jk  �Cpffffff�?ffffff�?ffffff�?ffffff�?ffffff�?ffffff�?ffffff�?������7@        ������7@     @9@     @9@     @9@������7@�t�bhhK ��h��R�(KKK��h!�]�(h�h�h�h�h�h�h��������et�be]�(h
h}�(hhhK ��h��R�(KK"��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<h=h>h?h@hAhBhChDhEhFet�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hGhHhIet�bh]Nu��R�h
h}�(hhhK ��h��R�(KK	��h!�]�(hJhKhLhMhNhOhPhQhRet�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hThUhVhWet�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hXhYet�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hZh[et�bh]Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�ht�mgr_locs��builtins��slice���K K"K��R�u}�(j�  jg  j�  j�  K"K%K��R�u}�(j�  jq  j�  j�  K%K.K��R�u}�(j�  j�  j�  j�  K.K/K��R�u}�(j�  j�  j�  j�  K/K3K��R�u}�(j�  j�  j�  j�  K3K5K��R�u}�(j�  j�  j�  j�  K5K7K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.