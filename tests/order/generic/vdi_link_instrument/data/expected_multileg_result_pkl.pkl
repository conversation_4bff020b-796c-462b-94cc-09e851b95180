��]#      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK8��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo�� executionDetails.routingStrategy�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	hierarchy��	_order.id��_orderState.id��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��_order.__meta_model__��_orderState.__meta_model__��
orderClass��"orderIdentifiers.aggregatedOrderId��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��.orderIdentifiers.tradingVenueTransactionIdCode��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��"priceFormingData.remainingQuantity��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��!transactionDetails.positionEffect��transactionDetails.quantity��$_order.transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��	__price__��__last_px__��__stop_px__��__newo_in_file__��instrumentDetails.instrument�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(h^N�start�K �stop�K�step�Ku��R�e]�(hhK ��h��R�(KK1K��h!�]�(�SELL�hp�BUYI�hqhqhphphqhqhq�ICE POF Exchange�hrhrhrhr�
2023-12-28�hs�
2024-02-05�hththphphqhqhq�NEWO�huhuhuhu�pandas._libs.missing��NA���hxhxhxhx�Limit�hyhyhyhy�1749|KBLGULA�hz�1749|KBLDAPA�h{h{hxhxhxhxhx�AOTC�h|h|h|h|]��IOCV�ah}]�h~ahh�
Standalone�h�h�h�h��718192805|107284169��718192805|5856230��877059909|107284160��877059909|107284161��877059909|5856230��718192805|107284169��718192805|5856230��877059909|107284160��877059909|107284161��877059909|5856230�]�(}�(�labelId��id:kyte��path��buyer��type��ARRAY�u}�(h�h�h��reportDetails.executingEntity�h��OBJECT�u}�(h��id:ice�h��seller�h�h�u}�(h�h�h��counterparty�h�h�u}�(h��id:1063�h��1tradersAlgosWaiversIndicators.executionWithinFirm�h�h�u}�(h��id:14210�h��clientIdentifiers.client�h�h�u}�(h��	id:glane1�h��trader�h�h�ueh�]�(}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h��id:1079�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h��id:dpatel10�h�h�h�h�ueh�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hxhxhxhxhxhxhxhxhxhxhxhxhxhxhxh�h�h�h�h�h�h�h�h�h�h�h�h�h�h�]�(}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�ueh�]�(}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�ueh�h��Order�h�h�h�h��
OrderState�h�h�h�h�hxhxhxhxhxhxhxhxhxhx�28016750�h��28037044�h�h��718192805|107284169��718192805|5856230��877059909|107284160��877059909|107284161��877059909|5856230��718192805|107284169��718192805|5856230��877059909|107284160��877059909|107284161��877059909|5856230��718192805:1:Ack�ȟ877059909:1:Ack�h�h�h�h�h�h�h͌�s3:\/\/jose.dev.steeleye.co\/ingress\/raw\/order-feed-ice-pof-fix\/20240219\/08885f61b1017aba972c085d6ff3deecc8c988ea0168b19b355ec10421201b93_11361.fix�hΌ�s3:\/\/jose.dev.steeleye.co\/ingress\/raw\/order-feed-ice-pof-fix\/20240219\/11068e6a515edc2aaa4bb6ef787aedca8ea809feae3bc8102bf669cd18508a83_11334.fix�h�hό2023-12-28T13:12:11.445155Z�hЌ2024-02-05T10:26:38.667007Z�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hphphqhqhqh�h�h�h�h��Open�h�h�h�hҌMarket Side�h�h�h�h�h|h|h|h|h|h�h�h�h�h�hxhxhxhxhx}�(�	sourceKey��*iceInstruments.IFLL.OPXXXX.20240221.132826��&id��/IFLL|IFMU0024_OMPA0000970003091624|20240916|EUR��cfiGroup��Put options��instrumentIdCode��GB00K8XMN336��cfiCategory��Listed options��instrumentFullName��(IFLL - I - 16-Sep-24 - 97 - Option - Put��&key��PVenueDirectInstrument:IFLL|IFMU0024_OMPA0000970003091624|20240916|EUR:1708522665��notionalCurrency1��EUR��
cfiAttribute1��Not Applicable/Undefined��
cfiAttribute2�h�
cfiAttribute3�h�
cfiAttribute4�h�instrumentClassification��OPXXXX��ext.emirEligible���#ext.alternativeInstrumentIdentifier��%IFLLIOP2024-09-16 00:00:0097.00000000��ext.exchangeSymbolRoot��I��ext.exchangeSymbolLocal��	107284169��ext.mifirEligible���ext.pricingReferences.ICE��isin/GB00K8XMN336/EUR��ext.bestExAssetClassSub�hx�ext.exchangeSymbol�� I   FMU0024_OMPA0000970003091624��ext.quantityNotation��UNIT��ext.bestExAssetClassMain��Other Instruments��ext.instrumentUniqueIdentifier�h،ext.priceNotation��MONE��ext.instrumentIdCodeType��ID��venue.tradingVenue��IFLL��"venue.financialInstrumentShortName�h��derivative.optionType��PUTO��derivative.expiryDate��
2024-09-16��derivative.optionExerciseStyle��AMER��derivative.priceDisplayFactor�K�derivative.strikePriceCurrency�h�derivative.isUserDefinedSpread���derivative.strikePrice��97.00��&instrumentClassificationEMIRAssetClass�hx�'instrumentClassificationEMIRProductType�hx�(instrumentClassificationEMIRContractType�hxu}�(hՌ*iceInstruments.IFLL.FCXXXX.20240218.221540�h׌IFLL|IFMU0024!|20240916|EUR�hٌCommodities futures�hیGB00H3PJDL87�h݌Futures�hߌIFLL - I - Sep-24 - Future�h�<VenueDirectInstrument:IFLL|IFMU0024!|20240916|EUR:1708295092�h�h�h�h�h�h�h�h�h�h�h�FCXXXX�h�h�IFLLIFF2024-09-16 00:00:00�h�h�h�5856230�h�h�isin/GB00H3PJDL87/EUR�h��:Futures and options admitted to trading on a trading venue�h��I   FMU0024!�h�h�h��;Commodities derivatives and emission allowances Derivatives�h�j  h�h�j   j  j  j  j  j   j  hxj  j  j	  hxj  Kj  hxj
  �j  hxj  �CO�j  �FUT�j  �FU�u}�(hՌ*iceInstruments.IFLL.OCXXXX.20240221.132826�h׌/IFLL|IFMU0024_OMCA0000965003091624|20240916|EUR�hٌCall options�hیGB00K8XMM361�h�h�hߌ+IFLL - I - 16-Sep-24 - 96.5 - Option - Call�h�PVenueDirectInstrument:IFLL|IFMU0024_OMCA0000965003091624|20240916|EUR:1708522880�h�h�h�h�h�h�h�h�h�h�h�OCXXXX�h�h�%IFLLIOC2024-09-16 00:00:0096.50000000�h�h�h�	107284160�h�h�isin/GB00K8XMM361/EUR�h�hxh�� I   FMU0024_OMCA0000965003091624�h�h�h�h�h�j'  h�h�j   j  j  j  j  j0  j  �CALL�j  j  j	  j
  j  Kj  h�j
  �j  �96.50�j  hxj  hxj  hxu}�(h�h�h׌/IFLL|IFMU0024_OMPA0000965003091624|20240916|EUR�h�h�hیGB00K8XMMZ84�h�h�hߌ*IFLL - I - 16-Sep-24 - 96.5 - Option - Put�h�PVenueDirectInstrument:IFLL|IFMU0024_OMPA0000965003091624|20240916|EUR:1708522665�h�h�h�h�h�h�h�h�h�h�h�h�h�h�%IFLLIOP2024-09-16 00:00:0096.50000000�h�h�h�	107284161�h�h�isin/GB00K8XMMZ84/EUR�h�hxh�� I   FMU0024_OMPA0000965003091624�h�h�h�h�h�j4  h�h�j   j  j  j  j  j;  j  j  j  j  j	  j
  j  Kj  h�j
  �j  j2  j  hxj  hxj  hxu}�(h�j  h�j  h�j  h�j  h�j  h�j  h�j  h�h�h�h�h�h�h�h�h�h�h�j  h�h�j  h�h�h�j  h�h�j  h�j  h�j   h�h�h�j!  h�j  h�h�j   j  j  j  j  j   j  hxj  j  j	  hxj  Kj  hxj
  �j  hxj  j"  j  j#  j  j$  uet�bhhK ��h��R�(KKK��h�f8�����R�(K�<�NNNJ����J����K t�b�C�     @�@     �r@     �b@     �b@     �T@     @�@     @�@     �b@     �b@     �b@                                                                                {�G�z�?{�G�z�?��Q��?��Q��?��Q��?                                        �t�bhhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C�t�be]�(h
h}�(hhhK ��h��R�(KK1��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<h=h>h?h@hAhBhChDhEhFhGhKhLhMhNhOhPhQhRhShUhVhWhZh\et�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hHhIhJhThXhYet�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h[at�bh^Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hm�mgr_locs�hhK ��h��R�(KK1��h�i8�����R�(KjE  NNNJ����J����K t�b�B�                                                                  	       
                     
                                                                                                                                             !       "       &       '       (       )       *       +       ,       -       .       0       1       2       5       7       �t�bu}�(jv  j@  jw  hhK ��h��R�(KK��j~  �C0#       $       %       /       3       4       �t�bu}�(jv  jK  jw  �builtins��slice���K6K7K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.