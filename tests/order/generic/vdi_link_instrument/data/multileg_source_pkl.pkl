��      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK8��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo�� executionDetails.routingStrategy�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	hierarchy��	_order.id��_orderState.id��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��_order.__meta_model__��_orderState.__meta_model__��
orderClass��"orderIdentifiers.aggregatedOrderId��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��.orderIdentifiers.tradingVenueTransactionIdCode��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��"priceFormingData.remainingQuantity��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��!transactionDetails.positionEffect��transactionDetails.quantity��$_order.transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��
__symbol__��	__price__��__last_px__��__stop_px__��__newo_in_file__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C               �t�bh^Nu��R�e]�(hhK ��h��R�(KKK��h�f8�����R�(KhlNNNJ����J����K t�b�C`     @�@     �b@     @�@     �b@                                {�G�z�?��Q��?                �t�bhhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C�t�bhhK ��h��R�(KK1K��h!�]�(�SELL��BUYI�h�h��ICE POF Exchange�h��
2023-12-28��
2024-02-05�h�h��NEWO�h��pandas._libs.missing��NA���h��Limit�h��1749|KBLGULA��1749|KBLDAPA�h�h��AOTC�h�]��IOCV�a]�h�a�
Standalone�h��718192805|20226488��877059909|20230948�h�h�]�(}�(�labelId��id:kyte��path��buyer��type��ARRAY�u}�(h�h�h��reportDetails.executingEntity�h��OBJECT�u}�(h��id:ice�h��seller�h�h�u}�(h�h�h��counterparty�h�h�u}�(h��id:1063�h��1tradersAlgosWaiversIndicators.executionWithinFirm�h�h�u}�(h��id:14210�h��clientIdentifiers.client�h�h�u}�(h��	id:glane1�h��trader�h�h�ue]�(}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h��id:1079�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h��id:dpatel10�h�h�h�h�ueh�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�]�(}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�ue]�(}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�u}�(h�h�h�h�h�h�ue�Order�hҌ
OrderState�h�h�h�h�h��28016750��28037044�h�h�h�h��718192805:1:Ack��877059909:1:Ack�h�h׌�s3:\/\/jose.dev.steeleye.co\/ingress\/raw\/order-feed-ice-pof-fix\/20240219\/08885f61b1017aba972c085d6ff3deecc8c988ea0168b19b355ec10421201b93_11361.fix���s3:\/\/jose.dev.steeleye.co\/ingress\/raw\/order-feed-ice-pof-fix\/20240219\/11068e6a515edc2aaa4bb6ef787aedca8ea809feae3bc8102bf669cd18508a83_11334.fix��2023-12-28T13:12:11.445155Z��2024-02-05T10:26:38.667007Z�h�h�h�h�h�h�h�h�h�hՌOpen�h܌Market Side�h�h�h�h�hی20226488��20230948�h�h�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(hHhIhJhThYhZet�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK1��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<h=h>h?h@hAhBhChDhEhFhGhKhLhMhNhOhPhQhRhShUhVhWhXh[et�bh^Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hu�mgr_locs�hhK ��h��R�(KK��hk�C0#       $       %       /       4       5       �t�bu}�(j  hj  �builtins��slice���K7K8K��R�u}�(j  h�j  hhK ��h��R�(KK1��hk�B�                                                                  	       
                     
                                                                                                                                             !       "       &       '       (       )       *       +       ,       -       .       0       1       2       3       6       �t�bueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.