import pandas as pd

from swarm_tasks.order.generic.utils import get_instrument_from_exch_symbol_local


def test_get_instrument_from_exch_symbol_local(
    instruments_with_same_exchange_symbol_local,
):
    security_id = "876241"
    trade_date = "2022-04-17"
    date_format = "%Y-%m-%d"
    result = get_instrument_from_exch_symbol_local(
        security_id=security_id,
        instruments=instruments_with_same_exchange_symbol_local,
        trade_date=trade_date,
        date_format=date_format,
    )
    assert (
        result
        == instruments_with_same_exchange_symbol_local.drop(columns=["&timestamp"])
        .iloc[0]
        .to_dict()
    )


def test_get_instrument_from_exch_symbol_local_no_instrument(
    instruments_with_same_exchange_symbol_local,
):
    security_id = "876241"
    trade_date = "2022-04-20"
    date_format = "%Y-%m-%d"

    # Since the trade date is greater the instruments &timestamp,
    # no instruments will be linked and pd.NA is expected as return value
    # because the instrument names are different and one instrument is a
    # userDefinedSpread
    result = get_instrument_from_exch_symbol_local(
        security_id=security_id,
        instruments=instruments_with_same_exchange_symbol_local,
        trade_date=trade_date,
        date_format=date_format,
    )
    assert pd.isna(result)
