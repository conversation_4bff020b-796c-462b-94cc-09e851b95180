import os
from pathlib import Path

import pandas as pd
import pytest
from prefect import context
from prefect.engine import signals
from swarm.conf import SettingsCls
from swarm.task.auditor import Auditor

from swarm_tasks.order.transformations.order_transform_maps import (
    saxo_bank_orders_transform_map,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
TEST_CFD_FILE_PATH = TEST_FILES_DIR.joinpath(r"CFDTradesExecuted_test.pkl")
EXPECTED_CFD_FILE_PATH = TEST_FILES_DIR.joinpath(r"CFDTradesExecuted_expected.pkl")
TEST_FUTURES_FILE_PATH = TEST_FILES_DIR.joinpath(r"FuturesTradesExecuted_test.pkl")
EXPECTED_FUTURES_FILE_PATH = TEST_FILES_DIR.joinpath(
    r"FuturesTradesExecuted_expected.pkl"
)
EXPECTED_FUTURES_THORNBRIDGE_FILE_PATH = TEST_FILES_DIR.joinpath(
    r"FuturesTradesExecuted_thornbridge_expected.pkl"
)
TEST_FX_OPTION_FILE_PATH = TEST_FILES_DIR.joinpath(r"FXOptionTradesExecuted_test.pkl")
EXPECTED_FX_OPTION_FILE_PATH = TEST_FILES_DIR.joinpath(
    r"FXOptionTradesExecuted_expected.pkl"
)
TEST_FX_FILE_PATH = TEST_FILES_DIR.joinpath(r"FXTradesExecuted_test.pkl")
EXPECTED_FX_FILE_PATH = TEST_FILES_DIR.joinpath(r"FXTradesExecuted_expected.pkl")
TEST_SHARE_FILE_PATH = TEST_FILES_DIR.joinpath(r"ShareTradesExecuted_test.pkl")
EXPECTED_SHARE_FILE_PATH = TEST_FILES_DIR.joinpath(r"ShareTradesExecuted_expected.pkl")
TEST_BONDS_FILE_PATH = TEST_FILES_DIR.joinpath(r"BondsTradesExecuted_test.pkl")
EXPECTED_BONDS_FILE_PATH = TEST_FILES_DIR.joinpath(r"BondsTradesExecuted_expected.pkl")

TEST_CASH_PATH = TEST_FILES_DIR.joinpath("CashTransactions_test.pkl")
TEST_CFD_OPTIONS_PATH = TEST_FILES_DIR.joinpath("CFDOptionTradesExecuted_test.pkl")
TEST_ORDER_ACTIVITY_PATH = TEST_FILES_DIR.joinpath("OrderActivity_test.pkl")
TEST_UNKNOWN_PATH = TEST_FILES_DIR.joinpath("Unknown_test.pkl")


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestSaxoBankOrderTransformations:
    """
    Test suite for SaxoBankOrderTransformations
    """

    @pytest.mark.parametrize(
        "test_file_path, expected_file_path",
        [
            (TEST_CFD_FILE_PATH, EXPECTED_CFD_FILE_PATH),
            (TEST_FUTURES_FILE_PATH, EXPECTED_FUTURES_FILE_PATH),
            (TEST_FX_OPTION_FILE_PATH, EXPECTED_FX_OPTION_FILE_PATH),
            (TEST_FX_FILE_PATH, EXPECTED_FX_FILE_PATH),
            (TEST_SHARE_FILE_PATH, EXPECTED_SHARE_FILE_PATH),
            (TEST_BONDS_FILE_PATH, EXPECTED_BONDS_FILE_PATH),
        ],
    )
    def test_end_to_end_transformations(
        self, mocker, test_file_path, expected_file_path, auditor
    ):
        """Test end-to-end transformations for all files"""
        os.environ["SWARM_FILE_URL"] = str(test_file_path)

        # mock realm to chose default transformation
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "default.dev.steeleye.co"

        source_frame = pd.read_pickle(test_file_path)
        task = saxo_bank_orders_transform_map.transformation(tenant="foo")(
            source_frame=source_frame, logger=context.get("logger"), auditor=auditor
        )
        result = task.process()
        expected = pd.read_pickle(expected_file_path)

        assert not pd.testing.assert_frame_equal(
            result.drop(["sourceKey"], axis=1), expected.drop(["sourceKey"], axis=1)
        )

    def test_thornbridge_overrides(self, mocker, auditor):
        os.environ["SWARM_FILE_URL"] = str(TEST_FUTURES_FILE_PATH)

        # mock realm to choose thornbridge transformation
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "thornbridge.steeleye.co"

        source_frame = pd.read_pickle(TEST_FUTURES_FILE_PATH)
        task = saxo_bank_orders_transform_map.transformation(tenant="thornbridge")(
            source_frame=source_frame, logger=context.get("logger"), auditor=auditor
        )
        result = task.process()
        expected = pd.read_pickle(EXPECTED_FUTURES_THORNBRIDGE_FILE_PATH)

        result_party_ids = result[["marketIdentifiers.parties"]]
        expected_party_ids = expected[["marketIdentifiers.parties"]]

        assert not pd.testing.assert_frame_equal(result_party_ids, expected_party_ids)
        assert not pd.testing.assert_frame_equal(
            result.drop(["sourceKey"], axis=1), expected.drop(["sourceKey"], axis=1)
        )

    @pytest.mark.parametrize(
        "test_file_path",
        [TEST_CASH_PATH, TEST_CFD_OPTIONS_PATH, TEST_ORDER_ACTIVITY_PATH],
    )
    def test_skipped_files(self, mocker, test_file_path, auditor):
        """Test that Cash, Order Activity and CFD Option files are always skipped"""
        os.environ["SWARM_FILE_URL"] = str(test_file_path)

        # mock realm to chose default transformation
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "default.dev.steeleye.co"

        source_frame = pd.read_pickle(test_file_path)
        with pytest.raises(signals.SKIP):
            saxo_bank_orders_transform_map.transformation(tenant="foo")(
                source_frame=source_frame, logger=context.get("logger"), auditor=auditor
            )

    def test_unknown_file(self, mocker, auditor):
        """Test that Cash and CFD Option files are always skipped"""
        os.environ["SWARM_FILE_URL"] = str(TEST_UNKNOWN_PATH)

        # mock realm to chose default transformation
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "default.dev.steeleye.co"

        source_frame = pd.read_pickle(TEST_UNKNOWN_PATH)
        with pytest.raises(signals.FAIL):
            saxo_bank_orders_transform_map.transformation(tenant="foo")(
                source_frame=source_frame, logger=context.get("logger"), auditor=auditor
            )
