��5       �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK4��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�Account��OrderNumber��CreationDate��InstrumentType��
Instrument��TransactionType��BuySell��	OrderType��Duration��OrderAmount��
OrderPrice��ExecutedAmount��
ExecutedPrice��	ValueDate��Exchange��ISINCode��PartnerAccountKey��
PositionID��UserID��OldOrderNumber��DurationDate��
LastChangedBy��TradedCurrency��
CounterpartID��CorrelationKey��OrderCashAmount��OrderCashAmountCurrency��SwitchInInstrument��
WithAdvice��FXType��Price��ISOMic��AccountCurrency��
ExpiryDate��TradeNumber��InstrumentCode��Venue��TradedAmount��UnderlyingInstrumentISINCode��
DirtyPrice��CallPut��Strike��ISIN��TermOfUnderlyingIndex��InstrumentISINCode��	TradeTime��UnderlyingInstrumentCode��	NetAmount��!ExecutingEntityIdentificationCode��InstrumentCurrency��
UserIdOnTrade��TradeExecutionTime�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK	��h�i8�����R�(K�<�NNNJ����J����K t�b�CH                                                                �t�bhZ�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK	��h!�]�(�pandas._libs.missing��NA���h}h}h}h}h}h}h}h}et�b�_dtype�hp�StringDtype���)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(h}h}h}h}h}h}h}h}h}et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(h}h}h}h}h}h}h}h}h}et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(h}h}h}h}h}h}h}h}h}et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(h}h}h}h}h}h}h}h}h}et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(h}h}h}h}h}h}h}h}h}et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(h}h}h}h}h}h}h}h}h}et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(h}h}h}h}h}h}h}h}h}et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(h}h}h}h}h}h}h}h}h}et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(h}h}h}h}h}h}h}h}h}et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(h}h}h}h}h}h}h}h}h}et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(h}h}h}h}h}h}h}h}h}et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(h}h}h}h}h}h}h}h}h}et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(h}h}h}h}h}h}h}h}h}et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(h}h}h}h}h}h}h}h}h}et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(h}h}h}h}h}h}h}h}h}et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(h}h}h}h}h}h}h}h}h}et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(h}h}h}h}h}h}h}h}h}et�bhh�)��ubhhK ��h��R�(KKK	��h�f8�����R�(KhhNNNJ����J����K t�b�Bh        �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KKK	��h!�]�(�
111111INET�j+  j+  j+  j+  j+  j+  j+  j+  �20211012 18:39:56��20211012 18:41:52��20211012 18:43:30��20211012 15:02:44��20211012 11:22:08��20211012 09:48:56��20211012 09:49:47��20211012 10:00:11��20211012 11:21:58��	CVAC:xnas�j5  j5  �M:xnys��EURUSD�j7  j7  j7  j7  �Place Order��Change Order��Cancel Order�j9  j:  j8  j9  j9  j:  �G.T.C.��G.T.C�j;  j<  j;  j;  j;  j;  j;  �2500�j=  j=  �600��1300��1500�j@  j@  j@  �41.68��41.54�jB  �32��1.15055��1.15645��1.1568��1.15675�jG  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �NYSE�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �11111111�jI  jI  jI  jI  jI  jI  jI  jI  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �11111111�jJ  jJ  jJ  jJ  jJ  jJ  jJ  jJ  �USD�jK  jK  jK  �EUR�jL  jL  jL  jL  �$01zz0z02-0z09-0z2z-9z10-002z0zzz235z�jM  jM  �$0zz20z03-061z-0009-z002-0230z030z90z��$z2z5z5zz-9zzz-0006-z3zz-3062zz053905��$5z6zzz1z-z012-020z-zz06-0z6zz0zzz639�jP  jP  jP  �0�jQ  jQ  jQ  jQ  jQ  jQ  jQ  jQ  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      et�bhr)��}�(huhhK ��h��R�(KK	��h!�]�(�	400000009��	400000010��	400000011��	400000012��	400000013��	400000014��	400000015��	400000016��	400000017�et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(�CFD�jl  jl  �Stocks��FX Spot�jn  jn  jn  jn  et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(�Sell��Sell��Sell��Sell��Buy��Sell��Sell��Sell��Sell�et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(�Limit�j�  j�  j�  j�  j�  j�  j�  j�  et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(�ZZZZ7ZV570Z1��ZZZZ7ZV570Z2��ZZZZ7ZV570Z3��ZZZZ7ZV570Z4��ZZZZ7ZV570Z5��ZZZZ7ZV570Z6��ZZZZ7ZV570Z7��ZZZZ7ZV570Z8��ZZZZ7ZV570Z9�et�bhh�)��ubhr)��}�(huhhK ��h��R�(KK	��h!�]�(�13692735�j�  j�  �10309049��5220232�j�  j�  j�  j�  et�bhh�)��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hRat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hChJhLhNhTet�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h%h'h)h*h-h.h/h0h1h2h3h5h6h7h8h9h:h;h=h>h?h@hAet�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bhZNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hs�mgr_locs��builtins��slice���KKK��R�u}�(j�  h�j�  j�  KK K��R�u}�(j�  h�j�  j�  K K!K��R�u}�(j�  h�j�  j�  K!K"K��R�u}�(j�  h�j�  j�  K"K#K��R�u}�(j�  h�j�  j�  K#K$K��R�u}�(j�  h�j�  j�  K$K%K��R�u}�(j�  h�j�  j�  K&K'K��R�u}�(j�  h�j�  j�  K(K)K��R�u}�(j�  h�j�  j�  K*K+K��R�u}�(j�  h�j�  j�  K+K,K��R�u}�(j�  h�j�  j�  K,K-K��R�u}�(j�  h�j�  j�  K-K.K��R�u}�(j�  h�j�  j�  K.K/K��R�u}�(j�  h�j�  j�  K0K1K��R�u}�(j�  j  j�  j�  K1K2K��R�u}�(j�  j
  j�  j�  K2K3K��R�u}�(j�  j  j�  j�  K3K4K��R�u}�(j�  j  j�  hhK ��h��R�(KK��hg�C(       %       '       )       /       �t�bu}�(j�  j(  j�  hhK ��h��R�(KK��hg�C�                                    	       
                     
                                                                                                  �t�bu}�(j�  jS  j�  j�  KKK��R�u}�(j�  je  j�  j�  KKK��R�u}�(j�  jq  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.