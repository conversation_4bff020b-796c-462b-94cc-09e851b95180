��L"      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK2��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType�� executionDetails.tradingCapacity��	hierarchy��	_order.id��_orderState.id��_order.__meta_model__��_orderState.__meta_model__��orderIdentifiers.orderIdCode��!orderIdentifiers.transactionRefNo��priceFormingData.price��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.tradingDateTime��timestamps.orderReceived��timestamps.orderSubmitted��timestamps.orderStatusUpdated��#transactionDetails.buySellIndicator��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��#transactionDetails.quantityCurrency��priceFormingData.tradedQuantity�� priceFormingData.initialQuantity��transactionDetails.quantity��#transactionDetails.quantityNotation��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C@                                                         �t�bhX�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KKK��h!�]�(�1�hs�2�hshshshshshshshthshshshshs�Saxobank�huhuhuhuhuhuhu�
2022-01-14��
2022-01-18��
2022-01-18��
2022-02-08��
2022-04-04��
2022-04-21��
2022-04-29��
2022-05-04��BUYI�h~�SELL�h~h~h~h~h~�NEWO�h�h�h�h�h�h�h��FILL�h�h�h�h�h�h�h��MARKET�h�h�h�h�h�h�h��AOTC�h�h�h�h�h�h�h��
Standalone�h�h�h�h�h�h�h��
**********��
**********��
**********��
**********��
**********��
**********��
**********��
**********�h�h�h�h�h�h�h�h��Order�h�h�h�h�h�h�h��
OrderState�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�b�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�
**********��
**********��
**********��
**********��
**********��
**********��
**********��
5218978125�et�b�_dtype�h��StringDtype���)��ubhhK ��h��R�(KKK��h�f8�����R�(KhfNNNJ����J����K t�b�C@��ъRiY@/O�RiY@��ъRiY@������X@��Y�Y@���3�Y@���
S�Y@sx�EǅY@�t�bh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�h�h�et�bh�h�)��ubhhK ��h��R�(KKK��he�C@                                                         �t�bhhK ��h��R�(KKK��h!�]�(�w/Users/<USER>/Github/se-prefect-dir/swarm-tasks/tests/order/transformations/saxobank/data/BondsTradesExecuted_test.pkl�h�h�h�h�h�h�hʌ2022-01-14T13:52:22.387000Z��2022-01-18T04:24:23.200000Z��2022-01-18T04:24:22.950000Z��2022-02-08T16:19:56.757000Z��2022-04-04T13:11:07.230000Z��2022-04-21T07:00:17.343000Z��2022-04-29T07:08:24.023000Z��2022-05-04T07:49:04.807000Z�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h~h~hh~h~h~h~h~et�bhhK ��h��R�(KKK��h��C@��ъRiY@/O�RiY@��ъRiY@������X@��Y�Y@���3�Y@���
S�Y@sx�EǅY@�t�bhhK ��h��R�(KKK��h!�]�(�USD�h�h�h�hߌEUR�h�h��PERC�h�h�h�h�h�h�h�USD�h�h�h�h�EUR�h�h�et�bhhK ��h��R�(KKK��h��C�    ��.A    ��.A    ��.A    ��.A    �SA    ��.A    ��.A    ��.A    ��.A    ��.A    ��.A    ��.A    �SA    ��.A    ��.A    ��.A    ��.A    ��.A    ��.A    ��.A    �SA    ��.A    ��.A    ��.A�t�bhhK ��h��R�(KKK��h!�]�(�MONE�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hҌTREU�h�h�h�h�MOTX�h�h�XOFF�h�h�h�h�h�h�h�]�(}�(�labelId��US912828N308��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(h��US912828N308USDTREU�h�h�h�j   ue]�(}�(h�h�h�h�h�j   u}�(h��US912828N308USDTREU�h�h�h�j   ue]�(}�(h�h�h�h�h�j   u}�(h��US912828N308USDTREU�h�h�h�j   ue]�(}�(h��US91282CCN92�h�h�h�j   u}�(h��US91282CCN92USDTREU�h�h�h�j   ue]�(}�(h��US912828TJ95�h�h�h�j   u}�(h��US912828TJ95USDTREU�h�h�h�j   ue]�(}�(h��DE0001102366�h�h�h�j   u}�(h��DE0001102366EURMOTX�h�h�h�j   ue]�(}�(h�j  h�h�h�j   u}�(h��DE0001102366EURMOTX�h�h�h�j   ue]�(}�(h�j  h�h�h�j   u}�(h��DE0001102366EURMOTX�h�h�h�j   ue]�(}�(h��lei:549300tl5406ic1xkd09�h��buyer�h�h��ARRAY���R�u}�(h��lei:549300tl5406ic1xkd09�h��reportDetails.executingEntity�h�j   u}�(h��id:15326664�h��seller�h�j(  u}�(h��id:15326664�h��counterparty�h�j   u}�(h��id:16152148�h��trader�h�j(  ue]�(}�(h��lei:549300tl5406ic1xkd09�h�j%  h�j(  u}�(h��lei:549300tl5406ic1xkd09�h�j+  h�j   u}�(h��id:15326664�h�j.  h�j(  u}�(h��id:15326664�h�j1  h�j   ue]�(}�(h��id:15326664�h�j%  h�j(  u}�(h��lei:549300tl5406ic1xkd09�h�j+  h�j   u}�(h��lei:549300tl5406ic1xkd09�h�j.  h�j(  u}�(h��id:15326664�h�j1  h�j   ue]�(}�(h��lei:549300tl5406ic1xkd09�h�j%  h�j(  u}�(h��lei:549300tl5406ic1xkd09�h�j+  h�j   u}�(h��id:15326664�h�j.  h�j(  u}�(h��id:15326664�h�j1  h�j   u}�(h��id:16152148�h�j4  h�j(  ue]�(}�(h��lei:549300tl5406ic1xkd09�h�j%  h�j(  u}�(h��lei:549300tl5406ic1xkd09�h�j+  h�j   u}�(h��id:15326664�h�j.  h�j(  u}�(h��id:15326664�h�j1  h�j   u}�(h��id:16152148�h�j4  h�j(  ue]�(}�(h��lei:549300tl5406ic1xkd09�h�j%  h�j(  u}�(h��lei:549300tl5406ic1xkd09�h�j+  h�j   u}�(h��id:15326664�h�j.  h�j(  u}�(h��id:15326664�h�j1  h�j   u}�(h��id:16152148�h�j4  h�j(  ue]�(}�(h��lei:549300tl5406ic1xkd09�h�j%  h�j(  u}�(h��lei:549300tl5406ic1xkd09�h�j+  h�j   u}�(h��id:15326664�h�j.  h�j(  u}�(h��id:15326664�h�j1  h�j   u}�(h��id:16152148�h�j4  h�j(  ue]�(}�(h��lei:549300tl5406ic1xkd09�h�j%  h�j(  u}�(h��lei:549300tl5406ic1xkd09�h�j+  h�j   u}�(h��id:15326664�h�j.  h�j(  u}�(h��id:15326664�h�j1  h�j   u}�(h��id:16152148�h�j4  h�j(  ue�lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��id:15326664��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��id:15326664��id:15326664��lei:549300tl5406ic1xkd09��id:15326664��id:15326664��id:15326664��id:15326664��id:15326664��id:15326664��id:15326664��id:15326664��id:15326664��id:15326664��id:15326664��id:15326664��id:15326664��pandas._libs.missing��NA���j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �id:16152148�j�  j�  �id:16152148��id:16152148��id:16152148��id:16152148��id:16152148�et�bhhK ��h��R�(KKK��h!�]�(]�(h�j  j#  j)  j,  j/  j2  e]�(j  j  j6  j8  j:  j<  e]�(j  j	  j?  jA  jC  jE  e]�(j  j  jH  jJ  jL  jN  jP  e]�(j  j  jS  jU  jW  jY  j[  e]�(j  j  j^  j`  jb  jd  jf  e]�(j  j  ji  jk  jm  jo  jq  e]�(j  j   jt  jv  jx  jz  j|  eet�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3et�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h8h9h:h;h<h=et�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h?h@hAet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hBhChDet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hEhFhGhHhIhJhKhLhMhNhOhPhQhRhShThUet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bhXNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hp�mgr_locs��builtins��slice���K KK��R�u}�(j!  h�j"  j%  KKK��R�u}�(j!  h�j"  j%  KKK��R�u}�(j!  h�j"  j%  KKK��R�u}�(j!  h�j"  j%  KKK��R�u}�(j!  h�j"  j%  KKK��R�u}�(j!  h�j"  j%  KKK��R�u}�(j!  h�j"  j%  KKK��R�u}�(j!  h�j"  j%  KK K��R�u}�(j!  h�j"  j%  K K1K��R�u}�(j!  j�  j"  j%  K1K2K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.