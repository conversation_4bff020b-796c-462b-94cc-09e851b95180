��1&      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKA��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�
ReportingDate��InstrumentType��
CounterpartID��CounterpartName��
AccountNumber��PartnerAccountKey��AccountCurrency��InstrumentCode��InstrumentDescription��InstrumentCurrency��ISINCode��ExchangeDescription��TradedAmount��
FigureSize��TradeNumber��OrderNumber��	TradeTime��	TradeDate��	ValueDate��BuySell��Price��QuotedValueInstrumentCurrency��CommissionInstrumentCurrency��ExchangeFeeInstrumentCurrency��RelatedTradeID��	TradeType��CAEventTypeName��TradeAllocation��RootTradeID��ExternalOrderID��OriginalTradeID��
CorrectionLeg��
InstrumentUIC��ExchangeISOCode��ISOMic��DVP��ActualTradeDate��!ExecutingEntityIdentificationCode��
UserIdOnTrade��
EmployeeId��DecisionMakerUserId��Venue��TradeExecutionTime��ShortSellingIndicator��OTCPostTradeIndicator��'SecuritiesFinancingTransactionIndicator��SaxoPortfolioManagerTriggered��CorrelationKey��InstrumentSubType��+AdditionalTransactionCostInstrumentCurrency��OriginatingTool��
WithAdvice��InstrumentISINCode��	NetAmount��ComplexTradeComponentId��Strike��TermOfUnderlyingIndex��UnderlyingInstrumentCode��FXType��TransmissionOfOrderIndicator��ISIN��
DirtyPrice��UnderlyingInstrumentISINCode��
ExpiryDate��CallPut�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C@                                                         �t�bhg�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KK$K��h!�]�(�20220114��20220118�h��20220208��20220404��20220421��20220429��20220504��1CCM GLOBAL QIAIF PLATFORM ICAV - WAVEBREAKER FUND�h�h�h�h�h�h�h��
637486INETUSD�h�h�h��637486INETUSD2��
637486INET�h�h�G�      G�      G�      G�      G�      G�      G�      G�      �0United States of America 2.125% 31 Dec 2022. USD�h�h��0United States of America 0.125% 31 Jul 2023. USD��0United States of America 1.625% 15 Aug 2022. USD��Germany 1% 15 Aug 2024. EUR�h�h��Bonds US Government�h�h�h�h��Borsa Italiana EUROMOT Bonds�h�h��0.01�h�h�h�h�h�h�h��
2112278269��
6000958419��
6000958418��
6023770589��
6085259536��
6099050158��
6105916031��
6110862641��20220114�h�h��20220208��20220404��20220421��20220429��20220504��20220117��20220118�h��20220209��20220406��20220425��20220503��20220506��-1016456.63�h��
1016456.63��
-984631.08��-5024698.94��-1025031.51��-1023800.68��-1020902.88��-507.70�h��507.70��-492.30��-2506.74��-509.05��-508.32��-506.84��0.00�h�h�h�h�h�h�h��0�h��
5102639196�h�h�h�h�h��Order Commander��Trade Correction�h�h��OpenAPI�h�h�h�G�      G�      G�      G�      G�      G�      G�      G�      �No�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�G�      G�      G�      G�      �BG_BG0241113�G�      G�      G�      G�      �
5102639196�h�G�      G�      G�      G�      G�      G�      �Rebook��Cancel�G�      G�      G�      G�      G�      �5055272�h�h24666875��5055264��5006041�h�hŌTREU�h�h�h�hƌMOTX�h�hǌNo�h�h�h�h�h�h�hȌ20220114�h�hɌ20220208��20220404��20220421��20220429��20220504��16152148�G�      G�      h�h�h�h�hό20806�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �ACTX�h�h�h�h�h�h�hьFalse�h�h�h�h�h�h�hҌFalse�h�h�h�h�h�h�hӌ$ACC20664-C688-4F04-914C-C81E14DED7C7�h�hԌ$B5B5BD75-7F07-4EDA-9B93-AE73167F3766��$579F8CE6-DA56-40B8-8C4C-DDEAFE9E7114��$D5F8256B-7E15-44E1-9A2F-2AF3ABE4A49F��$F5BEF860-DF74-4DC1-8BE0-F671B3A9BBFC��$F629BBCA-329F-4C7B-BE26-7E7B8841DBDB��None�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��SaxoTrader Pro�h�h�h�h�h�h�hیNo�h�h�h�h�h�h�h�et�b�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�Bonds�h�h�h�h�h�h�h�et�b�_dtype�hތStringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�15326664�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�USD�j   j   j   j   �EUR�j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�UNITEDSTATES-2.125-31DEC22�j  j  �UNITEDSTATES-0.125-31JUL23��UNITEDSTATES-1.625-15AUG22��GERMANY-1-15AUG24�j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�USD�j  j  j  j  �EUR�j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�pandas._libs.missing��NA���j%  j%  j%  j%  j%  j%  j%  et�bh�h�)��ubhhK ��h��R�(KKK��h�f8�����R�(KhuNNNJ����J����K t�b�B@      ��.A    ��.A    ��.�    ��.A    �SA    ��.A    ��.A    ��.A      �      �      �      �      �      �      �      �ףp���.Aףp���.Aףp���.�q=
��.A���SA�G����.A=
ף��.Aףp=ʶ.A      �      �      �      �      �      �      �      ���ъRiY@/O�RiY@��ъRiY@������X@��Y�Y@���3�Y@���
S�Y@sx�EǅY@�t�bh�)��}�(h�hhK ��h��R�(KK��h!�]�(�
5102639196��
5104311822��
5104311821��
5133156938��
5192051406��
5207225597��
5215005607��
5218978125�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�20220114 13:52:22��20220118 04:24:23��20220118 04:24:22��20220208 16:19:56��20220404 13:11:07��20220421 07:00:17��20220429 07:08:24��20220504 07:49:04�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�Buy�j[  �Sell�j[  j[  j[  j[  j[  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�TREU�jf  jf  jf  jf  �XMIL�jg  jg  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�549300TL5406IC1XKD09�jq  jq  jq  jq  jq  jq  jq  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�XOFF��US_GOV�j|  j|  j|  �EUROMOT�j}  j}  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�20220114 13:52:22.387��20220118 04:24:23.200��20220118 04:24:22.950��20220208 16:19:56.757��20220404 13:11:07.230��20220421 07:00:17.343��20220429 07:08:24.023��20220504 07:49:04.807�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�SELL�j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(j%  j%  j%  j%  j%  j%  j%  j%  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(j%  j%  j%  j%  j%  j%  j%  j%  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(j%  j%  j%  j%  j%  j%  j%  j%  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(j%  j%  j%  j%  j%  j%  j%  j%  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(j%  j%  j%  j%  j%  j%  j%  j%  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�False�j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�US912828N308�j�  j�  �US91282CCN92��US912828TJ95��DE0001102366�j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(j%  j%  j%  j%  j%  j%  j%  j%  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(j%  j%  j%  j%  j%  j%  j%  j%  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(j%  j%  j%  j%  j%  j%  j%  j%  et�bh�h�)��ube]�(h
h}�(hhhK ��h��R�(KK$��h!�]�(h%h(h)h*h-h0h2h4h6h7h:h;h<h=h>h?h@hAhBhChDhEhGhHhIhKhLhMhQhRhShThUhVhWhXet�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h1h9hZh\hbet�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hYat�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h[at�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h]at�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h^at�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h`at�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�haat�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hcat�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hdat�bhgNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�heat�bhgNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h�mgr_locs�hhK ��h��R�(KK$��h�i8�����R�(KhuNNNJ����J����K t�b�B                                              
                                                                                                                 "       #       $       &       '       (       ,       -       .       /       0       1       2       3       �t�bu}�(j�  h�j�  �builtins��slice���KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j  j�  j�  K	K
K��R�u}�(j�  j  j�  j�  K
KK��R�u}�(j�  j*  j�  hhK ��h��R�(KK��j�  �C(              5       7       =       �t�bu}�(j�  j2  j�  j�  KKK��R�u}�(j�  jC  j�  j�  KKK��R�u}�(j�  jT  j�  j�  KKK��R�u}�(j�  j_  j�  j�  K!K"K��R�u}�(j�  jj  j�  j�  K%K&K��R�u}�(j�  jt  j�  j�  K)K*K��R�u}�(j�  j�  j�  j�  K*K+K��R�u}�(j�  j�  j�  j�  K+K,K��R�u}�(j�  j�  j�  j�  K4K5K��R�u}�(j�  j�  j�  j�  K6K7K��R�u}�(j�  j�  j�  j�  K8K9K��R�u}�(j�  j�  j�  j�  K9K:K��R�u}�(j�  j�  j�  j�  K:K;K��R�u}�(j�  j�  j�  j�  K;K<K��R�u}�(j�  j�  j�  j�  K<K=K��R�u}�(j�  j�  j�  j�  K>K?K��R�u}�(j�  j�  j�  j�  K?K@K��R�u}�(j�  j�  j�  j�  K@KAK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.