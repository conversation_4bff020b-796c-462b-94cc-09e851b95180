���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKI��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�
ReportingDate��InstrumentType��
CounterpartID��CounterpartName��
AccountNumber��PartnerAccountKey��AccountCurrency��Sector��InstrumentCode��InstrumentDescription��InstrumentCurrency��ExchangeDescription��
ExpiryDate��TradedAmount��
FigureSize��TradeNumber��OrderNumber��	TradeTime��	TradeDate��	ValueDate��BuySell��Price��QuotedValueInstrumentCurrency��CommissionInstrumentCurrency��ExchangeFeeInstrumentCurrency��TaxInstrumentCurrency��RelatedTradeID��	TradeType��TradeAllocation��RootTradeID��Strike��CallPut��PremiumInstrumentCurrency��ToOpenClose��ISOMic��OptionStyle��UnderlyingInstrumentCode��SettlementStyle��OriginalTradeID��
CorrectionLeg��InstrumentSubType��	CAEventID��FinalSettlementPrice��PremiumStyle��CAEventTypeName��
CAEventTypeID��EODRate��ExchangeISOCode��
InstrumentUIC��ActualTradeDate��!ExecutingEntityIdentificationCode��InstrumentISINCode��InstrumentClassification��PriceMultiplier��UnderlyingInstrumentISINCode��
UserIdOnTrade��
EmployeeId��DecisionMakerUserId��Venue��TradeExecutionTime��CommodityDerivativeIndicator��'SecuritiesFinancingTransactionIndicator��CorrelationKey��+AdditionalTransactionCostInstrumentCurrency��OriginatingTool��OperatingMIC��	OrderType��
DirtyPrice��ISINCode��FXType��ISIN��TermOfUnderlyingIndex��	NetAmount�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C        �t�bho�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]��pandas._libs.missing��NA���at�b�_dtype�h��StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�h�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�h�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�h�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�h�at�bh�h�)��ubhhK ��h��R�(KKK��h�f8�����R�(Kh}NNNJ����J����K t�b�C      �      ��t�bhhK ��h��R�(KK/K��h!�]�(�
ReportingDate��CounterpartName��
AccountNumber��PartnerAccountKey��Sector��InstrumentDescription��ExchangeDescription�h��
FigureSize��	TradeDate��	ValueDate�h��QuotedValueInstrumentCurrency��CommissionInstrumentCurrency��ExchangeFeeInstrumentCurrency��TaxInstrumentCurrency��RelatedTradeID��	TradeType��TradeAllocation��RootTradeID�h��PremiumInstrumentCurrency��ToOpenClose��OptionStyle��SettlementStyle��OriginalTradeID��
CorrectionLeg��InstrumentSubType��	CAEventID��FinalSettlementPrice��PremiumStyle��CAEventTypeName��
CAEventTypeID��EODRate��ExchangeISOCode��
InstrumentUIC��ActualTradeDate��InstrumentClassification��PriceMultiplier��
EmployeeId��DecisionMakerUserId��CommodityDerivativeIndicator��'SecuritiesFinancingTransactionIndicator��CorrelationKey��+AdditionalTransactionCostInstrumentCurrency��OriginatingTool��OperatingMIC�et�bh�)��}�(h�hhK ��h��R�(KK��h!�]��InstrumentType�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]��
CounterpartID�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]��AccountCurrency�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]��InstrumentCode�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]��InstrumentCurrency�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]��
ExpiryDate�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]��TradeNumber�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]��OrderNumber�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]��	TradeTime�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]��BuySell�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]��CallPut�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]��ISOMic�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]��UnderlyingInstrumentCode�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]��!ExecutingEntityIdentificationCode�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]��InstrumentISINCode�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]��UnderlyingInstrumentISINCode�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]��
UserIdOnTrade�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]��Venue�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]��TradeExecutionTime�at�bh�h�)��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�hgat�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hiat�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hjat�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hkat�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hlat�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hhhmet�bhoNu��R�h
h}�(hhhK ��h��R�(KK/��h!�]�(h%h(h)h*h,h.h0h2h3h7h8h:h;h<h=h>h?h@hAhBhChEhFhHhJhKhLhMhNhOhPhQhRhShThUhVhYhZh]h^hahbhchdhehfet�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h[at�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bhoNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h`at�bhoNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���KBKCK��R�u}�(j�  h�j�  j�  KDKEK��R�u}�(j�  h�j�  j�  KEKFK��R�u}�(j�  h�j�  j�  KFKGK��R�u}�(j�  h�j�  j�  KGKHK��R�u}�(j�  h�j�  j�  KCKMK��R�u}�(j�  h�j�  hhK ��h��R�(KK/��h|�Bx                                      	              
                                                                                                          !       #       %       &       '       (       )       *       +       ,       -       .       /       0       1       4       5       8       9       <       =       >       ?       @       A       �t�bu}�(j�  h�j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j  j�  j�  KK	K��R�u}�(j�  j   j�  j�  K
KK��R�u}�(j�  j*  j�  j�  KK
K��R�u}�(j�  j4  j�  j�  KKK��R�u}�(j�  j>  j�  j�  KKK��R�u}�(j�  jH  j�  j�  KKK��R�u}�(j�  jR  j�  j�  KKK��R�u}�(j�  j\  j�  j�  KK K��R�u}�(j�  jf  j�  j�  K"K#K��R�u}�(j�  jp  j�  j�  K$K%K��R�u}�(j�  jz  j�  j�  K2K3K��R�u}�(j�  j�  j�  j�  K3K4K��R�u}�(j�  j�  j�  j�  K6K7K��R�u}�(j�  j�  j�  j�  K7K8K��R�u}�(j�  j�  j�  j�  K:K;K��R�u}�(j�  j�  j�  j�  K;K<K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.