���)      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKE��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�
ReportingDate��InstrumentType��
CounterpartID��CounterpartName��
AccountNumber��PartnerAccountKey��AccountCurrency��Sector��InstrumentCode��InstrumentDescription��InstrumentCurrency��ExchangeDescription��
ExpiryDate��TradedAmount��
FigureSize��TradeNumber��OrderNumber��	TradeTime��	TradeDate��	ValueDate��BuySell��Price��QuotedValueInstrumentCurrency��CommissionInstrumentCurrency��ExchangeFeeInstrumentCurrency��TaxInstrumentCurrency��RelatedTradeID��	TradeType��TradeAllocation��RootTradeID��ExternalOrderID��OriginalTradeID��
CorrectionLeg��FutureStrategy��ETORelatedClosingPosition��EODRate��ExchangeISOCode��ISOMic��
InstrumentUIC��ActualTradeDate��!ExecutingEntityIdentificationCode��InstrumentISINCode��InstrumentClassification��PriceMultiplier��SettlementStyle��
UserIdOnTrade��
EmployeeId��DecisionMakerUserId��Venue��TradeExecutionTime��OTCPostTradeIndicator��'SecuritiesFinancingTransactionIndicator��SaxoPortfolioManagerTriggered��CorrelationKey��+AdditionalTransactionCostInstrumentCurrency��OriginatingTool��ISIN��	NetAmount��FXType��TransmissionOfOrderIndicator��CallPut��ISINCode��
DirtyPrice��UnderlyingInstrumentISINCode��UnderlyingInstrumentCode��ShortSellingIndicator��Strike��ComplexTradeComponentId��TermOfUnderlyingIndex�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK	��h�i8�����R�(K�<�NNNJ����J����K t�b�CH                                                                �t�bhk�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK	��h!�]�(�pandas._libs.missing��NA���h�h�h�h�h�h�h�h�et�b�_dtype�h��StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�Contract Futures�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�15326664�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�FEXDZ2��ADH2��BPH2��CCK2��CDH2��HOH2��NEH2��PLJ2��SCOH2�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�EUR��USD�j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�20221216��20220314�j  �20220513��20220315��20220228�j  �20220427��20220331�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�
5124434607��
5124539811��
5124540732��
5124591803��
5124542604��
5124391132��
5124541694��
5124393109��
5124396195�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�20220201 08:13:13��20220201 09:07:05��20220201 09:07:49��20220201 09:47:48��20220201 09:09:20��20220201 07:51:41��20220201 09:08:37��20220201 07:54:22��20220201 07:58:40�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�Buy��Sell�jR  jR  jR  jQ  jR  jR  jR  et�bh�h�)��ubhhK ��h��R�(KKK	��h�f8�����R�(KhyNNNJ����J����K t�b�Bh        @      �       �      �       �      �?      �      �      �33333�]@[B>�٬�?�y�):��?     P�@��4�8E�?�G�z�p@=,Ԛ��?�����#�@fffffa@      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KK*K	��h!�]�(�20220201�jd  jd  jd  jd  jd  jd  jd  jd  �1CCM GLOBAL QIAIF PLATFORM ICAV - WAVEBREAKER FUND�je  je  je  je  je  je  je  je  �
637486INET��
637486INETUSD�jg  jg  jg  jg  jg  jg  jg  G�      G�      G�      G�      G�      G�      G�      G�      G�      �EUR��USD�ji  ji  ji  ji  ji  ji  ji  �Equity and Derivative Indexes��Financials - Currencies�jk  �Softs�jk  �Energies�jk  �Precious Metals��Base Metals��/EURO STOXX® 50 Index Dividend Index - Dec 2022��AUDUSD - Mar 2022��GBPUSD - Mar 2022��Cocoa - May 2022��CADUSD - Mar 2022��NY Harbor ULSD - Mar 2022��NZDUSD - Mar 2022��Platinum - Apr 2022��,Iron Ore CFR China (62% Fe Fines) - Mar 2022��Eurex��Chicago Mercantile Exchange�jz  �ICE Futures U.S.  – NYBOT�jz  �New York Mercantile Exchange�jz  j|  �,Singapore Exchange Derivatives (Commodities)��100��100000��62500��10�j  �420�j  �50�j~  �
6018006633��
6018111708��
6018115917��
6018166346��
6018115949��
6017964069��
6018115933��
6017964121��
6017968087��20220201�j�  j�  j�  j�  j�  j�  j�  j�  �20220201�j�  j�  j�  j�  j�  j�  j�  j�  �	-59900.00��70860.00��	168525.00��	104000.00��	157940.00��
-113727.60��65990.00��51645.00��13645.00��-10.00�j�  j�  j�  j�  j�  j�  j�  j�  �-6.00��-1.62��-3.24��-8.48�j�  �-1.52�j�  �-1.57��-2.00��0.00�j�  j�  j�  j�  j�  j�  j�  j�  �0�j�  j�  j�  j�  j�  j�  j�  j�  �OpenAPI�j�  j�  j�  j�  j�  j�  j�  j�  �No�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �MS__Vi19u0FSHuKpFpGkskq1gA��MS_msmy25WIRA-1M_7-ZMfH5gA��MS_wgi7eDlfRgOnW0ezQSbU5wA��MS_vMBdM9Z0R-K-aZlO3og53QA��MS_fct6SoAET_afe_XvJAR_SgA��MS_anMggaUDTGOI2P_tDPwF5AA��MS_URTm3eZcQ5-RS593Iiu9mwA��MS_3y1E7GCpTBaA4jVj0lVhOgA��JP_68805BC100A05EAA�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �No�j�  j�  j�  j�  j�  j�  j�  j�  G�      G�      G�      G�      G�      G�      G�      G�      G�      �120��0.71165��1.3514��2660��0.7873��274.12��0.6632��1023.2��137.07��XEUR��XCME�j�  �IFUS�j�  �XNYM�j�  j�  �XSIM��494094��23238624��21668361��20258086��20258087��21701134��25206901��20720178��24668646��20220201�j�  j�  j�  j�  j�  j�  j�  j�  �FFICSX��FFCPSX�j�  �FCAPSX�j�  �FCHPSX�j�  �FCEPSX��FCECSX��100��100000��62500��10�j�  �420�j�  �50�j�  �Cash settled��Physical Delivery�j�  j�  j�  j�  j�  j�  j�  �16152148�j�  j�  j�  j�  j�  j�  j�  j�  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �EUREX��CME�j�  �IFUS�j�  �NYMEX�j�  j�  �SGX-COM�G�      G�      G�      G�      G�      G�      G�      G�      G�      �False�j�  j�  j�  j�  j�  j�  j�  j�  �False�j�  j�  j�  j�  j�  j�  j�  j�  �$68291517-2377-464B-A66D-C2AE2998AE8C��$E19D1DD6-85F5-463B-85F2-0227F4519C89��$2D371737-B256-4E90-BF14-C8D9D7AED612��$1E1C32A7-2048-4173-949F-F236F74254AD��$CB56E62D-B71C-4E6E-A230-38D79BE01B6D��$9818ADDB-5786-4248-9027-CDB647F72824��$A26A30D4-EACC-4532-A621-AE25766A1A38��$4C648A16-6829-4C3F-8ABA-6792E17B562A��$00836D30-65E2-4AA3-8A7A-4571EED95206�j�  j�  j�  j�  j�  j�  j�  j�  j�  �SaxoTrader Pro�j�  j�  j�  j�  j�  j�  j�  j�  et�bh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�XEUR��XCME�j�  �ICUS�j�  j�  j�  j�  �XSES�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�549300TL5406IC1XKD09�j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�DE000F0VD0D8�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�20220201 08:13:13.590��20220201 09:07:05.713��20220201 09:07:49.537��20220201 09:47:48.767��20220201 09:09:20.730��20220201 07:51:41.113��20220201 09:08:37.367��20220201 07:54:22.953��20220201 07:58:40.553�et�bh�h�)��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�h]at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h`at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�haat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hbat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hdat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�heat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hfat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hhat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hiat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h2h:h^hchget�bhkNu��R�h
h}�(hhhK ��h��R�(KK*��h!�]�(h%h(h)h*h+h,h.h0h3h5h7h8h;h<h=h>h?h@hAhBhChDhEhFhGhHhJhKhLhOhPhQhRhShThUhWhXhYhZh[h\et�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bhkNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���K8K9K��R�u}�(j�  h�j�  j�  K:K;K��R�u}�(j�  h�j�  j�  K;K<K��R�u}�(j�  h�j�  j�  K<K=K��R�u}�(j�  h�j�  j�  K=K>K��R�u}�(j�  h�j�  j�  K?K@K��R�u}�(j�  h�j�  j�  K@KAK��R�u}�(j�  h�j�  j�  KAKBK��R�u}�(j�  h�j�  j�  KCKDK��R�u}�(j�  h�j�  j�  KDKEK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KK	K��R�u}�(j�  j  j�  j�  K
KK��R�u}�(j�  j  j�  j�  KK
K��R�u}�(j�  j&  j�  j�  KKK��R�u}�(j�  j8  j�  j�  KKK��R�u}�(j�  jJ  j�  j�  KKK��R�u}�(j�  jW  j�  hhK ��h��R�(KK��hx�C(
              9       >       B       �t�bu}�(j�  ja  j�  hhK ��h��R�(KK*��hx�BP                                             	                                                                                                                        !       "       #       %       &       '       *       +       ,       -       .       /       0       2       3       4       5       6       7       �t�bu}�(j�  j�  j�  j�  K$K%K��R�u}�(j�  j�  j�  j�  K(K)K��R�u}�(j�  j�  j�  j�  K)K*K��R�u}�(j�  j  j�  j�  K1K2K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.