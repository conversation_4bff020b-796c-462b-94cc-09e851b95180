���9      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK7��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo�� executionDetails.routingStrategy�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	hierarchy��	_order.id��_orderState.id��_order.isSynthetic��_order.__meta_model__��_orderState.__meta_model__��
orderClass��"orderIdentifiers.aggregatedOrderId��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��.orderIdentifiers.tradingVenueTransactionIdCode��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��"priceFormingData.remainingQuantity��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��!transactionDetails.positionEffect��transactionDetails.quantity��$_order.transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��multiLegReportingType��
__symbol__��	__price__��__last_px__��__stop_px__��__newo_in_file__��	__buyer__��
__seller__��
__client__��__counterparty__��__executing_entity__��__execution_within_firm__��__investment_decision__��
__trader__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(��                             
                                                                                                   !       #       �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R�h]Nu��R�e]�(hhK ��h��R�(KKK��h!�]�(�BUYI�hyhy�SELL�hyhzhzhzhzhyhyhzhzhyhyhyhzhyhzhyhyhyhzhyhzhzhzhzhyhyhzhzhyhyhyhzhyhz�ICE POF Exchange�h{h{h{h{h{h{h{h{h{h{h{h{h{h{h{h{h{h{�
2022-08-23��
2022-08-23��
2022-08-23��
2022-08-23��
2022-08-23��
2022-08-23��
2022-08-23��
2022-08-23��
2022-08-23��
2022-08-23��
2022-08-23��
2022-08-23��
2022-08-23��
2022-08-23��
2022-08-23��
2022-08-23��
2022-08-23��
2022-08-23��
2022-08-23�hyhyhyhzhyhzhzhzhzhyhyhzhzhyhyhyhzhyhz�NEWO�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��FILL��REME�h�h��REMO��pandas._libs.missing��NA���h�h��PARF�h�h�h�h�h�h�h�h�h�h��Limit�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��0|649|STLCLR��
649|STLCLR��0|649|STLCLR��0|649|STLCLR��YYour bid of 361.92 is out of the market by 3815 ticks, do you want to proceed?|649|STLCLR��
649|STLCLR��ZPrice is outside of Reasonability Limit of 1.4927|649|STLCLR|LeadTime:32|ReasonCode:random��^Your offer at 1.45840 is 1043 ticks below the Anchor Price, do you want to proceed?|649|STLCLR��0|649|STLCLR|MeterNumber:345��
649|STLCLR�� 649|STLCLR|LocationCode:Portugal��0|649|STLCLR��0|649|STLCLR���Your bid of 361.92 is out of the market by 3815 ticks, do you want to proceed?|649|STLCLR|DeliveryEndDate:2022-09-04|LocationCode:U.K��)0|649|STLCLR|DeliveryStartDate:2022-09-05��
649|STLCLR��0|649|STLCLR��XYour bid of 91.80 is 73 ticks above the Anchor Price, do you want to proceed?|649|STLCLR��
649|STLCLR�h�h�h��
ISV-TT|KBL�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��DEAL�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�]��DAVY�ah�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
Standalone�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��131650737|5253323��328021632|6880741��705750035|5871794��785154809|5351155��202877046|6880741��785154809|5351155��739376880|6777071��739376880|6777071��267797255|5280991��328021632|6880741��705750035|5871794��131650737|5351155��267797255|5280991��328021632|6880741��131650737|6841363��131650737|6841363��267797255|5280991��376808908|6018436��267797255|5280991�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhf(�            �h�b1�����R�(Kh"NNNJ����J����K t�bKK��hnt�R�hhK ��h��R�(KKK��h!�]�(�Order�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hь
OrderState�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hҌ
Regular Trade�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��5008215�h�h�h�h�h�h�h�h�h�h��5008215�h�h�h�h�h�h�h��5008213��2163875��1000651��5008211��	202877046��5008211��	739376880��	739376880��13000832��2163875��1000651��5008213��13000832��	328021632��5008213��5008213��13000832��	376808908��13000832�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h5008216��328021632:2:Unfill��1000652��5008212��	202877046��785154809:1:Ack��	739376880��	739376880��13000833��328021632:1:Ack��705750035:1:Ack��5008217��13000835��	328021632��5008215��131650737:1:Ack��13000834��	376808908��267797255:1:Ack�et�bhf(��            �~@      �?      �?      ~@      �?      ~@      �?      �?     �r@      �?      �?     �~@     �r@      �?     �~@     �~@     �r@       @     �r@�h�f8�����R�(KhkNNNJ����J����K t�bKK��hnt�R�hhK ��h��R�(KKK��h!�]�(G        G?�      G        G        G        G@~      G        G        G@i      G?�      G?�      G        G        G        G        G@~�     G@Y      G        G@r�     G@~�     G        G?�      G@~      G        G        G        G        G@Y      G        G        G@~�     G@Y      G        G@~�     G        G@Y      G        G        h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhf(��                             
                                                                                                   !       #       �hjKK��hnt�R�hhK ��h��R�(KKK��h!�]�(��s3://integration.uat.steeleye.co/sftp/feeds/batch/fix-trade/socar-pof-ice/20220823/002a16832849434e73a6b118c9fe06d2d883ecf27dc32bcb0848450298e12aa0_110083.fix���s3://integration.uat.steeleye.co/sftp/feeds/batch/fix-trade/socar-pof-ice/20220823/23022a098909eb2e507c44b5385a972f70d2031fd2155575f202278cd33bd914_110896.fix���s3://integration.uat.steeleye.co/sftp/feeds/batch/fix-trade/socar-pof-ice/20220823/2926c8053dbf9efb5d275549425dda44722dd254856167c3b13df9eaed3dfd8b_110088.fix���s3://integration.uat.steeleye.co/sftp/feeds/batch/fix-trade/socar-pof-ice/20220823/4148a9071b55c02276d8cb66e5c15dad872d703dd593b04927651ede07374308_110080.fix���s3://integration.uat.steeleye.co/sftp/feeds/batch/fix-trade/socar-pof-ice/20220823/46f6ffd94d25fca563c7cab5889baaaa48e0f5eeeb45567f5213d9fe969adedb_110091.fix���s3://integration.uat.steeleye.co/sftp/feeds/batch/fix-trade/socar-pof-ice/20220823/74a8b93e4490da44fd997cd9528a94b35acf753ba8c42a98c7b4547ba02b2872_110079.fix���s3://integration.uat.steeleye.co/sftp/feeds/batch/fix-trade/socar-pof-ice/20220823/74cf128a31ac3c822e3d221485b309c69abfcab6fbf9b50e7ab79911de447b8d_110090.fix���s3://integration.uat.steeleye.co/sftp/feeds/batch/fix-trade/socar-pof-ice/20220823/8b0c95ab47a1edab9dd35e26e6f3db5148ba3e66e368ace0b734b2d5bad5cd5a_110089.fix���s3://integration.uat.steeleye.co/sftp/feeds/batch/fix-trade/socar-pof-ice/20220823/8c7f222bcf3185eaf40354c7a10a14458ebe50513c4406e1388774f31cc1964b_110075.fix���s3://integration.uat.steeleye.co/sftp/feeds/batch/fix-trade/socar-pof-ice/20220823/9744cf2d87f2579fa7fd3528b113dd00903dbb4af9dfd02b2c7750e8d63798a9_110093.fix���s3://integration.uat.steeleye.co/sftp/feeds/batch/fix-trade/socar-pof-ice/20220823/b3a84ffbf4d9f3629fe1488ac11649bcac4183a133f72d92d0ffea13435a1417_110087.fix���s3://integration.uat.steeleye.co/sftp/feeds/batch/fix-trade/socar-pof-ice/20220823/b4f013e1e5d708de25010bb26bd6847ba4368a1cafee945ce9838e15ad54d747_110084.fix���s3://integration.uat.steeleye.co/sftp/feeds/batch/fix-trade/socar-pof-ice/20220823/ba5733a798bfbfdb22052e81176b264ec37d5875ecbfdb3a85570ec80893fabd_110077.fix���s3://integration.uat.steeleye.co/sftp/feeds/batch/fix-trade/socar-pof-ice/20220823/c208115bbe21bfafe0ec27ccb242c984410a25d69fcd0a367000cb3275291837_110092.fix���s3://integration.uat.steeleye.co/sftp/feeds/batch/fix-trade/socar-pof-ice/20220823/cdab43b2cd78ee06b2c6465d3a6a5b1d705aa81a56c033d59df63d94a3ddbfc5_110082.fix���s3://integration.uat.steeleye.co/sftp/feeds/batch/fix-trade/socar-pof-ice/20220823/ce7e1b2d20be19eeb677d3c5c0617a6eb08b3fa816f2b2cc042137eb3d5b2442_110081.fix���s3://integration.uat.steeleye.co/sftp/feeds/batch/fix-trade/socar-pof-ice/20220823/db7d1bd8e34c0fd1e86b0ac9816b5c610e775656339e3e76df75dc1e7d7a8095_110076.fix���s3://integration.uat.steeleye.co/sftp/feeds/batch/fix-trade/socar-pof-ice/20220823/e491728da44ca45ab71acdfdb406ca98cf0b2ad36822b4f9dd866935db1633ce_110078.fix���s3://integration.uat.steeleye.co/sftp/feeds/batch/fix-trade/socar-pof-ice/20220823/edc11019b2306d53de9abd51c978947e25e1b2288980afd630f5eab16193a622_110074.fix��2022-08-23T10:10:44.626687Z��2022-08-23T16:14:18.859601Z��2022-08-23T10:11:20.099689Z��2022-08-23T10:10:31.758030Z��2022-08-23T10:11:52.763205Z��2022-08-23T10:10:31.758030Z��2022-08-23T10:11:38.262911Z��2022-08-23T10:11:34.653834Z��2022-08-23T10:10:17.396876Z��2022-08-23T10:12:08.781463Z��2022-08-23T10:11:20.099689Z��2022-08-23T10:10:44.626687Z��2022-08-23T10:10:17.396876Z��2022-08-23T10:12:05.666404Z��2022-08-23T10:10:44.626687Z��2022-08-23T10:10:44.626687Z��2022-08-23T10:10:17.396876Z��2022-08-23T10:10:21.941262Z��2022-08-23T10:10:17.396876Z��2022-08-23T10:10:44.626687Z��2022-08-23T16:14:18.859601Z��2022-08-23T10:11:20.099689Z��2022-08-23T10:10:31.758030Z��2022-08-23T10:11:52.763205Z��2022-08-23T10:10:31.758030Z��2022-08-23T10:11:38.262911Z��2022-08-23T10:11:34.653834Z��2022-08-23T10:10:17.396876Z��2022-08-23T10:12:08.781463Z��2022-08-23T10:11:20.099689Z��2022-08-23T10:10:44.626687Z��2022-08-23T10:10:17.396876Z��2022-08-23T10:12:05.666404Z��2022-08-23T10:10:44.626687Z��2022-08-23T10:10:44.626687Z��2022-08-23T10:10:17.396876Z��2022-08-23T10:10:21.941262Z��2022-08-23T10:10:17.396876Z��2022-08-23T10:10:44.626687Z��2022-08-23T16:14:18.859601Z��2022-08-23T10:11:20.099689Z��2022-08-23T10:10:31.758030Z��2022-08-23T10:11:52.763205Z��2022-08-23T10:10:31.758030Z��2022-08-23T10:11:38.262911Z��2022-08-23T10:11:34.653834Z��2022-08-23T10:10:17.396876Z��2022-08-23T10:12:08.781463Z��2022-08-23T10:11:20.099689Z��2022-08-23T10:10:44.626687Z��2022-08-23T10:10:17.396876Z��2022-08-23T10:12:05.666404Z��2022-08-23T10:10:44.626687Z��2022-08-23T10:10:44.626687Z��2022-08-23T10:10:17.396876Z��2022-08-23T10:10:21.941262Z��2022-08-23T10:10:17.396876Z�j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j7  j8  j9  hyhyhyhzhyhzhzhzhzhyhyhzhzhyhyhyhzhyhzh�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�Open�j`  j`  j`  j`  j`  h�h�j`  j`  j`  j`  j`  j`  j`  j`  j`  j`  j`  G@~�     G        G?�      G@~      G        G        G        G        G@Y      G        G        G@~�     G@Y      G        G@~�     G        G@Y      G        G        �Market Side�ja  ja  ja  ja  ja  ja  ja  ja  ja  ja  ja  ja  ja  ja  ja  ja  ja  ja  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j7  j8  j9  �se_elastic_schema.static.mifid2��MultiLegReportingType����Single Leg of Multi-leg���R�jd  �Outright���R�jj  jj  jj  jj  jj  jj  jj  jj  jj  jg  jj  jj  jj  jj  jj  jj  jj  �5253323��6880741��5871794��5351155��6880741��5351155��6777071��6777071��5280991��6880741��5871794��5351155��5280991��6880741��6841363��6841363��5280991��6018436��5280991�et�bhf(��       )\���� ���Q��v@�$��C0@��Q��\@��Q��v@��Q��\@K�=�U�?K�=�U�?��(\�rW@��Q��v@�$��C0@)\���� ���(\�rW@��Q��v@)\���� �)\���� ���(\�rW@33333�V@��(\�rW@�j   KK��hnt�R�hhK ��h��R�(KKK��h!�]�(G@Z�Q��G        G@0C��$�G@\��Q�G        G        G        G        G@Wr�\(��G        G        G@\�p��
=G@Wr�\(��G        G� ��\)G        G@Wr�\(��G        G        h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��������������������et�bhhK ��h��R�(KKK��h!�]�(�id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:53013��id:53013��id:53013��id:53013��id:53013��id:53013��id:53013��id:53013��id:53013��id:53013��id:53013��id:53013��id:53013��id:53013��id:53013��id:53013��id:53013��id:53013��id:53013��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:SOCAR��id:95412�h��id:95412��id:95412�h��id:95412�h�h��id:95412�h��id:95412��id:95412��id:95412�h��id:95412��id:95412��id:95412��id:95412��id:95412��id:95412�h��id:95412��id:95412�h��id:95412�h�h��id:95412�h��id:95412��id:95412��id:95412�h��id:95412��id:95412��id:95412��id:95412��id:95412��id:steeleye-gui2��id:steeleye-gui2��id:steeleye-gui2��id:steeleye-gui2��id:steeleye-gui2��id:steeleye-gui2��id:steeleye-gui2��id:steeleye-gui2��id:steeleye-gui2��id:steeleye-gui2��id:steeleye-gui2��id:steeleye-gui2��id:steeleye-gui2��id:steeleye-gui2��id:steeleye-gui2��id:steeleye-gui2��id:steeleye-gui2��id:steeleye-gui2��id:steeleye-gui2�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3et�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h5h6h7h8h9h:h;h<et�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h>h?h@et�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hBhChDhEhFhGhHhIhJhKhLhMhNhOet�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hQhRhSet�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hThUhVhWhXhYhZh[et�bh]Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hv�mgr_locs��builtins��slice���K KK��R�u}�(j}  h�j~  j�  KKK��R�u}�(j}  h�j~  j�  KKK��R�u}�(j}  j  j~  j�  KKK��R�u}�(j}  j  j~  j�  KKK��R�u}�(j}  j  j~  j�  KKK��R�u}�(j}  j  j~  j�  KK+K��R�u}�(j}  j�  j~  j�  K+K,K��R�u}�(j}  j�  j~  j�  K,K/K��R�u}�(j}  j�  j~  j�  K/K7K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.