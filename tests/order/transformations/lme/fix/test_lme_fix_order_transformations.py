from pathlib import Path

import pandas as pd
import pytest
from prefect import context
from swarm.task.auditor import Auditor

from swarm_tasks.order.transformations.lme.fix.lme_fix_order_select_override_transformations import (
    LmeFixOrderSelectOverrideTransformations,
)
from swarm_tasks.order.transformations.lme.fix.lme_fix_order_transformations import (
    LmeFixOrderTransformations,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")

EXPECTED_SELECT_TRANSFORMED_DF = TEST_FILES_DIR.joinpath(
    "expected_select_target_frame.pkl"
)
EXPECTED_TRANSFORMED_DF = TEST_FILES_DIR.joinpath("expected_target_frame.pkl")
EXPECTED_TRANSFORMED_SINGLE_LEG_DF = TEST_FILES_DIR.joinpath(
    "expected_target_single_leg_frame.pkl"
)


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


@pytest.fixture()
def source_select_frame() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "S3FileURL": "s3://integration.uat.steeleye.co/sftp/lme_fix_icbc/"
                "196dd70f9d17b26b0c583b840de88f683281958a8327610ec03e9c94339be607_2353.fix",
                "BeginString": "FIX.4.4",
                "BodyLength": 460,
                "MsgType": "8",
                "SenderCompID": "LME",
                "TargetCompID": "SBLFIX10",
                "MsgSeqNum": 2353,
                "SendingTime": "********-19:02:10.813",
                "LastMsgSeqNumProcessed": 2268,
                "OrderID": "ON-NI-********-002066",
                "ClOrdID": "0003960-********",
                "ff_453": "5",
                "PartyID": ["SBLFIX13", "ICBC", "100003", "100003", "*********"],
                "PartyRole": [36, 11, 301, 300, 3],
                "PartyIDSource": "P",
                "ExecID": "ON-NI-********-002066-8234635",
                "ExecType": "D",
                "OrdStatus": "0",
                "ExecRestatementReason": 99,
                "AccountType": 2,
                "Symbol": "NI",
                "CFICode": "FCEPS",
                "ff_10010": "2",
                "ff_10004": ["S", "S"],
                "MaturityDate": ["********", "********"],
                "Side": "2",
                "OrderQty": 5.0,
                "OrdType": "2",
                "Price": -100.0,
                "TimeInForce": "0",
                "ExecInst": "S",
                "OrderRestrictions": "D",
                "LeavesQty": 5.0,
                "CumQty": 0.0,
                "AvgPx": 0.0,
                "TransactTime": "********-19:02:10.732",
                "Text": "4349981",
                "ff_10050": "Y",
                "ff_10051": "2",
                "ff_20023": "1",
                "CopyMsgIndicator": "Y",
                "CheckSum": "023",
                "PriceType": pd.NA,
                "ff_1057": pd.NA,
                "LastQty": pd.NA,
                "StopPx": pd.NA,
                "LegSide": ["1", "2"],
                "LegLastPx": [2144.5, 2145.25],
                "NoLegs": 2,
                "LastPx": -0.75,
            }
        ]
    )


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "S3FileURL": "s3://integration.uat.steeleye.co/sftp/lme_fix_icbc/dectrades/"
                "00bbc1a1871f44ef1c8288443e8832a6e122b6e9764a171d066a7b7e88b2d466_1110.fix",
                "BeginString": "FIX.4.4",
                "BodyLength": 470,
                "MsgType": "8",
                "SenderCompID": "LME",
                "TargetCompID": "SBLFIX10",
                "MsgSeqNum": 1110,
                "SendingTime": "********-09:21:56.259",
                "LastMsgSeqNumProcessed": 1093,
                "OrderID": "ON-CA-********-378527",
                "ClOrdID": "0004433-********",
                "ff_453": "5",
                "PartyID": ["SBLFIX13", "ICBC", "100022", "100022", "*********"],
                "PartyRole": [36, 11, 301, 300, 3],
                "PartyIDSource": "P",
                "ExecID": "ON-CA-********-378527-2227841",
                "ExecType": "0",
                "OrdStatus": "0",
                "AccountType": 2,
                "Symbol": "CA",
                "CFICode": "FCEPS",
                "ff_10010": "2",
                "ff_10004": ["R", "R"],
                "ff_10000": ["TOM", "C"],
                "MaturityDate": ["********", "********"],
                "Side": "1",
                "OrderQty": 5.0,
                "OrdType": "2",
                "Price": -8.5,
                "TimeInForce": "0",
                "ExecInst": "2",
                "OrderRestrictions": "E",
                "LeavesQty": 5.0,
                "CumQty": 0.0,
                "AvgPx": 0.0,
                "TransactTime": "********-09:21:56.257",
                "Text": "277022",
                "ff_10050": "Y",
                "ff_10051": "2",
                "ff_20023": "1",
                "CopyMsgIndicator": "Y",
                "CheckSum": "211",
                "ff_1057": pd.NA,
                "LastQty": pd.NA,
                "PriceType": pd.NA,
                "StopPx": pd.NA,
                "LegSide": ["1", "2"],
                "LegLastPx": [2144.5, 2145.25],
                "NoLegs": 2,
                "LastPx": -0.75,
            }
        ]
    )


@pytest.fixture()
def source_frame_single_leg_only() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "S3FileURL": "s3://icbc.uat.steeleye.co/fix/order-feed-lme-fix/"
                "********/037c43f51e90f158a2fcdba3ed46239c413cf8e2f4f9d3cd2f9119c20b52056f_117.fix",
                "BeginString": "FIX.4.4",
                "BodyLength": 482,
                "MsgType": "8",
                "MsgSeqNum": 117,
                "SenderCompID": "LME",
                "SendingTime": "********-01:00:49.562",
                "TargetCompID": "SBLFIX14",
                "LastMsgSeqNumProcessed": 89,
                "AvgPx": 8222.0,
                "ClOrdID": "0046739-00089825",
                "CumQty": 15.0,
                "ExecID": "TACA********X0000043A",
                "ExecInst": "2",
                "LastPx": 8222.0,
                "LastQty": 1.0,
                "OrderID": "ON-CA-********-001068",
                "OrderQty": 15.0,
                "OrdStatus": "2",
                "OrdType": "2",
                "Price": 8222.0,
                "Side": "2",
                "Symbol": "CA",
                "Text": "6443511",
                "TimeInForce": "0",
                "TransactTime": "********-01:00:49.560",
                "ExecType": "F",
                "LeavesQty": 0.0,
                "ff_453": "5",
                "PartyID": ["SBLFIX13", "ICBC", "100022", "100022", "*********"],
                "PartyRole": [36, 11, 301, 300, 3],
                "PartyIDSource": "P",
                "CFICode": "FCEPS",
                "OrderRestrictions": "E",
                "AccountType": 2,
                "CopyMsgIndicator": "Y",
                "AggressorIndicator": "N",
                "ff_10010": "1",
                "ff_10004": "R",
                "ff_10000": "3M",
                "MaturityDate": "********",
                "ff_10022": "TACA********X0000043",
                "ff_10050": "Y",
                "ff_10051": "2",
                "ff_20023": "1",
                "CheckSum": "195",
                "LegSide": pd.NA,
                "PriceType": pd.NA,
                "LegLastPx": pd.NA,
                "ff_1057": pd.NA,
                "NoLegs": pd.NA,
                "StopPx": pd.NA,
            },
            {
                "S3FileURL": "s3://icbc.uat.steeleye.co/fix/order-feed-lme-fix/********/0849c68059693edd856ae06e4b0cb3637289dcba741f90cfca7e15ec02aaeb0a_112.fix",
                "BeginString": "FIX.4.4",
                "BodyLength": 482,
                "MsgType": "8",
                "MsgSeqNum": 112,
                "SenderCompID": "LME",
                "SendingTime": "********-01:00:49.472",
                "TargetCompID": "SBLFIX14",
                "LastMsgSeqNumProcessed": 89,
                "AvgPx": 8222.0,
                "ClOrdID": "0046739-00089825",
                "CumQty": 3.0,
                "ExecID": "TACA********X0000032A",
                "ExecInst": "2",
                "LastPx": 8222.0,
                "LastQty": 1.0,
                "OrderID": "ON-CA-********-001068",
                "OrderQty": 15.0,
                "OrdStatus": "1",
                "OrdType": "2",
                "Price": 8222.0,
                "Side": "2",
                "Symbol": "CA",
                "Text": "6443511",
                "TimeInForce": "0",
                "TransactTime": "********-01:00:49.471",
                "ExecType": "F",
                "LeavesQty": 12.0,
                "ff_453": "5",
                "PartyID": ["SBLFIX13", "ICBC", "100022", "100022", "*********"],
                "PartyRole": [36, 11, 301, 300, 3],
                "PartyIDSource": "P",
                "CFICode": "FCEPS",
                "OrderRestrictions": "E",
                "AccountType": 2,
                "CopyMsgIndicator": "Y",
                "AggressorIndicator": "Y",
                "ff_10010": "1",
                "ff_10004": "R",
                "ff_10000": "3M",
                "MaturityDate": "********",
                "ff_10022": "TACA********X0000032",
                "ff_10050": "Y",
                "ff_10051": "2",
                "ff_20023": "1",
                "CheckSum": "197",
                "LegSide": pd.NA,
                "PriceType": pd.NA,
                "LegLastPx": pd.NA,
                "ff_1057": pd.NA,
                "NoLegs": pd.NA,
                "StopPx": pd.NA,
            },
        ]
    )


@pytest.fixture()
def expected_select_transformed_df() -> pd.DataFrame:
    return pd.read_pickle(EXPECTED_SELECT_TRANSFORMED_DF)


@pytest.fixture()
def expected_transformed_df() -> pd.DataFrame:
    return pd.read_pickle(EXPECTED_TRANSFORMED_DF)


@pytest.fixture()
def expected_transformed_single_leg_df() -> pd.DataFrame:
    return pd.read_pickle(EXPECTED_TRANSFORMED_SINGLE_LEG_DF)


class TestLmeFixOrderTransformations:
    """Tests LME FIX Order transformations""" ""

    def test_end_to_end_lme_fix_order_transformations(
        self,
        source_frame: pd.DataFrame,
        expected_transformed_df: pd.DataFrame,
        auditor: Auditor,
    ):
        task = LmeFixOrderTransformations(
            source_frame=source_frame,
            logger=context.get("logger"),
            auditor=auditor,
        )
        result = task.process()
        pd.testing.assert_frame_equal(result, expected_transformed_df)

    def test_end_to_end_lme_fix_order_single_leg_transformations(
        self,
        source_frame_single_leg_only: pd.DataFrame,
        expected_transformed_single_leg_df: pd.DataFrame,
        auditor: Auditor,
    ):
        task = LmeFixOrderTransformations(
            source_frame=source_frame_single_leg_only,
            logger=context.get("logger"),
            auditor=auditor,
        )
        result = task.process()
        pd.testing.assert_frame_equal(result, expected_transformed_single_leg_df)

    def test_end_to_end_lme_select_fix_order_transformations(
        self,
        source_select_frame: pd.DataFrame,
        expected_select_transformed_df: pd.DataFrame,
        auditor: Auditor,
    ):
        task = LmeFixOrderSelectOverrideTransformations(
            source_frame=source_select_frame,
            logger=context.get("logger"),
            auditor=auditor,
        )
        result = task.process()
        pd.testing.assert_frame_equal(result, expected_select_transformed_df)
