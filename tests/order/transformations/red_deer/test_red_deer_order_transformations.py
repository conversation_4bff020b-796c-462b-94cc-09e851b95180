import os
from pathlib import Path

import pandas as pd
import pytest
from prefect import context
from swarm.conf import Settings
from swarm.conf import SettingsCls
from swarm.task.auditor import Auditor

from swarm_tasks.order.transformations.red_deer.red_deer_order_transformations import (
    RedDeerOrderTransformations,
)
from swarm_tasks.order.transformations.red_deer.red_deer_order_transformations import (
    TempColumns,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
INPUT_FILE_PATH = TEST_FILES_DIR.joinpath("integration_test_with_normalised_cols.pkl")
EXPECTED_RESULT_FILE_PATH = TEST_FILES_DIR.joinpath("expected_result.pkl")


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    df = pd.read_pickle(INPUT_FILE_PATH)
    return df


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestRedDeerOrderTransformations:
    """
    Test suite for RedDeerOrderTransformations
    """

    def test_end_to_end_transformations(self, source_frame, auditor, mocker):
        """
        Runs an end-to-end test for Red Deer files using pickled data frames as the expected outputs.
        """
        os.environ["SWARM_FILE_URL"] = str(INPUT_FILE_PATH)
        Settings.STACK = "dev-blue"

        task = RedDeerOrderTransformations(
            source_frame=source_frame,
            logger=context.get("logger"),
            auditor=auditor,
        )

        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "dummy.dev.steeleye.co"

        mock_get_executing_entity = mocker.patch.object(
            RedDeerOrderTransformations, "_get_executing_entity"
        )

        mock_get_executing_entity.return_value = pd.DataFrame(
            data=pd.NA,
            index=source_frame.index,
            columns=[TempColumns.EXECUTING_ENTITY_WITH_LEI],
        )

        result = task.process()
        expected = pd.read_pickle(EXPECTED_RESULT_FILE_PATH)
        assert not pd.testing.assert_frame_equal(
            left=result.drop(["sourceKey"], axis=1),
            right=expected.drop(["sourceKey"], axis=1),
        )
