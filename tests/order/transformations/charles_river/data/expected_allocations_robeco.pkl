���5      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKW��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��(_orderState.executionDetails.orderStatus��#_order.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo��+executionDetails.passiveAggressiveIndicator�� executionDetails.routingStrategy�� executionDetails.tradingCapacity��	hierarchy��	_order.id��_orderState.id��_order.__meta_model__��_orderState.__meta_model__��"orderIdentifiers.aggregatedOrderId��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��orderIdentifiers.parentOrderId��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��"_orderState.priceFormingData.price��priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��3tradersAlgosWaiversIndicators.shortSellingIndicator��#transactionDetails.buySellIndicator��$_orderState.transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��asset_class_attribute��underlying_isin_attribute��option_type_attribute��option_strike_price_attribute��underlying_symbol_attribute��currency_attribute��expiry_date_attribute��notional_currency_2_attribute��venue_attribute��isin_attribute��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��SECURITYNAME��__option_type_mapped__��__instr_quantity_notation__��__newo_in_file__��__is_created_through_fallback__��$__inst_fb_best_ex_asset_class_main__��#__inst_fb_best_ex_asset_class_sub__��__inst_id_code__��__inst_alt_id_code__��__parties_fb_exec_entiy__��__parties_fb_trader__��__parties_fb_exec_within_firm__��__parties_fb_inv_dec_maker__��__parties_fb_client__��__parties_fb_counterparty__��__parties_fb_buyer__��__parties_fb_seller__��__fill_amount__��__order_net_amount__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(�(                                           �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R�h}�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.integer��IntegerArray���)��}�(�_data�h�(�(       [�]    [�]    N�]    N�]    B!�]    �h�i8�����R�(Kh�NNNJ����J����K t�bK��h�t�R��_mask�h�(�            �h�b1�����R�(Kh"NNNJ����J����K t�bK��h�t�R��_cache�}��dtype�h��
Int64Dtype���)��}�h�}�(�numpy_dtype�h��kind��i�usbsubh�)��}�(h�h�(�(       [�]    [�]    N�]    N�]    B!�]    �h�K��h�t�R�h�h�(�            �h�K��h�t�R�h�}�h�h�subh�)��}�(h�h�(�(       [�]    [�]    N�]    N�]    B!�]    �h�K��h�t�R�h�h�(�            �h�K��h�t�R�h�}�h�h�sub�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�DE0001102333�h،DE0006231004�hٌUS1266501006�et�b�_dtype�h͌StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�id:sogepafi��id:sogepafi��
id:jpmalgl��
id:jpmalgl��
id:jpmalgl�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�id:sogepafi��id:sogepafi��
id:jpmalgl��
id:jpmalgl��
id:jpmalgl�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�
id:rca5003��
id:rca5003��id:gen_trd_auto��id:gen_trd_auto��id:gen_trd_auto�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�*1.750 BUNDESREPUB. DEUTSCHLAND 15-FEB-2024�j  �Infineon Technologies AG�j  �CVS Health Corp�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�RCA5003�j/  �GEN_TRD_AUTO�j0  j0  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�SOGEPAFI��SOGEPAFI��JPMALGL��JPMALGL��JPMALGL�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�RNL0823��RNL0823��ROB3771��ROB3771��pandas._libs.missing��NA���et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�SOGEPAFI��SOGEPAFI��JPMALGL��JPMALGL��JPMALGL�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�SOGEPAFI��SOGEPAFI��JPMALGL��JPMALGL��JPMALGL�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�ALABAMA��ALABAMA��CGF_CEE��CGF_CEE��
IUF_CONDOR�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�SOGEPAFI��SOGEPAFI��JPMALGL��JPMALGL��JPMALGL�et�bh�h�)��ubh�(�       �h�KK��h�t�R�h�(�x           �CA    �CA    �j�@    �j�@     ��@�MbX�X@�MbX�X@tSVqA@tSVqA@��A<؞T@�MbX�X@�MbX�X@tSVqA@tSVqA@��A<؞T@�h�f8�����R�(Kh�NNNJ����J����K t�bKK��h�t�R�h�(�P                                           �       �       �       �       �       �h�KK��h�t�R�hhK ��h��R�(KK?K��h!�]�(�1�j�  j�  j�  �2�j�  j�  j�  j�  j�  �
Charles River�j�  j�  j�  j�  �
2023-03-05��
2023-03-05��
2023-03-06��
2023-03-06��
2023-03-06��BUYI�j�  j�  j�  �SELL�jN  �FILL�j�  jN  j�  �NEWO�j�  j�  j�  j�  �Market�j�  j�  j�  j�  �BOriginal Order Id: 1570246491, Source file : ROBECO_CRIMS_20230307��BOriginal Order Id: 1570246491, Source file : ROBECO_CRIMS_20230307��BOriginal Order Id: 1570246990, Source file : ROBECO_CRIMS_20230307��BOriginal Order Id: 1570246990, Source file : ROBECO_CRIMS_20230307��BOriginal Order Id: 1570251074, Source file : ROBECO_CRIMS_20230307�jN  �AGRE�j�  �PASV�j�  jN  jN  jN  jN  jN  �AOTC�j�  j�  j�  j�  �
Standalone�j�  j�  j�  j�  �A|ALABAMA|1570246491��A|ALABAMA|1570246491��A|CGF_CEE|1570246990��A|CGF_CEE|1570246990��A|IUF_CONDOR|1570251074�j�  j�  j�  j�  j�  �Order�j�  j�  j�  j�  �
OrderState�j�  j�  j�  j�  j�  j�  j�  j�  j�  �26792764|2023-03-05��26792764|2023-03-05��26792766|2023-03-06��26792766|2023-03-06��26790682|2023-03-06�jN  GAC�    G@�j�    jN  G@��     j�  j�  j�  j�  j�  �h/Users/<USER>/Desktop/steeleye/swarm-tasks/tests/order/transformations/charles_river/data/test_file.pkl�j�  j�  j�  j�  �2023-03-05T23:00:00.000000Z��2023-03-05T23:00:00.000000Z��2023-03-06T23:00:00.000000Z��2023-03-06T23:00:00.000000Z��2023-03-06T05:00:00.000000Z�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  jN  jN  jN  jN  jN  j�  j�  j�  j�  j�  �EUR�j�  j�  j�  �USD��PERC�j�  �MONE�j�  j�  jN  GAC�    G@�j�    jN  G@��     jN  jN  jN  jN  jN  �UNIT�j�  j�  j�  j�  �"se_elastic_schema.static.reference��OrderRecordType����
Allocation���R�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �IEXD�j�  �XETR�j�  �XNYS�j�  j�  j�  j�  j�  ]�(}�(�labelId�h،path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�u}�(j�  �DE0001102333EURIEXD�j�  j�  j�  j�  ue]�(}�(j�  h�j�  j�  j�  j�  u}�(j�  �DE0001102333EURIEXD�j�  j�  j�  j�  ue]�(}�(j�  h�j�  j�  j�  j�  u}�(j�  �DE0006231004EURXETR�j�  j�  j�  j�  ue]�(}�(j�  h�j�  j�  j�  j�  u}�(j�  �DE0006231004EURXETR�j�  j�  j�  j�  ue]�(}�(j�  h�j�  j�  j�  j�  u}�(j�  �US1266501006USDXNYS�j�  j�  j�  j�  uejN  jN  jN  jN  jN  jN  jN  jN  jN  jN  jN  jN  jN  jN  jN  jN  jN  jN  jN  jN  j�  j�  j�  j�  j�  jN  jN  jN  jN  jN  jN  jN  jN  jN  jN  j�  j�  j�  j�  j�  ]�(}�(j�  �
id:alabama�j�  �buyer�j�  j�  �ARRAY���R�u}�(j�  �lei:sample_lei�j�  �reportDetails.executingEntity�j�  j�  u}�(j�  �id:sogepafi�j�  �seller�j�  j�  u}�(j�  �id:sogepafi�j�  �counterparty�j�  j�  u}�(j�  �
id:rnl0823�j�  �:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�j�  j�  u}�(j�  �id:sogepafi�j�  �1tradersAlgosWaiversIndicators.executionWithinFirm�j�  j�  u}�(j�  �id:sogepafi�j�  �clientIdentifiers.client�j�  j�  u}�(j�  �
id:rca5003�j�  �trader�j�  j�  ue]�(}�(j�  �
id:alabama�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:sogepafi�j�  j�  j�  j�  u}�(j�  �id:sogepafi�j�  j  j�  j�  u}�(j�  �
id:rnl0823�j�  j  j�  j�  u}�(j�  �id:sogepafi�j�  j  j�  j�  u}�(j�  �id:sogepafi�j�  j
  j�  j�  u}�(j�  �
id:rca5003�j�  j
  j�  j�  ue]�(}�(j�  �
id:cgf_cee�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �
id:jpmalgl�j�  j�  j�  j�  u}�(j�  �
id:jpmalgl�j�  j  j�  j�  u}�(j�  �
id:rob3771�j�  j  j�  j�  u}�(j�  �
id:jpmalgl�j�  j  j�  j�  u}�(j�  �
id:jpmalgl�j�  j
  j�  j�  u}�(j�  �id:gen_trd_auto�j�  j
  j�  j�  ue]�(}�(j�  �
id:cgf_cee�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �
id:jpmalgl�j�  j�  j�  j�  u}�(j�  �
id:jpmalgl�j�  j  j�  j�  u}�(j�  �
id:rob3771�j�  j  j�  j�  u}�(j�  �
id:jpmalgl�j�  j  j�  j�  u}�(j�  �
id:jpmalgl�j�  j
  j�  j�  u}�(j�  �id:gen_trd_auto�j�  j
  j�  j�  ue]�(}�(j�  �
id:jpmalgl�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �
id:iuf_condor�j�  j�  j�  j�  u}�(j�  �
id:jpmalgl�j�  j  j�  j�  u}�(j�  �
id:jpmalgl�j�  j  j�  j�  u}�(j�  �
id:jpmalgl�j�  j
  j�  j�  u}�(j�  �id:gen_trd_auto�j�  j
  j�  j�  ue�lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��
id:alabama��
id:alabama��
id:cgf_cee��
id:cgf_cee��
id:jpmalgl��id:sogepafi��id:sogepafi��
id:jpmalgl��
id:jpmalgl��
id:iuf_condor�jN  jN  jN  jN  jN  jN  jN  jN  jN  jN  �
id:rnl0823��
id:rnl0823��
id:rob3771��
id:rob3771�jN  �id:sogepafi��id:sogepafi��
id:jpmalgl��
id:jpmalgl��
id:jpmalgl�]�(j�  j�  j�  j�  j�  j�  j  j  j  j  e]�(j�  j�  j  j  j  j  j  j  j  j  e]�(j�  j�  j   j"  j$  j&  j(  j*  j,  j.  e]�(j�  j�  j1  j3  j5  j7  j9  j;  j=  j?  e]�(j�  j�  jB  jD  jF  jH  jJ  jL  jN  ejN  jN  jN  jN  jN  j�  j�  j�  j�  j�  ������Debt Instruments�jm  �Equity�jn  jn  �Bonds�jo  � �jp  jp  �crims_DBR_FIXB_IEXD��crims_DBR_FIXB_IEXD��crims_IFX_COM_XETR��crims_IFX_COM_XETR��crims_CVS_COM_XNYS��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei�et�bh�(�(       �       �       �       �       �       �h�i8�����R�(Kh�NNNJ����J����K t�bKK��h�t�R�e]�(h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hTat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�haat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�heat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hgat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hiat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hpat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hsat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�htat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�huat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hvat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hwat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hxat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hyat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hmat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h;h<hGet�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h?hzet�bh}Nu��R�h
h}�(hhhK ��h��R�(KK?��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h8h:h=h>h@hAhBhChDhEhFhHhIhJhKhLhMhNhOhPhQhRhShUhVhWhXhYhZh[h]h^h_h`hbhchdhfhhhjhkhlhnhohqhret�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h{at�bh}Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���KKK��R�u}�(jR  h�jS  jV  KKK��R�u}�(jR  h�jS  jV  KKK��R�u}�(jR  h�jS  jV  K/K0K��R�u}�(jR  h�jS  jV  K7K8K��R�u}�(jR  h�jS  jV  K<K=K��R�u}�(jR  h�jS  jV  K@KAK��R�u}�(jR  j  jS  jV  KBKCK��R�u}�(jR  j  jS  jV  KDKEK��R�u}�(jR  j  jS  jV  KKKLK��R�u}�(jR  j(  jS  jV  KNKOK��R�u}�(jR  j3  jS  jV  KOKPK��R�u}�(jR  jA  jS  jV  KPKQK��R�u}�(jR  jQ  jS  jV  KQKRK��R�u}�(jR  j_  jS  jV  KRKSK��R�u}�(jR  jm  jS  jV  KSKTK��R�u}�(jR  j{  jS  jV  KTKUK��R�u}�(jR  j�  jS  jV  KHKIK��R�u}�(jR  j�  jS  h�(�                     "       �h�K��h�t�R�u}�(jR  j�  jS  jV  KK�K;��R�u}�(jR  j�  jS  h�(��                                                                      	       
                     
                                                                                                   !       #       $       %       &       '       (       )       *       +       ,       -       .       0       1       2       3       4       5       6       8       9       :       ;       =       >       ?       A       C       E       F       G       I       J       L       M       �h�K?��h�t�R�u}�(jR  j�  jS  jV  KVKWK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.