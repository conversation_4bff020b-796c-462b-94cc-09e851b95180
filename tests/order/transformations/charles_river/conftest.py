from typing import Dict

import pandas as pd
import pytest
from se_trades_tasks.order_and_tr.static import AssetClass

from swarm_tasks.order.feed.charles_river.static import SourceColumns
from swarm_tasks.order.feed.charles_river.static import TempColumns


@pytest.fixture()
def ultimate_venue_source_data() -> Dict[str, pd.DataFrame]:
    return {
        "source_frame": pd.DataFrame(
            [
                # Record 1: Neither future nor option. MIC not null,
                # Security Name not null, Last Market not Null -> MIC
                {
                    SourceColumns.MIC: "PINX",
                    SourceColumns.LAST_MARKET: "XNYS",
                    SourceColumns.SECURITY_NAME: "EURO-SCHATZ FUT XEUR 08-JUN-2023",
                    SourceColumns.BROKER_CODE: "BOFAEUFI",
                    SourceColumns.FILE_TYPE: "allocation",
                },
                # Record 2: Neither future nor option. MIC null,
                # Security Name not null, Last Market not null -> Last Market
                {
                    SourceColumns.MIC: pd.NA,
                    SourceColumns.LAST_MARKET: "XNYS",
                    SourceColumns.SECURITY_NAME: "EURO-SCHATZ FUT XEUR 08-JUN-2023",
                    SourceColumns.BROKER_CODE: "BOFAEUFI",
                    SourceColumns.FILE_TYPE: "allocation",
                },
                # Record 3: Future. MIC null, Security Name not null,
                # Last Market not null -> Security name
                {
                    SourceColumns.MIC: pd.NA,
                    SourceColumns.LAST_MARKET: "XNYS",
                    SourceColumns.SECURITY_NAME: "EURO-SCHATZ FUT XEUR 08-JUN-2023",
                    SourceColumns.BROKER_CODE: "BOFAEUFI",
                    SourceColumns.FILE_TYPE: "allocation",
                },
                # Record 4: Option. MIC null, Security Name not null,
                # Last Market not null -> Security name
                {
                    SourceColumns.MIC: pd.NA,
                    SourceColumns.LAST_MARKET: "XNYS",
                    SourceColumns.SECURITY_NAME: "EURO-SCHATZ FUT XEUR 08-JUN-2023",
                    SourceColumns.BROKER_CODE: "BOFAEUFI",
                    SourceColumns.FILE_TYPE: "allocation",
                },
                # Record 5: Neither future nor option. MIC null, Security Name not null,
                # Last Market Null, match in S3 file
                {
                    SourceColumns.MIC: pd.NA,
                    SourceColumns.LAST_MARKET: pd.NA,
                    SourceColumns.SECURITY_NAME: "WPP PLC",
                    SourceColumns.BROKER_CODE: "BOFAEUFI",
                    SourceColumns.FILE_TYPE: "allocation",
                },
                # Record 6: Neither future nor option. MIC null, Security Name not null,
                # Last Market Null, no match in S3 file
                {
                    SourceColumns.MIC: pd.NA,
                    SourceColumns.LAST_MARKET: pd.NA,
                    SourceColumns.SECURITY_NAME: "WPP PLC",
                    SourceColumns.BROKER_CODE: "NOMATCH",
                    SourceColumns.FILE_TYPE: "allocation",
                },
                # Record 7: Option. MIC null, Security Name not null but not valid MIC.
                # Last Market null -> match in S3 file
                {
                    SourceColumns.MIC: pd.NA,
                    SourceColumns.LAST_MARKET: pd.NA,
                    SourceColumns.SECURITY_NAME: "WPP PLC",
                    SourceColumns.BROKER_CODE: "BOFAEUFI",
                    SourceColumns.FILE_TYPE: "allocation",
                },
                # Record 8: Option. MIC null, Security Name not null but not valid MIC.
                # Last Market null -> no match in S3 file
                {
                    SourceColumns.MIC: pd.NA,
                    SourceColumns.LAST_MARKET: pd.NA,
                    SourceColumns.SECURITY_NAME: "WPP PLC",
                    SourceColumns.BROKER_CODE: "NOMATCH",
                    SourceColumns.FILE_TYPE: "allocation",
                },
            ]
        ),
        "pre_process_df": pd.DataFrame(
            [
                {TempColumns.ASSET_CLASS: pd.NA},
                {TempColumns.ASSET_CLASS: pd.NA},
                {TempColumns.ASSET_CLASS: AssetClass.FUTURE},
                {TempColumns.ASSET_CLASS: AssetClass.OPTION},
                {TempColumns.ASSET_CLASS: pd.NA},
                {TempColumns.ASSET_CLASS: pd.NA},
                {TempColumns.ASSET_CLASS: AssetClass.OPTION},
                {TempColumns.ASSET_CLASS: AssetClass.OPTION},
            ]
        ),
    }


@pytest.fixture()
def broker_code_mic_code_lookup_table() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "brokercode": "BOFAEUFI",
                "Broker Name": "BofA Securities Europe SA",
                "mic_code": "MLEX",
            },
            {
                "brokercode": "BOFANYFI",
                "Broker Name": "Bank of America Securities New York - Bonds",
                "mic_code": "BAML",
            },
            {
                "brokercode": "GSEUFI",
                "Broker Name": "Goldman Sachs Bank Europe SE",
                "mic_code": "GSEI",
            },
            {
                "brokercode": "SBNYNYFI",
                "Broker Name": "Citigroup New York - Bonds",
                "mic_code": "CGMI",
            },
            {
                "brokercode": "BARCEUFI",
                "Broker Name": "Barclays Bank Ireland PLC",
                "mic_code": "BBIE",
            },
            {
                "brokercode": "PARBLOFI",
                "Broker Name": "BNP Paribas London - Bonds & OTC derivatives",
                "mic_code": "BPSX",
            },
            {
                "brokercode": "NATWEUFI",
                "Broker Name": "NATWEST MARKETS NV",
                "mic_code": "NWNV",
            },
            {
                "brokercode": "MSINNYFI",
                "Broker Name": "Morgan Stanley New York - Bonds",
                "mic_code": "MSCO",
            },
            {
                "brokercode": "AGRIPAFI",
                "Broker Name": "Credit Agricole Paris - Bonds",
                "mic_code": "AACA",
            },
            {
                "brokercode": "INGBAMFI",
                "Broker Name": "ING Bank Amsterdam - Bonds en CDCP",
                "mic_code": "INGB",
            },
            {
                "brokercode": "BFCEPAFI",
                "Broker Name": "Natixis Banques Populaires Paris - Bonds",
                "mic_code": "NATX",
            },
            {
                "brokercode": "SBCOLOFI",
                "Broker Name": "UBS Securities London - Bonds & OTC derivatives",
                "mic_code": "UBSS",
            },
            {
                "brokercode": "HSBCEUFI",
                "Broker Name": "HSBC France SA",
                "mic_code": "HBFR",
            },
            {
                "brokercode": "CITIEUFI",
                "Broker Name": "Citibank Europe PLC",
                "mic_code": "CEPL",
            },
            {
                "brokercode": "RBCEUFI",
                "Broker Name": "RBC Capital Markets (Europe) GmbH",
                "mic_code": "RBCE",
            },
            {
                "brokercode": "ZURCCHFI",
                "Broker Name": "Zurcher Kantonalbank Zurich - Bonds",
                "mic_code": "ZKBX",
            },
            {
                "brokercode": "JSEUFI",
                "Broker Name": "Jane Street Netherlands B.V.",
                "mic_code": "JNSI",
            },
            {
                "brokercode": "BARCNYFI",
                "Broker Name": "Barclays Capital New York - Bonds",
                "mic_code": "BBOK",
            },
            {
                "brokercode": "MSEUFI",
                "Broker Name": "Morgan Stanley Europe SE",
                "mic_code": "MSEU",
            },
            {
                "brokercode": "DEUTFRFI",
                "Broker Name": "Deutsche Bank Frankfurt - Government bonds",
                "mic_code": "DBAG\xa0",
            },
            {
                "brokercode": "TDOMEUFI",
                "Broker Name": "TD GLOBAL FINANCE UNLIMITED COMPANY",
                "mic_code": "TDGF",
            },
            {
                "brokercode": "GSCONYFI",
                "Broker Name": "Goldman Sachs New York - Bonds",
                "mic_code": "GSCO\xa0",
            },
            {
                "brokercode": "HVBGDEFI",
                "Broker Name": "HVB Group Munchen - Bonds",
                "mic_code": "UCDE\xa0",
            },
            {
                "brokercode": "NORDEAFI",
                "Broker Name": "NORDEA BANK FINLAND",
                "mic_code": "XNOR",
            },
            {
                "brokercode": "MUTIGBFI",
                "Broker Name": "Mitsubishi Trust London - Bonds",
                "mic_code": "MUTI\xa0",
            },
            {
                "brokercode": "DABACOFI",
                "Broker Name": "Den Danske Bank Copenhagen - Bonds",
                "mic_code": "DASI",
            },
            {
                "brokercode": "MITSEUFI",
                "Broker Name": "MUFG Securities (Europe) N.V.",
                "mic_code": "MUSN",
            },
            {
                "brokercode": "JPMEUFI",
                "Broker Name": "J.P. Morgan AG",
                "mic_code": "JPEU",
            },
            {
                "brokercode": "SMBCEUFI",
                "Broker Name": "SMBC Bank EU AG",
                "mic_code": "SMFF",
            },
            {
                "brokercode": "FLOWAMFI",
                "Broker Name": "FLOW traders",
                "mic_code": "FLTR",
            },
            {
                "brokercode": "ABNALOFI",
                "Broker Name": "ABN AMRO Asset Management London - Bonds",
                "mic_code": "ABNA",
            },
            {
                "brokercode": "BSCHMAFI",
                "Broker Name": "Banco Santander Madrid - Bonds",
                "mic_code": "SANT",
            },
            {
                "brokercode": "RABOUTFI",
                "Broker Name": "Rabobank Nederland Utrecht - Bonds & OTC derivatives",
                "mic_code": "RABO",
            },
            {"brokercode": "BBVAEUFI", "Broker Name": "BBVA S.A.", "mic_code": "BBVA"},
            {
                "brokercode": "JEFFEUFI",
                "Broker Name": "Jefferies GmbH",
                "mic_code": "JEFE",
            },
            {
                "brokercode": "MIZUEUFI",
                "Broker Name": "Mizuho Securities Europe GmbH",
                "mic_code": "MHEU",
            },
            {
                "brokercode": "NOMUEUFI",
                "Broker Name": "Nomura Financial Products Europe GmbH",
                "mic_code": "NESI\xa0",
            },
            {
                "brokercode": "SOGEPAFI",
                "Broker Name": "Societe Generale Paris - Bonds & OTC derivatives",
                "mic_code": "XSGA",
            },
        ]
    )


@pytest.fixture()
def expected_result_ultimate_venue_with_s3_file() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {"__ultimate_venue__": "PINX"},
            {"__ultimate_venue__": "XNYS"},
            {"__ultimate_venue__": "XEUR"},
            {"__ultimate_venue__": "XEUR"},
            {"__ultimate_venue__": "MLEX"},
            {"__ultimate_venue__": "XOFF"},
            {"__ultimate_venue__": "MLEX"},
            {"__ultimate_venue__": "XOFF"},
        ]
    )


@pytest.fixture()
def expected_result_ultimate_venue_without_s3_file() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {"__ultimate_venue__": "PINX"},
            {"__ultimate_venue__": "XNYS"},
            {"__ultimate_venue__": "XEUR"},
            {"__ultimate_venue__": "XEUR"},
            {"__ultimate_venue__": "XOFF"},
            {"__ultimate_venue__": "XOFF"},
            {"__ultimate_venue__": "XOFF"},
            {"__ultimate_venue__": "XOFF"},
        ]
    )
