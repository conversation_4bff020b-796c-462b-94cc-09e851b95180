import os
from pathlib import Path
from unittest.mock import patch

import pandas as pd
import pytest
from prefect import context
from swarm.task.auditor import Auditor

from swarm_tasks.order.transformations.fidessa.order_progress.fidessa_order_progress_transformations import (
    FidessaOrderProgressTransformations,
)
from swarm_tasks.order.transformations.fidessa.order_progress.fidessa_order_progress_transformations import (
    SourceColumns,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
TEST_FILE_PATH = TEST_FILES_DIR.joinpath("order-progress-source-file.csv")
EXPECTED_RESULT_PICKLE_PATH = TEST_FILES_DIR.joinpath(
    "order_progress_expected_result.pkl"
)
MIC_TABLE_PICKLE_PATH = TEST_FILES_DIR.joinpath("mic_mapping_table.pkl")


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    df = pd.read_csv(TEST_FILE_PATH)
    df = df.set_index("__swarm_raw_index__")
    # Change the type of the date columns to object (from int)
    for col in [
        SourceColumns.AMENDED_DATE,
        SourceColumns.ENTERED_DATE,
        SourceColumns.EXPIRY_DATE,
        SourceColumns.RECEIVED_DATE,
        SourceColumns.ISIN_CODE,
    ]:
        df[col] = df[col].astype("str")
    return df


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestFidessaOrderProgressTransformations:
    """
    Test suite for FidessaOrderProgressTransformations
    """

    @patch.object(
        FidessaOrderProgressTransformations,
        "_get_mic_mapping_table_from_s3",
    )
    def test_fidessa_order_progress_end_to_end_transformations(
        self, mock_mic_table, auditor, source_frame
    ):
        """Runs an end-to-end test for FidessaOrderProgressTransformations"""
        os.environ["SWARM_FILE_URL"] = str(TEST_FILE_PATH)
        # Get the source frame from the appropriate fixture based on the file_type parameter

        mock_mic_table.return_value = pd.read_pickle(MIC_TABLE_PICKLE_PATH)
        task = FidessaOrderProgressTransformations(
            source_frame=source_frame,
            logger=context.get("logger"),
            auditor=auditor,
        )
        result = task.process()
        result = result.drop(["sourceKey"], axis=1).astype("str")

        expected = pd.read_pickle(EXPECTED_RESULT_PICKLE_PATH).astype("str")
        pd.testing.assert_frame_equal(
            left=result.sort_index(axis=1),
            right=expected.sort_index(axis=1),
            check_dtype=False,
        )
