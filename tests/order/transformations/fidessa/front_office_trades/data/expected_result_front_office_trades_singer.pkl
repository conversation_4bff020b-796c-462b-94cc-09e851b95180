��C      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK^��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��!_order.executionDetails.orderType��+executionDetails.passiveAggressiveIndicator�� executionDetails.routingStrategy��!executionDetails.settlementAmount�� executionDetails.tradingCapacity��&executionDetails.outgoingOrderAddlInfo��executionDetails.validityPeriod��_order.__meta_model__��_orderState.__meta_model__��
orderClass��"orderIdentifiers.aggregatedOrderId��(orderIdentifiers.initialOrderDesignation��$orderIdentifiers.internalOrderIdCode��!orderIdentifiers.orderRoutingCode��orderIdentifiers.sequenceNumber��.orderIdentifiers.tradingVenueTransactionIdCode��!orderIdentifiers.transactionRefNo��orderIdentifiers.orderIdCode��	hierarchy��	_order.id��_orderState.id��'_order.priceFormingData.initialQuantity��priceFormingData.price��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��timestamps.tradingDateTime��_order.timestamps.orderReceived�� _order.timestamps.orderSubmitted��timestamps.orderStatusUpdated��timestamps.validityPeriod��transactionDetails.basketId��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��transactionDetails.price��transactionDetails.priceAverage�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��'_orderState.transactionDetails.quantity��#transactionDetails.quantityNotation��transactionDetails.recordType��#transactionDetails.settlementAmount��!transactionDetails.settlementDate��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��isin_attribute��option_type_attribute��underlying_symbol_attribute��underlying_isin_attribute��asset_class_attribute��currency_attribute��venue_attribute��expiry_date_attribute��option_strike_price_attribute��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��__fallback_instr_class__��__fallback_instr_id_code__��__fallback_price_multiplier__��"__fallback_is_created_through_fb__��__fallback_id_code__��__fallback_instr_full_name__��__fallback_strike_price_type__��__fallback_strike_price_ccy__��__fallback_strike_price__��__fallback_exercise_style__��__fallback_option_type__��#__override_instrument_description__��__client_without_prefix__��!__trading_entity_without_prefix__��__trader_without_prefix__��&__investment_decision_without_prefix__��%__execution_decision_without_prefix__��__newo_in_file__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C0                                           �t�bh��__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KK9K��h!�]�(�1��2�h�h�h�h�h�h�h�h�h�h��Fidessa Front Office Trades�h�h�h�h�h��
2022-07-19��
2022-07-19��
2022-07-19��
2022-07-19��
2022-07-19��
2022-07-19��BUYI��SELL�h�h�h�h��NEWO�h�h�h�h�h��FILL�h�h�h�h�h��MARKET��QUOTE�h�h�h�h��PASV��AGRE�h�h�h�h��DEAL�h�h�h�h�h���Instrument Code: JTC.L,
Sedol: BF4X3P5,
EPIC: JTC,
Business_Transaction: Broker trade,
Entered By User Group: GENSalesTrader_MM,
Dealing Capacity: Principal��rInstrument Code: HUR.L,
Sedol: B580MF5,
EPIC: HUR,
Business_Transaction: Broker trade,
Dealing Capacity: Principal���Instrument Code: OXB.L,
Sedol: BDFBVT4,
EPIC: OXB,
Business_Transaction: Broker trade,
Entered By User Group: GENSalesTrader_MM,
Dealing Capacity: Principal���Instrument Code: OXB.L,
Sedol: BDFBVT4,
EPIC: OXB,
Business_Transaction: Broker trade,
Entered By User Group: GENSalesTrader_MM,
Dealing Capacity: Principal���Instrument Code: VTA.L,
Sedol: B28Y104,
EPIC: VTA,
Business_Transaction: Broker trade,
Entered By User Group: ICPMarketMaker,
Dealing Capacity: Principal���Instrument Code: CPH2.L,
Sedol: BP371R6,
EPIC: CPH2,
Business_Transaction: Broker trade,
Entered By User Group: GENSalesTrader_MM,
Dealing Capacity: Principal�G�      G�      G�      G�      G�      G�      �Order�h�h�h�h�h��
OrderState�h�h�h�h�h��Broker trade�h�h�h�h�h��00005723504TRLO1-1|1|20220719��00005723511TRLO1-1|1|20220719��00005723518TRLO1-1|1|20220719��00005723520TRLO1-1|1|20220719��00005723598TRLO1-1|1|20220719��00005724098TRLO1-1|1|20220719�h�h�h�h�h�h��
Standalone�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��2022-07-19T07:01:32.027472Z��2022-07-19T07:02:21.472478Z��2022-07-19T07:03:00.509797Z��2022-07-19T07:03:10.510110Z��2022-07-19T08:05:30.522912Z��2022-07-19T14:46:19.715780Z�h�h�h�h�h�h�h�h�h�h�h�hƌ2022-07-19T07:01:32.094018Z��2022-07-19T07:02:21.475634Z��2022-07-19T07:03:00.555567Z��2022-07-19T07:03:10.564139Z��2022-07-19T08:05:30.522912Z��2022-07-19T14:46:19.715780Z��pandas._libs.missing��NA���h�h�h�h�h�h�h�h�h�h�h��GBP�h�h�hЌEUR�hЌMONE�h�h�h�h�hҌUNIT�h�h�h�h�hӌMarket Side�h�h�h�h�hԌ
2022-07-21��
2022-07-21��
2022-07-21��
2022-07-21��
2022-07-21��
2022-07-21�h�h�h�h�h�h�h�h�h�h�h�hƌXOFF�h�h�h�h�h�]�(}�(�labelId��JE00BF4X3P53��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(hތJE00BF4X3P53GBPXLON�h�h�h�h�ue]�(}�(hތGB00B580MF54�h�h�h�h�u}�(hތGB00B580MF54GBPAIMX�h�h�h�h�ue]�(}�(hތGB00BDFBVT43�h�h�h�h�u}�(hތGB00BDFBVT43GBPXLON�h�h�h�h�ue]�(}�(h�h�h�h�h�h�u}�(hތGB00BDFBVT43GBPXLON�h�h�h�h�ue]�(}�(hތGG00B1GHHH78�h�h�h�h�u}�(hތGG00B1GHHH78EURXLON�h�h�h�h�ue]�(}�(hތGB00BP371R64�h�h�h�h�u}�(hތGB00BP371R64GBPAIMX�h�h�h�h�ueh�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�]�(}�(hތid:cnks�h��buyer�h�h�ARRAY���R�u}�(hތid:adam.crickmay�h��buyerDecisionMaker�h�j	  u}�(hތid:cnks�h��reportDetails.executingEntity�h�h�u}�(hތ&id:london clearing house ltd (lchgb2e)�h��seller�h�j	  u}�(hތ&id:london clearing house ltd (lchgb2e)�h��counterparty�h�h�u}�(hތid:lee.hibberd�h��:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�h�h�u}�(hތid:adam.crickmay�h��1tradersAlgosWaiversIndicators.executionWithinFirm�h�h�u}�(hތ&id:london clearing house ltd (lchgb2e)�h��clientIdentifiers.client�h�j	  u}�(hތid:adam.crickmay�h��trader�h�j	  ue]�(}�(hތid:ig markets ld (igim2)�h�j  h�j	  u}�(hތid:cnks�h�j  h�h�u}�(hތid:cnks�h�j  h�j	  u}�(hތid:james.foster�h��sellerDecisionMaker�h�j	  u}�(hތid:ig markets ld (igim2)�h�j  h�h�u}�(hތid:james.foster�h�j  h�h�u}�(hތid:james.foster�h�j  h�h�u}�(hތid:ig markets ld (igim2)�h�j  h�j	  u}�(hތid:rspq�h�j!  h�j	  ue]�(}�(hތid:cnks�h�j  h�j	  u}�(hތid:james.foster�h�j  h�j	  u}�(hތid:cnks�h�j  h�h�u}�(hތ"id:london clearing house ltd (lch)�h�j  h�j	  u}�(hތ"id:london clearing house ltd (lch)�h�j  h�h�u}�(hތid:lee.hibberd�h�j  h�h�u}�(hތid:james.foster�h�j  h�h�u}�(hތ"id:london clearing house ltd (lch)�h�j  h�j	  u}�(hތid:james.foster�h�j!  h�j	  ue]�(}�(hތid:cnks�h�j  h�j	  u}�(hތid:james.foster�h�j  h�j	  u}�(hތid:cnks�h�j  h�h�u}�(hތ'id:london clearing house ltd (itg_algo)�h�j  h�j	  u}�(hތ'id:london clearing house ltd (itg_algo)�h�j  h�h�u}�(hތid:lee.hibberd�h�j  h�h�u}�(hތid:james.foster�h�j  h�h�u}�(hތ'id:london clearing house ltd (itg_algo)�h�j  h�j	  u}�(hތid:james.foster�h�j!  h�j	  ue]�(}�(hތ(id:hargreaves lansdown stkbrokers (hlsl)�h�j  h�j	  u}�(hތid:cnks�h�j  h�h�u}�(hތid:cnks�h�j  h�j	  u}�(hތid:anthony.harmer�h�j+  h�j	  u}�(hތ(id:hargreaves lansdown stkbrokers (hlsl)�h�j  h�h�u}�(hތid:anthony.harmer�h�j  h�h�u}�(hތid:anthony.harmer�h�j  h�h�u}�(hތ(id:hargreaves lansdown stkbrokers (hlsl)�h�j  h�j	  u}�(hތid:anthony.harmer�h�j!  h�j	  ue]�(}�(hތid:credo capital plc (cred)�h�j  h�j	  u}�(hތid:cnks�h�j  h�h�u}�(hތid:cnks�h�j  h�j	  u}�(hތid:james.foster�h�j+  h�j	  u}�(hތid:credo capital plc (cred)�h�j  h�h�u}�(hތid:james.foster�h�j  h�h�u}�(hތid:james.foster�h�j  h�h�u}�(hތid:credo capital plc (cred)�h�j  h�j	  u}�(hތid:james.foster�h�j!  h�j	  ue�id:cnks��id:ig markets ld (igim2)��id:cnks��id:cnks��(id:hargreaves lansdown stkbrokers (hlsl)��id:credo capital plc (cred)��&id:london clearing house ltd (lchgb2e)��id:cnks��"id:london clearing house ltd (lch)��'id:london clearing house ltd (itg_algo)��id:cnks��id:cnks��&id:london clearing house ltd (lchgb2e)��id:ig markets ld (igim2)��"id:london clearing house ltd (lch)��'id:london clearing house ltd (itg_algo)��(id:hargreaves lansdown stkbrokers (hlsl)��id:credo capital plc (cred)��id:adam.crickmay�hόid:james.foster��id:james.foster�h�h�hόid:james.foster�h�hόid:anthony.harmer��id:james.foster��&id:london clearing house ltd (lchgb2e)��id:ig markets ld (igim2)��"id:london clearing house ltd (lch)��'id:london clearing house ltd (itg_algo)��(id:hargreaves lansdown stkbrokers (hlsl)��id:credo capital plc (cred)�]�(h�h�j  j
  j
  j  j  j  j  j  j  e]�(h�h�j#  j%  j'  j)  j,  j.  j0  j2  j4  e]�(h�h�j7  j9  j;  j=  j?  jA  jC  jE  jG  e]�(h�h�jJ  jL  jN  jP  jR  jT  jV  jX  jZ  e]�(h�h�j]  j_  ja  jc  je  jg  ji  jk  jm  e]�(h�j  jp  jr  jt  jv  jx  jz  j|  j~  j�  eh�h�h�h�h�h��ID�j�  j�  j�  j�  j�  �JTC.L JTC PLC ORD GBP0.01��HUR.L HURRICANE ENERG ORD 0.1P��OXB.L OXFORD BIOMED. ORD 50P��OXB.L OXFORD BIOMED. ORD 50P��VTA.L VOLTA FIN ORD NPV��"CPH2.L CLEAN POWER HYD ORD GBP0.01�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hό#LONDON CLEARING HOUSE LTD (LCHGB2E)��IG MARKETS LD (IGIM2)��LONDON CLEARING HOUSE LTD (LCH)��$LONDON CLEARING HOUSE LTD (ITG_ALGO)��%HARGREAVES LANSDOWN STKBROKERS (HLSL)��CREDO CAPITAL PLC (CRED)�������et�b�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(h�h�h�h�h�h�et�b�_dtype�j�  �StringDtype���)��ubhhK ��h��R�(KK	K��h�f8�����R�(Kh�NNNJ����J����K t�b�B�  \���(�V@�G�z�r@���Q��@�����"�@{�Ga/�@    `��@      ,@     d�@     @^@     �\@     �@     ��@ףp=
�@y�j���?�(\���@�(\���@��Q��@�G�z�?      ,@     d�@     @^@     �\@     �@     ��@ףp=
�@y�j���?�(\���@�(\���@��Q��@�G�z�?ףp=
�@y�j���?�(\���@�(\���@��Q��@�G�z�?      ,@     d�@     @^@     �\@     �@     ��@\���(�V@�G�z�r@���Q��@�����"�@{�Ga/�@    `��@                                                �t�bj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(h�h�h�h�h�h�et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(�
L6R0N7TVM0�hό
L6R0OBR820��
L6R0OBR83U�h�h�et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(h�h�h�h�h�h�et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(�GEN2��GEN1�j�  j�  �IFT1�j�  et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(h�h�h�h�h�h�et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(�5.82055E+14��
9.1793E+17�j  j  �1.02627E+18��5.95552E+17�et�bj�  j�  )��ubhhK ��h��R�(KKK��h��C0                                           �t�bj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(j�  j�  j�  j�  j�  j�  et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(�JTC.L��HUR.L��OXB.L�j*  �VTA.L��CPH2.L�et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(�XLON��AIMX�j6  j6  j6  j7  et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(h�h�h�h�h�j   et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(h�h�h�h�h�h�et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(h�h�h�h�h�h�et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(h�h�h�h�h�h�et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(j6  j7  j6  j6  j6  j7  et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��h!�]�(�id:cnks��id:cnks��id:cnks��id:cnks��id:cnks��id:cnks�et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��h!�]�(�id:lee.hibberd��id:james.foster��id:lee.hibberd��id:lee.hibberd��id:anthony.harmer��id:james.foster�et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��h!�]�(�id:adam.crickmay��id:james.foster��id:james.foster��id:james.foster��id:anthony.harmer��id:james.foster�et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��h!�]�(�id:adam.crickmay��id:rspq��id:james.foster��id:james.foster��id:anthony.harmer��id:james.foster�et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(h�h�h�h�h�h�et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(h�h�h�h�h�j   et�bj�  j�  )��ubhhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C�t�bj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(�JTC PLC ORD GBP0.01��HURRICANE ENERG ORD 0.1P��OXFORD BIOMED. ORD 50P�j�  �VOLTA FIN ORD NPV��CLEAN POWER HYD ORD GBP0.01�et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(�CNKS�j�  j�  j�  j�  j�  et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(�
Adam.Crickmay��RSPQ��James.Foster�j�  �Anthony.Harmer�j�  et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(�Lee.Hibberd��James.Foster�j�  j�  �Anthony.Harmer�j�  et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��j�  �]�(�
Adam.Crickmay��James.Foster�j�  j�  �Anthony.Harmer�j�  et�bj�  j�  )��ube]�(h
h}�(hhhK ��h��R�(KK9��h!�]�(h%h&h'h(h)h*h+h,h-h0h1h2h3h4h5h<h=h>h?h@hDhFhGhHhIhJhLhPhQhShThVhWhXhZh[h`hahchehghhhihjhkhnhphshuhvhwhxhyhzh{h}h�et�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK	��h!�]�(h/hAhBhChNhOhRhUhdet�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hYat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h]at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h^at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hbat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hfat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hlat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hmat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hoat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hqat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hrat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�htat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h|at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h~at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs�hhK ��h��R�(KK9��h�i8�����R�(Kh�NNNJ����J����K t�b�B�                                                                                
                                                                      !       "       #       $       %       '       +       ,       .       /       1       2       3       5       6       ;       <       >       @       B       C       D       E       F       I       K       N       P       Q       R       S       T       U       V       X       ]       �t�bu}�(j  j�  j  �builtins��slice���K	K
K��R�u}�(j  j�  j  hhK ��h��R�(KK	��j  �CH
                            )       *       -       0       ?       �t�bu}�(j  j�  j  j!  KKK��R�u}�(j  j�  j  j!  KKK��R�u}�(j  j�  j  j!  KKK��R�u}�(j  j�  j  j!  KKK��R�u}�(j  j�  j  j!  KKK��R�u}�(j  j  j  j!  KKK��R�u}�(j  j  j  j!  K K!K��R�u}�(j  j  j  j!  K&K'K��R�u}�(j  j!  j  j!  K(K)K��R�u}�(j  j/  j  j!  K4K5K��R�u}�(j  j:  j  j!  K7K8K��R�u}�(j  jC  j  j!  K8K9K��R�u}�(j  jL  j  j!  K9K:K��R�u}�(j  jU  j  j!  K:K;K��R�u}�(j  j^  j  j!  K=K>K��R�u}�(j  jg  j  j!  KAKBK��R�u}�(j  jv  j  j!  KGKHK��R�u}�(j  j�  j  j!  KHKIK��R�u}�(j  j�  j  j!  KJKKK��R�u}�(j  j�  j  j!  KLKMK��R�u}�(j  j�  j  j!  KMKNK��R�u}�(j  j�  j  j!  KOKPK��R�u}�(j  j�  j  j!  KWKXK��R�u}�(j  j�  j  j!  KYKZK��R�u}�(j  j�  j  j!  KZK[K��R�u}�(j  j�  j  j!  K[K\K��R�u}�(j  j�  j  j!  K\K]K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.