import os
from pathlib import Path

import pandas as pd
import pytest
from prefect import context
from swarm.task.auditor import Auditor

from swarm_tasks.order.transformations.fidessa.front_office_trades.cenkos_fidessa_front_office_trades_transformations import (
    CenkosFidessaFrontOfficeTradesTransformations,
)
from swarm_tasks.order.transformations.fidessa.front_office_trades.fidessa_front_office_trades_transformations import (
    FidessaFrontOfficeTradesTransformations,
)
from swarm_tasks.order.transformations.fidessa.front_office_trades.investec_fidessa_front_office_trades_transformations import (
    InvestecFidessaFrontOfficeTradesTransformations,
)
from swarm_tasks.order.transformations.fidessa.front_office_trades.singer_fidessa_front_office_trades_transformations import (
    SingerFidessaFrontOfficeTradesTransformations,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
TEST_FILE_PATH = TEST_FILES_DIR.joinpath("source_frame.pkl")
EXPECTED_RESULT_PICKLE_PATH_STANDARD = TEST_FILES_DIR.joinpath(
    "expected_result_front_office_trades.pkl"
)
EXPECTED_RESULT_PICKLE_PATH_CENKOS = TEST_FILES_DIR.joinpath(
    "expected_result_front_office_trades_cenkos.pkl"
)
EXPECTED_RESULT_PICKLE_PATH_INVESTEC = TEST_FILES_DIR.joinpath(
    "expected_result_front_office_trades_investec.pkl"
)
EXPECTED_RESULT_PICKLE_PATH_SINGER = TEST_FILES_DIR.joinpath(
    "expected_result_front_office_trades_singer.pkl"
)


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    return pd.read_pickle(TEST_FILE_PATH)


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestFidessaFrontOfficeTradesTransformations:
    """
    Test suite for FidessaFrontOfficeTradesTransformations
    """

    def test_front_office_end_to_end_transformations(self, auditor, source_frame):
        """Runs an end-to-end test for FidessaFrontOfficeTradesTransformations"""
        os.environ["SWARM_FILE_URL"] = str(TEST_FILE_PATH)
        result = self._run_task(
            front_office_task=FidessaFrontOfficeTradesTransformations,
            source_frame=source_frame,
            auditor=auditor,
        )
        expected = pd.read_pickle(EXPECTED_RESULT_PICKLE_PATH_STANDARD).drop(
            ["sourceKey"], axis=1, errors="ignore"
        )
        pd.testing.assert_frame_equal(
            left=result.sort_index(axis=1),
            right=expected.sort_index(axis=1),
            check_dtype=False,
        )

    def test_front_office_cenkos_override_transformations(self, auditor, source_frame):
        """Runs an end-to-end test for CenkosFidessaFrontOfficeTradesTransformations (Cenkos override)."""
        os.environ["SWARM_FILE_URL"] = str(TEST_FILE_PATH)
        result = self._run_task(
            front_office_task=CenkosFidessaFrontOfficeTradesTransformations,
            source_frame=source_frame,
            auditor=auditor,
        )
        expected = pd.read_pickle(EXPECTED_RESULT_PICKLE_PATH_CENKOS).drop(
            ["sourceKey"], axis=1, errors="ignore"
        )
        pd.testing.assert_frame_equal(
            left=result.sort_index(axis=1),
            right=expected.sort_index(axis=1),
            check_dtype=False,
        )

    def test_front_office_investec_override_transformations(
        self, auditor, source_frame
    ):
        """Runs an end-to-end test for InvestecFidessaFrontOfficeTradesTransformations (Investec override)."""
        os.environ["SWARM_FILE_URL"] = str(TEST_FILE_PATH)
        result = self._run_task(
            front_office_task=InvestecFidessaFrontOfficeTradesTransformations,
            source_frame=source_frame,
            auditor=auditor,
        )
        expected = pd.read_pickle(EXPECTED_RESULT_PICKLE_PATH_INVESTEC).drop(
            ["sourceKey"], axis=1, errors="ignore"
        )
        pd.testing.assert_frame_equal(
            left=result.sort_index(axis=1),
            right=expected.sort_index(axis=1),
            check_dtype=False,
        )

    def test_front_office_singer_override_transformations(self, auditor, source_frame):
        """Runs an end-to-end test for SingerFidessaFrontOfficeTradesTransformations (Singer override)."""
        os.environ["SWARM_FILE_URL"] = str(TEST_FILE_PATH)
        result = self._run_task(
            front_office_task=SingerFidessaFrontOfficeTradesTransformations,
            source_frame=source_frame,
            auditor=auditor,
        )
        expected = pd.read_pickle(EXPECTED_RESULT_PICKLE_PATH_SINGER)
        pd.testing.assert_frame_equal(
            left=result.sort_index(axis=1),
            right=expected.sort_index(axis=1),
            check_dtype=False,
        )

    @staticmethod
    def _run_task(
        front_office_task, source_frame: pd.DataFrame, auditor: Auditor
    ) -> pd.DataFrame:

        task = front_office_task(
            source_frame=source_frame,
            logger=context.get("logger"),
            auditor=auditor,
        )
        result = task.process()
        return result.drop(["sourceKey"], axis=1, errors="ignore")
