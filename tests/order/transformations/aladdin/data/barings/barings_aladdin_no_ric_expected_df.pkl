��o      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK_��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��executionDetails.limitPrice��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��executionDetails.stopPrice��&executionDetails.outgoingOrderAddlInfo�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	_order.id��_orderState.id�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��asset_class_attribute��currency_attribute��notional_currency_2_attribute��option_type_attribute��#instrument_classification_attribute��underlying_index_term_attribute��expiry_date_attribute��option_strike_price_attribute��underlying_symbol_attribute��venue_attribute��isin_attribute��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��__fallback_buyer__��__fallback_seller__��__fallback_counterparty__��__fallback_client__��__fallback_buyer_dec_maker__��__fallback_seller_dec_maker__��__fallback_inv_dec_in_firm__��__fallback_trader__��__fallback_exec_within_firm__��__fallback_executing_entity__��marketIdentifiers��_order.__meta_model__��_orderState.__meta_model__��(orderIdentifiers.initialOrderDesignation��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��!orderIdentifiers.transactionRefNo��.orderIdentifiers.tradingVenueTransactionIdCode�� priceFormingData.initialQuantity��priceFormingData.price��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��	sourceKey��sourceIndex��timestamps.orderReceived��timestamps.orderSubmitted�� timestamps.internalOrderReceived��!timestamps.internalOrderSubmitted��timestamps.orderStatusUpdated��&_orderState.timestamps.tradingDateTime��transactionDetails.basketId��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��'_orderState.transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��._orderState.transactionDetails.tradingDateTime��__ric__�� __instrument_unique_identifier__��__asset_class__��__pricing_reference_lxid__��__pricing_reference_redcode__��__newo_in_file__��__instrument_full_name__��__instrument_classification__��__cfi_category__��
__cfi_group__��__file_type_asset_class__��!__instrument_created_through_fb__��__best_ex_asset_class_main__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(�h                                                                       	       
                     �h�i8�����R�(K�<�NNNJ����J����K t�bK
���C�t�R�h��__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK
��h!�]�(�	C|1500317��	C|1475748��	C|1525406��	C|1553861��	C|1728166��	C|1677248��	C|1677348��	C|1677448��	C|1677548��
M|1551596|OTC��
M|1551796|OTC��	M|3650755��
M|1552061|OTC�et�b�_dtype�h��StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�pandas._libs.missing��NA���h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�id:2289��id:1978��id:1978��id:2177��id:1966��id:2171��id:2171��id:2171��id:2171��id:2045��id:2289��id:2169��id:2289�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�id:kwso��id:kwso��id:kwso��id:brku��id:brku��id:adri��id:adri��id:adri��id:adri��id:duto��id:hube��id:aido��id:hube�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�id:caad��id:caad��id:caad��id:duto��id:duto��id:sha��id:sha��id:sha��id:sha��id:duto��id:aido��id:aido��id:aido�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�id:caad��id:caad��id:caad��id:duto��id:duto��id:sha��id:sha��id:sha��id:sha��id:duto��id:aido��id:aido��id:aido�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�2289��1978��1978��2177��1966��2171��2171��2171��2171��2045��2289��2169��2289�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�kwso��kwso��kwso��brku��brku��adri��adri��adri��adri��duto��hube��aido��hube�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�caad��caad��caad��duto��duto��sha��sha��sha��sha��duto��aido��aido��aido�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�caad��caad��caad��duto��duto��sha��sha��sha��sha��duto��aido��aido��aido�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�CMLDRV�j�  j�  �MMLDRV�j�  �RPEMLDF��BCEMBTR��MMIFEM��40TREMF�j�  j�  �HOSTEM�j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(h�h�h�h�h�h�h�h�h�h�ȟTYU24�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�XXXXMXEAOC2025-06-13 00:00:00��XXXXSPLVOC2025-06-13 00:00:00��XXXXSPLVOC2025-06-13 00:00:00�h�ȟ XXXXUSD/ZAROP2024-10-03 00:00:00�� XXXXUSD/ZAROP2024-10-03 00:00:00�� XXXXUSD/ZAROP2024-10-03 00:00:00�� XXXXUSD/ZAROP2024-10-03 00:00:00�h�ȟaladdin-ric-TYU24�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�JUN25 MXEA  C @ 2451��JUN25 SPLVP5UP  C @ 276��JUN25 SPLVP5UP  C @ 289��!SWP: OIS 3.478000 30-AUG-2027 SOF��!SWP: OIS 3.349500 30-AUG-2029 SOF��USD P ZAR C @18.38500 EO�j�  j�  j�  �CSWAP: GBP/USD 05-OCT-2029��CSWAP: EUR/USD 13-NOV-2029��US 10YR NOTE SEP 24�j�  et�bh�h�)��ubh�(�
       �h�b1�����R�(Kh"NNNJ����J����K t�bKK
��h�t�R�h�(�h            �@    @�@    @�@   �I�A    �C�A    �^uA    �^uA    �^uA    �^uA    �׷A    ̿iA     �U@    `�6A�h�f8�����R�(Kh�NNNJ����J����K t�bKK
��h�t�R�h�(�h                                                                       	       
                     �h�KK
��h�t�R�hhK ��h��R�(KKKK
��h!�]�(�2��1�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �Aladdin�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
2024-07-01��
2024-07-01��
2024-07-01��
2024-08-28��
2024-08-28��
2024-08-28��
2024-08-28��
2024-08-28��
2024-08-28��
2024-08-28��
2024-08-28��
2024-08-26��
2024-08-28��SELL��BUYI�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  h�h�h�h�h�h�h�h�h�G�      G�      G�      G�      �NEWO�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �FILL�j�  j�  j�  j�  j�  j�  j�  j�  �PARF�j�  j�  j�  �Market�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  h�h�h�h�h�h�h�h�h�G�      G�      G�      G�      �OassetId: BGS2HPA96
Transaction Type: SELLOPEN
genComments: 
tradePurpose: MMALM��JassetId: BGS2HPAB1
Transaction Type: BUY
genComments: 
tradePurpose: MMALM��OassetId: BGS2HPA70
Transaction Type: SELLOPEN
genComments: 
tradePurpose: MMALM��RassetId: BGS2NW1D5
Transaction Type: RCVFIX INIT
genComments: 
tradePurpose: MMALM��RassetId: BGS2NW152
Transaction Type: RCVFIX INIT
genComments: 
tradePurpose: MMALM��LassetId: BGS2HSWE5
Transaction Type: SELL
genComments: 
tradePurpose: PER_PM��LassetId: BGS2HSWE5
Transaction Type: SELL
genComments: 
tradePurpose: PER_PM��LassetId: BGS2HSWE5
Transaction Type: SELL
genComments: 
tradePurpose: PER_PM��LassetId: BGS2HSWE5
Transaction Type: SELL
genComments: 
tradePurpose: PER_PM��SassetId: BGS2NV535
Transaction Type: RCVBASE INIT
genComments: 
tradePurpose: MMFCP��UassetId: BGS19PQ66
Transaction Type: PAYBASE UNWIND
genComments: 
tradePurpose: MMFCP��cPosRoll26AUG24 12:43:02
assetId: TYU420242
Transaction Type: SELL
genComments: 
tradePurpose: MMMHP��UassetId: BGS19PYK6
Transaction Type: PAYBASE UNWIND
genComments: 
tradePurpose: MMFCP��AOTC�j  j  j  j  j  j  j  j  j  j  j  j  ]��DAVY�a]�j  a]�j  a]�j  a]�j  a]�j  a]�j  a]�j  a]�j  a]�j  a]�j  a]�j  a]�j  a�XOFF�j  j  j  j  j  j  j  j  j  j  �XCBT�j  j  j  j  j  j  j  j  j  j  j  j  j  j  ]�(}�(�labelId�j�  �path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�u}�(j  �XXXXMXEAOC2025-06 00:00:00�j  j  j  j  u}�(j  �*XXXXMXEAOC2025-06-13 00:00:002451.00000000�j  j  j  j  u}�(j  �(XXXXMXEAOC2025-06-13 00:00:0024.51000000�j  j  j  j  u}�(j  �'XXXXMXEAOC2025-06-13 00:00:002.45100000�j  j  j  j  u}�(j  �'XXXXMXEAOC2025-06-13 00:00:000.24510000�j  j  j  j  u}�(j  �'XXXXMXEAOC2025-06 00:00:002451.00000000�j  j  j  j  u}�(j  �%XXXXMXEAOC2025-06 00:00:0024.51000000�j  j  j  j  u}�(j  �$XXXXMXEAOC2025-06 00:00:002.45100000�j  j  j  j  u}�(j  �$XXXXMXEAOC2025-06 00:00:000.24510000�j  j  j  j  ue]�(}�(j  j�  j  j  j  j  u}�(j  �XXXXSPLVOC2025-06 00:00:00�j  j  j  j  u}�(j  �)XXXXSPLVOC2025-06-13 00:00:00276.00000000�j  j  j  j  u}�(j  �'XXXXSPLVOC2025-06-13 00:00:002.76000000�j  j  j  j  u}�(j  �'XXXXSPLVOC2025-06-13 00:00:000.27600000�j  j  j  j  u}�(j  �'XXXXSPLVOC2025-06-13 00:00:000.02760000�j  j  j  j  u}�(j  �&XXXXSPLVOC2025-06 00:00:00276.00000000�j  j  j  j  u}�(j  �$XXXXSPLVOC2025-06 00:00:002.76000000�j  j  j  j  u}�(j  �$XXXXSPLVOC2025-06 00:00:000.27600000�j  j  j  j  u}�(j  �$XXXXSPLVOC2025-06 00:00:000.02760000�j  j  j  j  ue]�(}�(j  j�  j  j  j  j  u}�(j  �XXXXSPLVOC2025-06 00:00:00�j  j  j  j  u}�(j  �)XXXXSPLVOC2025-06-13 00:00:00289.00000000�j  j  j  j  u}�(j  �'XXXXSPLVOC2025-06-13 00:00:002.89000000�j  j  j  j  u}�(j  �'XXXXSPLVOC2025-06-13 00:00:000.28900000�j  j  j  j  u}�(j  �'XXXXSPLVOC2025-06-13 00:00:000.02890000�j  j  j  j  u}�(j  �&XXXXSPLVOC2025-06 00:00:00289.00000000�j  j  j  j  u}�(j  �$XXXXSPLVOC2025-06 00:00:002.89000000�j  j  j  j  u}�(j  �$XXXXSPLVOC2025-06 00:00:000.28900000�j  j  j  j  u}�(j  �$XXXXSPLVOC2025-06 00:00:000.02890000�j  j  j  j  ueh�h�]�(}�(j  j�  j  j  j  j  u}�(j  �XXXXUSD/ZAROP2024-10 00:00:00�j  j  j  j  u}�(j  �+XXXXUSD/ZAROP2024-10-03 00:00:0018.38500000�j  j  j  j  u}�(j  �*XXXXUSD/ZAROP2024-10-03 00:00:000.18385000�j  j  j  j  u}�(j  �*XXXXUSD/ZAROP2024-10-03 00:00:000.01838500�j  j  j  j  u}�(j  �*XXXXUSD/ZAROP2024-10-03 00:00:000.00183850�j  j  j  j  u}�(j  �(XXXXUSD/ZAROP2024-10 00:00:0018.38500000�j  j  j  j  u}�(j  �'XXXXUSD/ZAROP2024-10 00:00:000.18385000�j  j  j  j  u}�(j  �'XXXXUSD/ZAROP2024-10 00:00:000.01838500�j  j  j  j  u}�(j  �'XXXXUSD/ZAROP2024-10 00:00:000.00183850�j  j  j  j  ue]�(}�(j  j�  j  j  j  j  u}�(j  �XXXXUSD/ZAROP2024-10 00:00:00�j  j  j  j  u}�(j  �+XXXXUSD/ZAROP2024-10-03 00:00:0018.38500000�j  j  j  j  u}�(j  �*XXXXUSD/ZAROP2024-10-03 00:00:000.18385000�j  j  j  j  u}�(j  �*XXXXUSD/ZAROP2024-10-03 00:00:000.01838500�j  j  j  j  u}�(j  �*XXXXUSD/ZAROP2024-10-03 00:00:000.00183850�j  j  j  j  u}�(j  �(XXXXUSD/ZAROP2024-10 00:00:0018.38500000�j  j  j  j  u}�(j  �'XXXXUSD/ZAROP2024-10 00:00:000.18385000�j  j  j  j  u}�(j  �'XXXXUSD/ZAROP2024-10 00:00:000.01838500�j  j  j  j  u}�(j  �'XXXXUSD/ZAROP2024-10 00:00:000.00183850�j  j  j  j  ue]�(}�(j  j�  j  j  j  j  u}�(j  �XXXXUSD/ZAROP2024-10 00:00:00�j  j  j  j  u}�(j  �+XXXXUSD/ZAROP2024-10-03 00:00:0018.38500000�j  j  j  j  u}�(j  �*XXXXUSD/ZAROP2024-10-03 00:00:000.18385000�j  j  j  j  u}�(j  �*XXXXUSD/ZAROP2024-10-03 00:00:000.01838500�j  j  j  j  u}�(j  �*XXXXUSD/ZAROP2024-10-03 00:00:000.00183850�j  j  j  j  u}�(j  �(XXXXUSD/ZAROP2024-10 00:00:0018.38500000�j  j  j  j  u}�(j  �'XXXXUSD/ZAROP2024-10 00:00:000.18385000�j  j  j  j  u}�(j  �'XXXXUSD/ZAROP2024-10 00:00:000.01838500�j  j  j  j  u}�(j  �'XXXXUSD/ZAROP2024-10 00:00:000.00183850�j  j  j  j  ue]�(}�(j  j�  j  j  j  j  u}�(j  �XXXXUSD/ZAROP2024-10 00:00:00�j  j  j  j  u}�(j  �+XXXXUSD/ZAROP2024-10-03 00:00:0018.38500000�j  j  j  j  u}�(j  �*XXXXUSD/ZAROP2024-10-03 00:00:000.18385000�j  j  j  j  u}�(j  �*XXXXUSD/ZAROP2024-10-03 00:00:000.01838500�j  j  j  j  u}�(j  �*XXXXUSD/ZAROP2024-10-03 00:00:000.00183850�j  j  j  j  u}�(j  �(XXXXUSD/ZAROP2024-10 00:00:0018.38500000�j  j  j  j  u}�(j  �'XXXXUSD/ZAROP2024-10 00:00:000.18385000�j  j  j  j  u}�(j  �'XXXXUSD/ZAROP2024-10 00:00:000.01838500�j  j  j  j  u}�(j  �'XXXXUSD/ZAROP2024-10 00:00:000.00183850�j  j  j  j  ueh�h�]�(}�(j  �XCBTTYUFF2024-09-19 00:00:00�j  j  j  j  u}�(j  �XCBTTYUFF2024-09 00:00:00�j  j  j  j  ueȟoption�j�  j�  h�h�j�  j�  j�  j�  h�ȟfuture�ȟUSD�j�  j�  j�  j�  j�  j�  j�  j�  �GBP��EUR�j�  j�  h�h�h�h�h�h�h�h�h�h�h�h�ȟCALL�j�  j�  h�ȟPUTO�j�  j�  j�  h�h�h�ȟOCXXXX�j�  j�  �SRHXXX�j�  �OPXXXX�j�  j�  j�  �SEXXXX�j�  �FFXXXX�j�  h�h�ȟ1 DAY�j�  h�h�h�h�h�h�h�ȟ
2025-06-13��
2025-06-13��
2025-06-13��
2027-08-30��
2029-08-30��
2024-10-03��
2024-10-03��
2024-10-03��
2024-10-03��
2029-10-05��
2029-11-13��
2024-09-19��
2029-11-13�G@�&     G@q@     G@r     h�h�G@2b�\(��G@2b�\(��G@2b�\(��G@2b�\(��h�h�h�ȟMXEA��SPLV��SPLV�h�ȟUSD/ZAR�j�  j�  j�  �GBP/USD��EUR/USD��TYU��EUR/USD�j  j  j  j  j  j  j  j  j  j  j  j  j  ]�(}�(j  �id:2289�j  �buyer�j  j  �ARRAY���R�u}�(j  �!lei:test_aladdin_executing_entity�j  �reportDetails.executingEntity�j  j  u}�(j  �!lei:test_aladdin_executing_entity�j  �seller�j  j�  u}�(j  �id:2289�j  �counterparty�j  j  u}�(j  �id:kwso�j  �:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�j  j  u}�(j  �id:caad�j  �1tradersAlgosWaiversIndicators.executionWithinFirm�j  j  u}�(j  �id:857�j  �clientIdentifiers.client�j  j�  u}�(j  �id:caad�j  �trader�j  j�  ue]�(}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j�  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j  u}�(j  �id:1978�j  j�  j  j�  u}�(j  �id:1978�j  j�  j  j  u}�(j  �id:kwso�j  j�  j  j  u}�(j  �id:caad�j  j�  j  j  u}�(j  �id:857�j  j�  j  j�  u}�(j  �id:caad�j  j�  j  j�  ue]�(}�(j  �id:1978�j  j�  j  j�  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j�  u}�(j  �id:1978�j  j�  j  j  u}�(j  �id:kwso�j  j�  j  j  u}�(j  �id:caad�j  j�  j  j  u}�(j  �id:857�j  j�  j  j�  u}�(j  �id:caad�j  j�  j  j�  ue]�(}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j�  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j  u}�(j  �id:2177�j  j�  j  j�  u}�(j  �id:2177�j  j�  j  j  u}�(j  �id:brku�j  j�  j  j  u}�(j  �id:duto�j  j�  j  j  u}�(j  �id:1099�j  j�  j  j�  u}�(j  �id:duto�j  j�  j  j�  ue]�(}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j�  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j  u}�(j  �id:1966�j  j�  j  j�  u}�(j  �id:1966�j  j�  j  j  u}�(j  �id:brku�j  j�  j  j  u}�(j  �id:duto�j  j�  j  j  u}�(j  �id:1099�j  j�  j  j�  u}�(j  �id:duto�j  j�  j  j�  ue]�(}�(j  �id:2171�j  j�  j  j�  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j�  u}�(j  �id:2171�j  j�  j  j  u}�(j  �id:adri�j  j�  j  j  u}�(j  �id:sha�j  j�  j  j  u}�(j  �id:1902�j  j�  j  j�  u}�(j  �id:sha�j  j�  j  j�  ue]�(}�(j  �id:2171�j  j�  j  j�  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j�  u}�(j  �id:2171�j  j�  j  j  u}�(j  �id:adri�j  j�  j  j  u}�(j  �id:sha�j  j�  j  j  u}�(j  �id:214�j  j�  j  j�  u}�(j  �id:sha�j  j�  j  j�  ue]�(}�(j  �id:2171�j  j�  j  j�  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j�  u}�(j  �id:2171�j  j�  j  j  u}�(j  �id:adri�j  j�  j  j  u}�(j  �id:sha�j  j�  j  j  u}�(j  �id:1092�j  j�  j  j�  u}�(j  �id:sha�j  j�  j  j�  ue]�(}�(j  �id:2171�j  j�  j  j�  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j�  u}�(j  �id:2171�j  j�  j  j  u}�(j  �id:adri�j  j�  j  j  u}�(j  �id:sha�j  j�  j  j  u}�(j  �id:743�j  j�  j  j�  u}�(j  �id:sha�j  j�  j  j�  ue]�(}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j�  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j  u}�(j  �id:2045�j  j�  j  j�  u}�(j  �id:2045�j  j�  j  j  u}�(j  �id:duto�j  j�  j  j  u}�(j  �id:duto�j  j�  j  j  u}�(j  �id:duto�j  j�  j  j�  ue]�(}�(j  �id:2289�j  j�  j  j�  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j�  u}�(j  �id:2289�j  j�  j  j  u}�(j  �id:hube�j  j�  j  j  u}�(j  �id:aido�j  j�  j  j  u}�(j  �id:aido�j  j�  j  j�  ue]�(}�(j  �id:2169�j  j�  j  j�  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j�  u}�(j  �id:2169�j  j�  j  j  u}�(j  �id:aido�j  j�  j  j  u}�(j  �id:aido�j  j�  j  j  u}�(j  �id:aido�j  j�  j  j�  ue]�(}�(j  �id:2289�j  j�  j  j�  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j�  j  j�  u}�(j  �id:2289�j  j�  j  j  u}�(j  �id:hube�j  j�  j  j  u}�(j  �id:aido�j  j�  j  j  u}�(j  �id:aido�j  j�  j  j�  ue�!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:2289��!lei:test_aladdin_executing_entity��id:1978��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:2171��id:2171��id:2171��id:2171��!lei:test_aladdin_executing_entity��id:2289��id:2169��id:2289��!lei:test_aladdin_executing_entity��id:1978��!lei:test_aladdin_executing_entity��id:2177��id:1966��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:2045��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�ȟid:857��id:857��id:857��id:1099��id:1099��id:1902��id:214��id:1092��id:743�h�h�h�ȟ2289��test_aladdin_executing_entity��1978��test_aladdin_executing_entity��test_aladdin_executing_entity��2171��2171��2171��2171��test_aladdin_executing_entity��2289��2169��2289��test_aladdin_executing_entity��1978��test_aladdin_executing_entity��2177��1966��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��2045��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��857��857��857��1099��1099��1902��214��1092��743�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�ȟtest_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity�]�(j  j  j   j"  j$  j&  j(  j*  j,  j.  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j1  j2  j4  j6  j8  j:  j<  j>  j@  jB  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jE  jF  jH  jJ  jL  jN  jP  jR  jT  jV  j�  j�  j  j  j  j  j	  j  e]�(j  j  j  j  j  j  j  j  e]�(j  j!  j#  j%  j'  j)  j+  j-  e]�(jY  jZ  j\  j^  j`  jb  jd  jf  jh  jj  j0  j2  j4  j6  j8  j:  j<  j>  e]�(jm  jn  jp  jr  jt  jv  jx  jz  j|  j~  jA  jC  jE  jG  jI  jK  jM  jO  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  jR  jT  jV  jX  jZ  j\  j^  j`  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  jc  je  jg  ji  jk  jm  jo  jq  e]�(jt  jv  jx  jz  j|  j~  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e�Order�j  j  j  j  j  j  j  j  j  j  j  j  �
OrderState�j  j  j  j  j  j  j  j  j  j  j  j  �1475317��1475348��1500406��1552361��1727766��1676248�j#  j#  j#  �1551596��1551796��1552055��1552061�j  j  j   j!  j"  j#  j#  j#  j#  j$  j%  j&  j'  �4633��4638��4637��23058��23059��1790��12684��878��3657�h�ȟ1600755�h�j(  j)  j*  j+  j,  j-  j.  j/  j0  h�h�j1  h�G@Yp��
=qG@*�z�G�G@��Q�G        G        G@
!)�ZEG@
!)�ZEG@
!)�ZEG@
!)�ZEG        G@���K�G@\m     G@������G@�     G@�@    G@�@    GA�I�   GA�C�    GA>��    GAA��    G@�j     GA
�    GA�ׄ    GAi��    G@U�     GA6�`    j(  j)  j*  j+  j,  j-  j.  j/  j0  h�h�j1  ȟ)AladdinTCA.DERIV.Transaction.20240825.csv�j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  �2024-07-01T19:43:59.623000Z��2024-07-01T19:43:59.553000Z��2024-07-01T19:43:59.646000Z��2024-08-28T16:09:51.320000Z��2024-08-28T16:09:51.356000Z��2024-08-28T12:54:14.966000Z��2024-08-28T12:54:14.966000Z��2024-08-28T12:54:14.966000Z��2024-08-28T12:54:14.966000Z��2024-08-28T14:17:25.380000Z��2024-08-28T14:34:48.160000Z��2024-08-26T16:43:53.573000Z��2024-08-28T15:09:38.450000Z��2024-07-01T20:03:36.366000Z��2024-07-01T20:48:11.723000Z��2024-07-01T20:48:11.723000Z��2024-08-28T16:12:06.730000Z��2024-08-28T16:12:06.710000Z��2024-08-28T13:30:56.193000Z��2024-08-28T13:30:56.193000Z��2024-08-28T13:30:56.193000Z��2024-08-28T13:30:56.193000Z��2024-09-05T19:46:19.246000Z��2024-09-05T17:37:19.246000Z��2024-09-05T09:06:19.246000Z��2024-09-05T19:36:19.246000Z�j3  j4  j5  j6  j7  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  �2024-07-01T20:11:26.066000Z��2024-07-01T20:48:44.096000Z��2024-07-01T20:48:44.096000Z��2024-08-28T16:16:05.980000Z��2024-08-28T16:17:06.050000Z��2024-08-28T14:13:54.556000Z��2024-08-28T14:13:54.556000Z��2024-08-28T14:13:54.556000Z��2024-08-28T14:13:54.556000Z�jI  jJ  �2024-08-26T16:54:37.000000Z�jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jI  jJ  jV  jL  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j  j   j!  j"  j#  j#  j#  j#  j$  j%  j&  j'  G@Yp��
=qG@*�z�G�G@��Q�G        G        G@
!)�ZEG@
!)�ZEG@
!)�ZEG@
!)�ZEG        G@���K�G@\m     G@������j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �MONE�jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  G@�     G@�@    G@�@    GA�I�   GA�C�    GA>��    GAA��    G@�j     GA
�    GA�ׄ    GAi��    G@U�     GA6�`    h�h�h�h�h�h�h�h�h�h�h�h�ȟUNIT�jX  jX  jX  jX  jX  jX  jX  jX  jW  jW  jX  jW  �
Allocation�jY  jY  jY  jY  jY  jY  jY  jY  �Market Side�jZ  jZ  jZ  j  j  j  j  j  j  j  j  j  j  j  j  j  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jI  jJ  jV  jL  j�  j�  j�  h�h�j�  j�  j�  j�  h�h�j�  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�ḧ������������j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �Options�j[  j[  �Swaps�j\  j[  j[  j[  j[  h�ȟFutures�ȟCall options�j^  j^  h�ȟPut options�j_  j_  j_  h�ȟFinancial Futures�ȟDERIV�ja  ja  ja  ja  ja  ja  ja  ja  ja  ja  ja  ja  et�bhhK ��h��R�(KKK
��h!�]�(�Derivatives�jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hTat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hkat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hwat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hxat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h}at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hdat�bh�Nu��R�h
h}�(hhhK ��h��R�(KKK��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h4h5h6h7h8h9h:h;h<h=h>h?h@hBhChDhEhGhHhKhMhNhPhQhRhVhWhXhYhZh[h]h^h`hahbhchehfhghhhihjhlhmhnhohphqhrhshthuhvhyhzh{h|h~hh�h�et�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���K
KK��R�u}�(j/  h�j0  j3  KKK��R�u}�(j/  h�j0  j3  KKK��R�u}�(j/  h�j0  j3  K!K"K��R�u}�(j/  h�j0  j3  K$K%K��R�u}�(j/  h�j0  j3  K%K&K��R�u}�(j/  j  j0  j3  K'K(K��R�u}�(j/  j'  j0  j3  K*K+K��R�u}�(j/  j=  j0  j3  K.K/K��R�u}�(j/  jS  j0  j3  K/K0K��R�u}�(j/  ji  j0  j3  K0K1K��R�u}�(j/  j  j0  j3  K7K8K��R�u}�(j/  j�  j0  j3  KFKGK��R�u}�(j/  j�  j0  j3  KRKSK��R�u}�(j/  j�  j0  j3  KSKTK��R�u}�(j/  j�  j0  j3  KXKYK��R�u}�(j/  j�  j0  j3  K]K^K��R�u}�(j/  j�  j0  j3  K:K;K��R�u}�(j/  j�  j0  j3  K?K@K��R�u}�(j/  j�  j0  h�(�X                                                                      	       
                                                                                                                                             "       #       &       (       )       +       ,       -       1       2       3       4       5       6       8       9       ;       <       =       >       @       A       B       C       D       E       G       H       I       J       K       L       M       N       O       P       Q       T       U       V       W       Y       Z       [       \       �h�i8�����R�(Kh�NNNJ����J����K t�bKK��h�t�R�u}�(j/  je  j0  j3  K^K_K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.