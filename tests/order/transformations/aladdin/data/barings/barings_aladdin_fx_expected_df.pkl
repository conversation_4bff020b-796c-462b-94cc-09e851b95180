��      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK_��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��executionDetails.limitPrice��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��executionDetails.stopPrice��&executionDetails.outgoingOrderAddlInfo�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	_order.id��_orderState.id�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��asset_class_attribute��currency_attribute��notional_currency_2_attribute��option_type_attribute��#instrument_classification_attribute��underlying_index_term_attribute��expiry_date_attribute��option_strike_price_attribute��underlying_symbol_attribute��venue_attribute��isin_attribute��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��__fallback_buyer__��__fallback_seller__��__fallback_counterparty__��__fallback_client__��__fallback_buyer_dec_maker__��__fallback_seller_dec_maker__��__fallback_inv_dec_in_firm__��__fallback_trader__��__fallback_exec_within_firm__��__fallback_executing_entity__��marketIdentifiers��_order.__meta_model__��_orderState.__meta_model__��(orderIdentifiers.initialOrderDesignation��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��!orderIdentifiers.transactionRefNo��.orderIdentifiers.tradingVenueTransactionIdCode�� priceFormingData.initialQuantity��priceFormingData.price��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��	sourceKey��sourceIndex��timestamps.orderReceived��timestamps.orderSubmitted�� timestamps.internalOrderReceived��!timestamps.internalOrderSubmitted��timestamps.orderStatusUpdated��&_orderState.timestamps.tradingDateTime��transactionDetails.basketId��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��'_orderState.transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��._orderState.transactionDetails.tradingDateTime��__ric__�� __instrument_unique_identifier__��__asset_class__��__pricing_reference_lxid__��__pricing_reference_redcode__��__newo_in_file__��__instrument_full_name__��__instrument_classification__��__cfi_category__��
__cfi_group__��__file_type_asset_class__��!__instrument_created_through_fb__��__best_ex_asset_class_main__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(�h                                                                      	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       7       8       9       :       ;       <       =       >       ?       @       A       B       C       D       E       F       G       H       I       J       K       L       �h�i8�����R�(K�<�NNNJ����J����K t�bKM���C�t�R�h��__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KKM��h!�]�(�	C|1676747��	C|1676647��	C|1776693��	C|1753291��	C|1776209��	C|1776309��	C|1776409��	C|1776946��	C|1777046��	C|1802404��	C|1802304��	C|1802472��	C|1802633��	C|1878056��	C|1876948��	C|1876848��	C|1932006��	C|1927299��	C|1927399��	C|1927499��	C|1927599��	C|1927699��	C|1927799��	C|1927899��	C|1927999��	C|1928099��	C|1928199��	C|1928299��	C|1931799��	C|1931899��	C|1951934��	C|1951834��	C|1952634��	C|1952534��	C|2001271��	C|2001171��	C|2001551��	C|2001451��	C|2001728��	C|2077131��	C|2077231��	C|2129281��	C|2129381��	C|2458610��	C|2458710��	M|3800747��	M|3800847��	M|4500693��	M|3051491��
M|1775609|OTC��	M|4250646��	M|4250746��	M|3850504��	M|3850604��
M|1801972|OTC��	M|4151333��	M|4350556��	M|4700948��	M|4700848��	M|4850606��	M|4400799��
M|1926699|OTC��	M|4951034��	M|4951134��	M|4951434��	M|4951334��	M|4300671��	M|4300571��	M|5100951��	M|5100851��	M|5000928��	M|4450731��	M|4450831��	M|4351981��	M|4351881��	M|4101710��	M|4101610�et�b�_dtype�h��StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KKM��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKM��h!�]�(�pandas._libs.missing��NA���j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKM��h!�]�(�id:hube��id:hube��id:suto��id:suto��id:duto��id:duto��id:duto��id:suto��id:suto��id:tama��id:tama��id:duto��id:suto��id:hori��id:hube��id:hube��id:liso��id:evva��id:evva��id:evva��id:evva��id:evva��id:evva��id:evva��id:evva��id:evva��id:evva��id:evva��id:duto��id:duto��id:suto��id:suto��id:suto��id:suto��id:suto��id:suto��id:tama��id:tama��id:suto��id:hajo��id:hajo��id:suto��id:suto��id:hube��id:hube��id:hube��id:hube��id:suto��id:suto��id:duto��id:suto��id:suto��id:tama��id:tama��id:duto��id:suto��id:hori��id:hube��id:hube��id:liso��id:evva��id:duto��id:suto��id:suto��id:suto��id:suto��id:suto��id:suto��id:tama��id:tama��id:suto��	id:merged��	id:merged��id:suto��id:suto��id:hube��id:hube�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKM��h!�]�(�id:aido��id:aido��id:sha��id:mina��id:post��id:post��id:post��id:sha��id:sha��id:post��id:post��id:post��id:sha��id:post��id:post��id:post��id:chib��id:mina��id:mina��id:mina��id:mina��id:mina��id:mina��id:mina��id:mina��id:mina��id:mina��id:mina��id:aido��id:aido��id:sha��id:sha��id:mina��id:mina��id:sha��id:sha��id:post��id:post��id:sha��id:sha��id:sha��id:sha��id:sha��id:post��id:post��id:aido��id:aido��id:sha��id:mina��id:post��id:sha��id:sha��id:post��id:post��id:post��id:sha��id:post��id:post��id:post��id:chib��id:mina��id:aido��id:sha��id:sha��id:mina��id:mina��id:sha��id:sha��id:post��id:post��id:sha��id:sha��id:sha��id:sha��id:sha��id:post��id:post�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKM��h!�]�(�id:aido��id:aido��id:sha��id:mina��id:post��id:post��id:post��id:sha��id:sha��id:post��id:post��id:post��id:sha��id:post��id:post��id:post��id:chib��id:mina��id:mina��id:mina��id:mina��id:mina��id:mina��id:mina��id:mina��id:mina��id:mina��id:mina��id:aido��id:aido��id:sha��id:sha��id:mina��id:mina��id:sha��id:sha��id:post��id:post��id:sha��id:sha��id:sha��id:sha��id:sha��id:post��id:post��id:aido��id:aido��id:sha��id:mina��id:post��id:sha��id:sha��id:post��id:post��id:post��id:sha��id:post��id:post��id:post��id:chib��id:mina��id:aido��id:sha��id:sha��id:mina��id:mina��id:sha��id:sha��id:post��id:post��id:sha��id:sha��id:sha��id:sha��id:sha��id:post��id:post�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKM��h!�]�(�hube��hube��suto��suto��duto��duto��duto��suto��suto��tama��tama��duto��suto��hori��hube��hube��liso��evva��evva��evva��evva��evva��evva��evva��evva��evva��evva��evva��duto��duto��suto��suto��suto��suto��suto��suto��tama��tama��suto��hajo��hajo��suto��suto��hube��hube��hube��hube��suto��suto��duto��suto��suto��tama��tama��duto��suto��hori��hube��hube��liso��evva��duto��suto��suto��suto��suto��suto��suto��tama��tama��suto��merged��merged��suto��suto��hube��hube�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKM��h!�]�(�aido��aido��sha��mina��post��post��post��sha��sha��post��post��post��sha��post��post��post��chib��mina��mina��mina��mina��mina��mina��mina��mina��mina��mina��mina��aido��aido��sha��sha��mina��mina��sha��sha��post��post��sha��sha��sha��sha��sha��post��post��aido��aido��sha��mina��post��sha��sha��post��post��post��sha��post��post��post��chib��mina��aido��sha��sha��mina��mina��sha��sha��post��post��sha��sha��sha��sha��sha��post��post�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKM��h!�]�(�aido��aido��sha��mina��post��post��post��sha��sha��post��post��post��sha��post��post��post��chib��mina��mina��mina��mina��mina��mina��mina��mina��mina��mina��mina��aido��aido��sha��sha��mina��mina��sha��sha��post��post��sha��sha��sha��sha��sha��post��post��aido��aido��sha��mina��post��sha��sha��post��post��post��sha��post��post��post��chib��mina��aido��sha��sha��mina��mina��sha��sha��post��post��sha��sha��sha��sha��sha��post��post�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKM��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKM��h!�]�(�MMLDRV�j#  �GMCSF4��ABNEMB2�j#  j  j  �RWEHYB1�j&  �WINDMIL�j'  j#  �JPCLO2��JEREMY�j#  j#  �HBAF��AGIFVBE��SOKAEMD��EMDBTR��BCEMBTR��BCEMLDF��40TREMF��MMIFEM��EMRUSLC��BERWLLC��RPEMLDF��PCAEMB�j#  �GALDRV��ZSDLF4�j7  j%  j%  j7  j7  j'  j'  �ZEPL4��BCMHDGE�j9  �NICHYB�j:  j6  j6  j#  j#  j$  j%  j#  j&  j&  j'  j'  j#  j(  j)  j#  j#  j*  j2  j#  j7  j7  j%  j%  j7  j7  j'  j'  j8  j9  j9  j:  j:  j6  j6  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKM��h!�]�(�AUD/USD FWRD��AUD/USD SPOT��USD/GBP FWRD��USD/EUR FWRD��
EUR/USD CSWAP�j  j  �USD/EUR SPOT��USD/EUR FWRD��EUR/JPY SPOT��EUR/JPY FWRD��
GBP/USD CSWAP��USD/JPY FWRD��AUD/USD SPOT��AUD/USD FWRD��AUD/USD SPOT��AUD/GBP SPOT��USD/NGN FWRD��USD/NGN FWRD��USD/NGN FWRD��USD/NGN FWRD��USD/NGN FWRD��USD/NGN FWRD��USD/NGN FWRD��USD/NGN FWRD��USD/NGN FWRD��USD/NGN FWRD��USD/NGN FWRD��
CAD/USD CSWAP��
CAD/USD CSWAP��SEK/USD SPOT��SEK/USD FWRD��EUR/USD SPOT��EUR/USD FWRD��AUD/USD SPOT��AUD/USD FWRD��SEK/JPY SPOT��SEK/JPY FWRD��CHF/EUR FWRD��AUD/USD SPOT��AUD/USD SPOT��EUR/USD SPOT��EUR/USD FWRD��AUD/USD FWRD��AUD/USD SPOT��AUD/USD SPOT��AUD/USD FWRD��USD/GBP FWRD��USD/EUR FWRD��
EUR/USD CSWAP��USD/EUR SPOT��USD/EUR FWRD��EUR/JPY FWRD��EUR/JPY SPOT��
GBP/USD CSWAP��USD/JPY FWRD��AUD/USD SPOT��AUD/USD SPOT��AUD/USD FWRD��AUD/GBP SPOT��USD/NGN FWRD��
CAD/USD CSWAP��SEK/USD FWRD��SEK/USD SPOT��EUR/USD FWRD��EUR/USD SPOT��AUD/USD FWRD��AUD/USD SPOT��SEK/JPY FWRD��SEK/JPY SPOT��CHF/EUR FWRD��AUD/USD SPOT��AUD/USD SPOT��EUR/USD SPOT��EUR/USD FWRD��AUD/USD FWRD��AUD/USD SPOT�et�bh�h�)��ubh�(�M       �h�b1�����R�(Kh"NNNJ����J����K t�bKKM��h�t�R�h�(�h          �ޜA    �ޜA   ���@A    ��,A    �cA    �cA    �cA    ��A    ��A     �A     �A    �cA  �Y�[A)\��|4A   `���A   `���A    8�\A   �
�A   �
�A   �
�A   �
�A   �
�A   �
�A   �
�A   �
�A   �
�A   �
�A   �
�A   @_�A   @_�A    H�LA    H�LA
ף�k�CA
ף�k�CA    ;A    ;A    2PdA    2PdA��Qh�A    ��A    ��A�G�
l7A�G�
l7A    �-�A    �-�A    �ޜA    �ޜA   ���@A    ��,A    �cA    ��A    ��A     �A     �A    �cA  �Y�[A)\��|4A   `���A   `���A    8�\A   �
�A   @_�A    H�LA    H�LA
ף�k�CA
ף�k�CA    ;A    ;A    2PdA    2PdA��Qh�A    �sA    `�vA�G�
l7A�G�
l7A    �-�A    �-�A�h�f8�����R�(Kh�NNNJ����J����K t�bKKM��h�t�R�h�(�h                                                                      	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       7       8       9       :       ;       <       =       >       ?       @       A       B       C       D       E       F       G       H       I       J       K       L       �h�KKM��h�t�R�hhK ��h��R�(KKOKM��h!�]�(�2��1�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �Aladdin�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-22��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��
2024-10-23��SELL��BUYI�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �NEWO�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �FILL�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �PARF�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �Market�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �KassetId: BGS2U07N9
Transaction Type: SELL
genComments: 
tradePurpose: MMFCP��JassetId: BGS2U07M1
Transaction Type: BUY
genComments: 
tradePurpose: MMFCP��7assetId: BGS2U1319
Transaction Type: SELL
genComments: ��6assetId: BGS2U0BD6
Transaction Type: BUY
genComments: ��JassetId: BGS2U1DD2
Transaction Type: BUY
genComments: 
tradePurpose: MMFCP��#Transaction Type: BUY
genComments: ��#Transaction Type: BUY
genComments: ��7assetId: BGS2U01B1
Transaction Type: SELL
genComments: ��6assetId: BGS2U01A3
Transaction Type: BUY
genComments: ��6assetId: BGS2TZMU1
Transaction Type: BUY
genComments: ��7assetId: BGS2TZMT4
Transaction Type: SELL
genComments: ��JassetId: BGS2U0BQ7
Transaction Type: BUY
genComments: 
tradePurpose: MMFCP��7assetId: BGS2U1335
Transaction Type: SELL
genComments: ��7assetId: BGS2U0121
Transaction Type: SELL
genComments: ��KassetId: BGS2U05G6
Transaction Type: SELL
genComments: 
tradePurpose: MMFCP��JassetId: BGS2U05D3
Transaction Type: BUY
genComments: 
tradePurpose: MMFCP��7assetId: BGS2TYZY2
Transaction Type: SELL
genComments: ��LassetId: BGS2U0VX0
Transaction Type: SELL
genComments: 
tradePurpose: PER_PM��LassetId: BGS2U0VX0
Transaction Type: SELL
genComments: 
tradePurpose: PER_PM��LassetId: BGS2U0VX0
Transaction Type: SELL
genComments: 
tradePurpose: PER_PM��LassetId: BGS2U0VX0
Transaction Type: SELL
genComments: 
tradePurpose: PER_PM��LassetId: BGS2U0VX0
Transaction Type: SELL
genComments: 
tradePurpose: PER_PM��LassetId: BGS2U0VX0
Transaction Type: SELL
genComments: 
tradePurpose: PER_PM��LassetId: BGS2U0VX0
Transaction Type: SELL
genComments: 
tradePurpose: PER_PM��LassetId: BGS2U0VX0
Transaction Type: SELL
genComments: 
tradePurpose: PER_PM��LassetId: BGS2U0VX0
Transaction Type: SELL
genComments: 
tradePurpose: PER_PM��LassetId: BGS2U0VX0
Transaction Type: SELL
genComments: 
tradePurpose: PER_PM��LassetId: BGS2U0VX0
Transaction Type: SELL
genComments: 
tradePurpose: PER_PM��JassetId: BGS2U2465
Transaction Type: BUY
genComments: 
tradePurpose: MMFCP��JassetId: BGS2U2465
Transaction Type: BUY
genComments: 
tradePurpose: MMFCP��7assetId: BGS2U0832
Transaction Type: SELL
genComments: ��6assetId: BGS2U0840
Transaction Type: BUY
genComments: ��7assetId: BGS2U13D3
Transaction Type: SELL
genComments: ��6assetId: BGS2U13C5
Transaction Type: BUY
genComments: ��7assetId: BGS2U05X9
Transaction Type: SELL
genComments: ��6assetId: BGS2U05Y7
Transaction Type: BUY
genComments: ��7assetId: BGS2TZMY3
Transaction Type: SELL
genComments: ��6assetId: BGS2TZMX5
Transaction Type: BUY
genComments: ��7assetId: BGS2U0BB0
Transaction Type: SELL
genComments: ��7assetId: BGS2U16U2
Transaction Type: SELL
genComments: ��7assetId: BGS2U1756
Transaction Type: SELL
genComments: ��6assetId: BGS2U0881
Transaction Type: BUY
genComments: ��7assetId: BGS2U0873
Transaction Type: SELL
genComments: ��KassetId: BGS2U05F8
Transaction Type: SELL
genComments: 
tradePurpose: MMFCP��JassetId: BGS2U05E1
Transaction Type: BUY
genComments: 
tradePurpose: MMFCP��JassetId: BGS2U07M1
Transaction Type: BUY
genComments: 
tradePurpose: MMFCP��KassetId: BGS2U07N9
Transaction Type: SELL
genComments: 
tradePurpose: MMFCP��7assetId: BGS2U1319
Transaction Type: SELL
genComments: ��6assetId: BGS2U0BD6
Transaction Type: BUY
genComments: ��JassetId: BGS2U1DD2
Transaction Type: BUY
genComments: 
tradePurpose: MMFCP��7assetId: BGS2U01B1
Transaction Type: SELL
genComments: ��6assetId: BGS2U01A3
Transaction Type: BUY
genComments: ��7assetId: BGS2TZMT4
Transaction Type: SELL
genComments: ��6assetId: BGS2TZMU1
Transaction Type: BUY
genComments: ��JassetId: BGS2U0BQ7
Transaction Type: BUY
genComments: 
tradePurpose: MMFCP��7assetId: BGS2U1335
Transaction Type: SELL
genComments: ��7assetId: BGS2U0121
Transaction Type: SELL
genComments: ��JassetId: BGS2U05D3
Transaction Type: BUY
genComments: 
tradePurpose: MMFCP��KassetId: BGS2U05G6
Transaction Type: SELL
genComments: 
tradePurpose: MMFCP��7assetId: BGS2TYZY2
Transaction Type: SELL
genComments: ��LassetId: BGS2U0VX0
Transaction Type: SELL
genComments: 
tradePurpose: PER_PM��JassetId: BGS2U2465
Transaction Type: BUY
genComments: 
tradePurpose: MMFCP��6assetId: BGS2U0840
Transaction Type: BUY
genComments: ��7assetId: BGS2U0832
Transaction Type: SELL
genComments: ��6assetId: BGS2U13C5
Transaction Type: BUY
genComments: ��7assetId: BGS2U13D3
Transaction Type: SELL
genComments: ��6assetId: BGS2U05Y7
Transaction Type: BUY
genComments: ��7assetId: BGS2U05X9
Transaction Type: SELL
genComments: ��6assetId: BGS2TZMX5
Transaction Type: BUY
genComments: ��7assetId: BGS2TZMY3
Transaction Type: SELL
genComments: ��7assetId: BGS2U0BB0
Transaction Type: SELL
genComments: ��7assetId: BGS2U16U2
Transaction Type: SELL
genComments: ��7assetId: BGS2U1756
Transaction Type: SELL
genComments: ��6assetId: BGS2U0881
Transaction Type: BUY
genComments: ��7assetId: BGS2U0873
Transaction Type: SELL
genComments: ��KassetId: BGS2U05F8
Transaction Type: SELL
genComments: 
tradePurpose: MMFCP��JassetId: BGS2U05E1
Transaction Type: BUY
genComments: 
tradePurpose: MMFCP��AOTC�jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  ]��DAVY�a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  a]�jO  ae(]�jO  a�RTSP�j�  j�  �XOFF�j�  j�  j�  j�  j�  �TRAL�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  ]�(}�(�labelId��XXXXAUDUSDFXFWD2025-01-28��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�u}�(j�  �XXXXUSDAUDFXFWD2025-01-28�j�  j�  j�  j�  ue]�}�(j�  �XXXXAUDUSDFXSPOT�j�  j�  j�  j�  ua]�(}�(j�  �XXXXUSDGBPFXFWD2025-01-21�j�  j�  j�  j�  u}�(j�  �XXXXGBPUSDFXFWD2025-01-21�j�  j�  j�  j�  ue]�(}�(j�  �XXXXUSDEURFXFWD2025-01-08�j�  j�  j�  j�  u}�(j�  �XXXXEURUSDFXFWD2025-01-08�j�  j�  j�  j�  uej  j  j  ]�}�(j�  �XXXXUSDEURFXSPOT�j�  j�  j�  j�  ua]�(}�(j�  �XXXXUSDEURFXFWD2025-01-21�j�  j�  j�  j�  u}�(j�  �XXXXEURUSDFXFWD2025-01-21�j�  j�  j�  j�  ue]�}�(j�  �XXXXEURJPYFXSPOT�j�  j�  j�  j�  ua]�(}�(j�  �XXXXEURJPYFXFWD2024-12-17�j�  j�  j�  j�  u}�(j�  �XXXXJPYEURFXFWD2024-12-17�j�  j�  j�  j�  uej  ]�(}�(j�  �XXXXUSDJPYFXFWD2025-01-09�j�  j�  j�  j�  u}�(j�  �XXXXJPYUSDFXFWD2025-01-09�j�  j�  j�  j�  ue]�}�(j�  �XXXXAUDUSDFXSPOT�j�  j�  j�  j�  ua]�(}�(j�  �XXXXAUDUSDFXFWD2025-01-28�j�  j�  j�  j�  u}�(j�  �XXXXUSDAUDFXFWD2025-01-28�j�  j�  j�  j�  ue]�}�(j�  �XXXXAUDUSDFXSPOT�j�  j�  j�  j�  ua]�}�(j�  �XXXXAUDGBPFXSPOT�j�  j�  j�  j�  ua]�(}�(j�  �XXXXUSDNGNFXFWD2025-04-25�j�  j�  j�  j�  u}�(j�  �XXXXNGNUSDFXFWD2025-04-25�j�  j�  j�  j�  ue]�(}�(j�  �XXXXUSDNGNFXFWD2025-04-25�j�  j�  j�  j�  u}�(j�  �XXXXNGNUSDFXFWD2025-04-25�j�  j�  j�  j�  ue]�(}�(j�  �XXXXUSDNGNFXFWD2025-04-25�j�  j�  j�  j�  u}�(j�  �XXXXNGNUSDFXFWD2025-04-25�j�  j�  j�  j�  ue]�(}�(j�  �XXXXUSDNGNFXFWD2025-04-25�j�  j�  j�  j�  u}�(j�  �XXXXNGNUSDFXFWD2025-04-25�j�  j�  j�  j�  ue]�(}�(j�  �XXXXUSDNGNFXFWD2025-04-25�j�  j�  j�  j�  u}�(j�  �XXXXNGNUSDFXFWD2025-04-25�j�  j�  j�  j�  ue]�(}�(j�  �XXXXUSDNGNFXFWD2025-04-25�j�  j�  j�  j�  u}�(j�  �XXXXNGNUSDFXFWD2025-04-25�j�  j�  j�  j�  ue]�(}�(j�  �XXXXUSDNGNFXFWD2025-04-25�j�  j�  j�  j�  u}�(j�  �XXXXNGNUSDFXFWD2025-04-25�j�  j�  j�  j�  ue]�(}�(j�  �XXXXUSDNGNFXFWD2025-04-25�j�  j�  j�  j�  u}�(j�  �XXXXNGNUSDFXFWD2025-04-25�j�  j�  j�  j�  ue]�(}�(j�  �XXXXUSDNGNFXFWD2025-04-25�j�  j�  j�  j�  u}�(j�  �XXXXNGNUSDFXFWD2025-04-25�j�  j�  j�  j�  ue]�(}�(j�  �XXXXUSDNGNFXFWD2025-04-25�j�  j�  j�  j�  u}�(j�  �XXXXNGNUSDFXFWD2025-04-25�j�  j�  j�  j�  ue]�(}�(j�  �XXXXUSDNGNFXFWD2025-04-25�j�  j�  j�  j�  u}�(j�  �XXXXNGNUSDFXFWD2025-04-25�j�  j�  j�  j�  uej  j  ]�}�(j�  �XXXXSEKUSDFXSPOT�j�  j�  j�  j�  ua]�(}�(j�  �XXXXSEKUSDFXFWD2024-12-30�j�  j�  j�  j�  u}�(j�  �XXXXUSDSEKFXFWD2024-12-30�j�  j�  j�  j�  ue]�}�(j�  �XXXXEURUSDFXSPOT�j�  j�  j�  j�  ua]�(}�(j�  �XXXXEURUSDFXFWD2025-01-08�j�  j�  j�  j�  u}�(j�  �XXXXUSDEURFXFWD2025-01-08�j�  j�  j�  j�  ue]�}�(j�  �XXXXAUDUSDFXSPOT�j�  j�  j�  j�  ua]�(}�(j�  �XXXXAUDUSDFXFWD2024-12-30�j�  j�  j�  j�  u}�(j�  �XXXXUSDAUDFXFWD2024-12-30�j�  j�  j�  j�  ue]�}�(j�  �XXXXSEKJPYFXSPOT�j�  j�  j�  j�  ua]�(}�(j�  �XXXXSEKJPYFXFWD2024-11-14�j�  j�  j�  j�  u}�(j�  �XXXXJPYSEKFXFWD2024-11-14�j�  j�  j�  j�  ue]�(}�(j�  �XXXXCHFEURFXFWD2024-12-30�j�  j�  j�  j�  u}�(j�  �XXXXEURCHFFXFWD2024-12-30�j�  j�  j�  j�  ue]�}�(j�  �XXXXAUDUSDFXSPOT�j�  j�  j�  j�  ua]�}�(j�  �XXXXAUDUSDFXSPOT�j�  j�  j�  j�  ua]�}�(j�  �XXXXEURUSDFXSPOT�j�  j�  j�  j�  ua]�(}�(j�  �XXXXEURUSDFXFWD2024-11-29�j�  j�  j�  j�  u}�(j�  �XXXXUSDEURFXFWD2024-11-29�j�  j�  j�  j�  ue]�(}�(j�  �XXXXAUDUSDFXFWD2025-01-28�j�  j�  j�  j�  u}�(j�  �XXXXUSDAUDFXFWD2025-01-28�j�  j�  j�  j�  ue]�}�(j�  �XXXXAUDUSDFXSPOT�j�  j�  j�  j�  ua]�}�(j�  �XXXXAUDUSDFXSPOT�j�  j�  j�  j�  ua]�(}�(j�  �XXXXAUDUSDFXFWD2025-01-28�j�  j�  j�  j�  u}�(j�  �XXXXUSDAUDFXFWD2025-01-28�j�  j�  j�  j�  ue]�(}�(j�  �XXXXUSDGBPFXFWD2025-01-21�j�  j�  j�  j�  u}�(j�  �XXXXGBPUSDFXFWD2025-01-21�j�  j�  j�  j�  ue]�(}�(j�  �XXXXUSDEURFXFWD2025-01-08�j�  j�  j�  j�  u}�(j�  �XXXXEURUSDFXFWD2025-01-08�j�  j�  j�  j�  uej  ]�}�(j�  �XXXXUSDEURFXSPOT�j�  j�  j�  j�  ua]�(}�(j�  �XXXXUSDEURFXFWD2025-01-21�j�  j�  j�  j�  u}�(j�  �XXXXEURUSDFXFWD2025-01-21�j�  j�  j�  j�  ue]�(}�(j�  �XXXXEURJPYFXFWD2024-12-17�j�  j�  j�  j�  u}�(j�  �XXXXJPYEURFXFWD2024-12-17�j�  j�  j�  j�  ue]�}�(j�  �XXXXEURJPYFXSPOT�j�  j�  j�  j�  uaj  ]�(}�(j�  �XXXXUSDJPYFXFWD2025-01-09�j�  j�  j�  j�  u}�(j�  �XXXXJPYUSDFXFWD2025-01-09�j�  j�  j�  j�  ue]�}�(j�  �XXXXAUDUSDFXSPOT�j�  j�  j�  j�  ua]�}�(j�  �XXXXAUDUSDFXSPOT�j�  j�  j�  j�  ua]�(}�(j�  �XXXXAUDUSDFXFWD2025-01-28�j�  j�  j�  j�  u}�(j�  �XXXXUSDAUDFXFWD2025-01-28�j�  j�  j�  j�  ue]�}�(j�  �XXXXAUDGBPFXSPOT�j�  j�  j�  j�  ua]�(}�(j�  �XXXXUSDNGNFXFWD2025-04-25�j�  j�  j�  j�  u}�(j�  �XXXXNGNUSDFXFWD2025-04-25�j�  j�  j�  j�  uej  ]�(}�(j�  �XXXXSEKUSDFXFWD2024-12-30�j�  j�  j�  j�  u}�(j�  �XXXXUSDSEKFXFWD2024-12-30�j�  j�  j�  j�  ue]�}�(j�  �XXXXSEKUSDFXSPOT�j�  j�  j�  j�  ua]�(}�(j�  �XXXXEURUSDFXFWD2025-01-08�j�  j�  j�  j�  u}�(j�  �XXXXUSDEURFXFWD2025-01-08�j�  j�  j�  j�  ue]�}�(j�  �XXXXEURUSDFXSPOT�j�  j�  j�  j�  ua]�(}�(j�  �XXXXAUDUSDFXFWD2024-12-30�j�  j�  j�  j�  u}�(j�  �XXXXUSDAUDFXFWD2024-12-30�j�  j�  j�  j�  ue]�}�(j�  �XXXXAUDUSDFXSPOT�j�  j�  j�  j�  ua]�(}�(j�  �XXXXSEKJPYFXFWD2024-11-14�j�  j�  j�  j�  u}�(j�  �XXXXJPYSEKFXFWD2024-11-14�j�  j�  j�  j�  ue]�}�(j�  �XXXXSEKJPYFXSPOT�j�  j�  j�  j�  ua]�(}�(j�  �XXXXCHFEURFXFWD2024-12-30�j�  j�  j�  j�  u}�(j�  �XXXXEURCHFFXFWD2024-12-30�j�  j�  j�  j�  ue]�}�(j�  �XXXXAUDUSDFXSPOT�j�  j�  j�  j�  ua]�}�(j�  �XXXXAUDUSDFXSPOT�j�  j�  j�  j�  ua]�}�(j�  �XXXXEURUSDFXSPOT�j�  j�  j�  j�  ua]�(}�(j�  �XXXXEURUSDFXFWD2024-11-29�j�  j�  j�  j�  u}�(j�  �XXXXUSDEURFXFWD2024-11-29�j�  j�  j�  j�  ue]�(}�(j�  �XXXXAUDUSDFXFWD2025-01-28�j�  j�  j�  j�  u}�(j�  �XXXXUSDAUDFXFWD2025-01-28�j�  j�  j�  j�  ue]�}�(j�  �XXXXAUDUSDFXSPOT�j�  j�  j�  j�  ua�
fx forward��fx spot�j�  j�  j  j  j  j�  j�  j�  j�  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j�  j�  j�  j�  j  j�  j�  j�  j�  j�  j�  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �AUD��AUD��USD��USD��EUR�j  j  �USD��USD��EUR��EUR��GBP��USD��AUD��AUD��AUD��AUD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��CAD��CAD��SEK��SEK��EUR��EUR��AUD��AUD��SEK��SEK��CHF��AUD��AUD��EUR��EUR��AUD��AUD��AUD��AUD��USD��USD��EUR��USD��USD��EUR��EUR��GBP��USD��AUD��AUD��AUD��AUD��USD��CAD��SEK��SEK��EUR��EUR��AUD��AUD��SEK��SEK��CHF��AUD��AUD��EUR��EUR��AUD��AUD��USD��USD��GBP��EUR��USD�j  j  �EUR��EUR��JPY��JPY��USD��JPY��USD��USD��USD��GBP��NGN��NGN��NGN��NGN��NGN��NGN��NGN��NGN��NGN��NGN��NGN��USD��USD��USD��USD��USD��USD��USD��USD��JPY��JPY��EUR��USD��USD��USD��USD��USD��USD��USD��USD��GBP��EUR��USD��EUR��EUR��JPY��JPY��USD��JPY��USD��USD��USD��GBP��NGN��USD��USD��USD��USD��USD��USD��USD��JPY��JPY��EUR��USD��USD��USD��USD��USD��USD�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �
2025-01-28��
2024-10-28��
2025-01-21��
2025-01-08��
2024-10-29�j  j  �
2024-10-23��
2025-01-21��
2024-10-28��
2024-12-17��
2025-01-15��
2025-01-09��
2024-10-24��
2025-01-28��
2024-10-28��
2024-10-25��
2025-04-25��
2025-04-25��
2025-04-25��
2025-04-25��
2025-04-25��
2025-04-25��
2025-04-25��
2025-04-25��
2025-04-25��
2025-04-25��
2025-04-25��
2024-11-06��
2024-11-06��
2024-10-24��
2024-12-30��
2024-10-24��
2025-01-08��
2024-10-24��
2024-12-30��
2024-10-28��
2024-11-14��
2024-12-30��
2024-10-28��
2024-10-28��
2024-10-25��
2024-11-29��
2025-01-28��
2024-10-28��
2024-10-28��
2025-01-28��
2025-01-21��
2025-01-08��
2024-10-29��
2024-10-23��
2025-01-21��
2024-12-17��
2024-10-28��
2025-01-15��
2025-01-09��
2024-10-24��
2024-10-28��
2025-01-28��
2024-10-25��
2025-04-25��
2024-11-06��
2024-12-30��
2024-10-24��
2025-01-08��
2024-10-24��
2024-12-30��
2024-10-24��
2024-11-14��
2024-10-28��
2024-12-30��
2024-10-28��
2024-10-28��
2024-10-25��
2024-11-29��
2025-01-28��
2024-10-28�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e(j�  j�  ]�(}�(j�  �id:2279�j�  �buyer�j�  j�  �ARRAY���R�u}�(j�  �!lei:test_aladdin_executing_entity�j�  �reportDetails.executingEntity�j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  �seller�j�  j�  u}�(j�  �id:2279�j�  �counterparty�j�  j�  u}�(j�  �id:hube�j�  �:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�j�  j�  u}�(j�  �id:aido�j�  �1tradersAlgosWaiversIndicators.executionWithinFirm�j�  j�  u}�(j�  �id:1099�j�  �clientIdentifiers.client�j�  j�  u}�(j�  �id:aido�j�  �trader�j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2279�j�  j�  j�  j�  u}�(j�  �id:2279�j�  j�  j�  j�  u}�(j�  �id:hube�j�  j�  j�  j�  u}�(j�  �id:aido�j�  j�  j�  j�  u}�(j�  �id:1099�j�  j�  j�  j�  u}�(j�  �id:aido�j�  j�  j�  j�  ue]�(}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:966�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2171�j�  j�  j�  j�  u}�(j�  �id:2171�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  u}�(j�  �id:3716�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2045�j�  j�  j�  j�  u}�(j�  �id:2045�j�  j�  j�  j�  u}�(j�  �id:duto�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �id:1099�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:duto�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �id:941�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:duto�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �id:857�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �id:1964�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1964�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:117�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1964�j�  j�  j�  j�  u}�(j�  �id:1964�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:117�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2250�j�  j�  j�  j�  u}�(j�  �id:2250�j�  j�  j�  j�  u}�(j�  �id:tama�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �id:418�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �id:2250�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2250�j�  j�  j�  j�  u}�(j�  �id:tama�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �id:418�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �id:duto�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �id:1099�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �id:2252�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2252�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:1191�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �id:2270�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2270�j�  j�  j�  j�  u}�(j�  �id:hori�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �id:1001�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �id:hube�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �id:1099�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �id:hube�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �id:1099�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �id:2270�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2270�j�  j�  j�  j�  u}�(j�  �id:liso�j�  j�  j�  j�  u}�(j�  �id:chib�j�  j�  j�  j�  u}�(j�  �id:981�j�  j�  j�  j�  u}�(j�  �id:chib�j�  j�  j�  j�  ue]�(}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �id:evva�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  u}�(j�  �id:751�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  ue]�(}�(j�  �id:2132�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2132�j�  j�  j�  j�  u}�(j�  �id:evva�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  u}�(j�  �id:233�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  ue]�(}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �id:evva�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  u}�(j�  �id:900�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  ue]�(}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �id:evva�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  u}�(j�  �id:214�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  ue]�(}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �id:evva�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  u}�(j�  �id:216�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  ue]�(}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �id:evva�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  u}�(j�  �id:743�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  ue]�(}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �id:evva�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  u}�(j�  �id:1092�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  ue]�(}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �id:evva�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  u}�(j�  �id:903�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  ue]�(}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �id:evva�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  u}�(j�  �id:480�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  ue]�(}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �id:evva�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  u}�(j�  �id:1902�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  ue]�(}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �id:evva�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  u}�(j�  �id:733�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2289�j�  j�  j�  j�  u}�(j�  �id:2289�j�  j�  j�  j�  u}�(j�  �id:duto�j�  j�  j�  j�  u}�(j�  �id:aido�j�  j�  j�  j�  u}�(j�  �id:1099�j�  j�  j�  j�  u}�(j�  �id:aido�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2289�j�  j�  j�  j�  u}�(j�  �id:2289�j�  j�  j�  j�  u}�(j�  �id:duto�j�  j�  j�  j�  u}�(j�  �id:aido�j�  j�  j�  j�  u}�(j�  �id:941�j�  j�  j�  j�  u}�(j�  �id:aido�j�  j�  j�  j�  ue]�(}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:1814�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:1814�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �id:2252�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2252�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  u}�(j�  �id:3716�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2252�j�  j�  j�  j�  u}�(j�  �id:2252�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  u}�(j�  �id:3716�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  ue]�(}�(j�  �id:1978�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1978�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:1814�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1978�j�  j�  j�  j�  u}�(j�  �id:1978�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:1814�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �id:2250�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2250�j�  j�  j�  j�  u}�(j�  �id:tama�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �id:418�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2250�j�  j�  j�  j�  u}�(j�  �id:2250�j�  j�  j�  j�  u}�(j�  �id:tama�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �id:418�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:1802�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �id:2087�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2087�j�  j�  j�  j�  u}�(j�  �id:hajo�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:328�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �id:2087�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2087�j�  j�  j�  j�  u}�(j�  �id:hajo�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:328�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:706�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:706�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �id:hube�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �id:941�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �id:hube�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �id:941�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2279�j�  j�  j�  j�  u}�(j�  �id:2279�j�  j�  j�  j�  u}�(j�  �id:hube�j�  j�  j�  j�  u}�(j�  �id:aido�j�  j�  j�  j�  u}�(j�  �	id:'1099'�j�  j�  j�  j�  u}�(j�  �id:aido�j�  j�  j�  j�  ue]�(}�(j�  �id:2279�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2279�j�  j�  j�  j�  u}�(j�  �id:hube�j�  j�  j�  j�  u}�(j�  �id:aido�j�  j�  j�  j�  u}�(j�  �	id:'1099'�j�  j�  j�  j�  u}�(j�  �id:aido�j�  j�  j�  j�  ue]�(}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:'966'�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2171�j�  j�  j�  j�  u}�(j�  �id:2171�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  u}�(j�  �	id:'3716'�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2045�j�  j�  j�  j�  u}�(j�  �id:2045�j�  j�  j�  j�  u}�(j�  �id:duto�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �	id:'1099'�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �id:1964�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1964�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:'117'�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1964�j�  j�  j�  j�  u}�(j�  �id:1964�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:'117'�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �id:2250�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2250�j�  j�  j�  j�  u}�(j�  �id:tama�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �id:'418'�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2250�j�  j�  j�  j�  u}�(j�  �id:2250�j�  j�  j�  j�  u}�(j�  �id:tama�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �id:'418'�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �id:duto�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �	id:'1099'�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �id:2252�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2252�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �	id:'1191'�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �id:2270�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2270�j�  j�  j�  j�  u}�(j�  �id:hori�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �	id:'1001'�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �id:hube�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �	id:'1099'�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �id:hube�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �	id:'1099'�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �id:2270�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2270�j�  j�  j�  j�  u}�(j�  �id:liso�j�  j�  j�  j�  u}�(j�  �id:chib�j�  j�  j�  j�  u}�(j�  �id:'981'�j�  j�  j�  j�  u}�(j�  �id:chib�j�  j�  j�  j�  ue]�(}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2130�j�  j�  j�  j�  u}�(j�  �id:evva�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  u}�(j�  �id:'751'�j�  j�  j�  j�  u}�(j�  �id:'233'�j�  j�  j�  j�  u}�(j�  �id:'900'�j�  j�  j�  j�  u}�(j�  �id:'214'�j�  j�  j�  j�  u}�(j�  �id:'216'�j�  j�  j�  j�  u}�(j�  �id:'743'�j�  j�  j�  j�  u}�(j�  �	id:'1092'�j�  j�  j�  j�  u}�(j�  �id:'903'�j�  j�  j�  j�  u}�(j�  �id:'480'�j�  j�  j�  j�  u}�(j�  �	id:'1902'�j�  j�  j�  j�  u}�(j�  �id:'733'�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2289�j�  j�  j�  j�  u}�(j�  �id:2289�j�  j�  j�  j�  u}�(j�  �id:duto�j�  j�  j�  j�  u}�(j�  �id:aido�j�  j�  j�  j�  u}�(j�  �	id:'1099'�j�  j�  j�  j�  u}�(j�  �id:'941'�j�  j�  j�  j�  u}�(j�  �id:aido�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �	id:'1814'�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �	id:'1814'�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2252�j�  j�  j�  j�  u}�(j�  �id:2252�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  u}�(j�  �	id:'3716'�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  ue]�(}�(j�  �id:2252�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2252�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  u}�(j�  �	id:'3716'�j�  j�  j�  j�  u}�(j�  �id:mina�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1978�j�  j�  j�  j�  u}�(j�  �id:1978�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �	id:'1814'�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �id:1978�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1978�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �	id:'1814'�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2250�j�  j�  j�  j�  u}�(j�  �id:2250�j�  j�  j�  j�  u}�(j�  �id:tama�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �id:'418'�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �id:2250�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2250�j�  j�  j�  j�  u}�(j�  �id:tama�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �id:'418'�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �	id:'1802'�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �id:2087�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2087�j�  j�  j�  j�  u}�(j�  �	id:merged�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:'328'�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �id:2087�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:2087�j�  j�  j�  j�  u}�(j�  �	id:merged�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:'328'�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:'706'�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1984�j�  j�  j�  j�  u}�(j�  �id:suto�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  u}�(j�  �id:'706'�j�  j�  j�  j�  u}�(j�  �id:sha�j�  j�  j�  j�  ue]�(}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �id:hube�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �id:'941'�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue]�(}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �!lei:test_aladdin_executing_entity�j�  j�  j�  j�  u}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �id:1966�j�  j�  j�  j�  u}�(j�  �id:hube�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  u}�(j�  �id:'941'�j�  j�  j�  j�  u}�(j�  �id:post�j�  j�  j�  j�  ue�!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity����      �!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:2279��!lei:test_aladdin_executing_entity��id:1966��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:1964��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:2250��!lei:test_aladdin_executing_entity��id:2252��id:2270��id:1966��!lei:test_aladdin_executing_entity��id:2270��id:2130��id:2132��id:2130��id:2130��id:2130��id:2130��id:2130��id:2130��id:2130��id:2130��id:2130��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:1984��!lei:test_aladdin_executing_entity��id:2252��!lei:test_aladdin_executing_entity��id:1978��!lei:test_aladdin_executing_entity��id:2250��!lei:test_aladdin_executing_entity��id:1984��id:2087��id:2087��!lei:test_aladdin_executing_entity��id:1984��id:1966��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:2279��id:1966��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:1964��!lei:test_aladdin_executing_entity��id:2250��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:2252��id:2270��!lei:test_aladdin_executing_entity��id:1966��id:2270��id:2130��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:1984��!lei:test_aladdin_executing_entity��id:2252��!lei:test_aladdin_executing_entity��id:1978��!lei:test_aladdin_executing_entity��id:2250��id:1984��id:2087��id:2087��!lei:test_aladdin_executing_entity��id:1984��id:1966��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:2279��!lei:test_aladdin_executing_entity��id:2171��id:2045�j  j  �!lei:test_aladdin_executing_entity��id:1964��id:2250��!lei:test_aladdin_executing_entity��id:2130��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:1966��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:2289��id:2289��!lei:test_aladdin_executing_entity��id:1984��!lei:test_aladdin_executing_entity��id:2252��!lei:test_aladdin_executing_entity��id:1978��!lei:test_aladdin_executing_entity��id:2250��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:1984��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:1966��id:2279��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:2171��id:2045��!lei:test_aladdin_executing_entity��id:1964��!lei:test_aladdin_executing_entity��id:2250��id:2130��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:1966��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:2289��id:1984��!lei:test_aladdin_executing_entity��id:2252��!lei:test_aladdin_executing_entity��id:1978��!lei:test_aladdin_executing_entity��id:2250��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:1984��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:1966��id:2279��id:2279��id:1966��id:2171��id:2045�j  j  �id:1964��id:1964��id:2250��id:2250��id:2130��id:2252��id:2270��id:1966��id:1966��id:2270��id:2130��id:2132��id:2130��id:2130��id:2130��id:2130��id:2130��id:2130��id:2130��id:2130��id:2130��id:2289��id:2289��id:1984��id:1984��id:2252��id:2252��id:1978��id:1978��id:2250��id:2250��id:1984��id:2087��id:2087��id:1984��id:1984��id:1966��id:1966��id:2279��id:2279��id:1966��id:2171��id:2045��id:1964��id:1964��id:2250��id:2250��id:2130��id:2252��id:2270��id:1966��id:1966��id:2270��id:2130��id:2289��id:1984��id:1984��id:2252��id:2252��id:1978��id:1978��id:2250��id:2250��id:1984��id:2087��id:2087��id:1984��id:1984��id:1966��id:1966�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �id:1099��id:1099��id:966��id:3716��id:1099��id:941��id:857��id:117��id:117��id:418��id:418��id:1099��id:1191��id:1001��id:1099��id:1099��id:981��id:751��id:233��id:900��id:214��id:216��id:743��id:1092��id:903��id:480��id:1902��id:733��id:1099��id:941��id:1814��id:1814��id:3716��id:3716��id:1814��id:1814��id:418��id:418��id:1802��id:328��id:328��id:706��id:706��id:941��id:941��	id:'1099'��	id:'1099'��id:'966'��	id:'3716'��	id:'1099'��id:'117'��id:'117'��id:'418'��id:'418'��	id:'1099'��	id:'1191'��	id:'1001'��	id:'1099'��	id:'1099'��id:'981'��id:'751'��	id:'1099'��	id:'1814'��	id:'1814'��	id:'3716'��	id:'3716'��	id:'1814'��	id:'1814'��id:'418'��id:'418'��	id:'1802'��id:'328'��id:'328'��id:'706'��id:'706'��id:'941'��id:'941'��2279��test_aladdin_executing_entity��1966��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��1964��test_aladdin_executing_entity��test_aladdin_executing_entity��2250��test_aladdin_executing_entity��2252��2270��1966��test_aladdin_executing_entity��2270��2130��2132��2130��2130��2130��2130��2130��2130��2130��2130��2130��test_aladdin_executing_entity��test_aladdin_executing_entity��1984��test_aladdin_executing_entity��2252��test_aladdin_executing_entity��1978��test_aladdin_executing_entity��2250��test_aladdin_executing_entity��1984��2087��2087��test_aladdin_executing_entity��1984��1966��test_aladdin_executing_entity��test_aladdin_executing_entity��2279��1966��test_aladdin_executing_entity��test_aladdin_executing_entity��1964��test_aladdin_executing_entity��2250��test_aladdin_executing_entity��test_aladdin_executing_entity��2252��2270��test_aladdin_executing_entity��1966��2270��2130��test_aladdin_executing_entity��test_aladdin_executing_entity��1984��test_aladdin_executing_entity��2252��test_aladdin_executing_entity��1978��test_aladdin_executing_entity��2250��1984��2087��2087��test_aladdin_executing_entity��1984��1966��test_aladdin_executing_entity��test_aladdin_executing_entity��2279��test_aladdin_executing_entity��2171��2045�j  j  �test_aladdin_executing_entity��1964��2250��test_aladdin_executing_entity��2130��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��1966��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��2289��2289��test_aladdin_executing_entity��1984��test_aladdin_executing_entity��2252��test_aladdin_executing_entity��1978��test_aladdin_executing_entity��2250��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��1984��test_aladdin_executing_entity��test_aladdin_executing_entity��1966��2279��test_aladdin_executing_entity��test_aladdin_executing_entity��2171��2045��test_aladdin_executing_entity��1964��test_aladdin_executing_entity��2250��2130��test_aladdin_executing_entity��test_aladdin_executing_entity��1966��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��2289��1984��test_aladdin_executing_entity��2252��test_aladdin_executing_entity��1978��test_aladdin_executing_entity��2250��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��1984��test_aladdin_executing_entity��test_aladdin_executing_entity��1966��2279��2279��1966��2171��2045�j  j  �1964��1964��2250��2250��2130��2252��2270��1966��1966��2270��2130��2132��2130��2130��2130��2130��2130��2130��2130��2130��2130��2289��2289��1984��1984��2252��2252��1978��1978��2250��2250��1984��2087��2087��1984��1984��1966��1966��2279��2279��1966��2171��2045��1964��1964��2250��2250��2130��2252��2270��1966��1966��2270��2130��2289��1984��1984��2252��2252��1978��1978��2250��2250��1984��2087��2087��1984��1984��1966��1966��1099��1099��966��3716��1099��941��857��117��117��418��418��1099��1191��1001��1099��1099��981��751��233��900��214��216��743��1092��903��480��1902��733��1099��941��1814��1814��3716��3716��1814��1814��418��418��1802��328��328��706��706��941��941��'1099'��'1099'��'966'��'3716'��'1099'��'117'��'117'��'418'��'418'��'1099'��'1191'��'1001'��'1099'��'1099'��'981'��'751'��'1099'��'1814'��'1814'��'3716'��'3716'��'1814'��'1814'��'418'��'418'��'1802'��'328'��'328'��'706'��'706'��'941'��'941'�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  e(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity�]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j   j  j  j  e]�(j	  j  j
  j  j  j  e]�(j  j  j  j  j  j   e]�(j�  j#  j%  j'  j)  j+  j-  j/  j1  e]�(j�  j�  j4  j6  j8  j:  j<  j>  j@  jB  e]�(j�  jE  jG  jI  jK  jM  jO  jQ  jS  e]�(j�  j�  jV  jX  jZ  j\  j^  j`  jb  jd  e]�(jg  ji  jk  jm  jo  jq  js  ju  e]�(j�  j�  jx  jz  j|  j~  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j   j  j  j  j  j
  j  j  e]�(j�  j�  j  j  j  j  j  j  j  j  e]�(j�  j�  j"  j$  j&  j(  j*  j,  j.  j0  e]�(j�  j�  j3  j5  j7  j9  j;  j=  j?  jA  e]�(j  j  jD  jF  jH  jJ  jL  jN  jP  jR  e]�(j  j	  jU  jW  jY  j[  j]  j_  ja  jc  e]�(j  j  jf  jh  jj  jl  jn  jp  jr  jt  e]�(j  j  jw  jy  j{  j}  j  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j  j  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j!  j#  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j&  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j)  j+  j�  j	  j	  j	  j	  j		  j	  j
	  e]�(j.  j	  j	  j	  j	  j	  j	  j	  j	  e]�(j1  j3  j!	  j#	  j%	  j'	  j)	  j+	  j-	  j/	  e]�(j6  j8  j2	  j4	  j6	  j8	  j:	  j<	  j>	  j@	  e]�(j;  jC	  jE	  jG	  jI	  jK	  jM	  jO	  jQ	  e]�(j>  jT	  jV	  jX	  jZ	  j\	  j^	  j`	  jb	  e]�(jA  je	  jg	  ji	  jk	  jm	  jo	  jq	  js	  e]�(jD  jF  jv	  jx	  jz	  j|	  j~	  j�	  j�	  j�	  e]�(jI  jK  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  e]�(jN  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  e]�(jQ  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  e]�(jT  jV  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  e]�(jY  j[  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  e]�(j^  j`  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  e]�(j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  e]�(jc  j�	  j 
  j
  j
  j
  j
  j

  j
  e]�(jf  jh  j
  j
  j
  j
  j
  j
  j
  j
  e]�(jk  jm  j 
  j"
  j$
  j&
  j(
  j*
  j,
  j.
  e]�(jp  j1
  j3
  j5
  j7
  j9
  j;
  j=
  j?
  e]�(jB
  jD
  jF
  jH
  jJ
  jL
  jN
  jP
  e]�(js  ju  jS
  jU
  jW
  jY
  j[
  j]
  j_
  ja
  e]�(jx  jd
  jf
  jh
  jj
  jl
  jn
  jp
  jr
  e]�(j{  ju
  jw
  jy
  j{
  j}
  j
  j�
  j�
  e]�(j~  j�  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�  j  j  j  j  j
  j  j  j  e]�(j�  j  j  j  j  j  j  j  j!  e]�(j�  j�  j$  j&  j(  j*  j,  j.  j0  j2  e]�(j�  j5  j7  j9  j;  j=  j?  jA  jC  e]�(j�  j�  jF  jH  jJ  jL  jN  jP  jR  jT  e]�(j�  jW  jY  j[  j]  j_  ja  jc  je  e]�(j�  j�  jh  jj  jl  jn  jp  jr  jt  jv  e]�(j�  jy  j{  j}  j  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  e�Order�j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  �
OrderState�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �1676547��1676447��1751493��1752491��1775609�j+  j+  �1776346��1776446��1801904��1801804��1801972��1802633��1875756��1876748��1876648��1901406��1926099�j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  �1926699�j7  �1951234��1951134��1951934��1951834��2001271��2001171��2001551��2001451��2001728��2076731��2076831��2127981��2128081��2453710��2453810��1676447��1676547��1751493��1752491��1775609��1776346��1776446��1801804��1801904��1801972��1802633��1875756��1876648��1876748��1901406��1926099��1926699��1951134��1951234��1951834��1951934��2001171��2001271��2001451��2001551��2001728��2076731��2076831��2127981��2128081��2453710��2453810�j'  j(  j)  j*  j+  j+  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j7  j7  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  jX  jY  jZ  j[  j\  j]  j^  j_  j`  ja  jb  jc  jd  je  jf  �24156��24157��5666��1806��24161�j  j  �3696��3695��3572��3571��24159��437��1251��24155��24154��1013��4730��2701��5094��13969��15593��4320��935��4301��3893��2413��8979��24163��13256��324��325��1811��1812��322��323��3574��3573��126��763��764��6738��6737��13249��13248��2260347��2260447��3301593��3407191�j  �3094046��3093946��1812404��1812504�j  �2018133��3766156��3355448��3355348��3258206��3268099�j  �2572134��2572034��2572434��2572334��2900671��2900771��2861851��2861751��3393328��2380931��2381031��2603181��2603081��2701710��2701610�jg  jh  ji  jj  jk  j  j  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  jy  jz  j{  j|  j}  j~  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j�  j�  j�  j�  j  j�  j�  j�  j�  j�  j�  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  G?�?N�hn~G?�:��OYG?��Iʚ�G?�\M��G?�?|�hsG?�?|�hsG?�?|�hsG?���F�QG?���i�G@d�����G@du��i�4G        G@b��Q��G?�:�	��BG?�=f�XG?�8����<G?�t<�q6�G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G?�<�~�G?�<�~�G?�!���QG?�7�G?�;�"GR�G?�J�i�m7G?�<�y#�G?�@�����G@,����D�G@,��W��G?�3V��G?�1;���	G?�1;���	G?�>7ޓ��G?�Dыxn�G?�=f�XG?�8����<G?�:��OYG?�?N�hn~G?��Iʚ�G?�\M��G?�?|�hsG?���F�QG?���i�G@du��i�4G@d�����G        G@b��Q��G?�:�	��BG?�8����<G?�=f�XG?�t<�q6�G@��     G?�<�~�G?�7�G?�!���QG?�J�i�m7G?�;�"GR�G?�@�����G?�<�y#�G@,��W��G@,����D�G?�3V��G?�/�BA��G?�2�4~��G?�>7ޓ��G?�Dыxn�G?�=f�XG?�8����<GA�ޠ    GA�ޠ    GA@胀   GA,��    GAc�    j  j  GA��    GA��    GA�     GA�     GAc�    GA[�Y�� GA4|��\)GA���`   GA���`   GA\�8    GA1�]���GA��z�GA"�p��
GA,�@�Q�GAQ�g�{G@�#�z�G@�,]p��
G@�G�3333GA1� �{GA0�q�p��GAR>��\)GAyd>�   GAj��    GAL�H    GAL�H    GAC�k��
GAC�k��
GA;    GA;    GAdP2    GAdP2    GA�hQ�GAs�    GAv�`    GA7l
�G�GA7l
�G�GA�-�    GA�-�    GA�ޠ    GA�ޠ    GA@胀   GA,��    GAc�    GA��    GA��    GA�     GA�     GAc�    GA[�Y��  GA4|��\)GA���`   GA���`   GA\�8    GAl�8    GA�_@   GAL�H    GAL�H    GAC�k��
GAC�k��
GA;    GA;    GAdP2    GAdP2    GA�hQ�GAs�    GAv�`    GA7l
�G�GA7l
�G�GA�-�    GA�-�    jg  jh  ji  jj  jk  j  j  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  jy  jz  j{  j|  j}  j~  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j�  j�  j�  j�  j  j�  j�  j�  j�  j�  j�  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �&AladdinTCA.FX.Transaction.20241022.csv�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e(j�  j�  j�  j�  �2024-10-23T14:43:05.883000Z��2024-10-23T14:43:05.883000Z��2024-10-23T15:21:06.726000Z��2024-10-23T12:50:52.063000Z��2024-10-23T16:19:38.106000Z��2024-10-23T16:19:38.106000Z��2024-10-23T16:19:38.106000Z��2024-10-23T12:57:37.573000Z��2024-10-23T12:57:37.576000Z��2024-10-23T08:43:28.920000Z��2024-10-23T08:43:28.920000Z��2024-10-23T15:23:51.413000Z��2024-10-23T15:38:12.150000Z��2024-10-23T12:19:17.020000Z��2024-10-23T14:24:22.626000Z��2024-10-23T14:24:22.626000Z��2024-10-22T23:52:25.046000Z��2024-10-23T15:41:15.596000Z��2024-10-23T15:41:15.596000Z��2024-10-23T15:41:15.596000Z��2024-10-23T15:41:15.596000Z��2024-10-23T15:41:15.596000Z��2024-10-23T15:41:15.596000Z��2024-10-23T15:41:15.596000Z��2024-10-23T15:41:15.596000Z��2024-10-23T15:41:15.596000Z��2024-10-23T15:41:15.596000Z��2024-10-23T15:41:15.596000Z��2024-10-23T19:19:01.173000Z��2024-10-23T19:19:01.173000Z��2024-10-23T13:55:24.950000Z��2024-10-23T13:55:24.950000Z��2024-10-23T17:45:39.086000Z��2024-10-23T17:45:39.086000Z��2024-10-23T13:56:12.120000Z��2024-10-23T13:56:12.120000Z��2024-10-23T08:46:10.416000Z��2024-10-23T08:46:10.416000Z��2024-10-23T12:39:41.473000Z��2024-10-23T16:20:46.786000Z��2024-10-23T16:20:46.786000Z��2024-10-23T13:57:23.473000Z��2024-10-23T13:57:23.476000Z��2024-10-23T14:24:22.626000Z��2024-10-23T14:24:22.626000Z��2024-10-23T14:53:46.346000Z��2024-10-23T14:53:46.346000Z��2024-10-23T18:11:16.130000Z��2024-10-23T15:21:16.903000Z��2024-10-23T16:52:54.563000Z��2024-10-23T12:59:04.176000Z��2024-10-23T12:59:04.176000Z��2024-10-23T08:49:50.443000Z��2024-10-23T08:49:50.443000Z��2024-10-23T15:24:24.793000Z��2024-10-23T18:11:16.130000Z��2024-10-23T12:20:58.593000Z��2024-10-23T14:36:06.386000Z��2024-10-23T14:36:06.386000Z��2024-10-23T01:00:14.083000Z��2024-10-23T15:47:02.366000Z��2024-10-23T20:59:04.026000Z��2024-10-23T15:01:17.083000Z��2024-10-23T15:01:17.083000Z��2024-10-23T18:10:15.313000Z��2024-10-23T18:10:15.313000Z��2024-10-23T14:43:20.066000Z��2024-10-23T14:43:20.066000Z��2024-10-23T08:49:50.450000Z��2024-10-23T08:49:50.450000Z��2024-10-23T15:18:33.336000Z��2024-10-23T18:11:16.130000Z��2024-10-23T18:26:11.510000Z��2024-10-23T15:01:57.390000Z��2024-10-23T15:01:57.386000Z��2024-10-23T14:36:06.386000Z��2024-10-23T14:36:06.386000Z��2024-10-23T14:53:46.346000Z��2024-10-23T14:53:46.346000Z��2024-10-23T18:11:16.130000Z��2024-10-23T15:21:16.903000Z��2024-10-23T16:52:54.566000Z��2024-10-23T16:52:54.566000Z��2024-10-23T16:52:54.566000Z��2024-10-23T12:59:04.176000Z��2024-10-23T12:59:04.176000Z��2024-10-23T08:49:50.443000Z��2024-10-23T08:49:50.443000Z��2024-10-23T15:24:24.793000Z��2024-10-23T18:11:16.130000Z��2024-10-23T12:20:58.593000Z��2024-10-23T14:36:06.386000Z��2024-10-23T14:36:06.386000Z��2024-10-23T01:00:14.083000Z��2024-10-23T15:47:02.366000Z��2024-10-23T15:47:02.366000Z��2024-10-23T15:47:02.366000Z��2024-10-23T15:47:02.366000Z��2024-10-23T15:47:02.366000Z��2024-10-23T15:47:02.366000Z��2024-10-23T15:47:02.366000Z��2024-10-23T15:47:02.366000Z��2024-10-23T15:47:02.366000Z��2024-10-23T15:47:02.366000Z��2024-10-23T15:47:02.366000Z��2024-10-23T20:59:04.026000Z��2024-10-23T20:59:04.026000Z��2024-10-23T15:01:17.083000Z��2024-10-23T15:01:17.083000Z��2024-10-23T18:10:15.313000Z��2024-10-23T18:10:15.313000Z��2024-10-23T14:43:20.066000Z��2024-10-23T14:43:20.066000Z��2024-10-23T08:49:50.450000Z��2024-10-23T08:49:50.450000Z��2024-10-23T15:18:33.336000Z��2024-10-23T18:11:16.130000Z��2024-10-23T18:11:16.130000Z��2024-10-23T15:01:57.386000Z��2024-10-23T15:01:57.390000Z��2024-10-23T14:36:06.386000Z��2024-10-23T14:36:06.386000Z��2024-09-05T21:45:19.246000Z��2024-09-05T16:16:19.246000Z��2024-09-05T16:39:19.246000Z��2024-09-05T21:10:19.246000Z��2024-09-05T17:54:19.246000Z��2024-09-05T18:24:19.246000Z��2024-09-05T11:07:19.246000Z��2024-09-05T22:57:19.246000Z��2024-09-05T19:45:19.246000Z��2024-09-05T18:08:19.246000Z��2024-09-06T00:30:19.246000Z��2024-09-05T09:46:19.246000Z��2024-09-05T23:18:19.246000Z��2024-09-05T16:37:19.246000Z��2024-09-05T11:32:19.246000Z��2024-09-05T17:49:19.246000Z��2024-09-05T10:47:19.246000Z��2024-09-05T13:38:19.246000Z��2024-09-05T13:06:19.246000Z��2024-09-05T13:27:19.246000Z��2024-09-05T18:57:19.246000Z��2024-09-05T11:38:19.246000Z��2024-09-05T23:03:19.246000Z��2024-09-05T17:28:19.246000Z��2024-09-05T21:53:19.246000Z��2024-09-05T23:47:19.246000Z��2024-09-05T12:57:19.246000Z��2024-09-05T17:26:19.246000Z��2024-09-05T11:18:19.246000Z��2024-09-05T22:40:19.246000Z��2024-09-05T22:19:19.246000Z��2024-09-05T15:56:19.246000Z�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j   j  j  j  j  j  j  j  j  j	  j
  j  j  j
  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j   j!  j"  j#  j$  j%  j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j7  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  �2024-10-23T14:56:07.553000Z��2024-10-23T14:56:07.550000Z��2024-10-23T18:12:09.070000Z��2024-10-23T15:22:17.440000Z��2024-10-23T19:07:01.630000Z��2024-10-23T19:07:01.630000Z��2024-10-23T19:07:01.630000Z��2024-10-23T12:59:57.603000Z��2024-10-23T12:59:57.500000Z��2024-10-23T08:56:08.836000Z��2024-10-23T08:56:08.633000Z��2024-10-23T16:45:50.696000Z��2024-10-23T18:12:11.406000Z��2024-10-23T12:49:28.810000Z��2024-10-23T14:36:26.800000Z��2024-10-23T14:36:26.500000Z��2024-10-23T01:04:44.553000Z��2024-10-23T16:51:25.273000Z��2024-10-23T16:51:25.273000Z��2024-10-23T16:51:25.273000Z��2024-10-23T16:51:25.273000Z��2024-10-23T16:51:25.273000Z��2024-10-23T16:51:25.273000Z��2024-10-23T16:51:25.273000Z��2024-10-23T16:51:25.273000Z��2024-10-23T16:51:25.273000Z��2024-10-23T16:51:25.273000Z��2024-10-23T16:51:25.273000Z��2024-10-23T21:14:46.770000Z��2024-10-23T21:14:46.770000Z��2024-10-23T15:02:21.506000Z��2024-10-23T15:02:21.946000Z��2024-10-23T18:13:10.383000Z��2024-10-23T18:13:10.336000Z��2024-10-23T14:44:13.403000Z��2024-10-23T14:44:13.440000Z��2024-10-23T08:56:14.576000Z��2024-10-23T08:56:14.533000Z��2024-10-23T15:20:37.733000Z��2024-10-23T18:35:36.633000Z��2024-10-23T18:35:36.633000Z��2024-10-23T15:02:36.513000Z��2024-10-23T15:02:35.946000Z��2024-10-23T14:36:26.720000Z��2024-10-23T14:36:26.670000Z��2024-10-23T14:56:05.570000Z��2024-10-23T14:56:05.570000Z��2024-10-23T18:12:07.743000Z��2024-10-23T15:21:35.686000Z�j.  �2024-10-23T12:59:55.706000Z��2024-10-23T12:59:55.706000Z��2024-10-23T08:56:07.306000Z��2024-10-23T08:56:07.306000Z�j3  �2024-10-23T18:12:10.180000Z��2024-10-23T12:49:26.726000Z��2024-10-23T14:36:24.853000Z��2024-10-23T14:36:24.853000Z��2024-10-23T01:04:42.706000Z��2024-10-23T16:39:19.110000Z�j:  �2024-10-23T15:02:20.093000Z��2024-10-23T15:02:20.093000Z��2024-10-23T18:12:26.416000Z��2024-10-23T18:12:26.416000Z��2024-10-23T14:44:11.706000Z��2024-10-23T14:44:11.706000Z��2024-10-23T08:56:13.433000Z��2024-10-23T08:56:13.433000Z��2024-10-23T15:20:36.266000Z��2024-10-23T18:27:48.833000Z��2024-10-23T18:35:35.656000Z��2024-10-23T15:02:34.643000Z��2024-10-23T15:02:34.643000Z��2024-10-23T14:36:24.853000Z��2024-10-23T14:36:24.853000Z�jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  jX  jY  jZ  j[  j\  j]  j^  j_  j`  ja  jb  jc  jd  je  jf  jg  jh  ji  jj  jk  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  jy  jz  j.  j{  j|  j}  j~  j3  j  j�  j�  j�  j�  j�  j:  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j'  j(  j)  j*  j+  j+  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j7  j7  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  jX  jY  jZ  j[  j\  j]  j^  j_  j`  ja  jb  jc  jd  je  jf  G?�?N�hn~G?�:��OYG?��Iʚ�G?�\M��G?�?|�hsG?�?|�hsG?�?|�hsG?���F�QG?���i�G@d�����G@du��i�4G        G@b��Q��G?�:�	��BG?�=f�XG?�8����<G?�t<�q6�G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G?�<�~�G?�<�~�G?�!���QG?�7�G?�;�"GR�G?�J�i�m7G?�<�y#�G?�@�����G@,����D�G@,��W��G?�3V��G?�1;���	G?�1;���	G?�>7ޓ��G?�Dыxn�G?�=f�XG?�8����<G?�:��OYG?�?N�hn~G?��Iʚ�G?�\M��G?�?|�hsG?���F�QG?���i�G@du��i�4G@d�����G        G@b��Q��G?�:�	��BG?�8����<G?�=f�XG?�t<�q6�G@��     G?�<�~�G?�7�G?�!���QG?�J�i�m7G?�;�"GR�G?�@�����G?�<�y#�G@,��W��G@,����D�G?�3V��G?�/�BA��G?�2�4~��G?�>7ޓ��G?�Dыxn�G?�=f�XG?�8����<j�  j�  j�  j�  j�  j  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j   j  j  j  j  j  j  j  j  j	  j
  j  j  j
  j  j  j  j  �MONE�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  GA�ޠ    GA�ޠ    GA@胀   GA,��    GAc�    j  j  GA��    GA��    GA�     GA�     GAc�    GA[�Y�� GA4|��\)GA���`   GA���`   GA\�8    GA1�]���GA��z�GA"�p��
GA,�@�Q�GAQ�g�{G@�#�z�G@�,]p��
G@�G�3333GA1� �{GA0�q�p��GAR>��\)GAyd>�   GAj��    GAL�H    GAL�H    GAC�k��
GAC�k��
GA;    GA;    GAdP2    GAdP2    GA�hQ�GAs�    GAv�`    GA7l
�G�GA7l
�G�GA�-�    GA�-�    GA�ޠ    GA�ޠ    GA@胀   GA,��    GAc�    GA��    GA��    GA�     GA�     GAc�    GA[�Y��  GA4|��\)GA���`   GA���`   GA\�8    GAl�8    GA�_@   GAL�H    GAL�H    GAC�k��
GAC�k��
GA;    GA;    GAdP2    GAdP2    GA�hQ�GAs�    GAv�`    GA7l
�G�GA7l
�G�GA�-�    GA�-�    j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j   j!  j"  j#  j$  j%  j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j7  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  e(jX  jY  jZ  j[  j\  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
Allocation�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �Market Side�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jM  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  jX  jY  jZ  j[  j\  j]  j^  j_  j`  ja  jb  jc  jd  je  jf  jg  jh  ji  jj  jk  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  jy  jz  j.  j{  j|  j}  j~  j3  j  j�  j�  j�  j�  j�  j:  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j�  j�  j�  j�  j  j  j  j�  j�  j�  j�  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j  j
  j  j  j  j  j  j  j"  j'  j*  j/  j2  j7  j<  j?  jB  jE  jJ  jO  jR  jU  jZ  j_  j  jd  jg  jl  jq  j  jt  jy  j|  j  j�  j�  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j  j  j�  j�  j�  j�  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j�  j�  j�  j�  j  j�  j�  j�  j�  j�  j�  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �����������������������������������������������������������������������������j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  e(j  j  j  j  j  j  �FX�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bhhK ��h��R�(KKKM��h!�]�(�Currency Derivatives�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hTat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hkat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h}at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hdat�bh�Nu��R�h
h}�(hhhK ��h��R�(KKO��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h4h5h6h7h8h9h:h;h<h=h>h?h@hBhChDhEhFhGhHhKhMhNhOhPhQhRhVhWhXhYhZh[h]h^h`hahbhchehfhghhhihjhlhmhnhohphqhrhshthuhvhwhxhyhzh{h|h~hh�h�et�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���K
KK��R�u}�(jA  h�jB  jE  KKK��R�u}�(jA  j  jB  jE  KKK��R�u}�(jA  j  jB  jE  K$K%K��R�u}�(jA  je  jB  jE  K%K&K��R�u}�(jA  j�  jB  jE  K'K(K��R�u}�(jA  j  jB  jE  K.K/K��R�u}�(jA  jg  jB  jE  K/K0K��R�u}�(jA  j�  jB  jE  K0K1K��R�u}�(jA  j  jB  jE  K7K8K��R�u}�(jA  j  jB  jE  KFKGK��R�u}�(jA  j=  jB  jE  KXKYK��R�u}�(jA  j�  jB  jE  K]K^K��R�u}�(jA  j�  jB  jE  K:K;K��R�u}�(jA  j�  jB  jE  K?K@K��R�u}�(jA  j�  jB  h�(�x                                                                      	       
                                                                                                                                             !       "       #       &       (       )       *       +       ,       -       1       2       3       4       5       6       8       9       ;       <       =       >       @       A       B       C       D       E       G       H       I       J       K       L       M       N       O       P       Q       R       S       T       U       V       W       Y       Z       [       \       �h�i8�����R�(Kh�NNNJ����J����K t�bKO��h�t�R�u}�(jA  j�  jB  jE  K^K_K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.