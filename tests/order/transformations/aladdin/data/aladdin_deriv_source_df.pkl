��L*      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK1��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�order.activatedTimestampUtc��order.avgPrice��order.basketId��order.createdTimestampUtc��
order.isin��order.modifiedTimestampUtc��
order.orderId��order.orderQuantity��order.orderStatus��order.orderType��order.pmInitials��order.priceCcy��	order.ric��order.timeInForce��order.trader��order.tranType��orderdetail.orderId��orderdetail.orderDetailId��orderdetail.origOrderId��orderdetail.portfolioId��__order_type__��transaction.dealingCapacity��transaction.execCptyId��transaction.maturity��transaction.orderId��transaction.placementId��transaction.portfolioId��transaction.portfolioTicker��transaction.ric��transaction.secDesc1��transaction.secGroup��transaction.secType��transaction.tradeNum��transaction.tradeQuantity��placement.limitValue��placement.stopValue��placement.orderId��placement.placementId��fill.dealingCapacity��
fill.exchange��fill.executedPrice��fill.executedQuantity��fill.executedTimestampUtc��fill.fillId��fill.placementId��transaction.assetId��transaction.tradeCoupon��transaction.underlyingSnpCusip��transaction.isin�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(hWN�start�K �stop�K�step�Ku��R�e]�(�pandas.core.arrays.integer��IntegerArray���)��}�(�_data��numpy.core.numeric��_frombuffer���(�        0ķ     �ķ     �4�     O5�     �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R��_mask�hl(�           �h�b1�����R�(Kh"NNNJ����J����K t�bK��htt�R��_cache�}��dtype�hd�
Int64Dtype���)��}�h�}�(�numpy_dtype�h�i8�����R�(KhqNNNJ����J����K t�b�kind��i�usbsubhf)��}�(hihl(�        ��������       ��������5       �hpK��htt�R�hwhl(�           �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(�                                    �hpK��htt�R�hwhl(�           �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(�        \ŷ     �ķ     �5�     6�     �hpK��htt�R�hwhl(�           �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(�        |�     �     ���     c��     �hpK��htt�R�hwhl(�           �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(�        0ķ     �ķ     �4�     O5�     �hpK��htt�R�hwhl(�           �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(�        �       �       �       �       �hpK��htt�R�hwhl(�           �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(�        P       P       P       P       �hpK��htt�R�hwhl(�           �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(�        \ŷ     �ķ     �5�     6�     �hpK��htt�R�hwhl(�           �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(�        ơ    �š    k��    ���    �hpK��htt�R�hwhl(�           �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(�        �       �       �       �       �hpK��htt�R�hwhl(�           �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(�        V�      W�      T�      U�      �hpK��htt�R�hwhl(�           �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(�        ��������       ��������5       �hpK��htt�R�hwhl(�           �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(�                                    �hpK��htt�R�hwhl(�       �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(�                                    �hpK��htt�R�hwhl(�       �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(�                                    �hpK��htt�R�hwhl(�       �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(�                                    �hpK��htt�R�hwhl(�       �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(�                                    �hpK��htt�R�hwhl(�       �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(�                                    �hpK��htt�R�hwhl(�       �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(�                                    �hpK��htt�R�hwhl(�       �h{K��htt�R�h�}�h�h�sub�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�2022-10-27T01:05:44.220Z�jo  �2022-10-27T01:05:44.210Z�jp  et�b�_dtype�j`  �StringDtype���)��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(�pandas._libs.missing��NA���j  j  j  et�bjr  jt  )��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(�2022-10-27T00:26:20.950Z��2022-10-27T00:26:20.946Z�j�  j�  et�bjr  jt  )��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(j  j  �SGXDB0951002�j  et�bjr  jt  )��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(�2022-10-27T01:10:54.406Z��2022-10-27T01:10:54.346Z��2022-10-27T01:10:04.306Z�j�  et�bjr  jt  )��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(�F�j�  j�  j�  et�bjr  jt  )��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(�M�j�  j�  j�  et�bjr  jt  )��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(�AH�j�  j�  j�  et�bjr  jt  )��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(�MYR�j�  �USD�j�  et�bjr  jt  )��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(�FKLIV2��FKLIX2��SFCV2��SFCX2�et�bjr  jt  )��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(�AB�j�  j�  j�  et�bjr  jt  )��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(�O�j�  j�  j�  et�bjr  jt  )��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(�
10/31/2022��
11/30/2022��
10/28/2022��
11/29/2022�et�bjr  jt  )��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(�WCPI�j  j  j  et�bjr  jt  )��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(�FKLIV2��FKLIX2��SFCV2��SFCX2�et�bjr  jt  )��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(�KL COMPOSITE INDX OCT 22��KL COMPOSITE INDX NOV 22��FTSE CHINA A50 OCT 22��FTSE CHINA A50 NOV 22�et�bjr  jt  )��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(�FUTURE�j%  j%  j%  et�bjr  jt  )��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(�CSWAP�j/  �INDEX�j0  et�bjr  jt  )��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(j  j  j  j  et�bjr  jt  )��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(j  j  j  j  et�bjr  jt  )��ubjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(j  j  j  j  et�bjr  jt  )��ubhl(�             ��@     Ȗ@     @�@     <�@�h�f8�����R�(KhqNNNJ����J����K t�bKK��htt�R�hhK ��h��R�(KKK��jl  �]�(hthththtj  j  j  j  et�bhhK ��h��R�(KKK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�ASSETID�je  je  je  et�bhhK ��h��R�(KKK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�SELL��BUY��SELLOPEN��BUYCLOSE�et�bhhK ��h��R�(KKK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(j  j  j  j  et�bhhK ��h��R�(KKK��j{  �]�(j  j  j  j  et�bjb  )��}�(je  hhK ��h��R�(KK��jl  �]�(j  j  j  �SGXDB0983179�et�bjr  jt  )��h�}��ndim�Ksube]�(h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h0at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hCat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h9hMet�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hRat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hTat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bhWNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hg�mgr_locs��builtins��slice���KKK��R�u}�(jI  h�jJ  jM  KKK��R�u}�(jI  h�jJ  jM  K
KK��R�u}�(jI  h�jJ  jM  KKK��R�u}�(jI  h�jJ  jM  KKK��R�u}�(jI  h�jJ  jM  KKK��R�u}�(jI  h�jJ  jM  KKK��R�u}�(jI  h�jJ  jM  KKK��R�u}�(jI  h�jJ  jM  KKK��R�u}�(jI  h�jJ  jM  KKK��R�u}�(jI  h�jJ  jM  KKK��R�u}�(jI  h�jJ  jM  K K!K��R�u}�(jI  j  jJ  jM  K!K"K��R�u}�(jI  j  jJ  jM  K"K#K��R�u}�(jI  j  jJ  jM  K#K$K��R�u}�(jI  j)  jJ  jM  K$K%K��R�u}�(jI  j4  jJ  jM  K%K&K��R�u}�(jI  j?  jJ  jM  K)K*K��R�u}�(jI  jJ  jJ  jM  K+K,K��R�u}�(jI  jU  jJ  jM  K,K-K��R�u}�(jI  jc  jJ  jM  K KK��R�u}�(jI  jv  jJ  jM  KKK��R�u}�(jI  j�  jJ  jM  KKK��R�u}�(jI  j�  jJ  jM  KKK��R�u}�(jI  j�  jJ  jM  KKK��R�u}�(jI  j�  jJ  jM  KK	K��R�u}�(jI  j�  jJ  jM  K	K
K��R�u}�(jI  j�  jJ  jM  K
KK��R�u}�(jI  j�  jJ  jM  KKK��R�u}�(jI  j�  jJ  jM  KK
K��R�u}�(jI  j�  jJ  jM  KKK��R�u}�(jI  j�  jJ  jM  KKK��R�u}�(jI  j�  jJ  jM  KKK��R�u}�(jI  j�  jJ  jM  KKK��R�u}�(jI  j  jJ  jM  KKK��R�u}�(jI  j  jJ  jM  KKK��R�u}�(jI  j  jJ  jM  KKK��R�u}�(jI  j(  jJ  jM  KK K��R�u}�(jI  j3  jJ  jM  K&K'K��R�u}�(jI  j<  jJ  jM  K'K(K��R�u}�(jI  jE  jJ  jM  K*K+K��R�u}�(jI  jU  jJ  jM  KKK��R�u}�(jI  jX  jJ  jM  KK<K��R�u}�(jI  j^  jJ  jM  K-K.K��R�u}�(jI  ji  jJ  jM  KKK��R�u}�(jI  jw  jJ  jM  K.K/K��R�u}�(jI  j�  jJ  jM  K/K0K��R�u}�(jI  j�  jJ  jM  K0K1K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.