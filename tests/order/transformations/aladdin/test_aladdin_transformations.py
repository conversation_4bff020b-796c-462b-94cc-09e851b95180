import logging
import os
from pathlib import Path
from unittest.mock import patch

import pandas as pd
import pytest
from swarm.task.auditor import Auditor

from swarm_tasks.order.feed.aladdin.aladdin_order_frame_merger import (
    AladdinOrderFrameMerger,
)
from swarm_tasks.order.feed.aladdin.static import DevColumns
from swarm_tasks.order.transformations.aladdin import (
    barings_aladdin_order_transformations,
)
from swarm_tasks.order.transformations.aladdin.aladdin_order_transformations import (
    AladdinOrderTransformations,
)
from swarm_tasks.order.transformations.aladdin.barings_aladdin_order_transformations import (
    S3_LOOKUP_INSTRUMENT_FILE_PATH,
)
from swarm_tasks.order.transformations.aladdin.barings_aladdin_order_transformations import (
    S3_LOOKUP_PRIVATE_FILE_PATH,
)
from swarm_tasks.order.transformations.order_transform_maps import aladdin_transform_map

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
BARINGS_TEST_FILES_DIR = TEST_FILES_DIR.joinpath("barings")

# The source file is a pickle of the df which should be used as  the source dataframe
SOURCE_DERIV_PICKLE_FILE = TEST_FILES_DIR.joinpath("aladdin_deriv_source_df.pkl")
SOURCE_FX_PICKLE_FILE = TEST_FILES_DIR.joinpath("aladdin_fx_source_df.pkl")
EXPECTED_RESULT_PICKLE_PATH = TEST_FILES_DIR.joinpath(
    "aladdin_deriv_transformed_expected_df.pkl"
)
EXPECTED_FX_RESULT_PICKLE_PATH = TEST_FILES_DIR.joinpath(
    "aladdin_fx_transformed_expected_df.pkl"
)


# Barings
BARINGS_INSTRUMENT_LOOKUP_CSV_PATH = BARINGS_TEST_FILES_DIR.joinpath("Barings_SM.csv")
BARINGS_PRIVATE_LOOKUP_CSV_PATH = BARINGS_TEST_FILES_DIR.joinpath(
    "Barings_Private_SM.csv"
)
SOURCE_BARINGS_LOANS_PICKLE_PATH = BARINGS_TEST_FILES_DIR.joinpath(
    "barings_aladdin_loans_source_df.pkl"
)
EXPECTED_BARINGS_LOANS_RESULT_PICKLE_PATH = BARINGS_TEST_FILES_DIR.joinpath(
    "barings_aladdin_loans_expected_df.pkl"
)
SOURCE_BARINGS_CDS_PICKLE_PATH = BARINGS_TEST_FILES_DIR.joinpath(
    "barings_aladdin_cds_source_df.pkl"
)
EXPECTED_BARINGS_CDS_RESULT_PICKLE_PATH = BARINGS_TEST_FILES_DIR.joinpath(
    "barings_aladdin_cds_expected_df.pkl"
)
SOURCE_BARINGS_NO_RIC_PICKLE_PATH = BARINGS_TEST_FILES_DIR.joinpath(
    "barings_aladdin_no_ric_source_df.pkl"
)
EXPECTED_BARINGS_NO_RIC_RESULT_PICKLE_PATH = BARINGS_TEST_FILES_DIR.joinpath(
    "barings_aladdin_no_ric_expected_df.pkl"
)
SOURCE_BARINGS_FX_PICKLE_PATH = BARINGS_TEST_FILES_DIR.joinpath(
    "barings_aladdin_fx_source_df.pkl"
)
EXPECTED_BARINGS_FX_RESULT_PICKLE_PATH = BARINGS_TEST_FILES_DIR.joinpath(
    "barings_aladdin_fx_expected_df.pkl"
)
logger = logging.getLogger(__name__)


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestAladdinOrderTransformations:
    """
    Test suite for Primary Transformations for the Aladdin Feed
    """

    @patch.object(
        AladdinOrderTransformations,
        "_get_executing_entity",
    )
    def test_end_to_end_aladdin_deriv_transformations(
        self, mock_executing_entity, auditor
    ):
        os.environ["SWARM_FILE_URL"] = "AladdinTCA.cfs_DERIV.Fill.20221107"
        source_frame = pd.read_pickle(SOURCE_DERIV_PICKLE_FILE)
        mock_executing_entity.return_value = pd.DataFrame(
            data="lei:test_aladdin_executing_entity",
            index=source_frame.index,
            columns=[DevColumns.EXECUTING_ENTITY_WITH_LEI],
        )

        task = aladdin_transform_map.transformation(tenant="test")(
            source_frame=source_frame, logger=logger, auditor=None
        )
        result = task.process()
        result = result.drop(["sourceKey"], axis=1)

        expected = pd.read_pickle(EXPECTED_RESULT_PICKLE_PATH)
        pd.testing.assert_frame_equal(
            left=result.sort_index(axis=1),
            right=expected.sort_index(axis=1),
            check_dtype=False,
        )

    @patch.object(
        AladdinOrderTransformations,
        "_get_executing_entity",
    )
    def test_end_to_end_aladdin_fx_transformations(
        self, mock_executing_entity, auditor
    ):
        os.environ["SWARM_FILE_URL"] = "AladdinTCA.cfs_FX.Fill.20221107"
        source_frame = pd.read_pickle(SOURCE_FX_PICKLE_FILE)
        mock_executing_entity.return_value = pd.DataFrame(
            data="lei:test_aladdin_executing_entity",
            index=source_frame.index,
            columns=[DevColumns.EXECUTING_ENTITY_WITH_LEI],
        )

        task = aladdin_transform_map.transformation(tenant="test")(
            source_frame=source_frame, logger=logger, auditor=None
        )
        result = task.process()
        result = result.drop(["sourceKey"], axis=1)

        expected = pd.read_pickle(EXPECTED_FX_RESULT_PICKLE_PATH)
        pd.testing.assert_frame_equal(
            left=result.sort_index(axis=1),
            right=expected.sort_index(axis=1),
            check_dtype=False,
        )

    @pytest.mark.parametrize(
        "file_url, source_pickle_path, expected_pickle_path",
        [
            (
                # Test for Loans
                "AladdinTCA.FI.Transaction.20240125.csv",
                SOURCE_BARINGS_LOANS_PICKLE_PATH,
                EXPECTED_BARINGS_LOANS_RESULT_PICKLE_PATH,
            ),
            (
                # Test for CDS
                "AladdinTCA.DERIV.Transaction.20240125.csv",
                SOURCE_BARINGS_CDS_PICKLE_PATH,
                EXPECTED_BARINGS_CDS_RESULT_PICKLE_PATH,
            ),
            (
                # Test where RIC is null
                "AladdinTCA.DERIV.Transaction.20240825.csv",
                SOURCE_BARINGS_NO_RIC_PICKLE_PATH,
                EXPECTED_BARINGS_NO_RIC_RESULT_PICKLE_PATH,
            ),
            (
                # Test where RIC is null
                "AladdinTCA.FX.Transaction.20241022.csv",
                SOURCE_BARINGS_FX_PICKLE_PATH,
                EXPECTED_BARINGS_FX_RESULT_PICKLE_PATH,
            ),
        ],
    )
    @patch.object(
        AladdinOrderTransformations,
        "_get_executing_entity",
    )
    @patch.object(
        barings_aladdin_order_transformations.DfFromS3Csv,
        "process",
    )
    def test_end_to_end_barings_aladdin_transformations(
        self,
        mock_lookup_df,
        mock_executing_entity,
        auditor,
        file_url,
        source_pickle_path,
        expected_pickle_path,
    ):
        # Set the environment variable for the file URL
        os.environ["SWARM_FILE_URL"] = file_url

        # Load the source dataframe from the specified pickle file
        source_frame = pd.read_pickle(source_pickle_path)

        # Mock the side effect for the lookup dataframe
        mock_lookup_df.side_effect = self.df_from_s3_csv_side_effect

        # Define the task based on the tenant
        task = aladdin_transform_map.transformation(tenant="barings")(
            source_frame=source_frame, logger=logger, auditor=auditor
        )

        # Mock the executing entity return value
        mock_executing_entity.return_value = pd.DataFrame(
            data="lei:test_aladdin_executing_entity",
            index=task.source_frame.index,
            columns=[DevColumns.EXECUTING_ENTITY_WITH_LEI],
        )

        # Process the task
        result = task.process()
        result = result.drop(["sourceKey"], axis=1)

        # Load the expected result dataframe from the expected pickle file
        expected = pd.read_pickle(expected_pickle_path)
        expected = expected.drop(columns=["sourceKey"], errors="ignore")

        # Assert that the result matches the expected output
        pd.testing.assert_frame_equal(
            left=result.sort_index(axis=1),
            right=expected.sort_index(axis=1),
            check_dtype=False,
        )

    @staticmethod
    def df_from_s3_csv_side_effect(**kwargs):
        s3_key = kwargs["params"].s3_key

        if s3_key == S3_LOOKUP_INSTRUMENT_FILE_PATH:
            return pd.read_csv(BARINGS_INSTRUMENT_LOOKUP_CSV_PATH)
        elif s3_key == S3_LOOKUP_PRIVATE_FILE_PATH:
            return pd.read_csv(BARINGS_PRIVATE_LOOKUP_CSV_PATH)
