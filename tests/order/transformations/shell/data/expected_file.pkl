��      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK`��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date�� executionDetails.aggregatedOrder��!executionDetails.buySellIndicator��(_orderState.executionDetails.orderStatus��#_order.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo��&executionDetails.shortSellingIndicator�� executionDetails.tradingCapacity��executionDetails.validityPeriod�� executionDetails.waiverIndicator��	hierarchy��	_order.id��_orderState.id��_order.__meta_model__��_orderState.__meta_model__��
orderClass��"orderIdentifiers.aggregatedOrderId��(orderIdentifiers.initialOrderDesignation��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��!orderIdentifiers.orderRoutingCode��orderIdentifiers.sequenceNumber��.orderIdentifiers.tradingVenueTransactionIdCode��!orderIdentifiers.transactionRefNo��'_order.priceFormingData.initialQuantity��,_orderState.priceFormingData.initialQuantity��_order.priceFormingData.price��"_orderState.priceFormingData.price��priceFormingData.tradedQuantity��)_order.priceFormingData.remainingQuantity��._orderState.priceFormingData.remainingQuantity��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��3tradersAlgosWaiversIndicators.shortSellingIndicator��-tradersAlgosWaiversIndicators.waiverIndicator��#transactionDetails.buySellIndicator��_order.transactionDetails.price��$_orderState.transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��!transactionDetails.settlementDate��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��!transactionDetails.upFrontPayment��)transactionDetails.upFrontPaymentCurrency��transactionDetails.venue��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��__fallback_buyer__��__fallback_seller__��__fallback_counterparty__��__fallback_client__��__fallback_buyer_dec_maker__��__fallback_seller_dec_maker__��__fallback_inv_dec_in_firm__��__fallback_trader__��__fallback_exec_within_firm__��__fallback_executing_entity__��marketIdentifiers��C_ASSET_CLASS(32)��
C_BBID(32)��F_CONTRACT_SIZE(20)��
C_FIGI(32)��C_PARSEKEY(30)��C_TICKER(10)��C_IDENTIFIER(12)��__bond_maturity_date__��__expiry_date_fallback__��__delivery_type__��__isin__��__instr_full_name__��__synth_order__��__is_created_through_fallback__��__newo_in_file__�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(h�N�start�K �stop�Ka�step�Ku��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KKa��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�CHMO�h�h�h�h�h�h�h��FILL�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��CAME�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��PARF�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�b�_dtype�h��StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KKa��h!�]�(�1239045�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��1238884�h�h�h�h�h�h�h�h�h�h�h��1238957�h�h�h�h�h�h�h�h�h�h��1254241�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��1251837�h�h�h�h�h�h�h�h�h�h�h�h�h�h��1244447�h�h�h�h�h��1249764�h�h�h�h��1245728�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKa��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKa��h��]�(�AGGREGATED FROM�ȟELECTRONIC WORKING ORDER��COMPETING QUOTE ENTERED�h�h�h�hΌELECTRONIC FILL OCCURRED��USER CHOSE: MANUAL ALLOCATION�h�ȟORDER ALLOCATED�hьLINK ALLOC BLOCK TO EXECUTIONS��TCTM RELEASED TO CTM��MOM ALLOCATION SENT��UNASSIGNED FROM TRADER��RELEASE TO FEEDS - ALLOC MATCH��GTD ORDER ROLLED OFF BLOTTER�h׌SELL ORDER PRE ALLOCATED��ASSIGNED TO TRADER��
ORDER PENDING�h͌CANCEL REQUEST��
CANCEL ACCEPT��ORDER ROLLED OVER TO NEXT DAY�h�hٌINSTRUCTIONS CHNGD��
AGGREGATED TO�h�h�h�h�h�h�h�h�h�h�h�h�h،CHANGED ASGND TRDR��TRADING DESK CHNGD��REASON CODE ASSIGNED��REMOVE FROM BASKET��ADDED TO BASKET�h݌E-SENT: ROUTED TO ELEC BRKR��E-WORK: ELEC BRKR ACKED��E-PFIL: ELEC PARTFILL OCCURRED�h�h�h�h�h�E-FILL: ELEC FILL OCCURRED��USER CHOSE: AUTO-ALLOCATION�h�h�h�h�h֌BUY ORDER PRE ALLOCATED��ASSIGNED TO TRADER��COMPETING QUOTE ENTERED�h�h�COMPETING QUOTE UPDATED�h�h�EXECUTION OCCURRED��AUTO - ALLOCATED��LINK ALLOC BLOCK TO EXECUTIONS��CXL/COR MASTER TICKET��ALLOCATION CANCELLED��CORRECTED ALLOCATION��UPD MASTER W/ ALLOC CHANGES��SELL ORDER PRE ALLOCATED��CHANGED ASGND TRDR��TRADING DESK CHNGD��REASON CODE ASSIGNED��REMOVE FROM BASKET��
AGGREGATED TO��ADDED TO BASKET��AGGREGATED FROM�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKa��h��]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKa��h��]�(�2.20E+15�j  j  j  j  j  j  j  �20220117P5646F7154566�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  � FXEMTX5646X1645516477X23068738X3�� FXEMTX5646X1645516477X23068738X4�� FXEMTX5646X1645516477X23068738X5�� FXEMTX5646X1645516477X23068738X6�� FXEMTX5646X1645516477X23068738X7�� FXEMTX5646X1645516477X23068738X8�� FXEMTX5646X1645516477X23068738X9�j  j  j  j  j  j  �2.20E+15�j  j  j  j  j  j  j  �20220218P5646F7252078�j  j  j  j  j  j  �2.20E+15�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKa��h��]�(�pandas._libs.missing��NA���j&  j&  j&  j&  j&  j&  j&  j  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j  j  j  j  j  j  j  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKa��h��]�(j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �XAMS�j0  j0  j0  j0  j0  j0  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKa��h��]�(j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j0  j0  j0  j0  j0  j0  j0  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKa��h��]�(j&  j&  �FICORP�j&  j&  j&  j&  j&  jC  j&  j&  j&  jC  jC  j&  jC  jC  j&  j&  jC  jC  jC  j&  jC  jC  jC  jC  jC  j&  j&  j&  j&  j&  jC  j&  jC  jC  jC  jC  j&  j&  j&  j&  j&  �EQUITY�jD  jD  jD  j&  j&  jD  jD  jD  j&  j&  j&  j&  j&  j&  jD  j&  jD  j&  jD  jD  j&  �	CDSWINDEX�j&  j&  j&  j&  j&  j&  j&  jE  jE  j&  jE  jE  jE  jE  �EQUITY�jF  jF  jF  j&  j&  j&  j&  j&  j&  j&  jF  jF  jF  j&  j&  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKa��h��]�(j&  j&  �COBR6433683�jP  jP  jP  jP  jP  jP  j&  j&  j&  jP  jP  j&  jP  jP  j&  j&  jP  jP  jP  j&  jP  jP  jP  jP  jP  j&  j&  j&  j&  j&  jP  j&  jP  jP  jP  jP  j&  j&  j&  j&  j&  �EQ0011594800001000�jQ  jQ  jQ  j&  j&  jQ  jQ  jQ  j&  j&  j&  j&  j&  j&  jQ  j&  jQ  j&  jQ  jQ  j&  �
COSPMS04ER�j&  jR  jR  jR  jR  jR  jR  jR  jR  j&  jR  jR  jR  jR  �EQ0000000062081068�jS  jS  jS  j&  j&  j&  j&  j&  j&  j&  jS  jS  jS  j&  j&  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKa��h��]�(j&  j&  �BBG012Q9D8Z6�j]  j]  j]  j]  j]  j]  j&  j&  j&  j]  j]  j&  j]  j]  j&  j&  j]  j]  j]  j&  j]  j]  j]  j]  j]  j&  j&  j&  j&  j&  j]  j&  j]  j]  j]  j]  j&  j&  j&  j&  j&  �BBG000BB3HV7�j^  j^  j^  j&  j&  j^  j^  j^  j&  j&  j&  j&  j&  j&  j^  j&  j^  j&  j^  j^  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �BBG00JRJQWR9�j_  j_  j_  j&  j&  j&  j&  j&  j&  j&  j_  j_  j_  j&  j&  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKa��h��]�(j&  j&  �BR643368     Corp�ji  ji  ji  ji  ji  ji  j&  j&  j&  ji  ji  j&  ji  ji  j&  j&  ji  ji  ji  j&  ji  ji  ji  ji  ji  j&  j&  j&  j&  j&  ji  j&  ji  ji  ji  ji  j&  j&  j&  j&  j&  �AALB NA Equity�jj  jj  jj  j&  j&  jj  jj  jj  j&  j&  j&  j&  j&  j&  jj  j&  jj  j&  jj  jj  j&  �
SPMS04ER Corp�j&  jk  jk  jk  jk  jk  jk  jk  jk  j&  jk  jk  jk  jk  �
DMP GR Equity�jl  jl  jl  j&  j&  j&  j&  j&  j&  j&  jl  jl  jl  j&  j&  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKa��h��]�(j&  j&  �MCD�jv  jv  jv  jv  jv  jv  j&  j&  j&  jv  jv  j&  jv  jv  j&  j&  jv  jv  jv  j&  jv  jv  jv  jv  jv  j&  j&  j&  j&  j&  jv  j&  jv  jv  jv  jv  j&  j&  j&  j&  j&  �AALB�jw  jw  jw  j&  j&  jw  jw  jw  j&  j&  j&  j&  j&  j&  jw  j&  jw  j&  jw  jw  j&  �/CDX36�j&  jx  jx  jx  jx  jx  jx  jx  jx  j&  jx  jx  jx  jx  �DMP�jy  jy  jy  j&  j&  j&  j&  j&  j&  j&  jy  jy  jy  j&  j&  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKa��h��]�(j&  j&  �XS2393236695�j�  j�  j�  j�  j�  j�  j&  j&  j&  j�  j�  j&  j�  j�  j&  j&  j�  j�  j�  j&  j�  j�  j�  j�  j�  j&  j&  j&  j&  j&  j�  j&  j�  j�  j�  j�  j&  j&  j&  j&  j&  �NL0000852564�j�  j�  j�  j&  j&  j�  j�  j�  j&  j&  j&  j&  j&  j&  j�  j&  j�  j&  j�  j�  j&  �	SPMS04ER8�j&  j�  j�  j�  j�  j�  j�  j�  j�  j&  j�  j�  j�  j�  �DE000A2GS5D8�j�  j�  j�  j&  j&  j&  j&  j&  j&  j&  j�  j�  j�  j&  j&  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KKa��h!�]�(�XS2393236695�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �NL0000852564�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �DE000A2GS5D8�j�  j�  j�  j�  j�  �DE000A2GS5D8�j&  j&  j&  j&  j�  j�  j�  j�  j�  et�bh�h�)��ubhhK ��h��R�(KKKa��h�b1�����R�(Kh"NNNJ����J����K t�b�B�                                                                                                                                                                                                                                                                              �t�bhhK ��h��R�(KKKa��h�i8�����R�(K�<�NNNJ����J����K t�b�B                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       7       8       9       :       ;       <       =       >       ?       @       A       B       C       D       E       F       G       H       I       J       K       L       M       N       O       P       Q       R       S       T       U       V       W       X       Y       Z       [       \       ]       ^       _       `       �t�bhhK ��h��R�(KKKKa��h!�]�(�2�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �1�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �SAMCo BBG AUDT�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
2022-01-17��
2022-01-17��
2022-01-17��
2022-01-17��
2022-01-17��
2022-01-17��
2022-01-17��
2022-01-17��
2022-01-17��
2022-01-17��
2022-01-17��
2022-01-17��
2022-01-17��
2022-01-17��
2022-01-17��
2022-01-17��
2022-01-17��
2022-01-17��
2022-01-17��
2022-01-17��
2022-01-17��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-01-14��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-21��
2022-02-18��
2022-02-18��
2022-02-18��
2022-02-18��
2022-02-18��
2022-02-18��
2022-02-18��
2022-02-18��
2022-02-18��
2022-02-18��
2022-02-18��
2022-02-18��
2022-02-18��
2022-02-18��
2022-02-18��
2022-02-07��
2022-02-07��
2022-02-07��
2022-02-07��
2022-02-07��
2022-02-07��
2022-02-07��
2022-02-07��
2022-02-07��
2022-02-07��
2022-02-07��
2022-02-07��
2022-02-07��
2022-02-07��
2022-02-07��
2022-02-07��SELL�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �BUYI�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �NEWO�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �MKT�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �MKT�j  j  j  j  j  j  j  j  j  j  j  j  j  j  �MKT�j  j  j  j  j  �MKT�j&  j&  j&  j&  j  j  j  j  j  X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-17T11:48:28.998567+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-17T11:48:29.047404+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: 7154538, 
Ticket Number: 7154566, 
Client Order ID: , 
Broker: BRK-TBD, 
Price: , 
Account: , 
Order Name: merge, 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 8, 
UTC Time: 2022-01-17T11:56:25.158121+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: 7154538, 
Ticket Number: 7154569, 
Client Order ID: , 
Broker: JANE-FI, 
Price: 96.186, 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: , 
Ident Type: 8, 
UTC Time: 2022-01-17T11:57:35.648385+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: 7154538, 
Ticket Number: 7154570, 
Client Order ID: , 
Broker: CG-FI, 
Price: 96.118, 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: , 
Ident Type: 8, 
UTC Time: 2022-01-17T11:57:35.714952+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: 7154538, 
Ticket Number: 7154571, 
Client Order ID: , 
Broker: RBC-FI, 
Price: 96.026, 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: , 
Ident Type: 8, 
UTC Time: 2022-01-17T11:57:35.775734+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: 7154538, 
Ticket Number: 7154572, 
Client Order ID: , 
Broker: BNP-FI, 
Price: 95.789, 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: , 
Ident Type: 8, 
UTC Time: 2022-01-17T11:57:35.837164+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: 7154538, 
Ticket Number: 7154573, 
Client Order ID: , 
Broker: ML-FI, 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: 8, 
UTC Time: 2022-01-17T11:57:35.898109+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: 7154538, 
Ticket Number: 7154566, 
Client Order ID: , 
Broker: BARC-FI, 
Price: 96.26, 
Account: , 
Order Name: merge, 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 8, 
UTC Time: 2022-01-17T11:57:35.959851+00:00, 
Venue Order ID: �X�  Function: OAX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-17T11:58:35.505153+00:00, 
Venue Order ID: �X�  Function: OAX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-17T11:58:35.738509+00:00, 
Venue Order ID: �X�  Function: OAX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-17T11:58:35.739011+00:00, 
Venue Order ID: �X�  Function: OAX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: 7154577, 
Ticket Number: 7154578, 
Client Order ID: , 
Broker: BARC-FI, 
Price: 96.26, 
Account: F01IGSEN, 
Order Name: merge, 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 8, 
UTC Time: 2022-01-17T11:58:36.249722+00:00, 
Venue Order ID: �X�  Function: OAX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: 7154577, 
Ticket Number: 7154579, 
Client Order ID: , 
Broker: BARC-FI, 
Price: 96.26, 
Account: F26IGSEN, 
Order Name: merge, 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 8, 
UTC Time: 2022-01-17T11:58:36.928967+00:00, 
Venue Order ID: �X�  Function: OAX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-17T11:58:37.420017+00:00, 
Venue Order ID: �X�  Function: TC, 
User: System, 
Audit ID: 2.20E+15, 
Master Number: 7154538, 
Ticket Number: 7154577, 
Client Order ID: , 
Broker: BARC-FI, 
Price: 96.26, 
Account: , 
Order Name: merge, 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 8, 
UTC Time: 2022-01-17T11:58:37.682717+00:00, 
Venue Order ID: �X�  Function: TC, 
User: System, 
Audit ID: 2.20E+15, 
Master Number: 7154538, 
Ticket Number: 7154577, 
Client Order ID: , 
Broker: BARC-FI, 
Price: 96.26, 
Account: , 
Order Name: merge, 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 8, 
UTC Time: 2022-01-17T11:58:37.698629+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-17T11:59:12.992596+00:00, 
Venue Order ID: �X�  Function: TCTM, 
User: System, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-17T12:02:44.299291+00:00, 
Venue Order ID: �X  Function: TSOX, 
User: SKIPPERS, 
Audit ID: 2.20E+15, 
Master Number: 7150676, 
Ticket Number: 7150677, 
Client Order ID: , 
Broker: BARC-FI, 
Price: 99.672, 
Account: F26IGSEN, 
Order Name: bilficre in case of cash needs, 
Order Instructions: S, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: bilficre in case of cash needs, 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 8, 
UTC Time: 2022-01-17T23:12:21.318382+00:00, 
Venue Order ID: �X  Function: TSOX, 
User: SKIPPERS, 
Audit ID: 2.20E+15, 
Master Number: 7151310, 
Ticket Number: 7151311, 
Client Order ID: , 
Broker: BARC-FI, 
Price: 99.672, 
Account: F01IGSEN, 
Order Name: sspf align exposures eu ig nf, 
Order Instructions: S, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: sspf align exposures eu ig nf, 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 8, 
UTC Time: 2022-01-17T23:12:21.322511+00:00, 
Venue Order ID: �X  Function: OI, 
User: SKIPPERS, 
Audit ID: 2.20E+15, 
Master Number: 7150676, 
Ticket Number: 7150677, 
Client Order ID: , 
Broker: , 
Price: 99.672, 
Account: F26IGSEN, 
Order Name: bilficre in case of cash needs, 
Order Instructions: cbbt ask, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: bilficre in case of cash needs, 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 8, 
UTC Time: 2022-01-14T12:56:48.419210+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-14T13:06:51.260635+00:00, 
Venue Order ID: �X  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: 7150676, 
Ticket Number: 7150697, 
Client Order ID: ********:5646:********:7053047735617847358:1, 
Broker: BRKR-TBD, 
Price: , 
Account: , 
Order Name: bilficre in case of cash needs, 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 8, 
UTC Time: 2022-01-14T13:07:24.289213+00:00, 
Venue Order ID: �XN  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: 7150676, 
Ticket Number: 7150677, 
Client Order ID: ********:5646:********:7053047735617847358:1, 
Broker: , 
Price: 99.672, 
Account: F26IGSEN, 
Order Name: bilficre in case of cash needs, 
Order Instructions: cbbt ask, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: bilficre in case of cash needs, 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 8, 
UTC Time: 2022-01-14T13:07:24.860452+00:00, 
Venue Order ID: ********�XF  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: 7150676, 
Ticket Number: 7150677, 
Client Order ID: ********:5646:********:7053047735617847358:1, 
Broker: , 
Price: 99.672, 
Account: F26IGSEN, 
Order Name: bilficre in case of cash needs, 
Order Instructions: cbbt ask, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: bilficre in case of cash needs, 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 8, 
UTC Time: 2022-01-14T15:30:29.928705+00:00, 
Venue Order ID: �XF  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: 7150676, 
Ticket Number: 7150677, 
Client Order ID: ********:5646:********:7053047735617847358:1, 
Broker: , 
Price: 99.672, 
Account: F26IGSEN, 
Order Name: bilficre in case of cash needs, 
Order Instructions: cbbt ask, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: bilficre in case of cash needs, 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 8, 
UTC Time: 2022-01-14T15:30:30.110013+00:00, 
Venue Order ID: �X  Function: TSOX, 
User: SKIPPERS, 
Audit ID: 2.20E+15, 
Master Number: 7150676, 
Ticket Number: 7150677, 
Client Order ID: , 
Broker: , 
Price: 99.672, 
Account: F26IGSEN, 
Order Name: bilficre in case of cash needs, 
Order Instructions: cbbt ask, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: bilficre in case of cash needs, 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 8, 
UTC Time: 2022-01-14T23:12:36.614011+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-17T10:55:39.063255+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-17T10:55:46.769351+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: S, 
Old Instruction: cbbt ask, 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-17T11:48:23.762243+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-17T11:48:29.042823+00:00, 
Venue Order ID: �X�  Function: OAX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-17T11:58:35.738766+00:00, 
Venue Order ID: �X3  Function: OI, 
User: SKIPPERS, 
Audit ID: 2.20E+15, 
Master Number: 7151310, 
Ticket Number: 7151311, 
Client Order ID: , 
Broker: , 
Price: 99.672, 
Account: F01IGSEN, 
Order Name: sspf align exposures eu ig nf, 
Order Instructions: market ask levels-MCD 0 7/8 10/04/33, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: sspf align exposures eu ig nf, 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 8, 
UTC Time: 2022-01-14T14:15:19.654916+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-14T14:16:25.921393+00:00, 
Venue Order ID: �X  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: 7151310, 
Ticket Number: 7151336, 
Client Order ID: ********:5646:********:7053065727235850431:1, 
Broker: BRKR-TBD, 
Price: , 
Account: , 
Order Name: sspf align exposures eu ig nf, 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 8, 
UTC Time: 2022-01-14T14:17:13.327870+00:00, 
Venue Order ID: �Xh  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: 7151310, 
Ticket Number: 7151311, 
Client Order ID: ********:5646:********:7053065727235850431:1, 
Broker: , 
Price: 99.672, 
Account: F01IGSEN, 
Order Name: sspf align exposures eu ig nf, 
Order Instructions: market ask levels-MCD 0 7/8 10/04/33, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: sspf align exposures eu ig nf, 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 8, 
UTC Time: 2022-01-14T14:17:14.374856+00:00, 
Venue Order ID: ********�X4  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: 7151310, 
Ticket Number: 7151311, 
Client Order ID: , 
Broker: , 
Price: 99.672, 
Account: F01IGSEN, 
Order Name: sspf align exposures eu ig nf, 
Order Instructions: market ask levels-MCD 0 7/8 10/04/33, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: sspf align exposures eu ig nf, 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 8, 
UTC Time: 2022-01-14T15:49:05.966171+00:00, 
Venue Order ID: �X5  Function: TSOX, 
User: SKIPPERS, 
Audit ID: 2.20E+15, 
Master Number: 7151310, 
Ticket Number: 7151311, 
Client Order ID: , 
Broker: , 
Price: 99.672, 
Account: F01IGSEN, 
Order Name: sspf align exposures eu ig nf, 
Order Instructions: market ask levels-MCD 0 7/8 10/04/33, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: sspf align exposures eu ig nf, 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 8, 
UTC Time: 2022-01-14T23:12:37.130702+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-17T10:55:39.320783+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-17T10:55:47.042849+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: S, 
Old Instruction: market ask levels-MCD 0 7/8 10/04/33, 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-17T11:48:10.189833+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-17T11:48:29.089530+00:00, 
Venue Order ID: �X�  Function: OAX, 
User: SZITMAN, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-01-17T11:58:35.739260+00:00, 
Venue Order ID: �X�  Function: OI, 
User: PSMID, 
Audit ID: 2.20E+15, 
Master Number: 7257253, 
Ticket Number: 7257254, 
Client Order ID: , 
Broker: , 
Price: 51.54, 
Account: E01SFIEU, 
Order Name: 220221_PS_E01SFIEU1, 
Order Instructions: MOC, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: 220221_PS_E01SFIEU1, 
Measurement Unit: 1.0, 
Trading desk: , 
Reason code: 4, 
Ident Type: 8, 
UTC Time: 2022-02-21T11:31:32.054225+00:00, 
Venue Order ID: �X�  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: 7257253, 
Ticket Number: 7257254, 
Client Order ID: , 
Broker: , 
Price: 51.54, 
Account: E01SFIEU, 
Order Name: 220221_PS_E01SFIEU1, 
Order Instructions: MOC, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: 220221_PS_E01SFIEU1, 
Measurement Unit: 1.0, 
Trading desk: , 
Reason code: 4, 
Ident Type: 8, 
UTC Time: 2022-02-21T11:41:54.630063+00:00, 
Venue Order ID: �X�  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: 7257253, 
Ticket Number: 7257254, 
Client Order ID: , 
Broker: , 
Price: 51.54, 
Account: E01SFIEU, 
Order Name: 220221_PS_E01SFIEU1, 
Order Instructions: MOC, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: 220221_PS_E01SFIEU1, 
Measurement Unit: 1.0, 
Trading desk: EQ-TD, 
Reason code: 4, 
Ident Type: 8, 
UTC Time: 2022-02-21T11:41:54.673121+00:00, 
Venue Order ID: �X�  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: 7257253, 
Ticket Number: 7257254, 
Client Order ID: , 
Broker: , 
Price: 51.54, 
Account: E01SFIEU, 
Order Name: 220221_PS_E01SFIEU1, 
Order Instructions: MOC, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: 220221_PS_E01SFIEU1, 
Measurement Unit: 1.0, 
Trading desk: EQ-TD, 
Reason code: 1, 
Ident Type: 8, 
UTC Time: 2022-02-21T16:23:31.346275+00:00, 
Venue Order ID: �X�  Function: EMS, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: 220221_PS_E01SFIEU1, 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-21T16:36:05.185552+00:00, 
Venue Order ID: �X�  Function: EMS, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: 220222_TAA_SSPF_EMEA, 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-21T16:36:05.234081+00:00, 
Venue Order ID: �X�  Function: EMSX, 
User: PSMID, 
Audit ID: 2.20E+15, 
Master Number: 7257253, 
Ticket Number: 7257254, 
Client Order ID: , 
Broker: , 
Price: 51.54, 
Account: E01SFIEU, 
Order Name: 220221_PS_E01SFIEU1, 
Order Instructions: MOC, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: 220221_PS_E01SFIEU1, 
Measurement Unit: 1.0, 
Trading desk: EQ-TD, 
Reason code: 1, 
Ident Type: 8, 
UTC Time: 2022-02-21T23:13:24.560508+00:00, 
Venue Order ID: �X  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: 7257253, 
Ticket Number: 7257254, 
Client Order ID: , 
Broker: CG-PT, 
Price: 51.54, 
Account: E01SFIEU, 
Order Name: 220221_PS_E01SFIEU1, 
Order Instructions: MOC, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: 220221_PS_E01SFIEU1, 
Measurement Unit: 1.0, 
Trading desk: EQ-TD, 
Reason code: 1, 
Ident Type: 8, 
UTC Time: 2022-02-22T07:54:50.791954+00:00, 
Venue Order ID: �X  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: 7257253, 
Ticket Number: 7257254, 
Client Order ID: , 
Broker: CG-PT, 
Price: 51.54, 
Account: E01SFIEU, 
Order Name: 220221_PS_E01SFIEU1, 
Order Instructions: MAN, 
Old Instruction: , 
Route Instructions(EMSX): MOC 22 Feb 2022, 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: 220221_PS_E01SFIEU1, 
Measurement Unit: 1.0, 
Trading desk: EQ-TD, 
Reason code: 90, 
Ident Type: 8, 
UTC Time: 2022-02-22T07:55:13.805820+00:00, 
Venue Order ID: �X�  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): MOC 22 Feb 2022, 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-22T16:35:04.264553+00:00, 
Venue Order ID: �X�  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): MOC 22 Feb 2022, 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-22T16:35:04.348262+00:00, 
Venue Order ID: �X�  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): MOC 22 Feb 2022, 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-22T16:35:04.431544+00:00, 
Venue Order ID: �X�  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): MOC 22 Feb 2022, 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-22T16:35:04.460765+00:00, 
Venue Order ID: �X�  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): MOC 22 Feb 2022, 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-22T16:35:04.489378+00:00, 
Venue Order ID: �X�  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): MOC 22 Feb 2022, 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-22T16:35:04.571378+00:00, 
Venue Order ID: �X  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: 7257253, 
Ticket Number: 7257254, 
Client Order ID: , 
Broker: , 
Price: 51.54, 
Account: E01SFIEU, 
Order Name: 220221_PS_E01SFIEU1, 
Order Instructions: MOC, 
Old Instruction: , 
Route Instructions(EMSX): MOC 22 Feb 2022, 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: 220221_PS_E01SFIEU1, 
Measurement Unit: 1.0, 
Trading desk: EQ-TD, 
Reason code: 1, 
Ident Type: 8, 
UTC Time: 2022-02-22T16:35:04.657056+00:00, 
Venue Order ID: �X�  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-22T16:48:41.110846+00:00, 
Venue Order ID: �X�  Function: OAX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: 7268699, 
Ticket Number: 7268702, 
Client Order ID: , 
Broker: CG-PT, 
Price: 51.38, 
Account: E01SFIEU, 
Order Name: 220221_PS_E01SFIEU1, 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1.0, 
Trading desk: , 
Reason code: 90, 
Ident Type: 8, 
UTC Time: 2022-02-22T16:49:10.853301+00:00, 
Venue Order ID: �X�  Function: OAX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-22T16:49:11.103383+00:00, 
Venue Order ID: �X�  Function: TC, 
User: System, 
Audit ID: 2.20E+15, 
Master Number: 7257253, 
Ticket Number: 7268699, 
Client Order ID: , 
Broker: CG-PT, 
Price: 51.38, 
Account: , 
Order Name: 220221_PS_E01SFIEU1, 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1.0, 
Trading desk: , 
Reason code: 90, 
Ident Type: 8, 
UTC Time: 2022-02-22T16:49:12.912092+00:00, 
Venue Order ID: �X�  Function: TC, 
User: System, 
Audit ID: 2.20E+15, 
Master Number: 7257253, 
Ticket Number: 7268699, 
Client Order ID: , 
Broker: CG-PT, 
Price: 51.38, 
Account: , 
Order Name: 220221_PS_E01SFIEU1, 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1.0, 
Trading desk: , 
Reason code: 90, 
Ident Type: 8, 
UTC Time: 2022-02-22T16:49:12.929702+00:00, 
Venue Order ID: �X�  Function: TCTM, 
User: System, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-22T16:56:03.065806+00:00, 
Venue Order ID: �X  Function: GSO, 
User: DCOLABUFO1, 
Audit ID: 2.20E+15, 
Master Number: 7251884, 
Ticket Number: 7251885, 
Client Order ID: , 
Broker: ML-SW, 
Price: -4.2106, 
Account: F01HYSMF, 
Order Name: Buy: /CDX36 100 12/2 2/18/22 11:45, 
Order Instructions: @ market, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 1, 
UTC Time: 2022-02-18T11:45:20.084147+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: PBONEKAMP1, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-18T11:47:11.880660+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: PBONEKAMP1, 
Audit ID: 2.20E+15, 
Master Number: 7251884, 
Ticket Number: 7252075, 
Client Order ID: , 
Broker: CG-SW, 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 1, 
UTC Time: 2022-02-18T15:22:03.374616+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: PBONEKAMP1, 
Audit ID: 2.20E+15, 
Master Number: 7251884, 
Ticket Number: 7252076, 
Client Order ID: , 
Broker: BARC-SW, 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 1, 
UTC Time: 2022-02-18T15:22:03.475243+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: PBONEKAMP1, 
Audit ID: 2.20E+15, 
Master Number: 7251884, 
Ticket Number: 7252077, 
Client Order ID: , 
Broker: ML-SW, 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 1, 
UTC Time: 2022-02-18T15:22:03.544261+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: PBONEKAMP1, 
Audit ID: 2.20E+15, 
Master Number: 7251884, 
Ticket Number: 7252075, 
Client Order ID: , 
Broker: CG-SW, 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 1, 
UTC Time: 2022-02-18T15:22:03.790919+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: PBONEKAMP1, 
Audit ID: 2.20E+15, 
Master Number: 7251884, 
Ticket Number: 7252076, 
Client Order ID: , 
Broker: BARC-SW, 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 1, 
UTC Time: 2022-02-18T15:22:03.813685+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: PBONEKAMP1, 
Audit ID: 2.20E+15, 
Master Number: 7251884, 
Ticket Number: 7252077, 
Client Order ID: , 
Broker: ML-SW, 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 1, 
UTC Time: 2022-02-18T15:22:03.836162+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: PBONEKAMP1, 
Audit ID: 2.20E+15, 
Master Number: 7251884, 
Ticket Number: 7252078, 
Client Order ID: , 
Broker: JPM-SW, 
Price: -4.2466, 
Account: , 
Order Name: Buy: /CDX36 100 12/2 2/18/22 11:45, 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 1, 
UTC Time: 2022-02-18T15:22:05.494623+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: PBONEKAMP1, 
Audit ID: 2.20E+15, 
Master Number: 7252079, 
Ticket Number: 7252080, 
Client Order ID: , 
Broker: JPM-SW, 
Price: -4.2466, 
Account: F01HYSMF, 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 1, 
UTC Time: 2022-02-18T15:22:14.612933+00:00, 
Venue Order ID: �X�  Function: TSOX, 
User: PBONEKAMP1, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-18T15:22:15.504405+00:00, 
Venue Order ID: �X�  Function: TC, 
User: PBONEKAMP1, 
Audit ID: 2.20E+15, 
Master Number: 7251884, 
Ticket Number: 7252090, 
Client Order ID: , 
Broker: JPM-SW, 
Price: -4.2466, 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 1, 
UTC Time: 2022-02-18T15:28:35.397302+00:00, 
Venue Order ID: �X�  Function: TC, 
User: PBONEKAMP1, 
Audit ID: 2.20E+15, 
Master Number: 7252079, 
Ticket Number: 7252080, 
Client Order ID: , 
Broker: JPM-SW, 
Price: -4.2466, 
Account: F01HYSMF, 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 1, 
UTC Time: 2022-02-18T15:28:35.635626+00:00, 
Venue Order ID: �X�  Function: TC, 
User: PBONEKAMP1, 
Audit ID: 2.20E+15, 
Master Number: 7252090, 
Ticket Number: 7252091, 
Client Order ID: , 
Broker: JPM-SW, 
Price: -4.2466, 
Account: F01HYSMF, 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 1, 
UTC Time: 2022-02-18T15:28:36.003374+00:00, 
Venue Order ID: �X�  Function: TC, 
User: PBONEKAMP1, 
Audit ID: 2.20E+15, 
Master Number: 7251884, 
Ticket Number: 7252090, 
Client Order ID: , 
Broker: JPM-SW, 
Price: -4.2466, 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: 1000.0, 
Trading desk: , 
Reason code: 13, 
Ident Type: 1, 
UTC Time: 2022-02-18T15:28:36.741347+00:00, 
Venue Order ID: �X�  Function: OI, 
User: PSMID, 
Audit ID: 2.20E+15, 
Master Number: 7209999, 
Ticket Number: 7210000, 
Client Order ID: , 
Broker: , 
Price: 69.55, 
Account: E12SFIEU, 
Order Name: 220207_PS_E12SFIEU1, 
Order Instructions: MOC, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: 220207_PS_E12SFIEU1, 
Measurement Unit: 1.0, 
Trading desk: , 
Reason code: 4, 
Ident Type: 8, 
UTC Time: 2022-02-07T10:06:22.580967+00:00, 
Venue Order ID: �X�  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: 7209999, 
Ticket Number: 7210000, 
Client Order ID: , 
Broker: , 
Price: 69.55, 
Account: E12SFIEU, 
Order Name: 220207_PS_E12SFIEU1, 
Order Instructions: MOC, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: 220207_PS_E12SFIEU1, 
Measurement Unit: 1.0, 
Trading desk: , 
Reason code: 4, 
Ident Type: 8, 
UTC Time: 2022-02-07T14:42:22.730640+00:00, 
Venue Order ID: �X�  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: 7209999, 
Ticket Number: 7210000, 
Client Order ID: , 
Broker: , 
Price: 69.55, 
Account: E12SFIEU, 
Order Name: 220207_PS_E12SFIEU1, 
Order Instructions: MOC, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: 220207_PS_E12SFIEU1, 
Measurement Unit: 1.0, 
Trading desk: EQ-TD, 
Reason code: 4, 
Ident Type: 8, 
UTC Time: 2022-02-07T14:42:22.774731+00:00, 
Venue Order ID: �X�  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: 7209999, 
Ticket Number: 7210000, 
Client Order ID: , 
Broker: , 
Price: 69.55, 
Account: E12SFIEU, 
Order Name: 220207_PS_E12SFIEU1, 
Order Instructions: MOC, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: 220207_PS_E12SFIEU1, 
Measurement Unit: 1.0, 
Trading desk: EQ-TD, 
Reason code: 1, 
Ident Type: 8, 
UTC Time: 2022-02-07T15:09:41.084819+00:00, 
Venue Order ID: �X�  Function: EMS, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: 220207_PS_E12SFIEU1, 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-07T15:20:42.413751+00:00, 
Venue Order ID: �X�  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-07T15:20:56.182127+00:00, 
Venue Order ID: �X�  Function: EMS, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: 220208_SCPF_EMEA, 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-07T15:20:42.471387+00:00, 
Venue Order ID: �X�  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-07T15:20:56.102433+00:00, 
Venue Order ID: �X�  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-07T15:20:56.144035+00:00, 
Venue Order ID: �X�  Function: EMS, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: 220208_SCPF_EMEA, 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-07T15:44:34.775605+00:00, 
Venue Order ID: �X�  Function: EMS, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: 220208_SCPF_EMEA_il, 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-07T15:44:34.808659+00:00, 
Venue Order ID: �X  Function: BDS, 
User: TULICEVIC3, 
Audit ID: 2.20E+15, 
Master Number: 7212650, 
Ticket Number: 7212651, 
Client Order ID: , 
Broker: , 
Price: 69.65, 
Account: E12SQIEU, 
Order Name: AE12SQIEU 1244996 2/ 7 11:18, 
Order Instructions: MAX10VOL, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: AE12SQIEU 1244996 2/ 7 11:18, 
Measurement Unit: 1.0, 
Trading desk: EQ-TD, 
Reason code: 1, 
Ident Type: 8, 
UTC Time: 2022-02-07T11:24:26.380565+00:00, 
Venue Order ID: �X  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: 7212650, 
Ticket Number: 7212651, 
Client Order ID: , 
Broker: , 
Price: 69.65, 
Account: E12SQIEU, 
Order Name: AE12SQIEU 1244996 2/ 7 11:18, 
Order Instructions: MAX10VOL, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: AE12SQIEU 1244996 2/ 7 11:18, 
Measurement Unit: 1.0, 
Trading desk: EQ-TD, 
Reason code: 1, 
Ident Type: 8, 
UTC Time: 2022-02-07T14:51:41.876788+00:00, 
Venue Order ID: �X  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: 7212650, 
Ticket Number: 7212651, 
Client Order ID: , 
Broker: , 
Price: 69.65, 
Account: E12SQIEU, 
Order Name: AE12SQIEU 1244996 2/ 7 11:18, 
Order Instructions: MAX10VOL, 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: AE12SQIEU 1244996 2/ 7 11:18, 
Measurement Unit: 1.0, 
Trading desk: EQ-TD, 
Reason code: 1, 
Ident Type: 8, 
UTC Time: 2022-02-07T15:10:23.834705+00:00, 
Venue Order ID: �X�  Function: EMS, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: AE12SQIEU 1244996 2/, 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-07T15:20:42.428189+00:00, 
Venue Order ID: �X�  Function: EMSX, 
User: RFONHOF, 
Audit ID: 2.20E+15, 
Master Number: , 
Ticket Number: , 
Client Order ID: , 
Broker: , 
Price: , 
Account: , 
Order Name: , 
Order Instructions: , 
Old Instruction: , 
Route Instructions(EMSX): , 
Execution Instructions: , 
Old Execution Instruction: , 
Basket Name: , 
Measurement Unit: , 
Trading desk: , 
Reason code: , 
Ident Type: , 
UTC Time: 2022-02-07T15:20:56.199417+00:00, 
Venue Order ID: �j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j&  j&  j&  j&  j&  j&  j&  j&  �DEAL�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �      �DEAL�j&  j&  j&  j&  j&  j&  j&  j&  j&  ]��GTCV�aj&  ]�j  aj&  j&  j&  j&  j&  ]�j  aj&  j&  j&  j&  j&  j&  j&  j&  j&  j&  ]�j  a]�j  aj~  j&  ]�j  a]�j  a]�j  a]�j  a]�j  aj&  j&  e(j&  j&  j&  ]�j  aj&  ]�j  a]�j  a]�j  a]�j  aj&  j&  j&  j&  j&  ]�j  a]�j  a]�j  a]�j  aj&  j&  ]�j  a]�j  a]�j  aj&  j&  j&  j&  j&  j&  ]�j  aj&  j&  j&  j&  j&  j&  ]�j  aj&  j&  j&  j&  j&  j&  j&  ]�j  aj&  j&  j&  j&  j&  j&  ]�j  a]�j  a]�j  a]�j  aj&  j&  ]��GTCV�aj&  j&  j&  j&  ]�j  a]�j  a]�j  aj&  j&  j&  j&  j&  j&  j&  j&  j&  j&  ]��ILQD�aj&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �
Standalone�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �Order�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
OrderState�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �1238884��1238957�j&  j&  j&  j&  j&  j&  j&  j&  j�  j�  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �1239045�j�  j&  j&  j&  j&  j&  j&  j&  j&  j&  j�  j�  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �1249764�j&  �1244447��1245728�j&  j&  j&  j&  j&  j&  j�  j&  j&  �3�j&  j&  j&  j&  j&  j�  j&  j&  j&  �1238970�j�  j&  j�  j�  j&  j&  �1238864��1238931�j�  j&  j�  j�  j�  j�  j�  j&  j&  j&  j&  j&  j�  j&  j�  j�  j�  j�  j&  j&  j&  j&  j&  �1254238�j�  j�  j�  j&  j&  j�  j�  j�  j&  j&  j&  j&  j&  j&  j�  j&  j�  j&  j�  j�  j&  j&  j&  j&  j&  j&  j&  j&  j&  j�  j&  j&  j&  j&  j&  j&  �1244413�j�  j�  j�  j&  j&  j&  j&  j&  j&  j&  �1244996�j�  j�  j&  j&  �1238884,1238957�j�  j&  j&  j&  j&  j&  j&  j&  j&  j�  j�  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �1244447,1245728�j�  j�  j&  j�  j&  j&  j&  j&  j&  �2�j�  j&  j&  j&  j&  j&  j&  j&  j&  j�  j�  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �1�j�  j&  j&  j&  j&  j&  j&  j&  j&  j&  �1�j�  j&  j&  j&  j&  j&  �1�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �1��1�j�  j�  j&  j�  j&  j&  j&  j&  �1��
2.20E+15|0��
2.20E+15|1��
2.20E+15|2��
2.20E+15|3��
2.20E+15|4��
2.20E+15|5��
2.20E+15|6��
2.20E+15|7��20220117P5646F7154566|8��
2.20E+15|9��2.20E+15|10��2.20E+15|11��2.20E+15|12��2.20E+15|13��2.20E+15|14��2.20E+15|15��2.20E+15|16��2.20E+15|17��2.20E+15|18��2.20E+15|19��2.20E+15|20��2.20E+15|21��2.20E+15|22��2.20E+15|23��2.20E+15|24��2.20E+15|25��2.20E+15|26��2.20E+15|27��2.20E+15|28��2.20E+15|29��2.20E+15|30��2.20E+15|31��2.20E+15|32��2.20E+15|33��2.20E+15|34��2.20E+15|35��2.20E+15|36��2.20E+15|37��2.20E+15|38��2.20E+15|39��2.20E+15|40��2.20E+15|41��2.20E+15|42��2.20E+15|43��2.20E+15|44��2.20E+15|45��2.20E+15|46��2.20E+15|47��2.20E+15|48��2.20E+15|49��2.20E+15|50��2.20E+15|51��2.20E+15|52��#FXEMTX5646X1645516477X23068738X3|53��#FXEMTX5646X1645516477X23068738X4|54��#FXEMTX5646X1645516477X23068738X5|55��#FXEMTX5646X1645516477X23068738X6|56��#FXEMTX5646X1645516477X23068738X7|57��#FXEMTX5646X1645516477X23068738X8|58��#FXEMTX5646X1645516477X23068738X9|59��2.20E+15|60��2.20E+15|61��2.20E+15|62��2.20E+15|63��2.20E+15|64��2.20E+15|65��2.20E+15|66��2.20E+15|67��2.20E+15|68��2.20E+15|69��2.20E+15|70��2.20E+15|71��2.20E+15|72��2.20E+15|73��20220218P5646F7252078|74��2.20E+15|75��2.20E+15|76��2.20E+15|77��2.20E+15|78��2.20E+15|79��2.20E+15|80��2.20E+15|81��2.20E+15|82��2.20E+15|83��2.20E+15|84��2.20E+15|85��2.20E+15|86��2.20E+15|87��2.20E+15|88��2.20E+15|89��2.20E+15|90��2.20E+15|91��2.20E+15|92��2.20E+15|93��2.20E+15|94��2.20E+15|95��2.20E+15|96�G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@y      G@y      G@y      G@y      G@y      G@y      G@y      G@y      G@y      G@y      G@y      G@y      G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     e(G@�     G@�     G@�     G@�     G@�     G@�     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@А�    G@А�    G@А�    G@А�    G@А�    G@А�    G@�t     G�      G�      G�      G�      G@�4     G@�4     G@�4     G@�4     G@�4     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@y      G@y      G@y      G@y      G@y      G@y      G@y      G@y      G@y      G@y      G@y      G@y      G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@��     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@�X     G@А�    G@А�    G@А�    G@А�    G@А�    G@А�    G�      G�      G�      G�      G�      G@�4     G@�4     G@�4     G@�4     G@�4     G@X�I�^j&  j&  G@X�l�C�G@X�O�;dG@X���l�G@W�~��"�j&  G@X��
=qj&  j&  j&  G@X��
=qG@X��
=qj&  G@X��
=qG@X��
=qj&  j&  G@X�I�^G@X�I�^G@X�I�^j&  j&  G@X�I�^G@X�I�^G@X�I�^G@X�I�^j&  j&  j&  j&  j&  G@X�I�^j&  j&  G@X�I�^G@X�I�^G@X�I�^j&  j&  j&  j&  j&  G@I��Q�G@I��Q�G@I��Q�G@I��Q�j&  j&  G@I��Q�G@I��Q�G@I��Q�G@I���
=qG@I���
=qG@I���
=qG@I���
=qG@I���
=qG@I���
=qG@I���
=qj&  G@I���
=qj&  G@I���
=qG@I���
=qj&  G�ק��&�j&  j&  j&  j&  j&  j&  j&  G������?G������?j&  G������?G������?G������?G������?G@Qc33333G@Qc33333G@Qc33333G@Qc33333j&  j&  G@Qi�����j&  j&  j&  j&  G@Qi�����G@Qi�����G@Qi�����j&  j&  j&  j&  j&  G@X�l�C�G@X�O�;dG@X���l�G@W�~��"�j&  G@X��
=qj&  j&  j&  G@X��
=qG@X��
=qj&  G@X��
=qG@X��
=qj&  j&  G@X�I�^G@X�I�^G@X�I�^j&  j&  G@X�I�^G@X�I�^G@X�I�^G@X�I�^j&  j&  j&  j&  j&  G@X�I�^j&  j&  G@X�I�^G@X�I�^G@X�I�^j&  j&  j&  j&  j&  G@I��Q�G@I��Q�G@I��Q�G@I��Q�j&  j&  G@I��Q�G@I��Q�G@I��Q�G@I���
=qG@I���
=qG@I���
=qG@I���
=qG@I���
=qG@I���
=qG@I���
=qj&  G@I���
=qj&  G@I���
=qG@I���
=qj&  G�ק��&�j&  j&  j&  j&  j&  j&  j&  G������?G������?j&  G������?G������?G������?G������?G@Qc33333G@Qc33333G@Qc33333G@Qc33333j&  j&  j&  j&  j&  j&  j&  G@Qi�����G@Qi�����G@Qi�����j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  G@��     j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  G@��     G?�      G@W      G@w@     G@��     G@�@     G@�8     j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  G@�X     j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  G@��     j&  j&  j&  j&  j&  j&  j&  G        j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  G@y      j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  G@��     j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  G@�     j&  j&  j&  j&  j&  j&  j&  j&  G@��     G@��     G@��     G@�
     G@�<     G@�8     G        j&  j&  j&  j&  j&  j&  G@�X     j&  j&  j&  j&  j&  j&  j&  G        j&  j&  j&  j&  j&  j&  G@А�    j&  j&  j&  j&  j&  G@�t     j&  j&  j&  j&  G@�4     j&  j&  j&  j&  G@��     j&  j&  j&  j&  j&  j&  j&  G        j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  G@y      j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  G@��     j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  G@�     j&  j&  j&  j&  j&  j&  j&  j&  G@��     G@��     G@��     G@�
     G@�<     G@�8     G        j&  j&  j&  j&  j&  j&  G@�X     j&  j&  j&  j&  j&  j&  j&  G        j&  j&  j&  j&  j&  j&  G@А�    j&  j&  j&  j&  j&  G        j&  j&  j&  j&  G@�4     j&  j&  j&  j&  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j   j  j  j  j  j  j  j  j  j	  j
  j  j  j
  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �	mock_path�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �2022-01-17T11:48:28.998567Z�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �2022-01-14T12:56:48.419210Z�j  j  j  j  j  j  j  j  j  j  j  �2022-01-14T14:15:19.654916Z�j   j   j   j   j   j   j   j   j   j   �2022-02-21T11:31:32.054225Z�j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  �2022-02-18T11:45:20.084147Z�j"  j"  j"  j"  j"  j"  j"  j"  j"  j"  j"  j"  j"  j"  �2022-02-07T10:06:22.580967Z�j#  j#  j#  j#  j#  �2022-02-07T15:20:42.471387Z�j$  j$  j$  j$  �2022-02-07T11:24:26.380565Z�j%  j%  j%  j%  j  �2022-01-17T11:48:29.047404Z��2022-01-17T11:56:25.158121Z��2022-01-17T11:57:35.648385Z��2022-01-17T11:57:35.714952Z��2022-01-17T11:57:35.775734Z��2022-01-17T11:57:35.837164Z��2022-01-17T11:57:35.898109Z��2022-01-17T11:57:35.959851Z��2022-01-17T11:58:35.505153Z��2022-01-17T11:58:35.738509Z��2022-01-17T11:58:35.739011Z��2022-01-17T11:58:36.249722Z��2022-01-17T11:58:36.928967Z��2022-01-17T11:58:37.420017Z��2022-01-17T11:58:37.682717Z��2022-01-17T11:58:37.698629Z��2022-01-17T11:59:12.992596Z��2022-01-17T12:02:44.299291Z��2022-01-17T23:12:21.318382Z��2022-01-17T23:12:21.322511Z�j  �2022-01-14T13:06:51.260635Z��2022-01-14T13:07:24.289213Z��2022-01-14T13:07:24.860452Z��2022-01-14T15:30:29.928705Z��2022-01-14T15:30:30.110013Z��2022-01-14T23:12:36.614011Z��2022-01-17T10:55:39.063255Z��2022-01-17T10:55:46.769351Z��2022-01-17T11:48:23.762243Z��2022-01-17T11:48:29.042823Z��2022-01-17T11:58:35.738766Z�j   �2022-01-14T14:16:25.921393Z��2022-01-14T14:17:13.327870Z��2022-01-14T14:17:14.374856Z��2022-01-14T15:49:05.966171Z��2022-01-14T23:12:37.130702Z��2022-01-17T10:55:39.320783Z��2022-01-17T10:55:47.042849Z��2022-01-17T11:48:10.189833Z��2022-01-17T11:48:29.089530Z��2022-01-17T11:58:35.739260Z�j!  �2022-02-21T11:41:54.630063Z��2022-02-21T11:41:54.673121Z��2022-02-21T16:23:31.346275Z��2022-02-21T16:36:05.185552Z��2022-02-21T16:36:05.234081Z��2022-02-21T23:13:24.560508Z��2022-02-22T07:54:50.791954Z��2022-02-22T07:55:13.805820Z��2022-02-22T16:35:04.264553Z��2022-02-22T16:35:04.348262Z��2022-02-22T16:35:04.431544Z��2022-02-22T16:35:04.460765Z��2022-02-22T16:35:04.489378Z��2022-02-22T16:35:04.571378Z��2022-02-22T16:35:04.657056Z��2022-02-22T16:48:41.110846Z��2022-02-22T16:49:10.853301Z��2022-02-22T16:49:11.103383Z��2022-02-22T16:49:12.912092Z��2022-02-22T16:49:12.929702Z��2022-02-22T16:56:03.065806Z�j"  �2022-02-18T11:47:11.880660Z��2022-02-18T15:22:03.374616Z��2022-02-18T15:22:03.475243Z��2022-02-18T15:22:03.544261Z��2022-02-18T15:22:03.790919Z��2022-02-18T15:22:03.813685Z��2022-02-18T15:22:03.836162Z��2022-02-18T15:22:05.494623Z��2022-02-18T15:22:14.612933Z��2022-02-18T15:22:15.504405Z��2022-02-18T15:28:35.397302Z��2022-02-18T15:28:35.635626Z��2022-02-18T15:28:36.003374Z��2022-02-18T15:28:36.741347Z�j#  �2022-02-07T14:42:22.730640Z��2022-02-07T14:42:22.774731Z��2022-02-07T15:09:41.084819Z��2022-02-07T15:20:42.413751Z��2022-02-07T15:20:56.182127Z�j$  �2022-02-07T15:20:56.102433Z��2022-02-07T15:20:56.144035Z�e(�2022-02-07T15:44:34.775605Z��2022-02-07T15:44:34.808659Z�j%  �2022-02-07T14:51:41.876788Z��2022-02-07T15:10:23.834705Z��2022-02-07T15:20:42.428189Z��2022-02-07T15:20:56.199417Z�j&  j&  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j&  j&  j&  j<  j<  j<  j<  j<  j<  j<  jC  jC  j&  j&  j&  jG  jG  jG  jG  jG  jG  jM  jM  j&  j&  j&  j&  j&  j&  j&  jU  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  jv  j&  j&  j&  j&  j&  j&  j&  j&  j&  j~  j&  j&  j&  j&  j&  j&  j&  j&  j-  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  jW  jX  jY  jZ  j[  j\  j]  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  jk  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j&  j&  j&  j&  j&  j&  j&  j&  ]�j�  aj&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j&  j&  j&  j&  j&  j&  j&  j&  G@X��
=qj&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  G@I���
=qG@I���
=qG@I���
=qG@I���
=qG@I���
=qG@I���
=qG@I���
=qj&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  G������?j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  G@X��
=qj&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  G@I���
=qG@I���
=qG@I���
=qG@I���
=qG@I���
=qG@I���
=qG@I���
=qj&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  G������?j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �EUR�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �USD�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �EUR�j�  j�  j�  j�  j�  �EUR�j&  j&  j&  j&  j�  j�  j�  j�  j�  �PERC�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �MONE�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �MONE�j&  j&  j&  j&  j�  j�  j�  j�  j�  j&  j&  j&  j&  j&  j&  j&  j&  G@��     j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  G@��     G?�      G@W      G@w@     G@��     G@�@     G@�8     j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  G@�X     j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  e(j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �NOML�j&  j�  j&  j&  j&  j&  j&  j�  j&  j&  j&  j�  j�  j&  j�  j�  j&  j&  j�  j�  j�  j&  j�  j�  j�  j�  j�  j&  j&  j&  j&  j&  j�  j&  j�  j�  j�  j�  j&  j&  j&  j&  j&  �UNIT�j�  j�  j�  j&  j&  j�  j�  j�  j&  j&  j&  j&  j&  j&  j�  j&  j�  j&  j�  j�  j&  j�  j&  j&  j&  j&  j&  j&  j&  j�  j�  j&  j�  j�  j�  j�  j�  j�  j�  j�  j&  j&  �UNIT�j&  j&  j&  j&  j�  j�  j�  j&  j&  �
2022-01-18�j&  �
2022-01-19��
2022-01-19��
2022-01-19��
2022-01-19��
2022-01-19��
2022-01-19��
2022-01-19�j&  j&  j&  �
2022-01-19��
2022-01-19�j&  �
2022-01-19��
2022-01-19�j&  j&  �
2022-01-19��
2022-01-19�j�  j&  �
2022-01-18��
2022-01-18��
2022-01-18��
2022-01-18��
2022-01-19�j&  j&  j&  j&  j&  �
2022-01-18�j&  �
2022-01-18��
2022-01-18��
2022-01-18��
2022-01-19�j&  j&  j&  j&  j&  �
2022-02-23��
2022-02-23��
2022-02-23��
2022-02-23�j&  j&  �
2022-02-24��
2022-02-24��
2022-02-24��
2022-02-24��
2022-02-24��
2022-02-24��
2022-02-24��
2022-02-24��
2022-02-24��
2022-02-24�j&  �
2022-02-24�j&  �
2022-02-24��
2022-02-24�j&  �
2022-02-19�j&  �
2022-02-19��
2022-02-19��
2022-02-19��
2022-02-19��
2022-02-19��
2022-02-19��
2022-02-19��
2022-02-19�j&  �
2022-02-19��
2022-02-19��
2022-02-19��
2022-02-19��
2022-02-09��
2022-02-09��
2022-02-09��
2022-02-09�j&  j&  �
2022-02-09�j&  j&  j&  j&  �
2022-02-09��
2022-02-09��
2022-02-09�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j|  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �DEAL�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j-  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  jW  jX  jY  jZ  j[  j\  j]  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  jk  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �-294738.9375�j&  j&  j&  j&  j&  j&  j&  �-297259�j�  j&  j�  j�  j�  j�  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �USD�j&  j&  j&  j&  j&  j&  j&  j�  j�  j&  j�  j�  j�  j�  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  ]�(}�(�labelId��id:brkr-tbd��path��buyer��type��se_schema.static.market��IdentifierType����ARRAY���R�u}�(j�  �lei:sample_lei�j�  �reportDetails.executingEntity�j�  j�  �OBJECT���R�u}�(j�  �lei:sample_lei�j�  �seller�j�  j�  u}�(j�  �id:brkr-tbd�j�  �counterparty�j�  j�  u}�(j�  �id:17992028�j�  �:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�j�  j�  u}�(j�  �id:********�j�  �trader�j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �
id:brk-tbd�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �
id:brk-tbd�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �
id:barc-fi�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �
id:barc-fi�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �
id:barc-fi�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �
id:barc-fi�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:01�j�  �clientIdentifiers.client�j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �
id:barc-fi�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:26�j�  j�  j�  j�  u}�(j�  �
id:barc-fi�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:26�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �
id:barc-fi�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �
id:barc-fi�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �
id:barc-fi�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �
id:barc-fi�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �
id:barc-fi�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:26�j�  j�  j�  j�  u}�(j�  �
id:barc-fi�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:26�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �
id:barc-fi�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �
id:barc-fi�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:26�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:26�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:26�j�  j�  j�  j�  u}�(j�  �id:26�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �id:brkr-tbd�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:26�j�  j�  j�  j�  u}�(j�  �id:brkr-tbd�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:26�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:26�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:26�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:26�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:26�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:26�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:26�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:26�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:26�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:26�j�  j�  j�  j�  u}�(j�  �id:26�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:26�j�  j�  j�  j�  u}�(j�  �id:26�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:26�j�  j�  j�  j�  u}�(j�  �id:26�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:26�j�  j�  j�  j�  u}�(j�  �id:26�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:26�j�  j�  j�  j�  u}�(j�  �id:26�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �id:brkr-tbd�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:brkr-tbd�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:17992028�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �id:********�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:23985681�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:23985681�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:23985681�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:23985681�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:23985681�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �id:cg-pt�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:cg-pt�j�  j�  j�  j�  u}�(j�  �id:23985681�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �id:cg-pt�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:cg-pt�j�  j�  j�  j�  u}�(j�  �id:23985681�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:23985681�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �id:cg-pt�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:cg-pt�j�  j�  j�  j�  u}�(j�  �id:23985681�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �id:cg-pt�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:cg-pt�j�  j�  j�  j�  u}�(j�  �id:23985681�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �id:cg-pt�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:cg-pt�j�  j�  j�  j�  u}�(j�  �id:23985681�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:ml-sw�j�  j�  j�  j�  u}�(j�  �id:ml-sw�j�  j�  j�  j�  u}�(j�  �id:10356430�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:3233146�j�  j�  j�  j�  ue]�(}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:3233146�j�  j�  j�  j�  ue]�(}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:3233146�j�  j�  j�  j�  ue]�(}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:3233146�j�  j�  j�  j�  ue]�(}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:3233146�j�  j�  j�  j�  ue]�(}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:cg-sw�j�  j�  j�  j�  u}�(j�  �id:cg-sw�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:3233146�j�  j�  j�  j�  ue]�(}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �
id:barc-sw�j�  j�  j�  j�  u}�(j�  �
id:barc-sw�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:3233146�j�  j�  j�  j�  ue]�(}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:ml-sw�j�  j�  j�  j�  u}�(j�  �id:ml-sw�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:3233146�j�  j�  j�  j�  ue]�(}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:jpm-sw�j�  j�  j�  j�  u}�(j�  �	id:jpm-sw�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:3233146�j�  j�  j�  j�  ue]�(}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:jpm-sw�j�  j�  j�  j�  u}�(j�  �	id:jpm-sw�j�  j�  j�  j�  u}�(j�  �id:10356430�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:3233146�j�  j�  j�  j�  ue]�(}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:3233146�j�  j�  j�  j�  ue]�(}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:jpm-sw�j�  j�  j�  j�  u}�(j�  �	id:jpm-sw�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:3233146�j�  j�  j�  j�  ue]�(}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:jpm-sw�j�  j�  j�  j�  u}�(j�  �	id:jpm-sw�j�  j�  j�  j�  u}�(j�  �id:10356430�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:3233146�j�  j�  j�  j�  ue]�(}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:jpm-sw�j�  j�  j�  j�  u}�(j�  �	id:jpm-sw�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:3233146�j�  j�  j�  j�  ue]�(}�(j�  �id:01�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:jpm-sw�j�  j�  j�  j�  u}�(j�  �	id:jpm-sw�j�  j�  j�  j�  u}�(j�  �id:01�j�  j[  j�  j�  u}�(j�  �
id:3233146�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:12�j�  j�  j�  j�  u}�(j�  �id:23985681�j�  j�  j�  j�  u}�(j�  �id:12�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:12�j�  j�  j�  j�  u}�(j�  �id:23985681�j�  j�  j�  j�  u}�(j�  �id:12�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:12�j�  j�  j�  j�  u}�(j�  �id:23985681�j�  j�  j�  j�  u}�(j�  �id:12�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:12�j�  j�  j�  j�  u}�(j�  �id:23985681�j�  j�  j�  j�  u}�(j�  �id:12�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:12�j�  j�  j�  j�  u}�(j�  �id:12�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:12�j�  j�  j�  j�  u}�(j�  �id:12�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �
id:7292237�j�  j�  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:12�j�  j�  j�  j�  u}�(j�  �
id:7292237�j�  j�  j�  j�  u}�(j�  �id:12�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:12�j�  j�  j�  j�  u}�(j�  �
id:7292237�j�  j�  j�  j�  u}�(j�  �id:12�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:12�j�  j�  j�  j�  u}�(j�  �
id:7292237�j�  j�  j�  j�  u}�(j�  �id:12�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:12�j�  j�  j�  j�  u}�(j�  �id:12�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:12�j�  j�  j�  j�  u}�(j�  �id:12�j�  j[  j�  j�  u}�(j�  �
id:8082391�j�  j�  j�  j�  ue�lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��id:brkr-tbd�j&  �
id:brk-tbd�j&  j&  j&  j&  j&  �
id:barc-fi�j&  j&  j&  �
id:barc-fi��
id:barc-fi�j&  �
id:barc-fi��
id:barc-fi�j&  j&  �
id:barc-fi��
id:barc-fi�j&  j&  �id:brkr-tbd�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �id:brkr-tbd�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �id:cg-pt��id:cg-pt�j&  j&  j&  j&  j&  j&  j&  j&  �id:cg-pt�j&  �id:cg-pt��id:cg-pt�j&  �id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��id:01��id:26��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��id:26��id:01��id:26��id:26��id:26��id:26��id:26��id:26��id:26��id:26��id:26��id:26��id:26��id:26��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01�e(�id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:ml-sw�j&  j&  j&  j&  �id:cg-sw��
id:barc-sw��id:ml-sw��	id:jpm-sw��	id:jpm-sw�j&  �	id:jpm-sw��	id:jpm-sw��	id:jpm-sw��	id:jpm-sw��id:12��id:12��id:12��id:12��id:12��id:12��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��id:12��id:12��id:12��id:12��id:12��id:brkr-tbd�j&  �
id:brk-tbd�j&  j&  j&  j&  j&  �
id:barc-fi�j&  j&  j&  �
id:barc-fi��
id:barc-fi�j&  �
id:barc-fi��
id:barc-fi�j&  j&  �
id:barc-fi��
id:barc-fi�j&  j&  �id:brkr-tbd�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �id:brkr-tbd�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �id:cg-pt��id:cg-pt�j&  j&  j&  j&  j&  j&  j&  j&  �id:cg-pt�j&  �id:cg-pt��id:cg-pt�j&  �id:ml-sw�j&  j&  j&  j&  �id:cg-sw��
id:barc-sw��id:ml-sw��	id:jpm-sw��	id:jpm-sw�j&  �	id:jpm-sw��	id:jpm-sw��	id:jpm-sw��	id:jpm-sw�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �id:17992028�j&  �id:17992028��id:17992028��id:17992028��id:17992028��id:17992028��id:17992028��id:17992028�j&  j&  j&  �id:17992028��id:17992028�j&  �id:17992028��id:17992028�j&  j&  �id:17992028��id:17992028��id:17992028�j&  �id:17992028��id:17992028��id:17992028��id:17992028��id:17992028�j&  j&  j&  j&  j&  �id:17992028�j&  �id:17992028��id:17992028��id:17992028��id:17992028�j&  j&  j&  j&  j&  �id:23985681��id:23985681��id:23985681��id:23985681�j&  j&  �id:23985681��id:23985681��id:23985681�j&  j&  j&  j&  j&  j&  �id:23985681�j&  �id:23985681�j&  �id:23985681��id:23985681�j&  �id:10356430�j&  j&  j&  j&  j&  j&  j&  j&  �id:10356430�j&  j&  �id:10356430�j&  j&  �id:23985681��id:23985681��id:23985681��id:23985681�j&  j&  �
id:7292237�j&  j&  j&  j&  �
id:7292237��
id:7292237��
id:7292237�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �id:01��id:26�j&  j&  j&  j&  j&  �id:26��id:01��id:26��id:26��id:26��id:26��id:26��id:26��id:26��id:26��id:26��id:26��id:26��id:26��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:01��id:12��id:12��id:12��id:12��id:12��id:12�j&  j&  j&  j&  j&  �id:12��id:12��id:12��id:12��id:12��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��id:********��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:3233146��
id:3233146��
id:3233146��
id:3233146��
id:3233146��
id:3233146��
id:3233146��
id:3233146��
id:3233146��
id:3233146��
id:3233146��
id:3233146��
id:3233146��
id:3233146��
id:3233146��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391�j&  j&  j&  j&  �
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��brkr-tbd�j&  �brk-tbd�j&  j&  j&  j&  j&  �barc-fi�j&  j&  j&  �barc-fi��barc-fi�j&  �barc-fi��barc-fi�j&  j&  �barc-fi��barc-fi�j&  j&  �brkr-tbd�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �brkr-tbd�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �cg-pt��cg-pt�j&  j&  j&  j&  j&  j&  j&  j&  �cg-pt�j&  �cg-pt��cg-pt�j&  �01��01��01��01��01��01��01��01��01��01��01��01��01��01��01�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��01��26��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��26��01��26��26��26��26��26��26��26��26��26��26��26��26��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��ml-sw�j&  j&  j&  j&  �cg-sw��barc-sw��ml-sw��jpm-sw��jpm-sw�j&  �jpm-sw��jpm-sw��jpm-sw��jpm-sw��12��12��12��12��12��12��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��12��12��12��12��12��brkr-tbd�j&  �brk-tbd�j&  j&  j&  j&  j&  �barc-fi�j&  j&  j&  �barc-fi��barc-fi�j&  �barc-fi��barc-fi�j&  j&  �barc-fi��barc-fi�j&  j&  �brkr-tbd�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �brkr-tbd�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �cg-pt��cg-pt�j&  j&  j&  j&  j&  j&  j&  j&  �cg-pt�j&  �cg-pt��cg-pt�j&  �ml-sw�j&  j&  j&  j&  �cg-sw��barc-sw��ml-sw��jpm-sw��jpm-sw�j&  �jpm-sw��jpm-sw��jpm-sw��jpm-sw�j&  j&  e(j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �01��26�j&  j&  j&  j&  j&  �26��01��26��26��26��26��26��26��26��26��26��26��26��26��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��01��12��12��12��12��12��12�j&  j&  j&  j&  j&  �12��12��12��12��12�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �17992028�j&  �17992028��17992028��17992028��17992028��17992028��17992028��17992028�j&  j&  j&  �17992028��17992028�j&  �17992028��17992028�j&  j&  �17992028��17992028��17992028�j&  �17992028��17992028��17992028��17992028��17992028�j&  j&  j&  j&  j&  �17992028�j&  �17992028��17992028��17992028��17992028�j&  j&  j&  j&  j&  �23985681��23985681��23985681��23985681�j&  j&  �23985681��23985681��23985681�j&  j&  j&  j&  j&  j&  �23985681�j&  �23985681�j&  �23985681��23985681�j&  �10356430�j&  j&  j&  j&  j&  j&  j&  j&  �10356430�j&  j&  �10356430�j&  j&  �23985681��23985681��23985681��23985681�j&  j&  �7292237�j&  j&  j&  j&  �7292237��7292237��7292237�j&  j&  �********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��********��8082391��8082391��8082391��8082391��8082391��8082391��8082391��8082391��8082391��8082391��8082391��8082391��8082391��8082391��8082391��8082391��8082391��8082391��8082391��8082391��8082391��8082391��3233146��3233146��3233146��3233146��3233146��3233146��3233146��3233146��3233146��3233146��3233146��3233146��3233146��3233146��3233146��8082391��8082391��8082391��8082391��8082391��8082391��8082391�j&  j&  j&  j&  �8082391��8082391��8082391��8082391��8082391�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei�]�(j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  e]�(j   j  j  j  e]�(j	  j  j
  j  e]�(j  j  j  j  e]�(j  j  j  j!  e]�(j$  j&  j(  j*  e]�(j-  j/  j1  j3  j5  j7  e]�(j:  j<  j>  e]�(jA  jC  jE  e]�(jH  jJ  jL  e]�(jO  jQ  jS  jU  jW  jY  j\  e]�(j_  ja  jc  je  jg  ji  jk  e]�(jn  jp  jr  e]�(ju  jw  jy  j{  j}  j  e]�(j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  e]�(j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j  j  j  j  e]�(j
  j  j  j  e]�(j  j  j  j  e]�(j  j  j   j"  e]�(j%  j'  j)  j+  e]�(j.  j0  j2  j4  e]�(j7  j9  j;  j=  j?  e]�(jB  jD  jF  jH  e]�(jK  jM  jO  jQ  jS  jU  jW  e]�(jZ  j\  j^  j`  jb  e]�(je  jg  ji  jk  jm  e]�(jp  jr  jt  jv  jx  e]�(j{  j}  j  j�  e]�(j�  j�  j�  j�  e]�(j�  j�  j�  j�  e]�(j�  j�  j�  j�  e]�(j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  e]�(j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j   j  j  j  j  j
  j  e]�(j  j  j  j  e]�(j  j  j  j  e]�(j!  j#  j%  j'  e]�(j*  j,  j.  j0  e]�(j3  j5  j7  j9  e]�(j<  j>  j@  jB  e]�(jE  jG  jI  jK  jM  e]�(jP  jR  jT  jV  e]�(jY  j[  j]  j_  ja  jc  je  e]�(jh  jj  jl  jn  e]�(jq  js  ju  jw  jy  j{  j}  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  e]�(j�  j�  j�  j�  e]�(j�  j�  j�  j�  e]�(j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  e]�(j�  j  j  j  j  j	  j  e]�(j  j  j  j  e]�(j  j  j  j  j  j!  e]�(j$  j&  j(  j*  j,  j.  j0  e]�(j3  j5  j7  j9  j;  j=  e]�(j@  jB  jD  jF  jH  jJ  e]�(jM  jO  jQ  jS  jU  e]�(jX  jZ  j\  j^  j`  e]�(jc  je  jg  ji  jk  e]�(jn  jp  jr  jt  jv  e]�(jy  j{  j}  j  e]�(j�  j�  j�  j�  e]�(j�  j�  j�  j�  e]�(j�  j�  e]�(j�  j�  e]�(j�  j�  e]�(j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  e]�(j�  j�  j�  j�  eG�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      j&  j&  �
2033-10-04�j&  j&  j&  j&  j&  �
2033-10-04�j&  j&  j&  �
2033-10-04��
2033-10-04�j&  �
2033-10-04��
2033-10-04�j&  j&  �
2033-10-04��
2033-10-04��
2033-10-04�j&  �
2033-10-04��
2033-10-04��
2033-10-04��
2033-10-04��
2033-10-04�j&  j&  j&  j&  j&  �
2033-10-04�j&  �
2033-10-04��
2033-10-04��
2033-10-04��
2033-10-04�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  e(j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �
2026-12-20�j&  j&  j&  j&  j&  j&  j&  �
2026-12-20��
2026-12-20�j&  �
2026-12-20��
2026-12-20��
2026-12-20��
2026-12-20�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �CASH�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �#MCDONALD'S CORP(MCD 0.875 10/04/33)�j&  �#MCDONALD'S CORP(MCD 0.875 10/04/33)��#MCDONALD'S CORP(MCD 0.875 10/04/33)��#MCDONALD'S CORP(MCD 0.875 10/04/33)��#MCDONALD'S CORP(MCD 0.875 10/04/33)��#MCDONALD'S CORP(MCD 0.875 10/04/33)��#MCDONALD'S CORP(MCD 0.875 10/04/33)��#MCDONALD'S CORP(MCD 0.875 10/04/33)�j&  j&  j&  �#MCDONALD'S CORP(MCD 0.875 10/04/33)��#MCDONALD'S CORP(MCD 0.875 10/04/33)�j&  �#MCDONALD'S CORP(MCD 0.875 10/04/33)��#MCDONALD'S CORP(MCD 0.875 10/04/33)�j&  j&  �#MCDONALD'S CORP(MCD 0.875 10/04/33)��#MCDONALD'S CORP(MCD 0.875 10/04/33)�j�  j&  �#MCDONALD'S CORP(MCD 0.875 10/04/33)��#MCDONALD'S CORP(MCD 0.875 10/04/33)��#MCDONALD'S CORP(MCD 0.875 10/04/33)��#MCDONALD'S CORP(MCD 0.875 10/04/33)��#MCDONALD'S CORP(MCD 0.875 10/04/33)�j&  j&  j&  j&  j&  �#MCDONALD'S CORP(MCD 0.875 10/04/33)�j&  �#MCDONALD'S CORP(MCD 0.875 10/04/33)��#MCDONALD'S CORP(MCD 0.875 10/04/33)��#MCDONALD'S CORP(MCD 0.875 10/04/33)��#MCDONALD'S CORP(MCD 0.875 10/04/33)�j&  j&  j&  j&  j&  �AALBERTS NV(AALB NA)��AALBERTS NV(AALB NA)��AALBERTS NV(AALB NA)��AALBERTS NV(AALB NA)�j&  j&  �AALBERTS NV(AALB NA)��AALBERTS NV(AALB NA)��AALBERTS NV(AALB NA)�j&  j&  j&  j&  j&  j&  �AALBERTS NV(AALB NA)�j&  �AALBERTS NV(AALB NA)�j&  �AALBERTS NV(AALB NA)��AALBERTS NV(AALB NA)�j&  �ML-SW(/CDX36 100 12/20/26)��>      j&  �ML-SW(/CDX36 100 12/20/26)��ML-SW(/CDX36 100 12/20/26)��ML-SW(/CDX36 100 12/20/26)��ML-SW(/CDX36 100 12/20/26)��ML-SW(/CDX36 100 12/20/26)��ML-SW(/CDX36 100 12/20/26)��ML-SW(/CDX36 100 12/20/26)��ML-SW(/CDX36 100 12/20/26)�j&  �ML-SW(/CDX36 100 12/20/26)��/CDX36 100 12/20/26�j  �ML-SW(/CDX36 100 12/20/26)��DERMAPHARM HOLDING SE(DMP GR)��DERMAPHARM HOLDING SE(DMP GR)��DERMAPHARM HOLDING SE(DMP GR)��DERMAPHARM HOLDING SE(DMP GR)�j&  j&  �DERMAPHARM HOLDING SE(DMP GR)�j&  j&  j&  j&  �DERMAPHARM HOLDING SE(DMP GR)��DERMAPHARM HOLDING SE(DMP GR)��DERMAPHARM HOLDING SE(DMP GR)�j&  j&  et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hvat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hwat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hyat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hzat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h{at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h|at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h)h�h�h�et�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bh�Nu��R�h
h}�(hhhK ��h��R�(KKK��h!�]�(h%h&h'h(h*h,h-h.h/h0h1h2h3h6h7h9h:h;h>h@hAhBhChDhEhFhGhHhJhKhLhMhNhOhPhQhRhShThUhVhWhXhYhZh[h]h^h`hahbhchdhehfhghhhihjhkhlhmhnhohphqhrhshthuhxh}h~hh�et�bh�Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j)  j�  j�  K7K8K��R�u}�(j�  j3  j�  j�  K:K;K��R�u}�(j�  j<  j�  j�  KQKRK��R�u}�(j�  jI  j�  j�  KRKSK��R�u}�(j�  jV  j�  j�  KTKUK��R�u}�(j�  jb  j�  j�  KUKVK��R�u}�(j�  jo  j�  j�  KVKWK��R�u}�(j�  j|  j�  j�  KWKXK��R�u}�(j�  j�  j�  j�  K[K\K��R�u}�(j�  j�  j�  hhK ��h��R�(KK��j�  �C        ]       ^       _       �t�bu}�(j�  j�  j�  j�  K$K%K��R�u}�(j�  j�  j�  hhK ��h��R�(KKK��j�  �BX                                                    	       
                     
                                                                                                   !       "       #       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       8       9       ;       <       =       >       ?       @       A       B       C       D       E       F       G       H       I       J       K       L       M       N       O       P       S       X       Y       Z       \       �t�bueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.