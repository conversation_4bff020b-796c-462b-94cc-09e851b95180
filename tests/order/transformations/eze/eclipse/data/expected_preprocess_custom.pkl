���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�'__AGGREGATED_DESTINATION_DISPLAY_NAME__��3__AGGREGATED_DESTINATION_DISPLAY_NAME_WITH_PREFIX__��__AGGREGATED_TRADER__��!__AGGREGATED_TRADER_WITH_PREFIX__��.__AGGREGATED_INVESTMENT_DECISION_WITHIN_FIRM__��:__AGGREGATED_INVESTMENT_DECISION_WITHIN_FIRM_WITH_PREFIX__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(�0                                                                      	       
                     
                                                                                                                                             !       "       #       $       %       �h�i8�����R�(K�<�NNNJ����J����K t�bK&���C�t�R�h,�__swarm_raw_index__�u��R�e]�hhK ��h��R�(KKK&��h!�]�(�GSCO��GSCO��MSCO��MSCO��MSCO��MSCO��MSCO��MSCO��MSCO��MSCO��GSCO��GSCO��GSCO��GSCO��RLHN��MSCO��MSCO��MSCO��MSCO��MSCO��MSCO��GSCO��GSCO��GSCO��MSCO�ha�GSCO��GSCO��GSCO��GSCO�hbhb�BAML��GSCO��GSCO��MSCO��MSCO�hW�id:GSCO��id:GSCO��id:MSCO��id:MSCO��id:MSCO��id:MSCO��id:MSCO��id:MSCO��id:MSCO��id:MSCO��id:GSCO��id:GSCO��id:GSCO��id:GSCO��id:RLHN��id:MSCO��id:MSCO��id:MSCO��id:MSCO��id:MSCO��id:MSCO��id:GSCO��id:GSCO��id:GSCO��id:MSCO��id:MSCO��id:GSCO��id:GSCO��id:GSCO��id:GSCO��id:GSCO��id:GSCO��id:BAML��id:GSCO��id:GSCO��id:MSCO��id:MSCO��id:RLHN��OTD��OTD��OTD��OTD��OTD��OTD��OTD��OTD��OTD��OTD��OTD��OTD��OTD��OTD��pandas._libs.missing��NA����OTD��OTD��OTD��OTD��OTD��OTD��OTD��OTD��OTD��OTD�h�h��OTD��OTD��OTD�h�h�h��OTD��OTD��OTD��OTD�h��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD�h��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD��id:OTD�h��GTG��GTG��GTG��GTG��GTG��GTG�h�h�h�h��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:OTD��id:OTD��id:OTD��id:OTD��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG��id:GTG�et�ba]�h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(h)h*et�bh,Nu��R�a}��0.14.1�}�(�axes�h
�blocks�]�}�(�values�hF�mgr_locs��builtins��slice���K KK��R�uaust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.