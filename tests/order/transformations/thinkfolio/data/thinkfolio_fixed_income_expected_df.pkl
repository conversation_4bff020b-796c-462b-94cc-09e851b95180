��:      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKC��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��!executionDetails.settlementAmount�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	hierarchy��orderIdentifiers.orderIdCode��	_order.id��_orderState.id��_order.__meta_model__��_orderState.__meta_model__��orderIdentifiers.parentOrderId��!orderIdentifiers.transactionRefNo��priceFormingData.price�� transactionDetails.priceCurrency��#transactionDetails.quantityCurrency��+_orderState.priceFormingData.tradedQuantity�� priceFormingData.initialQuantity��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.orderSubmitted��$_order.timestamps.orderStatusUpdated��)_orderState.timestamps.orderStatusUpdated��timestamps.orderReceived��&_orderState.timestamps.tradingDateTime��#transactionDetails.buySellIndicator��transactionDetails.price�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityNotation��transactionDetails.recordType��#transactionDetails.settlementAmount��"transactionDetails.tradingCapacity��._orderState.transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��isin_attribute��notional_currency_2_attribute��venue_attribute��#instrument_classification_attribute��expiry_date_attribute��swap_near_leg_date_attribute��currency_attribute��asset_class_attribute��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��__is_created_through_fallback__��__newo_in_file__��__classification__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK9��h�i8�����R�(K�<�NNNJ����J����K t�b�B�                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       7       8       �t�bhi�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK9��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�Market�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�b�_dtype�h�StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK9��h��]�(�AOTC�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK9��h��]�(�2850683��2955870��2956169��2947999��2949976��2955493��2953947��2956267��2955060��2955974��2955722��2956076��2956078��2955735��2955741��2955749��2956168��2956200��2956207��2956208��2956209��2956210��2956217��2956220��2956223��2956224��2956225��2956226��2956227��2956228��2956229��2956230��2956231��2956232��2956233��2956234��2956235��2956236��2956237��2956238��2956239��2956240��2956241��2956242��2956243��2956246��2956251��2956247��2956254��2956248��2956249��2956250��2956525��2956526��2956527��2956528��2956529�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK9��h��]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK9��h��]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK9��h��]�(�2850683��2945761�h��2947999��2949014��2953373��2953947�h��2955060��2955446��2955722�j  j  �2955735��2955741��2955749��2956168��2956200��2956207��2956208��2956209��2956210��2956217��2956220��2956223��2956224��2956225��2956226��2956227��2956228��2956229��2956230��2956231��2956232��2956233��2956234��2956235��2956236��2956237��2956238��2956239��2956240��2956241��2956242��2956243��2956246�j"  �2956247�j#  �2956248��2956249�j%  �2956525��2956526��2956527��2956528��2956529�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK9��h��]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK9��h��]�(�RU000A0ZYYN4��USP8718AAN65�j>  �US84265VAH87��US50066CAU36��KR103502GD64��XS1883879006�jB  �XS2216900105��XS2214238441��XS2236363227�jE  jE  �XS2328261263��XS2592028091��USY6080GAB33��IDP000004509��ZAG000016320��USY20721BU20��US71654QDD16��US71654QCC42��XS1056560920��XS2623560781��XS0551307100��US698299BB98��US731011AW25��XS1837994794��XS1696899035��US731011AV42��XS2270576965��US715638BM30��USY20721AL30��XS2155352664��US715638AP79��US77586TAE64��XS1694218469��USY20721AJ83��US760942BA98��XS1694217495��US195325BK01��US718286BG11��US718286BB24��US445545AL04��USY20721AE96��US91087BAX82��USP29853AA99�jf  �USC3535CAP35�jg  jP  �USP0R80BAG79�jh  �XS2306962841��US71647NAN93��XS1061043367��USP9401CAA01��USA35155AB50�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK9��h!�]�(�	id:bar-gb��	id:spg-gb��	id:spg-gb��	id:spg-gb��
id:db-degb��id:hsb-hkkr��	id:cgm-gb��	id:cgm-gb��	id:jef-gb��	id:lfd-ae��
id:db-degb��	id:nom-gb��	id:nom-gb��	id:anz-au��	id:cgm-gb��	id:cgm-gb��id:stc-gbid��id:ms-gb��	id:bnp-fr��	id:jef-gb��	id:jef-gb��	id:jef-gb��id:ml-gb��	id:hsb-gb��
id:db-degb��	id:cgm-gb��
id:db-degb��	id:abu-ae��	id:cgm-gb��	id:hsb-gb��	id:hsb-gb��	id:stc-gb��	id:jef-gb��	id:hsb-gb��	id:ing-nl��	id:stc-gb��id:ms-gb��	id:cgm-gb��	id:mzh-gb��	id:cgm-gb��id:ml-gb��id:ms-gb��	id:rai-at��id:ml-gb��id:ml-gb��	id:hsb-gb��	id:hsb-gb��id:ms-gb��id:ms-gb��id:ml-gb��	id:bar-gb��	id:bar-gb��	id:cgm-gb��	id:mxs-gb��	id:hsb-gb��	id:jef-gb��id:jpm-usgb�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK9��h!�]�(�id:xux��
id:hodgesr��
id:hodgesr��
id:hodgesr��	id:chingc��	id:osheaj��id:christiansent��id:christiansent��	id:chingc��
id:samieia��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��	id:kumara��id:berishab��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��
id:edmondsona��
id:edmondsona��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK9��h!�]�(�id:sanjuans��
id:hodgesr��
id:hodgesr��
id:hodgesr��	id:chingc��	id:osheaj��id:sanjuans��id:sanjuans��	id:chingc��id:sanjuans��	id:chingc��	id:chingc��	id:chingc��	id:chingc��	id:chingc��	id:chingc��
id:apriyantid��
id:carvera��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��
id:hodgesr��
id:hodgesr��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��
id:hodgesr��
id:hodgesr��
id:hodgesr��
id:hodgesr��
id:hodgesr��
id:hodgesr��
id:hodgesr��
id:hodgesr��
id:hodgesr��
id:hodgesr��
id:hodgesr��
id:hodgesr�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK9��h!�]�(�id:sanjuans��
id:hodgesr��
id:hodgesr��
id:hodgesr��	id:chingc��	id:osheaj��id:sanjuans��id:sanjuans��	id:chingc��id:sanjuans��	id:chingc��	id:chingc��	id:chingc��	id:chingc��	id:chingc��	id:chingc��
id:apriyantid��
id:carvera��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��
id:hodgesr��
id:hodgesr��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��
id:hodgesr��
id:hodgesr��
id:hodgesr��
id:hodgesr��
id:hodgesr��
id:hodgesr��
id:hodgesr��
id:hodgesr��
id:hodgesr��
id:hodgesr��
id:hodgesr��
id:hodgesr�et�bh�h�)��ubhhK ��h��R�(KKK9��h�b1�����R�(Kh"NNNJ����J����K t�b�Cr                                                         �t�bhhK ��h��R�(KKK9��h�f8�����R�(KhwNNNJ����J����K t�b�Bx      ��&A   @PA   ��`RA     �=A   @5IA  F�]�A    �`RA   ��GQA    |DA    @�&A    PAfff�k�VA   ��PA    �A3333�? A    �A  �L�'BR��>�2A     AA    �'�@    `��@    @HA    �P2A    R3A     \A    ��@    �p�@    `�A    @9�@    ��A    آA     ��@     �	A����,zA3333�w�@    ��A     ��@    ���@    �<A    `�@    @Y�@    0��@ffff�z�@    `��@    ��A    �(Aq=
W)�1A     �/A    p�/A    ��6A    @&,A    8 3A    �� A    ВA    d�A    �8A    ��A     �D@     �Q@�G�z�Q@     @X@�����	Y@\���(\X@fffffX@33333#X@     @U@     �B@��ʡU@�rh��U@�rh��U@�����,V@i��|?Y@fffff�W@fffff�X@��#�Z@     �W@33333�P@������N@��Q��S@������X@������X@������R@����̜Y@fffffV@:��v�V@     �X@=
ףpT@n���qY@     P_@)\���(X@E���Ԉ_@�~j�t�X@�Q��KV@������\@������X@�~j�t�W@33333�X@�z�G\@������]@�Zd;�X@=
ףp5`@fffff�Y@33333sX@33333sX@      Z@��Q��Y@�Q���X@���(\W@���(\W@��Q��U@���Q�U@fffffVW@�z�G�V@     �X@    @w;A    ldVA    ��YA    ��>A    \+IA   ��*�A    �SA    �QA    ��GA    ��>A    �SA    [A    �SA    `"A    �, A    @$A   �vH'B    <�1A     jA     j�@     j�@     jA    �v2A    �`3A     jA     L�@     ��@     jA     L�@     jA    �OA     j�@     �
A     L�@     ��@     jA     j�@     ��@     jA     j�@     j�@     j�@     ��@     j�@     jA    ��.A    ��5A    ��.A    ��.A    `�6A    ��.A    p�4A    �9#A     e!A    ��A     jA     �A    @w;A    ��yA    ��yA    ��QA    @�mA  ���� B    l|bA    l|bA    ��GA    �
A    �IpA    �IpA    �IpA    `"A    �, A    @$A    _�2B    <�1A     jA     j�@     j�@     jA    �v2A    �`3A     jA     L�@     ��@     jA     L�@     jA    �OA     j�@     �
A     L�@     ��@     jA     j�@     ��@     jA     j�@     j�@     j�@     ��@     j�@     jA    �BA    �BA    ��>A    ��>A    `�6A    ��AA    ��AA    �9#A     e!A    ��A     jA     �A     �D@     �Q@�G�z�Q@     @X@�����	Y@\���(\X@fffffX@33333#X@     @U@     �B@��ʡU@�rh��U@�rh��U@�����,V@i��|?Y@fffff�W@fffff�X@��#�Z@     �W@33333�P@������N@��Q��S@������X@������X@������R@����̜Y@fffffV@:��v�V@     �X@=
ףpT@n���qY@     P_@)\���(X@E���Ԉ_@�~j�t�X@�Q��KV@������\@������X@�~j�t�W@33333�X@�z�G\@������]@�Zd;�X@=
ףp5`@fffff�Y@33333sX@33333sX@      Z@��Q��Y@�Q���X@���(\W@���(\W@��Q��U@���Q�U@fffffVW@�z�G�V@     �X@    @w;A    ldVA    ��YA    ��>A    \+IA   ��*�A    �SA    �QA    ��GA    ��>A    �SA    [A    �SA    `"A    �, A    @$A   �vH'B    <�1A     jA     j�@     j�@     jA    �v2A    �`3A     jA     L�@     ��@     jA     L�@     jA    �OA     j�@     �
A     L�@     ��@     jA     j�@     ��@     jA     j�@     j�@     j�@     ��@     j�@     jA    ��.A    ��5A    ��.A    ��.A    `�6A    ��.A    p�4A    �9#A     e!A    ��A     jA     �A    ��&A   @PA   ��`RA     �=A   @5IA  F�]�A    �`RA   ��GQA    |DA    @�&A    PAfff�k�VA   ��PA    �A3333�? A    �A  �L�'BR��>�2A     AA    �'�@    `��@    @HA    �P2A    R3A     \A    ��@    �p�@    `�A    @9�@    ��A    آA     ��@     �	A����,zA3333�w�@    ��A     ��@    ���@    �<A    `�@    @Y�@    0��@ffff�z�@    `��@    ��A    �(Aq=
W)�1A     �/A    p�/A    ��6A    @&,A    8 3A    �� A    ВA    d�A    �8A    ��A�t�bhhK ��h��R�(KKK9��hv�B�                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       7       8       �t�bhhK ��h��R�(KK-K9��h!�]�(�2��1�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
Thinkfolio�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-19��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��
2023-07-20��SELL��BUYI�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �NEWO�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �FILL�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  ]��GTCV�aj�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
Standalone�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �Order�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
OrderState�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �	2850683_0��	2955870_1��	2956169_2��	2947999_3��	2949976_4��	2955493_5��	2953947_6��	2956267_7��	2955060_8��	2955974_9��
2955722_10��
2956076_11��
2956078_12��
2955735_13��
2955741_14��
2955749_15��
2956168_16��
2956200_17��
2956207_18��
2956208_19��
2956209_20��
2956210_21��
2956217_22��
2956220_23��
2956223_24��
2956224_25��
2956225_26��
2956226_27��
2956227_28��
2956228_29��
2956229_30��
2956230_31��
2956231_32��
2956232_33��
2956233_34��
2956234_35��
2956235_36��
2956236_37��
2956237_38��
2956238_39��
2956239_40��
2956240_41��
2956241_42��
2956242_43��
2956243_44��
2956246_45��
2956251_46��
2956247_47��
2956254_48��
2956248_49��
2956249_50��
2956250_51��
2956525_52��
2956526_53��
2956527_54��
2956528_55��
2956529_56��USD�j  j  j  j  �KRW�j  j  j  j  j  j  j  j  j  j  �IDR��ZAR�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j   j  j  j  j  j  j  j  j  j	  j
  j  j  j
  j  j  j  j  j  j  j  �+dummy/path/TEST_MARKITFI_TRD_1_20230616.pkl�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �2023-07-20T16:36:00.000000Z��2023-06-29T12:16:29.000000Z��2023-07-20T14:06:00.000000Z��2023-06-29T12:16:29.000000Z��2023-07-20T02:40:00.000000Z��2023-07-20T05:45:01.000000Z��2023-07-20T13:46:00.000000Z��2023-07-20T13:48:00.000000Z��2023-07-20T08:15:00.000000Z��2023-07-19T07:53:00.000000Z��2023-07-20T02:43:00.000000Z��2023-07-20T07:28:00.000000Z��2023-07-20T02:50:00.000000Z��2023-07-20T02:08:37.000000Z��2023-07-20T02:04:00.000000Z��2023-07-20T01:40:27.000000Z��2023-07-20T08:17:00.000000Z��2023-07-20T09:37:45.000000Z��2023-07-20T13:17:52.000000Z��2023-07-20T13:17:52.000000Z��2023-07-20T13:17:52.000000Z��2023-07-20T13:17:52.000000Z��2023-07-20T10:56:07.000000Z��2023-07-20T10:56:07.000000Z��2023-07-20T11:00:38.000000Z��2023-07-20T11:00:38.000000Z��2023-07-20T11:00:38.000000Z��2023-07-20T11:00:38.000000Z��2023-07-20T11:00:38.000000Z��2023-07-20T11:00:39.000000Z��2023-07-20T13:17:52.000000Z��2023-07-20T13:17:52.000000Z��2023-07-20T11:00:39.000000Z��2023-07-20T13:17:53.000000Z��2023-07-20T11:00:39.000000Z��2023-07-20T11:00:39.000000Z��2023-07-20T13:17:53.000000Z��2023-07-20T13:17:53.000000Z��2023-07-20T11:00:39.000000Z��2023-07-20T13:17:53.000000Z��2023-07-20T13:17:53.000000Z��2023-07-20T13:17:53.000000Z��2023-07-20T11:00:38.000000Z��2023-07-20T13:17:52.000000Z��2023-07-20T13:17:52.000000Z��2023-07-20T11:55:47.000000Z��2023-07-20T12:10:00.000000Z��2023-07-20T12:26:53.000000Z��2023-07-20T12:16:14.000000Z��2023-07-20T11:55:47.000000Z��2023-07-20T11:55:47.000000Z��2023-07-20T12:07:00.000000Z��2023-07-20T15:52:59.000000Z��2023-07-20T15:52:59.000000Z��2023-07-20T15:52:59.000000Z��2023-07-20T15:53:00.000000Z��2023-07-20T15:53:00.000000Z��2022-09-21T14:45:53.000000Z��2023-06-23T16:50:55.000000Z��2023-06-23T16:50:55.000000Z��2023-06-21T17:12:15.000000Z��2023-07-03T06:23:42.000000Z��2023-07-12T15:16:01.000000Z��2023-07-14T10:03:33.000000Z��2023-07-14T10:03:33.000000Z��2023-07-18T03:29:21.000000Z��2023-07-17T14:13:12.000000Z��2023-07-19T10:49:32.000000Z��2023-07-19T10:49:32.000000Z��2023-07-19T10:49:32.000000Z��2023-07-19T11:08:11.000000Z��2023-07-19T11:11:51.000000Z��2023-07-19T11:11:53.000000Z��2023-07-20T08:13:30.000000Z��2023-07-20T09:27:55.000000Z��2023-07-20T09:36:41.000000Z��2023-07-20T09:36:41.000000Z��2023-07-20T09:36:41.000000Z��2023-07-20T09:36:41.000000Z��2023-07-20T10:28:59.000000Z��2023-07-20T10:31:55.000000Z��2023-07-20T10:57:35.000000Z��2023-07-20T10:57:35.000000Z��2023-07-20T10:57:35.000000Z��2023-07-20T10:57:35.000000Z��2023-07-20T10:57:35.000000Z��2023-07-20T10:57:35.000000Z��2023-07-20T10:57:35.000000Z�e(�2023-07-20T10:57:36.000000Z��2023-07-20T10:57:36.000000Z��2023-07-20T10:57:36.000000Z��2023-07-20T10:57:36.000000Z��2023-07-20T10:57:36.000000Z��2023-07-20T10:57:36.000000Z��2023-07-20T10:57:36.000000Z��2023-07-20T10:57:36.000000Z��2023-07-20T10:57:36.000000Z��2023-07-20T10:57:36.000000Z��2023-07-20T10:57:36.000000Z��2023-07-20T10:57:37.000000Z��2023-07-20T10:57:37.000000Z��2023-07-20T10:57:37.000000Z��2023-07-20T11:25:03.000000Z��2023-07-20T11:25:03.000000Z��2023-07-20T11:26:31.000000Z��2023-07-20T11:26:31.000000Z��2023-07-20T11:28:43.000000Z��2023-07-20T11:30:57.000000Z��2023-07-20T11:30:57.000000Z��2023-07-20T15:17:58.000000Z��2023-07-20T15:32:24.000000Z��2023-07-20T15:34:01.000000Z��2023-07-20T15:35:05.000000Z��2023-07-20T15:36:17.000000Z��2023-07-20T16:36:00.000000Z��2023-07-20T09:15:48.000000Z��2023-07-20T14:06:00.000000Z��2023-07-20T16:43:10.000000Z��2023-07-20T02:40:00.000000Z��2023-07-20T05:45:01.000000Z��2023-07-20T13:46:00.000000Z��2023-07-20T13:48:00.000000Z��2023-07-20T08:15:00.000000Z��2023-07-19T07:53:00.000000Z��2023-07-20T02:43:00.000000Z��2023-07-20T07:28:00.000000Z��2023-07-20T02:50:00.000000Z��2023-07-20T02:08:37.000000Z��2023-07-20T02:04:00.000000Z��2023-07-20T01:52:48.000000Z��2023-07-20T08:17:00.000000Z��2023-07-20T09:37:45.000000Z��2023-07-20T13:17:52.000000Z��2023-07-20T13:17:52.000000Z��2023-07-20T13:17:52.000000Z��2023-07-20T13:17:52.000000Z��2023-07-20T10:56:07.000000Z��2023-07-20T10:56:07.000000Z��2023-07-20T11:00:38.000000Z��2023-07-20T11:00:38.000000Z��2023-07-20T11:00:38.000000Z��2023-07-20T11:00:38.000000Z��2023-07-20T11:00:38.000000Z��2023-07-20T11:00:39.000000Z��2023-07-20T13:17:52.000000Z��2023-07-20T13:17:52.000000Z��2023-07-20T11:00:39.000000Z��2023-07-20T13:17:53.000000Z��2023-07-20T11:00:39.000000Z��2023-07-20T11:00:39.000000Z��2023-07-20T13:17:53.000000Z��2023-07-20T13:17:53.000000Z��2023-07-20T11:00:39.000000Z��2023-07-20T13:17:53.000000Z��2023-07-20T13:17:53.000000Z��2023-07-20T13:17:53.000000Z��2023-07-20T11:00:38.000000Z��2023-07-20T13:17:52.000000Z��2023-07-20T13:17:52.000000Z��2023-07-20T11:55:47.000000Z��2023-07-20T12:10:00.000000Z��2023-07-20T12:26:53.000000Z��2023-07-20T12:16:14.000000Z��2023-07-20T11:55:47.000000Z��2023-07-20T11:55:47.000000Z��2023-07-20T12:07:00.000000Z��2023-07-20T15:52:59.000000Z��2023-07-20T15:52:59.000000Z��2023-07-20T15:52:59.000000Z��2023-07-20T15:53:00.000000Z��2023-07-20T15:53:00.000000Z�jS  jT  jU  jV  jW  jX  jY  jZ  j[  j\  j]  j^  j_  j`  ja  jb  jc  jd  je  jf  jg  jh  ji  jj  jk  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  jy  jz  j{  j|  j}  j~  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �PERC�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �NOML�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �Market Side�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �XOFF�j�  j�  j�  j�  �MAEL�j�  j�  j�  j�  j�  j�  j�  �BMTF�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  ]�(}�(�labelId�j=  �path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(j�  �RU000A0ZYYN4USDXXXX�j�  j�  j�  j�  ue]�(}�(j�  j>  j�  j�  j�  j�  u}�(j�  �USP8718AAN65USDXXXX�j�  j�  j�  j�  ue]�(}�(j�  j>  j�  j�  j�  j�  u}�(j�  �USP8718AAN65USDXXXX�j�  j�  j�  j�  ue]�(}�(j�  j?  j�  j�  j�  j�  u}�(j�  �US84265VAH87USDXXXX�j�  j�  j�  j�  ue]�(}�(j�  j@  j�  j�  j�  j�  u}�(j�  �US50066CAU36USDXXXX�j�  j�  j�  j�  ue]�(}�(j�  jA  j�  j�  j�  j�  u}�(j�  �KR103502GD64KRWMAEL�j�  j�  j�  j�  ue]�(}�(j�  jB  j�  j�  j�  j�  u}�(j�  �XS1883879006USDXXXX�j�  j�  j�  j�  ue]�(}�(j�  jB  j�  j�  j�  j�  u}�(j�  �XS1883879006USDXXXX�j�  j�  j�  j�  ue]�(}�(j�  jC  j�  j�  j�  j�  u}�(j�  �XS2216900105USDXXXX�j�  j�  j�  j�  ue]�(}�(j�  jD  j�  j�  j�  j�  u}�(j�  �XS2214238441USDXXXX�j�  j�  j�  j�  ue]�(}�(j�  jE  j�  j�  j�  j�  u}�(j�  �XS2236363227USDXXXX�j�  j�  j�  j�  ue]�(}�(j�  jE  j�  j�  j�  j�  u}�(j�  �XS2236363227USDXXXX�j�  j�  j�  j�  ue]�(}�(j�  jE  j�  j�  j�  j�  u}�(j�  �XS2236363227USDXXXX�j�  j�  j�  j�  ue]�(}�(j�  jF  j�  j�  j�  j�  u}�(j�  �XS2328261263USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  jG  j�  j�  j�  j�  u}�(j�  �XS2592028091USDXXXX�j�  j�  j�  j�  ue]�(}�(j�  jH  j�  j�  j�  j�  u}�(j�  �USY6080GAB33USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  jI  j�  j�  j�  j�  u}�(j�  �IDP000004509IDRXXXX�j�  j�  j�  j�  ue]�(}�(j�  jJ  j�  j�  j�  j�  u}�(j�  �ZAG000016320ZARMAEL�j�  j�  j�  j�  ue]�(}�(j�  jK  j�  j�  j�  j�  u}�(j�  �USY20721BU20USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  jL  j�  j�  j�  j�  u}�(j�  �US71654QDD16USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  jM  j�  j�  j�  j�  u}�(j�  �US71654QCC42USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  jN  j�  j�  j�  j�  u}�(j�  �XS1056560920USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  jO  j�  j�  j�  j�  u}�(j�  �XS2623560781USDMAEL�j�  j�  j�  j�  ue]�(}�(j�  jP  j�  j�  j�  j�  u}�(j�  �XS0551307100USDMAEL�j�  j�  j�  j�  ue]�(}�(j�  jQ  j�  j�  j�  j�  u}�(j�  �US698299BB98USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  jR  j�  j�  j�  j�  u}�(j�  �US731011AW25USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  jS  j�  j�  j�  j�  u}�(j�  �XS1837994794USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  jT  j�  j�  j�  j�  u}�(j�  �XS1696899035USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  jU  j�  j�  j�  j�  u}�(j�  �US731011AV42USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  jV  j�  j�  j�  j�  u}�(j�  �XS2270576965USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  jW  j�  j�  j�  j�  u}�(j�  �US715638BM30USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  jX  j�  j�  j�  j�  u}�(j�  �USY20721AL30USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  jY  j�  j�  j�  j�  u}�(j�  �XS2155352664USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  jZ  j�  j�  j�  j�  u}�(j�  �US715638AP79USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  j[  j�  j�  j�  j�  u}�(j�  �US77586TAE64USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  j\  j�  j�  j�  j�  u}�(j�  �XS1694218469USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  j]  j�  j�  j�  j�  u}�(j�  �USY20721AJ83USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  j^  j�  j�  j�  j�  u}�(j�  �US760942BA98USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  j_  j�  j�  j�  j�  u}�(j�  �XS1694217495USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  j`  j�  j�  j�  j�  u}�(j�  �US195325BK01USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  ja  j�  j�  j�  j�  u}�(j�  �US718286BG11USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  jb  j�  j�  j�  j�  u}�(j�  �US718286BB24USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  jc  j�  j�  j�  j�  u}�(j�  �US445545AL04USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  jd  j�  j�  j�  j�  u}�(j�  �USY20721AE96USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  je  j�  j�  j�  j�  u}�(j�  �US91087BAX82USDBMTF�j�  j�  j�  j�  ue]�(}�(j�  jf  j�  j�  j�  j�  u}�(j�  �USP29853AA99USDMAEL�j�  j�  j�  j�  ue]�(}�(j�  jf  j�  j�  j�  j�  u}�(j�  �USP29853AA99USDXXXX�j�  j�  j�  j�  ue]�(}�(j�  jg  j�  j�  j�  j�  u}�(j�  �USC3535CAP35USDXXXX�j�  j�  j�  j�  ue]�(}�(j�  jg  j�  j�  j�  j�  u}�(j�  �USC3535CAP35USDMAEL�j�  j�  j�  j�  ue]�(}�(j�  jP  j�  j�  j�  j�  u}�(j�  �XS0551307100USDMAEL�j�  j�  j�  j�  ue]�(}�(j�  jh  j�  j�  j�  j�  u}�(j�  �USP0R80BAG79USDMAEL�j�  j�  j�  j�  ue]�(}�(j�  jh  j�  j�  j�  j�  u}�(j�  �USP0R80BAG79USDXXXX�j�  j�  j�  j�  ue]�(}�(j�  ji  j�  j�  j�  j�  u}�(j�  �XS2306962841USDMAEL�j�  j�  j�  j�  ue]�(}�(j�  jj  j�  j�  j�  j�  u}�(j�  �US71647NAN93USDMAEL�j�  j�  j�  j�  ue]�(}�(j�  jk  j�  j�  j�  j�  u}�(j�  �XS1061043367USDMAEL�j�  j�  j�  j�  ue]�(}�(j�  jl  j�  j�  j�  j�  u}�(j�  �USP9401CAA01USDMAEL�j�  j�  j�  j�  ue]�(}�(j�  jm  j�  j�  j�  j�  u}�(j�  �USA35155AB50USDMAEL�j�  j�  j�  j�  ue�pandas._libs.missing��NA���j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �bond�j�  j�  j�  j�  e(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  ]�(}�(j�  �	id:bar-gb�j�  �buyer�j�  j�  �ARRAY���R�u}�(j�  �lei:test_executing_entity�j�  �reportDetails.executingEntity�j�  j�  u}�(j�  �lei:test_executing_entity�j�  �seller�j�  j�  u}�(j�  �	id:bar-gb�j�  �counterparty�j�  j�  u}�(j�  �id:xux�j�  �:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�j�  j�  u}�(j�  �id:sanjuans�j�  �1tradersAlgosWaiversIndicators.executionWithinFirm�j�  j�  u}�(j�  �id:xux�j�  �clientIdentifiers.client�j�  j�  u}�(j�  �id:sanjuans�j�  �trader�j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:spg-gb�j�  j�  j�  j�  u}�(j�  �	id:spg-gb�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:spg-gb�j�  j�  j�  j�  u}�(j�  �	id:spg-gb�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:spg-gb�j�  j�  j�  j�  u}�(j�  �	id:spg-gb�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �
id:db-degb�j�  j�  j�  j�  u}�(j�  �
id:db-degb�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �id:hsb-hkkr�j�  j�  j�  j�  u}�(j�  �id:hsb-hkkr�j�  j�  j�  j�  u}�(j�  �	id:osheaj�j�  j�  j�  j�  u}�(j�  �	id:osheaj�j�  j�  j�  j�  u}�(j�  �	id:osheaj�j�  j�  j�  j�  u}�(j�  �	id:osheaj�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:cgm-gb�j�  j�  j�  j�  u}�(j�  �	id:cgm-gb�j�  j�  j�  j�  u}�(j�  �id:christiansent�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:christiansent�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:cgm-gb�j�  j�  j�  j�  u}�(j�  �	id:cgm-gb�j�  j�  j�  j�  u}�(j�  �id:christiansent�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:christiansent�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:jef-gb�j�  j�  j�  j�  u}�(j�  �	id:jef-gb�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  ue]�(}�(j�  �	id:lfd-ae�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:lfd-ae�j�  j�  j�  j�  u}�(j�  �
id:samieia�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �
id:samieia�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �
id:db-degb�j�  j�  j�  j�  u}�(j�  �
id:db-degb�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:nom-gb�j�  j�  j�  j�  u}�(j�  �	id:nom-gb�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:nom-gb�j�  j�  j�  j�  u}�(j�  �	id:nom-gb�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:anz-au�j�  j�  j�  j�  u}�(j�  �	id:anz-au�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:cgm-gb�j�  j�  j�  j�  u}�(j�  �	id:cgm-gb�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:cgm-gb�j�  j�  j�  j�  u}�(j�  �	id:cgm-gb�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �	id:chingc�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �id:stc-gbid�j�  j�  j�  j�  u}�(j�  �id:stc-gbid�j�  j�  j�  j�  u}�(j�  �	id:kumara�j�  j�  j�  j�  u}�(j�  �
id:apriyantid�j�  j�  j�  j�  u}�(j�  �	id:kumara�j�  j�  j�  j�  u}�(j�  �
id:apriyantid�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �id:ms-gb�j�  j�  j�  j�  u}�(j�  �id:ms-gb�j�  j�  j�  j�  u}�(j�  �id:berishab�j�  j�  j�  j�  u}�(j�  �
id:carvera�j�  j�  j�  j�  u}�(j�  �id:berishab�j�  j�  j�  j�  u}�(j�  �
id:carvera�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:bnp-fr�j�  j�  j�  j�  u}�(j�  �	id:bnp-fr�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:jef-gb�j�  j�  j�  j�  u}�(j�  �	id:jef-gb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:jef-gb�j�  j�  j�  j�  u}�(j�  �	id:jef-gb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:jef-gb�j�  j�  j�  j�  u}�(j�  �	id:jef-gb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �id:ml-gb�j�  j�  j�  j�  u}�(j�  �id:ml-gb�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:hsb-gb�j�  j�  j�  j�  u}�(j�  �	id:hsb-gb�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  ue]�(}�(j�  �
id:db-degb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �
id:db-degb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �	id:cgm-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:cgm-gb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �
id:db-degb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �
id:db-degb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �	id:abu-ae�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:abu-ae�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �	id:cgm-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:cgm-gb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �	id:hsb-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:hsb-gb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �	id:hsb-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:hsb-gb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �	id:stc-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:stc-gb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �	id:jef-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:jef-gb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �	id:hsb-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:hsb-gb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �	id:ing-nl�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:ing-nl�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �	id:stc-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:stc-gb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �id:ms-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �id:ms-gb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �	id:cgm-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:cgm-gb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �	id:mzh-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:mzh-gb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �	id:cgm-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:cgm-gb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �id:ml-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �id:ml-gb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �id:ms-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �id:ms-gb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �	id:rai-at�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:rai-at�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �id:ml-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �id:ml-gb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �id:ml-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �id:ml-gb�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  u}�(j�  �id:sanjuans�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:hsb-gb�j�  j�  j�  j�  u}�(j�  �	id:hsb-gb�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:hsb-gb�j�  j�  j�  j�  u}�(j�  �	id:hsb-gb�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �id:ms-gb�j�  j�  j�  j�  u}�(j�  �id:ms-gb�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �id:ms-gb�j�  j�  j�  j�  u}�(j�  �id:ms-gb�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �id:ml-gb�j�  j�  j�  j�  u}�(j�  �id:ml-gb�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:bar-gb�j�  j�  j�  j�  u}�(j�  �	id:bar-gb�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  ue]�(}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:bar-gb�j�  j�  j�  j�  u}�(j�  �	id:bar-gb�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  ue]�(}�(j�  �	id:cgm-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:cgm-gb�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  ue]�(}�(j�  �	id:mxs-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:mxs-gb�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  ue]�(}�(j�  �	id:hsb-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:hsb-gb�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  ue]�(}�(j�  �	id:jef-gb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �	id:jef-gb�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  ue]�(}�(j�  �id:jpm-usgb�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �lei:test_executing_entity�j�  j�  j�  j�  u}�(j�  �id:jpm-usgb�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  u}�(j�  �
id:edmondsona�j�  j�  j�  j�  u}�(j�  �
id:hodgesr�j�  j�  j�  j�  ue�lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��	id:bar-gb��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��	id:lfd-ae��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��
id:db-degb��	id:cgm-gb��
id:db-degb��	id:abu-ae��	id:cgm-gb��	id:hsb-gb��	id:hsb-gb��	id:stc-gb��	id:jef-gb��	id:hsb-gb��	id:ing-nl��	id:stc-gb��id:ms-gb��	id:cgm-gb��	id:mzh-gb��	id:cgm-gb��id:ml-gb��id:ms-gb��	id:rai-at��id:ml-gb��id:ml-gb��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��	id:cgm-gb��	id:mxs-gb��	id:hsb-gb��	id:jef-gb��id:jpm-usgb��lei:test_executing_entity��	id:spg-gb��	id:spg-gb��	id:spg-gb��
id:db-degb��id:hsb-hkkr��	id:cgm-gb��	id:cgm-gb��	id:jef-gb��lei:test_executing_entity��
id:db-degb��	id:nom-gb��	id:nom-gb��	id:anz-au��	id:cgm-gb��	id:cgm-gb��id:stc-gbid��id:ms-gb��	id:bnp-fr��	id:jef-gb��	id:jef-gb��	id:jef-gb��id:ml-gb��	id:hsb-gb��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��	id:hsb-gb��	id:hsb-gb��id:ms-gb��id:ms-gb��id:ml-gb��	id:bar-gb��	id:bar-gb��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �id:xux��
id:hodgesr��
id:hodgesr��
id:hodgesr��	id:chingc��	id:osheaj��id:christiansent��id:christiansent��	id:chingc��
id:samieia��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��	id:kumara��id:berishab��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��
id:edmondsona��
id:edmondsona��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��id:sanjuans��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona��
id:edmondsona�]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j   j  j  j  j  j
  e]�(j�  j�  j
  j  j  j  j  j  j  j  e]�(j�  j�  j  j   j"  j$  j&  j(  j*  j,  e]�(j�  j�  j/  j1  j3  j5  j7  j9  j;  j=  e]�(j�  j�  j@  jB  jD  jF  jH  jJ  jL  jN  e]�(j�  j�  jQ  jS  jU  jW  jY  j[  j]  j_  e]�(j�  j�  jb  jd  jf  jh  jj  jl  jn  jp  e]�(j�  j�  js  ju  jw  jy  j{  j}  j  j�  e]�(j  j  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j  j  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j
  j  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j  j  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j  j  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j  j  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j  j  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j  j  j�  j�  j�  j  j  j  j  j	  e]�(j"  j#  j  j  j  j  j  j  j  j  e]�(j&  j'  j  j  j!  j#  j%  j'  j)  j+  e]�(j*  j+  j.  j0  j2  j4  j6  j8  j:  j<  e]�(j.  j/  j?  jA  jC  jE  jG  jI  jK  jM  e]�(j2  j3  jP  jR  jT  jV  jX  jZ  j\  j^  e]�(j6  j7  ja  jc  je  jg  ji  jk  jm  jo  e]�(j:  j;  jr  jt  jv  jx  jz  j|  j~  j�  e]�(j>  j?  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jB  jC  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jF  jG  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jJ  jK  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jN  jO  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jR  jS  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jV  jW  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jZ  j[  j�  j�  j�  j   j  j  j  j  e]�(j^  j_  j  j
  j  j  j  j  j  j  e]�(jb  jc  j  j  j   j"  j$  j&  j(  j*  e]�(jf  jg  j-  j/  j1  j3  j5  j7  j9  j;  e]�(jj  jk  j>  j@  jB  jD  jF  jH  jJ  jL  e]�(jn  jo  jO  jQ  jS  jU  jW  jY  j[  j]  e]�(jr  js  j`  jb  jd  jf  jh  jj  jl  jn  e]�(jv  jw  jq  js  ju  jw  jy  j{  j}  j  e]�(jz  j{  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j~  j  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j  j  j  j  e]�(j�  j�  j
  j  j  j  j  j  j  j  e]�(j�  j�  j  j  j  j!  j#  j%  j'  j)  e]�(j�  j�  j,  j.  j0  j2  j4  j6  j8  j:  e]�(j�  j�  j=  j?  jA  jC  jE  jG  jI  jK  e]�(j�  j�  jN  jP  jR  jT  jV  jX  jZ  j\  e]�(j�  j�  j_  ja  jc  je  jg  ji  jk  jm  e]�(j�  j�  jp  jr  jt  jv  jx  jz  j|  j~  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  ej�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h]at�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h`at�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�haat�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hcat�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hehfet�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h-h8h;h<hFhHhKet�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhiNu��R�h
h}�(hhhK ��h��R�(KK-��h!�]�(h%h&h'h(h)h*h+h/h0h4h5h7h9h:h=h?h@hAhBhChDhEhGhIhJhMhNhOhPhRhShThUhVhWhXhYhZh[h\h^h_hbhdhget�bhiNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���KKK��R�u}�(jG
  h�jH
  jK
  K	K
K��R�u}�(jG
  h�jH
  jK
  KK
K��R�u}�(jG
  h�jH
  jK
  K
KK��R�u}�(jG
  h�jH
  jK
  KKK��R�u}�(jG
  h�jH
  jK
  KKK��R�u}�(jG
  j-  jH
  jK
  K'K(K��R�u}�(jG
  j6  jH
  jK
  K,K-K��R�u}�(jG
  jp  jH
  jK
  K8K9K��R�u}�(jG
  j�  jH
  jK
  K;K<K��R�u}�(jG
  j�  jH
  jK
  K<K=K��R�u}�(jG
  j6  jH
  jK
  K>K?K��R�u}�(jG
  jz  jH
  jK
  K@KBK��R�u}�(jG
  j�  jH
  hhK ��h��R�(KK��h�i8�����R�(KhwNNNJ����J����K t�b�C8                            !       #       &       �t�bu}�(jG
  j�  jH
  jK
  KKK��R�u}�(jG
  j�  jH
  hhK ��h��R�(KK-��jy
  �Bh                                                    
                                                                                                          "       $       %       (       )       *       +       -       .       /       0       1       2       3       4       5       6       7       9       :       =       ?       B       �t�bueust�b�.       �_typ��	dataframe��	_metadata�]��attrs�}�ub.