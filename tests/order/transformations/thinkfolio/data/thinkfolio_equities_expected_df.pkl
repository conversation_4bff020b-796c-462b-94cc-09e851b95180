���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKE��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��executionDetails.limitPrice��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��+executionDetails.passiveAggressiveIndicator�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	hierarchy��orderIdentifiers.orderIdCode��	_order.id��_orderState.id��_order.__meta_model__��_orderState.__meta_model__��orderIdentifiers.parentOrderId��!orderIdentifiers.transactionRefNo��priceFormingData.price�� transactionDetails.priceCurrency��#transactionDetails.quantityCurrency��+_orderState.priceFormingData.tradedQuantity�� priceFormingData.initialQuantity��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.orderReceived��$_order.timestamps.orderStatusUpdated��)_orderState.timestamps.orderStatusUpdated��timestamps.orderSubmitted��&_orderState.timestamps.tradingDateTime��#transactionDetails.buySellIndicator��#transactionDetails.commissionAmount��+transactionDetails.commissionAmountCurrency��transactionDetails.price�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��._orderState.transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��isin_attribute��notional_currency_2_attribute��venue_attribute��#instrument_classification_attribute��expiry_date_attribute��swap_near_leg_date_attribute��currency_attribute��asset_class_attribute��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��__is_created_through_fallback__��__newo_in_file__��__classification__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C�                                                                	       
                     
                                                                                                                       �t�bhk�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�Market�h�h�h�h�h�h�h�h�h�h�h��Limit�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�b�_dtype�h��StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�pandas._libs.missing��NA���h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�2934008�h�h�h�h�h�h�h�h�h�h�h��2933972�h�h�h�h�h�h�h�h�h��2934023�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�2932395�h�h�h�h�h�h�h�h�h�h�hȌ2932536�h�h�h�h�h�h�h�h�hɌ2932755�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�1.50335982035928�h�h�h�h�h�h�h�h�h�h�hԌ0.00690042533081286�h�h�h�h�h�h�h�h�hՌ0.617128888888889�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�XNSE�h�h�h�h�h�h�h�h�h�h�h�CLHK��XHKG�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�INE018E01016�h�h�h�h�h�h�h�h�h�h�h��CNE100002359�h�h�h�h�h�h�h�h�h��INE438A01022�j   j   j   j   j   j   j   et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��<id:jmi-in:jm financial institutional securities ltd - jmi-in��<id:jmi-in:jm financial institutional securities ltd - jmi-in��<id:jmi-in:jm financial institutional securities ltd - jmi-in��<id:jmi-in:jm financial institutional securities ltd - jmi-in��<id:jmi-in:jm financial institutional securities ltd - jmi-in��<id:jmi-in:jm financial institutional securities ltd - jmi-in��<id:jmi-in:jm financial institutional securities ltd - jmi-in��<id:jmi-in:jm financial institutional securities ltd - jmi-in�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�id:rashi talwar��id:rashi talwar��id:rashi talwar��id:rashi talwar��id:rashi talwar��id:rashi talwar��id:rashi talwar��id:rashi talwar��id:rashi talwar��id:rashi talwar��id:rashi talwar��id:rashi talwar��id:fernando assad��id:fernando assad��id:fernando assad��id:fernando assad��id:fernando assad��id:fernando assad��id:fernando assad��id:fernando assad��id:fernando assad��id:fernando assad��id:arpit kapoor��id:arpit kapoor��id:arpit kapoor��id:arpit kapoor��id:arpit kapoor��id:arpit kapoor��id:arpit kapoor��id:arpit kapoor�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim��
id:joanne sim�et�bh�h�)��ubhhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C<                              �t�bhhK ��h��R�(KKK��h�f8�����R�(KhyNNNJ����J����K t�b�B�        �      �      �      �      �      �      �      �      �      �      �      �3333331@3333331@3333331@3333331@3333331@3333331@3333331@3333331@3333331@3333331@      �      �      �      �      �      �      �      ���镲ۋ@a2U0*ۋ@n4���ۋ@jM�Sڋ@�JY�8�@5�8E��@���QI܋@�~�:pۋ@;M��ދ@��_ߋ@O����@+���@{�G�:1@3333331@�G�zT1@=
ףp=1@���Q81@���Q81@�(\��51@H�z�G1@3333331@3333331@�G�z�v@�z�G	w@�G�zw@)\��� w@      w@����̜v@
ףp=�v@q=
ף�v@     �~@     @�@     Ј@     P�@     *�@     ��@     �o@     @|@     �e@     �e@     @Y@     @w@      �@      y@      �@      y@      y@      y@      y@      �@      y@      �@     ��@     ��@     P�@     ,�@     ؉@     h�@     �@     �@��镲ۋ@a2U0*ۋ@n4���ۋ@jM�Sڋ@�JY�8�@5�8E��@���QI܋@�~�:pۋ@;M��ދ@��_ߋ@O����@+���@{�G�:1@3333331@�G�zT1@=
ףp=1@���Q81@���Q81@�(\��51@H�z�G1@3333331@3333331@�G�z�v@�z�G	w@�G�zw@)\��� w@      w@����̜v@
ףp=�v@q=
ף�v@     �~@     @�@     Ј@     P�@     *�@     ��@     �o@     @|@     �e@     �e@     @Y@     @w@      �@      y@      �@      y@      y@      y@      y@      �@      y@      �@     ��@     ��@     P�@     ,�@     ؉@     h�@     �@     �@�t�bhhK ��h��R�(KKK��h�i8�����R�(KhyNNNJ����J����K t�b�B�  �     �     �     �     �     �     �     �     �     �     �     �     �:     �:     �:     �:     �:     �:     �:     �:     �:     �:      �      �      �      �      �      �      �      �                                                                     	       
                     
                                                                                                                       �t�bhhK ��h��R�(KK,K��h!�]�(�1�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �2�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
Thinkfolio�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
2023-05-15��
2023-05-15��
2023-05-15��
2023-05-15��
2023-05-15��
2023-05-15��
2023-05-15��
2023-05-15��
2023-05-15��
2023-05-15��
2023-05-15��
2023-05-15��
2022-10-18��
2022-10-18��
2022-10-18��
2022-10-18��
2022-10-18��
2022-10-18��
2022-10-18��
2022-10-18��
2023-05-15��
2023-05-15��
2022-10-19��
2023-05-15��
2023-05-15��
2023-05-15��
2023-05-15��
2023-05-15��
2023-05-15��
2023-05-15��BUYI�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �SELL�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �NEWO�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �FILL�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �AGRE��PASV�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�]��DAVY�aj�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  ]��GTCV�aj�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
Standalone�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �Order�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
OrderState�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �!2934008_2023-05-15T08:18:20.776_0��!2934008_2023-05-15T08:18:28.521_1��!2934008_2023-05-15T08:20:29.581_2��!2934008_2023-05-15T08:20:29.581_3��!2934008_2023-05-15T08:22:24.222_4��!2934008_2023-05-15T08:22:28.253_5��!2934008_2023-05-15T08:24:27.824_6��!2934008_2023-05-15T08:24:49.147_7��!2934008_2023-05-15T08:26:32.905_8��!2934008_2023-05-15T08:26:32.906_9��"2934008_2023-05-15T08:28:02.406_10��"2934008_2023-05-15T08:28:18.192_11��"2933972_2022-10-18T19:09:21.000_12��"2933972_2022-10-18T19:11:34.000_13��"2933972_2022-10-18T19:11:35.000_14��"2933972_2022-10-18T19:11:35.000_15��"2933972_2022-10-18T19:11:57.000_16��"2933972_2022-10-18T19:11:59.000_17��"2933972_2022-10-18T19:12:17.000_18��"2933972_2022-10-18T19:14:32.000_19��"2933972_2023-05-15T01:30:31.000_20��"2933972_2023-05-15T05:59:55.000_21��"2934023_2022-10-19T02:58:31.000_22��"2934023_2023-05-15T03:45:34.000_23��"2934023_2023-05-15T03:47:33.000_24��"2934023_2023-05-15T03:49:34.000_25��"2934023_2023-05-15T03:51:34.000_26��"2934023_2023-05-15T07:48:42.000_27��"2934023_2023-05-15T07:50:42.000_28��"2934023_2023-05-15T07:52:43.000_29��INR�j  j  j  j  j  j  j  j  j  j  j  �HKD�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j�  j�  j�  j�  j�  j�  j�  j   j  j  j  j  j  j  j  j  j	  j
  j  j  j
  j  j  j  j  j  j  j  j  j  �$dummy/path/TEST_FILLS_1_20230616.pkl�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �2023-05-08T10:27:11.000000Z��2023-05-08T10:27:11.000000Z��2023-05-08T10:27:11.000000Z��2023-05-08T10:27:11.000000Z��2023-05-08T10:27:11.000000Z��2023-05-08T10:27:11.000000Z��2023-05-08T10:27:11.000000Z��2023-05-08T10:27:11.000000Z��2023-05-08T10:27:11.000000Z��2023-05-08T10:27:11.000000Z��2023-05-08T10:27:11.000000Z��2023-05-08T10:27:11.000000Z��2023-05-09T07:22:29.000000Z��2023-05-09T07:22:29.000000Z��2023-05-09T07:22:29.000000Z��2023-05-09T07:22:29.000000Z��2023-05-09T07:22:29.000000Z��2023-05-09T07:22:29.000000Z��2023-05-09T07:22:29.000000Z��2023-05-09T07:22:29.000000Z��2023-05-09T07:22:29.000000Z��2023-05-09T07:22:29.000000Z��2023-05-10T01:53:41.000000Z��2023-05-10T01:53:41.000000Z��2023-05-10T01:53:41.000000Z��2023-05-10T01:53:41.000000Z��2023-05-10T01:53:41.000000Z��2023-05-10T01:53:41.000000Z��2023-05-10T01:53:41.000000Z��2023-05-10T01:53:41.000000Z�j  j  j  j  j  j  j   j!  j"  j#  j$  j%  j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j7  �2023-05-15T08:18:20.776000Z��2023-05-15T08:18:28.521000Z��2023-05-15T08:20:29.581000Z��2023-05-15T08:20:29.581000Z��2023-05-15T08:22:24.222000Z��2023-05-15T08:22:28.253000Z��2023-05-15T08:24:27.824000Z��2023-05-15T08:24:49.147000Z��2023-05-15T08:26:32.905000Z��2023-05-15T08:26:32.906000Z��2023-05-15T08:28:02.406000Z��2023-05-15T08:28:18.192000Z��2022-10-18T19:09:21.000000Z��2022-10-18T19:11:34.000000Z��2022-10-18T19:11:35.000000Z��2022-10-18T19:11:35.000000Z��2022-10-18T19:11:57.000000Z��2022-10-18T19:11:59.000000Z��2022-10-18T19:12:17.000000Z��2022-10-18T19:14:32.000000Z��2023-05-15T01:30:31.000000Z��2023-05-15T05:59:55.000000Z��2022-10-19T02:58:31.000000Z��2023-05-15T03:45:34.000000Z��2023-05-15T03:47:33.000000Z��2023-05-15T03:49:34.000000Z��2023-05-15T03:51:34.000000Z��2023-05-15T07:48:42.000000Z��2023-05-15T07:50:42.000000Z��2023-05-15T07:52:43.000000Z��2023-05-09T02:07:18.000000Z��2023-05-09T02:07:18.000000Z��2023-05-09T02:07:18.000000Z��2023-05-09T02:07:18.000000Z��2023-05-09T02:07:18.000000Z��2023-05-09T02:07:18.000000Z��2023-05-09T02:07:18.000000Z��2023-05-09T02:07:18.000000Z��2023-05-09T02:07:18.000000Z��2023-05-09T02:07:18.000000Z��2023-05-09T02:07:18.000000Z��2023-05-09T02:07:18.000000Z��2023-05-09T07:22:29.000000Z��2023-05-09T07:22:29.000000Z��2023-05-09T07:22:29.000000Z��2023-05-09T07:22:29.000000Z��2023-05-09T07:22:29.000000Z��2023-05-09T07:22:29.000000Z��2023-05-09T07:22:29.000000Z��2023-05-09T07:22:29.000000Z��2023-05-09T07:22:29.000000Z��2023-05-09T07:22:29.000000Z��2023-05-10T06:26:33.000000Z��2023-05-10T06:26:33.000000Z��2023-05-10T06:26:33.000000Z��2023-05-10T06:26:33.000000Z��2023-05-10T06:26:33.000000Z��2023-05-10T06:26:33.000000Z��2023-05-10T06:26:33.000000Z��2023-05-10T06:26:33.000000Z�j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �MONE�jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  �UNIT�ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  ju  �Market Side�jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  ]�(}�(�labelId�h��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(jy  �INE018E01016INRXNSE�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �INE018E01016INRXNSE�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �INE018E01016INRXNSE�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �INE018E01016INRXNSE�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �INE018E01016INRXNSE�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �INE018E01016INRXNSE�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �INE018E01016INRXNSE�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �INE018E01016INRXNSE�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �INE018E01016INRXNSE�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �INE018E01016INRXNSE�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �INE018E01016INRXNSE�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �INE018E01016INRXNSE�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �CNE100002359HKDCLHK�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �CNE100002359HKDXHKG�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �CNE100002359HKDCLHK�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �CNE100002359HKDCLHK�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �CNE100002359HKDCLHK�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �CNE100002359HKDXHKG�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �CNE100002359HKDCLHK�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �CNE100002359HKDXHKG�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �CNE100002359HKDXHKG�jz  j{  j|  j�  ue]�(}�(jy  h�jz  j{  j|  j�  u}�(jy  �CNE100002359HKDXHKG�jz  j{  j|  j�  ue]�(}�(jy  j   jz  j{  j|  j�  u}�(jy  �INE438A01022INRXNSE�jz  j{  j|  j�  ue]�(}�(jy  j   jz  j{  j|  j�  u}�(jy  �INE438A01022INRXNSE�jz  j{  j|  j�  ue]�(}�(jy  j   jz  j{  j|  j�  u}�(jy  �INE438A01022INRXNSE�jz  j{  j|  j�  ue]�(}�(jy  j   jz  j{  j|  j�  u}�(jy  �INE438A01022INRXNSE�jz  j{  j|  j�  ue]�(}�(jy  j   jz  j{  j|  j�  u}�(jy  �INE438A01022INRXNSE�jz  j{  j|  j�  ue]�(}�(jy  j   jz  j{  j|  j�  u}�(jy  �INE438A01022INRXNSE�jz  j{  j|  j�  ue]�(}�(jy  j   jz  j{  j|  j�  u}�(jy  �INE438A01022INRXNSE�jz  j{  j|  j�  ue]�(}�(jy  j   jz  j{  j|  j�  u}�(jy  �INE438A01022INRXNSE�jz  j{  j|  j�  ueG�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      j  j  j  j  j  j  j  j  j  j  e(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �equity�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  ]�(}�(jy  �lei:test_executing_entity�jz  �buyer�j|  j  �ARRAY���R�u}�(jy  �lei:test_executing_entity�jz  �reportDetails.executingEntity�j|  j�  u}�(jy  �id:cls-gb:clsa (uk)�jz  �seller�j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  �counterparty�j|  j�  u}�(jy  �id:rashi talwar�jz  �:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�j|  j�  u}�(jy  �
id:joanne sim�jz  �1tradersAlgosWaiversIndicators.executionWithinFirm�j|  j�  u}�(jy  �id:rashi talwar�jz  �clientIdentifiers.client�j|  j   u}�(jy  �
id:joanne sim�jz  �trader�j|  j   ue]�(}�(jy  �lei:test_executing_entity�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �id:cls-gb:clsa (uk)�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �lei:test_executing_entity�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �id:cls-gb:clsa (uk)�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �lei:test_executing_entity�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �id:cls-gb:clsa (uk)�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �lei:test_executing_entity�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �id:cls-gb:clsa (uk)�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �lei:test_executing_entity�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �id:cls-gb:clsa (uk)�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �lei:test_executing_entity�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �id:cls-gb:clsa (uk)�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �lei:test_executing_entity�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �id:cls-gb:clsa (uk)�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �lei:test_executing_entity�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �id:cls-gb:clsa (uk)�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �lei:test_executing_entity�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �id:cls-gb:clsa (uk)�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �lei:test_executing_entity�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �id:cls-gb:clsa (uk)�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �lei:test_executing_entity�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �id:cls-gb:clsa (uk)�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:rashi talwar�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �id:cls-gb:clsa (uk)�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �lei:test_executing_entity�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �id:cls-gb:clsa (uk)�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �lei:test_executing_entity�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �id:cls-gb:clsa (uk)�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �lei:test_executing_entity�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �id:cls-gb:clsa (uk)�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �lei:test_executing_entity�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �id:cls-gb:clsa (uk)�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �lei:test_executing_entity�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �id:cls-gb:clsa (uk)�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �lei:test_executing_entity�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �id:cls-gb:clsa (uk)�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �lei:test_executing_entity�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �id:cls-gb:clsa (uk)�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �lei:test_executing_entity�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �id:cls-gb:clsa (uk)�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �lei:test_executing_entity�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �id:cls-gb:clsa (uk)�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �lei:test_executing_entity�jz  j  j|  j   u}�(jy  �id:cls-gb:clsa (uk)�jz  j	  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:fernando assad�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �<id:jmi-in:jm financial institutional securities ltd - jmi-in�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �lei:test_executing_entity�jz  j  j|  j   u}�(jy  �<id:jmi-in:jm financial institutional securities ltd - jmi-in�jz  j	  j|  j�  u}�(jy  �id:arpit kapoor�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:arpit kapoor�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �<id:jmi-in:jm financial institutional securities ltd - jmi-in�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �lei:test_executing_entity�jz  j  j|  j   u}�(jy  �<id:jmi-in:jm financial institutional securities ltd - jmi-in�jz  j	  j|  j�  u}�(jy  �id:arpit kapoor�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:arpit kapoor�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �<id:jmi-in:jm financial institutional securities ltd - jmi-in�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �lei:test_executing_entity�jz  j  j|  j   u}�(jy  �<id:jmi-in:jm financial institutional securities ltd - jmi-in�jz  j	  j|  j�  u}�(jy  �id:arpit kapoor�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:arpit kapoor�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �<id:jmi-in:jm financial institutional securities ltd - jmi-in�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �lei:test_executing_entity�jz  j  j|  j   u}�(jy  �<id:jmi-in:jm financial institutional securities ltd - jmi-in�jz  j	  j|  j�  u}�(jy  �id:arpit kapoor�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:arpit kapoor�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �<id:jmi-in:jm financial institutional securities ltd - jmi-in�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �lei:test_executing_entity�jz  j  j|  j   u}�(jy  �<id:jmi-in:jm financial institutional securities ltd - jmi-in�jz  j	  j|  j�  u}�(jy  �id:arpit kapoor�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:arpit kapoor�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �<id:jmi-in:jm financial institutional securities ltd - jmi-in�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �lei:test_executing_entity�jz  j  j|  j   u}�(jy  �<id:jmi-in:jm financial institutional securities ltd - jmi-in�jz  j	  j|  j�  u}�(jy  �id:arpit kapoor�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:arpit kapoor�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �<id:jmi-in:jm financial institutional securities ltd - jmi-in�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �lei:test_executing_entity�jz  j  j|  j   u}�(jy  �<id:jmi-in:jm financial institutional securities ltd - jmi-in�jz  j	  j|  j�  u}�(jy  �id:arpit kapoor�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:arpit kapoor�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue]�(}�(jy  �<id:jmi-in:jm financial institutional securities ltd - jmi-in�jz  j�  j|  j   u}�(jy  �lei:test_executing_entity�jz  j  j|  j�  u}�(jy  �lei:test_executing_entity�jz  j  j|  j   u}�(jy  �<id:jmi-in:jm financial institutional securities ltd - jmi-in�jz  j	  j|  j�  u}�(jy  �id:arpit kapoor�jz  j  j|  j�  u}�(jy  �
id:joanne sim�jz  j  j|  j�  u}�(jy  �id:arpit kapoor�jz  j  j|  j   u}�(jy  �
id:joanne sim�jz  j  j|  j   ue�lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��<id:jmi-in:jm financial institutional securities ltd - jmi-in��<id:jmi-in:jm financial institutional securities ltd - jmi-in��<id:jmi-in:jm financial institutional securities ltd - jmi-in��<id:jmi-in:jm financial institutional securities ltd - jmi-in��<id:jmi-in:jm financial institutional securities ltd - jmi-in��<id:jmi-in:jm financial institutional securities ltd - jmi-in��<id:jmi-in:jm financial institutional securities ltd - jmi-in��<id:jmi-in:jm financial institutional securities ltd - jmi-in��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��id:cls-gb:clsa (uk)��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��id:rashi talwar��id:rashi talwar��id:rashi talwar��id:rashi talwar��id:rashi talwar��id:rashi talwar��id:rashi talwar��id:rashi talwar��id:rashi talwar��id:rashi talwar��id:rashi talwar��id:rashi talwar��id:fernando assad��id:fernando assad��id:fernando assad��id:fernando assad��id:fernando assad��id:fernando assad��id:fernando assad��id:fernando assad��id:fernando assad��id:fernando assad��id:arpit kapoor��id:arpit kapoor��id:arpit kapoor��id:arpit kapoor��id:arpit kapoor��id:arpit kapoor��id:arpit kapoor��id:arpit kapoor�]�(jx  j�  j�  j  j  j  j
  j
  j  j  e]�(j�  j�  j  j  j  j  j  j!  j#  j%  e]�(j�  j�  j(  j*  j,  j.  j0  j2  j4  j6  e]�(j�  j�  j9  j;  j=  j?  jA  jC  jE  jG  e]�(j�  j�  jJ  jL  jN  jP  jR  jT  jV  jX  e]�(j�  j�  j[  j]  j_  ja  jc  je  jg  ji  e]�(j�  j�  jl  jn  jp  jr  jt  jv  jx  jz  e]�(j�  j�  j}  j  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j   j  e]�(j�  j�  j  j  j	  j  j
  j  j  j  e]�(j�  j�  j  j  j  j  j  j   j"  j$  e]�(j�  j�  j'  j)  j+  j-  j/  j1  j3  j5  e]�(j�  j�  j8  j:  j<  j>  j@  jB  jD  jF  e]�(j�  j�  jI  jK  jM  jO  jQ  jS  jU  jW  e]�(j�  j�  jZ  j\  j^  j`  jb  jd  jf  jh  e]�(j�  j�  jk  jm  jo  jq  js  ju  jw  jy  e]�(j�  j�  j|  j~  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j  eh�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hbat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hcat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�heat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hghhet�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h*h9h<hIhKet�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h=h?et�bhkNu��R�h
h}�(hhhK ��h��R�(KK,��h!�]�(h%h&h'h(h)h+h,h.h0h1h5h6h8h:h;h>h@hAhBhChDhEhFhHhJhLhMhOhRhThVhWhXhYhZh[h\h]h^h`hahdhfhiet�bhkNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���KK	K��R�u}�(jV  h�jW  jZ  K
KK��R�u}�(jV  h�jW  jZ  K
KK��R�u}�(jV  h�jW  jZ  KKK��R�u}�(jV  h�jW  jZ  KKK��R�u}�(jV  h�jW  jZ  KKK��R�u}�(jV  h�jW  jZ  K"K#K��R�u}�(jV  h�jW  jZ  K)K*K��R�u}�(jV  h�jW  jZ  K+K,K��R�u}�(jV  h�jW  jZ  K,K-K��R�u}�(jV  h�jW  jZ  K.K/K��R�u}�(jV  j  jW  jZ  K0K1K��R�u}�(jV  j  jW  jZ  K:K;K��R�u}�(jV  j3  jW  jZ  K=K>K��R�u}�(jV  jZ  jW  jZ  K>K?K��R�u}�(jV  j�  jW  jZ  K@KAK��R�u}�(jV  j�  jW  jZ  KBKDK��R�u}�(jV  j�  jW  hhK ��h��R�(KK��j�  �C(                     $       &       �t�bu}�(jV  j�  jW  jZ  KKK��R�u}�(jV  j�  jW  hhK ��h��R�(KK,��j�  �B`                                                    	                                                                                                          !       #       %       '       (       *       -       /       1       2       3       4       5       6       7       8       9       ;       <       ?       A       D       �t�bueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.