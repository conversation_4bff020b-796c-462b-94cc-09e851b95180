��D      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKJ��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�buySell��dataSourceName��date��!executionDetails.buySellIndicator��executionDetails.limitPrice��executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo�� executionDetails.tradingCapacity��id��__meta_model__��"orderIdentifiers.aggregatedOrderId��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode�� priceFormingData.initialQuantity��"priceFormingData.remainingQuantity��sourceIndex��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.validityPeriod��#transactionDetails.buySellIndicator��transactionDetails.positionId�� transactionDetails.priceCurrency��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��__EXECUTING_ENTITY__��
__CLIENT__��__INVESTMENT_DEC_WITHIN_FIRM__��__TEMP_BUY_SELL_INDICATOR__��__TEMP_INST_ID_SYMBOL__��=__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE__��<__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_TYPE__��;__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS__��?__TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_BASE_PRODUCT__��F__TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_FURTHER_SUB_PRODUCT__��__TEMP_INST_ID_DELIVERY_TYPE__��!__TEMP_INST_ID_PRICE_MULTIPLIER__��$__TEMP_INST_ID_EXT_EXCHANGE_SYMBOL__��(__TEMP_INST_ID_INSTRUMENT_ID_CODE_TYPE__��__TEMP_INST_ID_PRICE_NOTATION__��*__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION__��%__TEMP_INST_ID_INSTRUMENT_FULL_NAME__��)__TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID__��&__TEMP_INST_ID_UNDERLYING_INDEX_NAME__��__TEMP_INST_ASSET_CLASS__��(__TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED__��TEMP_ASSET_CLASS��(__TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED__��*__TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED__��#__TEMP_INST_UNIQUE_ID_CASH_EQUITY__��%__TEMP_INST_ID_INSTRUMENT_UNIQUE_ID__��$__TEMP_IS_CREATED_THROUGH_FALLBACK__��TEMP_ORDER_STATUS��ContractSize��TEMP_XXXX_VENUE�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(hpN�start�K �stop�K�step�Ku��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�US30��US100��US100�et�b�_dtype�h}�StringDtype���)��ub�numpy.core.numeric��_frombuffer���(�       �h�b1�����R�(Kh"NNNJ����J����K t�bKK���C�t�R�h�(�`                                    ��@     @�@     @�@     ��@     @�@     @�@      �?      �?      �?�h�f8�����R�(K�<�NNNJ����J����K t�bKK��h�t�R�h�(�0                                                  �h�i8�����R�(Kh�NNNJ����J����K t�bKK��h�t�R�hhK ��h��R�(KKAK��h��]�(�1��2�h��MetaTrader5 - Orders�h�h��
2022-02-14��
2022-02-14��
2022-02-14��BUYI��SELL�h��NEWO�h��CAME��Market�h�h��HModify Flags - 0, Action - Comment, Login - 4006656, mt5_symbol - US30, ��IModify Flags - 0, Action - Comment, Login - 4006545, mt5_symbol - US100, ��IModify Flags - 0, Action - Comment, Login - 4006545, mt5_symbol - US100, ��DEAL�h�hČ260242|260242|4006656��263222|263222|4006656��263222|263222|4006656��Order�hȌ
OrderState��260242��263222�hˌ260242-O��263222-O�h�h�h�hǌn/Users/<USER>/Desktop/steeleye/swarm-tasks/tests/order/transformations/metatrader/mt5/datasample_orders.csv�h�hΌ2022-02-14T13:57:10.597000Z��2022-02-14T21:28:37.145000Z��2022-02-14T21:28:37.145000Z�h�h�h�h�h�h�]�h�a]�h�a]�h�ah�h�h�h�h�hˌUSD�h�hՌpandas._libs.missing��NA���h�h،UNIT�h�hٌClient Side�h�h�h�h�hČXXXX�h�h�h�h�h�]�}�(�labelId��%US Wall St 30-equity shares-CASH/spot��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(hތUS Nas 100-indices-cfd�h�h�h�h�ua]�}�(hތUS Nas 100-indices-cfd�h�h�h�h�ua]�(}�(hތ
id:4006656�h��buyer�h�h�ARRAY���R�u}�(hތlei:sample_lei�h��reportDetails.executingEntity�h�h�u}�(hތlei:sample_lei�h��seller�h�h�u}�(hތlei:sample_lei�h��sellerDecisionMaker�h�h�u}�(hތlei:sample_lei�h��counterparty�h�h�u}�(hތ
id:4006656�h��clientIdentifiers.client�h�h�u}�(hތ
id:4006656�h��trader�h�h�ue]�(}�(hތlei:sample_lei�h�h�h�h�u}�(hތlei:sample_lei�h��buyerDecisionMaker�h�h�u}�(hތlei:sample_lei�h�h�h�h�u}�(hތ
id:4006545�h�h�h�h�u}�(hތlei:sample_lei�h�j  h�h�u}�(hތ
id:4006545�h�j  h�h�u}�(hތ
id:4006545�h�j  h�h�ue]�(}�(hތlei:sample_lei�h�h�h�h�u}�(hތlei:sample_lei�h�j
  h�h�u}�(hތlei:sample_lei�h�h�h�h�u}�(hތ
id:4006545�h�h�h�h�u}�(hތlei:sample_lei�h�j  h�h�u}�(hތ
id:4006545�h�j  h�h�u}�(hތ
id:4006545�h�j  h�h�ue�lei:sample_lei��lei:sample_lei��lei:sample_lei��
id:4006656��lei:sample_lei��lei:sample_lei��lei:sample_lei��
id:4006545��
id:4006545��lei:sample_lei��lei:sample_lei��lei:sample_lei�h،lei:sample_lei��lei:sample_lei��lei:sample_lei�h�h�h�h�h�h�h�h،
id:4006656��
id:4006545��
id:4006545��
id:4006656��
id:4006545��
id:4006545�]�(h�h�h�h�h�h�j  j  e]�(h�j	  j  j  j  j  j  j  e]�(h�j  j  j  j  j!  j#  j%  e�lei:sample_lei�j?  j?  �
id:4006656��
id:4006545��
id:4006545�h�h�h�h�h�h��OT��CD�jD  h،CFD�jE  �EQ�jF  jF  h�h�h�h�h�h،CASH�jG  jG  �
US Wall St 30��
US Nas 100�jI  �OTHR�jJ  jJ  �MONE�jK  jK  �JEIXCC�jL  jL  h�h�h�example_isi2��US6311011026�jN  �US30��US100�jP  h�jE  jE  �XXXXUS2605661048USD��XXXXUS6311011026USDCFD��XXXXUS6311011026USDCFD�� �jT  jT  �XXXXexample_isi2USD��
XXXXUSDCFD��
XXXXUSDCFD��XXXXUS30��XXXXUS100CFD��XXXXUS100CFD��example_isi2USDXXXX��USDXXXX��USDXXXX�j[  jR  jS  G�      G�      h�et�bhhK ��h��R�(KKK��h!�]�(�XXXX�jd  jd  et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hkat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h)h3h4hmet�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h5h\et�bhpNu��R�h
h}�(hhhK ��h��R�(KKA��h!�]�(h%h&h'h(h*h+h,h-h.h/h0h1h2h6h7h8h9h:h;h<h=h>h?h@hAhBhChDhEhFhGhHhIhJhKhLhMhNhOhPhQhRhShThVhWhXhYhZh[h]h^h_h`hahbhchdhehfhghhhihjhlet�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hnat�bhpNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���K0K1K��R�u}�(j�  h�j�  j�  KFKGK��R�u}�(j�  h�j�  h�(�                             H       �h�K��h�t�R�u}�(j�  h�j�  j�  KK^K'��R�u}�(j�  h�j�  h�(�                                                               	       
                     
                                                                                                                        !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       1       2       3       4       5       6       8       9       :       ;       <       =       >       ?       @       A       B       C       D       E       G       �h�KA��h�t�R�u}�(j�  ja  j�  j�  KIKJK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.