���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK<��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�	S3FileURL��BeginString��
BodyLength��MsgType��	MsgSeqNum��LastMsgSeqNumProcessed��SendingTime��SenderCompID��SenderSubID��TargetCompID��TargetSubID��TargetLocationID��Account��AvgPx��ClOrdID��CumQty��ExecID��
ExecTransType��LastPx��LastQty��OrderID��	OrdStatus��OrdType��OrigClOrdID��
SecurityID��Side��Symbol��TransactTime��	TradeDate��SecurityDesc��ExecType��SecurityType��ContraTrader��ContraBroker��MultiLegReportingType��SecondaryExecID��ManualOrderIndicator��CustOrderHandlingInst��ff_5979��ff_9717��ff_1362��
FillExecID��FillPx��FillQty��
FillYieldType��CheckSum��OrderQty��Price��TimeInForce��	LeavesQty��
ExpireDate��ff_393��AggressorIndicator��ff_37711��StopPx��Text��ff_1031��PositionEffect��ff_1028��SecondaryOrderId�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C(                                    �t�bhbNu��R�e]�(hhK ��h��R�(KKK��h!�]�(��s3://shrenik.uat.steeleye.co/fix/order-feed-cme-fix/CME_ALL_6_APR/073989f390055bd2575c6cb68a92adc65c313a0d9fe4a526fb0ee3a4e52a8a68_7813.fix���s3://shrenik.uat.steeleye.co/fix/order-feed-cme-fix/CME_ALL_6_APR/1f12bbc6be46e2bc4bb3ee93e093cf666c16deba9b93c343f8c3bf36d5fb096c_7796.fix���s3://shrenik.uat.steeleye.co/fix/order-feed-cme-fix/CME_ALL_6_APR/68688494b6d8c5b96dd039c379911b67e4c178a4d73b92921a1f55de926c86d1_7816.fix���s3://shrenik.uat.steeleye.co/fix/order-feed-cme-fix/CME_ALL_6_APR/aa844d38e44cc6027d211289e768d94ef7e8d1f2f857a3e946cd4147f0057dd6_7815.fix���s3://shrenik.uat.steeleye.co/fix/order-feed-cme-fix/CME_ALL_6_APR/f0526b509f8c704e4cd0902ae42778a30c1924421bbbc3ebe9c198d84e74b87c_7812.fix��FIX.4.2��FIX.4.2��FIX.4.2��FIX.4.2��FIX.4.2�et�bhhK ��h��R�(KKK��ho�C(�      ]      �      �      �      �t�bhhK ��h��R�(KKK��h!�]�(�8�h�h�h�h�et�bhhK ��h��R�(KKK��ho�CP�      t      �      �      �      �      s      �      �      �      �t�bhhK ��h��R�(KKK��h!�]�(�20220406-09:01:04.624��20220406-08:53:39.552��20220406-09:01:04.628��20220406-09:01:04.628��20220406-09:01:04.624��CME��CME��CME��CME��CME��G�h�h�h�h��GHW3TDN��GHW3TDN��GHW3TDN��GHW3TDN��GHW3TDN��	CME_GLANE��	CME_GLANE��	CME_GLANE��	CME_GLANE��	CME_GLANE��GB��GB��GB��GB��GB��KBL01��KBL01��KBL01��KBL01��KBL01�et�bhhK ��h��R�(KKK��h�f8�����R�(KhpNNNJ����J����K t�b�C(                                        �t�bhhK ��h��R�(KKK��h!�]�(�16319346��16319346��16319346��16319346��16319346�et�bhhK ��h��R�(KKK��hŉC(     �P@              l@     �b@      F@�t�bhhK ��h��R�(KK,K��h!�]�(�58661:M:86568TN0000001��58661:138850513��58661:M:86574TN0000002��58661:M:86573TN0000005��58661:M:86567TN0000004��0�h�h�h�h�G?�      �pandas._libs.missing��NA���G?�      G?�      G?�      G@P�     h�G@c�     G@Z�     G@F      �58116018859828��58116018859828��58116018859828��58116018859828��58116018859828��1�h�2�h�h�h�h�h�h�h�h�h�h�h�h�553201��876241��553201��605219��605219�h�h�h�h�h�TE��T$��TE��TE��TE��20220406-09:01:04.620��20220406-08:53:39.552��20220406-09:01:04.624��20220406-09:01:04.624��20220406-09:01:04.620��20220406�h�20220406��20220406��20220406��ZN2J2 P1180��UD:T$: 23 0406876241��ZN2J2 P1180��ZN2J2 P1195��ZN2J2 P1195�h�h�h�h�h�OPT�h�OPT��OPT��OPT��TRADE�h�TRADE��TRADE��TRADE��CME000A�h�CME000A��CME000A��CME000A�h�h�h�h�h�58116018859828202204061�h�58116018859828202204062��58116018859828202204062��58116018859828202204061��Y�j  j  j  j  j  j  j  j  j  �1649235664620273223��1649235219552549683��1649235664624365187��1649235664624365187��1649235664620273223��16319346��16319346��16319346��16319346��16319346�h�h�h�h�h�Y1�h�Y1��Y1��Y1�G?�      h�G?�      G?�      G?�      G@P�     h�G@c�     G@Z�     G@F      �14�h�14��14��14��062��217��239��173��012�h�G@R�     h�h�h�h�G?�      h�h�h�h�h�h�h�h�h�G@R�     h�h�h�h�20220406�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&et�bhbNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bhbNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bhbNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h)h*et�bhbNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h+h,h-h.h/h0h1et�bhbNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bhbNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bhbNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhbNu��R�h
h}�(hhhK ��h��R�(KK,��h!�]�(h5h6h7h8h9h:h;h<h=h>h?h@hAhBhChDhEhFhGhHhIhJhKhLhMhNhOhPhQhRhShThUhVhWhXhYhZh[h\h]h^h_h`et�bhbNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hy�mgr_locs��builtins��slice���K KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KK
K��R�u}�(j�  h�j�  j�  K
KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KK<K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.