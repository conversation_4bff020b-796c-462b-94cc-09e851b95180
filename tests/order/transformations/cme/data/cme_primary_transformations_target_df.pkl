���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK3��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��"orderIdentifiers.aggregatedOrderId��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo��%executionDetails.passiveOnlyIndicator��executionDetails.validityPeriod��	_order.id��_orderState.id��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��_order.__meta_model__��_orderState.__meta_model__��(orderIdentifiers.initialOrderDesignation��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��.orderIdentifiers.tradingVenueTransactionIdCode��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��"priceFormingData.remainingQuantity��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��timestamps.orderReceived��timestamps.orderSubmitted��timestamps.tradingDateTime��#transactionDetails.buySellIndicator��!transactionDetails.positionEffect��$_order.transactionDetails.recordType��"transactionDetails.tradingDateTime��'_orderState.transactionDetails.quantity��__multi_leg_reporting_type__��__security_id__��	__price__��__last_px__��__stop_px__��__newo_in_file__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C(                                    �t�bhYNu��R�e]�(hhK ��h��R�(KK2K��h!�]�(�SELL��BUYI�hshththshthshtht�CME�huhuhuhu�
2022-04-06��
2022-04-06��
2022-04-06��
2022-04-06��
2022-04-06��0�h{h{h{h{hshthshtht�NEWO�h|h|h|h|�PARF��pandas._libs.missing��NA����FILL�h�h}�Limit�h�h�h�h��ALGO�h�h�h�h�h�]��DAVY�ah�h�h��58116018859828|553201��58116018859828|876241��58116018859828|553201��58116018859828|605219��58116018859828|605219�h�h�h�h�h�]�(}�(�labelId��id:kbl01��path��buyer��type��se_schema.static.market��IdentifierType����ARRAY���R�u}�(h��lei:894500wota5040khgx73�h��reportDetails.executingEntity�h�h��OBJECT���R�u}�(h��id:cme�h��seller�h�h�u}�(h��id:cme�h��counterparty�h�h�u}�(h��id:kbl01�h��clientIdentifiers.client�h�h�u}�(h��id:cme_glane�h��trader�h�h�ue]�(}�(h��id:kbl01�h�h�h�h�u}�(h��lei:894500wota5040khgx73�h�h�h�h�u}�(h��id:cme�h�h�h�h�u}�(h��id:cme�h�h�h�h�u}�(h��id:kbl01�h�h�h�h�u}�(h��id:cme_glane�h�h�h�h�ue]�(}�(h��id:kbl01�h�h�h�h�u}�(h��lei:894500wota5040khgx73�h�h�h�h�u}�(h��id:cme�h�h�h�h�u}�(h��id:cme�h�h�h�h�u}�(h��id:kbl01�h�h�h�h�u}�(h��id:cme_glane�h�h�h�h�ue]�(}�(h��id:kbl01�h�h�h�h�u}�(h��lei:894500wota5040khgx73�h�h�h�h�u}�(h��id:cme�h�h�h�h�u}�(h��id:cme�h�h�h�h�u}�(h��id:kbl01�h�h�h�h�u}�(h��id:cme_glane�h�h�h�h�ue]�(}�(h��id:kbl01�h�h�h�h�u}�(h��lei:894500wota5040khgx73�h�h�h�h�u}�(h��id:cme�h�h�h�h�u}�(h��id:cme�h�h�h�h�u}�(h��id:kbl01�h�h�h�h�u}�(h��id:cme_glane�h�h�h�h�ue�lei:894500wota5040khgx73��lei:894500wota5040khgx73��lei:894500wota5040khgx73��lei:894500wota5040khgx73��lei:894500wota5040khgx73��id:kbl01��id:kbl01��id:kbl01��id:kbl01��id:kbl01��id:cme��id:cme��id:cme��id:cme��id:cme��id:cme��id:cme��id:cme��id:cme��id:cme�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��id:kbl01��id:kbl01��id:kbl01��id:kbl01��id:kbl01��id:cme_glane��id:cme_glane��id:cme_glane��id:cme_glane��id:cme_glane�]�(h�h�h�h�h�h�e]�(h�h�h�h�h�h�e]�(h�h�h�h�h�h�e]�(h�h�h�h�h�h�e]�(h�h�h�h�h�h�e�Order�j  j  j  j  �
OrderState�j  j  j  j  �1649235664620273223��1649235219552549683��1649235664624365187��1649235664624365187��1649235664620273223��16319346��16319346��16319346��16319346��16319346�h�h�h�h�h�h�h�h�h�h��58661:M:86568TN0000001��58661:138850513��58661:M:86574TN0000002��58661:M:86573TN0000005��58661:M:86567TN0000004�h�G@R�     h�h�h��0�G@R�     j  j  j  G@P�     h�G@c�     G@Z�     G@F      j
  j  j  j  j  �2022-04-06T09:01:04.624000Z��2022-04-06T08:53:39.552000Z��2022-04-06T09:01:04.628000Z��2022-04-06T09:01:04.628000Z��2022-04-06T09:01:04.624000Z�j  j  j  j  j  j  j  j  j  j  hshthshthth�h�h�h�h��Market Side�j  j  j  j  j  j  j  j  j  G@P�     h�G@c�     G@Z�     G@F      �2�h�j  j  j  �553201��876241��553201��605219��605219�h�G?�      h�h�h�G?�      h�G?�      G?�      G?�      h�h�h�h�h������et�bhhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C�t�be]�(h
h}�(hhhK ��h��R�(KK2��h!�]�(h%h&h'h(h)h*h+h,h-h.h0h1h2h3h4h5h6h7h8h9h:h;h<h=h>h?h@hAhBhChDhEhFhGhHhIhJhKhLhMhNhOhPhQhRhShThUhVhWet�bhYNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhYNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hp�mgr_locs�hhK ��h��R�(KK2��h�i8�����R�(KhgNNNJ����J����K t�b�B�                                                                  	                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       �t�bu}�(jD  j"  jE  �builtins��slice���K
KK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.