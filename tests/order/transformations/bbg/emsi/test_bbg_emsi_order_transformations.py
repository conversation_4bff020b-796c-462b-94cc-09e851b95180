import os
from pathlib import Path

import pandas as pd
import pytest
from mock.mock import <PERSON><PERSON><PERSON>
from prefect import context
from swarm.conf import SettingsCls
from swarm.task.auditor import Auditor

from swarm_tasks.order.feed.bbg.emsi.static import TempColumns
from swarm_tasks.order.transformations.order_transform_maps import (
    bbg_audt_emsi_transform_map,
)
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
TEST_FILE_PATH = TEST_FILES_DIR.joinpath(r"test_file.pkl")
ARISAIG_TEST_FILE_PATH = TEST_FILES_DIR.joinpath(r"test_file_arisaig.pkl")
EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(r"expected_file.pkl")

PARTNERS_EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(r"partners_expected_file.pkl")
CAXTON_EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(r"caxton_expected_file.pkl")
ARISAIG_EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(r"arisaig_expected_file.pkl")


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestBbgEmsiOrderTransformations:
    """
    Test BBG EMSI Order Transformations
    """

    @staticmethod
    def mock_get_tenant_lei(mocker, test_df):
        mock_get_tenant_lei = mocker.patch.object(GetTenantLEI, "process")
        tenant_lei_df = pd.DataFrame(index=test_df.index)
        tenant_lei_df = tenant_lei_df.assign(
            **{TempColumns.ACCOUNT_FIRM_LEI: "894500WOTA5040KHGX73"}
        )
        mock_get_tenant_lei.return_value = tenant_lei_df
        return mock_get_tenant_lei

    @pytest.mark.parametrize(
        "expected_file,bucket_name",
        [
            # test for default tenant
            (EXPECTED_FILE_PATH, "default.dev.steeleye.co"),
            # test for Partners tenant
            (PARTNERS_EXPECTED_FILE_PATH, "partners.dev.steeleye.co"),
            # test for Caxton tenant
            (CAXTON_EXPECTED_FILE_PATH, "caxton.dev.steeleye.co"),
            # test for Arisaig tenant
            (ARISAIG_EXPECTED_FILE_PATH, "arisaig.dev.steeleye.co"),
        ],
    )
    def test_end_to_end_transformations(
        self,
        expected_file: Path,
        bucket_name: str,
        mocker: MagicMock,
        auditor: Auditor,
    ):
        os.environ["SWARM_FILE_URL"] = "dummy_test_path"

        # Mock realm to chose default transformation
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = bucket_name

        test_file_path = (
            ARISAIG_TEST_FILE_PATH
            if bucket_name == "arisaig.dev.steeleye.co"
            else TEST_FILE_PATH
        )
        source_frame = pd.read_pickle(test_file_path)
        expected = pd.read_pickle(expected_file)

        self.mock_get_tenant_lei(mocker, source_frame)

        task = bbg_audt_emsi_transform_map.transformation(
            tenant=bucket_name.split(".")[0]
        )(source_frame=source_frame, logger=context.get("logger"), auditor=auditor)
        result = task.process()

        pd.testing.assert_frame_equal(left=result, right=expected, check_dtype=False)
