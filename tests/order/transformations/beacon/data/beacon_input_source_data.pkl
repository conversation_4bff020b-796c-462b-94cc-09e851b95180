���3      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK=��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�COUNTRY��CURRENCY��TICKER��BBSYMBOL��UNDERLYING_TICKER��UNDERLYING_ISIN��ISIN��RIC��
SECURITY_TYPE��
SECURITY_NAME��EXCHANGE_REGION��MIC��
EXCHANGE_NAME��ISCFD��CLIENTID��PMGROUP��ACCOUNT��
CLIENTORDERID��ORDERID��ORDER_STATUS��
ORDER_TYPE��MSG_TYPE��	MSGSEQNUM��POSITION_EFFECT��
TIME_IN_FORCE��MANAGER��DESK��TRADER��
TRADE_DATE��SIDE��ORDERQTY��CUMQTY��
LEAVES_QTY��LIMIT_PRICE��LAST_MARKET��PRICE��PRICE_NOTATION��
TRADED_QTY��QUANTITY_NOTATION��TRADING_CAPACITY��
LAST_CAPACITY��BROKER_CODE��BROKER_NAME��
COUNTER_PARTY��TRADE_ID��NET_PROCEEDS��COVERED_OR_UNCOVERED��MATURITY_MONTH_YEAR��EXPIRE_TIME��
TRANSACT_TIME��LEI��LOCATION��	BROKER_SI��
ORDER_TIME��EXECUTION_TIME��UNDERYLING_TICKER��EFFECTIVE_TIME��EXPIRE_DATE��MATURITY_DAY��	PUTORCALL��STRIKE_PRICE�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK	��h�i8�����R�(K�<�NNNJ����J����K t�b�CH                                                                �t�bhc�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KKK	��h!�]�(�US��IT�h~h~h~h~�FR�h~h~et�b�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK	��h!�]�(�USD��EUR�h�h�h�h�h�h�h�et�b�_dtype�h��StringDtype���)��ubhhK ��h��R�(KKK	��h!�]�(�LMNR��SPM��NRIX��VRM��COP��ALDX��JFCEZ2��GRTS��ALLO�et�bh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�LMNR US Equity��
SPM IM Equity��NRIX US Equity��
VRM US Equity��
COP US Equity��ALDX US Equity��
CFZ2 Index��GRTS US Equity��ALLO US Equity�et�bh�h�)��ubhhK ��h��R�(KKK	��h!�]�(�LMNR��SPM��NRIX��VRM��COP��ALDX��JFCE��GRTS��ALLO�et�bh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�US5327461043��IT0005495657��US67080M1036��US92918V1098��US20825C1045��US01438T1060��pandas._libs.missing��NA����US39868T1051��US0197701065�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�US5327461043��IT0005495657��US67080M1036��US92918V1098��US20825C1045��US01438T1060��FRENX0864111��US39868T1051��US0197701065�et�bh�h�)��ubhhK ��h��R�(KKK	��h!�]�(�LMNR.OQ��SPMI.MI��NRIX.OQ��VRM.OQ��COP.N��ALDX.OQ��FCEZ2��GRTS.OQ��ALLO.OQ�et�bh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�Stock�j   j   j   j   j   �FUT�j   j   et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�	Limoneira��Saipem��Nurix��Vroom��ConocoPhillips��Aldeyra��CAC 40 Index;Z22��
Gritstone Bio��Allogene�et�bh�h�)��ubhhK ��h��R�(KKK	��h!�]�(�Americas��EMEA�j  j  j  j  j  j  j  et�bh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�XNGS��MTAA��XNMS�j%  �XNYS��XNCM��XMAT�j%  j%  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�NAS��MIL�j4  j4  �NYS�j4  �PAF�j4  j4  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�0�jA  jA  jA  jA  jA  jA  jA  jA  et�bh�h�)��ubhhK ��h��R�(KKK	��h!�]�(�
TEST00000TEST�jI  jI  jI  jI  jI  jI  jI  jI  �
EwenHiscox��ArthursA�jJ  jJ  jJ  jJ  jK  jJ  jJ  et�bh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�2022_YC0R1JWGNWFG��2022_YF59778L0Q82��2022_YC0R1JWGNWJM��2022_YC0R1JWGNWG7��2022_YC0R1JWGNW8T��2022_YC0R1JWGNW9L��2022_YF5977JXM7XG��2022_YC0R1JWGNWCN��2022_YC0R1JWGNWB5�et�bh�h�)��ubhhK ��h��R�(KKK	��h!�]�(�TEST�jd  �*2022_b188c793-c41e-4beb-aa19-38c455c4016e5��*2022_5fbbc576-6025-4029-8d55-2051b86c5c9d1��*2022_d6559918-8851-4d4f-99ba-c5bc1e8986c85��*2022_ee51b735-562d-4829-bbb6-d76f0f0b9fac1��*2022_48ce5923-4b2c-40ea-a4ce-fdf0be81288a1��*2022_0c716315-ebcf-4881-a11d-64ce80ebab311��*2022_22067694-4c7c-4560-8ad4-e9407b9d98225�et�bh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�
PartFilled�jt  jt  jt  jt  jt  jt  jt  jt  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�Market�j~  j~  j~  j~  j~  j~  j~  j~  et�bh�h�)��ubhhK ��h��R�(KKK	��h!�]�(�Execution Report�j�  j�  j�  j�  j�  j�  j�  j�  �300842��182676��284556��316515��98970��337261��12920��298124��333875�G�      G�      G�      G�      G�      G�      G�      G�      G�      et�bh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�DAY�j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�dhiscox��aarthurs�j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubhhK ��h��R�(KKK	��h!�]�(�	PM_DIRECT�j�  j�  j�  j�  j�  j�  j�  j�  et�bh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�dhiscox��aarthurs�j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�
2022-12-02�j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�Buy��Sell��Short�j�  j�  j�  j�  j�  j�  et�bh�h�)��ubhhK ��h��R�(KKK	��h�f8�����R�(KhqNNNJ����J����K t�b�B        ��@    ��A      @     @�@     H�@     �@      $@     0�@     Ȅ@     �s@    |�A      Y@     ��@      I@     P�@       @      �@      y@     �@     `�@     �x@     p�@     ��@      ~@       @     P�@     �p@������)@X9��v�?)\���((@R���Q�?R���^@�(\���@    �P�@ffffff@R����"@�t�bh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�EDGX��MTAA��NASD��JPBX��XNYS��JPMX��XPAR�j�  j�  et�bh�h�)��ubhhK ��h��R�(KKK	��j�  �CH   @33*@    �M�?     �(@   �Pk�?   ��^@   �z@     J�@    Ԛ@    ��"@�t�bh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�Monetary�j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubhhK ��h��R�(KKK	��h!�]�(�11.0��3414.0��100.0�j�  �13.0�j�  �1.0�j�  j�  et�bh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�Unit�j
  j
  j
  j
  j
  j
  j
  j
  et�bh�h�)��ubhhK ��h��R�(KKK	��h!�]�(�AOTC�j  j  j  j  j  j  j  j  �Agent��Cross as pricipal�j  j  j  �Cross as Agent�j  j  j  et�bh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�	JPMS-ALGO��	GSAT-EURO�j  j  j  j  �GS-EU-ALGFT�j  j  et�bh�h�)��ubhhK ��h��R�(KKK	��h!�]�(�JP MORGAN ALGO��
GOLDMAN SACHS�j(  j(  j(  j(  j)  j(  j(  �K6Q0W1PS1L1O4IQL9C32��W22LROWP2IHZNBB6K528�j*  j*  j*  j*  j+  j*  j*  et�bh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�0202268f1766f-2c0d-43e7-a8e1-0f38992c2bfa1x300842��020222f4176a2-a4dd-402e-95f2-916ae35901832x182676��02022b188c793-c41e-4beb-aa19-38c455c4016e5x284556��020225fbbc576-6025-4029-8d55-2051b86c5c9d1x316515��/2022d6559918-8851-4d4f-99ba-c5bc1e8986c85x98970��02022ee51b735-562d-4829-bbb6-d76f0f0b9fac1x337261��/202248ce5923-4b2c-40ea-a4ce-fdf0be81288a1x12920��020220c716315-ebcf-4881-a11d-64ce80ebab311x298124��0202222067694-4c7c-4560-8ad4-e9407b9d98225x333875�et�bh�h�)��ubhhK ��h��R�(KKK	��h!�]�(�144.1��3478.8659999999995��1225.0��115.12��1599.13��652.0��6730.0��295.06��946.28�G�      G�      G�      G�      G�      G�      G�      G�      G�      et�bh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�hӌ202212�h�h�et�bh�h�)��ubhhK ��h��R�(KKK	��h!�]�(G�      G�      G�      G�      G�      G�      G�      G�      G�      et�bh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�2022-12-02T20:25:46��2022-12-02T16:11:50.599��2022-12-02T19:53:48��2022-12-02T20:42:38��2022-12-02T15:06:17��2022-12-02T20:57:38��2022-12-02T08:21:33.382��2022-12-02T20:20:59��2022-12-02T20:55:37�et�bh�h�)��ubhhK ��h��R�(KKK	��h!�]�(G�      �W22LROWP2IHZNBB6K528�G�      G�      G�      G�      G�      G�      G�      et�bh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�XOFF�j~  j~  j~  j~  j~  j~  j~  j~  et�bh�h�)��ubhhK ��h��R�(KKK	��h!�]�(G�      G�      G�      G�      G�      G�      G�      G�      G�      et�bh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�2022-12-02T19:15:40.931��2022-12-02T10:04:25��2022-12-02T19:15:42.997��2022-12-02T19:15:41.125��2022-12-02T14:50:23.247��2022-12-02T19:15:38.340��2022-12-02T07:32:09.104��2022-12-02T19:15:39.727��2022-12-02T19:15:38.533�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�2022-12-02T20:25:46��2022-12-02T16:11:50.599��2022-12-02T19:53:48��2022-12-02T20:42:38��2022-12-02T15:06:17��2022-12-02T20:57:38��2022-12-02T08:21:33.382��2022-12-02T20:20:59��2022-12-02T20:55:37�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubhhK ��h��R�(KKK	��j�  �CH      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KKK	��h!�]�(�TEST�j�  j�  j�  j�  j�  j�  j�  j�  et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h0at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h3h4et�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h:h;h<et�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hChDhEhFet�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hLhMet�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hOhPet�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hRhSet�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hTat�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hYat�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hZat�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h[at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h]at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h^at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h`at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�haat�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhcNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h{�mgr_locs��builtins��slice���K KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KK	K��R�u}�(j�  j  j�  j�  K	K
K��R�u}�(j�  j  j�  j�  K
KK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j-  j�  j�  KK
K��R�u}�(j�  j:  j�  j�  K
KK��R�u}�(j�  jF  j�  j�  KKK��R�u}�(j�  jM  j�  j�  KKK��R�u}�(j�  ja  j�  j�  KKK��R�u}�(j�  jm  j�  j�  KKK��R�u}�(j�  jw  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KK"K��R�u}�(j�  j�  j�  j�  K"K#K��R�u}�(j�  j�  j�  j�  K#K$K��R�u}�(j�  j�  j�  j�  K$K%K��R�u}�(j�  j�  j�  j�  K%K&K��R�u}�(j�  j  j�  j�  K&K'K��R�u}�(j�  j  j�  j�  K'K)K��R�u}�(j�  j  j�  j�  K)K*K��R�u}�(j�  j%  j�  j�  K*K,K��R�u}�(j�  j-  j�  j�  K,K-K��R�u}�(j�  jA  j�  j�  K-K/K��R�u}�(j�  jN  j�  j�  K/K0K��R�u}�(j�  jZ  j�  j�  K0K1K��R�u}�(j�  j^  j�  j�  K1K2K��R�u}�(j�  jr  j�  j�  K2K3K��R�u}�(j�  jw  j�  j�  K3K4K��R�u}�(j�  j�  j�  j�  K4K5K��R�u}�(j�  j�  j�  j�  K5K6K��R�u}�(j�  j�  j�  j�  K6K7K��R�u}�(j�  j�  j�  j�  K7K8K��R�u}�(j�  j�  j�  j�  K8K9K��R�u}�(j�  j�  j�  j�  K9K:K��R�u}�(j�  j�  j�  j�  K:K;K��R�u}�(j�  j�  j�  j�  K;K<K��R�u}�(j�  j�  j�  j�  K<K=K��R�u}�(j�  j�  j�  j�  KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.