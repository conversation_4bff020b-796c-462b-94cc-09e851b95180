���=      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKE��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��"_order.executionDetails.limitPrice��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��!_order.executionDetails.orderType�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	_order.id��_orderState.id��marketIdentifiers.instrument��bbg_figi_id_attribute��underlying_symbol_attribute��underlying_isin_attribute��isin_attribute��asset_class_attribute��currency_attribute��expiry_date_attribute��option_strike_price_attribute��option_type_attribute��venue_attribute��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��_order.__meta_model__��_orderState.__meta_model__��orderIdentifiers.orderIdCode��-_orderState.orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��priceFormingData.price��)_order.priceFormingData.remainingQuantity��&_order.priceFormingData.tradedQuantity��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��#transactionDetails.buySellIndicator�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��__asset_class__��__currency__��
__expiry__��__input_asset_class__��__input_venue__��__expiry_date__��__newo_in_file__��__strike_price_type__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK	��h�i8�����R�(K�<�NNNJ����J����K t�b�CH                                                                �t�bhk�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KK.K	��h!�]�(�BUYI��SELL�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
Beacon OMS�h�h�h�h�h�h�h�h��
2022-12-02��
2022-12-02��
2022-12-02��
2022-12-02��
2022-12-02��
2022-12-02��
2022-12-02��
2022-12-02��
2022-12-02�h�h�h�h�h�h�h�h�h��NEWO�h�h�h�h�h�h�h�h��PARF�h�h�h�h�h�h�h�h��AOTC�h�h�h�h�h�h�h�h�]��DAVY�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a�2022_YC0R1JWGNWFG��2022_YF59778L0Q82��2022_YC0R1JWGNWJM��2022_YC0R1JWGNWG7��2022_YC0R1JWGNW8T��2022_YC0R1JWGNW9L��2022_YF5977JXM7XG��2022_YC0R1JWGNWCN��2022_YC0R1JWGNWB5�h�h�h�h�h�h�h�h�h�]�(}�(�labelId��US5327461043��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(h��US5327461043USDEDGX�h�h�h�h�u}�(h��LMNR US Equity�h�h�h�h�ue]�(}�(h��IT0005495657�h�h�h�h�u}�(h��IT0005495657EURMTAA�h�h�h�h�u}�(h��
SPM IM Equity�h�h�h�h�ue]�(}�(h��US67080M1036�h�h�h�h�u}�(h��US67080M1036USDNASD�h�h�h�h�u}�(h��NRIX US Equity�h�h�h�h�ue]�(}�(h��US92918V1098�h�h�h�h�u}�(h��US92918V1098USDJPBX�h�h�h�h�u}�(h��
VRM US Equity�h�h�h�h�ue]�(}�(h��US20825C1045�h�h�h�h�u}�(h��US20825C1045USDXNYS�h�h�h�h�u}�(h��
COP US Equity�h�h�h�h�ue]�(}�(h��US01438T1060�h�h�h�h�u}�(h��US01438T1060USDJPMX�h�h�h�h�u}�(h��ALDX US Equity�h�h�h�h�ue]�(}�(h��FRENX0864111�h�h�h�h�u}�(h��FRENX0864111EURXPAR�h�h�h�h�u}�(h��XPARJFCEFF2022-12-28 00:00:00�h�h�h�h�u}�(h��XPARJFCEFF2022-12 00:00:00�h�h�h�h�u}�(h��
CFZ2 Index�h�h�h�h�ue]�(}�(h��US39868T1051�h�h�h�h�u}�(h��US39868T1051USDJPBX�h�h�h�h�u}�(h��GRTS US Equity�h�h�h�h�ue]�(}�(h��US0197701065�h�h�h�h�u}�(h��US0197701065USDJPBX�h�h�h�h�u}�(h��ALLO US Equity�h�h�h�h�ue�LMNR��SPM��NRIX��VRM��COP��ALDX��JFCE��GRTS��ALLO��cds single stock�h�h�h�h�h��future�h�h��USD��EUR�j   j   j   j   j  j   j   �pandas._libs.missing��NA���j  j  j  j  j  �
2022-12-28�j  j  j  j  j  j  j  j  j  j  j  ]�(}�(h��id:test�h��buyer�h�h��ARRAY���R�u}�(h��lei:549300il8tqt0jmdjj80�h��buyerDecisionMaker�h�j  u}�(h��lei:549300il8tqt0jmdjj80�h��reportDetails.executingEntity�h�h�u}�(h��id:jpms-algo�h��seller�h�j  u}�(h��id:jpms-algo�h��counterparty�h�h�u}�(h��
id:dhiscox�h��:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�h�h�u}�(h��
id:dhiscox�h��1tradersAlgosWaiversIndicators.executionWithinFirm�h�h�u}�(h��id:test�h��clientIdentifiers.client�h�j  u}�(h��
id:dhiscox�h��trader�h�j  ue]�(}�(h��id:gsat-euro�h�j	  h�j  u}�(h��lei:549300il8tqt0jmdjj80�h�j  h�h�u}�(h��id:test�h�j  h�j  u}�(h��lei:549300il8tqt0jmdjj80�h��sellerDecisionMaker�h�j  u}�(h��id:gsat-euro�h�j  h�h�u}�(h��id:aarthurs�h�j  h�h�u}�(h��id:aarthurs�h�j  h�h�u}�(h��id:test�h�j!  h�j  u}�(h��id:aarthurs�h�j$  h�j  ue]�(}�(h��id:jpms-algo�h�j	  h�j  u}�(h��lei:549300il8tqt0jmdjj80�h�j  h�h�u}�(h��id:test�h�j  h�j  u}�(h��lei:549300il8tqt0jmdjj80�h�j.  h�j  u}�(h��id:jpms-algo�h�j  h�h�u}�(h��
id:dhiscox�h�j  h�h�u}�(h��
id:dhiscox�h�j  h�h�u}�(h��id:test�h�j!  h�j  u}�(h��
id:dhiscox�h�j$  h�j  ue]�(}�(h��id:test�h�j	  h�j  u}�(h��lei:549300il8tqt0jmdjj80�h�j  h�j  u}�(h��lei:549300il8tqt0jmdjj80�h�j  h�h�u}�(h��id:jpms-algo�h�j  h�j  u}�(h��id:jpms-algo�h�j  h�h�u}�(h��
id:dhiscox�h�j  h�h�u}�(h��
id:dhiscox�h�j  h�h�u}�(h��id:test�h�j!  h�j  u}�(h��
id:dhiscox�h�j$  h�j  ue]�(}�(h��id:jpms-algo�h�j	  h�j  u}�(h��lei:549300il8tqt0jmdjj80�h�j  h�h�u}�(h��id:test�h�j  h�j  u}�(h��lei:549300il8tqt0jmdjj80�h�j.  h�j  u}�(h��id:jpms-algo�h�j  h�h�u}�(h��
id:dhiscox�h�j  h�h�u}�(h��
id:dhiscox�h�j  h�h�u}�(h��id:test�h�j!  h�j  u}�(h��
id:dhiscox�h�j$  h�j  ue]�(}�(h��id:test�h�j	  h�j  u}�(h��lei:549300il8tqt0jmdjj80�h�j  h�j  u}�(h��lei:549300il8tqt0jmdjj80�h�j  h�h�u}�(h��id:jpms-algo�h�j  h�j  u}�(h��id:jpms-algo�h�j  h�h�u}�(h��
id:dhiscox�h�j  h�h�u}�(h��
id:dhiscox�h�j  h�h�u}�(h��id:test�h�j!  h�j  u}�(h��
id:dhiscox�h�j$  h�j  ue]�(}�(h��id:test�h�j	  h�j  u}�(h��lei:549300il8tqt0jmdjj80�h�j  h�j  u}�(h��lei:549300il8tqt0jmdjj80�h�j  h�h�u}�(h��id:gs-eu-algft�h�j  h�j  u}�(h��id:gs-eu-algft�h�j  h�h�u}�(h��id:aarthurs�h�j  h�h�u}�(h��id:aarthurs�h�j  h�h�u}�(h��id:test�h�j!  h�j  u}�(h��id:aarthurs�h�j$  h�j  ue]�(}�(h��id:test�h�j	  h�j  u}�(h��lei:549300il8tqt0jmdjj80�h�j  h�j  u}�(h��lei:549300il8tqt0jmdjj80�h�j  h�h�u}�(h��id:jpms-algo�h�j  h�j  u}�(h��id:jpms-algo�h�j  h�h�u}�(h��
id:dhiscox�h�j  h�h�u}�(h��
id:dhiscox�h�j  h�h�u}�(h��id:test�h�j!  h�j  u}�(h��
id:dhiscox�h�j$  h�j  ue]�(}�(h��id:jpms-algo�h�j	  h�j  u}�(h��lei:549300il8tqt0jmdjj80�h�j  h�h�u}�(h��id:test�h�j  h�j  u}�(h��lei:549300il8tqt0jmdjj80�h�j.  h�j  u}�(h��id:jpms-algo�h�j  h�h�u}�(h��
id:dhiscox�h�j  h�h�u}�(h��
id:dhiscox�h�j  h�h�u}�(h��id:test�h�j!  h�j  u}�(h��
id:dhiscox�h�j$  h�j  ue�lei:549300il8tqt0jmdjj80��lei:549300il8tqt0jmdjj80��lei:549300il8tqt0jmdjj80��lei:549300il8tqt0jmdjj80��lei:549300il8tqt0jmdjj80��lei:549300il8tqt0jmdjj80��lei:549300il8tqt0jmdjj80��lei:549300il8tqt0jmdjj80��lei:549300il8tqt0jmdjj80��id:test��id:gsat-euro��id:jpms-algo��id:test��id:jpms-algo��id:test��id:test��id:test��id:jpms-algo��id:jpms-algo��id:test��id:test��id:jpms-algo��id:test��id:jpms-algo��id:gs-eu-algft��id:jpms-algo��id:test��lei:549300il8tqt0jmdjj80�j  j  �lei:549300il8tqt0jmdjj80�j  �lei:549300il8tqt0jmdjj80��lei:549300il8tqt0jmdjj80��lei:549300il8tqt0jmdjj80�j  j  �lei:549300il8tqt0jmdjj80��lei:549300il8tqt0jmdjj80�j  �lei:549300il8tqt0jmdjj80�j  j  j  �lei:549300il8tqt0jmdjj80��id:test��id:test��id:test��id:test��id:test��id:test��id:test��id:test��id:test�]�(h�h�h�j  j
  j  j  j  j  j  j  j"  e]�(h�h�h�j&  j(  j*  j,  j/  j1  j3  j5  j7  e]�(h�h�h�j:  j<  j>  j@  jB  jD  jF  jH  jJ  e]�(h�h�h�jM  jO  jQ  jS  jU  jW  jY  j[  j]  e]�(h�h�h�j`  jb  jd  jf  jh  jj  jl  jn  jp  e]�(h�h�h�js  ju  jw  jy  j{  j}  j  j�  j�  e]�(h�h�h�h�h�j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(h�h�h�j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(h�h�h�j�  j�  j�  j�  j�  j�  j�  j�  j�  e�Order�j�  j�  j�  j�  j�  j�  j�  j�  �
OrderState�j�  j�  j�  j�  j�  j�  j�  j�  �0.0�j�  j�  j�  j�  j�  j�  j�  j�  �2022-12-02T19:15:40.931000Z��2022-12-02T10:04:25.000000Z��2022-12-02T19:15:42.997000Z��2022-12-02T19:15:41.125000Z��2022-12-02T14:50:23.247000Z��2022-12-02T19:15:38.340000Z��2022-12-02T07:32:09.104000Z��2022-12-02T19:15:39.727000Z��2022-12-02T19:15:38.533000Z��2022-12-02T20:25:46.000000Z��2022-12-02T16:11:50.599000Z��2022-12-02T19:53:48.000000Z��2022-12-02T20:42:38.000000Z��2022-12-02T15:06:17.000000Z��2022-12-02T20:57:38.000000Z��2022-12-02T08:21:33.382000Z��2022-12-02T20:20:59.000000Z��2022-12-02T20:55:37.000000Z�j�  j�  j�  j�  j�  j�  j�  j�  j�  �2022-12-02T20:25:46.000000Z��2022-12-02T16:11:50.599000Z��2022-12-02T19:53:48.000000Z��2022-12-02T20:42:38.000000Z��2022-12-02T15:06:17.000000Z��2022-12-02T20:57:38.000000Z��2022-12-02T08:21:33.382000Z��2022-12-02T20:20:59.000000Z��2022-12-02T20:55:37.000000Z�h�h�h�h�h�h�h�h�h�j   j  j   j   j   j   j  j   j   �MONE�j  j  j  j  j  j  j  j  j   j  j   j   j   j   j  j   j   �UNIT�j  j  j  j  j  j  j  j  h�h�h�h�h�h�h�h�h�j	  j
  j  j  j
  j  j  j  j  h�h�h�h�h�h�h�h�h�j   j  j   j   j   j   j  j   j   j  j  j  j  j  j  j  j  j  h�h�h�h�h�h�h�h�h�j  j  j  j  j  j  j  j  j  ����������MntryVal�j  j  j  j  j  j  j  j  et�bhhK ��h��R�(KKK	��h�f8�����R�(KhyNNNJ����J����K t�b�B�  ������)@X9��v�?)\���((@R���Q�?R���^@�(\���@    �P�@ffffff@R����"@      �      �      �      �      �      �      �      �      �     ��@    ��A      @     @�@     H�@     �@      $@     0�@     Ȅ@   @33*@    �M�?     �(@   �Pk�?   ��^@   �z@     J�@    Ԛ@    ��"@     �@     `�@     �x@     p�@     ��@      ~@       @     P�@     �p@     �s@    |�A      Y@     ��@      I@     P�@       @      �@      y@     ��@    ��A      @     @�@     H�@     �@      $@     0�@     Ȅ@�t�b�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK	��h!�]�(�Market�j+  j+  j+  j+  j+  j+  j+  j+  et�b�_dtype�j   �StringDtype���)��ubj"  )��}�(j%  hhK ��h��R�(KK	��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(h�h�h�h�h�h�h�h�h�et�bj-  j/  )��ubj"  )��}�(j%  hhK ��h��R�(KK	��j9  �]�(�US5327461043��IT0005495657��US67080M1036��US92918V1098��US20825C1045��US01438T1060�j  �US39868T1051��US0197701065�et�bj-  j/  )��ubj"  )��}�(j%  hhK ��h��R�(KK	��j9  �]�(h�h�h�h�h�h�h�h�h�et�bj-  j/  )��ubj"  )��}�(j%  hhK ��h��R�(KK	��j9  �]�(�EDGX��MTAA��NASD��JPBX��XNYS��JPMX��XPAR�jb  jb  et�bj-  j/  )��ubj"  )��}�(j%  hhK ��h��R�(KK	��h!�]�(�id:jpms-algo��id:gsat-euro��id:jpms-algo��id:jpms-algo��id:jpms-algo��id:jpms-algo��id:gs-eu-algft��id:jpms-algo��id:jpms-algo�et�bj-  j/  )��ubj"  )��}�(j%  hhK ��h��R�(KK	��h!�]�(�
id:dhiscox��id:aarthurs��
id:dhiscox��
id:dhiscox��
id:dhiscox��
id:dhiscox��id:aarthurs��
id:dhiscox��
id:dhiscox�et�bj-  j/  )��ubj"  )��}�(j%  hhK ��h��R�(KK	��h!�]�(�
id:dhiscox��id:aarthurs��
id:dhiscox��
id:dhiscox��
id:dhiscox��
id:dhiscox��id:aarthurs��
id:dhiscox��
id:dhiscox�et�bj-  j/  )��ubj"  )��}�(j%  hhK ��h��R�(KK	��h!�]�(�
id:dhiscox��id:aarthurs��
id:dhiscox��
id:dhiscox��
id:dhiscox��
id:dhiscox��id:aarthurs��
id:dhiscox��
id:dhiscox�et�bj-  j/  )��ubj"  )��}�(j%  hhK ��h��R�(KK	��j9  �]�(h�h�h�h�h�h�h�h�h�et�bj-  j/  )��ubj"  )��}�(j%  hhK ��h��R�(KK	��j9  �]�(�0202268f1766f-2c0d-43e7-a8e1-0f38992c2bfa1x300842��020222f4176a2-a4dd-402e-95f2-916ae35901832x182676��02022b188c793-c41e-4beb-aa19-38c455c4016e5x284556��020225fbbc576-6025-4029-8d55-2051b86c5c9d1x316515��/2022d6559918-8851-4d4f-99ba-c5bc1e8986c85x98970��02022ee51b735-562d-4829-bbb6-d76f0f0b9fac1x337261��/202248ce5923-4b2c-40ea-a4ce-fdf0be81288a1x12920��020220c716315-ebcf-4881-a11d-64ce80ebab311x298124��0202222067694-4c7c-4560-8ad4-e9407b9d98225x333875�et�bj-  j/  )��ubj"  )��}�(j%  hhK ��h��R�(KK	��j9  �]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bj-  j/  )��ubhhK ��h��R�(KKK	��hx�CH                                                                �t�bj"  )��}�(j%  hhK ��h��R�(KK	��j9  �]�(j_  j`  ja  jb  jc  jd  je  jb  jb  et�bj-  j/  )��ubj"  )��}�(j%  hhK ��h��R�(KK	��h!�]�(�XOFF�j�  j�  j�  j�  j�  j�  j�  j�  et�bj-  j/  )��ubj"  )��}�(j%  hhK ��h��R�(KK	��j9  �]�(j_  j`  ja  jb  jc  jd  je  jb  jb  et�bj-  j/  )��ube]�(h
h}�(hhhK ��h��R�(KK.��h!�]�(h%h&h'h(h)h+h,h.h/h0h1h2h4h7h8h9h;h=h>h?h@hBhChFhHhIhJhPhThUhVhWhXhYhZh\h]h^h_hbhchdhehghhhiet�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h*h:hMhNhOhQh[et�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hRat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h`at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�haat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hfat�bhkNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs�hhK ��h��R�(KK.��h�i8�����R�(KhyNNNJ����J����K t�b�Bp                                                    	       
                     
                                                                                    !       #       $       %       +       /       0       1       2       3       4       5       7       8       9       :       =       >       ?       @       B       C       D       �t�bu}�(j�  j  j�  hhK ��h��R�(KK��j�  �C8              (       )       *       ,       6       �t�bu}�(j�  j#  j�  �builtins��slice���KK	K��R�u}�(j�  j1  j�  j�  KKK��R�u}�(j�  j>  j�  j�  KKK��R�u}�(j�  jO  j�  j�  KKK��R�u}�(j�  jX  j�  j�  KKK��R�u}�(j�  jh  j�  j�  KKK��R�u}�(j�  jz  j�  j�  KK K��R�u}�(j�  j�  j�  j�  K K!K��R�u}�(j�  j�  j�  j�  K"K#K��R�u}�(j�  j�  j�  j�  K&K'K��R�u}�(j�  j�  j�  j�  K'K(K��R�u}�(j�  j�  j�  j�  K-K.K��R�u}�(j�  j�  j�  j�  K.K/K��R�u}�(j�  j�  j�  j�  K;K<K��R�u}�(j�  j�  j�  j�  K<K=K��R�u}�(j�  j�  j�  j�  KAKBK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.