����      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK?��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��executionDetails.limitPrice��&executionDetails.outgoingOrderAddlInfo�� executionDetails.tradingCapacity��	hierarchy��	_order.id��_orderState.id��_order.__meta_model__��_orderState.__meta_model__��"orderIdentifiers.aggregatedOrderId��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��orderIdentifiers.parentOrderId��!orderIdentifiers.transactionRefNo��priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.orderReceived��timestamps.orderSubmitted��timestamps.tradingDateTime��timestamps.orderStatusUpdated��#transactionDetails.buySellIndicator�� transactionDetails.priceCurrency��priceFormingData.price��transactionDetails.price�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��underlying_symbol_attribute��expiry_date_attribute��bbg_figi_id_attribute��isin_attribute��asset_class_attribute��currency_attribute��venue_attribute��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��__is_created_through_fallback__��__asset_class_main__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK3��h�i8�����R�(K�<�NNNJ����J����K t�b�B�                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       �t�bhe�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KK	K3��h!�]�(�2�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��1�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��	FlexTrade�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
2022-10-20��
2022-10-20��
2022-10-20��
2022-10-20��
2022-10-20��
2022-10-20��
2022-10-20��
2022-10-20��
2022-10-20��
2022-10-20��
2022-10-20��
2022-10-20��
2022-10-20��
2022-10-20��
2022-10-20��
2022-10-20��
2022-10-20��
2022-10-20��
2022-10-20��
2022-10-20��
2022-10-20��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��
2022-10-19��SELL�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��BUYI�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��NEWO�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��pandas._libs.missing��NA���h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��MARKET�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��LIMIT�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��4.040000�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�b�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK3��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(��115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221020-21:44:50.000;150=1;151=38000;167=CS;198=72294435;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221020-23:09:10.022;150=1;151=37800;167=CS;198=72295646;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221020-23:09:10.022;150=1;151=37797;167=CS;198=72295647;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221020-23:09:10.022;150=1;151=37782;167=CS;198=72295648;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221020-23:09:10.022;150=1;151=37527;167=CS;198=72295649;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221020-23:09:10.022;150=1;151=37515;167=CS;198=72295650;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221020-23:14:57.439;150=1;151=30000;167=CS;198=72295970;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221020-23:17:27.899;150=1;151=150000;167=CS;198=72296093;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221020-23:17:39.026;150=1;151=149383;167=CS;198=72296110;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=148644;167=CS;198=72305561;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=148568;167=CS;198=72305571;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=148560;167=CS;198=72305584;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=144743;167=CS;198=72305597;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=134743;167=CS;198=72305613;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=134731;167=CS;198=72305625;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=134578;167=CS;198=72305638;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=134563;167=CS;198=72305660;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=134486;167=CS;198=72305674;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=134471;167=CS;198=72305686;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=134468;167=CS;198=72305703;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=123383;167=CS;198=72305721;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221019-22:01:03.000;150=1;151=48927;167=CS;198=72280281;20001=213800SE4KPTY278KZ54��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXC;48=6182281;49=LQNTEU;60=20221020-00:30:41.630;76=LNEL;150=1;151=28536;167=CS;851=1��u115=LQNTUK;15=AUD;20=0;22=2;29=1;30=CXAP;48=6182281;49=LQNTEU;60=20221020-00:30:41.258;76=LNEL;150=1;151=27072;167=CS��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXC;48=6182281;49=LQNTEU;60=20221020-00:30:41.261;76=LNEL;150=1;151=24876;167=CS;851=1��u115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXC;48=6182281;49=LQNTEU;60=20221020-00:30:41.630;76=LNEL;150=1;151=22680;167=CS��u115=LQNTUK;15=AUD;20=0;22=2;29=1;30=CXAP;48=6182281;49=LQNTEU;60=20221020-01:10:58.407;76=LNEL;150=1;151=21262;167=CS��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXC;48=6182281;49=LQNTEU;60=20221020-01:10:58.411;76=LNEL;150=1;151=19576;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=CXAP;48=6182281;49=LQNTEU;60=20221020-01:10:58.788;76=LNEL;150=1;151=18549;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=JPMX;48=6182281;49=LQNTEU;60=20221020-01:36:05.792;76=LNEL;150=1;151=18293;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXC;48=6182281;49=LQNTEU;60=20221020-01:59:51.728;76=LNEL;150=1;151=17415;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXC;48=6182281;49=LQNTEU;60=20221020-02:15:30.625;76=LNEL;150=1;151=11581;167=CS;851=1��z115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXC;48=6182281;49=LQNTEU;60=20221020-02:15:30.225;76=LNEL;150=1;151=9997;167=CS;851=1��z115=LQNTUK;15=AUD;20=0;22=2;29=1;30=JPMX;48=6182281;49=LQNTEU;60=20221020-02:15:31.488;76=LNEL;150=1;151=9748;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221019-23:02:07.000;76=LNEL;150=1;151=14668;167=CS;851=4��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221019-23:02:07.000;76=LNEL;150=1;151=14666;167=CS;851=4��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221019-23:02:07.000;76=LNEL;150=1;151=14654;167=CS;851=4��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=CXAC;48=6182281;49=LQNTEU;60=20221020-00:29:05.409;76=LNEL;150=1;151=14648;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221020-00:29:09.959;76=LNEL;150=1;151=14584;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=CXAC;48=6182281;49=LQNTEU;60=20221020-00:29:09.959;76=LNEL;150=1;151=14580;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221020-00:30:44.354;76=LNEL;150=1;151=14454;167=CS;851=2��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=CXAC;48=6182281;49=LQNTEU;60=20221020-00:30:44.354;76=LNEL;150=1;151=14376;167=CS;851=2��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=CXAC;48=6182281;49=LQNTEU;60=20221020-00:30:44.354;76=LNEL;150=1;151=14342;167=CS;851=2��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=CXAC;48=6182281;49=LQNTEU;60=20221020-00:30:44.354;76=LNEL;150=1;151=14333;167=CS;851=2��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221020-00:39:35.389;76=LNEL;150=1;151=14240;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221020-00:41:02.878;76=LNEL;150=1;151=14204;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221020-00:42:47.589;76=LNEL;150=1;151=14203;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221020-00:43:50.866;76=LNEL;150=1;151=14145;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=CXAC;48=6182281;49=LQNTEU;60=20221020-00:43:50.867;76=LNEL;150=1;151=14124;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221020-00:43:50.867;76=LNEL;150=1;151=14083;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221020-01:36:04.841;76=LNEL;150=1;151=13580;167=CS;851=1�et�b�_dtype�h��StringDtype���)��ubhhK ��h��R�(KKK3��h!�]�(�DEAL�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �
Standalone�j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  et�bh�)��}�(h�hhK ��h��R�(KK3��h̉]�(�CAA0007-20221021�j  j  j  j  j  j  �CAA0068-20221021�j  j  j  j  j  j  j  j  j  j  j  j  j  �CAA0024-20221020��CAA0038-20221020�j  j  j  j  j  j  j  j  j  j  j  �CAA0039-20221020�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bj  j  )��ubh�)��}�(h�hhK ��h��R�(KK3��h̉]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bj  j  )��ubhhK ��h��R�(KKK3��h!�]�(�Order�j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  j+  �
OrderState�j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  j,  et�bh�)��}�(h�hhK ��h��R�(KK3��h̉]�(�13061�j5  j5  j5  j5  j5  j5  j5  j5  j5  j5  j5  j5  j5  j5  j5  j5  j5  j5  j5  j5  �13023��!FSIMALGO::CAA0038-20221020@LQNTIN�j7  j7  j7  j7  j7  j7  j7  j7  j7  j7  j7  �!FSIMALGO::CAA0039-20221020@LQNTIN�j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  et�bj  j  )��ubh�)��}�(h�hhK ��h��R�(KK3��h̉]�(�
1478490821OMS�jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  �
1478268211OMS�jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  et�bj  j  )��ubh�)��}�(h�hhK ��h��R�(KK3��h̉]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bj  j  )��ubh�)��}�(h�hhK ��h��R�(KK3��h̉]�(�CAA0007-20221021�jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  �CAA0024-20221020��CAA0038-20221020�jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  �CAA0039-20221020�jY  jY  jY  jY  jY  jY  jY  jY  jY  jY  jY  jY  jY  jY  jY  jY  et�bj  j  )��ubh�)��}�(h�hhK ��h��R�(KK3��h̉]�(�1344899��1344909��1344910��1344911��1344912��1344913��1344914��1344918��1344919��1345082��1345083��1345084��1345085��1345086��1345087��1345088��1345089��1345090��1345091��1345092��1345093��1343403��32022-10-20 00:30:41705��32022-10-20 00:30:41714��32022-10-20 00:30:41716��32022-10-20 00:30:41851��32022-10-20 01:10:58892��32022-10-20 01:10:58901��32022-10-20 01:10:58903��32022-10-20 01:36:05886��32022-10-20 01:59:52212��32022-10-20 02:15:30757��32022-10-20 02:15:30855��32022-10-20 02:15:31570��32022-10-19 23:02:07465��32022-10-19 23:02:07484��32022-10-19 23:02:07510��32022-10-20 00:29:05553��32022-10-20 00:29:10075��32022-10-20 00:29:10077��32022-10-20 00:30:44464��32022-10-20 00:30:44470��32022-10-20 00:30:44476��32022-10-20 00:30:44483��32022-10-20 00:39:35499��32022-10-20 00:41:02987��32022-10-20 00:42:47698��32022-10-20 00:43:50975��32022-10-20 00:43:50980��32022-10-20 00:43:50987��32022-10-20 01:36:04977�et�bj  j  )��ubh�)��}�(h�hhK ��h��R�(KK3��h̉]�(�12000��200��3��15��255��12��7515��3113��617��739��76��8��3817��10000�j�  �153�j�  �77�j�  j�  �11085��1073��1464�j�  �2196�j�  �1418��1686��1027��256��878��5834��1584��249��332�h�j�  �6��64��4��126��78��34��9��93��36�h��58��21��41��503�et�bj  j  )��ubh�)��}�(h�hhK ��h��R�(KK3��h̉]�(jc  jd  je  jf  jg  jh  ji  jj  jk  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  jy  jz  j{  j|  j}  j~  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bj  j  )��ubhhK ��h��R�(KKK3��hr�B�                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       �t�bhhK ��h��R�(KKK3��h!�]�(�b/Users/<USER>/code/swarm-tasks/tests/order/transformations/flextrade/data/exe/exe_source_frame.pkl�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �2022-10-20T21:44:40.000000Z��2022-10-20T21:44:40.000000Z��2022-10-20T21:44:40.000000Z��2022-10-20T21:44:40.000000Z��2022-10-20T21:44:40.000000Z��2022-10-20T21:44:40.000000Z��2022-10-20T21:44:40.000000Z��2022-10-20T23:17:07.000000Z��2022-10-20T23:17:07.000000Z��2022-10-20T23:17:07.000000Z��2022-10-20T23:17:07.000000Z��2022-10-20T23:17:07.000000Z��2022-10-20T23:17:07.000000Z��2022-10-20T23:17:07.000000Z��2022-10-20T23:17:07.000000Z��2022-10-20T23:17:07.000000Z��2022-10-20T23:17:07.000000Z��2022-10-20T23:17:07.000000Z��2022-10-20T23:17:07.000000Z��2022-10-20T23:17:07.000000Z��2022-10-20T23:17:07.000000Z��2022-10-19T22:00:41.000000Z��2022-10-19T22:34:27.000000Z��2022-10-19T22:34:27.000000Z��2022-10-19T22:34:27.000000Z��2022-10-19T22:34:27.000000Z��2022-10-19T22:34:27.000000Z��2022-10-19T22:34:27.000000Z��2022-10-19T22:34:27.000000Z��2022-10-19T22:34:27.000000Z��2022-10-19T22:34:27.000000Z��2022-10-19T22:34:27.000000Z��2022-10-19T22:34:27.000000Z��2022-10-19T22:34:27.000000Z��2022-10-19T22:34:35.000000Z��2022-10-19T22:34:35.000000Z��2022-10-19T22:34:35.000000Z��2022-10-19T22:34:35.000000Z��2022-10-19T22:34:35.000000Z��2022-10-19T22:34:35.000000Z��2022-10-19T22:34:35.000000Z��2022-10-19T22:34:35.000000Z��2022-10-19T22:34:35.000000Z��2022-10-19T22:34:35.000000Z��2022-10-19T22:34:35.000000Z��2022-10-19T22:34:35.000000Z��2022-10-19T22:34:35.000000Z��2022-10-19T22:34:35.000000Z��2022-10-19T22:34:35.000000Z��2022-10-19T22:34:35.000000Z��2022-10-19T22:34:35.000000Z�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j   j  j  j  j  j  j  j  j  j	  j
  j  j  j
  j  j  j  j  j  �2022-10-20T21:44:50.000000Z��2022-10-20T23:09:10.000000Z��2022-10-20T23:09:10.000000Z��2022-10-20T23:09:10.000000Z��2022-10-20T23:09:10.000000Z��2022-10-20T23:09:10.000000Z��2022-10-20T23:14:57.000000Z��2022-10-20T23:17:27.000000Z��2022-10-20T23:17:39.000000Z��2022-10-21T03:59:55.000000Z��2022-10-21T03:59:55.000000Z��2022-10-21T03:59:55.000000Z��2022-10-21T03:59:55.000000Z��2022-10-21T03:59:55.000000Z��2022-10-21T03:59:55.000000Z��2022-10-21T03:59:55.000000Z��2022-10-21T03:59:55.000000Z��2022-10-21T03:59:55.000000Z��2022-10-21T03:59:55.000000Z��2022-10-21T03:59:55.000000Z��2022-10-21T03:59:55.000000Z��2022-10-19T22:01:03.000000Z��2022-10-20T00:30:41.000000Z��2022-10-20T00:30:41.000000Z��2022-10-20T00:30:41.000000Z��2022-10-20T00:30:41.000000Z��2022-10-20T01:10:58.000000Z��2022-10-20T01:10:58.000000Z��2022-10-20T01:10:58.000000Z��2022-10-20T01:36:05.000000Z��2022-10-20T01:59:51.000000Z��2022-10-20T02:15:30.000000Z��2022-10-20T02:15:30.000000Z��2022-10-20T02:15:31.000000Z��2022-10-19T23:02:07.000000Z��2022-10-19T23:02:07.000000Z��2022-10-19T23:02:07.000000Z��2022-10-20T00:29:05.000000Z��2022-10-20T00:29:09.000000Z��2022-10-20T00:29:09.000000Z��2022-10-20T00:30:44.000000Z��2022-10-20T00:30:44.000000Z��2022-10-20T00:30:44.000000Z��2022-10-20T00:30:44.000000Z��2022-10-20T00:39:35.000000Z��2022-10-20T00:41:02.000000Z��2022-10-20T00:42:47.000000Z��2022-10-20T00:43:50.000000Z��2022-10-20T00:43:50.000000Z��2022-10-20T00:43:50.000000Z��2022-10-20T01:36:04.000000Z�j  j  j  j  j  j  j  j  j  j  j  j  j  j   j!  j"  j#  j$  j%  j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j7  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD�et�bhhK ��h��R�(KKK3��h�f8�����R�(KhsNNNJ����J����K t�b�B0  ������?H�z�G�?H�z�G�?H�z�G�?��Q��?��Q��?��Q��?��Q��?��Q��?�������?�������?�������?�������?�������?�������?�������?�������?�������?�������?�������?�������?�G�z�?)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@�p=
�#@��Q�@��Q�@��Q�@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@������?H�z�G�?H�z�G�?H�z�G�?��Q��?��Q��?��Q��?��Q��?��Q��?�������?�������?�������?�������?�������?�������?�������?�������?�������?�������?�������?�������?�G�z�?)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@�p=
�#@��Q�@��Q�@��Q�@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@�t�bhhK ��h��R�(KKK3��h!�]�(�MONE�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�)��}�(h�hhK ��h��R�(KK3��h̉]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  h�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  h�j�  j�  j�  j�  et�bj  j  )��ubhhK ��h��R�(KKK3��h!�]�(�UNIT�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �Market Side�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j   j!  j"  j#  j$  j%  j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j7  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  �NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��ASXC��CXAP��ASXC��ASXC��CXAP��ASXC��CXAP��JPMX��ASXC��ASXC��ASXC��JPMX��ASXT��ASXT��ASXT��CXAC��ASXT��CXAC��ASXT��CXAC��CXAC��CXAC��ASXT��ASXT��ASXT��ASXT��CXAC��ASXT��ASXT��XOFF�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  ]�}�(�labelId��ERD.NZ��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  j�  j�  j�  j�  j�  ua]�}�(j�  �CWP.AU�j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  uaet�bh�)��}�(h�hhK ��h��R�(KK3��h!�]�(�ERD��ERD��ERD��ERD��ERD��ERD��ERD��ERD��ERD��ERD��ERD��ERD��ERD��ERD��ERD��ERD��ERD��ERD��ERD��ERD��ERD��ERD��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP��CWP�et�bj  j  )��ubhhK ��h��R�(KKK3��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�)��}�(h�hhK ��h��R�(KK3��h̉]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bj  j  )��ubhhK ��h��R�(KKK3��h!�]�(�AU0000��AU0001��AU0002��AU0003��AU0004��AU0005��AU0006��AU0007��AU0008��AU0009��AU00010��AU00011��AU00012��AU00013��AU00014��AU00015��AU00016��AU00017��AU00018��AU00019��AU00020��AU00021��AU00022��AU00023��AU00024��AU00025��AU00026��AU00027��AU00028��AU00029��AU00030��AU00031��AU00032��AU00033��AU00034��AU00035��AU00036��AU00037��AU00038��AU00039��AU00040��AU00041��AU00042��AU00043��AU00044��AU00045��AU00046��AU00047��AU00048��AU00049��AU00050�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  jX  jY  jZ  j[  j\  j]  j^  j_  j`  ja  jb  jc  jd  je  jf  jg  jh  ji  jj  jk  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  ]�(}�(j�  �id:jrdn�j�  �buyer�j�  j�  �ARRAY���R�u}�(j�  �lei:sample_lei�j�  �reportDetails.executingEntity�j�  j�  u}�(j�  �lei:sample_lei�j�  �seller�j�  j�  u}�(j�  �id:jrdn�j�  �counterparty�j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  �:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�j�  j�  u}�(j�  �id:sloms_298033�j�  �clientIdentifiers.client�j�  j�  u}�(j�  �id:and�j�  �trader�j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:jrdn�j�  j�  j�  j�  u}�(j�  �lei:213800se4kpty278kz54�j�  j�  j�  j�  u}�(j�  �id:sloms_298033�j�  j�  j�  j�  u}�(j�  �id:and�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue]�(}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �	id:lqnteu�j�  j�  j�  j�  u}�(j�  �id:bloms_292275�j�  j�  j�  j�  u}�(j�  �id:tod�j�  j�  j�  j�  ue�lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu�et�bh�)��}�(h�hhK ��h��R�(KK3��h!�]�(�id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��id:jrdn��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu��	id:lqnteu�et�bj  j  )��ubhhK ��h��R�(KKK3��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54��lei:213800se4kpty278kz54�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�)��}�(h�hhK ��h��R�(KK3��h!�]�(�id:sloms_298033��id:sloms_298033��id:sloms_298033��id:sloms_298033��id:sloms_298033��id:sloms_298033��id:sloms_298033��id:sloms_298033��id:sloms_298033��id:sloms_298033��id:sloms_298033��id:sloms_298033��id:sloms_298033��id:sloms_298033��id:sloms_298033��id:sloms_298033��id:sloms_298033��id:sloms_298033��id:sloms_298033��id:sloms_298033��id:sloms_298033��id:sloms_298033��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275��id:bloms_292275�et�bj  j  )��ubh�)��}�(h�hhK ��h��R�(KK3��h!�]�(�id:and��id:and��id:and��id:and��id:and��id:and��id:and��id:and��id:and��id:and��id:and��id:and��id:and��id:and��id:and��id:and��id:and��id:and��id:and��id:and��id:and��id:and��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod��id:tod�et�bj  j  )��ubhhK ��h��R�(KKK3��h!�]�(]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j  j  j  j  j	  e]�(j�  j  j  j  j  j  j  j  e]�(j�  j  j  j  j!  j#  j%  j'  e]�(j�  j*  j,  j.  j0  j2  j4  j6  e]�(j�  j9  j;  j=  j?  jA  jC  jE  e]�(j�  jH  jJ  jL  jN  jP  jR  jT  e]�(j�  jW  jY  j[  j]  j_  ja  jc  e]�(j�  jf  jh  jj  jl  jn  jp  jr  e]�(j�  ju  jw  jy  j{  j}  j  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j  j�  j�  j�  j�  j�  j�  j�  e]�(j  j�  j�  j   j  j  j  j  e]�(j  j  j
  j  j  j  j  j  e]�(j  j  j  j  j   j"  j$  e]�(j
  j'  j)  j+  j-  j/  j1  e]�(j  j4  j6  j8  j:  j<  j>  e]�(j  jA  jC  jE  jG  jI  jK  e]�(j  jN  jP  jR  jT  jV  jX  e]�(j  j[  j]  j_  ja  jc  je  e]�(j  jh  jj  jl  jn  jp  jr  e]�(j  ju  jw  jy  j{  j}  j  e]�(j  j�  j�  j�  j�  j�  j�  e]�(j  j�  j�  j�  j�  j�  j�  e]�(j  j�  j�  j�  j�  j�  j�  e]�(j  j�  j�  j�  j�  j�  j�  e]�(j   j�  j�  j�  j�  j�  j�  e]�(j"  j�  j�  j�  j�  j�  j�  e]�(j$  j�  j�  j�  j�  j�  j�  e]�(j&  j�  j�  j�  j�  j�  j�  e]�(j(  j�  j�  j�  j�  j�  j�  e]�(j*  j�  j�  j�  j�  j�  j  e]�(j,  j  j  j  j
  j  j  e]�(j.  j  j  j  j  j  j  e]�(j0  j  j   j"  j$  j&  j(  e]�(j2  j+  j-  j/  j1  j3  j5  e]�(j4  j8  j:  j<  j>  j@  jB  e]�(j6  jE  jG  jI  jK  jM  jO  e]�(j8  jR  jT  jV  jX  jZ  j\  e]�(j:  j_  ja  jc  je  jg  ji  e]�(j<  jl  jn  jp  jr  jt  jv  e]�(j>  jy  j{  j}  j  j�  j�  e]�(j@  j�  j�  j�  j�  j�  j�  eet�bhhK ��h��R�(KKK3��h�b1�����R�(Kh"NNNJ����J����K t�b�C3�t�bhhK ��h��R�(KKK3��h!�]�(�Equity�jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  jD  et�be]�(h
h}�(hhhK ��h��R�(KK	��h!�]�(h%h&h'h(h)h*h+h,h-et�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h/h0et�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h3h4et�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h=h>h?h@hAhBhCet�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hDhEet�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hHhIhJhKhLhMhNet�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hRhShThUhVhWhXhYet�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hZat�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h[h\h]h^et�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h`at�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�haat�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hbat�bheNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hcat�bheNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h}�mgr_locs��builtins��slice���K K	K��R�u}�(j\	  h�j]	  j`	  K	K
K��R�u}�(j\	  j	  j]	  j`	  K
KK��R�u}�(j\	  j  j]	  j`	  KK
K��R�u}�(j\	  j  j]	  j`	  K
KK��R�u}�(j\	  j(  j]	  j`	  KKK��R�u}�(j\	  j.  j]	  j`	  KKK��R�u}�(j\	  j;  j]	  j`	  KKK��R�u}�(j\	  jF  j]	  j`	  KKK��R�u}�(j\	  jO  j]	  j`	  KKK��R�u}�(j\	  j\  j]	  j`	  KKK��R�u}�(j\	  j�  j]	  j`	  KKK��R�u}�(j\	  j�  j]	  j`	  KKK��R�u}�(j\	  j�  j]	  j`	  KKK��R�u}�(j\	  j�  j]	  j`	  KKK��R�u}�(j\	  j|  j]	  j`	  KK!K��R�u}�(j\	  j�  j]	  j`	  K!K"K��R�u}�(j\	  j�  j]	  j`	  K"K#K��R�u}�(j\	  j�  j]	  j`	  K#K*K��R�u}�(j\	  jB  j]	  j`	  K*K+K��R�u}�(j\	  j�  j]	  j`	  K+K,K��R�u}�(j\	  j�  j]	  j`	  K,K-K��R�u}�(j\	  j�  j]	  j`	  K-K5K��R�u}�(j\	  j,  j]	  j`	  K5K6K��R�u}�(j\	  jj  j]	  j`	  K6K:K��R�u}�(j\	  j�  j]	  j`	  K:K;K��R�u}�(j\	  j�  j]	  j`	  K;K<K��R�u}�(j\	  j�  j]	  j`	  K<K=K��R�u}�(j\	  j7  j]	  j`	  K=K>K��R�u}�(j\	  jA  j]	  j`	  K>K?K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.