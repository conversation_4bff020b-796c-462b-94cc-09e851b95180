��^V      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�exe_user_fix_tag_Currency��!exe_user_fix_tag_SecurityIDSource��exe_user_fix_tag_LastMkt��exe_user_fix_tag_SecurityID��exe_user_fix_tag_TransactTime��exe_user_fix_tag_LeavesQty��exe_user_fix_tag_SecurityType��exe_user_fix_tag_SecurityAltID��$exe_user_fix_tag_SecurityAltIDSource��exe_user_fix_tag_ff_20001��exe_exec-id��exe_order-id��
exe_portfolio��exe_portfolio-side��exe_destination��
exe_symbol��exe_exec-shares��exe_exec-price��exe_broker-orderid��exe_transaction-time��exe_prim-orderid��exe_userfixtag��exe_user-initials��prim_rpl_fixtags��(prim_rpl_incoming-fix-orderid/strategyid��prim_rpl_limit-price��prim_rpl_order-time��
exe_status��prim_rpl_fix_tag_Currency��
prim_rpl_isin�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK3��h�i8�����R�(K�<�NNNJ����J����K t�b�B�                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       �t�bhD�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK3��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�1344899��1344909��1344910��1344911��1344912��1344913��1344914��1344918��1344919��1345082��1345083��1345084��1345085��1345086��1345087��1345088��1345089��1345090��1345091��1345092��1345093��1343403��32022-10-20 00:30:41705��32022-10-20 00:30:41714��32022-10-20 00:30:41716��32022-10-20 00:30:41851��32022-10-20 01:10:58892��32022-10-20 01:10:58901��32022-10-20 01:10:58903��32022-10-20 01:36:05886��32022-10-20 01:59:52212��32022-10-20 02:15:30757��32022-10-20 02:15:30855��32022-10-20 02:15:31570��32022-10-19 23:02:07465��32022-10-19 23:02:07484��32022-10-19 23:02:07510��32022-10-20 00:29:05553��32022-10-20 00:29:10075��32022-10-20 00:29:10077��32022-10-20 00:30:44464��32022-10-20 00:30:44470��32022-10-20 00:30:44476��32022-10-20 00:30:44483��32022-10-20 00:39:35499��32022-10-20 00:41:02987��32022-10-20 00:42:47698��32022-10-20 00:43:50975��32022-10-20 00:43:50980��32022-10-20 00:43:50987��32022-10-20 01:36:04977�et�b�_dtype�hZ�StringDtype���)��ubh\)��}�(h_hhK ��h��R�(KK3��hf�]�(�CAA0007-20221021�h�h�h�h�h�h��CAA0068-20221021�h�h�h�h�h�h�h�h�h�h�h�h�h��CAA0024-20221020��CAA0038-20221020�h�h�h�h�h�h�h�h�h�h�h��CAA0039-20221020�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh\)��}�(h_hhK ��h��R�(KK3��hf�]�(�SLOMS_298033�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��BLOMS_292275�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh\)��}�(h_hhK ��h��R�(KK3��hf�]�(�SEL�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��BUY�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh\)��}�(h_hhK ��h��R�(KK3��hf�]�(�JRDN�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�ȟLQNTEU�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh\)��}�(h_hhK ��h��R�(KK3��hf�]�(�ERD.NZ�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h׌CWP.AU�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh\)��}�(h_hhK ��h��R�(KK3��hf�]�(�12000��200��3��15��255��12��7515��3113��617��739��76��8��3817��10000�h�153�h�77�h�h�11085��1073��1464�h�2196�h��1418��1686��1027��256��878��5834��1584��249��332��2�h�6��64��4��126��78��34��9��93��36��1��58��21��41��503�et�bh�h�)��ubh\)��}�(h_hhK ��h��R�(KK3��hf�]�(�13061�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �13023��!FSIMALGO::CAA0038-20221020@LQNTIN�j  j  j  j  j  j  j  j  j  j  j  �!FSIMALGO::CAA0039-20221020@LQNTIN�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh\)��}�(h_hhK ��h��R�(KK3��hf�]�(�2022-10-20 21:44:50��2022-10-20 23:09:10�j%  j%  j%  j%  �2022-10-20 23:14:57��2022-10-20 23:17:27��2022-10-20 23:17:39��2022-10-21 03:59:55�j)  j)  j)  j)  j)  j)  j)  j)  j)  j)  j)  �2022-10-19 22:01:03��2022-10-20 00:30:41�j+  j+  j+  �2022-10-20 01:10:58�j,  j,  �2022-10-20 01:36:05��2022-10-20 01:59:51��2022-10-20 02:15:30�j/  �2022-10-20 02:15:31��2022-10-19 23:02:07�j1  j1  �2022-10-20 00:29:05��2022-10-20 00:29:09�j3  �2022-10-20 00:30:44�j4  j4  j4  �2022-10-20 00:39:35��2022-10-20 00:41:02��2022-10-20 00:42:47��2022-10-20 00:43:50�j8  j8  �2022-10-20 01:36:04�et�bh�h�)��ubh\)��}�(h_hhK ��h��R�(KK3��hf�]�(�CAA0007-20221021�jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  jC  �CAA0024-20221020��CAA0038-20221020�jE  jE  jE  jE  jE  jE  jE  jE  jE  jE  jE  �CAA0039-20221020�jF  jF  jF  jF  jF  jF  jF  jF  jF  jF  jF  jF  jF  jF  jF  jF  et�bh�h�)��ubh\)��}�(h_hhK ��h��R�(KK3��hf�]�(��115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221020-21:44:50.000;150=1;151=38000;167=CS;198=72294435;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221020-23:09:10.022;150=1;151=37800;167=CS;198=72295646;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221020-23:09:10.022;150=1;151=37797;167=CS;198=72295647;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221020-23:09:10.022;150=1;151=37782;167=CS;198=72295648;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221020-23:09:10.022;150=1;151=37527;167=CS;198=72295649;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221020-23:09:10.022;150=1;151=37515;167=CS;198=72295650;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221020-23:14:57.439;150=1;151=30000;167=CS;198=72295970;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221020-23:17:27.899;150=1;151=150000;167=CS;198=72296093;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221020-23:17:39.026;150=1;151=149383;167=CS;198=72296110;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=148644;167=CS;198=72305561;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=148568;167=CS;198=72305571;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=148560;167=CS;198=72305584;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=144743;167=CS;198=72305597;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=134743;167=CS;198=72305613;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=134731;167=CS;198=72305625;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=134578;167=CS;198=72305638;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=134563;167=CS;198=72305660;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=134486;167=CS;198=72305674;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=134471;167=CS;198=72305686;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=134468;167=CS;198=72305703;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221021-03:59:55.000;150=1;151=123383;167=CS;198=72305721;20001=213800SE4KPTY278KZ54���115=AU_IRESS;15=NZD;20=0;22=2;29=1;30=NZ;48=BP4VX26;49=JRDN;60=20221019-22:01:03.000;150=1;151=48927;167=CS;198=72280281;20001=213800SE4KPTY278KZ54��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXC;48=6182281;49=LQNTEU;60=20221020-00:30:41.630;76=LNEL;150=1;151=28536;167=CS;851=1��u115=LQNTUK;15=AUD;20=0;22=2;29=1;30=CXAP;48=6182281;49=LQNTEU;60=20221020-00:30:41.258;76=LNEL;150=1;151=27072;167=CS��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXC;48=6182281;49=LQNTEU;60=20221020-00:30:41.261;76=LNEL;150=1;151=24876;167=CS;851=1��u115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXC;48=6182281;49=LQNTEU;60=20221020-00:30:41.630;76=LNEL;150=1;151=22680;167=CS��u115=LQNTUK;15=AUD;20=0;22=2;29=1;30=CXAP;48=6182281;49=LQNTEU;60=20221020-01:10:58.407;76=LNEL;150=1;151=21262;167=CS��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXC;48=6182281;49=LQNTEU;60=20221020-01:10:58.411;76=LNEL;150=1;151=19576;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=CXAP;48=6182281;49=LQNTEU;60=20221020-01:10:58.788;76=LNEL;150=1;151=18549;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=JPMX;48=6182281;49=LQNTEU;60=20221020-01:36:05.792;76=LNEL;150=1;151=18293;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXC;48=6182281;49=LQNTEU;60=20221020-01:59:51.728;76=LNEL;150=1;151=17415;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXC;48=6182281;49=LQNTEU;60=20221020-02:15:30.625;76=LNEL;150=1;151=11581;167=CS;851=1��z115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXC;48=6182281;49=LQNTEU;60=20221020-02:15:30.225;76=LNEL;150=1;151=9997;167=CS;851=1��z115=LQNTUK;15=AUD;20=0;22=2;29=1;30=JPMX;48=6182281;49=LQNTEU;60=20221020-02:15:31.488;76=LNEL;150=1;151=9748;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221019-23:02:07.000;76=LNEL;150=1;151=14668;167=CS;851=4��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221019-23:02:07.000;76=LNEL;150=1;151=14666;167=CS;851=4��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221019-23:02:07.000;76=LNEL;150=1;151=14654;167=CS;851=4��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=CXAC;48=6182281;49=LQNTEU;60=20221020-00:29:05.409;76=LNEL;150=1;151=14648;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221020-00:29:09.959;76=LNEL;150=1;151=14584;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=CXAC;48=6182281;49=LQNTEU;60=20221020-00:29:09.959;76=LNEL;150=1;151=14580;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221020-00:30:44.354;76=LNEL;150=1;151=14454;167=CS;851=2��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=CXAC;48=6182281;49=LQNTEU;60=20221020-00:30:44.354;76=LNEL;150=1;151=14376;167=CS;851=2��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=CXAC;48=6182281;49=LQNTEU;60=20221020-00:30:44.354;76=LNEL;150=1;151=14342;167=CS;851=2��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=CXAC;48=6182281;49=LQNTEU;60=20221020-00:30:44.354;76=LNEL;150=1;151=14333;167=CS;851=2��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221020-00:39:35.389;76=LNEL;150=1;151=14240;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221020-00:41:02.878;76=LNEL;150=1;151=14204;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221020-00:42:47.589;76=LNEL;150=1;151=14203;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221020-00:43:50.866;76=LNEL;150=1;151=14145;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=CXAC;48=6182281;49=LQNTEU;60=20221020-00:43:50.867;76=LNEL;150=1;151=14124;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221020-00:43:50.867;76=LNEL;150=1;151=14083;167=CS;851=1��{115=LQNTUK;15=AUD;20=0;22=2;29=1;30=ASXT;48=6182281;49=LQNTEU;60=20221020-01:36:04.841;76=LNEL;150=1;151=13580;167=CS;851=1�et�bh�h�)��ubh\)��}�(h_hhK ��h��R�(KK3��hf�]�(�AND�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �TOD�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh\)��}�(h_hhK ��h��R�(KK3��hf�]�(�128=2;10001=REALINDEX;15=NZD�j�  j�  j�  j�  j�  j�  �15=NZD;10001=REALINDEX;15=NZD�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �X128=2;9000=DARK-AP;9117=AP08.21-FLEX;9011=2;9054=Dark_Only;9023=N;10001=REALINDEX;15=AUD�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �x128=2;9000=DYNPOV-AP;9117=AP08.21-FLEX;9015=Y;9023=Y;9005=10;9006=20;9090=1;9025=5;9096=2;9072=50;10001=REALINDEX;15=AUD�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh\)��}�(h_hhK ��h��R�(KK3��hf�]�(�
1478490821OMS�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
1478268211OMS�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh\)��}�(h_hhK ��h��R�(KK3��hf�]�(�MKT-DAY�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �4.040000�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh\)��}�(h_hhK ��h��R�(KK3��hf�]�(�2022-10-20 21:44:40�j�  j�  j�  j�  j�  j�  �2022-10-20 23:17:07�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �2022-10-19 22:00:41��2022-10-19 22:34:27�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �2022-10-19 22:34:35�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh\)��}�(h_hhK ��h��R�(KK3��hf�]�(�pandas._libs.missing��NA���j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubhhK ��h��R�(KKK3��h�f8�����R�(KhRNNNJ����J����K t�b�B�  ������?H�z�G�?H�z�G�?H�z�G�?��Q��?��Q��?��Q��?��Q��?��Q��?�������?�������?�������?�������?�������?�������?�������?�������?�������?�������?�������?�������?�G�z�?)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@�p=
�#@��Q�@��Q�@��Q�@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@)\���(@�t�bhhK ��h��R�(KKK3��hf�]�(�NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��NZ��ASXC��CXAP��ASXC��ASXC��CXAP��ASXC��CXAP��JPMX��ASXC��ASXC��ASXC��JPMX��ASXT��ASXT��ASXT��CXAC��ASXT��CXAC��ASXT��CXAC��CXAC��CXAC��ASXT��ASXT��ASXT��ASXT��CXAC��ASXT��ASXT��BP4VX26��BP4VX26��BP4VX26��BP4VX26��BP4VX26��BP4VX26��BP4VX26��BP4VX26��BP4VX26��BP4VX26��BP4VX26��BP4VX26��BP4VX26��BP4VX26��BP4VX26��BP4VX26��BP4VX26��BP4VX26��BP4VX26��BP4VX26��BP4VX26��BP4VX26��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��6182281��20221020-21:44:50.000��20221020-23:09:10.022��20221020-23:09:10.022��20221020-23:09:10.022��20221020-23:09:10.022��20221020-23:09:10.022��20221020-23:14:57.439��20221020-23:17:27.899��20221020-23:17:39.026��20221021-03:59:55.000��20221021-03:59:55.000��20221021-03:59:55.000��20221021-03:59:55.000��20221021-03:59:55.000��20221021-03:59:55.000��20221021-03:59:55.000��20221021-03:59:55.000��20221021-03:59:55.000��20221021-03:59:55.000��20221021-03:59:55.000��20221021-03:59:55.000��20221019-22:01:03.000��20221020-00:30:41.630��20221020-00:30:41.258��20221020-00:30:41.261��20221020-00:30:41.630��20221020-01:10:58.407��20221020-01:10:58.411��20221020-01:10:58.788��20221020-01:36:05.792��20221020-01:59:51.728��20221020-02:15:30.625��20221020-02:15:30.225��20221020-02:15:31.488��20221019-23:02:07.000��20221019-23:02:07.000��20221019-23:02:07.000��20221020-00:29:05.409��20221020-00:29:09.959��20221020-00:29:09.959��20221020-00:30:44.354��20221020-00:30:44.354��20221020-00:30:44.354��20221020-00:30:44.354��20221020-00:39:35.389��20221020-00:41:02.878��20221020-00:42:47.589��20221020-00:43:50.866��20221020-00:43:50.867��20221020-00:43:50.867��20221020-01:36:04.841��38000��37800��37797��37782��37527��37515��30000��150000��149383��148644��148568��148560��144743��134743��134731��134578��134563��134486��134471��134468��123383��48927��28536��27072��24876��22680��21262��19576��18549��18293��17415��11581��9997��9748��14668��14666��14654��14648��14584��14580��14454��14376��14342��14333��14240��14204��14203��14145��14124��14083��13580��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS��CS�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54��213800SE4KPTY278KZ54�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��NZD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD�et�bhhK ��h��R�(KKK3��h!�]�(�AU0000��AU0001��AU0002��AU0003��AU0004��AU0005��AU0006��AU0007��AU0008��AU0009��AU00010��AU00011��AU00012��AU00013��AU00014��AU00015��AU00016��AU00017��AU00018��AU00019��AU00020��AU00021��AU00022��AU00023��AU00024��AU00025��AU00026��AU00027��AU00028��AU00029��AU00030��AU00031��AU00032��AU00033��AU00034��AU00035��AU00036��AU00037��AU00038��AU00039��AU00040��AU00041��AU00042��AU00043��AU00044��AU00045��AU00046��AU00047��AU00048��AU00049��AU00050�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h0at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(h)h*h+h,h-h.hAet�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bhDNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h]�mgr_locs��builtins��slice���K
KK��R�u}�(jM  h�jN  jQ  KKK��R�u}�(jM  h�jN  jQ  KK
K��R�u}�(jM  h�jN  jQ  K
KK��R�u}�(jM  h�jN  jQ  KKK��R�u}�(jM  h�jN  jQ  KKK��R�u}�(jM  h�jN  jQ  KKK��R�u}�(jM  j  jN  jQ  KKK��R�u}�(jM  j  jN  jQ  KKK��R�u}�(jM  j<  jN  jQ  KKK��R�u}�(jM  jI  jN  jQ  KKK��R�u}�(jM  j�  jN  jQ  KKK��R�u}�(jM  j�  jN  jQ  KKK��R�u}�(jM  j�  jN  jQ  KKK��R�u}�(jM  j�  jN  jQ  KKK��R�u}�(jM  j�  jN  jQ  KKK��R�u}�(jM  j�  jN  jQ  KKK��R�u}�(jM  j�  jN  jQ  KKK��R�u}�(jM  j�  jN  hhK ��h��R�(KK��hQ�CX                                                                	              �t�bu}�(jM  jZ  jN  jQ  KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.