���&      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKZ��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�buySell��__synth_parent.buySell��dataSourceName��date��!executionDetails.buySellIndicator��executionDetails.limitPrice��executionDetails.orderStatus��+__synth_parent.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo��&executionDetails.shortSellingIndicator��executionDetails.stopPrice�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	hierarchy��id��__synth_parent.id��__synth_parent.__meta_model__��__meta_model__��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��orderIdentifiers.parentOrderId��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��priceFormingData.price��priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��3tradersAlgosWaiversIndicators.shortSellingIndicator��transactionDetails.basketId��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��notional_currency_2_attribute��asset_class_attribute��bbg_figi_id_attribute��isin_attribute��option_type_attribute��option_strike_price_attribute��underlying_isin_attribute��venue_attribute��'underlying_symbol_expiry_code_attribute��underlying_symbol_attribute��currency_attribute��expiry_date_attribute��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��__fallback_buyer__��__fallback_seller__��__fallback_counterparty__��__fallback_client__��__fallback_buyer_dec_maker__��__fallback_seller_dec_maker__��__fallback_inv_dec_in_firm__��__fallback_trader__��__fallback_exec_within_firm__��__fallback_executing_entity__��marketIdentifiers��__asset_class__��__isntr_ids_currency__��	__venue__�� __instrument_unique_identifier__��__instrument_full_name__��__price_reference_ric__��__instrument_classification__��__created_through_fallback__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C                      �t�bh��__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KKEK��h!�]�(�2�h�h�h�h�h��Enfusion�h�h��
2022-05-27��
2022-05-27��
2022-05-27��SELL�h�h��pandas._libs.missing��NA���h�G@      �PARF�h�h��NEWO�h�h���Order Status Text: SOLD(LAC) 182 at 28.14(USD) for ORDER ID # 18161574, CUSIP: 53680Q207, RIC: LAC, SEDOL: BF4X269, Description: LITHIUM AMERS ORD, BBYellow: LAC US Equity���Order Status Text: SOLD(LAC) 182 at 28.14(USD) for ORDER ID # 18161574, CUSIP: 53680Q207, RIC: LAC, SEDOL: BF4X269, Description: LITHIUM AMERS ORD, BBYellow: LAC US Equity���Order Status Text: SOLD(LAC) 182 at 28.14(USD) for ORDER ID # 18161574, CUSIP: 53680Q207, RIC: LAC, SEDOL: BF4X269, Description: LITHIUM AMERS ORD, BBYellow: LAC US Equity�h�h�h�h�h�h��AOTC�h�h�]��GTCV�a]�h�a]�h�a�
Standalone�h�h��17591573�h�h�h�h�h��Order�h�h��
OrderState�h�h�h�h�h�h�h�h��2022-05-27T02:39:48.000000Z��2022-05-27T02:39:48.000000Z��2022-05-27T02:39:48.000000Z��2022-05-27T13:40:49.000000Z��2022-05-27T13:40:52.000000Z��2022-05-27T13:40:53.000000Z�h�h�h�h�h�h�h�h�h��!PROG-202310051659191672474-665764�h�h�h�h�h��USD�h�h��MONE�h�h�h�h�h��UNIT�h�h��Market Side�h�h�h�h�h�h�h�h��XOFF�h�h�]�(}�(�labelId��CA53680Q2071��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�u}�(h��kal�h�h�h�h�ue]�(}�(h�h�h�h�h�h�u}�(h��NKM24�h�h�h�h�ue]�(}�(h�h�h�h�h�h�u}�(h��LAC�h�h�h�h�ueh�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�]�(}�(h��id:emsx�hbuyer�h�hǌARRAY���R�u}�(h��lei:2138002ee8dt72x2qn40�hreportDetails.executingEntity�h�h�u}�(h��lei:2138002ee8dt72x2qn40�hseller�h�h�u}�(h��id:emsx�hcounterparty�h�h�u}�(h��id:emsx�hclientIdentifiers.client�h�h�u}�(h��id:tyson appadoo�htrader�h�h�ue]�(}�(h��id:emsx�h�h�h�h�u}�(h��lei:2138002ee8dt72x2qn40�h�h�h�h�u}�(h��lei:2138002ee8dt72x2qn40�h�h�h�h�u}�(h��id:emsx�h�h�h�h�u}�(h��id:emsx�h�h�h�h�u}�(h��id:tyson appadoo�h�h�h�h�ue]�(}�(h��id:emsx�h�h�h�h�u}�(h��lei:2138002ee8dt72x2qn40�h�h�h�h�u}�(h��lei:2138002ee8dt72x2qn40�h�h�h�h�u}�(h��id:emsx�h�h�h�h�u}�(h��id:emsx�h�h�h�h�u}�(h��id:tyson appadoo�h�h�h�h�ue�lei:2138002ee8dt72x2qn40��lei:2138002ee8dt72x2qn40��lei:2138002ee8dt72x2qn40��id:emsx��id:emsx��id:emsx��lei:2138002ee8dt72x2qn40��lei:2138002ee8dt72x2qn40��lei:2138002ee8dt72x2qn40��id:emsx��id:emsx��id:emsx�h�h�h�h�h�h�h�h�h�h�h�h��id:emsx��id:emsx��id:emsx��emsx��emsx��emsx��2138002ee8dt72x2qn40��2138002ee8dt72x2qn40��2138002ee8dt72x2qn40��emsx��emsx��emsx��emsx��emsx��emsx�h�h�h�h�h�h�h�h�h�h�h�h��2138002ee8dt72x2qn40��2138002ee8dt72x2qn40��2138002ee8dt72x2qn40�]�(h�h�h�h�h�h�h�h�e]�(h�h�h�h�h�h�h�h�e]�(h�h�h�h�h�h�j  j  eh�h�h�h�h�h�h�h�h�h�h�h�et�b�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�Market�j2  �Limit�et�b�_dtype�j'  �StringDtype���)��ubj)  )��}�(j,  hhK ��h��R�(KK��h!�]�(�2022052715394750517-616960�j@  j@  et�bj5  j7  )��ubj)  )��}�(j,  hhK ��h��R�(KK��h!�]�(�17615542�jJ  jJ  et�bj5  j7  )��ubj)  )��}�(j,  hhK ��h��R�(KK��h!�]�(�	527313231��	527313397��	527313417�et�bj5  j7  )��ubhhK ��h��R�(KKK��h�f8�����R�(Kh�NNNJ����J����K t�b�Cx�G�z.<@�(\��5<@333333<@      Y@      Y@     �r@�G�z.<@�(\��5<@333333<@      Y@      Y@     �r@      �      �      ��t�bj)  )��}�(j,  hhK ��h��R�(KK��h!�]�(jT  jU  jV  et�bj5  j7  )��ubhhK ��h��R�(KKK��h��C                      �t�bj)  )��}�(j,  hhK ��h��R�(KK��h!�]�(j@  j@  j@  et�bj5  j7  )��ubj)  )��}�(j,  hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(h�h�h�et�bj5  j7  )��ubj)  )��}�(j,  hhK ��h��R�(KK��h!�]�(h�h�h�et�bj5  j7  )��ubj)  )��}�(j,  hhK ��h��R�(KK��h!�]�(h�h�h�et�bj5  j7  )��ubj)  )��}�(j,  hhK ��h��R�(KK��h!�]�(�id:tyson appadoo��id:tyson appadoo��id:tyson appadoo�et�bj5  j7  )��ubj)  )��}�(j,  hhK ��h��R�(KK��h!�]�(�
tyson appadoo��
tyson appadoo��
tyson appadoo�et�bj5  j7  )��ubj)  )��}�(j,  hhK ��h��R�(KK��j�  �]�(h�h�h�et�bj5  j7  )��ubj)  )��}�(j,  hhK ��h��R�(KK��j�  �]�(�LAC�j�  j�  et�bj5  j7  )��ubj)  )��}�(j,  hhK ��h��R�(KK��j�  �]�(h�h�h�et�bj5  j7  )��ubhhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C�t�be]�(h
h}�(hhhK ��h��R�(KKE��h!�]�(h%h&h'h(h)h*h+h,h.h/h0h1h2h3h4h5h6h7h9h<hAhBhChDhEhFhGhJhKhMhNhOhPhQhShThUhVhYh[h\h]h^h_h`hahbhchdhehfhghhhihjhlhmhnhohphqhrhthuhvhwhxhyhzet�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h=h>hIhLhZet�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hRat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hkat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hsat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h{at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h|at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h}at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h~at�bh�Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs�hhK ��h��R�(KKE��h�i8�����R�(Kh�NNNJ����J����K t�b�B(                                                           	       
                     
                                                                                            !       "       %       &       (       )       *       +       ,       .       /       0       1       4       6       7       8       9       :       ;       <       =       >       ?       @       A       B       C       D       E       G       H       I       J       K       L       M       O       P       Q       R       S       T       U       �t�bu}�(j�  j*  j�  �builtins��slice���KK	K��R�u}�(j�  j9  j�  j�  KKK��R�u}�(j�  jC  j�  j�  KKK��R�u}�(j�  jM  j�  j�  KKK��R�u}�(j�  j[  j�  hhK ��h��R�(KK��j�  �C(              $       '       5       �t�bu}�(j�  jc  j�  j�  KKK��R�u}�(j�  jn  j�  j�  KKK��R�u}�(j�  jr  j�  j�  K#K$K��R�u}�(j�  j{  j�  j�  K-K.K��R�u}�(j�  j�  j�  j�  K2K3K��R�u}�(j�  j�  j�  j�  K3K4K��R�u}�(j�  j�  j�  j�  KFKGK��R�u}�(j�  j�  j�  j�  KNKOK��R�u}�(j�  j�  j�  j�  KVKWK��R�u}�(j�  j�  j�  j�  KWKXK��R�u}�(j�  j�  j�  j�  KXKYK��R�u}�(j�  j�  j�  j�  KYKZK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.