���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK5��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�ORDERID��
PARENTORDERID��	ORDERDATE��ORDERTIMESTAMP��	EVENTTYPE��INS<PERSON><PERSON><PERSON><PERSON>DENTIFICATIONCODE��DESCRIPTION��	ORDERSIDE��ORDERTOTALQUANTITY��QUANTITYTYPE��EXECUTIONORDERTYPE��ORDERLIMITPRICE��ORDERSTOPPRICE��CURRENCY��ORDERTIMEINFORCE��ORDERSTATUSTEXT��ORDERREFERENCE��!EXECUTIONENTITYIDENTIFICATIONCODE��TRADER��BUYERIDENTIFICATIONCODE��SELLERIDENTIFICATIONCODE��ORDEREXECUTIONDESTINATION��LASTQTY��LASTPX��
EXECUTIONDATE��
EXECUTIONTIME��EXECID��	EXECREFID��ISIN��SEDOL��CUSIP��BBYELLOW��TICKER��OSI��RIC��EXCHANGESHORTNAME��EXCHAN<PERSON><PERSON><PERSON><PERSON><PERSON>NSTRUMENTCLASSIFICATION��
OPTIONTYPE��OPTIONSTRIKE��OPTIONEXPIRATIONDATE��FUTUREEXPIRATIONDATE��NOTIONALCURRENCY2��UNDERLYINGINSTRUMENTCODE��FUTUREBLOOMBERGROOT��OPTIONCONTRACTBLOOMBERGROOTCODE��
EXPIRYDATE��QUANTITY��LASTFI<PERSON><PERSON><PERSON>��<PERSON>ADINGDATE<PERSON>ME��INSTRUMENTFULLNAME��LENAME��	PROGRAMID�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(h[N�start�K �stop�K	�step�Ku��R�e]�(hhK ��h��R�(KKK	��h�f8�����R�(K�<�NNNJ����J����K t�b�B        �      �      �      �      �      �      �      �      �    `
 A    `
 A    ��@    ��@    ��@    ��@    ��@    ��@    ��@     ��@     ��@     0�@     0�@     0�@     0�@     0�@     0�@     0�@      �      �      �      �      �      �      �      �      �      @      �?     �Q@     �@@                                     ��@     Ό@     Ό@     0�@fffff.�@                                S�!��/�@      �      �      �      �   �l��A   ��s�A  ���s�A   �l��A      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KKK	��h�i8�����R�(KhoNNNJ����J����K t�b�C�͂1    ͂1    ��'    ��'    ��'    ��'    ��'    ��'    ��'    �B�6    �B�6    %�}    .�}    �G�6    �G�6    �G�6    �G�6    I�6    ��4    ��4    ��4    ��4    ��4    ��4    ��4    ��4    ��4    �t�bhhK ��h��R�(KKK	��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�10:16:53.000�h��10:02:56.000�h�h�h�h�h�h��New�h�h�h��Correct�h��Cancel�h�h��INE102D01028�h��INE933S01016�h�h�h�h�h�h��GODREJ CONSUMER PRODUCTS ORD�h��INDIAMART INTERMESH ORD�h�h�h�h�h�h��sell�h��buy�h�h�h�h�h�h��UNIT�h�h�h�h�h�h�h�h��Limit�h�h�h�h�h�h�h�h��INR�h�h�h�h�h�h�h�h��GTC�h�h�h�h�h�h�h�h��2SOLD(GCPL) 1 at 921.75(INR) for ORDER ID # 4691895�h��7BOT(INMART) 2779 at 4399.65(INR) for ORDER ID # 4670637�h�h�h�h�h�h��202209201816526099-650225�h��202208261802563073-650225�h�h�h�h�h�h��2549002KVU12T4K1KL65�h�h�h�h�h�h�h�h��Goh Chaimei�h�h�h�h�h�h�h�h��INVB�h��2549002KVU12T4K1KL65�h�h�h�h�h�h��2549002KVU12T4K1KL65�h��UBSW�h�h�h�h�h�h��EMSX�h�h�h�h�h�h�h�h��2022-09-21T09:57:52.000�h��2022-09-21T05:57:08.000�h��2022-09-21T10:02:42.000�h�h�h��2022-09-21T10:03:51.000��09:57:52.000 +0000�h��05:57:08.000 +0000�h��10:02:42.000 +0000�h�h�h��10:03:51.000 +0000��INE102D01028�h��INE933S01016�h�h�h�h�h�h��B1BDGY0�h��BKDX4P8�h�h�h�h�h�h��GCPL IN Equity�h��INMART IN Equity�h�h�h�h�h�h��GODREJCP�h��	INDIAMART�h�h�h�h�h�h��GOCP.NS�h��INMR.NS�h�h�h�h�h�h��NSE�h�h�h�h�h�h�h�h��XNSE�h�h�h�h�h�h�h�h�et�bhhK ��h��R�(KKK	��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�pandas._libs.missing��NA���h�h�h�h�h�h�h�h�et�bhhK ��h��R�(KKK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bhhK ��h��R�(KKK	��h!�]�(�20220923��20220923��20220829��20220829��20220829��20220829��20220829��20220829��20220829�et�bhhK ��h��R�(KKK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bhhK ��h��R�(KKK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bhhK ��h��R�(KKK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bhhK ��h��R�(KKK	��h!�]�(h�h�h�h�h�h�h�h�h�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h&h-h0h1h;h<h@hChFhJhKhLhMhNhOhPhQhRet�bh[Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h%h?hSet�bh[Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h(h)h*h+h,h.h/h2h3h4h5h6h7h8h9h:h=h>hAhBhDhEhGhHhIet�bh[Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hTat�bh[Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bh[Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bh[Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bh[Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bh[Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bh[Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hYat�bh[Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hj�mgr_locs�hhK ��h��R�(KK��hy�C�                                                        !       %       &       '       (       )       *       +       ,       -       �t�bu}�(jQ  hujR  hhK ��h��R�(KK��h�i8�����R�(KhoNNNJ����J����K t�b�C               .       �t�bu}�(jQ  hjR  hhK ��h��R�(KK��hy�C�                                   	       
       
                                                                                                          "       #       $       �t�bu}�(jQ  h�jR  �builtins��slice���K/K0K��R�u}�(jQ  h�jR  jn  K0K1K��R�u}�(jQ  h�jR  jn  KKK��R�u}�(jQ  h�jR  jn  K1K2K��R�u}�(jQ  h�jR  jn  K2K3K��R�u}�(jQ  h�jR  jn  K3K4K��R�u}�(jQ  h�jR  jn  K4K5K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.