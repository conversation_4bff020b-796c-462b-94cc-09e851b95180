import os
from pathlib import Path
from unittest.mock import patch

import pandas as pd
import pytest
from prefect import context
from swarm.conf import SettingsCls
from swarm.task.auditor import Auditor

from swarm_tasks.order.feed.enfusion.v2.static import SourceColumns, TempColumns
from swarm_tasks.order.transformations.enfusion.v2.sellaronda_enfusion_v2_order_transformations import (
    SellarondaEnfusionV2OrderTransformations,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


@pytest.fixture()
def sample_data_with_film():
    """Sample data with FILM destination for testing Sellaronda override"""
    return pd.DataFrame({
        SourceColumns.DESTINATION_DISPLAY_NAME: ["FILM", "OTHER", "FILM", "NONE"],
        SourceColumns.COUNTERPARTY_CODE: ["FILM_CPTY_001", "OTHER_CPTY_001", "FILM_CPTY_002", "NONE_CPTY_001"],
        SourceColumns.COUNTERPARTY: ["REGULAR_CPTY_001", "REGULAR_CPTY_002", "REGULAR_CPTY_003", "REGULAR_CPTY_004"],
        SourceColumns.ORDER_EXECUTION_DESTINATION: ["DEST_001", "DEST_002", "DEST_003", "DEST_004"],
        SourceColumns.BUYER_IDENTIFICATION_CODE: ["BUYER_001", "BUYER_002", "BUYER_003", "BUYER_004"],
        SourceColumns.SELLER_IDENTIFICATION_CODE: ["SELLER_001", "SELLER_002", "SELLER_003", "SELLER_004"],
        SourceColumns.TRANSACTION_TYPE: ["Buy", "Sell", "Buy", "Sell"],
        SourceColumns.ORDER_SIDE: ["Buy", "Sell", "Buy", "Sell"],
        SourceColumns.LE_NAME: ["LE_001", "LE_002", "LE_003", "LE_004"],
        # Add other required columns
        SourceColumns.INSTRUMENT_FULL_NAME: ["INST_001", "INST_002", "INST_003", "INST_004"],
        SourceColumns.ORDER_ID: ["ORDER_001", "ORDER_002", "ORDER_003", "ORDER_004"],
        SourceColumns.TRANSACTION_REFERENCE_NUMBER: ["TXN_001", "TXN_002", "TXN_003", "TXN_004"],
    })


class TestSellarondaEnfusionV2OrderTransformations:
    """
    Test suite for SellarondaEnfusionV2OrderTransformations
    """

    def test_seller_override_for_film_destination_buy_order(self, sample_data_with_film, auditor):
        """Test that for BUY orders with FILM destination, seller uses COUNTERPARTY_CODE"""
        os.environ["SWARM_FILE_URL"] = "test_allocations_file.csv"
        
        # Mock realm
        with patch.object(SettingsCls, "realm", new_callable=lambda: lambda: "test.dev.steeleye.co"):
            task = SellarondaEnfusionV2OrderTransformations(
                source_frame=sample_data_with_film,
                auditor=auditor
            )
            
            # Mock the pre_process_df to include required temp columns
            task.pre_process_df = pd.DataFrame(index=sample_data_with_film.index)
            task.pre_process_df[TempColumns.BUY_SELL_INDICATOR] = ["1", "2", "1", "2"]  # 1=BUY, 2=SELL
            task.pre_process_df[TempColumns.AGGREGATION_CODE] = [None, None, None, None]
            
            # Test the seller method
            result = task._temp_party_ids_seller()
            
            # For BUY orders with FILM destination (rows 0 and 2), 
            # seller should use COUNTERPARTY_CODE
            assert "lei:FILM_CPTY_001" in result.iloc[0][TempColumns.SELLER]
            assert "lei:FILM_CPTY_002" in result.iloc[2][TempColumns.SELLER]
            
            # For other orders, should use regular logic
            # Row 1 is SELL order, should use executing entity (first case field)
            # Row 3 is SELL order, should use executing entity (first case field)

    def test_counterparty_override_for_film_destination_buy_order(self, sample_data_with_film, auditor):
        """Test that for BUY orders with FILM destination, counterparty uses COUNTERPARTY_CODE"""
        os.environ["SWARM_FILE_URL"] = "test_allocations_file.csv"
        
        # Mock realm
        with patch.object(SettingsCls, "realm", new_callable=lambda: lambda: "test.dev.steeleye.co"):
            task = SellarondaEnfusionV2OrderTransformations(
                source_frame=sample_data_with_film,
                auditor=auditor
            )
            
            # Mock the pre_process_df to include required temp columns
            task.pre_process_df = pd.DataFrame(index=sample_data_with_film.index)
            task.pre_process_df[TempColumns.BUY_SELL_INDICATOR] = ["1", "2", "1", "2"]  # 1=BUY, 2=SELL
            task.pre_process_df[TempColumns.AGGREGATION_CODE] = [None, None, None, None]
            task.pre_process_df[TempColumns.SELLER] = ["SELLER_001", "SELLER_002", "SELLER_003", "SELLER_004"]
            task.pre_process_df[TempColumns.BUYER] = ["BUYER_001", "BUYER_002", "BUYER_003", "BUYER_004"]
            
            # Test the counterparty method
            result = task._temp_party_ids_counterparty_and_client()
            
            # For BUY orders with FILM destination (rows 0 and 2), 
            # counterparty should use COUNTERPARTY_CODE directly
            assert "lei:FILM_CPTY_001" in result.iloc[0][TempColumns.COUNTERPARTY]
            assert "lei:FILM_CPTY_002" in result.iloc[2][TempColumns.COUNTERPARTY]

    def test_no_override_for_non_film_destination(self, sample_data_with_film, auditor):
        """Test that non-FILM destinations use regular logic"""
        os.environ["SWARM_FILE_URL"] = "test_allocations_file.csv"
        
        # Mock realm
        with patch.object(SettingsCls, "realm", new_callable=lambda: lambda: "test.dev.steeleye.co"):
            task = SellarondaEnfusionV2OrderTransformations(
                source_frame=sample_data_with_film,
                auditor=auditor
            )
            
            # Mock the pre_process_df to include required temp columns
            task.pre_process_df = pd.DataFrame(index=sample_data_with_film.index)
            task.pre_process_df[TempColumns.BUY_SELL_INDICATOR] = ["1", "2", "1", "2"]  # 1=BUY, 2=SELL
            task.pre_process_df[TempColumns.AGGREGATION_CODE] = [None, None, None, None]
            task.pre_process_df[TempColumns.SELLER] = ["SELLER_001", "SELLER_002", "SELLER_003", "SELLER_004"]
            task.pre_process_df[TempColumns.BUYER] = ["BUYER_001", "BUYER_002", "BUYER_003", "BUYER_004"]
            
            # Test the counterparty method
            result = task._temp_party_ids_counterparty_and_client()
            
            # For non-FILM destinations (rows 1 and 3), should use regular logic
            # Row 1: SELL order should use BUYER
            assert result.iloc[1][TempColumns.COUNTERPARTY] == "BUYER_002"
            # Row 3: SELL order should use BUYER  
            assert result.iloc[3][TempColumns.COUNTERPARTY] == "BUYER_004"
