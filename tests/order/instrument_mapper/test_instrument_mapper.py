import datetime

import pandas as pd
import pytest
from se_trades_tasks.order.universal.instrument_mapper.instrument_mapper import (
    run_instrument_mapper,
)
from se_trades_tasks.order.universal.instrument_mapper.schema import (
    InstrumentMapperResult,
)
from swarm.conf import Settings
from swarm.conf import SettingsCls
from swarm.task.io.write.elastic.result import ElasticBulkWriterResult
from swarm.task.io.write.elastic.static import WriteStatus
from swarm.task.transform.result import TransformResult

from swarm_tasks.steeleye.orders.instrument_mapper import instrument_mapper
from swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper import (
    InstrumentMapper,
)


@pytest.fixture
def order():
    data = {
        "instrumentDetails.instrument": [
            {
                "bond_maturity_date": datetime.date(9999, 12, 31),
                "ext.instrumentUniqueIdentifier": "unmapped_instrument_id_1",
            }
        ],
        "&model": ["OrderState"],
        "&key": ["OrderState:1234567:2022-05-17"],
        "&id": ["1234567:2022-05-17"],
        "executionDetails.orderStatus": ["FILL"],
        "timestamps.orderReceived": [pd.NA],
        "timestamps.orderSubmitted": [pd.NA],
        "timestamps.tradingDateTime": ["2022-05-17T10:30:32.000000Z"],
    }
    return TransformResult(target=pd.DataFrame(data))


@pytest.fixture
def order_with_no_meta():
    return TransformResult(
        target=pd.DataFrame(
            data={
                "instrumentDetails.instrument": {},
                "executionDetails.orderStatus": "FILL",
                "timestamps.orderReceived": pd.NA,
                "timestamps.orderSubmitted": pd.NA,
                "timestamps.tradingDateTime": "2022-05-17T10:30:32.000000Z",
                "ext.instrumentUniqueIdentifier": "",
            },
            index=[0],
        )
    )


@pytest.fixture
def bulk_writer_result():
    return ElasticBulkWriterResult(
        frame=pd.DataFrame(data={"status": WriteStatus.CREATED}, index=[0]),
        quarantined=False,
        total_bytes=10,
    )


@pytest.fixture
def es_client():
    class es:
        class meta:
            model = "&model"
            key = "&key"
            id = "&id"

    return es


@pytest.fixture
def expected_key():
    return {
        "stack": "dev",
        "tenant": "dummy",
        "model": "OrderState",
        "key": "OrderState:1234567:2022-05-17",
        "id": "1234567:2022-05-17",
        "orderStatus": "FILL",
        "orderReceived": "",
        "orderSubmitted": "",
        "tradingDateTime": "2022-05-17T10:30:32.000000Z",
        "instrumentUniqueIdentifier": None,
        "instrumentModel": "",
    }


class TestInstrumentMapper:
    def test_instruments_mapper_without_order_df_without_meta(
        self, order_with_no_meta, bulk_writer_result, caplog
    ):
        task = InstrumentMapper(name="InstrumentMapper")
        result = task.execute(
            source_frame=order_with_no_meta,
            bulk_writer_result=bulk_writer_result,
        )
        assert result.number_of_submitted_instruments == 0
        assert result.master_data_api_response == {}

    def test_instruments_mapper_with_empty_bulk_writer_result(self, order):
        task = InstrumentMapper(name="InstrumentMapper")
        result = task.execute(
            source_frame=order,
            bulk_writer_result=ElasticBulkWriterResult(
                frame=pd.DataFrame(), quarantined=False, total_bytes=0
            ),
        )
        assert result.number_of_submitted_instruments == 0
        assert result.master_data_api_response == {}

    def test_instruments_mapper_with_order_df_with_meta(
        self, mocker, order, bulk_writer_result, caplog
    ):
        Settings.STACK = "dev"
        mock_tenant = mocker.patch.object(
            SettingsCls, "tenant", new_callable=mocker.PropertyMock
        )
        mock_tenant.return_value = "dummy"
        mock_instrument_mapper_task = mocker.patch.object(
            instrument_mapper, "run_instrument_mapper"
        )
        mock_instrument_mapper_task.return_value = InstrumentMapperResult(
            number_of_submitted_instruments=1,
            master_data_api_response={
                "already_mapped": [],
                "submitted_for_map": ["unmapped_instrument_id_1"],
            },
        )

        task = InstrumentMapper(name="InstrumentMapper")
        result = task.execute(
            source_frame=order,
            bulk_writer_result=bulk_writer_result,
        )
        assert result.number_of_submitted_instruments == 1
        assert result.master_data_api_response == {
            "already_mapped": [],
            "submitted_for_map": ["unmapped_instrument_id_1"],
        }

        assert caplog.messages == [
            "Instruments already mapped :0",
            "Instruments submitted for mapping :1",
            "Messages for Instruments Mapper :1",
            "Messages not eligible for Instruments Mapper:0",
        ]

    def test_instrument_mapper_imports(self):
        assert run_instrument_mapper
