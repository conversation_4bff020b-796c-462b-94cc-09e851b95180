id: boring-bundle-without-control-flow
name: Boring bundle without control flow
image: swarm_tasks:3.31.3
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
parameters:
- name: flow_args
  envVar: SWARM_FLOW_ARGS
  description: "flow arguments"
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
tasks:
- path: swarm_tasks.io.read.aws.fetch_and_merge_text_files:FetchAndMergeTextFiles
  name: FetchAndMergeTextFiles
  upstreamTasks:
    - taskName: flow_args
      key: flow_args
- path: swarm.task.base:BaseTask
  name: Task2
  upstreamTasks:
    - taskName: FetchAndMergeTextFiles
      key: fix_dataframe