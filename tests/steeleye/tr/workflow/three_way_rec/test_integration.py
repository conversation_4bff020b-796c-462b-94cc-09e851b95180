from pathlib import Path

import addict
import pandas as pd
import pytest
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_elastic_schema.models import find_model
from swarm.utilities.indict import Indict

from swarm_tasks.io.read import xml_file_splitter
from swarm_tasks.io.read.xml_file_splitter import XMLFileSplitter
from swarm_tasks.steeleye.tr.static import RecFields
from swarm_tasks.steeleye.tr.workflow.three_way_rec import reconcile_report_transactions
from swarm_tasks.steeleye.tr.workflow.three_way_rec.build_transactions_from_report import (
    BuildTransactionsFromReport,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.link_report_transactions_to_steeleye import (
    Params,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.reconcile_report_transactions import (
    ReconcileReportTransactions,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.report_mechanism_mappings import (
    REPORT_MECHANISM_MAP,
)

SCRIPT_PATH = Path(__file__).parent


@pytest.fixture()
def extend_list_as_series_value_series() -> pd.Series:
    series = pd.Series(data=[["a", "b", "c"]])
    series.name = "idx1"
    return series


@pytest.fixture()
def extend_list_as_series_value_target() -> pd.DataFrame:
    df = pd.DataFrame(
        data=[[pd.NA], [["d"]]], columns=["extend_res"], index=["idx1", "idx2"]
    )
    return df


@pytest.fixture()
def extend_list_as_series_value_target_with_preexisting_list() -> pd.DataFrame:
    df = pd.DataFrame(
        data=[[["d"]], [pd.NA]], columns=["extend_res"], index=["idx1", "idx2"]
    )
    return df


@pytest.fixture()
def preprocess_data_df() -> pd.DataFrame:
    df = pd.DataFrame(
        data={
            "&id": ["id1", "id2", "id3"],
            "&model": ["RTS22Transaction", "RTS22Transaction", "RTS22Transaction"],
            "&hash": ["hash1", "hash2", "hash3"],
            RecFields.FIELD_BREAKS: [
                [
                    {
                        "field": "transactionDetails.quantity",
                        "value": {"se": 1, "nca": 2},
                    }
                ],
                [
                    {
                        "field": "transactionDetails.price",
                        "value": {"se": 100, "arm": 101},
                    }
                ],
                pd.NA,
            ],
            RecFields.TEMP_RECONCILIATION_NCA_SOURCE_KEY: [
                "test_file.xml",
                "test_file.xml",
                "test_file.xml",
            ],
        }
    )
    return df


class TestIntegrations:
    """
    Test cases for "ReconcileReportTransactions" and "BuildReportTransactions" classes
    """

    nca_params = Params(report_type="nca_fca")
    arm_params = Params(report_type="arm_unavista")
    es_client = addict.Dict(
        {
            "MAX_TERMS_SIZE": 1024,
            "meta": {"key": "&key", "model": "&model", "id": "&id", "hash": "&hash"},
        }
    )

    def test_extend_list_as_series_value(
        self,
        extend_list_as_series_value_series: pd.Series,
        extend_list_as_series_value_target: pd.DataFrame,
    ):
        task = ReconcileReportTransactions(
            name="reconcile-report-transactions", params=self.nca_params
        )

        task.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            self.nca_params.report_type
        ]()

        result = task.report_mechanism_reconciliation._extend_list_as_series_value(
            list_as_series=extend_list_as_series_value_series,
            target_col_name="extend_res",
            target=extend_list_as_series_value_target,
        )

        assert result == ["a", "b", "c"]

    def test_extend_list_as_series_value_with_preexisting_list(
        self,
        extend_list_as_series_value_series: pd.Series,
        extend_list_as_series_value_target_with_preexisting_list: pd.DataFrame,
    ):
        task = ReconcileReportTransactions(
            name="reconcile-report-transactions", params=self.nca_params
        )

        task.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            self.nca_params.report_type
        ]()

        result = task.report_mechanism_reconciliation._extend_list_as_series_value(
            list_as_series=extend_list_as_series_value_series,
            target_col_name="extend_res",
            target=extend_list_as_series_value_target_with_preexisting_list,
        )

        assert result == ["d", "a", "b", "c"]

    def test_nca_preprocess_data(
        self,
        preprocess_data_df,
    ):
        task = ReconcileReportTransactions(
            name="reconcile-report-transactions", params=self.nca_params
        )

        task.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            self.nca_params.report_type
        ]()

        target = pd.DataFrame(index=preprocess_data_df.index)
        task._preprocess_data(
            source_frame=preprocess_data_df, target=target, es=self.es_client
        )

        expected_result = pd.DataFrame(
            data={
                "&id": ["id1", "id2", "id3"],
                "&model": ["RTS22Transaction", "RTS22Transaction", "RTS22Transaction"],
                "&hash": ["hash1", "hash2", "hash3"],
                RecFields.RECONCILIATION_SE_MATCH: [True, True, True],
                RecFields.FIELD_BREAKS: [
                    pd.NA,
                    [
                        {
                            "field": "transactionDetails.price",
                            "value": {"se": 100, "arm": 101},
                        }
                    ],
                    pd.NA,
                ],
                RecFields.RECONCILIATION_NCA_FIELD_BREAK: [False, False, False],
                RecFields.RECONCILIATION_NCA_MATCH: [True, True, True],
                RecFields.RECONCILIATION_NCA_SOURCE_KEY: [
                    "test_file.xml",
                    "test_file.xml",
                    "test_file.xml",
                ],
            },
            index=preprocess_data_df.index,
        )

        assert target.equals(expected_result)

    def test_nca_reconciliation_with_xml_test_data(self):

        report_df = _build_nca_report_df(
            script_path=SCRIPT_PATH, xml_file_path="data/test_file1.xml"
        )
        se_record_df = pd.read_pickle(
            SCRIPT_PATH.joinpath("data/test_file1.pkl").__str__()
        )
        source_df = pd.concat([report_df, se_record_df], axis=1)

        reconcile_report_transactions.prefect.context = addict.Dict(
            {"swarm": {"tenant_configuration": {"trPIEnrichmentEnabled": False}}}
        )
        reconcile_task = ReconcileReportTransactions(
            name="reconcile-report-transactions", params=self.nca_params
        )
        reconcile_task.clients = {"test": self.es_client}
        reconcile_task.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            self.nca_params.report_type
        ]()

        resources = ReconcileReportTransactions.resources_class(es_client_key="test")

        target = reconcile_task.execute(
            source_frame=source_df, params=self.nca_params, resources=resources
        )

        breaks = target.loc[0, "reconciliation.fieldBreaks"]
        fields_breaking = [x["field"] for x in breaks]
        assert (
            fields_breaking.sort()
            == [
                "workflow.isReported",
                "workflow.nca.status",
                "transmissionDetails.orderTransmissionIndicator",
                "parties.executingEntity.firmIdentifiers.lei",
                "parties.executingEntity.details.mifidRegistered",
                "parties.executionWithinFirm.officialIdentifiers.mifirId",
                "parties.investmentDecisionWithinFirm.officialIdentifiers.mifirId",
                "parties.seller[].firmIdentifiers.lei",
                "parties.seller|Document.FinInstrmRptgTxRpt.Tx.New.Sellr.AcctOwnr.CtryOfBrnch",
            ].sort()
        )

    def test_nca_build_with_xml_test_data(self):

        report_df = _build_nca_report_df(
            script_path=SCRIPT_PATH, xml_file_path="data/test_file2.xml"
        )

        report_df[RecFields.TEMP_RECONCILIATION_NCA_SOURCE_KEY] = "test_file2.xml"

        build_task = BuildTransactionsFromReport(
            name="build-report-transactions", params=self.nca_params
        )
        build_task.clients = {"test": self.es_client}
        build_task.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            self.nca_params.report_type
        ]()

        resources = BuildTransactionsFromReport.resources_class(es_client_key="test")

        target = build_task.execute(
            source_frame=report_df, params=self.nca_params, resources=resources
        )
        target[RecFields.REPORT_DETAILS_REPORT_STATUS] = "NEWT"
        model = find_model("RTS22Transaction")

        target = (
            Indict(
                obj=target,
                from_dataframe_params={"date_format": "iso", "orient": "records"},
            )
            .remove_empty()
            .unflatten()
            .to_dict()
            .get("data")
        )

        valid_records = 0
        for it, record in enumerate(target):
            model(**record)
            valid_records += 1

        assert valid_records == 4


def _build_nca_report_df(script_path: Path, xml_file_path: str) -> pd.DataFrame:

    xml_nca_report_path = script_path.joinpath(xml_file_path)
    params = xml_file_splitter.Params(nested_data_path="Document.FinInstrmRptgTxRpt.Tx")
    xml_file_splitter.context = addict.Dict({"swarm": {"sources_dir": script_path}})
    xml_file_splitter_task = XMLFileSplitter(
        name="test-xml-file-splitter", params=params
    )
    xml_file_splitter_task.execute(
        extractor_result=ExtractPathResult(path=xml_nca_report_path), params=params
    )
    report_df = pd.read_csv(SCRIPT_PATH.joinpath("batch_0.csv"))

    for col in report_df.columns:
        col_not_null_mask = report_df.loc[:, col].notnull()
        report_df.loc[col_not_null_mask, col] = report_df.loc[
            col_not_null_mask, col
        ].astype(str)

    return report_df
