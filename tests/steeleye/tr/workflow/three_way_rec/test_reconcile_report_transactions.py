import addict
import pandas as pd
import pytest

from swarm_tasks.steeleye.tr.static import RecFields
from swarm_tasks.steeleye.tr.static import RecSources
from swarm_tasks.steeleye.tr.workflow.three_way_rec.link_report_transactions_to_steeleye import (
    Params,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.reconcile_report_transactions import (
    ReconcileReportTransactions,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.enums import ReportTypeEnum
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.report_mechanism_mappings import (
    REPORT_MECHANISM_MAP,
)


class TestReconcileReportTransactions:
    nca_params = Params(report_type="nca_fca")
    es_client = addict.Dict(
        {
            "MAX_TERMS_SIZE": 1024,
            "meta": {"key": "&key", "model": "&model", "id": "&id", "hash": "&hash"},
        }
    )

    def test_transaction_price_conversion(
        self, se_parse_price_source_frame_df: pd.DataFrame
    ) -> None:
        result = ReconcileReportTransactions.sanitize_price_based_on_price_notation(
            source_data=se_parse_price_source_frame_df
        )

        # MONE - Scaled to 13
        assert result[0] == 1.1111111111111
        # PERC - Scaled to 10
        assert result[1] == 1.1111111111
        # YIEL - Scaled to 10
        assert result[2] == 1.1111111111
        # BAPO - Scaled to 17
        assert result[3] == 1.11111111111111111

    def test_validate_bool_report_type_field_break_1(
        self, target_df_with_no_field_breaks_na_df: pd.DataFrame
    ):
        result = ReconcileReportTransactions.validate_bool_report_type_field_break(
            target=target_df_with_no_field_breaks_na_df,
            report_type=ReportTypeEnum.NCA_FCA,
        )

        assert not result[RecFields.RECONCILIATION_NCA_FIELD_BREAK][0]
        assert not result[RecFields.RECONCILIATION_NCA_FIELD_BREAK][1]
        assert not result[RecFields.RECONCILIATION_NCA_FIELD_BREAK][2]

    def test_validate_bool_report_type_field_break_2(
        self, target_df_with_no_field_breaks_list_df: pd.DataFrame
    ):
        result = ReconcileReportTransactions.validate_bool_report_type_field_break(
            target=target_df_with_no_field_breaks_list_df,
            report_type=ReportTypeEnum.ARM_UNAVISTA,
        )

        assert not result[RecFields.RECONCILIATION_ARM_FIELD_BREAK][0]
        assert not result[RecFields.RECONCILIATION_ARM_FIELD_BREAK][1]
        assert not result[RecFields.RECONCILIATION_ARM_FIELD_BREAK][2]

    def test_validate_bool_report_type_field_break_3(
        self, target_df_with_nca_field_breaks_df: pd.DataFrame
    ):
        result = ReconcileReportTransactions.validate_bool_report_type_field_break(
            target=target_df_with_nca_field_breaks_df,
            report_type=ReportTypeEnum.NCA_FCA,
        )

        assert result[RecFields.RECONCILIATION_NCA_FIELD_BREAK][0]
        assert result[RecFields.RECONCILIATION_NCA_FIELD_BREAK][1]

    def test_validate_bool_report_type_field_break_4(
        self, target_df_with_arm_field_breaks_df: pd.DataFrame
    ):
        result = ReconcileReportTransactions.validate_bool_report_type_field_break(
            target=target_df_with_arm_field_breaks_df,
            report_type=ReportTypeEnum.ARM_UNAVISTA,
        )

        assert result[RecFields.RECONCILIATION_ARM_FIELD_BREAK][0]
        assert result[RecFields.RECONCILIATION_ARM_FIELD_BREAK][1]

    def test_validate_bool_report_type_field_break_5(
        self, target_df_with_both_nca_and_arm_field_breaks_df: pd.DataFrame
    ):
        result = ReconcileReportTransactions.validate_bool_report_type_field_break(
            target=target_df_with_both_nca_and_arm_field_breaks_df,
            report_type=ReportTypeEnum.ARM_UNAVISTA,
        )

        assert not result[RecFields.RECONCILIATION_ARM_FIELD_BREAK][0]
        assert result[RecFields.RECONCILIATION_ARM_FIELD_BREAK][1]
        assert result[RecFields.RECONCILIATION_ARM_FIELD_BREAK][2]

    def test_preprocess_data_with_both_arm_and_nca_fieldbreaks(
        self, target_df_with_both_nca_and_arm_field_breaks_df: pd.DataFrame
    ):
        task = ReconcileReportTransactions(
            name="reconcile-report-transactions", params=self.nca_params
        )

        task.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            self.nca_params.report_type
        ]()

        target = pd.DataFrame(
            index=target_df_with_both_nca_and_arm_field_breaks_df.index
        )
        task._preprocess_data(
            source_frame=target_df_with_both_nca_and_arm_field_breaks_df,
            target=target,
            es=self.es_client,
        )

        assert pd.isna(target[RecFields.FIELD_BREAKS][0])
        assert target[RecFields.FIELD_BREAKS][1]
        assert target[RecFields.FIELD_BREAKS][2]
        with pytest.raises(KeyError):
            target[RecFields.FIELD_BREAKS][2][0][RecFields.FIELD_BREAKS_VALUE][
                RecSources.NCA
            ]
