import pandas as pd

from swarm_tasks.steeleye.tr.workflow.three_way_rec.nca_fca_report_mechanism_reconciliation import (
    NcaFcaReportMechanismReconciliation,
)


class TestBuildNCATransactions:
    def test_transaction_details_quantity_notation(
        self, nca_transaction_details_quantity_notation_df: pd.DataFrame,
    ):
        target = pd.DataFrame(index=nca_transaction_details_quantity_notation_df.index)
        task = NcaFcaReportMechanismReconciliation()
        task.populate_compound_mappings(
            source_data=nca_transaction_details_quantity_notation_df, target=target
        )

        assert list(target["transactionDetails.quantity"].values) == [
            "5",
            "20",
            pd.NA,
            "3",
            "50",
        ]

        assert list(target["transactionDetails.quantityNotation"].values) == [
            "UNIT",
            "MONE",
            pd.NA,
            "NOML",
            "MONE",
        ]
