���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�dataSourceName��date��__meta_model__��,reportDetails.investmentFirmCoveredDirective��reportDetails.reportStatus��sourceIndex��	sourceKey��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��transactionDetails.venue�� transactionDetails.ultimateVenue��.transmissionDetails.orderTransmissionIndicator��reportDetails.transactionRefNo��__asset_class__��__currency__��	__venue__��marketIdentifiers.instrument��marketIdentifiers.parties��marketIdentifiers��__expiry_date__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK	��h�i8�����R�(K�<�NNNJ����J����K t�b�CH                                                                �t�bhC�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KKK	��h�b1�����R�(Kh"NNNJ����J����K t�b�C         �t�bhhK ��h��R�(KKK	��h�f8�����R�(KhQNNNJ����J����K t�b�C�33333�]@[B>�٬�?�y�):��?     P�@��4�8E�?�G�z�p@=,Ԛ��?�����#�@fffffa@      @      �?       @      @       @      �?      �?      �?      �?�t�bhhK ��h��R�(KKK	��hP�CH                                                                �t�bhhK ��h��R�(KKK	��h!�]�(�Saxobank�hxhxhxhxhxhxhxhx�
2022-02-01��
2022-02-01��
2022-02-01��
2022-02-01��
2022-02-01��
2022-02-01��
2022-02-01��
2022-02-01��
2022-02-01��RTS22Transaction�h�h�h�h�h�h�h�h��NEWT�h�h�h�h�h�h�h�h��y/Users/<USER>/Desktop/steeleye/se-prefect-dir/swarm-tasks/tests/steeleye/tr/feed/saxo_bank/data/FuturesTrades_test.pkl�h�h�h�h�h�h�h�h��BUYI��SELL�h�h�h�h�h�h�h��pandas._libs.missing��NA���h�h�h�h�h�h�h�h��EUR��USD�h�h�h�h�h�h�h��MONE�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��UNIT�h�h�h�h�h�h�h�h��AOTC�h�h�h�h�h�h�h�h��2022-02-01T08:13:13.590000Z��2022-02-01T09:07:05.713000Z��2022-02-01T09:07:49.537000Z��2022-02-01T09:47:48.767000Z��2022-02-01T09:09:20.730000Z��2022-02-01T07:51:41.113000Z��2022-02-01T09:08:37.367000Z��2022-02-01T07:54:22.953000Z��2022-02-01T07:58:40.553000Z��XOFF��XXXX�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��$51244346072022020108131320220201BUYI��$51245398112022020109070520220201SELL��$51245407322022020109074920220201SELL��$51245918032022020109474820220201SELL��$51245426042022020109092020220201SELL��$51243911322022020107514120220201BUYI��$51245416942022020109083720220201SELL��$51243931092022020107542220220201SELL��$51243961952022020107584020220201SELL��future�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��XEUR��XCME�h��IFUS�h��XNYM�h�h��XSIM�]�(}�(�labelId��DE000F0VD0D8��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(h��DE000F0VD0D8EURXEUR�h�h�h�h�u}�(h��XEURFEXDFF2022-12-16 00:00:00�h�h�h�h�u}�(h��XEURFEXDFF2022-12 00:00:00�h�h�h�h�ue]�(}�(h��XCMEADFF2022-03-14 00:00:00�h�h�h�h�u}�(h��XCMEADFF2022-03 00:00:00�h�h�h�h�ue]�(}�(h��XCMEBPFF2022-03-14 00:00:00�h�h�h�h�u}�(h��XCMEBPFF2022-03 00:00:00�h�h�h�h�ue]�(}�(h��IFUSCCFF2022-05-13 00:00:00�h�h�h�h�u}�(h��IFUSCCFF2022-05 00:00:00�h�h�h�h�ue]�(}�(h��XCMECDFF2022-03-15 00:00:00�h�h�h�h�u}�(h��XCMECDFF2022-03 00:00:00�h�h�h�h�ue]�(}�(h��XNYMHOFF2022-02-28 00:00:00�h�h�h�h�u}�(h��XNYMHOFF2022-02 00:00:00�h�h�h�h�ue]�(}�(h��XCMENEFF2022-03-14 00:00:00�h�h�h�h�u}�(h��XCMENEFF2022-03 00:00:00�h�h�h�h�ue]�(}�(h��XNYMPLFF2022-04-27 00:00:00�h�h�h�h�u}�(h��XNYMPLFF2022-04 00:00:00�h�h�h�h�ue]�(}�(h��XSIMSCOFF2022-03-31 00:00:00�h�h�h�h�u}�(h��XSIMSCOFF2022-03 00:00:00�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h��parties.counterparty�h�h�u}�(h��id:16152148�h��parties.trader�h�h��ARRAY���R�u}�(h��lei:549300tl5406ic1xkd09�h��parties.seller�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:16152148�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h��
parties.buyer�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:16152148�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:16152148�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:16152148�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:16152148�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:16152148�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:16152148�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:16152148�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(h�h�h�h�h�h�h�e]�(h�h�h�h�h�e]�(h�h�h�h�h�e]�(h�h�j  j  j  e]�(h�h�j  j
  j  e]�(h�h�j  j  j  e]�(h�h�j  j  j  e]�(h�h�j  j  j!  e]�(h�h�j$  j&  j(  eet�bhhK ��h��R�(KKK	��h!�]�(�
2022-12-16��
2022-03-14��
2022-03-14��
2022-05-13��
2022-03-15��
2022-02-28��
2022-03-14��
2022-04-27��
2022-03-31�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h(h,h9et�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h/h2et�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h)h+h-h.h0h1h3h4h5h6h7h8h:h;h<h=h>h?h@et�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhCNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h[�mgr_locs�hhK ��h��R�(KK��h�i8�����R�(KhQNNNJ����J����K t�b�C                     �t�bu}�(jx  hejy  �builtins��slice���K
KK��R�u}�(jx  hojy  j�  KKK��R�u}�(jx  hujy  hhK ��h��R�(KK��j�  �C�                                           	                                                                                                                �t�bu}�(jx  j6  jy  j�  KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.