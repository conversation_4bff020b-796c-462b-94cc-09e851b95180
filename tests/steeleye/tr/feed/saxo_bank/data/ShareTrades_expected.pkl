���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�dataSourceName��date��__meta_model__��,reportDetails.investmentFirmCoveredDirective��reportDetails.reportStatus��sourceIndex��	sourceKey��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��3tradersAlgosWaiversIndicators.shortSellingIndicator��#transactionDetails.buySellIndicator��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��transactionDetails.venue�� transactionDetails.ultimateVenue��.transmissionDetails.orderTransmissionIndicator��reportDetails.transactionRefNo��__asset_class__��__currency__��	__venue__��marketIdentifiers.instrument��marketIdentifiers.parties��marketIdentifiers��__expiry_date__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK	��h�i8�����R�(K�<�NNNJ����J����K t�b�CH                                                                �t�bhC�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK	��h!�]�(�SELL�hdhdhdhdhdhdhdhdet�b�_dtype�hY�StringDtype���)��ubhhK ��h��R�(KKK	��h�b1�����R�(Kh"NNNJ����J����K t�b�C         �t�bhhK ��h��R�(KKK	��h�f8�����R�(KhQNNNJ����J����K t�b�C�)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?      n@      n@     ��@     @�@      I@     ��@    �v�@    �v�@     @@�t�bhhK ��h��R�(KKK	��hP�CH                                                                �t�bhhK ��h��R�(KKK	��h!�]�(�Saxobank�h�h�h�h�h�h�h�h��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��RTS22Transaction�h�h�h�h�h�h�h�h��NEWT�h�h�h�h�h�h�h�h��w/Users/<USER>/Desktop/steeleye/se-prefect-dir/swarm-tasks/tests/steeleye/tr/feed/saxo_bank/data/ShareTrades_test.pkl�h�h�h�h�h�h�h�h��BUYI��SELL�h�h�h�h�h�h�h��EUR�h��CAD�h��USD�h�h�h�h��MONE�h�h�h�h�h�h�h�h��pandas._libs.missing��NA���h�h�h�h�h�h�h�h��UNIT�h�h�h�h�h�h�h�h��AOTC�h�h�h�h�h�h�h�h��2021-10-12T10:39:51.000000Z��2021-10-12T10:39:51.000000Z��2021-10-12T16:19:33.107000Z��2021-10-12T15:42:11.470000Z��2021-10-12T13:30:09.990000Z��2021-10-12T09:41:04.270000Z��2021-10-12T10:39:51.000000Z��2021-10-12T10:39:51.000000Z��2021-10-12T22:03:07.430000Z��XOFF�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��$50168917172021101210395120211012BUYI��$50168917162021101210395120211012SELL��$50174664472021101216193320211012BUYI��$50174924472021101215421120211012BUYI��$50171925222021101213300920211012SELL��$50167881412021101209410420211012SELL��$50168912572021101210395120211012BUYI��$50168912562021101210395120211012SELL��$50178127852021101222030720211012SELL�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�]�(}�(�labelId��id:040300zz0406ic1zzz00��path��parties.counterparty��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(h��id:040300zz0406ic1zzz00�h��parties.seller�h�h��ARRAY���R�ue]�(}�(h��id:040300zz0406ic1zzz00�h�h�h�h�u}�(h��id:040300zz0406ic1zzz00�h��
parties.buyer�h�h�ue]�(}�(h��id:040300zz0406ic1zzz00�h�h�h�h�u}�(h��
id:1111111�h��parties.trader�h�h�u}�(h��id:040300zz0406ic1zzz00�h�h�h�h�ue]�(}�(h��id:040300zz0406ic1zzz00�h�h�h�h�u}�(h��
id:1111111�h�h�h�h�u}�(h��id:040300zz0406ic1zzz00�h�h�h�h�ue]�(}�(h��id:040300zz0406ic1zzz00�h�h�h�h�u}�(h��
id:1111111�h�h�h�h�u}�(h��id:040300zz0406ic1zzz00�h�h�h�h�ue]�(}�(h��id:040300zz0406ic1zzz00�h�h�h�h�u}�(h��
id:1111111�h�h�h�h�u}�(h��id:040300zz0406ic1zzz00�h�h�h�h�ue]�(}�(h��id:040300zz0406ic1zzz00�h�h�h�h�u}�(h��id:040300zz0406ic1zzz00�h�h�h�h�ue]�(}�(h��id:040300zz0406ic1zzz00�h�h�h�h�u}�(h��id:040300zz0406ic1zzz00�h�h�h�h�ue]�(}�(h��id:040300zz0406ic1zzz00�h�h�h�h�u}�(h��id:040300zz0406ic1zzz00�h�h�h�h�ue]�(h�h�e]�(h�h�e]�(h�h�h�e]�(h�h�h�e]�(h�h�h�e]�(h�h�h�e]�(h�h�e]�(h�h�e]�(h�h�eet�bhhK ��h��R�(KKK	��h!�]�(h�h�h�h�h�h�h�h�h�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h(h,h9et�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h/h2et�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h)h+h.h0h1h3h4h5h6h7h8h:h;h<h=h>h?h@et�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhCNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h\�mgr_locs��builtins��slice���KK	K��R�u}�(jG  hljH  hhK ��h��R�(KK��h�i8�����R�(KhQNNNJ����J����K t�b�C                     �t�bu}�(jG  hvjH  jK  K
KK��R�u}�(jG  h�jH  jK  KKK��R�u}�(jG  h�jH  hhK ��h��R�(KK��jU  �C�                                    	                                                                                                                �t�bu}�(jG  j  jH  jK  KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.