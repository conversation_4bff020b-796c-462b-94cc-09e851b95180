���(      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKJ��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�
ReportingDate��InstrumentType��
CounterpartID��CounterpartName��
AccountNumber��PartnerAccountKey��AccountCurrency��InstrumentCode��InstrumentDescription��InstrumentCurrency��ISINCode��ExchangeDescription��TradedAmount��
FigureSize��TradeNumber��OrderNumber��	TradeTime��	TradeDate��	ValueDate��BuySell��Price��QuotedValueInstrumentCurrency��CommissionInstrumentCurrency��ExchangeFeeInstrumentCurrency��TransferFee��TaxInstrumentCurrency��RelatedTradeID��	TradeType��CAEventTypeName��	CAEventID��TradeAllocation��RootTradeID��ExternalOrderID��OriginalTradeID��
CorrectionLeg��
PositionID��
InstrumentUIC��ExchangeISOCode��ISOMic��ETORelatedClosingTrade��
CAEventTypeID��DVP��EODRate��ActualTradeDate��!ExecutingEntityIdentificationCode��
UserIdOnTrade��
EmployeeId��DecisionMakerUserId��Venue��TradeExecutionTime��ShortSellingIndicator��OTCPostTradeIndicator��'SecuritiesFinancingTransactionIndicator��SaxoPortfolioManagerTriggered��CorrelationKey��InstrumentSubType��ParentInstrumentUIC��ParentInstrumentName��+AdditionalTransactionCostInstrumentCurrency��OriginatingTool��
WithAdvice��InstrumentISINCode��	NetAmount��ComplexTradeComponentId��Strike��TermOfUnderlyingIndex��UnderlyingInstrumentCode��FXType��TransmissionOfOrderIndicator��ISIN��
DirtyPrice��UnderlyingInstrumentISINCode��
ExpiryDate��CallPut�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK	��h�i8�����R�(K�<�NNNJ����J����K t�b�CH                                                                �t�bhp�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK	��h!�]�(�pandas._libs.missing��NA���h�h�h�h�h�h�h�h�et�b�_dtype�h��StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�Shares�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�1111111�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�LHA:xetr��LOCK-8202:xxxx��MGG:xtsx��EQX:xtse��	XOMA:xnas��ACX:xmce�j  j  �	VBIO:xnas�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�EUR�j  �CAD�j  �USD�j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�ZZ0008232125�j!  �ZZ60252Q1019��ZZ29446Y5020��ZZ98419J2069��ZZ0132105018�j!  j!  �ZZ56089M1071�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�
5016891717��
5016891716��
5017466447��
5017492447��
5017192522��
5016788141��
5016891257��
5016891256��
5017812785�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�20211012 10:39:51�jB  �20211012 16:19:33��20211012 15:42:11��20211012 13:30:09��20211012 09:41:04�jB  jB  �20211012 22:03:07�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�Buy��Sell�jQ  jQ  jR  jR  jQ  jR  jR  et�bh�h�)��ubhhK ��h��R�(KKK	��h�f8�����R�(Kh~NNNJ����J����K t�b�Bh        n@      n�     ��@     @�@      I�     ���    �v�@    �v��     @�)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KK/K	��h!�]�(�20211012�jd  jd  jd  jd  jd  jd  jd  jd  �name�je  je  je  je  je  je  je  je  �
111111INET�jf  jf  jf  jf  jf  jf  jf  jf  G�      G�      G�      G�      G�      G�      G�      G�      G�      �EUR�jg  jg  jg  jg  jg  jg  jg  �USD��instrument name�ji  ji  ji  ji  ji  ji  ji  ji  �
exchange name�jj  jj  jj  jj  jj  jj  jj  jj  �1�jk  jk  jk  jk  jk  jk  jk  jk  �
2030661949��
2030662490��
2031185458��
2031211429��
2030927872��
2030567677��
2030662697��
2030663896��
2031519502��20211012�ju  ju  ju  ju  ju  ju  ju  ju  �20211012�jv  �20211014�jw  jw  jw  jv  jv  �20160816��-1.01�jy  jy  jy  �1.01�jz  jy  jz  jz  �0�j{  �-25��-30��-14.56��-10.4�j{  j{  j{  j{  j{  j{  j{  �-0.01�j{  j{  j{  j{  G�      G�      G�      G�      G�      G�      G�      G�      G�      j{  j{  j{  j{  j{  j{  j{  j{  j{  j{  j{  j{  j{  j{  j{  j{  j{  j{  �Corporate Action Tool�j�  �OpenAPI�j�  j�  j�  j�  j�  �UIC Change Position Move��Merger�j�  G�      G�      G�      G�      j�  j�  G�      �8831163�j�  j{  j{  j{  j{  j�  j�  j{  �No�j�  j�  j�  j�  j�  j�  j�  j�  j{  j{  j{  j{  j{  j{  j{  j{  j{  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �	990651763�G�      G�      G�      G�      G�      G�      G�      G�      �Cancel��	520000000��	520000001��	520000002��	520000003��	520000004��	520000005��	520000006��	520000007��	520000008��117621��25165972��253164��18471786��8979��1154�j�  j�  �4770666��XETR�j�  �XTNX��XTSE��XNAS��XMAD�j�  j�  �PINX�G�      G�      G�      G�      G�      G�      G�      G�      G�      �184�j�  G�      G�      G�      G�      j�  j�  G�      �No�j�  j�  j�  j�  j�  j�  j�  j�  �5.812�j�  �0.365��9.51��25.8��11.835�j�  j�  �0.3355��20211012�j�  j�  j�  j�  j�  j�  j�  �20160816�G�      G�      �1111111�j�  j�  j�  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �FSE�j�  �XTSX��TSE��CDED��XMAD�j�  j�  �OOTC�G�      G�      G�      G�      G�      G�      G�      G�      G�      �FALSE�j�  j�  j�  j�  j�  j�  j�  j�  �FALSE�j�  j�  j�  j�  j�  j�  j�  j�  �$6F1F2504-8AA1-4BA0-BF88-753C74F442BF�j�  �$8FDE9F64-8DE8-4780-B5E8-4698DBA4658C��$1E2B3C11-53F6-4971-B65D-2455696F93D2��$9892325E-F793-4F6C-B5C4-39F7A7BF8900��$1823256D-E812-4C6B-AF2A-1B25E0115FE9�j�  j�  �$2914A705-D946-4F38-86C8-89DAE32A996C��None�j�  j�  j�  j�  j�  j�  j�  j�  �25165972�j�  j{  j{  j{  j{  j�  j�  j{  �parent name�j�  G�      G�      G�      G�      j�  j�  G�      j{  j{  j{  j{  j{  j{  j{  j{  j{  �Corporate Action�j�  �SaxoTraderGO�j�  j�  j�  j�  j�  �UIC Change Position Move�G�      G�      G�      G�      G�      G�      G�      G�      G�      et�bh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�XETR�j�  �XTSX��XTSE��XNAS��XMCE�j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�040300ZZ0406IC1ZZz00�j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�20211012 10:39:51.000�j�  �20211012 16:19:33.107��20211012 15:42:11.470��20211012 13:30:09.990��20211012 09:41:04.270�j�  j�  �20211012 22:03:07.430�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK	��h!�]�(�SELL�j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�hbat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hdat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hfat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hgat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hhat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hiat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hjat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hlat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hmat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hnat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h1h9hchehket�bhpNu��R�h
h}�(hhhK ��h��R�(KK/��h!�]�(h%h(h)h*h+h-h0h2h4h6h7h:h;h<h=h>h?h@hAhBhChDhEhFhGhHhIhKhLhMhNhOhPhRhShThUhXhYhZh[h\h]h^h_h`haet�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bhpNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���K=K>K��R�u}�(j�  h�j�  j�  K?K@K��R�u}�(j�  h�j�  j�  KAKBK��R�u}�(j�  h�j�  j�  KBKCK��R�u}�(j�  h�j�  j�  KCKDK��R�u}�(j�  h�j�  j�  KDKEK��R�u}�(j�  h�j�  j�  KEKFK��R�u}�(j�  h�j�  j�  KGKHK��R�u}�(j�  h�j�  j�  KHKIK��R�u}�(j�  h�j�  j�  KIKJK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  j  j�  j�  K	K
K��R�u}�(j�  j  j�  j�  K
KK��R�u}�(j�  j)  j�  j�  KKK��R�u}�(j�  j;  j�  j�  KKK��R�u}�(j�  jJ  j�  j�  KKK��R�u}�(j�  jW  j�  hhK ��h��R�(KK��h}�C(              >       @       F       �t�bu}�(j�  ja  j�  hhK ��h��R�(KK/��h}�Bx                                                    
                                                                                                                 !       "       #       $       &       '       (       )       *       +       -       .       /       0       3       4       5       6       7       8       9       :       ;       <       �t�bu}�(j�  j�  j�  j�  K%K&K��R�u}�(j�  j�  j�  j�  K,K-K��R�u}�(j�  j�  j�  j�  K1K2K��R�u}�(j�  j�  j�  j�  K2K3K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.