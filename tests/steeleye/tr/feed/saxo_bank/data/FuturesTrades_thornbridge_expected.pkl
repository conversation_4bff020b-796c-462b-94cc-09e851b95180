��\      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�dataSourceName��date��__meta_model__��,reportDetails.investmentFirmCoveredDirective��reportDetails.reportStatus��sourceIndex��	sourceKey��:tradersAlgosWaiversIndicators.commodityDerivativeIndicator��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityNotation��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��transactionDetails.venue�� transactionDetails.ultimateVenue��.transmissionDetails.orderTransmissionIndicator��reportDetails.transactionRefNo��marketIdentifiers.instrument��marketIdentifiers.parties��marketIdentifiers��__expiry_date__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK	��h�i8�����R�(K�<�NNNJ����J����K t�b�CH                                                                �t�bh@�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KKK	��h�b1�����R�(Kh"NNNJ����J����K t�b�C$                  �t�bhhK ��h��R�(KKK	��h�f8�����R�(KhNNNNJ����J����K t�b�C�33333�]@[B>�٬�?�y�):��?     P�@��4�8E�?�G�z�p@=,Ԛ��?�����#�@fffffa@      @      �?       @      @       @      �?      �?      �?      �?�t�bhhK ��h��R�(KKK	��hM�CH                                                                �t�bhhK ��h��R�(KKK	��h!�]�(�Saxobank�huhuhuhuhuhuhuhu�
2022-02-01��
2022-02-01��
2022-02-01��
2022-02-01��
2022-02-01��
2022-02-01��
2022-02-01��
2022-02-01��
2022-02-01��RTS22Transaction�hhhhhhhh�NEWT�h�h�h�h�h�h�h�h��XC:\dev_steeleye\swarm-tasks\tests\steeleye\tr\feed\saxo_bank\data\FuturesTrades_test.pkl�h�h�h�h�h�h�h�h��BUYI��SELL�h�h�h�h�h�h�h��pandas._libs.missing��NA���h�h�h�h�h�h�h�h��EUR��USD�h�h�h�h�h�h�h��MONE�h�h�h�h�h�h�h�h��UNIT�h�h�h�h�h�h�h�h��AOTC�h�h�h�h�h�h�h�h��2022-02-01T08:13:13.590000Z��2022-02-01T09:07:05.713000Z��2022-02-01T09:07:49.537000Z��2022-02-01T09:47:48.767000Z��2022-02-01T09:09:20.730000Z��2022-02-01T07:51:41.113000Z��2022-02-01T09:08:37.367000Z��2022-02-01T07:54:22.953000Z��2022-02-01T07:58:40.553000Z��XOFF��XXXX�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��$51244346072022020108131320220201BUYI��$51245398112022020109070520220201SELL��$51245407322022020109074920220201SELL��$51245918032022020109474820220201SELL��$51245426042022020109092020220201SELL��$51243911322022020107514120220201BUYI��$51245416942022020109083720220201SELL��$51243931092022020107542220220201SELL��$51243961952022020107584020220201SELL�]�(}�(�labelId��DE000F0VD0D8��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(h��DE000F0VD0D8EURXEUR�h�h�h�h�u}�(h��XEURFEXDFF2022-12-16 00:00:00�h�h�h�h�u}�(h��XEURFEXDFF2022-12 00:00:00�h�h�h�h�ue]�(}�(h��XCMEADFF2022-03-14 00:00:00�h�h�h�h�u}�(h��XCMEADFF2022-03 00:00:00�h�h�h�h�ue]�(}�(h��XCMEBPFF2022-03-14 00:00:00�h�h�h�h�u}�(h��XCMEBPFF2022-03 00:00:00�h�h�h�h�ue]�(}�(h��ICUSCCFF2022-05-13 00:00:00�h�h�h�h�u}�(h��ICUSCCFF2022-05 00:00:00�h�h�h�h�ue]�(}�(h��XCMECDFF2022-03-15 00:00:00�h�h�h�h�u}�(h��XCMECDFF2022-03 00:00:00�h�h�h�h�ue]�(}�(h��XCMEHOFF2022-02-28 00:00:00�h�h�h�h�u}�(h��XCMEHOFF2022-02 00:00:00�h�h�h�h�ue]�(}�(h��XCMENEFF2022-03-14 00:00:00�h�h�h�h�u}�(h��XCMENEFF2022-03 00:00:00�h�h�h�h�ue]�(}�(h��XCMEPLFF2022-04-27 00:00:00�h�h�h�h�u}�(h��XCMEPLFF2022-04 00:00:00�h�h�h�h�ue]�(}�(h��XSESSCOFF2022-03-31 00:00:00�h�h�h�h�u}�(h��XSESSCOFF2022-03 00:00:00�h�h�h�h�ue]�(}�(h��lei:2138008neory2dqv4r24�h��parties.executingEntity�h�h�u}�(h��lei:549300tl5406ic1xkd09�h��parties.counterparty�h�h�u}�(h��id:lars wind�h��parties.executionWithinFirm�h�h�u}�(h��id:lars wind�h��$parties.investmentDecisionWithinFirm�h�h�u}�(h��lei:254900lfcdlgd2nhgm50�h��
parties.buyer�h�h��ARRAY���R�u}�(h��lei:549300tl5406ic1xkd09�h��parties.seller�h�h�u}�(h��lei:2138008neory2dqv4r24�h��parties.buyerDecisionMaker�h�h�ue]�(}�(h��lei:2138008neory2dqv4r24�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:lars wind�h�h�h�h�u}�(h��id:lars wind�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��lei:254900lfcdlgd2nhgm50�h�h�h�h�u}�(h��lei:2138008neory2dqv4r24�h��parties.sellerDecisionMaker�h�h�ue]�(}�(h��lei:2138008neory2dqv4r24�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:lars wind�h�h�h�h�u}�(h��id:lars wind�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��lei:254900lfcdlgd2nhgm50�h�h�h�h�u}�(h��lei:2138008neory2dqv4r24�h�j  h�h�ue]�(}�(h��lei:2138008neory2dqv4r24�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:lars wind�h�h�h�h�u}�(h��id:lars wind�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��lei:254900lfcdlgd2nhgm50�h�h�h�h�u}�(h��lei:2138008neory2dqv4r24�h�j  h�h�ue]�(}�(h��lei:2138008neory2dqv4r24�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:lars wind�h�h�h�h�u}�(h��id:lars wind�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��lei:254900lfcdlgd2nhgm50�h�h�h�h�u}�(h��lei:2138008neory2dqv4r24�h�j  h�h�ue]�(}�(h��lei:2138008neory2dqv4r24�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:lars wind�h�h�h�h�u}�(h��id:lars wind�h�h�h�h�u}�(h��lei:254900lfcdlgd2nhgm50�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��lei:2138008neory2dqv4r24�h�h�h�h�ue]�(}�(h��lei:2138008neory2dqv4r24�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:lars wind�h�h�h�h�u}�(h��id:lars wind�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��lei:254900lfcdlgd2nhgm50�h�h�h�h�u}�(h��lei:2138008neory2dqv4r24�h�j  h�h�ue]�(}�(h��lei:2138008neory2dqv4r24�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:lars wind�h�h�h�h�u}�(h��id:lars wind�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��lei:254900lfcdlgd2nhgm50�h�h�h�h�u}�(h��lei:2138008neory2dqv4r24�h�j  h�h�ue]�(}�(h��lei:2138008neory2dqv4r24�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:lars wind�h�h�h�h�u}�(h��id:lars wind�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��lei:254900lfcdlgd2nhgm50�h�h�h�h�u}�(h��lei:2138008neory2dqv4r24�h�j  h�h�ue]�(h�h�h�h�h�h�h�h�h�h�h�e]�(h�h�h�h�h�h�h�h�j  e]�(h�h�j  j  j	  j  j
  j  j  e]�(h�h�j  j  j  j  j  j  j   e]�(h�h�j#  j%  j'  j)  j+  j-  j/  e]�(h�h�j2  j4  j6  j8  j:  j<  j>  e]�(h�h�jA  jC  jE  jG  jI  jK  jM  e]�(h�h�jP  jR  jT  jV  jX  jZ  j\  e]�(h�h�j_  ja  jc  je  jg  ji  jk  e�
2022-12-16��
2022-03-14��
2022-03-14��
2022-05-13��
2022-03-15��
2022-02-28��
2022-03-14��
2022-04-27��
2022-03-31�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h(h,h-h9et�bh@Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h0h3et�bh@Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bh@Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h)h+h.h/h1h2h4h5h6h7h8h:h;h<h=h>et�bh@Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hX�mgr_locs�hhK ��h��R�(KK��h�i8�����R�(KhNNNNJ����J����K t�b�C                             �t�bu}�(j�  hbj�  �builtins��slice���KKK��R�u}�(j�  hlj�  j�  KKK��R�u}�(j�  hrj�  hhK ��h��R�(KK��j�  �C�                                    	       
              
                                                                             �t�bueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.