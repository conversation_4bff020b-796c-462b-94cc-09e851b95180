����      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKX��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�
Report Status��Transaction Reference Number��Submitting Entity ID��Executing Entity ID��Investment Firm Indicator��
Buyer ID Type��Buyer ID��Buyer Decision Maker ID Type��Buyer Decision Maker ID��Seller ID Type��	Seller ID��Seller Decision Maker ID Type��Seller Decision Maker ID��Order Transmission Indicator��Trading Date Time��Trading Capacity��Quantity��
Quantity Type��Price��
Price Type��Price Currency��Venue��Instrument ID Type��
Instrument ID��Instrument Name��Investment Decision ID Type��Investment Decision ID��Firm Execution ID Type��Firm Execution ID��Short Selling Indicator��
SFT Indicator��Internal Client Identification��Unnamed: 87��Buyer Country of Branch��	Buyer DOB��Buyer Decision Maker DOB��Buyer Decision Maker First Name�� Buyer Decision Maker ID Sub Type��Buyer Decision Maker Surname��Buyer First Name��Buyer ID Sub Type��
Buyer Surname��Buyer Transmitter ID��Commodity Derivative Indicator��Complex Trade Component ID��Country of Branch��
Data Category��
Delivery Type��Derivative Notional Change��Expiry Date�� Firm Execution Country of Branch��Firm Execution ID Sub Type��Instrument Classification��%Investment Decision Country of Branch��Investment Decision ID Sub Type��
Maturity Date��
Net Amount��Notional Currency 1��Notional Currency 2��Notional Currency 2 Type��OTC Post Trade Indicator��Option Style��Option Type��Price Multiplier��Quantity Currency��Seller Country of Branch��
Seller DOB��Seller Decision Maker DOB�� Seller Decision Maker First Name��!Seller Decision Maker ID Sub Type��Seller Decision Maker Surname��Seller First Name��Seller ID Sub Type��Seller Surname��Seller Transmitter ID��Strike Price��Strike Price Currency��Strike Price Type��UV Index Classification��UV Instrument Classification��Underlying Index ID��Underlying Index Name��Underlying Index Term��Underlying Instrument ID��Up-Front Payment��Up-Front Payment Currency��Venue Transaction ID��Waiver Indicator�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK)��h�i8�����R�(K�<�NNNJ����J����K t�b�BH                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       �t�bh~�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK)��h!�]�(�NEWT�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�b�_dtype�h��StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�BJCHBAU24123013550000001��BJCHBAU24123034840000001��BJCHBAU24123034840000002��BJCHBAU24123034840000003��BJCHBAU24123034840000004��BJCHBAU24123034840000005��BJCHBAU24123034840000006��BJCHBAU24123034840000007��BJCHBAU24123034840000008��BJCHBAU24123034840000009��BJCHBAU24123034840000010��BJCHBAU24123034840000011��BJCHBAU24123034840000012��BJCHBAU24123034840000013��BJCHBAU24123034840000014��BJCHBAU24123034840000015��BJCHBAU24123034840000016��BJCHBAU24123034840000017��BJCHBAU24123034840000018��BJCHBAU24123034840000019��BJCHBAU24123034840000020��BJCHBAU24123034840000021��BJCHBAU24123034840000022��BJCHBAU24123034840000023��BJCHBAU24123034840000024��BJCHBAU24123053820000001��BJCHBAU24123053820000002��BJCHBAU24123053820000003��BJCHBAU24123053820000004��BJCHBAU24123053820000005��BJCHBAU24123053820000006��BJCHBAU24123053820000007��BJCHBAU24123053820000008��BJCHBAU24123053820000009��BJCHBAU24123053820000010��BJCHBAU24123053820000011��BJCHBAU24123053820000012��BJCHBAU24123053820000013��BJCHBAU24123053820000014��BJCHBAU24123053820000015��BJCHBAU24123053820000016�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�2138008NEORY2DQV4R24�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�2138008NEORY2DQV4R24�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�true�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�L��T�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�PNWU8O0BLT17BBV61Y18��03189544�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�pandas._libs.missing��NA���h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  �2138008NEORY2DQV4R24�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�03189544��PNWU8O0BLT17BBV61Y18�j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(h�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�2138008NEORY2DQV4R24�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�true�jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  jO  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�2024-03-27 09:49:19.220000��2024-03-27 13:31:01.950000��2024-03-27 13:31:05.770000��2024-03-27 13:31:05.730000��2024-03-27 13:30:47.010000��2024-03-27 13:30:14.200000��2024-03-27 13:30:14.250000��2024-03-27 13:30:13.710000��2024-03-27 13:30:13.760000��2024-03-27 13:30:14.400000��2024-03-27 13:30:14.590000��2024-03-27 13:30:00.700000��2024-03-27 13:30:00.000000��2024-03-27 13:30:00.040000��2024-03-27 13:30:00.350000��2024-03-27 13:30:00.380000��2024-03-27 13:30:00.460000��2024-03-27 13:30:00.310000��2024-03-27 13:30:00.450000��2024-03-27 13:30:00.470000��2024-03-27 13:30:00.290000��2024-03-27 13:30:00.430000��2024-03-27 13:30:00.810000��2024-03-27 13:30:00.850000��2024-03-27 13:30:00.330000��2024-03-27 13:30:00.680000��2024-03-27 13:30:00.780000��2024-03-27 13:30:00.800000��2024-03-27 13:30:00.870000��2024-03-27 13:30:00.070000��2024-03-27 13:30:00.100000�jj  jl  �2024-03-27 13:30:00.550000��2024-03-27 13:30:00.570000�jr  �2024-03-27 13:30:00.760000�jo  �2024-03-27 13:30:00.960000��2024-03-27 13:30:00.010000��2024-03-27 13:30:00.140000�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�AOTC�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubhhK ��h��R�(KKK)��h�f8�����R�(Kh�NNNJ����J����K t�b�BH       @�@      Y@      Y@      Y@      Y@      i@      i@      Y@     �R@      Y@      9@     �C@      .@      @      Y@      i@      i@      >@      $@      �?      Y@      Y@     �r@      Y@      i@      N@      �?     8�@      4@      @      @      @     �r@      @     �N@      @      ,@     @@     `f@     �r@     �o@�t�bh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�Unit�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubhhK ��h��R�(KKK)��j�  �BH  )�
�0�X@R���aB@��(\�bB@��(\�bB@�G�zdB@��Q�eB@��Q�eB@H�z�gB@H�z�gB@H�z�gB@H�z�gB@
ףp=jB@
ףp=jB@
ףp=jB@
ףp=jB@
ףp=jB@
ףp=jB@
ףp=jB@
ףp=jB@
ףp=jB@
ףp=jB@
ףp=jB@
ףp=jB@
ףp=jB@
ףp=jB@ףp=
W2@ףp=
W2@ףp=
W2@ףp=
W2@ףp=
W2@ףp=
W2@ףp=
W2@ףp=
W2@ףp=
W2@ףp=
W2@ףp=
W2@ףp=
W2@ףp=
W2@ףp=
W2@ףp=
W2@ףp=
W2@�t�bh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�MntryValAmt�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�USD�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�XOFF�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�FinInstrm.Id�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�IE00BCRY5Y77��US4642898831�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �US4270965084�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�HiShares IV PLC - iShares USD Short Duration Corp Bond UCITS ETF; Shs USD��-iShares Core Conservative Allocation ETF; Shs�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �$Hercules Capital Inc; Registered Shs�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�
Inv.Dec.ID�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�U00901�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�SELL�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�false�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(�03189544�j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubhhK ��h��R�(KKK)��j�  �BH        �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubhhK ��h��R�(KKK)��j�  �BH        �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubhhK ��h��R�(KKK)��j�  �BH        �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubhhK ��h��R�(KKK)��j�  �BH        �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK)��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h0at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hCat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hRat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hTat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hYat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hZat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h[at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h]at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h^at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h`at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�haat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hbat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hcat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hdat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�heat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hfat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hgat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hhat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hiat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hjat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hkat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hlat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hmat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hnat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hoat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hpat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hqat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hrat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hsat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�htat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�huat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hvat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hwat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hxat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hyat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hzat�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h{at�bh~Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h|at�bh~Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���K KK��R�u}�(j4  h�j5  j8  KKK��R�u}�(j4  h�j5  j8  KKK��R�u}�(j4  h�j5  j8  KKK��R�u}�(j4  h�j5  j8  KKK��R�u}�(j4  h�j5  j8  KKK��R�u}�(j4  j   j5  j8  KKK��R�u}�(j4  j  j5  j8  KKK��R�u}�(j4  j  j5  j8  KK	K��R�u}�(j4  j!  j5  j8  K	K
K��R�u}�(j4  j*  j5  j8  K
KK��R�u}�(j4  j5  j5  j8  KKK��R�u}�(j4  j>  j5  j8  KK
K��R�u}�(j4  jH  j5  j8  K
KK��R�u}�(j4  jR  j5  j8  KKK��R�u}�(j4  j�  j5  j8  KKK��R�u}�(j4  j�  j5  j8  KKK��R�u}�(j4  j�  j5  j8  KKK��R�u}�(j4  j�  j5  j8  KKK��R�u}�(j4  j�  j5  j8  KKK��R�u}�(j4  j�  j5  j8  KKK��R�u}�(j4  j�  j5  j8  KKK��R�u}�(j4  j�  j5  j8  KKK��R�u}�(j4  j�  j5  j8  KKK��R�u}�(j4  j�  j5  j8  KKK��R�u}�(j4  j�  j5  j8  KKK��R�u}�(j4  j�  j5  j8  KKK��R�u}�(j4  j�  j5  j8  KKK��R�u}�(j4  j   j5  j8  KKK��R�u}�(j4  j
  j5  j8  KKK��R�u}�(j4  j  j5  j8  KKK��R�u}�(j4  j  j5  j8  KK K��R�u}�(j4  j(  j5  j8  K K!K��R�u}�(j4  j1  j5  j8  K!K"K��R�u}�(j4  j:  j5  j8  K"K#K��R�u}�(j4  jC  j5  j8  K#K$K��R�u}�(j4  jL  j5  j8  K$K%K��R�u}�(j4  jU  j5  j8  K%K&K��R�u}�(j4  j^  j5  j8  K&K'K��R�u}�(j4  jg  j5  j8  K'K(K��R�u}�(j4  jp  j5  j8  K(K)K��R�u}�(j4  jy  j5  j8  K)K*K��R�u}�(j4  j�  j5  j8  K*K+K��R�u}�(j4  j�  j5  j8  K+K,K��R�u}�(j4  j�  j5  j8  K,K-K��R�u}�(j4  j�  j5  j8  K-K.K��R�u}�(j4  j�  j5  j8  K.K/K��R�u}�(j4  j�  j5  j8  K/K0K��R�u}�(j4  j�  j5  j8  K0K1K��R�u}�(j4  j�  j5  j8  K1K2K��R�u}�(j4  j�  j5  j8  K2K3K��R�u}�(j4  j�  j5  j8  K3K4K��R�u}�(j4  j�  j5  j8  K4K5K��R�u}�(j4  j�  j5  j8  K5K6K��R�u}�(j4  j�  j5  j8  K6K7K��R�u}�(j4  j�  j5  j8  K7K8K��R�u}�(j4  j  j5  j8  K8K9K��R�u}�(j4  j  j5  j8  K9K:K��R�u}�(j4  j  j5  j8  K:K;K��R�u}�(j4  j  j5  j8  K;K<K��R�u}�(j4  j!  j5  j8  K<K=K��R�u}�(j4  j*  j5  j8  K=K>K��R�u}�(j4  j3  j5  j8  K>K?K��R�u}�(j4  j>  j5  j8  K?K@K��R�u}�(j4  jB  j5  j8  K@KAK��R�u}�(j4  jK  j5  j8  KAKBK��R�u}�(j4  jT  j5  j8  KBKCK��R�u}�(j4  j]  j5  j8  KCKDK��R�u}�(j4  jf  j5  j8  KDKEK��R�u}�(j4  jo  j5  j8  KEKFK��R�u}�(j4  jx  j5  j8  KFKGK��R�u}�(j4  j�  j5  j8  KGKHK��R�u}�(j4  j�  j5  j8  KHKIK��R�u}�(j4  j�  j5  j8  KIKJK��R�u}�(j4  j�  j5  j8  KJKKK��R�u}�(j4  j�  j5  j8  KKKLK��R�u}�(j4  j�  j5  j8  KLKMK��R�u}�(j4  j�  j5  j8  KMKNK��R�u}�(j4  j�  j5  j8  KNKOK��R�u}�(j4  j�  j5  j8  KOKPK��R�u}�(j4  j�  j5  j8  KPKQK��R�u}�(j4  j�  j5  j8  KQKRK��R�u}�(j4  j�  j5  j8  KRKSK��R�u}�(j4  j�  j5  j8  KSKTK��R�u}�(j4  j�  j5  j8  KTKUK��R�u}�(j4  j�  j5  j8  KUKVK��R�u}�(j4  j  j5  j8  KVKWK��R�u}�(j4  j  j5  j8  KWKXK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.