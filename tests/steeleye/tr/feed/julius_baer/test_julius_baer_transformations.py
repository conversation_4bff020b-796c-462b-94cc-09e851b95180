import os
from pathlib import Path

import pandas as pd
import pytest
from prefect import context
from swarm.conf import SettingsCls
from swarm.task.auditor import Auditor

from swarm_tasks.steeleye.tr.transformations.rts22_transform_maps import (
    julius_baer_transform_map,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
TEST_FILE_PATH = TEST_FILES_DIR.joinpath(r"source_df.pkl")
EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(r"expected_result.pkl")


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestJuliusBaerTransformations:
    """
    Text JuliusBaerTransformations
    """

    @pytest.mark.parametrize(
        "test_file_path, expected_file_path",
        [
            (TEST_FILE_PATH, EXPECTED_FILE_PATH),
        ],
    )
    def test_end_to_end_transformations(
        self, mocker, test_file_path, expected_file_path, auditor
    ):
        os.environ["SWARM_FILE_URL"] = "dummy/test/path"

        # mock realm to chose default transformation
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "default.dev.steeleye.co"

        source_frame = pd.read_pickle(test_file_path)
        expected = pd.read_pickle(expected_file_path)

        task = julius_baer_transform_map.transformation(tenant="foo")(
            source_frame=source_frame, logger=context.get("logger"), auditor=auditor
        )
        result = task.process()

        pd.testing.assert_frame_equal(left=result, right=expected)
