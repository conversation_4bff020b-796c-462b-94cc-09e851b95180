import pandas as pd
import pytest
from prefect.engine.signals import SKIP

from swarm_tasks.steeleye.tr.feed.enfusion.v2.processor.transaction_record_transformer import (
    EnfusionTransactionFrameTransformer,
)
from swarm_tasks.steeleye.tr.feed.enfusion.v2.static import ExecutionSourceColumns


@pytest.fixture()
def sample_source_frame_list() -> dict:
    frame_dict = {
        "executions": pd.DataFrame(
            {
                ExecutionSourceColumns.ORDER_ID: [1, 2, 3],
                ExecutionSourceColumns.ORDER_DATE: [
                    "2022-04-13",
                    "2022-04-14",
                    "2022-04-15",
                ],
                "flag": ["A", "B", "C"],
            }
        ),
        "allocations": pd.DataFrame(
            {
                ExecutionSourceColumns.ORDER_ID: [1, 1, 2, 3],
                ExecutionSourceColumns.ORDER_DATE: [
                    "2022-04-13",
                    "2022-04-13",
                    "2022-04-14",
                    "2022-04-15",
                ],
                "flag": ["D", "D", "E", "F"],
            }
        ),
    }
    return frame_dict


@pytest.fixture()
def sample_s3_file_url_df() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "s3_executions_file_url": ["/dir/random_execution_name.csv"],
            "s3_allocations_file_url": ["/dir/random_allocation_name.csv"],
        }
    )


class TestEnfusionTransactionFrameTransformer:
    """
    Test Enfusion TR Record Merge Logic
    """

    def test_skip_scenario(self, sample_source_frame_list):
        pre_process_result = {"executions": sample_source_frame_list["executions"]}
        with pytest.raises(SKIP):
            task = EnfusionTransactionFrameTransformer(name="test_task")
            task.execute(pre_process_result=pre_process_result)

    def test_empty_data_scenario(
        self,
    ):
        with pytest.raises(SKIP):
            task = EnfusionTransactionFrameTransformer(name="test_task")
            task.execute(pre_process_result=dict())

    def test_valid_execution(
        self,
        sample_source_frame_list,
        sample_s3_file_url_df,
    ):
        task = EnfusionTransactionFrameTransformer(name="test_task")
        result = task.execute(
            pre_process_result=sample_source_frame_list,
            s3_file_url_df=sample_s3_file_url_df,
        )
        result_df = pd.read_csv(result.path).fillna(pd.NA)
        expected_df = pd.DataFrame(
            [
                {
                    "ORDERID": 1,
                    "ORDERDATE": "2022-04-13",
                    "FLAG": "A",
                    "ORDERDATE_allocations": "2022-04-13",
                    "FLAG_allocations": "D",
                    "__TRIGGER_FILE_NAME__": "/dir/random_execution_name.csv",
                },
                {
                    "ORDERID": 2,
                    "ORDERDATE": "2022-04-14",
                    "FLAG": "B",
                    "ORDERDATE_allocations": "2022-04-14",
                    "FLAG_allocations": "E",
                    "__TRIGGER_FILE_NAME__": "/dir/random_execution_name.csv",
                },
                {
                    "ORDERID": 3,
                    "ORDERDATE": "2022-04-15",
                    "FLAG": "C",
                    "ORDERDATE_allocations": "2022-04-15",
                    "FLAG_allocations": "F",
                    "__TRIGGER_FILE_NAME__": "/dir/random_execution_name.csv",
                },
            ]
        )

        assert not pd.testing.assert_frame_equal(
            result_df.sort_index(axis=1), expected_df.sort_index(axis=1)
        )
