import pandas as pd
import pytest

from swarm_tasks.steeleye.tr.data_source.schroders_brs import (
    map_swap_directionalities_schroders,
)


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    df = pd.DataFrame()
    return df


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "__uv_index_classification__": [
                pd.NA,
                ["SwapOutTest"],
                ["SwapOutTest", "SwapInTest"],
                ["OtherTest"],
                ["SwapOutTest", "SwapInTest", "OtherTest"],
            ],
            "__uv_instrument_classification__": [["SB"], ["SB"], ["SB"], pd.NA, pd.NA],
        }
    )
    return df


@pytest.fixture()
def missing_some_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "__uv_index_classification__": [
                pd.NA,
                ["SwapOutTest"],
                ["SwapOutTest", "SwapInTest"],
                ["OtherTest"],
                ["SwapOutTest", "SwapInTest", "OtherTest"],
            ]
        }
    )

    return df


class TestMapSwapDirectionalitiesSchroders:
    """
    Test cases for "TestMapSwapDirectionalitiesSchroders" class
    """

    def test_empty_input_df_without_source_columns(self, empty_source_df):

        task, params = self._init_task()
        result = task.execute(empty_source_df, params=params)
        assert result.empty

    def test_all_col_in_source_df(self, all_col_in_source_df: pd.DataFrame):

        expected_result = pd.DataFrame(
            {
                "transactionDetails.swapDirectionalities": [
                    ["Other"],
                    ["Swap Out"],
                    ["Swap Out", "Swap In"],
                    pd.NA,
                    pd.NA,
                ]
            }
        )
        task, params = self._init_task()
        result = task.execute(all_col_in_source_df, params=params)

        assert result.equals(expected_result)

    def test_missing_some_col_in_source_df(
        self, missing_some_col_in_source_df: pd.DataFrame
    ):
        expected_result = pd.DataFrame(
            {
                "transactionDetails.swapDirectionalities": [
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                ]
            }
        )
        task, params = self._init_task()
        result = task.execute(missing_some_col_in_source_df, params=params)
        assert result.equals(expected_result)

    def _init_task(self):

        params = map_swap_directionalities_schroders.Params(
            source_index_classification_attribute="__uv_index_classification__",
            source_instrument_classification_attribute="__uv_instrument_classification__",
            swapin_pattern="swapin",
            swapout_pattern="swapout",
            target_attribute="transactionDetails.swapDirectionalities",
        )
        task = map_swap_directionalities_schroders.MapSwapDirectionalitiesSchroders(
            name="map-swap-directionalities", params=params
        )
        return task, params
