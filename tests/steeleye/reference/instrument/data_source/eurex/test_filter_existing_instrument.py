import addict
import pandas as pd
import pytest
from prefect.engine.signals import SKIP
from swarm.conf import SettingsCls

from swarm_tasks.generic.instrument_fallback.static import InstrumentFields
from swarm_tasks.steeleye.reference.instrument.data_source.eurex.filter_existing_instrument import (
    FilterExistingInstruments,
)
from swarm_tasks.steeleye.reference.instrument.data_source.eurex.filter_existing_instrument import (
    Params,
)
from swarm_tasks.steeleye.reference.instrument.data_source.eurex.filter_existing_instrument import (
    Resources,
)


class TestFilterExistingInstruments(object):
    """
    Test cases for "TestPreProcessEurexInstrument" class
    """

    def test_empty_input_df_without_source_columns(self, empty_source_df: pd.DataFrame):
        resources = Resources(es_client_key="tenant-data")
        task = FilterExistingInstruments(name="test-pre-process-data")
        result = task.execute(empty_source_df, resources=resources)
        assert result.empty

    def test_all_col_in_source_df_and_no_existing_instruments(
        self,
        pre_processed_all_col_in_source_df: dict,
        expected_filtered_data_no_existing_instrument: dict,
        mocker,
    ):
        params = Params(currency="__major_currency__")
        task, resource = self._init_task(mocker, params)
        resources = Resources(es_client_key="tenant-data")
        source_df = self.get_pre_processed_source_dict(
            pre_processed_all_col_in_source_df
        )
        mock_es_scroll = mocker.patch.object(FilterExistingInstruments, "es_scroll")
        mock_es_scroll.return_value = pd.DataFrame()
        result = task.execute(source_df, resources=resources, params=params)
        assert len(result) == 8
        assert (
            result.fillna(0).to_dict() == expected_filtered_data_no_existing_instrument
        )

    def test_all_col_in_source_df_and_some_existing_instruments(
        self,
        pre_processed_all_col_in_source_df: pd.DataFrame,
        expected_result_all_col_in_source_df_and_some_existing_instruments,
        mocker,
    ):
        params = Params(currency="__major_currency__")
        task, resource = self._init_task(mocker, params)
        resources = Resources(es_client_key="tenant-data")
        source_df = self.get_pre_processed_source_dict(
            pre_processed_all_col_in_source_df
        )
        mock_es_scroll = mocker.patch.object(FilterExistingInstruments, "es_scroll")
        existing_instrumetns = [
            "XEURDE000C5RQD52EUR",
            "XEURDE000C52GU94EUR",
            "XEURDE000C58EEB1USD",
            "XEURDE000C58Q2M1EUR",
        ]
        mock_es_scroll.return_value = pd.DataFrame(
            {InstrumentFields.EXT_INSTRUMENT_UNIQUE_ID: existing_instrumetns}
        )
        result = task.execute(source_df, resources=resources, params=params)
        assert len(result) == 4
        assert (
            result.fillna(0).to_dict()
            == expected_result_all_col_in_source_df_and_some_existing_instruments
        )

    def test_all_col_in_source_df_and_all_existing_instruments(
        self,
        pre_processed_all_col_in_source_df: pd.DataFrame,
        expected_result_all_col_in_source_df_and_some_existing_instruments,
        mocker,
    ):
        params = Params(currency="__major_currency__")
        task, resource = self._init_task(mocker, params)
        resources = Resources(es_client_key="tenant-data")
        source_df = self.get_pre_processed_source_dict(
            pre_processed_all_col_in_source_df
        )
        mock_es_scroll = mocker.patch.object(FilterExistingInstruments, "es_scroll")
        existing_instrumetns = [
            "XEURDE000C5RQD52EUR",
            "XEURDE000C52GU94EUR",
            "XEURDE000C58EEB1USD",
            "XEURDE000C58Q2M1EUR",
            "XEURDE000C55BF20EUR",
            "XEURDE000C55BF38EUR",
            "XEURDE000C58WDC3GBP",
            "XEURDE000C58WDD1USD",
        ]
        mock_es_scroll.return_value = pd.DataFrame(
            {InstrumentFields.EXT_INSTRUMENT_UNIQUE_ID: existing_instrumetns}
        )
        with pytest.raises(SKIP) as _:
            task.execute(source_df, resources=resources, params=params)

    def test_all_col_in_source_df_no_unique_instrument_id(
        self, pre_processed_all_col_in_source_df
    ):
        params = Params(currency="__major_currency__")
        task = FilterExistingInstruments(name="test-pre-process-data", params=params)
        resources = Resources(es_client_key="tenant-data")
        source_df = self.get_pre_processed_source_dict(
            pre_processed_all_col_in_source_df
        )
        source_df.loc[:, InstrumentFields.EXT_INSTRUMENT_UNIQUE_ID] = pd.NA
        with pytest.raises(SKIP) as _:
            task._filter_data(data=source_df, resources=resources)

    def get_pre_processed_source_dict(self, pre_processed_all_col_in_source_df):
        source_df = pd.DataFrame(pre_processed_all_col_in_source_df)
        source_df["__currency__"] = [
            "EUR",
            "EUR",
            "USc",
            "EUR",
            "EUR",
            "CNH",
            "EUR",
            "EUR",
            "EUR",
            "GBX",
            "USD",
        ]
        source_df["__major_currency__"] = [
            "EUR",
            "EUR",
            "USD",
            "EUR",
            "EUR",
            "CNY",
            "EUR",
            "EUR",
            "EUR",
            "GBP",
            "USD",
        ]
        return source_df

    @staticmethod
    def _init_task(mocker, params):
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "tenant-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    },
                    "MAX_TERMS_SIZE": 1024,
                }
            }
        )

        mocker.patch.object(
            SettingsCls, "tenant", new_callable=mocker.PropertyMock, return_value="test"
        )

        resources = addict.Dict({"es_client_key": "tenant-data"})

        task = FilterExistingInstruments(name="LinkParticipants", params=params)

        return task, resources
