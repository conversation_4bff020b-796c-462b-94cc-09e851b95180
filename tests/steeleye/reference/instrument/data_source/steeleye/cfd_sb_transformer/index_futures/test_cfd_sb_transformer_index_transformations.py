from pathlib import Path

import pandas as pd
import pytest
from addict import addict
from swarm.conf import SettingsCls
from swarm.task.auditor import Auditor

from swarm_tasks.primary_transformations.get_primary_transformations import (
    GetPrimaryTransformations,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
TEST_FILE_PATH = TEST_FILES_DIR.joinpath(r"input_instrument.pkl")
EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(r"expected_instrument.pkl")


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestCFDSBTransformerCommodityTransformations:
    def test_end_to_end(self, mocker, auditor):
        SettingsCls.STACK = "srp"

        # mock bundle to chose correct transformation map
        mock_realm = mocker.patch.object(
            SettingsCls, "bundle", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "reference-instrument-se-cfdsb-index-transformer"

        # mock realm to chose default transformation
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "default.dev.steeleye.co"

        # mock reference-data
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "reference-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    }
                }
            }
        )

        source_frame = pd.read_pickle(TEST_FILE_PATH)
        task = GetPrimaryTransformations(name="GetPrimaryTransformations")
        result = task.execute(source_frame=source_frame)
        expected = pd.read_pickle(EXPECTED_FILE_PATH)

        assert not pd.testing.assert_frame_equal(result, expected)
