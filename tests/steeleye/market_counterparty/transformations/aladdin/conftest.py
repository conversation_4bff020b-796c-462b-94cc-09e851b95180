import pandas as pd
import pytest


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    data = [
        {
            "issuerLongName": "Sparkasse",
            "allCptyIds": ["2423", "2424"],
            "cptyPrefixIds": ["id:2423", "id:2424"],
        },
        {
            "issuerLongName": "Commerzbank",
            "allCptyIds": ["1321", "8392", "7492"],
            "cptyPrefixIds": ["id:1321", "id:8392", "id:7492"],
        },
        {
            "issuerLongName": "Deutsche Bank",
            "allCptyIds": ["9110"],
            "cptyPrefixIds": ["id:9110"],
        },
        {
            "issuerLongName": "Volksbank",
            "allCptyIds": ["8748", "1111"],
            "cptyPrefixIds": ["id:8748", "id:1111"],
        },
        {
            "issuerLongName": "Postbank",
            "allCptyIds": ["2987", "3302"],
            "cptyPrefixIds": ["id:2987", "id:3302"],
        },
    ]

    return pd.DataFrame(data)


@pytest.fixture()
def expected_result():
    return pd.DataFrame(
        [
            {
                "name": "Sparkasse",
                "__meta_model__": "MarketCounterparty",
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"label": "id", "id": "2423"},
                    {"label": "id", "id": "2424"},
                ],
                "sourceIndex": 0,
                "sourceKey": "s3://test.steeleye.co/flows/mm/123.csv",
                "uniqueIds": ["id:2423", "id:2424"],
            },
            {
                "name": "Commerzbank",
                "__meta_model__": "MarketCounterparty",
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"label": "id", "id": "1321"},
                    {"label": "id", "id": "8392"},
                    {"label": "id", "id": "7492"},
                ],
                "sourceIndex": 1,
                "sourceKey": "s3://test.steeleye.co/flows/mm/123.csv",
                "uniqueIds": ["id:1321", "id:8392", "id:7492"],
            },
            {
                "name": "Deutsche Bank",
                "__meta_model__": "MarketCounterparty",
                "sinkIdentifiers.tradeFileIdentifiers": [{"label": "id", "id": "9110"}],
                "sourceIndex": 2,
                "sourceKey": "s3://test.steeleye.co/flows/mm/123.csv",
                "uniqueIds": ["id:9110"],
            },
            {
                "name": "Volksbank",
                "__meta_model__": "MarketCounterparty",
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"label": "id", "id": "8748"},
                    {"label": "id", "id": "1111"},
                ],
                "sourceIndex": 3,
                "sourceKey": "s3://test.steeleye.co/flows/mm/123.csv",
                "uniqueIds": ["id:8748", "id:1111"],
            },
            {
                "name": "Postbank",
                "__meta_model__": "MarketCounterparty",
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"label": "id", "id": "2987"},
                    {"label": "id", "id": "3302"},
                ],
                "sourceIndex": 4,
                "sourceKey": "s3://test.steeleye.co/flows/mm/123.csv",
                "uniqueIds": ["id:2987", "id:3302"],
            },
        ]
    )
