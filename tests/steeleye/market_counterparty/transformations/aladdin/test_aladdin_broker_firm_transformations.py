import logging
import os

import pandas as pd

from swarm_tasks.steeleye.mymarket.transformations.market_transform_maps import (
    aladdin_broker_transform_map,
)

logger = logging.getLogger(__name__)


class TestAladdinBrokerFirmTransformations:

    """
    Integration Test Suite for AladdinBrokerFirmTransformations
    """

    def test_end_to_end_transformations(
        self,
        source_frame,
        expected_result,
    ):
        """Runs an end-to-end test for Kyte MarketCounterparty using the logic to update records."""

        os.environ["SWARM_FILE_URL"] = "s3://test.steeleye.co/flows/mm/123.csv"
        task = aladdin_broker_transform_map.transformation(tenant="test")(
            source_frame=source_frame, logger=logger, auditor=None
        )
        result = task.process()

        pd.testing.assert_frame_equal(left=result, right=expected_result)
