import pandas as pd
import pytest
from swarm.task.auditor import Auditor


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    data = [
        {
            "crmClientId": "Mercury# 174",
            "name": "<PERSON><PERSON><PERSON>",
            "lei": "549300TK038P6EV4YU51",
            "status": "Closed",
            "firmwideClientId": "FWC-DXB-10000",
            "region": "DXB",
            "mifidFirm": False,
            "countryCode": "LT",
            "isEu": True,
            "groupwideClientId": "GWC-3850",
            "&id": "dummy",
            "sinkIdentifiers.tradeFileIdentifiers": [
                {"id": "549300TK038P6EV4YU51", "label": "lei"},
                {"id": "FWC-DXB-10000", "label": "id"},
                {"id": "FWC-DXB-10000", "label": "account"},
                {"id": "GWC-3850", "label": "id"},
                {"id": "GWC-3850", "label": "account"},
            ],
        }
    ]
    df = pd.DataFrame(data)
    return df


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


@pytest.fixture()
def expected_result():
    return pd.DataFrame(
        [
            {
                "__lei__": "549300TK038P6EV4YU51",
                "__trade_file_identifiers_id__firm_wide_client_id__": "FWC-DXB-10000",
                "__trade_file_identifiers_id__group_wide_client_id__": "GWC-3850",
                "details.clientMandate": "DISCRETIONARY",
                "details.firmType": "Client",
                "details.inEEA": True,
                "details.mifidRegistered": False,
                "details.retailOrProfessional": "PROFESSIONAL",
                "firmIdentifiers.branchCountry": "LT",
                "name": "Siauliu Bankas",
                "__meta_model__": "MarketCounterparty",
                "__to_update__": False,
                "es_id": pd.NA,
                "es_trade_file_identifiers": pd.NA,
                "es_trade_ids": pd.NA,
            }
        ]
    )


@pytest.fixture()
def expected_result_update():
    return pd.DataFrame(
        [
            {
                "__lei__": "549300TK038P6EV4YU51",
                "__trade_file_identifiers_id__firm_wide_client_id__": "FWC-DXB-10000",
                "__trade_file_identifiers_id__group_wide_client_id__": "GWC-3850",
                "details.clientMandate": "DISCRETIONARY",
                "details.firmType": "Client",
                "details.inEEA": True,
                "details.mifidRegistered": False,
                "details.retailOrProfessional": "PROFESSIONAL",
                "firmIdentifiers.branchCountry": "LT",
                "name": "Siauliu Bankas",
                "__meta_model__": "MarketCounterparty",
                "__to_update__": True,
                "es_id": "dummy",
                "es_trade_file_identifiers": [
                    {"id": "549300TK038P6EV4YU51", "label": "lei"},
                    {"id": "FWC-DXB-10000", "label": "id"},
                    {"id": "FWC-DXB-10000", "label": "account"},
                    {"id": "GWC-3850", "label": "id"},
                    {"id": "GWC-3850", "label": "account"},
                ],
                "es_trade_ids": [
                    "lei:549300TK038P6EV4YU51",
                    "id:FWC-DXB-10000",
                    "account:FWC-DXB-10000",
                    "id:GWC-3850",
                    "account:GWC-3850",
                ],
            }
        ]
    )
