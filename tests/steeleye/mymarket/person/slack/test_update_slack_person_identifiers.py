from unittest.mock import MagicMock

import pandas as pd
import pytest
from addict import addict
from prefect.engine import signals
from se_core_tasks.mymarket.static import PersonColumns
from swarm.conf import SettingsCls

from swarm_tasks.steeleye.mymarket.person.slack import update_slack_person_identifiers
from swarm_tasks.steeleye.mymarket.person.slack.update_slack_person_identifiers import (
    Params,
)
from swarm_tasks.steeleye.mymarket.person.slack.update_slack_person_identifiers import (
    UNIQUE_PROPS,
)
from swarm_tasks.steeleye.mymarket.person.slack.update_slack_person_identifiers import (
    UpdateSlackPersonIdentifiers,
)


@pytest.fixture()
def params_fixture() -> Params:
    return Params(
        **{"source_email_column": "email", "source_slack_id_column": "slack_id"}
    )


@pytest.fixture()
def source_frame_required_columns_absent() -> pd.DataFrame:
    return pd.DataFrame({"a": [1, 2]})


@pytest.fixture()
def valid_source_frame() -> pd.DataFrame:
    return pd.DataFrame(
        [
            # Record 1: email id matches with <PERSON><PERSON>, slack id exists as live chat in Elastic imAccounts
            {"slack_id": "u01bfe9nz6h", "email": "<EMAIL>"},
            # Record 2: email id matches with Elastic, slack id exists as Slack id in Elastic imAccounts
            {"slack_id": "u03pgflh47q", "email": "<EMAIL>"},
            # Record 3: email id matches with Elastic, slack id does not exist in Elastic imAccounts,
            # but at least one other imAccount exists
            {"slack_id": "u05pgfmi76f", "email": "<EMAIL>"},
            # Record 4: Email id matches with Elastic, slack id does not exist in Elastic, the imAccounts
            # field itself doesn't exist in Elastic
            {"slack_id": "u07pgf31d6g", "email": "<EMAIL>"},
            # Record 5: email id does not match with Elastic, nothing updated
            {"slack_id": "u09gff315hi", "email": "<EMAIL>"},
        ]
    )


@pytest.fixture()
def mock_elastic_results() -> pd.DataFrame:
    """Mock df for results fetched from Elastic"""

    return pd.DataFrame(
        [
            # Record 1: Same ID (u01bfe9nz6h) exists in Elastic for Live Chat, but not for Slack.
            {
                "sourceKey": "s3://test.dev.steeleye.co/flows/mymarket-universal-steeleye-person/test.csv",
                "&id": "a0c6b39e-3503-4133-b04e-d989819b2389",
                "&uniqueProps": ["<EMAIL>", "u01bfe9nz6h"],
                "retailOrProfessional": "PROFESSIONAL",
                "&key": "AccountPerson:a0c6b39e-3503-4133-b04e-d989819b2389:*************",
                "sourceIndex": "5",
                "uniqueIds": ["<EMAIL>", "u01bfe9nz6h"],
                "&model": "AccountPerson",
                "&version": 1,
                "name": "John Doe",
                "&hash": "e74d5038f883d50d9b8700b60b4f99bfb06b872ddd67182baaaa41952ee9bc9f",
                "&timestamp": *************,
                "&user": "system",
                "communications.emails": ["<EMAIL>"],
                "communications.imAccounts": [
                    {"id": "u01bfe9nz6h", "label": "Live Chat"},
                ],
                "personalDetails.firstName": "John",
                "personalDetails.lastName": "Doe",
            },
            # Record 2: Same id (u03pgflh47q) exists in Elastic for Slack, NOT added
            {
                "sourceKey": "s3://test.dev.steeleye.co/flows/mymarket-universal-steeleye-person/test.csv",
                "&id": "a8de180a-e9c6-4213-a821-e1ac1bf6116d",
                "&uniqueProps": ["<EMAIL>", "u03pgflh47q"],
                "retailOrProfessional": "PROFESSIONAL",
                "&key": "AccountPerson:a8de180a-e9c6-4213-a821-e1ac1bf6116d:*************",
                "sourceIndex": "6",
                "uniqueIds": ["<EMAIL>", "u03pgflh47q"],
                "&model": "AccountPerson",
                "&version": 1,
                "name": "Jane Doe",
                "&hash": "1fed8e83b7cccc17f093bf5e7cc862d55b954a7c6e91049ab74a2e75150c291d",
                "&timestamp": *************,
                "&user": "system",
                "communications.emails": ["<EMAIL>"],
                "communications.imAccounts": [
                    {"id": "u03pgflh47q", "label": "Slack"},
                ],
                "personalDetails.firstName": "Jane",
                "personalDetails.lastName": "Doe",
            },
            # Record 3: ID (u05pgfmi76f) does not exist for Slack or any other IM Account: added
            {
                "sourceKey": "s3://test.dev.steeleye.co/flows/mymarket-universal-steeleye-person/test.csv",
                "&id": "2e92cf20-63ba-45eb-821f-04a81fb47311",
                "&uniqueProps": [
                    "<EMAIL>",
                ],
                "retailOrProfessional": "PROFESSIONAL",
                "&key": "AccountPerson:2e92cf20-63ba-45eb-821f-04a81fb47311:*************",
                "sourceIndex": "7",
                "uniqueIds": [
                    "<EMAIL>",
                ],
                "&model": "AccountPerson",
                "&version": 1,
                "name": "Jim Doe",
                "&hash": "f835b0a7b82dae25d6f9cba4e3b9ef4532b444a2f435e812cb3be95ef3b24c54",
                "&timestamp": *************,
                "&user": "system",
                "communications.emails": ["<EMAIL>"],
                "communications.imAccounts": [
                    {"id": "<EMAIL>", "label": "Live Chat"},
                ],
                "personalDetails.firstName": "Jim",
                "personalDetails.lastName": "Doe",
            },
            # Record 4: ID (u07pgf31d6g) does not exist for Slack or any other IM Account: added
            {
                "sourceKey": "s3://test.dev.steeleye.co/flows/mymarket-universal-steeleye-person/test.csv",
                "&id": "2e92cf20-63ba-45eb-821f-04a81fb47311",
                "&uniqueProps": [
                    "<EMAIL>",
                ],
                "retailOrProfessional": "PROFESSIONAL",
                "&key": "AccountPerson:2e92cf20-63ba-45eb-821f-04a81fb47311:*************",
                "sourceIndex": "7",
                "uniqueIds": [
                    "<EMAIL>",
                ],
                "&model": "AccountPerson",
                "&version": 1,
                "name": "Jack Doe",
                "&hash": "f835b0a7b82dae25d6f9cba4e3b9ef4532b444a2f435e812cb3be95ef3b24c54",
                "&timestamp": *************,
                "&user": "system",
                "communications.emails": ["<EMAIL>"],
                "personalDetails.firstName": "Jack",
                "personalDetails.lastName": "Doe",
            },
        ]
    )


@pytest.fixture()
def mock_single_elastic_result_with_slack_id_present() -> pd.DataFrame:
    """Mock df for a single record fetched from Elastic. This record already has the
    required Slack ID in communications.imAccounts with the right label 'Slack'"""

    return pd.DataFrame(
        [
            # Record 2: Same id (u03pgflh47q) exists in Elastic for Slack, NOT added
            {
                "sourceKey": "s3://test.dev.steeleye.co/flows/mymarket-universal-steeleye-person/test.csv",
                "&id": "a8de180a-e9c6-4213-a821-e1ac1bf6116d",
                "&uniqueProps": ["<EMAIL>", "u03pgflh47q"],
                "retailOrProfessional": "PROFESSIONAL",
                "&key": "AccountPerson:a8de180a-e9c6-4213-a821-e1ac1bf6116d:*************",
                "sourceIndex": "6",
                "uniqueIds": ["<EMAIL>", "u03pgflh47q"],
                "&model": "AccountPerson",
                "&version": 1,
                "name": "Jane Doe",
                "&hash": "1fed8e83b7cccc17f093bf5e7cc862d55b954a7c6e91049ab74a2e75150c291d",
                "&timestamp": *************,
                "&user": "system",
                "communications.emails": ["<EMAIL>"],
                "communications.imAccounts": [
                    {"id": "u03pgflh47q", "label": "Slack"},
                ],
                "personalDetails.firstName": "Jane",
                "personalDetails.lastName": "Doe",
            }
        ]
    )


@pytest.fixture()
def expected_result_multiple_elements_in_elastic():
    """Expected result when there are multiple matches in Elastic"""
    return pd.DataFrame(
        [
            {
                # Elastic record with new Slack id in communications.imAccounts. uniqueProps and uniqueIds
                # remain the same as the Slack ID already exists as a Live chat id
                "sourceKey": "s3://test.dev.steeleye.co/flows/mymarket-universal-steeleye-person/test.csv",
                "&id": "a0c6b39e-3503-4133-b04e-d989819b2389",
                "&uniqueProps": ["u01bfe9nz6h", "<EMAIL>"],
                "retailOrProfessional": "PROFESSIONAL",
                "&key": "AccountPerson:a0c6b39e-3503-4133-b04e-d989819b2389:*************",
                "sourceIndex": "5",
                "uniqueIds": ["u01bfe9nz6h", "<EMAIL>"],
                "&model": "AccountPerson",
                "&version": 1,
                "name": "John Doe",
                "&hash": "e74d5038f883d50d9b8700b60b4f99bfb06b872ddd67182baaaa41952ee9bc9f",
                "&timestamp": *************,
                "&user": "system",
                "communications.emails": ["<EMAIL>"],
                "communications.imAccounts": [
                    {"id": "u01bfe9nz6h", "label": "Live Chat"},
                    {"id": "u01bfe9nz6h", "label": "Slack"},
                ],
                "personalDetails.firstName": "John",
                "personalDetails.lastName": "Doe",
            },
            # Elastic record with new Slack ID added in communications.imAccounts. The slack id is also
            # added to uniqueProps and uniqueIds.
            {
                "sourceKey": "s3://test.dev.steeleye.co/flows/mymarket-universal-steeleye-person/test.csv",
                "&id": "2e92cf20-63ba-45eb-821f-04a81fb47311",
                "&uniqueProps": ["<EMAIL>", "u05pgfmi76f"],
                "retailOrProfessional": "PROFESSIONAL",
                "&key": "AccountPerson:2e92cf20-63ba-45eb-821f-04a81fb47311:*************",
                "sourceIndex": "7",
                "uniqueIds": ["<EMAIL>", "u05pgfmi76f"],
                "&model": "AccountPerson",
                "&version": 1,
                "name": "Jim Doe",
                "&hash": "f835b0a7b82dae25d6f9cba4e3b9ef4532b444a2f435e812cb3be95ef3b24c54",
                "&timestamp": *************,
                "&user": "system",
                "communications.emails": ["<EMAIL>"],
                "communications.imAccounts": [
                    {"id": "<EMAIL>", "label": "Live Chat"},
                    {"id": "u05pgfmi76f", "label": "Slack"},
                ],
                "personalDetails.firstName": "Jim",
                "personalDetails.lastName": "Doe",
            },
            # Elastic record with new Slack ID added in communications.imAccounts, which was previously
            # not present. The slack id is also added to uniqueProps and uniqueIds.
            {
                "sourceKey": "s3://test.dev.steeleye.co/flows/mymarket-universal-steeleye-person/test.csv",
                "&id": "2e92cf20-63ba-45eb-821f-04a81fb47311",
                "&uniqueProps": ["u07pgf31d6g", "<EMAIL>"],
                "retailOrProfessional": "PROFESSIONAL",
                "&key": "AccountPerson:2e92cf20-63ba-45eb-821f-04a81fb47311:*************",
                "sourceIndex": "7",
                "uniqueIds": ["u07pgf31d6g", "<EMAIL>"],
                "&model": "AccountPerson",
                "&version": 1,
                "name": "Jack Doe",
                "&hash": "f835b0a7b82dae25d6f9cba4e3b9ef4532b444a2f435e812cb3be95ef3b24c54",
                "&timestamp": *************,
                "&user": "system",
                "communications.emails": ["<EMAIL>"],
                "communications.imAccounts": [{"id": "u07pgf31d6g", "label": "Slack"}],
                "personalDetails.firstName": "Jack",
                "personalDetails.lastName": "Doe",
            },
        ]
    )


@pytest.fixture()
def source_frame_to_test_for_unique_props_emails_missing_values():
    """Source frame with records for which the elastic matches have uniqueProps incorrectly
    populated (missing emails) and the other way around"""
    return pd.DataFrame(
        [
            # Record 1: email id matches with uniqueProps in Elastic, but not communications.emails
            # The email is uppercase and should match with lowercase emails in elastic
            {"slack_id": "u01bfe9nz6h", "email": "<EMAIL>"},
            # Record 2: email id matches with communications.emails in Elastic, but not communications.emails
            {"slack_id": "u03pgflh47q", "email": "<EMAIL>"},
        ]
    )


@pytest.fixture()
def mock_elastic_results_uniqueprops_emails_mismatch() -> pd.DataFrame:
    """Mock df for results fetched from Elastic where the uniqueProps isn't updated with
    the latest set of emails OR the other way around"""

    return pd.DataFrame(
        [
            # Record 1: Email exists in Elastic in uniqueProps, but not communications.emails. Slack ID does not exist.
            # communications.emails is null
            {
                "sourceKey": "s3://test.dev.steeleye.co/flows/mymarket-universal-steeleye-person/test.csv",
                "&id": "a0c6b39e-3503-4133-b04e-d989819b2389",
                "&uniqueProps": ["<EMAIL>"],
                "retailOrProfessional": "PROFESSIONAL",
                "&key": "AccountPerson:a0c6b39e-3503-4133-b04e-d989819b2389:*************",
                "sourceIndex": "5",
                "uniqueIds": ["<EMAIL>"],
                "&model": "AccountPerson",
                "&version": 1,
                "name": "John Doe",
                "&hash": "e74d5038f883d50d9b8700b60b4f99bfb06b872ddd67182baaaa41952ee9bc9f",
                "&timestamp": *************,
                "&user": "system",
                "personalDetails.firstName": "John",
                "personalDetails.lastName": "Doe",
            },
            # Record 2: Email exists in Elastic in communications.emails, but not uniqueProps. Slack ID does not exist.
            # uniqueProps and uniqueIds are null
            {
                "sourceKey": "s3://test.dev.steeleye.co/flows/mymarket-universal-steeleye-person/test.csv",
                "&id": "a8de180a-e9c6-4213-a821-e1ac1bf6116d",
                "retailOrProfessional": "PROFESSIONAL",
                "&key": "AccountPerson:a8de180a-e9c6-4213-a821-e1ac1bf6116d:*************",
                "sourceIndex": "6",
                "&model": "AccountPerson",
                "&version": 1,
                "name": "Jane Doe",
                "&hash": "1fed8e83b7cccc17f093bf5e7cc862d55b954a7c6e91049ab74a2e75150c291d",
                "&timestamp": *************,
                "&user": "system",
                "communications.emails": ["<EMAIL>"],
                "personalDetails.firstName": "Jane",
                "personalDetails.lastName": "Doe",
            },
        ]
    )


@pytest.fixture()
def expected_result_elastic_unique_props_emails_mismatch():
    """Expected result when the results from Elastic do not have the same values in uniqueProps
    and communications.emails"""
    return pd.DataFrame(
        [
            {
                "sourceKey": "s3://test.dev.steeleye.co/flows/mymarket-universal-steeleye-person/test.csv",
                "&id": "a0c6b39e-3503-4133-b04e-d989819b2389",
                "&uniqueProps": ["<EMAIL>", "u01bfe9nz6h"],
                "retailOrProfessional": "PROFESSIONAL",
                "&key": "AccountPerson:a0c6b39e-3503-4133-b04e-d989819b2389:*************",
                "sourceIndex": "5",
                "uniqueIds": ["<EMAIL>", "u01bfe9nz6h"],
                "&model": "AccountPerson",
                "&version": 1,
                "name": "John Doe",
                "&hash": "e74d5038f883d50d9b8700b60b4f99bfb06b872ddd67182baaaa41952ee9bc9f",
                "&timestamp": *************,
                "&user": "system",
                "personalDetails.firstName": "John",
                "personalDetails.lastName": "Doe",
                "communications.emails": pd.NA,
                "communications.imAccounts": [{"id": "u01bfe9nz6h", "label": "Slack"}],
            },
            {
                "sourceKey": "s3://test.dev.steeleye.co/flows/mymarket-universal-steeleye-person/test.csv",
                "&id": "a8de180a-e9c6-4213-a821-e1ac1bf6116d",
                "&uniqueProps": ["u03pgflh47q"],
                "retailOrProfessional": "PROFESSIONAL",
                "&key": "AccountPerson:a8de180a-e9c6-4213-a821-e1ac1bf6116d:*************",
                "sourceIndex": "6",
                "uniqueIds": ["u03pgflh47q"],
                "&model": "AccountPerson",
                "&version": 1,
                "name": "Jane Doe",
                "&hash": "1fed8e83b7cccc17f093bf5e7cc862d55b954a7c6e91049ab74a2e75150c291d",
                "&timestamp": *************,
                "&user": "system",
                "personalDetails.firstName": "Jane",
                "personalDetails.lastName": "Doe",
                "communications.emails": ["<EMAIL>"],
                "communications.imAccounts": [{"id": "u03pgflh47q", "label": "Slack"}],
            },
        ]
    )


class TestUpdateSlackPersonIdentifiers:
    """Test suite for UpdateSlackPersonIdentifiers"""

    def test_empty_source(self, mocker: MagicMock, params_fixture: Params):
        """
        Test for the case where the source frame is empty
        """

        with pytest.raises(signals.FAIL):
            self._run_task(
                source_frame=pd.DataFrame(), params=params_fixture, mocker=mocker
            )

    def test_required_source_columns_not_present(
        self,
        mocker: MagicMock,
        source_frame_required_columns_absent: pd.DataFrame,
        params_fixture: Params,
    ):
        """
        Test for the case where the required source columns are not present in the source frame
        """

        with pytest.raises(signals.FAIL):
            self._run_task(
                source_frame=source_frame_required_columns_absent,
                params=params_fixture,
                mocker=mocker,
            )

    def test_multiple_results_in_elastic(
        self,
        mocker: MagicMock,
        valid_source_frame: pd.DataFrame,
        mock_elastic_results: pd.DataFrame,
        expected_result_multiple_elements_in_elastic: pd.DataFrame,
        params_fixture: Params,
    ):
        """
        Test for the case where multiple records are fetched from Elastic. See comments for each
        record in the corresponding fixtures for a detailed explanation of the cases tested.
        :return:
        """
        result = self._run_task(
            source_frame=valid_source_frame,
            params=params_fixture,
            mocker=mocker,
            elastic_df=mock_elastic_results,
        )
        # Sort uniqueProps and uniqueIds
        for col in {UNIQUE_PROPS, PersonColumns.UNIQUE_IDS}:
            result[col] = result[col].apply(sorted)
            expected_result_multiple_elements_in_elastic[
                col
            ] = expected_result_multiple_elements_in_elastic[col].apply(sorted)
        pd.testing.assert_frame_equal(
            left=result, right=expected_result_multiple_elements_in_elastic
        )

    def test_single_record_from_elastic_with_slack_id_raises_skip(
        self,
        mocker: MagicMock,
        valid_source_frame: pd.DataFrame,
        mock_single_elastic_result_with_slack_id_present: pd.DataFrame,
        params_fixture: Params,
    ):
        """
        Test for the case where 1 records is fetched from Elastic, but this record already has
        the slack ID populated in communications.imAccounts with the label 'Slack'. A SKIP
        is raised in this case.
        """
        with pytest.raises(signals.SKIP) as e:
            self._run_task(
                source_frame=valid_source_frame,
                params=params_fixture,
                mocker=mocker,
                elastic_df=mock_single_elastic_result_with_slack_id_present,
            )

        assert e.match(
            "All records dropped as they already have Slack IDs. Nothing to update"
        )

    def test_no_results_in_elastic_raises_skip(
        self,
        mocker: MagicMock,
        valid_source_frame: pd.DataFrame,
        params_fixture: Params,
    ):
        """
        Test for the case where no results are fetched from Elastic. A SKIP is raised in this case
        """
        with pytest.raises(signals.SKIP) as e:
            self._run_task(
                source_frame=valid_source_frame, params=params_fixture, mocker=mocker
            )

        assert e.match(
            "No records found with the given email addresses. No further processing required"
        )

    def test_unique_props_emails_mismtach_in_elastic(
        self,
        mocker: MagicMock,
        source_frame_to_test_for_unique_props_emails_missing_values: pd.DataFrame,
        mock_elastic_results_uniqueprops_emails_mismatch: pd.DataFrame,
        params_fixture: Params,
        expected_result_elastic_unique_props_emails_mismatch: pd.DataFrame,
    ):
        """
        Test for the cases where the uniqueProps and communications.emails in the fetched Elastic records do not
        match.
        This is an unlikely scenario, but is handled nevertheless.
        The source emails should match with whichever one of them is present in this case.
        """
        result = self._run_task(
            source_frame=source_frame_to_test_for_unique_props_emails_missing_values,
            params=params_fixture,
            mocker=mocker,
            elastic_df=mock_elastic_results_uniqueprops_emails_mismatch,
        )
        # Sort uniqueProps and uniqueIds
        for col in {UNIQUE_PROPS, PersonColumns.UNIQUE_IDS}:
            result[col] = result[col].apply(sorted)
            expected_result_elastic_unique_props_emails_mismatch[
                col
            ] = expected_result_elastic_unique_props_emails_mismatch[col].apply(sorted)
        pd.testing.assert_frame_equal(
            left=result, right=expected_result_elastic_unique_props_emails_mismatch
        )

    @staticmethod
    def _run_task(
        source_frame: pd.DataFrame,
        params: Params,
        mocker: MagicMock,
        elastic_df: pd.DataFrame = pd.DataFrame(),
    ):
        task = UpdateSlackPersonIdentifiers(
            name="UpdateSlackPersonIdentifiers", params=params
        )
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )

        mock_connections.return_value = addict.Dict(
            {
                "tenant-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    },
                    "MAX_TERMS_SIZE": 1024,
                }
            }
        )

        mocker.patch.object(
            SettingsCls, "tenant", new_callable=mocker.PropertyMock, return_value="test"
        )
        mock_es_scroll = mocker.patch.object(
            update_slack_person_identifiers, "es_scroll"
        )
        mock_es_scroll.return_value = elastic_df
        mock_auditor = mocker.patch.object(
            UpdateSlackPersonIdentifiers,
            "auditor",
        )
        mock_auditor.return_value = None
        return task.execute(
            source_frame=source_frame,
            params=params,
        )
