import json
import shutil
from pathlib import Path

import addict
import fsspec
import pandas as pd
import pytest
from prefect.engine import signals
from se_core_tasks.mymarket.static import PersonColumns
from swarm.conf import SettingsCls

from swarm_tasks.steeleye.mymarket.person.bp import get_records_for_deletion
from swarm_tasks.steeleye.mymarket.person.bp.get_records_for_deletion import (
    Params,
)


@pytest.fixture()
def empty_df() -> pd.DataFrame:
    return pd.DataFrame()


@pytest.fixture()
def missing_required_col_df() -> pd.DataFrame:
    df = pd.DataFrame({"dummy": [1, 2]})
    return df


@pytest.fixture()
def source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        [
            # Record 1:
            {
                PersonColumns.NAME: "Ralf Schumacher",
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID: "0215rm",
                PersonColumns.SOURCE_INDEX: 0,
                "&id": "114c6b6b-32a4-486d-bd94-d7b30a2d473d",
                "&hash": "a192d8cf3bd2efaba495852a4ce12ef7203e46a2ec4f87c502ba21f82e1d2423",
                "&model": "AccountPerson",
            },
            # Record 2:
            {
                PersonColumns.NAME: "Juan Pablo Montoya",
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID: "0215st",
                PersonColumns.SOURCE_INDEX: 1,
                "&id": "e28fd6f1-bdf7-4305-b470-db11ad79f628",
                "&hash": "a192d8cf3bd2efaba495852a4ce12ef7203e46a2ec4f87c502ba21f82e1d2423",
                "&model": "AccountPerson",
            },
        ]
    )
    return df


@pytest.fixture()
def params_fixture() -> Params:
    params = Params(**{"ntid_column_in_json": "ntids_list"})
    return params


@pytest.fixture()
def expected_result_when_one_row_returned_from_elastic():
    """Expected result when all of the input rows are found in Elastic. In this case, the &id
    and the &key from Elastic are expected to replace the &id and &key in the source frame
    for the matching record. For the non-matching record, the source record is sent to the output
    without any id/key replacements."""
    df = pd.DataFrame(
        [
            # Record 1: not present in Elastic (Create)
            {
                PersonColumns.COMMUNICATIONS_EMAILS: ["<EMAIL>"],
                PersonColumns.NAME: "Ralf Schumacher",
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID: "0215rm",
                PersonColumns.SOURCE_INDEX: 0,
                "&id": "114c6b6b-32a4-486d-bd94-d7b30a2d473d",
                "&key": "AccountPerson:114c6b6b-32a4-486d-bd94-d7b30a2d473d:*************",
                "create_update_indicator": "create",
            },
            # Record 2: present in Elastic (Update)
            {
                PersonColumns.COMMUNICATIONS_EMAILS: ["<EMAIL>"],
                PersonColumns.NAME: "Juan Pablo Montoya",
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID: "0215st",
                PersonColumns.SOURCE_INDEX: 1,
                "&id": "7b893bab-447b-47ba-86f1-b5995687d9e5",
                "&key": "AccountPerson:7b893bab-447b-47ba-86f1-b5995687d9e5:*************",
                "create_update_indicator": "update",
            },
        ]
    )
    return df


@pytest.fixture()
def expected_results_id_frame():
    """Expected results"""
    df = pd.DataFrame(
        [
            {
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID: "0213mj",
                "&id": "********-6dc0-4929-930c-2d0e4492c623",
                "&hash": "old_hash_1",
                "&model": "AccountPerson",
            },
            {
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID: "0213as",
                "&id": "234r543214-447b-47ba-86f1-b5995687d9e5",
                "&hash": "old_hash_2",
                "&model": "AccountPerson",
            },
        ]
    )
    return df


@pytest.fixture()
def mock_scroll_two_results():
    """Mocks es scroll for the case where 2 rows are fetched from Elastic"""
    df = pd.DataFrame(
        [
            {
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID: "0213mj",
                "&id": "********-6dc0-4929-930c-2d0e4492c623",
                "&hash": "old_hash_1",
                "&model": "AccountPerson",
            },
            {
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID: "0213as",
                "&id": "234r543214-447b-47ba-86f1-b5995687d9e5",
                "&hash": "old_hash_2",
                "&model": "AccountPerson",
            },
        ]
    )
    return df


class TestGetRecordsForDeletion:
    """Test Suite for GetRecordsForDeletion"""

    def teardown_method(self):
        try:
            shutil.rmtree(Path(__file__).parent.joinpath("tempdir").as_posix())
        except FileNotFoundError:
            pass

    def test_source_frame_empty(
        self, mocker, empty_df: pd.DataFrame, params_fixture: Params
    ):
        task = self._init_task(mocker=mocker, params=params_fixture)
        with pytest.raises(signals.FAIL):
            task.execute(source_frame=empty_df, params=params_fixture)

    def test_file_url_missing_in_source_frame(
        self, mocker, missing_required_col_df: pd.DataFrame, params_fixture: Params
    ):
        task = self._init_task(mocker=mocker, params=params_fixture)
        with pytest.raises(signals.FAIL):
            task.execute(source_frame=missing_required_col_df, params=params_fixture)

    def test_all_ntids_in_previous_file_exist(
        self,
        mocker,
        source_df: pd.DataFrame,
        params_fixture: Params,
    ):
        """Test for the case where all ids which existed in the previous ntid file
        exist. A Skip is expected to be raised, but the ntid output files are still
        written"""
        task = self._init_task(mocker=mocker, params=params_fixture)
        mock_output_paths = mocker.patch.object(
            get_records_for_deletion.GetRecordsForDeletion, "create_output_file_paths"
        )
        ntid_file = (
            Path(__file__).parent.joinpath("tempdir").joinpath("ntid_list_test1.json")
        )
        archive_file = (
            Path(__file__)
            .parent.joinpath("tempdir")
            .joinpath("ntid_list_archive_test1.json")
        )
        mock_output_paths.return_value = (ntid_file, archive_file)
        mocker.patch.object(
            get_records_for_deletion,
            "fsspec_read_json",
            return_value={params_fixture.ntid_column_in_json: ["0215rm", "0215st"]},
        )
        with pytest.raises(signals.SKIP):
            task.execute(source_frame=source_df, params=params_fixture)

        # The output files should still be written, despite the skip being raised
        with fsspec.open(Path(ntid_file).as_posix(), "r") as json_file:
            json_content = json.load(json_file)
        json_content = self.sort_json_list(json_content=json_content)
        assert json_content == {"ntids_list": ["0215rm", "0215st"]}
        with fsspec.open(Path(archive_file).as_posix(), "r") as archive_json_file:
            json_content_archive = json.load(archive_json_file)
        json_content_archive = self.sort_json_list(json_content=json_content_archive)
        assert json_content_archive == {"ntids_list": ["0215rm", "0215st"]}

    def test_some_ntids_from_previous_file_dont_exist(
        self,
        mocker,
        source_df: pd.DataFrame,
        params_fixture: Params,
        expected_results_id_frame: pd.DataFrame,
        mock_scroll_two_results: pd.DataFrame,
    ):
        """Test for the case where some of the previous file's ntids don't exist.
        These records are written to the output, and all current ntids are written to
        the output files.
        """
        task = self._init_task(mocker=mocker, params=params_fixture)
        mock_es_scroll = mocker.patch.object(get_records_for_deletion, "es_scroll")
        mock_es_scroll.return_value = mock_scroll_two_results
        mock_output_paths = mocker.patch.object(
            get_records_for_deletion.GetRecordsForDeletion, "create_output_file_paths"
        )
        ntid_file = (
            Path(__file__).parent.joinpath("tempdir").joinpath("ntid_list_test2.json")
        )
        archive_file = (
            Path(__file__)
            .parent.joinpath("tempdir")
            .joinpath("ntid_list_archive_test2.json")
        )
        mock_output_paths.return_value = (ntid_file, archive_file)
        mocker.patch.object(
            get_records_for_deletion,
            "fsspec_read_json",
            return_value={
                params_fixture.ntid_column_in_json: [
                    "0215rm",
                    "0215st",
                    "0213mj",
                    "0213as",
                ]
            },
        )
        result = task.execute(source_frame=source_df, params=params_fixture)
        assert result.equals(expected_results_id_frame)
        with fsspec.open(Path(ntid_file).as_posix(), "r") as json_file:
            json_content = json.load(json_file)
        json_content = self.sort_json_list(json_content=json_content)
        assert json_content == {"ntids_list": ["0215rm", "0215st"]}
        with fsspec.open(Path(archive_file).as_posix(), "r") as archive_json_file:
            json_content_archive = json.load(archive_json_file)
        json_content_archive = self.sort_json_list(json_content=json_content_archive)
        assert json_content_archive == {"ntids_list": ["0215rm", "0215st"]}

    def test_first_run_for_new_client(
        self,
        mocker,
        source_df: pd.DataFrame,
        params_fixture: Params,
    ):
        """When the flow runs for the first time for a new client, no ntid file exists (fsspec
        read raises a FileNotFoundError).
        In this case, nothing is deleted, and the rest of the flow is skipped.
        But the ntid files are still written to the output.
        """
        task = self._init_task(mocker=mocker, params=params_fixture)
        mock_output_paths = mocker.patch.object(
            get_records_for_deletion.GetRecordsForDeletion, "create_output_file_paths"
        )
        ntid_file = (
            Path(__file__).parent.joinpath("tempdir").joinpath("ntid_list_test1.json")
        )
        archive_file = (
            Path(__file__)
            .parent.joinpath("tempdir")
            .joinpath("ntid_list_archive_test1.json")
        )
        mock_output_paths.return_value = (ntid_file, archive_file)
        mocker.patch.object(
            get_records_for_deletion, "fsspec_read_json", side_effect=FileNotFoundError
        )
        with pytest.raises(signals.SKIP):
            task.execute(source_frame=source_df, params=params_fixture)

        # The output files should still be written, despite the skip being raised
        with fsspec.open(Path(ntid_file).as_posix(), "r") as json_file:
            json_content = json.load(json_file)
        json_content = self.sort_json_list(json_content=json_content)
        assert json_content == {"ntids_list": ["0215rm", "0215st"]}
        with fsspec.open(Path(archive_file).as_posix(), "r") as archive_json_file:
            json_content_archive = json.load(archive_json_file)
        json_content_archive = self.sort_json_list(json_content=json_content_archive)
        assert json_content_archive == {"ntids_list": ["0215rm", "0215st"]}

    @staticmethod
    def _init_task(mocker, params):
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "tenant-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    },
                    "MAX_TERMS_SIZE": 1024,
                }
            }
        )

        mocker.patch.object(
            SettingsCls, "tenant", new_callable=mocker.PropertyMock, return_value="test"
        )
        mocker.patch.object(
            SettingsCls,
            "realm",
            new_callable=mocker.PropertyMock,
            return_value="test.dev.steeleye.co",
        )

        task = get_records_for_deletion.GetRecordsForDeletion(
            name="GetRecordsForDeletion", params=params
        )

        return task

    @staticmethod
    def sort_json_list(json_content: dict):
        """Sorts the list value in the json dict (key: ntids_list)"""
        json_content["ntids_list"] = sorted(json_content["ntids_list"])
        return json_content
