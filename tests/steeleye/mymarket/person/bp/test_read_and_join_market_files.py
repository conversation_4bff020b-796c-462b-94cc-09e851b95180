from typing import Tuple
from unittest.mock import patch

import pandas as pd
import pytest
from prefect.engine import signals

from swarm_tasks.steeleye.mymarket.person.bp import read_and_join_bp_market_files
from swarm_tasks.steeleye.mymarket.person.bp.read_and_join_bp_market_files import Params
from swarm_tasks.steeleye.mymarket.person.bp.read_and_join_bp_market_files import (
    ReadAndJoinBpMarketFiles,
)


@pytest.fixture()
def empty_df() -> pd.DataFrame:
    """Empty source data frame"""
    df = pd.DataFrame({})
    return df


@pytest.fixture()
def source_df() -> pd.DataFrame:
    """Source data frame containing 4 records"""
    df = pd.DataFrame(
        {
            "s3_bpistmrl_file_url": [
                "s3://test.dev.steeleye.co/flows/mymarket-bp-person-controller/BPISTMRL_20220401.csv",
            ],
            "s3_kpmgrmrl_file_url": [
                "s3://test.dev.steeleye.co/flows/mymarket-bp-person-controller/KPMGrmrl_20220401.csv"
            ],
        }
    )
    return df


@pytest.fixture()
def mocked_bp_df() -> pd.DataFrame:
    """Mocks the DF for the BP file"""
    df = pd.DataFrame(
        [
            # Record 1
            {
                "NTID": "0215rm",
                "LAST_NAME": "Schumacher",
                "FIRST_NAME": "Ralf",
                "BUSINESS_UNIT": "T&S/GPTI/European Gas & Power/Power Originators",
                "GRBU": "GPTI",
                "COUNTRY": "United Kingdom",
                "CITY": "London",
                "BUILDING": "20 Canada Square",
                "FLOOR": "2",
                "DESK": "TBC",
                "MOBILE_NUMBER": pd.NA,
                "CHANNEL": "EMAIL",
                "START_DATE": "01-Apr-22",
            },
            {
                "NTID": "0215rm",
                "LAST_NAME": "Schumacher",
                "FIRST_NAME": "Ralf",
                "BUSINESS_UNIT": "T&S/GPTI/European Gas & Power/Power Originators",
                "GRBU": "GPTI",
                "COUNTRY": "United Kingdom",
                "CITY": "London",
                "BUILDING": "20 Canada Square",
                "FLOOR": "2",
                "DESK": "TBC",
                "MOBILE_NUMBER": pd.NA,
                "CHANNEL": "IM",
                "START_DATE": "01-Apr-22",
            },
            {
                "NTID": "0215rm",
                "LAST_NAME": "Schumacher",
                "FIRST_NAME": "Ralf",
                "BUSINESS_UNIT": "T&S/GPTI/European Gas & Power/Power Originators",
                "GRBU": "GPTI",
                "COUNTRY": "United Kingdom",
                "CITY": "London",
                "BUILDING": "20 Canada Square",
                "FLOOR": "2",
                "DESK": "TBC",
                "MOBILE_NUMBER": pd.NA,
                "CHANNEL": "TVOICE",
                "START_DATE": "01-Apr-22",
            },
            # Record which doesn't have a corresponding record in the second file
            {
                "NTID": "0215mj",
                "LAST_NAME": "Jin",
                "FIRST_NAME": "Min",
                "BUSINESS_UNIT": "T&S/GPTI/European Gas & Power/Power Originators",
                "GRBU": "GPTI",
                "COUNTRY": "United Kingdom",
                "CITY": "London",
                "BUILDING": "20 Canada Square",
                "FLOOR": "2",
                "DESK": "TBC",
                "MOBILE_NUMBER": pd.NA,
                "CHANNEL": "TVOICE",
                "START_DATE": "01-Apr-22",
            },
        ]
    )
    return df


@pytest.fixture()
def mocked_kpmg_df() -> pd.DataFrame:
    """Mocks the DF for the BP file"""
    df = pd.DataFrame(
        [
            # Record 1
            {
                "User": "Juan Pablo Montoya",
                "Primary SMTP address": "<EMAIL>",
                "NTID": "0215RM",
                "Email Aliases": "<EMAIL>",
            },
            {
                "User": "Juan Pablo Montoya",
                "Primary SMTP address": "<EMAIL>",
                "NTID": "0215rm",
                "Email Aliases": "<EMAIL>",
            },
            {
                "User": "Juan Pablo Montoya",
                "Primary SMTP address": "<EMAIL>",
                "NTID": "0215rm",
                "Email Aliases": "<EMAIL>",
            },
        ]
    )
    return df


@pytest.fixture()
def csv_from_s3_download_side_effect(
    mocked_bp_df, mocked_kpmg_df
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    return mocked_bp_df, mocked_kpmg_df


@pytest.fixture()
def params_fixture() -> Params:
    params = Params(
        **{
            "bp_file_column": "s3_bpistmrl_file_url",
            "kpmg_file_column": "s3_kpmgrmrl_file_url",
            "ntid_column": "NTID",
            "dataframe_columns": [
                "BUILDING",
                "BUSINESS_UNIT",
                "CITY",
                "COUNTRY",
                "DESK",
                "FIRST_NAME",
                "FLOOR",
                "GRBU",
                "LAST_NAME",
                "MOBILE_NUMBER",
                "NTID",
                "Primary SMTP address",
            ],
        }
    )
    return params


@pytest.fixture()
def expected_result_for_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        [
            {
                "NTID": "0215rm",
                "LAST_NAME": "Schumacher",
                "FIRST_NAME": "Ralf",
                "BUSINESS_UNIT": "T&S/GPTI/European Gas & Power/Power Originators",
                "GRBU": "GPTI",
                "COUNTRY": "United Kingdom",
                "CITY": "London",
                "BUILDING": "20 Canada Square",
                "FLOOR": "2",
                "DESK": "TBC",
                "MOBILE_NUMBER": pd.NA,
                "CHANNEL": "EMAIL",
                "START_DATE": "01-Apr-22",
                "User": "Juan Pablo Montoya",
                "Primary SMTP address": "<EMAIL>",
                "Email Aliases": "<EMAIL>",
            },
            {
                "NTID": "0215mj",
                "LAST_NAME": "Jin",
                "FIRST_NAME": "Min",
                "BUSINESS_UNIT": "T&S/GPTI/European Gas & Power/Power Originators",
                "GRBU": "GPTI",
                "COUNTRY": "United Kingdom",
                "CITY": "London",
                "BUILDING": "20 Canada Square",
                "FLOOR": "2",
                "DESK": "TBC",
                "MOBILE_NUMBER": pd.NA,
                "CHANNEL": "TVOICE",
                "START_DATE": "01-Apr-22",
                "User": pd.NA,
                "Primary SMTP address": pd.NA,
                "Email Aliases": pd.NA,
            },
        ]
    )
    return df


class TestReadAndJoinBpMarketFiles:
    """Test suite for ReadAndJoinBpMarketFiles"""

    def test_empty_source_df(self, empty_df, params_fixture):
        """Test for the case where the source data frame is empty"""
        task = ReadAndJoinBpMarketFiles(
            name="ReadAndJoinBpMarketFiles", params=params_fixture
        )
        with pytest.raises(signals.SKIP):
            task.execute(empty_df, params=params_fixture)

    @patch.object(read_and_join_bp_market_files, "read_csv_from_s3_download")
    def test_values_for_all_columns(
        self,
        mock_download,
        source_df,
        expected_result_for_source_df,
        params_fixture,
        csv_from_s3_download_side_effect,
    ):
        """Test for the case where the 2 files are read, deduplicated and
        joined together
        """
        task = ReadAndJoinBpMarketFiles(
            name="ReadAndJoinBpMarketFiles", params=params_fixture
        )
        mock_download.side_effect = csv_from_s3_download_side_effect
        result = task.execute(source_df, params=params_fixture)
        assert result.equals(expected_result_for_source_df)
