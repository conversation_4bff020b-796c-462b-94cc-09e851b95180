import addict
import pandas as pd
import pytest
from prefect.engine import signals
from se_core_tasks.mymarket.static import PersonColumns
from swarm.conf import SettingsCls

from swarm_tasks.steeleye.mymarket.person.bp import fetch_person_records_from_elastic
from swarm_tasks.steeleye.mymarket.person.bp.fetch_person_records_from_elastic import (
    Params,
)


@pytest.fixture()
def empty_df() -> pd.DataFrame:
    return pd.DataFrame()


@pytest.fixture()
def missing_required_col_df() -> pd.DataFrame:
    df = pd.DataFrame({"dummy": [1, 2]})
    return df


@pytest.fixture()
def source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        [
            # Record 1:
            {
                PersonColumns.COMMUNICATIONS_EMAILS: ["<EMAIL>"],
                PersonColumns.NAME: "<PERSON><PERSON>",
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID: "0215rm",
                PersonColumns.SOURCE_INDEX: 0,
                "&id": "114c6b6b-32a4-486d-bd94-d7b30a2d473d",
                "&key": "AccountPerson:114c6b6b-32a4-486d-bd94-d7b30a2d473d:*************",
            },
            # Record 2:
            {
                PersonColumns.COMMUNICATIONS_EMAILS: ["<EMAIL>"],
                PersonColumns.NAME: "Juan Pablo Montoya",
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID: "0215st",
                PersonColumns.SOURCE_INDEX: 1,
                "&id": "e28fd6f1-bdf7-4305-b470-db11ad79f628",
                "&key": "AccountPerson:e28fd6f1-bdf7-4305-b470-db11ad79f628:*************",
            },
        ]
    )
    return df


@pytest.fixture()
def params_fixture() -> Params:
    params = Params(**{"create_update_indicator_column": "create_update_indicator"})
    return params


@pytest.fixture()
def expected_result_when_one_row_returned_from_elastic():
    """Expected result when all of the input rows are found in Elastic. In this case, the &id
    and the &key from Elastic are expected to replace the &id and &key in the source frame
    for the matching record. For the non-matching record, the source record is sent to the output
    without any id/key replacements."""
    df = pd.DataFrame(
        [
            # Record 1: not present in Elastic (Create)
            {
                PersonColumns.COMMUNICATIONS_EMAILS: ["<EMAIL>"],
                PersonColumns.NAME: "Ralf Schumacher",
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID: "0215rm",
                PersonColumns.SOURCE_INDEX: 0,
                "&id": "114c6b6b-32a4-486d-bd94-d7b30a2d473d",
                "&key": "AccountPerson:114c6b6b-32a4-486d-bd94-d7b30a2d473d:*************",
                "create_update_indicator": "create",
            },
            # Record 2: present in Elastic (Update)
            {
                PersonColumns.COMMUNICATIONS_EMAILS: ["<EMAIL>"],
                PersonColumns.NAME: "Juan Pablo Montoya",
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID: "0215st",
                PersonColumns.SOURCE_INDEX: 1,
                "&id": "7b893bab-447b-47ba-86f1-b5995687d9e5",
                "&key": "AccountPerson:7b893bab-447b-47ba-86f1-b5995687d9e5:*************",
                "create_update_indicator": "update",
            },
        ]
    )
    return df


@pytest.fixture()
def expected_result_when_no_rows_returned_from_elastic():
    """Expected result when all of the input rows are found in Elastic. In this case, the input record
    is written to the output without any id/key replacements"""
    df = pd.DataFrame(
        [
            # Record 1: not present in Elastic (Create)
            {
                PersonColumns.COMMUNICATIONS_EMAILS: ["<EMAIL>"],
                PersonColumns.NAME: "Ralf Schumacher",
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID: "0215rm",
                PersonColumns.SOURCE_INDEX: 0,
                "&id": "114c6b6b-32a4-486d-bd94-d7b30a2d473d",
                "&key": "AccountPerson:114c6b6b-32a4-486d-bd94-d7b30a2d473d:*************",
                "create_update_indicator": "create",
            },
            # Record 2: not present in Elastic (Create)
            {
                PersonColumns.COMMUNICATIONS_EMAILS: ["<EMAIL>"],
                PersonColumns.NAME: "Juan Pablo Montoya",
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID: "0215st",
                PersonColumns.SOURCE_INDEX: 1,
                "&id": "e28fd6f1-bdf7-4305-b470-db11ad79f628",
                "&key": "AccountPerson:e28fd6f1-bdf7-4305-b470-db11ad79f628:*************",
                "create_update_indicator": "create",
            },
        ]
    )
    return df


@pytest.fixture()
def expected_result_when_all_rows_returned_from_elastic():
    """Expected result when all of the input rows are found in Elastic. In this case, the &id
    and the &key from Elastic are expected to replace the &id and &key in the source frame
    for both records"""
    df = pd.DataFrame(
        [
            # Record 1: present in Elastic (Update)
            {
                PersonColumns.COMMUNICATIONS_EMAILS: ["<EMAIL>"],
                PersonColumns.NAME: "Ralf Schumacher",
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID: "0215rm",
                PersonColumns.SOURCE_INDEX: 0,
                "&id": "39e719ca-6dc0-4929-930c-2d0e4492c623",
                "&key": "AccountPerson:39e719ca-6dc0-4929-930c-2d0e4492c623:*************",
                "create_update_indicator": "update",
            },
            # Record 2: present in Elastic (Update)
            {
                PersonColumns.COMMUNICATIONS_EMAILS: ["<EMAIL>"],
                PersonColumns.NAME: "Juan Pablo Montoya",
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID: "0215st",
                PersonColumns.SOURCE_INDEX: 1,
                "&id": "7b893bab-447b-47ba-86f1-b5995687d9e5",
                "&key": "AccountPerson:7b893bab-447b-47ba-86f1-b5995687d9e5:*************",
                "create_update_indicator": "update",
            },
        ]
    )
    return df


@pytest.fixture()
def mock_scroll_one_result():
    """Mocks es scroll for the case where 1 row is fetched from Elastic"""
    df = pd.DataFrame(
        [
            {
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID: "0215st",
                "&id": "7b893bab-447b-47ba-86f1-b5995687d9e5",
                "&key": "AccountPerson:7b893bab-447b-47ba-86f1-b5995687d9e5:*************",
            }
        ]
    )
    return df


@pytest.fixture()
def mock_scroll_two_results():
    """Mocks es scroll for the case where 2 rows are fetched from Elastic"""
    df = pd.DataFrame(
        [
            {
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID: "0215rm",
                "&id": "39e719ca-6dc0-4929-930c-2d0e4492c623",
                "&key": "AccountPerson:39e719ca-6dc0-4929-930c-2d0e4492c623:*************",
            },
            {
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID: "0215st",
                "&id": "7b893bab-447b-47ba-86f1-b5995687d9e5",
                "&key": "AccountPerson:7b893bab-447b-47ba-86f1-b5995687d9e5:*************",
            },
        ]
    )
    return df


class TestFetchPersonRecordsFromElastic:
    """Test Suite for FetchPersonRecordsFromElastic"""

    def test_source_frame_empty(
        self, mocker, empty_df: pd.DataFrame, params_fixture: Params
    ):
        task = self._init_task(mocker=mocker, params=params_fixture)
        with pytest.raises(signals.FAIL):
            task.execute(source_frame=empty_df, params=params_fixture)

    def test_file_url_missing_in_source_frame(
        self, mocker, missing_required_col_df: pd.DataFrame, params_fixture: Params
    ):
        task = self._init_task(mocker=mocker, params=params_fixture)
        with pytest.raises(signals.FAIL):
            task.execute(source_frame=missing_required_col_df, params=params_fixture)

    def test_no_results_in_elastic(
        self,
        mocker,
        source_df: pd.DataFrame,
        params_fixture: Params,
        expected_result_when_no_rows_returned_from_elastic: pd.DataFrame,
    ):
        """Test for the case where no results are found in Elastic"""
        task = self._init_task(mocker=mocker, params=params_fixture)
        mock_es_scroll = mocker.patch.object(
            fetch_person_records_from_elastic, "es_scroll"
        )
        mock_es_scroll.return_value = pd.DataFrame()
        result = task.execute(source_frame=source_df, params=params_fixture)
        assert result.equals(expected_result_when_no_rows_returned_from_elastic)

    def test_one_row_fetched_from_elastic(
        self,
        mocker,
        source_df: pd.DataFrame,
        params_fixture: Params,
        expected_result_when_one_row_returned_from_elastic: pd.DataFrame,
        mock_scroll_one_result: pd.DataFrame,
    ):
        """Test for the case where 1 rows was present in Elastic and one rows wasn't"""
        task = self._init_task(mocker=mocker, params=params_fixture)
        mock_es_scroll = mocker.patch.object(
            fetch_person_records_from_elastic, "es_scroll"
        )
        mock_es_scroll.return_value = mock_scroll_one_result
        result = task.execute(source_frame=source_df, params=params_fixture)
        assert result.equals(expected_result_when_one_row_returned_from_elastic)

    def test_both_rows_fetched_from_elastic(
        self,
        mocker,
        source_df: pd.DataFrame,
        params_fixture: Params,
        expected_result_when_all_rows_returned_from_elastic: pd.DataFrame,
        mock_scroll_two_results: pd.DataFrame,
    ):
        """Test for the case where both of the input rows have a match in Elastic"""
        task = self._init_task(mocker=mocker, params=params_fixture)
        mock_es_scroll = mocker.patch.object(
            fetch_person_records_from_elastic, "es_scroll"
        )
        mock_es_scroll.return_value = mock_scroll_two_results
        result = task.execute(source_frame=source_df, params=params_fixture)
        assert result.equals(expected_result_when_all_rows_returned_from_elastic)

    @staticmethod
    def _init_task(mocker, params):
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "tenant-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    },
                    "MAX_TERMS_SIZE": 1024,
                }
            }
        )

        mocker.patch.object(
            SettingsCls, "tenant", new_callable=mocker.PropertyMock, return_value="test"
        )

        task = fetch_person_records_from_elastic.FetchPersonRecordsFromElastic(
            name="FetchPersonRecordsFromElastic", params=params
        )

        return task
