import pandas as pd
import pytest

from swarm_tasks.steeleye.mymarket.person.additional_person_identifiers import (
    AdditionalPersonIdentifiers,
)
from swarm_tasks.steeleye.mymarket.person.additional_person_identifiers import Params


@pytest.fixture()
def params_fixture() -> Params:
    params = Params(
        **{
            "source_dob_col": "__date_of_birth__",
            "source_nationality_col": "__nationality__",
            "source_first_name_col": "personalDetails.firstName",
            "source_last_name_col": "personalDetails.lastName",
            "source_national_id_col": "officialIdentifiers.nationalIds",
            "source_passport_col": "officialIdentifiers.passports",
            "target_concat_id_column": "officialIdentifiers.concatId",
            "target_mifir_id_column": "officialIdentifiers.mifirId",
            "target_mifir_id_type_column": "officialIdentifiers.mifirIdType",
            "target_mifir_id_subtype_column": "officialIdentifiers.mifirIdSubType",
        }
    )
    return params


@pytest.fixture()
def empty_df() -> pd.DataFrame:
    """Empty source data frame"""
    df = pd.DataFrame({})
    return df


@pytest.fixture()
def source_df() -> pd.DataFrame:
    """Source data frame containing 5 records."""
    df = pd.DataFrame(
        {
            "__nationality__": ["GB", "FR", "US", "IN", "DE", pd.NA],
            "__date_of_birth__": [
                "19620811",
                "19610226",
                "19660311",
                "19980325",
                pd.NA,
                "19960211",
            ],
            "personalDetails.firstName": [
                "Ashley",
                "Susan",
                "Alan",
                "Rahul",
                "Georg",
                "Conor",
            ],
            "personalDetails.lastName": [
                "Cole",
                "Shelley",
                "Wilkins",
                "Dravid",
                "Schneemann",
                "Coady",
            ],
            "officialIdentifiers.passports": [
                # Record 1
                [{"id": "mIw7Trab3", "label": "GB"}],
                # Record 2
                [{"id": "helCFFZ42", "label": "FR"}],
                # Record 3
                [{"id": "L5f99a7b", "label": "US"}],
                # Record 4
                [{"id": "eab37yds3s", "label": "IN"}],
                # Record 5
                [{"id": "nIw7Trbb3", "label": "DE"}],
                # Record 6
                pd.NA,
            ],
            "officialIdentifiers.nationalIds": [
                # Record 1
                [{"id": "mIw7Trab3", "label": "GB - National Insurance"}],
                # Record 2
                [{"id": "L5f99a7b", "label": "FR - INSEE code"}],
                # Record 3
                [{"id": "eab37yds3s", "label": "US - Social Security Number"}],
                # Record 4
                [{"id": "ZiBofV8m4", "label": "IN - Aadhaar"}],
                # Record 5
                [{"id": "nIw7Trbb3", "label": "DE - Personalausweis"}],
                # Record 6
                pd.NA,
            ],
        }
    )
    return df


@pytest.fixture()
def expected_result_for_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "officialIdentifiers.concatId": [
                "GB19620811ASHLECOLE#",
                "FR19610226SUSANSHELL",
                "US19660311ALAN#WILKI",
                "IN19980325RAHULDRAVI",
                pd.NA,
                pd.NA,
            ],
            "officialIdentifiers.mifirId": [
                "GBmIw7Trab3",
                "FR19610226SUSANSHELL",
                "USL5f99a7b",
                "INeab37yds3s",
                "DEnIw7Trbb3",
                pd.NA,
            ],
            "officialIdentifiers.mifirIdSubType": [
                # Record 1
                "NIDN",
                # Record 2
                "CONCAT",
                # Record 3
                "CCPT",
                # Record 4
                "CCPT",
                # Record 5
                "CONCAT",
                # Record 6
                pd.NA,
            ],
            "officialIdentifiers.mifirIdType": ["N", "N", "N", "N", "N", pd.NA],
        }
    )
    return df


class TestAdditionalPersonIdentifiers:
    """Test suite for AdditionalPersonIdentifiers"""

    def test_empty_source_df(self, empty_df, params_fixture):
        """Test for the case where the source data frame is empty"""
        task = AdditionalPersonIdentifiers(
            name="MapNationalIdentifiers", params=params_fixture
        )
        result = task.execute(empty_df, params=params_fixture)
        assert result.empty

    def test_values_for_all_columns(
        self, source_df, expected_result_for_source_df, params_fixture
    ):
        """Test for the case where the different columns are populated. Different
        tests are conducted for the 5 records in the source_df.
        Note: Last row, the concat ID should be null, and as a result the Mifir id
        should be None
        """
        task = AdditionalPersonIdentifiers(
            name="MapNationalIdentifiers", params=params_fixture
        )
        result = task.execute(source_df, params=params_fixture)
        assert result.equals(expected_result_for_source_df)
