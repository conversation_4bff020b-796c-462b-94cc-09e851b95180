import pandas as pd
import pytest

from swarm_tasks.tr.feed.onezero.dna.client_leg_generator import ClientLegGenerator
from swarm_tasks.tr.feed.onezero.dna.client_leg_generator import Params


@pytest.fixture
def source_frame_empty():
    data = pd.DataFrame({})
    return data


@pytest.fixture
def source_frame_with_data():
    data = {
        "__taker_executed_price__": [123.123, 456.456, 789.789],
        "__temp_taker_login__": ["ZYX", "WVU", "TSR"],
        "__temp_maker_login__": ["QPO", "NML", "KJI"],
        "__buyer_with_id__": ["Maker8", "Maker8", "Maker8"],
        "__seller_with_id__": ["Maker3", "Maker3", "Maker3"],
        "transactionDetails.buySellIndicator": ["BUYI", "BUYI", "SELL"],
        "transactionDetails.price": [20.0, 30.0, 40.0],
        "reportDetails.transactionRefNo": ["987LP", "654LP", "321LP"],
    }
    return pd.DataFrame(data)


@pytest.fixture
def expected_result_for_source_frame_with_data():
    """Expected result obtained while using the source_frame_with_data fixture"""
    # Note: the source and target have the same columns, but the values
    # are different
    data = {
        "__taker_executed_price__": [123.123, 456.456, 789.789],
        "__temp_taker_login__": ["ZYX", "WVU", "TSR"],
        "__temp_maker_login__": ["QPO", "NML", "KJI"],
        "__buyer_with_id__": ["ZYX", "WVU", "TSR"],
        "__seller_with_id__": ["QPO", "NML", "KJI"],
        "transactionDetails.buySellIndicator": ["SELL", "SELL", "BUYI"],
        "transactionDetails.price": [123.123, 456.456, 789.789],
        "reportDetails.transactionRefNo": ["987CL", "654CL", "321CL"],
    }
    return pd.DataFrame(data)


class TestClientLegGenerator:
    """Test suite for ClientLegGenerator"""

    def test_source_frame_empty(self, source_frame_empty):
        """Tests the task for the case where the source frame is empty"""
        task, params = self.execute_task()
        result = task.execute(source_frame_empty, params)
        assert result.empty

    def test_source_frame_with_values(
        self, source_frame_with_data, expected_result_for_source_frame_with_data
    ):
        """Tests the task for the case where the source frame has values for all
        columns"""
        task, params = self.execute_task()
        result = task.execute(source_frame_with_data, params)
        assert result.equals(expected_result_for_source_frame_with_data)

    # Test individual functions

    @staticmethod
    def execute_task():
        params = Params(
            source_price_attribute="__taker_executed_price__",
            source_buyer_field="__temp_taker_login__",
            source_seller_field="__temp_maker_login__",
            buyer_column="__buyer_with_id__",
            seller_column="__seller_with_id__",
            source_transaction_ref_no_suffix="LP",
            target_transaction_ref_no_suffix="CL",
        )
        task = ClientLegGenerator(name="client-leg-gen", params=params)
        return task, params
