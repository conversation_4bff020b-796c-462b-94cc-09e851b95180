import pandas as pd
import pytest
from se_elastic_schema.components.market.identifier import Identifier
from se_elastic_schema.static.market import IdentifierType

from swarm_tasks.tr.feed.ice.fix.instrument_identifier import InstrumentIdentifier
from swarm_tasks.tr.feed.ice.fix.instrument_identifier import Params

params = Params(
    asset_class_attribute="__asset_class__",
    expiry_date_attribute="__expiry_date__",
    option_strike_price_attribute="__option_strike_price__",
    option_type_attribute="OptionType",
    swap_near_leg_date_attribute="date",
    underlying_symbol_attribute="__underlying_symbol__",
    venue_attribute="Venue",
    target_attribute="marketIdentifiers.instrument",
)


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    return pd.DataFrame()


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "__asset_class__": ["future", "option", pd.NA, "future"],
            "__expiry_date__": ["2021-06-01", "2021-06-01", pd.NA, "2021-06-01"],
            "__option_strike_price__": [1, 1, pd.NA, 1],
            "OptionType": ["XXXXX", "XXXXX", pd.NA, "XXXXX"],
            "date": ["2021-06-01", "2021-06-01", "2021-06-01", "2021-06-01"],
            "__underlying_symbol__": ["G", "G", "G", "G"],
            "Venue": ["XXXX", "XXXX", pd.NA, "NDEX"],
        }
    )
    return df


@pytest.fixture()
def missing_some_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "__asset_class__": ["future", "option", pd.NA, "future"],
            "__expiry_date__": ["2021-06-01", "2021-06-01", pd.NA, "2021-06-01"],
            "date": ["2021-06-01", "2021-06-01", "2021-06-01", "2021-06-01"],
            "__underlying_symbol__": ["G", "G", "G", "G"],
            "Venue": ["XXXX", "XXXX", pd.NA, "NDEX"],
        }
    )
    return df


class TestInstrumentIdentifier(object):
    """
    Test cases for "TestInstrumentIdentifiers" class
    """

    def test_empty_input_df_without_source_columns(self, empty_source_df: pd.DataFrame):
        task = InstrumentIdentifier(name="test-instrument-identifiers", params=params)
        result = task.execute(empty_source_df, params=params)
        assert result.empty

    def test_all_cols_in_source_df(self, all_col_in_source_df: pd.DataFrame):
        task = InstrumentIdentifier(name="test-instrument-identifiers", params=params)
        result = task.execute(all_col_in_source_df, params=params)
        expected_data = pd.Series(
            [
                ["XXXXGFF2021-06-01 00:00:00", "XXXXGFF2021-06 00:00:00"],
                [
                    "XXXXGOX2021-06-01 00:00:00",
                    "XXXXGOX2021-06 00:00:00",
                    "XXXXGOX2021-06-01 00:00:001.00000000",
                    "XXXXGOX2021-06-01 00:00:000.01000000",
                    "XXXXGOX2021-06-01 00:00:000.00100000",
                    "XXXXGOX2021-06-01 00:00:000.00010000",
                    "XXXXGOX2021-06 00:00:001.00000000",
                    "XXXXGOX2021-06 00:00:000.01000000",
                    "XXXXGOX2021-06 00:00:000.00100000",
                    "XXXXGOX2021-06 00:00:000.00010000",
                ],
                pd.NA,
                [
                    "NDEXGFF2021-06-01 00:00:00",
                    "NDEXGFF2021-06 00:00:00",
                    "IFEUGFF2021-06-01 00:00:00",
                    "IFEUGFF2021-06 00:00:00",
                ],
            ]
        )
        expected_result = pd.DataFrame(
            data=pd.NA,
            index=all_col_in_source_df.index,
            columns=[params.target_attribute],
        )
        with_ids_mask = expected_data.notnull()
        expected_result.loc[
            with_ids_mask, "marketIdentifiers.instrument"
        ] = expected_data.loc[with_ids_mask].apply(
            lambda x: list(
                map(
                    lambda y: Identifier(
                        labelId=y,
                        path="instrumentDetails.instrument",
                        type=IdentifierType.OBJECT,
                    ).dict(),
                    x,
                )
            )
        )
        assert result.equals(expected_result)

    def test_missing_some_cols_in_source_df(
        self, missing_some_col_in_source_df: pd.DataFrame
    ):
        task = InstrumentIdentifier(name="test-instrument-identifiers", params=params)
        result = task.execute(missing_some_col_in_source_df, params=params)
        expected_data = pd.Series(
            [
                ["XXXXGFF2021-06-01 00:00:00", "XXXXGFF2021-06 00:00:00"],
                pd.NA,
                pd.NA,
                [
                    "NDEXGFF2021-06-01 00:00:00",
                    "NDEXGFF2021-06 00:00:00",
                    "IFEUGFF2021-06-01 00:00:00",
                    "IFEUGFF2021-06 00:00:00",
                ],
            ]
        )
        expected_result = pd.DataFrame(
            data=pd.NA,
            index=missing_some_col_in_source_df.index,
            columns=[params.target_attribute],
        )
        with_ids_mask = expected_data.notnull()
        expected_result.loc[
            with_ids_mask, "marketIdentifiers.instrument"
        ] = expected_data.loc[with_ids_mask].apply(
            lambda x: list(
                map(
                    lambda y: Identifier(
                        labelId=y,
                        path="instrumentDetails.instrument",
                        type=IdentifierType.OBJECT,
                    ).dict(),
                    x,
                )
            )
        )
        assert result.equals(expected_result)
