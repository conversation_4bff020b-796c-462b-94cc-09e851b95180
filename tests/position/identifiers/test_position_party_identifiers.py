from pathlib import Path

import pandas as pd

from swarm_tasks.position.identifiers.position_party_identifiers import Params
from swarm_tasks.position.identifiers.position_party_identifiers import (
    PositionPartyIdentifiers,
)


SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
SOURCE_DATA = TEST_FILES_DIR.joinpath("party_source_data_without_prefix.pkl")
EXPECTED_RESULT = TEST_FILES_DIR.joinpath("expected_parties_identifiers.pkl")


class TestPositionPartyIdentifiers:
    """Test for PositionParty Identifiers"""

    def test_end_to_end_party_identifiers(self):
        source_frame = pd.read_pickle(SOURCE_DATA)
        params = Params(
            target_attribute="marketIdentifiers.parties",
            level="level",
        )
        task = PositionPartyIdentifiers(name="PositionPartyIdentifiers", params=params)

        result = task.process(source_frame=source_frame, params=params)
        expected = pd.read_pickle(EXPECTED_RESULT)
        pd.testing.assert_frame_equal(left=result, right=expected)
