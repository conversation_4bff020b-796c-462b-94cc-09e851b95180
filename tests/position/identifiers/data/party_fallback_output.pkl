���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]��
parties.value�at�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK2��h�i8�����R�(K�<�NNNJ����J����K t�b�B�                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       �t�bh'�__swarm_raw_index__�u��R�e]�hhK ��h��R�(KKK2��h!�]�(}�(�&id��$155c266d-afac-4b36-9409-b4329da44f3a��retailOrProfessional��PROFESSIONAL��&key��?MarketPerson:155c266d-afac-4b36-9409-b4329da44f3a:1674715395055��	uniqueIds�]��
id:trader2�a�name��SHRENIK Parakh��personalDetails.firstName��SHRENIK��personalDetails.lastName��Parakh��$sinkIdentifiers.tradeFileIdentifiers�]�}�(�id��trader2��label��id�uauhB}�(h'�new_fund_id��&id�hZ�&key��MarketCounterparty:�u}�(h'�new_fund_id�h[h_h\h]u}�(h'�new_fund_id�h[hah\h]u}�(h'�new_fund_id�h[hch\h]u}�(h'�new_fund_id�h[heh\h]u}�(h'�new_fund_id�h[hgh\h]u}�(h'�new_fund_id�h[hih\h]u}�(h'�new_fund_id�h[hkh\�AccountPerson:�u}�(h'�trader2�h[hnh\h]u}�(h'�trader2�h[hph\h]u}�(h'�trader2�h[hrh\h]u}�(h'�trader2�h[hth\h]u}�(h'�trader2�h[hvh\h]u}�(h'�trader2�h[hxh\h]u}�(h'�trader2�h[hzh\h]u}�(h'�trader2�h[h|h\h]u}�(h'�trader2�h[h~h\h]u}�(h'�trader2�h[h�h\h]uhBhBhBhBhBhBhBhBhBhBhBhBhBhBhBhBhBhBhBhB}�(h'�trader1�h[h�h\hlu}�(h'�trader1�h[h�h\hlu}�(h'�trader1�h[h�h\hlu}�(h'�trader1�h[h�h\h]u}�(h'�trader1�h[h�h\h]u}�(h'�trader1�h[h�h\hlu}�(h'�trader1�h[h�h\hlu}�(h'�trader1�h[h�h\hlu}�(h'�trader1�h[h�h\hlu}�(h'�trader1�h[h�h\hluet�ba]�h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bh'Nu��R�a}��0.14.1�}�(�axes�h
�blocks�]�}�(�values�h?�mgr_locs��builtins��slice���K KK��R�uaust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.