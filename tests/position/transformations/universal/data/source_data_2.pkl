���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�DATE��	DIRECTION��CURRENCY��RIC��ISIN��AMOUNT��LEVEL��CLIENTID��	ACCOUNTID��TRADERID��QUANTITY��	PNLAMOUNT��QUANTITYNOTATION��ADDITIONALINFORMATION��FUNDID��PORTFOLIOMANAGERID��RISKENTITYID�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�Cp                            	                                                                      �t�bh7�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�********��********�hY�********�hX�********�h[�********��********�h[�********��********��********�h^et�b�_dtype�hM�StringDtype���)��ubhO)��}�(hRhhK ��h��R�(KK��h!�]�(�Long�hmhm�Short�hnhmhmhmhnhnhmhmhnhnet�bhbhd)��ubhO)��}�(hRhhK ��h��R�(KK��h!�]�(�CAD�hxhxhxhxhxhxhxhxhxhxhxhxhxet�bhbhd)��ubhO)��}�(hRhhK ��h��R�(KK��h!�]�(�pandas._libs.missing��NA���h�h��JBR.CD�h�h�h�h��CS.PA�h�h�h�h�h�et�bhbhd)��ubhO)��}�(hRhhK ��h��R�(KK��h!�]�(�CA4702731031�h�h�h�h��FR0000120628�h�h�h�h�h�h�h�h�et�bhbhd)��ubhhK ��h��R�(KKK��h�f8�����R�(KhENNNJ����J����K t�b�Cp    ��A     �A     �A     �A    ��A     ��@     ��@      �@      �@     ��@    ��A     �A     �A    ��A�t�bhO)��}�(hRhhK ��h��R�(KK��h!�]�(�Trader��	Client ID�h��
Account ID�h�h�h�h�h�h�h�h�h�h�et�bhbhd)��ubhO)��}�(hRhhK ��h��R�(KK��h!�]�(h��UBS�h�h�h�h�h�h�h�h�h��
Snow White�h�h�et�bhbhd)��ubhO)��}�(hRhhK ��h��R�(KK��h!�]�(h�h�h��RISKARB�h�h�h�h�h�h�h�h�h�h�et�bhbhd)��ubhO)��}�(hRhhK ��h��R�(KK��h!�]�(�JP01�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhbhd)��ubhhK ��h��R�(KKK��h��C�      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bhO)��}�(hRhhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhbhd)��ubhO)��}�(hRhhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhbhd)��ubhO)��}�(hRhhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhbhd)��ubhO)��}�(hRhhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhbhd)��ubhO)��}�(hRhhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhbhd)��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bh7Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bh7Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bh7Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bh7Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bh7Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bh7Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bh7Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bh7Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bh7Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bh7Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h/h0et�bh7Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bh7Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bh7Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bh7Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bh7Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bh7Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hP�mgr_locs��builtins��slice���K KK��R�u}�(j�  hfj�  j�  KKK��R�u}�(j�  hqj�  j�  KKK��R�u}�(j�  h{j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KK	K��R�u}�(j�  h�j�  j�  K	K
K��R�u}�(j�  h�j�  j�  K
KK��R�u}�(j�  h�j�  j�  KK
K��R�u}�(j�  h�j�  j�  K
KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.