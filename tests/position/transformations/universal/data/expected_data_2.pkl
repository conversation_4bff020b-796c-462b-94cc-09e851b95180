���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK ��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�additionalInformation��
amount.native��amount.nativeCurrency��date��amount.amountInChf��amount.amountInEur��amount.amountInGbp��amount.amountInJpy��amount.amountInUsd��dataSourceName��	direction��level��marketIdentifiers.instrument��isin_attribute��marketIdentifiers.parties��parties.fileIdentifier��marketIdentifiers��__meta_model__��parties.type��pnlAmount.native��pnlAmount.nativeCurrency��pnlAmount.amountInChf��pnlAmount.amountInEur��pnlAmount.amountInGbp��pnlAmount.amountInJpy��pnlAmount.amountInUsd��quantity��quantityNotation��sourceIndex��	__party__��__ric__��__isin__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�Cp                            	                                                                      �t�bhF�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�pandas._libs.missing��NA���hmhmhmhmhmhmhmhmhmhmhmhmhmet�b�_dtype�h\�StringDtype���)��ubhhK ��h��R�(KKK��h�f8�����R�(KhTNNNJ����J����K t�b�C�    ��A     �A     �A     �A    ��A     ��@     ��@      �@      �@     ��@    ��A     �A     �A    ��A      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bh^)��}�(hahhK ��h��R�(KK��hh�]�(�CAD�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhohq)��ubhhK ��h��R�(KKK��h!�]�(�
2022-06-08��
2022-06-07��
2022-06-07��
2022-06-09��
2022-06-08��
2021-06-08��
2021-06-08��
2021-06-07��
2021-06-09��
2021-06-08��
2023-06-08��
2023-06-07��
2023-06-09��
2023-06-08�h�scalar���hyCzah��( A���R�h�hyC]�G��L�@���R�h�hyC]�G��L�@���R�h�hyC�*�X�A���R�h�hyCzah��( A���R�h�hyC�ǽݗ��@���R�h�hyC�ǽݗ��@���R�h�hyC�'�����@���R�h�hyC��Qh�֔@���R�h�hyC�ǽݗ��@���R�hmhmhmhmh�hyCl������@���R�h�hyC�u+#�@���R�h�hyC�u+#�@���R�h�hyC
l���D A���R�h�hyCl������@���R�h�hyC�0����@���R�h�hyC�0����@���R�h�hyC�E`�@���R�h�hyCG�P�@���R�h�hyC�0����@���R�hmhmhmhmh�hyC��&Ò_�@���R�h�hyCM����@���R�h�hyCM����@���R�h�hyC�R>���@���R�h�hyC��&Ò_�@���R�h�hyC+.b�v�@���R�h�hyC+.b�v�@���R�h�hyC�Թw�:�@���R�h�hyC��U͊n�@���R�h�hyC+.b�v�@���R�hmhmhmhmh�hyC\��QwSqA���R�h�hyC�w.-pA���R�h�hyC�w.-pA���R�h�hyC|(T�#KrA���R�h�hyC\��QwSqA���R�h�hyCɋY���A���R�h�hyCɋY���A���R�h�hyC�v���A���R�h�hyC�(���A���R�h�hyCɋY���A���R�hmhmhmhmh�hyC��=�Y� A���R�h�hyC|�u����@���R�h�hyC|�u����@���R�h�hyCO���BzA���R�h�hyC��=�Y� A���R�h�hyCJ��g� �@���R�h�hyCJ��g� �@���R�h�hyC)��ߵ�@���R�h�hyC2=�MI�@���R�h�hyCJ��g� �@���R�hmhmhmhm�Universal Position Blotter�j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  �LONG�j3  j3  �SHORT�j4  j3  j3  j3  j4  j4  j3  j3  j4  j4  �Trader��Client�j6  �Account�j5  j5  j5  j6  j7  j5  j5  j6  j7  j5  ]�}�(�labelId��CA4702731031��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(j:  j;  j<  j=  j>  jD  ua]�}�(j:  j;  j<  j=  j>  jD  ua]�}�(j:  j;  j<  j=  j>  jD  ua]�}�(j:  j;  j<  j=  j>  jD  ua]�}�(j:  �FR0000120628�j<  j=  j>  jD  ua]�}�(j:  jO  j<  j=  j>  jD  ua]�}�(j:  jO  j<  j=  j>  jD  ua]�}�(j:  jO  j<  j=  j>  jD  ua]�}�(j:  jO  j<  j=  j>  jD  ua]�}�(j:  j;  j<  j=  j>  jD  ua]�}�(j:  j;  j<  j=  j>  jD  ua]�}�(j:  j;  j<  j=  j>  jD  ua]�}�(j:  j;  j<  j=  j>  jD  ua]�}�(j:  �id:jp01�j<  �
parties.value�j>  jD  ua]�}�(j:  �id:ubs�j<  jc  j>  jD  ua]�}�(j:  �id:ubs�j<  jc  j>  jD  ua]�}�(j:  �
id:riskarb�j<  jc  j>  jD  ua]�}�(j:  �id:jp01�j<  jc  j>  jD  ua]�}�(j:  �id:jp01�j<  jc  j>  jD  ua]�}�(j:  �id:jp01�j<  jc  j>  jD  ua]�}�(j:  �id:ubs�j<  jc  j>  jD  ua]�}�(j:  �
id:riskarb�j<  jc  j>  jD  ua]�}�(j:  �id:jp01�j<  jc  j>  jD  ua]�}�(j:  �id:jp01�j<  jc  j>  jD  ua]�}�(j:  �
id:snow white�j<  jc  j>  jD  ua]�}�(j:  �
id:riskarb�j<  jc  j>  jD  ua]�}�(j:  �id:jp01�j<  jc  j>  jD  ua�jp01��ubs��ubs��riskarb��jp01��jp01��jp01��ubs��riskarb��jp01��jp01��
snow white��riskarb��jp01�]�(j9  ja  e]�(jF  je  e]�(jH  jh  e]�(jJ  jk  e]�(jL  jn  e]�(jN  jq  e]�(jQ  jt  e]�(jS  jw  e]�(jU  jz  e]�(jW  j}  e]�(jY  j�  e]�(j[  j�  e]�(j]  j�  e]�(j_  j�  e�Position�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j5  j6  j6  j7  j5  j5  j5  j6  j7  j5  j5  j6  j7  j5  hmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhmhm�jp01��ubs��ubs��riskarb��jp01��jp01��jp01��ubs��riskarb��jp01��jp01��
snow white��riskarb��jp01�et�bh^)��}�(hahhK ��h��R�(KK��hh�]�(j;  j;  j;  j;  j;  jO  jO  jO  jO  jO  j;  j;  j;  j;  et�bhohq)��ubhhK ��h��R�(KKK��hS�Cp                            	                                                                      �t�bh^)��}�(hahhK ��h��R�(KK��hh�]�(hmhmhm�JBR.CD�hmhmhmhm�CS.PA�hmhmhmj�  hmet�bhohq)��ubh^)��}�(hahhK ��h��R�(KK��hh�]�(j;  j;  j;  j;  j;  jO  jO  jO  jO  jO  j;  j;  j;  j;  et�bhohq)��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bhFNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h&h?et�bhFNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bhFNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h(h)h*h+h,h-h.h/h0h1h3h4h5h6h7h8h9h:h;h<h=h>h@hBet�bhFNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bhFNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhFNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hCat�bhFNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bhFNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h_�mgr_locs��builtins��slice���K KK��R�u}�(j*  huj+  j.  KK3K��R�u}�(j*  h}j+  j.  KKK��R�u}�(j*  h�j+  hhK ��h��R�(KK��h�i8�����R�(KhTNNNJ����J����K t�b�C�                                          	       
                                                                                                                       �t�bu}�(j*  j�  j+  j.  K
KK��R�u}�(j*  j�  j+  j.  KKK��R�u}�(j*  j�  j+  j.  KKK��R�u}�(j*  j�  j+  j.  KK K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.