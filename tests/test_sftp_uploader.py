import pytest
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.client.sftp import SftpClient
from swarm.schema.flow.bundle.components import ResourceConfig

from swarm_tasks.io.write.sftp_uploader import SftpUploader


@pytest.mark.skip(reason="fix extra fields not permitted error")
def test_sftp_uploader():

    # classes
    params = SftpUploader.params_class(sftp_file_path="upload")
    resources = SftpUploader.resources_class(sftp_client_key="target-sftp")

    data_types_task = SftpUploader(name="sftp-uploader", params=params)

    # clients
    resource_config = ResourceConfig(
        client_type="sftp",
        host="demo.wftpserver.com",
        username="demo-user",
        password="demo-user",
        port=2222,
        scheme="sftp",
    )
    clients = {"target-sftp": SftpClient(config=resource_config)}

    data_types_task.clients = clients
    # identifier = DotDict({"tenant": "schroders"})
    # context.swarm.identifier = identifier

    previous_result = ExtractPathResult(path="/tmp/test.txt")

    # Execute
    result = data_types_task.execute(
        result=previous_result, params=params, resources=resources
    )
    result
