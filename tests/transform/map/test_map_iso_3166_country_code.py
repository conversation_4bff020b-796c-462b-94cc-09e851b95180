from unittest.mock import patch

import pandas as pd
import pycountry
import pytest
from swarm.task.auditor import Auditor

from swarm_tasks.transform.map.map_iso_3166_country_code import MapIso3166CountryCode
from swarm_tasks.transform.map.map_iso_3166_country_code import Params


@pytest.fixture()
def empty_df() -> pd.DataFrame:
    """Empty source data frame"""
    df = pd.DataFrame({})
    return df


@pytest.fixture()
def source_df() -> pd.DataFrame:
    """Source data frame containing 8 records"""
    df = pd.DataFrame(
        {
            "__country__": [
                "United Kingdom",
                "USA",
                "France",
                "IN",
                "United Arab Emirates",
                pd.NA,
                "Invalid",
                "UK",
            ]
        }
    )
    return df


@pytest.fixture()
def params_fixture_alpha2() -> Params:
    params = Params(
        **{
            "source_country_column": "__country__",
            "target_country_column": "__country_code__",
            "target_country_code_type": "alpha_2",
        }
    )
    return params


@pytest.fixture()
def params_fixture_alpha3() -> Params:
    params = Params(
        **{
            "source_country_column": "__country__",
            "target_country_column": "__country_code__",
            "target_country_code_type": "alpha_3",
        }
    )
    return params


@pytest.fixture()
def expected_result_for_alpha2() -> pd.DataFrame:
    df = pd.DataFrame(
        {"__country_code__": ["GB", "US", "FR", "IN", "AE", pd.NA, pd.NA, "GB"]}
    )
    return df


@pytest.fixture()
def expected_result_for_alpha3() -> pd.DataFrame:
    df = pd.DataFrame(
        {"__country_code__": ["GBR", "USA", "FRA", "IND", "ARE", pd.NA, pd.NA, "GBR"]}
    )
    return df


class TestMapIso3166CountryCode:
    """Test suite for MapIso3166CountryCode"""

    @patch.object(MapIso3166CountryCode, "auditor")
    def test_empty_source_df(self, mock_auditor, empty_df, params_fixture_alpha2):
        """Test for the case where the source data frame is empty"""
        mock_auditor.return_value = Auditor(task_name="MapIso3166CountryCode")
        task = MapIso3166CountryCode(
            name="MapIso3166CountryCode", params=params_fixture_alpha2
        )
        result = task.execute(empty_df, params=params_fixture_alpha2)
        assert result.empty

    @patch.object(MapIso3166CountryCode, "auditor")
    def test_alpha2_conversion(
        self, mock_auditor, source_df, expected_result_for_alpha2, params_fixture_alpha2
    ):
        """Test for the case where the target is an alpha2 code"""
        mock_auditor.return_value = Auditor(task_name="MapIso3166CountryCode")
        task = MapIso3166CountryCode(
            name="MapIso3166CountryCode", params=params_fixture_alpha2
        )
        result = task.execute(source_df, params=params_fixture_alpha2)
        assert result.equals(expected_result_for_alpha2)

    @patch.object(MapIso3166CountryCode, "auditor")
    def test_alpha3_conversion(
        self, mock_auditor, source_df, expected_result_for_alpha3, params_fixture_alpha3
    ):
        """Test for the case where the target is an alpha3 code"""
        mock_auditor.return_value = Auditor(task_name="MapIso3166CountryCode")
        task = MapIso3166CountryCode(
            name="MapIso3166CountryCode", params=params_fixture_alpha3
        )
        result = task.execute(source_df, params=params_fixture_alpha3)
        assert result.equals(expected_result_for_alpha3)

    def test_pycountry_uk_value_throws_lookup_error(
        self, source_df, expected_result_for_alpha3, params_fixture_alpha3
    ):
        """
        pycountry does not currently handle the value 'UK'. It treats it as
        an invalid country, and raises a LookupError
        NOTE: If pycountry handles 'UK' values correctly in the future, this
        test (and the associated code which handles 'UK' values in the
        MapIso3166CountryCode task) can be removed.
        """
        with pytest.raises(LookupError):
            pycountry.countries.lookup("UK")
