import pandas as pd
import pytest
from pydantic import ValidationError

from swarm_tasks.transform.map.map_from_list import MapFromList
from swarm_tasks.transform.map.map_from_list import Params


@pytest.fixture()
def source_dataframe() -> pd.DataFrame:
    """Represents a source dataframe parsed from an input csv file."""
    df = pd.DataFrame(
        {"source": [["hello", "world"], "abc", pd.NA], "another_col": ["a", "b", "c"]}
    )
    return df


@pytest.fixture()
def source_dataframe_with_column_to_exclude_list_elements_by() -> pd.DataFrame:
    """Represents a source dataframe parsed from an input csv file."""
    df = pd.DataFrame(
        {
            "source": [["hello", "world"], ["abc"], pd.NA, ["foo", "bar"]],
            "another_col": ["world", "foo", "c", pd.NA],
        }
    )
    return df


@pytest.fixture()
def empty_df() -> pd.DataFrame:
    return pd.DataFrame()


class TestMapFromList:
    def test_default_params(self, source_dataframe):
        params = Params(
            source_attribute="source", target_attribute="target", item_index=0
        )

        mapping = MapFromList(name="test-map-to-nested", params=params)

        outcome_df = mapping.execute(source_frame=source_dataframe, params=params)

        expected_df = pd.DataFrame({"target": ["hello", pd.NA, pd.NA]})

        assert outcome_df.equals(expected_df)

    def test_param_index_less_than_0(self):
        with pytest.raises(ValidationError) as e:
            _ = Params(
                source_attribute="source", target_attribute="target", item_index=-1
            )
        assert (
            e.value.errors()[0]["msg"]
            == "ensure this value is greater than or equal to 0"
        )

    def test_param_index_greater_for_input_data(self, source_dataframe):
        params = Params(
            source_attribute="source", target_attribute="target", item_index=3
        )

        mapping = MapFromList(name="test-map-to-nested", params=params)

        outcome_df = mapping.execute(source_frame=source_dataframe, params=params)

        expected_df = pd.DataFrame({"target": [pd.NA, pd.NA, pd.NA]})

        assert outcome_df.equals(expected_df)

    def test_param_mask(self, source_dataframe):
        params = Params(
            source_attribute="source",
            target_attribute="target",
            item_index=0,
            mask="`another_col` == 'a'",
        )

        mapping = MapFromList(name="test-map-to-nested", params=params)

        outcome_df = mapping.execute(source_frame=source_dataframe, params=params)

        expected_df = pd.DataFrame({"target": ["hello", pd.NA, pd.NA]})

        assert outcome_df.equals(expected_df)

    def test_missing_params(self):
        with pytest.raises(ValueError) as e:
            Params(
                source_attribute="source",
                target_attribute="target",
            )

        assert e.match(
            "Either `item_index` or `column_to_exclude_list_elements_by` must be populated"
        )

    def test_invalid_params(self):
        with pytest.raises(ValueError) as e:
            Params(
                source_attribute="source",
                target_attribute="target",
                item_index=0,
                column_to_exclude_list_elements_by="foo",
            )

        assert e.match(
            "Only one of `item_index` or `column_to_exclude_list_elements_by` must be populated"
        )

    def test_empty_df(self, empty_df):
        params = Params(
            source_attribute="source",
            target_attribute="target",
            column_to_exclude_list_elements_by="another_col",
        )

        mapping = MapFromList(name="test-map-to-nested", params=params)

        outcome_df = mapping.execute(source_frame=empty_df, params=params)

        assert outcome_df.empty

    def test_df_missing_column(
        self, source_dataframe_with_column_to_exclude_list_elements_by
    ):
        params = Params(
            source_attribute="source",
            target_attribute="target",
            column_to_exclude_list_elements_by="another_col",
        )

        mapping = MapFromList(name="test-map-to-nested", params=params)

        outcome_df = mapping.execute(
            source_frame=pd.DataFrame(
                source_dataframe_with_column_to_exclude_list_elements_by.iloc[:, 0]
            ),
            params=params,
        )

        expected_result = pd.DataFrame(
            {"target": [["hello", "world"], ["abc"], pd.NA, ["foo", "bar"]]}
        )

        assert outcome_df.equals(expected_result)

    def test_df_exclude_by_column(
        self, source_dataframe_with_column_to_exclude_list_elements_by
    ):
        params = Params(
            source_attribute="source",
            target_attribute="target",
            column_to_exclude_list_elements_by="another_col",
        )

        mapping = MapFromList(name="test-map-to-nested", params=params)

        outcome_df = mapping.execute(
            source_frame=source_dataframe_with_column_to_exclude_list_elements_by,
            params=params,
        )

        expected_result = pd.DataFrame(
            {"target": [["hello"], ["abc"], pd.NA, ["foo", "bar"]]}
        )

        assert outcome_df.equals(expected_result)
