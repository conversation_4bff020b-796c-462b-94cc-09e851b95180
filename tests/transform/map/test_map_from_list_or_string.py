from typing import Dict
from typing import Tuple

import pandas as pd
import pytest
from prefect.engine import signals

from swarm_tasks.transform.map.map_from_list_or_string import MapFromListOrString
from swarm_tasks.transform.map.map_from_list_or_string import Params


@pytest.fixture()
def params_fixture() -> Params:
    return Params(
        **{"source_attribute": "ff_16118", "target_attribute": "__output_col_1__"}
    )


@pytest.fixture()
def empty_frame() -> pd.DataFrame:
    return pd.DataFrame({})


@pytest.fixture()
def required_col_missing_df() -> pd.DataFrame:
    return pd.DataFrame({"dummy": [1, 3]})


@pytest.fixture()
def source_frame_with_all_nulls() -> pd.DataFrame:
    return pd.DataFrame({"ff_16118": [pd.NA, pd.NA]})


@pytest.fixture()
def expected_result_with_all_nulls() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "__output_col_1__": [pd.NA, pd.NA],
        }
    )


@pytest.fixture()
def source_frame_with_no_list_elements() -> pd.DataFrame:
    return pd.DataFrame({"ff_16118": ["1", "2", "3"]})


@pytest.fixture()
def expected_result_with_no_list_elements() -> pd.DataFrame:
    return pd.DataFrame({"__output_col_1__": ["1", "2", "3"]})


@pytest.fixture()
def source_frame_with_lists() -> pd.DataFrame:
    return pd.DataFrame({"ff_16118": ["1", ["2", "3"], "4", ["5", "6", "7"], pd.NA]})


@pytest.fixture()
def expected_result_with_lists() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "__output_col_1__": ["1", "2", "4", "5", pd.NA],
        }
    )


@pytest.fixture()
def fixtures_dict(
    source_frame_with_all_nulls: pd.DataFrame,
    expected_result_with_all_nulls: pd.DataFrame,
    source_frame_with_no_list_elements: pd.DataFrame,
    expected_result_with_no_list_elements: pd.DataFrame,
    source_frame_with_lists: pd.DataFrame,
    expected_result_with_lists: pd.DataFrame,
) -> Dict[str, Tuple[pd.DataFrame, pd.DataFrame]]:

    return {
        "all_nulls": (source_frame_with_all_nulls, expected_result_with_all_nulls),
        "no_list_elements": (
            source_frame_with_no_list_elements,
            expected_result_with_no_list_elements,
        ),
        "list_elements": (
            source_frame_with_lists,
            expected_result_with_lists,
        ),
    }


class TestMapFromListOrString:
    """Test Suite for MapFromListOrString"""

    def test_empty_source_frame(
        self, params_fixture: Params, empty_frame: pd.DataFrame
    ):
        with pytest.raises(signals.SKIP):
            self.run_task(params=params_fixture, source_frame=empty_frame)

    def test_source_frame_with_missing_required_column(
        self, params_fixture: Params, required_col_missing_df: pd.DataFrame
    ):
        with pytest.raises(signals.SKIP):
            self.run_task(params=params_fixture, source_frame=required_col_missing_df)

    @pytest.mark.parametrize(
        argnames="fixture_to_pick",
        argvalues=[
            "all_nulls",
            "no_list_elements",
            "list_elements",
        ],
    )
    def test_successful_result(
        self,
        fixture_to_pick,
        fixtures_dict: Dict[str, Tuple[pd.DataFrame, pd.DataFrame]],
        params_fixture: Params,
    ):
        """Paremetrized test for all possible cases"""
        source_frame, expected_result = fixtures_dict.get(fixture_to_pick)
        result = self.run_task(params=params_fixture, source_frame=source_frame)
        assert not pd.testing.assert_frame_equal(left=result, right=expected_result)

    @staticmethod
    def run_task(params: Params, source_frame: pd.DataFrame) -> pd.DataFrame:
        task = MapFromListOrString(name="MapFromListOrString", params=params)
        return task.execute(source_frame=source_frame, params=params)
