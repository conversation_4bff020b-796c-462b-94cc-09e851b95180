import pandas as pd
import pytest

from swarm_tasks.transform.map import map_boolean_fields


@pytest.fixture()
def source_dataframe() -> pd.DataFrame:
    """Represents a source dataframe parsed from an input csv file."""
    df = pd.DataFrame(
        {
            "Is Pad?": ["1", "true", "t", "f", "0"],
            "Transmission of Order": ["off", "n", pd.NA, "test", "1"],
        }
    )
    return df


class TestMapBooleanFields:
    def test_execute_with_basic_params(self, source_dataframe):
        params = map_boolean_fields.Params(
            source_attribute="Is Pad?",
            target_attribute="marDetails.isPersonalAccountDealing",
        )
        mapping = map_boolean_fields.MapBooleanFields(
            name="test-map-boolean-fields", params=params
        )
        outcome_df = mapping.execute(source_frame=source_dataframe, params=params)
        expected_df = pd.DataFrame(
            {"marDetails.isPersonalAccountDealing": [True, True, True, False, False]}
        )
        assert outcome_df.equals(expected_df)

    def test_execute_with_no_values_to_map(self, source_dataframe):
        source_dataframe["Is Pad?"] = pd.NA
        params = map_boolean_fields.Params(source_attribute="", target_attribute="")
        mapping = map_boolean_fields.MapBooleanFields(
            name="test-map-boolean-fields", params=params
        )
        outcome_df = mapping.execute(source_frame=source_dataframe, params=params)

        expected_df = pd.DataFrame(pd.NA, index=[0, 1, 2, 3, 4], columns=[""])
        assert outcome_df.equals(expected_df)

    def test_execute_invalid_values_to_map(self, source_dataframe):
        source_dataframe["Transmission of Order"] = [
            "test1",
            "test2",
            "test3",
            "test4",
            "test5",
        ]
        params = map_boolean_fields.Params(
            source_attribute="Transmission of Order",
            target_attribute="transmissionDetails.orderTransmissionIndicator",
        )
        mapping = map_boolean_fields.MapBooleanFields(
            name="test-map-boolean-fields", params=params
        )
        outcome_df = mapping.execute(source_frame=source_dataframe, params=params)

        expected_df = pd.DataFrame(
            {
                "transmissionDetails.orderTransmissionIndicator": [
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                ]
            }
        )
        assert outcome_df.equals(expected_df)

    def test_params_raise_on_missing_attribute(self, source_dataframe):
        params = map_boolean_fields.Params(
            source_attribute="Test",
            target_attribute="marDetails.isPersonalAccountDealing",
        )

        mapping = map_boolean_fields.MapBooleanFields(
            name="test-map-boolean-fields", params=params
        )

        outcome_df = mapping.execute(source_frame=source_dataframe, params=params)
        expected_df = pd.DataFrame(
            {"marDetails.isPersonalAccountDealing": [pd.NA, pd.NA, pd.NA, pd.NA, pd.NA]}
        )

        assert outcome_df.equals(expected_df)
