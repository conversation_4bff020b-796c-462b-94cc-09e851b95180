import numpy as np
import pandas as pd
import pytest

from swarm_tasks.transform.map.map_from_mapping_table import MapFromMappingTable
from swarm_tasks.transform.map.map_from_mapping_table import Params


@pytest.fixture()
def params_fix() -> Params:
    params = Params(
        source_attribute="source_attr",
        target_attribute="target_attr",
        no_match_default_attribute="no_match_attr",
        matching_column="match_col",
        output_column="output_col",
    )
    return params


@pytest.fixture()
def empty_source_df_fix() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    return pd.DataFrame()


@pytest.fixture()
def source_data_fix() -> pd.DataFrame:
    data = {
        "source_attr": ["AA", pd.NA, "DD", "EE"],
        "no_match_attr": ["ZZ0", "ZZ1", "ZZ2", "ZZ3"],
    }
    return pd.DataFrame(data)


@pytest.fixture()
def mapping_table_fix() -> pd.DataFrame:
    data = {
        "match_col": ["AA", "BB", "CC", "DD"],
        "output_col": ["AAA", "BBB", "CCC", np.nan],
    }
    return pd.DataFrame(data)


class TestMapFromMappingTable(object):
    """
    Test cases for "TestMapFromMappingTable" class
    """

    def test_empty_input_df_without_source_columns(
        self,
        empty_source_df_fix: pd.DataFrame,
        mapping_table_fix: pd.DataFrame,
        params_fix: Params,
    ):
        task = MapFromMappingTable(name="test-contract-market-data", params=params_fix)

        result = task.execute(
            source_frame=empty_source_df_fix,
            mapping_table=mapping_table_fix,
            params=params_fix,
        )

        expected = pd.DataFrame(index=empty_source_df_fix.index)

        assert result.equals(expected)

    def test_normal_source_data(
        self,
        source_data_fix: pd.DataFrame,
        mapping_table_fix: pd.DataFrame,
        params_fix: Params,
    ):
        task = MapFromMappingTable(name="test-contract-market-data", params=params_fix)

        result = task.execute(
            source_frame=source_data_fix,
            mapping_table=mapping_table_fix,
            params=params_fix,
        )

        expected = pd.DataFrame({"target_attr": ["AAA", "ZZ1", "ZZ2", "ZZ3"]})

        assert result.equals(expected)
