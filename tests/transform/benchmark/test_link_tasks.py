import json
from pathlib import Path

import pandas as pd
import pytest
from prefect import context
from prefect.utilities.collections import DotDict
from se_elasticsearch.repository import get_repository_by_cluster_version
from swarm.schema.flow.bundle.components import ResourceConfig

from swarm_tasks.transform.steeleye.link.instrument import LinkInstrument
from swarm_tasks.transform.steeleye.link.parties import LinkParties


@pytest.mark.skip(reason="needs realignment")
def test_benchmark_link_parties():
    data = json.loads(
        Path(__file__)
        .parent.joinpath("data", "market_identifiers_parties.json")
        .read_text()
    )

    df = pd.DataFrame(data)

    # classes
    resources = LinkParties.resources_class(es_client_key="tenant-data")

    data_types_task = LinkParties(name="link_parties")

    # clients
    resource_config = ResourceConfig(
        host="elasticsearch.dev-schro.steeleye.co",
        port=9200,
    )
    clients = {
        "tenant-data": get_repository_by_cluster_version(
            resource_config=resource_config
        )
    }

    data_types_task.clients = clients
    identifier = DotDict({"tenant": "schroders"})
    context.swarm = DotDict()
    context.swarm.identifier = identifier

    # Execute
    result = data_types_task.execute(source_frame=df, resources=resources)
    result


@pytest.mark.skip(reason="needs realignment")
def test_benchmark_link_instruments():
    """
    Original
    [2020-05-30 18:55:19] INFO - prefect.link_instrument | source cache for 29 unique label ids
    [2020-05-30 18:55:19] INFO - prefect.link_instrument | scrolling instruments for 29 identifiers
    [2020-05-30 18:55:20] INFO - prefect.link_instrument | map instruments to cache
    2.04s call

    :return:
    """
    df = pd.read_csv(
        Path(__file__)
        .parent.joinpath("data", "market_identifiers_instrument.csv")
        .as_posix()
    )

    df["marketIdentifiers.instrument"] = df["marketIdentifiers.instrument"].apply(eval)

    # classes
    resources = LinkInstrument.resources_class(ref_data_key="reference-data")

    from swarm_tasks.transform.steeleye.link.instrument import Params
    from swarm.task.auditor import Auditor

    auditor = Auditor(task_name="link instrument")

    params = Params(
        venue_attribute="transactionDetails.venue",
        currency_attribute="transactionDetails.priceCurrency",
        identifiers_path="marketIdentifiers.instrument",
    )

    data_types_task = LinkInstrument(name="link_instrument", params=params)
    data_types_task._auditor = auditor

    # clients
    resource_config = ResourceConfig(host="elasticsearch.srp.steeleye.co", port=9201)
    clients = {
        "reference-data": get_repository_by_cluster_version(
            resource_config=resource_config
        )
    }

    data_types_task.clients = clients
    identifier = DotDict({"tenant": "schroders"})
    context.swarm = DotDict()
    context.swarm.identifier = identifier

    # Execute
    result = data_types_task.execute(
        source_frame=df, params=params, resources=resources
    )
    result
