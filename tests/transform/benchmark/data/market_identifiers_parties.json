{"marketIdentifiers.parties": [[{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:k6q0w1ps1l1o4iql9c32", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:sc105644", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:sc105644", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:7h6glxdrugqfu57rne97", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu021551", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu021551", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:7h6glxdrugqfu57rne97", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu021551", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu021551", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:7h6glxdrugqfu57rne97", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu021551", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu021551", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:7h6glxdrugqfu57rne97", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu021551", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu021551", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:7h6glxdrugqfu57rne97", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu021551", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu021551", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:7h6glxdrugqfu57rne97", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu021551", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu021551", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:4pquhn3jpfgfnf3bb653", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:sc105801", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:sc105801", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:54930087vymsigk4ty26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:54930087vymsigk4ty26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:54930087vymsigk4ty26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:54930087vymsigk4ty26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:54930087vymsigk4ty26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:5493003p29lapf3hfv10", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:54930087vymsigk4ty26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:nore", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}], [{"labelId": "lei:f3js33dei6xq4zbptn86", "path": "parties.counterparty", "type": "OBJECT"}, {"labelId": "lei:2138004pm63r6524xr26", "path": "parties.executingEntity", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:eu017525", "path": "parties.trader", "type": "ARRAY"}]]}