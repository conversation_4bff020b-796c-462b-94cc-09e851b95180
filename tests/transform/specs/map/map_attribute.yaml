task:
  package: swarm_tasks.transform.map.map_attribute
  class: MapAttribute
tests:
- name: map foo:A to bar:A
  test_params:
    params:
      source_attribute: foo
      target_attribute: bar
  input:
    data:
      foo:
      - A
  expected:
    target:
      data:
        bar:
        - A
    audit:
      data: []
- name: strip whitespace
  test_params:
    params:
      source_attribute: foo
      target_attribute: bar
      strip_whitespace: True
  input:
    data:
      foo:
      - "A     "
  expected:
    target:
      data:
        bar:
        - A
    audit:
      data: []