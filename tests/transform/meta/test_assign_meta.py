import re
from itertools import chain
from pathlib import Path
from typing import Type

import pandas as pd
import pytest
from prefect import context
from prefect.utilities.collections import DotDict
from se_core_tasks.core.auditor import parse_audit_messages
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import (
    SteelEyeSchemaBaseModelES8,
)
from se_elastic_schema.models import find_model
from se_elastic_schema.models import Order
from se_elastic_schema.models import RTS22Transaction
from se_elastic_schema.static.mifid2 import DeliveryType
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.reference import InstrumentIdCodeType
from se_elastic_schema.steeleye_record_validations.categories import flags
from se_elasticsearch.repository.static import MetaPrefix
from swarm.client.meta_helper import Meta
from swarm.conf import Settings
from swarm.conf import SettingsCls
from swarm.schema.task.auditor.model import AGGREGATION_DELIMITER
from swarm.task.auditor import Auditor
from swarm.utilities.indict import Indict

from swarm_tasks.transform.steeleye.meta import assign_meta as am

Settings.FLOW_ID = "test.dev.steeleye.co:some-random-bundle"

TEST_RECORD = {
    "&id": "dummyID",
    "&hash": "dummyHash",
    "&key": "Instrument:dummyID:1662140154253",
    "&model": "Instrument",
    "&timestamp": 1662140154253,
    "&user": "system",
    "&version": 1,
    "cfiAttribute1": "Energy",
    "cfiAttribute2": "Not Applicable/Undefined",
    "cfiAttribute3": "Contract for difference",
    "cfiAttribute4": "Cash",
    "cfiCategory": "Forwards",
    "cfiGroup": "Commodities",
    "commoditiesOrEmissionAllowanceDerivativeInd": True,
    "derivative": {
        "deliveryType": DeliveryType.CASH,
        "priceMultiplier": 1.0,
        "underlyingInstruments": [
            {
                "underlyingInstrumentClassification": "FCECSX",
                "underlyingInstrumentCode": "dummyIDCode",
            }
        ],
    },
    "ext": {
        "alternativeInstrumentIdentifier": "dummyAII",
        "bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives",
        "bestExAssetClassSub": "Other commodities derivatives and emission allowances derivatives",
        "instrumentIdCodeType": InstrumentIdCodeType.OTHR,
        "instrumentUniqueIdentifier": "dummyID",
        "priceNotation": PriceNotation.MONE,
        "quantityNotation": QuantityNotation.UNIT,
        "underlyingInstruments": [
            {
                "derivative": {"expiryDate": "2022-04-06"},
                "instrumentIdCode": "dummyIDCode",
                "venue": {"tradingVenue": "IFEU"},
            }
        ],
    },
    "instrumentClassification": "JTJXXC",
    "instrumentClassificationEMIRAssetClass": "CO",
    "instrumentClassificationEMIRContractType": "CD",
    "instrumentClassificationEMIRProductType": "CFD",
    "instrumentFullName": "dummyName",
    "notionalCurrency1": "USD",
    "sourceKey": "dummyKey.xml_cfd",
    "venue": {"financialInstrumentShortName": "dummyShortName", "tradingVenue": "XXXX"},
}


@pytest.fixture
def source_frame_model_with_id_properties():
    # source_frame for the RTS22Transactions model which has ID_PROPERTIES
    df = pd.read_pickle(
        Path(__file__)
        .parent.joinpath("data", "assign_meta_with_id_properties.pkl")
        .as_posix()
    )
    df["transactionDetails.tradingDateTime"] = pd.to_datetime(
        df["transactionDetails.tradingDateTime"]
    )
    return df


@pytest.fixture
def source_frame_model_with_meta_parent():
    # source_frame for the OrderState model which must have &parent populated by the AssignMeta task
    df = pd.read_pickle(
        Path(__file__)
        .parent.joinpath("data", "assign_meta_with_meta_parent.pkl")
        .as_posix()
    )
    return df


@pytest.fixture
def call_source_frame():
    df = pd.DataFrame(
        {
            "identifiers.fromId": ["442078900000", "442078900033"],
            "identifiers.toIds": [
                ["to_id_1", "442078900033"],
                ["to_id_1", "442078900055"],
            ],
            "__meta_model__": ["Call", "Call"],
            "__swarm_raw_index__": [0, 1],
        }
    )

    return df


@pytest.fixture()
def invalid_order_record():
    records = [
        {
            "records": {
                "buySell": "SELL",
                "date": "2022-04-29",
                "executionDetails": {
                    "buySellIndicator": "SELL",
                    "orderStatus": "NEWO",
                },
                "id": "8210424527490|21436",
                "orderIdentifiers": {
                    "internalOrderIdCode": "1650823225557",
                    "orderIdCode": "8210424527490|21436",
                },
                "timestamps": {
                    "orderReceived": "2022-04-29T06:48:54.669000Z",
                    "orderSubmitted": "2022-04-29T06:48:54.669000Z",
                    "tradingDateTime": "2022-04-29T06:48:54.669000Z",
                },
                "traderFileIdentifier": "id:glane",
                "transactionDetails": {
                    "buySellIndicator": "SELL",
                    "tradingDateTime": "2022-04-29T06:48:54.669000Z",
                },
                "some_extra_field": "not_allowed",
            }
        }
    ]
    return pd.DataFrame(records)


class TestAssignMeta:

    EXPECTED_COLUMNS = sorted(
        [
            "&model",
            "&timestamp",
            "&user",
            "&version",
            "&id",
            "&key",
            "&hash",
            "&validationErrors",
            "&parent",
            "&uniqueProps",
        ]
    )

    def test_source_frame_model_with_id_properties(
        self, source_frame_model_with_id_properties
    ):

        task, params = self._init_task(
            tenant_configuration={"trPIEnrichmentEnabled": True}
        )
        result = task.execute(source_frame_model_with_id_properties, params)
        assert sorted(result.columns) == self.EXPECTED_COLUMNS

    @pytest.mark.asyncio
    async def test_source_frame_without_meta_parent(
        self, source_frame_model_with_id_properties
    ):

        task, params = self._init_task(
            tenant_configuration={"trPIEnrichmentEnabled": True}
        )
        source_frame_models = source_frame_model_with_id_properties[
            "__meta_model__"
        ].unique()
        instantiated_model_dict = {
            resolved_model: _find_model(resolved_model)
            for resolved_model in source_frame_models
        }

        # this also tests that tenant_configuration
        # is passed on to the steeleye validations
        result = await task.apply_meta(
            df=source_frame_model_with_id_properties,
            params=params,
            instantiated_model_dict=instantiated_model_dict,
        )

        assert result["&parent"].values.tolist() == [None, None]

    @pytest.mark.asyncio
    async def test_source_frame_with_meta_parent(
        self, source_frame_model_with_meta_parent
    ):

        task, params = self._init_task(
            tenant_configuration={"trPIEnrichmentEnabled": True, "foo": "bar"}
        )
        source_frame_model_with_meta_parent[params.model_attribute] = "Order"
        instantiated_model_dict = {"Order": Order}
        params.parent_attribute = "__meta_parent__"

        # this also tests that an invalid TenantConfiguration
        # is parsed as an Addict object
        result = await task.apply_meta(
            df=source_frame_model_with_meta_parent,
            params=params,
            instantiated_model_dict=instantiated_model_dict,
        )

        assert result["&parent"].values.tolist() == [
            "1621555201669057809:1:NEWO",
            "1621555200085088197:2:NEWO",
        ]

    def test_source_frame_model_max_meta_id_length(
        self, source_frame_model_with_id_properties
    ):

        task, params = self._init_task(
            tenant_configuration={"trPIEnrichmentEnabled": True, "foo": "bar"}
        )

        source_frame_model_with_id_properties["reportDetails.transactionRefNo"] = (
            "test" * 512
        )
        result = task.execute(source_frame_model_with_id_properties, params)
        assert sorted(result.columns) == self.EXPECTED_COLUMNS
        # sha512 hash is of 128 character length
        assert re.match(r"^[0-9a-f]{128}$", result["&id"][0])  # check if id is hash

    @pytest.mark.asyncio
    async def test_assign_meta_for_call(self, call_source_frame):

        task, params = self._init_task(tenant_configuration={})
        source_frame_models = call_source_frame["__meta_model__"].unique()
        instantiated_model_dict = {
            resolved_model: _find_model(resolved_model)
            for resolved_model in source_frame_models
        }
        result = await task.apply_meta(
            df=call_source_frame,
            params=params,
            instantiated_model_dict=instantiated_model_dict,
        )
        assert result["&model"].tolist() == ["Call", "Call"]
        # assert result["&timestamp"].tolist() == [1636122270357, 1636122270357]
        assert result["&user"].tolist() == ["system", "system"]
        assert result["&version"].tolist() == [1, 1]
        assert result["&id"].tolist() == [
            "442078900000:['442078900033', 'to_id_1']",
            "442078900033:['442078900055', 'to_id_1']",
        ]
        # assert result["&key"].tolist() == ["Call:442078900000:['442078900033', 'to_id_1']:1636122270357", "Call:442078900033:['442078900055', 'to_id_1']:1636122270357"]
        # The hashes had to be changed as se_elastic_schema
        # 0.0.60 populated transcribed default value as false
        assert result["&hash"].tolist() == [
            "47009a0496bd0cfa0a614f97897610861a6a44757740309a6b7d008369935536",
            "613c3adbf47202241b21dfb29a7ea41b69ebf22365bce8bca9edb864c3185c0f",
        ]
        assert result["&uniqueProps"].tolist() == [
            ["442078900000", "442078900033", "to_id_1"],
            ["442078900033", "442078900055", "to_id_1"],
        ]

    @pytest.mark.asyncio
    async def test_assign_meta_for_order_with_excluded_validation_errors(
        self, mocker, source_frame_model_with_id_properties
    ):
        task, params = self._init_task(tenant_configuration={})

        source_frame_model_with_id_properties[
            params.model_attribute
        ] = "RTS22Transaction"
        instantiated_model_dict = {"RTS22Transaction": RTS22Transaction}
        params.parent_attribute = "__meta_parent__"

        # Changing data so that SteeleyeValidation349 is triggered
        source_frame_model_with_id_properties[
            "instrumentDetails.instrument.commoditiesOrEmissionAllowanceDerivativeInd"
        ] = False
        source_frame_model_with_id_properties[
            "tradersAlgosWaiversIndicators.commodityDerivativeIndicator"
        ] = True

        result = await task.apply_meta(
            df=source_frame_model_with_id_properties,
            params=params,
            instantiated_model_dict=instantiated_model_dict,
        )

        # Assert that the validation error is present in the record
        assert flags.SteeleyeValidation294.code in list(
            map(
                lambda x: x["code"],
                chain.from_iterable(result["&validationErrors"].tolist()),
            )
        )

        mock_realm = mocker.patch.object(
            SettingsCls, "tenant", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "dummy"
        mock_realm = mocker.patch.object(
            SettingsCls, "bundle", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "tr-schroders-brs"

        result = await task.apply_meta(
            df=source_frame_model_with_id_properties,
            params=params,
            instantiated_model_dict=instantiated_model_dict,
        )

        # Assert that the validation error not is present
        # in the record for `dummy` and `tr-schroders-brs`
        assert flags.SteeleyeValidation294.code not in list(
            map(
                lambda x: x["code"],
                chain.from_iterable(result["&validationErrors"].tolist()),
            )
        )

    def test_assign_meta_pydantic_error_audit(self, invalid_order_record):
        task, params = self._init_task(tenant_configuration={})
        meta_field_record = {"__meta_model__": "Order", "__swarm_raw_index__": 0}
        task.add_steel_eye_meta(
            record=invalid_order_record["records"].to_dict()[0],
            meta_field_record=meta_field_record,
            instantiated_model_dict=dict(Order=_find_model("Order")),
            meta_timestamp=0,
            params=params,
        )
        assert (
            parse_audit_messages(task.auditor.to_dataframe().loc[0, "ctx.error"])
            == f"Order {AGGREGATION_DELIMITER} "
            f"['some_extra_field - extra fields not permitted'] {AGGREGATION_DELIMITER} id: 8210424527490|21436"
        )

    @pytest.mark.asyncio
    async def test_source_frame_with_invalid_models(
        self, source_frame_model_with_meta_parent
    ):
        task, params = self._init_task()
        source_frame_model_with_meta_parent[params.model_attribute] = "Order"
        instantiated_model_dict = {"Order": Order}
        params.parent_attribute = "__meta_parent__"
        source_frame_model_with_meta_parent["__meta_model__"].iloc[0] = ""
        result = await task.apply_meta(
            df=source_frame_model_with_meta_parent,
            params=params,
            instantiated_model_dict=instantiated_model_dict,
        )

        assert result["&parent"].fillna("").values.tolist() == [
            "",
            "1621555200085088197:2:NEWO",
        ]

    @pytest.mark.asyncio
    async def test_source_frame_with_invalid_record_which_cannot_be_unflattened(
        self, mocker, source_frame_model_with_meta_parent
    ):

        task, params = self._init_task()
        source_frame_models = source_frame_model_with_meta_parent[
            "__meta_model__"
        ].unique()
        instantiated_model_dict = {
            resolved_model: _find_model(resolved_model)
            for resolved_model in source_frame_models
        }
        params.parent_attribute = "__meta_parent__"

        # Mock exception while performing Indict operations
        mock_indict_exception = mocker.patch.object(
            Indict,
            "flatten",
        )
        mock_indict_exception.side_effect = Exception()

        result = await task.apply_meta(
            df=source_frame_model_with_meta_parent,
            params=params,
            instantiated_model_dict=instantiated_model_dict,
        )

        assert result.empty

    @staticmethod
    def _init_task(tenant_configuration=None):

        auditor = Auditor(task_name="assign meta")
        identifier = DotDict({"tenant": "pinafore"})
        context.swarm = DotDict()
        context.swarm.identifier = identifier
        context.swarm.tenant_configuration = tenant_configuration
        params = am.Params(model_attribute="__meta_model__")
        assign_meta_task = am.AssignMeta(name="assign-meta", params=params)
        assign_meta_task.clients = DotDict(
            {"tenant-data": DotDict({"meta": Meta(prefix=MetaPrefix.AMPERSAND)})}
        )
        assign_meta_task._auditor = auditor
        return assign_meta_task, params


def _find_model(model_name) -> Type[SteelEyeSchemaBaseModelES8]:

    res = None
    try:
        res = find_model(model_name)
    except ImportError:
        if model_name == "OrderState":
            res = Order

    return res
