��n      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKJ��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier��!sellerDecisionMakerFileIdentifier�� buyerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��buyer��buyerDecisionMaker��clientIdentifiers.client��counterparty��reportDetails.executingEntity��1tradersAlgosWaiversIndicators.executionWithinFirm��:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm��seller��sellerDecisionMaker��trader��instrumentDetails.instrument��marketIdentifiers��__meta_model__��sourceIndex��dataSourceName�� executionDetails.tradingCapacity��"transactionDetails.tradingCapacity�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��#transactionDetails.quantityNotation��+executionDetails.passiveAggressiveIndicator��transactionDetails.recordType��date��timestamps.orderReceived��!executionDetails.buySellIndicator��executionDetails.orderType��!transactionDetails.positionEffect��timestamps.orderSubmitted��"transactionDetails.tradingDateTime��timestamps.tradingDateTime��timestamps.orderStatusUpdated��$orderIdentifiers.internalOrderIdCode��"orderIdentifiers.aggregatedOrderId��orderIdentifiers.orderIdCode��id��&executionDetails.outgoingOrderAddlInfo��buySell��executionDetails.limitPrice��executionDetails.stopPrice�� priceFormingData.initialQuantity��"priceFormingData.remainingQuantity��priceFormingData.tradedQuantity��transactionDetails.trailId��#transactionDetails.buySellIndicator��executionDetails.validityPeriod��	sourceKey��transactionDetails.venue��reportDetails.transactionRefNo��!orderIdentifiers.transactionRefNo�� transactionDetails.ultimateVenue��executionDetails.orderStatus��__meta_parent__��priceFormingData.price��transactionDetails.price��"bestExecutionData.rts27UtcTimeBand�� bestExecutionData.rts27ValueBand��bestExecutionData.largeInScale��#bestExecutionData.pendingDisclosure��bestExecutionData.timeToFill��+bestExecutionData.timeAcceptExecuteReceived��)bestExecutionData.timeAcceptExecutePlaced��)bestExecutionData.timeRfqResponseReceived��'bestExecutionData.timeRfqResponsePlaced��#bestExecutionData.transactionVolume�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C              �t�bhpNu��R�e]�(hhK ��h��R�(KK3K��h!�]�(�id:msffr��id:msffr��id:msffr��id:eurex��id:eurex��id:msffr��id:eurex��id:eurex��pandas._libs.missing��NA���h�h�h�h�h��	id:mnp001��	id:coh001��id:2��id:2��id:64799��id:65676�]�}�(hp�id:MSFFR��&key��
MarketPerson:�ua]�}�(hp�id:EUREX�h�h�uah�h�]�}�(hp�id:2��&key��MarketCounterparty:�ua]�}�(hp�id:2�h�h�ua}�(hp�id:EUREX��&key�h�u}�(hph�h�h�u}�(hph��&key�h�u}�(hp�id:MSFFR�h�h�u}�(hp�	id:MNP001��&key�h�u}�(hp�	id:COH001�h�h�uh�h�]�}�(hph��&key�h�ua]�}�(hph�h�h�uah�h�]�}�(hp�id:64799��&key�h�ua]�}�(hp�id:65676�h�h�uah�h�]�(}�(�labelId��5578488��path�h9�type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(hƌid:msffr�h�h/h�ȟARRAY���R�u}�(hƌid:msffr�hȌreportDetails.executingEntity�h�h�u}�(hƌid:eurex�h�h6h�h�u}�(hƌid:eurex�h�h2h�h�u}�(hƌ	id:mnp001�hȌ1tradersAlgosWaiversIndicators.executionWithinFirm�h�h�u}�(hƌid:2�hȌclientIdentifiers.client�h�h�u}�(hƌid:64799�h�h8h�h�ue]�(}�(hƌ4156615�h�h9h�h�u}�(hƌid:eurex�h�h/h�h�u}�(hƌid:msffr�h�h�h�h�u}�(hƌid:msffr�h�h6h�h�u}�(hƌid:eurex�h�h2h�h�u}�(hƌ	id:coh001�h�h�h�h�u}�(hƌid:2�h�h�h�h�u}�(hƌid:65676�h�h8h�h�ue�
OrderState�h��0��2��EUREX�h��AOTC�h��AOTC�h��EUR�h��MONE�h��UNIT�h��PASV�h��Market Side�h��
2021-05-21��
2021-05-21��2021-05-21T07:14:35.801000Z��2021-05-21T06:20:20.744000Z��BUYI��SELL��Limit�j  �Open�j  j  j  j  j  j  j  j  j  h�h��
1621550100811��
1621550100749��1621555201669057809��1621555200085088197�j
  j  h�h��1��2�G@c��G�{G@�<     h�h�h�h�G�      G�      et�bhhK ��h��R�(KKK��h�f8�����R�(Kh~NNNJ����J����K t�b�C       @       @�t�bhhK ��h��R�(KK
K��h!�]�(�1621581275797315162��1621578020730902321�j  j  h�h���s3://tom.uat.steeleye.co/fix/order-feed-eurex-fix/20210521/13045417a702981a7c8a2d46897ba1bea11b2207ee081890078ea33a6c50e713_4152.fix���s3://tom.uat.steeleye.co/fix/order-feed-eurex-fix/20210521/162411caa0c2ff9fa0d920c3c5e281e5e8da8e967ff208aad6950e0e2b309dd5_3988.fix��XEUR��XEUR��
3526847491��
1127326751�j$  j%  j"  j#  �PARF�j&  �1621555201669057809:1:NEWO��1621555200085088197:2:NEWO�et�bhhK ��h��R�(KKK��j  �C {�G��c@     <�@{�G��c@     <�@�t�bhhK ��h��R�(KK
K��h!�]�(NNKK����h�h�h�h�h�h�h�h�h�h�h�h�et�be]�(h
h}�(hhhK ��h��R�(KK3��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<h=h>h?h@hAhBhChDhEhFhGhHhIhJhKhLhMhNhOhPhQhRhShThUhVhWet�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bhpNu��R�h
h}�(hhhK ��h��R�(KK
��h!�]�(hYhZh[h\h]h^h_h`hahbet�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hchdet�bhpNu��R�h
h}�(hhhK ��h��R�(KK
��h!�]�(hehfhghhhihjhkhlhmhnet�bhpNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���K K3K��R�u}�(jk  j  jl  jo  K3K4K��R�u}�(jk  j  jl  jo  K4K>K��R�u}�(jk  j,  jl  jo  K>K@K��R�u}�(jk  j2  jl  jo  K@KJK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.