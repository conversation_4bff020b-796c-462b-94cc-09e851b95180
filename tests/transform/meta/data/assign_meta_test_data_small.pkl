���3      �pandas.core.frame��	DataFrame���)��}�(�_data��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK ��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(� transactionDetails.priceCurrency��#transactionDetails.quantityCurrency��)transactionDetails.upFrontPaymentCurrency��sourceIndex��	sourceKey��__meta_model__�� transactionDetails.ultimateVenue��transactionDetails.price��!transactionDetails.upFrontPayment��3tradersAlgosWaiversIndicators.shortSellingIndicator��#transactionDetails.buySellIndicator�� transactionDetails.priceNotation��#transactionDetails.quantityNotation��:tradersAlgosWaiversIndicators.commodityDerivativeIndicator��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��.transmissionDetails.orderTransmissionIndicator��*transactionDetails.complexTradeComponentId��transactionDetails.netAmount��3tradersAlgosWaiversIndicators.otcPostTradeIndicator��transactionDetails.quantity��reportDetails.reportStatus��"transactionDetails.tradingCapacity��+reportDetails.tradingVenueTransactionIdCode��-tradersAlgosWaiversIndicators.waiverIndicator��transactionDetails.venue��*transactionDetails.branchMembershipCountry��"transactionDetails.tradingDateTime��date��workflow.eligibility��workflow.isReported��workflow.status��,reportDetails.investmentFirmCoveredDirective�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK)��h�i8�����R�(K�<�NNNJ����J����K t�b�BH                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       �t�bhF�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK)��h!�]�(�EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR�et�b�_dtype�h\�StringDtype���)��ubh^)��}�(hahhK ��h��R�(KK)��h!�]�(�EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR��EUR�et�bh�h�)��ubh^)��}�(hahhK ��h��R�(KK)��h!�]�(�NEWT�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh^)��}�(hahhK ��h��R�(KK)��h!�]�(�AOTC�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh^)��}�(hahhK ��h��R�(KK)��h!�]�(�XOFF�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh^)��}�(hahhK ��h��R�(KK)��h!�]�(�GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB��GB�et�bh�h�)��ubhhK ��h��R�(KKK)��h�b1�����R�(Kh"NNNJ����J����K t�b�C�                                                                                                                           �t�bhhK ��h��R�(KKK)��h�f8�����R�(KhTNNNJ����J����K t�b�B@
        Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      Y@      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �    @�A    ���@    @kA    @�A    Ѓ%A    `
A    h�2A    ��A    |2@A    @0A    � "A    �OA     F�@    �$ A    �|!A    �'A     �A    p�$A    �2A    ��1A    `�A    @�A     �A    ��A    @�A     �A    �IA    `HA    P�*A     �$A    P.A    ��!A    ��!A    0!A    `HA    `A    @�"A    �� A    �P'A     #A     eA      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �    @�A    ���@    @kA    @�A    Ѓ%A    `
A    h�2A    ��A    |2@A    @0A    � "A    �OA     F�@    �$ A    �|!A    �'A     �A    p�$A    �2A    ��1A    `�A    @�A     �A    ��A    @�A     �A    �IA    `HA    P�*A     �$A    P.A    ��!A    ��!A    0!A    `HA    `A    @�"A    �� A    �P'A     #A     eA      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KKK)��hS�BH                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       �t�bhhK ��h��R�(KK
K)��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�pandas._libs.missing��NA���j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  �
test_data.csv�j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  �RTS22Transaction�j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j>  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  �BUYI�j?  j?  j?  j?  j?  j?  j?  j?  j?  �SELL�j?  j?  j?  j?  j?  j?  j?  j?  j?  j@  j?  j?  j?  j?  j?  j?  j?  j?  j?  j?  j?  j?  j?  j?  j?  j?  j?  j?  j?  j?  �PERC�jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  jA  �NOML�jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  jB  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  �2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��2021-01-15T16:40:00.000000Z��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15��
2021-01-15�}�(�eligible���onFirds���totv���utotv���underlyingOnFirds��u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u}�(j�  �j�  �j�  �j�  �j�  �u�se_schema.static.mifid2��RTS22TransactionStatus����
REPORTABLE���R�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bhFNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bhFNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bhFNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bhFNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bhFNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhFNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h3h4hBhDet�bhFNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h,h-h5h6h7h8h;h<et�bhFNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bhFNu��R�h
h}�(hhhK ��h��R�(KK
��h!�]�(h'h)h*h+h.h/h0h1h2h?h@hAhCet�bhFNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h_�mgr_locs��builtins��slice���K KK��R�u}�(j,  h�j-  j0  KKK��R�u}�(j,  h�j-  j0  KKK��R�u}�(j,  h�j-  j0  KKK��R�u}�(j,  h�j-  j0  KKK��R�u}�(j,  h�j-  j0  KKK��R�u}�(j,  j  j-  hhK ��h��R�(KK��h�i8�����R�(KhTNNNJ����J����K t�b�C                             �t�bu}�(j,  j#  j-  hhK ��h��R�(KK��jI  �C@                                                        �t�bu}�(j,  j-  j-  j0  KKK��R�u}�(j,  j3  j-  hhK ��h��R�(KK
��jI  �Ch                            	       
                     
                                   �t�bueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.