import pandas as pd
import pytest

from swarm_tasks.transform.frame import frame_splitter


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    df = pd.DataFrame()
    return df


@pytest.fixture()
def non_empty_test_frame() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "_order.orderIdentifiers.orderID": ["test1"],
            "executionDetails.TradeID": ["test1"],
            "orderState.executionDetails.orderState": ["test1"],
            "transactionDetails.PriceCurrency": ["test1"],
            "transactionDetails.quantityNotation": ["test1"],
            "transactionDetails.netAmount": ["test1"],
            "priceFormingData.Price": ["test1"],
        }
    )
    return df


class TestFrameSplitter(object):
    """
    Test cases for "FrameSplitter" class
    """

    def test_empty_input_df_input(self, empty_source_df):
        params = frame_splitter.Params(prefix="transactionDetails.")
        task = frame_splitter.FrameSplitter(params=params, name="test-frame-splitter")
        result = task.execute(empty_source_df, params)
        assert result.empty

    def test_with_prefix(self, non_empty_test_frame):
        params = frame_splitter.Params(prefix="transactionDetails.")
        task = frame_splitter.FrameSplitter(params=params, name="test-frame-splitter")

        expected_result = pd.DataFrame(
            {
                "transactionDetails.PriceCurrency": ["test1"],
                "transactionDetails.quantityNotation": ["test1"],
                "transactionDetails.netAmount": ["test1"],
            }
        )

        result = task.execute(non_empty_test_frame, params)
        assert result.equals(expected_result)

    def test_prefix_with_strip_prefix(self, non_empty_test_frame):
        params = frame_splitter.Params(prefix="transactionDetails.", strip_prefix=True)
        task = frame_splitter.FrameSplitter(params=params, name="test-frame-splitter")

        expected_result = pd.DataFrame(
            {
                "PriceCurrency": ["test1"],
                "quantityNotation": ["test1"],
                "netAmount": ["test1"],
            }
        )

        result = task.execute(non_empty_test_frame, params)
        assert result.equals(expected_result)

    def test_except_prefix_with_strip_prefix(self, non_empty_test_frame):
        params = frame_splitter.Params(except_prefix="_order.", strip_prefix=True)
        task = frame_splitter.FrameSplitter(params=params, name="test-frame-splitter")

        expected_result = pd.DataFrame(
            {
                "executionDetails.TradeID": ["test1"],
                "orderState.executionDetails.orderState": ["test1"],
                "transactionDetails.PriceCurrency": ["test1"],
                "transactionDetails.quantityNotation": ["test1"],
                "transactionDetails.netAmount": ["test1"],
                "priceFormingData.Price": ["test1"],
            }
        )

        result = task.execute(non_empty_test_frame, params)
        assert result.equals(expected_result)
