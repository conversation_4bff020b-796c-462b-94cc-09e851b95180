import pandas as pd
import pytest

from swarm_tasks.transform.frame import frame_column_manipulator


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    df = pd.DataFrame()
    return df


@pytest.fixture()
def non_empty_test_frame() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "executionDetails.TradeID": ["test1", "test1"],
            "orderState.executionDetails.orderState": ["test1", "test1"],
            "transactionDetails.PriceCurrency": ["test1", "test1"],
            "transactionDetails.quantityNotation": ["test1", "test1"],
            "priceFormingData.Price": ["test1", "test1"],
            "parties.buyer": ["test1", "test2"],
            "parties.seller": ["test1", "test2"],
        }
    )
    return df


class TestFrameColumnManipulator(object):
    """
    Test cases for "FrameColumnManipulator" class
    """

    def test_empty_input_df_input(self, empty_source_df):
        params = frame_column_manipulator.Params(action="add", prefix="prefix.")
        task = frame_column_manipulator.FrameColumnManipulator(
            params=params, name="test-frame-column-manipulator"
        )
        result = task.execute(empty_source_df, params)
        assert result.empty

    def test_add_prefix(self, non_empty_test_frame):
        params = frame_column_manipulator.Params(action="add", prefix="prefix.")
        task = frame_column_manipulator.FrameColumnManipulator(
            params=params, name="test-frame-column-manipulator"
        )

        expected_result = pd.DataFrame(
            {
                "prefix.executionDetails.TradeID": ["test1", "test1"],
                "prefix.orderState.executionDetails.orderState": ["test1", "test1"],
                "prefix.transactionDetails.PriceCurrency": ["test1", "test1"],
                "prefix.transactionDetails.quantityNotation": ["test1", "test1"],
                "prefix.priceFormingData.Price": ["test1", "test1"],
                "prefix.parties.buyer": ["test1", "test2"],
                "prefix.parties.seller": ["test1", "test2"],
            }
        )

        result = task.execute(non_empty_test_frame, params)
        assert result.equals(expected_result)

    def test_strip_prefix(self, non_empty_test_frame):
        params = frame_column_manipulator.Params(
            action="strip", prefix="transactionDetails."
        )
        task = frame_column_manipulator.FrameColumnManipulator(
            params=params, name="test-frame-column-manipulator"
        )

        expected_result = pd.DataFrame(
            {
                "executionDetails.TradeID": ["test1", "test1"],
                "orderState.executionDetails.orderState": ["test1", "test1"],
                "PriceCurrency": ["test1", "test1"],
                "quantityNotation": ["test1", "test1"],
                "priceFormingData.Price": ["test1", "test1"],
                "parties.buyer": ["test1", "test2"],
                "parties.seller": ["test1", "test2"],
            }
        )

        result = task.execute(non_empty_test_frame, params)
        assert result.equals(expected_result)

    def test_rename(self, non_empty_test_frame):
        rename_dict = {"parties.buyer": "buyer", "parties.seller": "seller"}
        params = frame_column_manipulator.Params(
            action="rename", rename_dict=rename_dict
        )
        task = frame_column_manipulator.FrameColumnManipulator(
            params=params, name="test-frame-column-manipulator"
        )

        expected_result = pd.DataFrame(
            {
                "executionDetails.TradeID": ["test1", "test1"],
                "orderState.executionDetails.orderState": ["test1", "test1"],
                "transactionDetails.PriceCurrency": ["test1", "test1"],
                "transactionDetails.quantityNotation": ["test1", "test1"],
                "priceFormingData.Price": ["test1", "test1"],
                "buyer": ["test1", "test2"],
                "seller": ["test1", "test2"],
            }
        )

        result = task.execute(non_empty_test_frame, params)
        assert result.equals(expected_result)

    def test_rename_columns(self, non_empty_test_frame):
        rename_list = [
            {"source_attribute": "parties.buyer", "target_attribute": "buyer"},
            {"source_attribute": "parties.seller", "target_attribute": "seller"},
        ]
        params = frame_column_manipulator.Params(
            action="rename", rename_columns=rename_list
        )
        task = frame_column_manipulator.FrameColumnManipulator(
            params=params, name="test-frame-column-manipulator"
        )

        expected_result = pd.DataFrame(
            {
                "executionDetails.TradeID": ["test1", "test1"],
                "orderState.executionDetails.orderState": ["test1", "test1"],
                "transactionDetails.PriceCurrency": ["test1", "test1"],
                "transactionDetails.quantityNotation": ["test1", "test1"],
                "priceFormingData.Price": ["test1", "test1"],
                "buyer": ["test1", "test2"],
                "seller": ["test1", "test2"],
            }
        )

        result = task.execute(non_empty_test_frame, params)
        assert result.equals(expected_result)

    def test_add_suffix(self, non_empty_test_frame):
        params = frame_column_manipulator.Params(action="add", suffix=".suffix")
        task = frame_column_manipulator.FrameColumnManipulator(
            params=params, name="test-frame-column-manipulator"
        )

        expected_result = pd.DataFrame(
            {
                "executionDetails.TradeID.suffix": ["test1", "test1"],
                "orderState.executionDetails.orderState.suffix": ["test1", "test1"],
                "transactionDetails.PriceCurrency.suffix": ["test1", "test1"],
                "transactionDetails.quantityNotation.suffix": ["test1", "test1"],
                "priceFormingData.Price.suffix": ["test1", "test1"],
                "parties.buyer.suffix": ["test1", "test2"],
                "parties.seller.suffix": ["test1", "test2"],
            }
        )

        result = task.execute(non_empty_test_frame, params)
        assert result.equals(expected_result)

    def test_strip_suffix(self, non_empty_test_frame):
        params = frame_column_manipulator.Params(action="strip", suffix=".TradeID")
        task = frame_column_manipulator.FrameColumnManipulator(
            params=params, name="test-frame-column-manipulator"
        )

        expected_result = pd.DataFrame(
            {
                "executionDetails": ["test1", "test1"],
                "orderState.executionDetails.orderState": ["test1", "test1"],
                "transactionDetails.PriceCurrency": ["test1", "test1"],
                "transactionDetails.quantityNotation": ["test1", "test1"],
                "priceFormingData.Price": ["test1", "test1"],
                "parties.buyer": ["test1", "test2"],
                "parties.seller": ["test1", "test2"],
            }
        )

        result = task.execute(non_empty_test_frame, params)
        assert result.equals(expected_result)
