from typing import List

import pandas as pd
import pytest

from swarm_tasks.transform.frame.replicate_rows_by_condition import Params
from swarm_tasks.transform.frame.replicate_rows_by_condition import (
    ReplicateRowsByCondition,
)


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    df = pd.DataFrame({"foo": ["a", "a", "b"], "bar": ["a", "b", "c"]})
    return df


@pytest.fixture()
def empty_source_frame() -> pd.DataFrame:
    df = pd.DataFrame()
    return df


@pytest.fixture()
def query() -> str:
    return "`foo` != 'a'"


@pytest.fixture()
def columns_to_set_as_null() -> List[str]:
    return ["bar"]


class TestReplicateRowsByCondition:
    """
    Test suite for ReplicateRowsByCondition
    """

    def test_empty_frame(
        self,
        empty_source_frame: pd.DataFrame,
        query: str,
        columns_to_set_as_null: List[str],
    ):
        params = Params(query=query, columns_to_set_as_null=columns_to_set_as_null)
        task = ReplicateRowsByCondition(
            params=params,
            name="test-replicate-rows-by-condition",
        )

        result = task.execute(source_frame=empty_source_frame, params=params)
        assert result.empty

    def test_end_to_end(
        self,
        source_frame: pd.DataFrame,
        query: str,
        columns_to_set_as_null: List[str],
    ):
        params = Params(query=query, columns_to_set_as_null=columns_to_set_as_null)
        task = ReplicateRowsByCondition(
            params=params,
            name="test-replicate-rows-by-condition",
        )

        result = task.execute(source_frame=source_frame, params=params)
        expected_result = pd.DataFrame(
            {"foo": ["a", "a", "b", "b"], "bar": ["a", "b", "c", pd.NA]}
        )
        assert result.equals(expected_result)
