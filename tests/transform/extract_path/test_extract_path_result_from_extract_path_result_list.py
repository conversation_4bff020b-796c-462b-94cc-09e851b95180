from pathlib import PosixPath

import pytest
from prefect.engine import signals
from se_core_tasks.core.core_dataclasses import ExtractPathResult

from swarm_tasks.transform.extract_path.extract_path_result_from_extract_path_result_list import (
    ExtractPathResultFromExtractPathResultList,
)
from swarm_tasks.transform.extract_path.extract_path_result_from_extract_path_result_list import (
    Params,
)


@pytest.fixture()
def source_extract_result_list() -> list:
    """Creates a sample extract_result_list"""
    return [
        ExtractPathResult(
            path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/Recordings.html"
            )
        ),
        ExtractPathResult(
            path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/000000000004405.opus"
            )
        ),
        ExtractPathResult(
            path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/000000000004444.opus"
            )
        ),
    ]


class TestExtractPathResultFromExtractPathResultList:
    """Class to hold all the test cases for ExtractPathResultFromExtractPathResultList"""

    def test_task_with_empty_extract_result_list(self):
        """Tests the task when extract_result_list is empty"""

        params = Params(file_name="Recordings.html")
        # mock realm and env from swarm.conf:SettingsCls
        task = ExtractPathResultFromExtractPathResultList(
            name="ExtractPathResultFromExtractPathResultList", params=params
        )
        with pytest.raises(signals.SKIP):
            task.execute(extract_result_list=None, params=params)

    def test_task_with_file_not_present_in_extract_path_list(
        self, source_extract_result_list
    ):
        """Tests the task with an unknown file as the file_name param"""

        params = Params(file_name="Unknown.html")
        task = ExtractPathResultFromExtractPathResultList(
            name="ExtractPathResultFromExtractPathResultList", params=params
        )
        with pytest.raises(signals.SKIP):
            task.execute(extract_result_list=source_extract_result_list, params=params)

    def test_task_with_file_present_in_extract_path_list(
        self, source_extract_result_list
    ):
        """Tests the task with a file that is present as the file_name param"""

        params = Params(file_name="Recordings.html")

        task = ExtractPathResultFromExtractPathResultList(
            name="ExtractPathResultFromExtractPathResultList", params=params
        )
        result = task.execute(
            extract_result_list=source_extract_result_list, params=params
        )
        assert result == ExtractPathResult(
            path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/Recordings.html"
            )
        )
