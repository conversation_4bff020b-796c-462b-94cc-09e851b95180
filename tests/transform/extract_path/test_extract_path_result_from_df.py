import os
import shutil
from pathlib import Path

import pandas as pd
import pytest
from addict import addict
from prefect.engine import signals
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.conf import SettingsCls

from swarm_tasks.transform.extract_path.extract_path_result_from_df import (
    ExtractPathResultFromDf,
)
from swarm_tasks.transform.extract_path.extract_path_result_from_df import Params

SOURCE_DIR = Path("tmp_dir/2022-11-02_104356/sources")


@pytest.fixture()
def empty_source_frame() -> pd.DataFrame:
    return pd.DataFrame()


@pytest.fixture()
def non_empty_source_frame() -> pd.DataFrame:
    return pd.DataFrame({"a": [1, 2, 3]})


@pytest.fixture()
def original_file_url() -> str:
    return (
        "s3://ashwath.dev.steeleye.co/flows/order-feed-flextrade-controller-prim-rpl-processor/"
        "Flextrade/ON-2621/FSI-FT-report-20211101.csv"
    )


@pytest.fixture()
def params_fixture() -> Params:
    return Params(**{"output_csv_file_name": "exe_input"})


class TestExtractPathResultFromDf:
    """Test suite for ExtractPathResultFromDf"""

    @staticmethod
    def setup_method():
        os.makedirs(SOURCE_DIR, exist_ok=True)

    @staticmethod
    def teardown_method():
        tmp_dir = Path(__file__).parent.joinpath("tmp_dir")
        if tmp_dir.exists() and tmp_dir.is_dir():
            shutil.rmtree(tmp_dir)

    def test_empty_source_frame(
        self,
        empty_source_frame: pd.DataFrame,
        original_file_url: str,
        params_fixture: Params,
    ):
        task = ExtractPathResultFromDf(
            name="ExtractPathResultFromDf", params=params_fixture
        )
        with pytest.raises(signals.SKIP):
            task.execute(
                source_frame=empty_source_frame,
                original_file_url=original_file_url,
                params=params_fixture,
            )

    def test_successful_execution(
        self,
        mocker,
        non_empty_source_frame: pd.DataFrame,
        original_file_url: str,
        params_fixture: Params,
    ):
        mock_context = mocker.patch.object(
            SettingsCls, "context", new_callable=mocker.PropertyMock
        )
        mock_context.return_value = addict.Dict({"sources_dir": SOURCE_DIR})
        task = ExtractPathResultFromDf(
            name="ExtractPathResultFromDf", params=params_fixture
        )
        result = task.execute(
            source_frame=non_empty_source_frame,
            original_file_url=original_file_url,
            params=params_fixture,
        )
        expected_result = ExtractPathResult(
            SOURCE_DIR.joinpath("FSI-FT-report-20211101_exe_input.csv")
        )
        assert result.path == expected_result.path
        expected_df = pd.read_csv(expected_result.path)
        assert expected_df.equals(non_empty_source_frame)
