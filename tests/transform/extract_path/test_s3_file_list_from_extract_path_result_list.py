from pathlib import PosixPath

import pytest
from prefect.engine import signals
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.core.core_dataclasses import S3Action
from se_core_tasks.core.core_dataclasses import S3File
from swarm.conf import SettingsCls

from swarm_tasks.transform.extract_path.s3_file_list_from_extract_path_result_list import (
    Params,
)
from swarm_tasks.transform.extract_path.s3_file_list_from_extract_path_result_list import (
    S3FileListFromExtractPathResultList,
)


@pytest.fixture()
def source_extract_result_list() -> list:
    """Creates a sample extract_result_list"""
    return [
        ExtractPathResult(
            path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/Avaya.png"
            )
        ),
        ExtractPathResult(
            path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/000000000004405.opus"
            )
        ),
        ExtractPathResult(
            path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/000000000004444.opus"
            )
        ),
    ]


@pytest.fixture()
def source_single_extract_result() -> ExtractPathResult:
    """Creates a single ExtractPathResult"""
    return ExtractPathResult(
        path=PosixPath(
            "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/Avaya.png"
        )
    )


@pytest.fixture()
def expected_result_no_suffix_param() -> list:
    """Expected result when the suffix param is not supplied"""
    return [
        S3File(
            file_path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/Avaya.png"
            ),
            bucket_name="dummy.dev.steeleye.co",
            key_name="attachments/test/Avaya.png",
            action=S3Action.UPLOAD,
            bytes=0,
        ),
        S3File(
            file_path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/000000000004405.opus"
            ),
            bucket_name="dummy.dev.steeleye.co",
            key_name="attachments/test/000000000004405.opus",
            action=S3Action.UPLOAD,
            bytes=0,
        ),
        S3File(
            file_path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/000000000004444.opus"
            ),
            bucket_name="dummy.dev.steeleye.co",
            key_name="attachments/test/000000000004444.opus",
            action=S3Action.UPLOAD,
            bytes=0,
        ),
    ]


@pytest.fixture()
def expected_result_single_extract_result():
    return [
        S3File(
            file_path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/Avaya.png"
            ),
            bucket_name="dummy.dev.steeleye.co",
            key_name="attachments/test/Avaya.png",
            action=S3Action.UPLOAD,
            bytes=0,
        )
    ]


@pytest.fixture()
def expected_result_opus_suffix_param() -> list:
    """Expected result when the suffix param is '.opus''"""
    return [
        S3File(
            file_path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/000000000004405.opus"
            ),
            bucket_name="dummy.dev.steeleye.co",
            key_name="attachments/test/000000000004405.opus",
            action=S3Action.UPLOAD,
            bytes=0,
        ),
        S3File(
            file_path=PosixPath(
                "/var/folders/9h/v_23x_g177b549fgsr0h5vqr0000gn/T/__extract__/000000000004444.opus"
            ),
            bucket_name="dummy.dev.steeleye.co",
            key_name="attachments/test/000000000004444.opus",
            action=S3Action.UPLOAD,
            bytes=0,
        ),
    ]


class TestS3FileListFromExtractPathResultList:
    """Class to hold all the test cases for S3FileListFromExtractPathResultList"""

    def test_task_with_empty_extract_result_list(self):
        """Tests the task when extract_result_list is empty"""

        params = Params(s3_key_prefix="attachments/test")
        # mock realm and env from swarm.conf:SettingsCls
        task = S3FileListFromExtractPathResultList(
            name="S3FileListFromExtractPathResultList", params=params
        )
        with pytest.raises(signals.SKIP):
            task.execute(extract_result_list=None, params=params)

    def test_task_with_no_suffix_filter(
        self, mocker, source_extract_result_list, expected_result_no_suffix_param
    ):
        """Tests the task without a suffix filter"""

        params = Params(s3_key_prefix="attachments/test")
        # mock realm and env from swarm.conf:SettingsCls
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "dummy.dev.steeleye.co"
        task = S3FileListFromExtractPathResultList(
            name="S3FileListFromExtractPathResultList", params=params
        )
        result = task.execute(
            extract_result_list=source_extract_result_list, params=params
        )
        assert result == expected_result_no_suffix_param

    def test_task_with_single_extract_result(
        self,
        mocker,
        source_single_extract_result,
        expected_result_single_extract_result,
    ):
        """Test for the case where the source is not a list of ExtractPathResult objects, but
        a single ExtractPathResult"""
        params = Params(s3_key_prefix="attachments/test")
        # mock realm and env from swarm.conf:SettingsCls
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "dummy.dev.steeleye.co"
        task = S3FileListFromExtractPathResultList(
            name="S3FileListFromExtractPathResultList", params=params
        )
        result = task.execute(
            extract_result_list=source_single_extract_result, params=params
        )
        assert result == expected_result_single_extract_result

    def test_task_with_opus_suffix_filter(
        self, mocker, source_extract_result_list, expected_result_opus_suffix_param
    ):
        """Tests the task with a suffix filter for .opus files"""

        params = Params(s3_key_prefix="attachments/test", file_suffix=".opus")
        # mock realm and env from swarm.conf:SettingsCls
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "dummy.dev.steeleye.co"
        task = S3FileListFromExtractPathResultList(
            name="S3FileListFromExtractPathResultList", params=params
        )
        result = task.execute(
            extract_result_list=source_extract_result_list, params=params
        )
        assert result == expected_result_opus_suffix_param
