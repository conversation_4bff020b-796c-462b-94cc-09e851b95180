import pandas as pd
import pytest
from pydantic.error_wrappers import ValidationError

from swarm_tasks.transform.datetime import join_date_and_time


@pytest.fixture()
def source_dataframe() -> pd.DataFrame:
    """
    Represents a source dataframe with two columns
    """
    df = pd.DataFrame(
        {
            "source_date": ["20211109", "20210922", "20211109"],
            "source_time": [
                "07:47:05.402675 +0000",
                "07:53:59.801387 +0100s",
                "16:55:00.000000 -0500",
            ],
            "source_date_time": [
                "20211109 07:47:05.402675 +0000",
                "20210922 07:53:59.801387 +0100s",
                "20211109 16:55:00.000000 -0500",
            ],
        }
    )
    return df


@pytest.fixture()
def invalid_date_source_dataframe() -> pd.DataFrame:
    """
    Represents a source dataframe with two columns where the dates are invalid
    """
    df = pd.DataFrame(
        {
            "source_date": ["0", "0", "00"],
            "source_time": [
                "07:47:05.402675 +0000",
                "07:53:59.801387 +0100s",
                "16:55:00.000000 -0500",
            ],
        }
    )
    return df


@pytest.fixture()
def source_dataframe_nanoseconds() -> pd.DataFrame:
    """
    Represents a source dataframe with two columns
    """
    df = pd.DataFrame(
        {
            "source_date": ["20211109", "20210922", "20211109"],
            "source_time": [
                "07:47:05.402675999Z",
                "07:53:59.8013879",
                "16:55:00.00000099",
            ],
        }
    )
    return df


class TestJoinDateAndTime:
    """
    Test cases for "TestJoinDateAndTime" task
    """

    def test_valid_source_data(self, source_dataframe):
        params = join_date_and_time.Params(
            source_date_attribute="source_date",
            source_time_attribute="source_time",
            target_attribute="target_date_time",
            source_formats=["%Y%m%d%H:%M:%S.%f %zs", "%Y%m%d%H:%M:%S.%f %z"],
            target_format="%Y-%m-%dT%H:%M:%S.%fZ",
        )
        task = join_date_and_time.JoinDateAndTimeFormat(
            name="join-date-and-time", params=params
        )
        outcome_df = task.execute(source_frame=source_dataframe, params=params)
        expected_data = pd.DataFrame(
            {
                "target_date_time": [
                    "2021-11-09T07:47:05.402675Z",
                    "2021-09-22T06:53:59.801387Z",
                    "2021-11-09T21:55:00.000000Z",
                ]
            }
        )
        assert outcome_df.equals(expected_data)

    def test_skip_time_attribute(self, source_dataframe):
        params = join_date_and_time.Params(
            source_date_attribute="source_date_time",
            skip_time_attribute=True,
            target_attribute="target_date_time",
            source_formats=["%Y%m%d %H:%M:%S.%f %zs", "%Y%m%d %H:%M:%S.%f %z"],
            target_format="%Y-%m-%dT%H:%M:%S.%fZ",
        )
        task = join_date_and_time.JoinDateAndTimeFormat(
            name="join-date-and-time", params=params
        )
        outcome_df = task.execute(source_frame=source_dataframe, params=params)
        expected_data = pd.DataFrame(
            {
                "target_date_time": [
                    "2021-11-09T07:47:05.402675Z",
                    "2021-09-22T06:53:59.801387Z",
                    "2021-11-09T21:55:00.000000Z",
                ]
            }
        )
        assert outcome_df.equals(expected_data)

    def test_invalid_params(self, source_dataframe):

        with pytest.raises(ValidationError) as e:
            join_date_and_time.Params(
                source_date_attribute="source_date",
                source_time_attribute="source_time",
                skip_time_attribute=True,
                target_attribute="target_date_time",
                source_formats=["%Y%m%d %H:%M:%S.%f %zs", "%Y%m%d %H:%M:%S.%f %z"],
                target_format="%Y-%m-%dT%H:%M:%S.%fZ",
            )
        assert (
            e.value.errors()[0]["msg"]
            == "`source_time_attribute` and `skip_time_attribute` are mutually exclusive"
        )
        with pytest.raises(ValidationError) as e:
            join_date_and_time.Params(
                source_date_attribute="source_date",
                target_attribute="target_date_time",
                source_formats=["%Y%m%d %H:%M:%S.%f %zs", "%Y%m%d %H:%M:%S.%f %z"],
                target_format="%Y-%m-%dT%H:%M:%S.%fZ",
            )
        assert (
            e.value.errors()[0]["msg"]
            == "Either `source_time_attribute` should be provided or `skip_time_attribute` should be true"
        )

    def test_remove_nanoseconds(self, source_dataframe_nanoseconds):
        params = join_date_and_time.Params(
            source_date_attribute="source_date",
            source_time_attribute="source_time",
            target_attribute="target_date_time",
            source_formats=[
                "%Y%m%d%H:%M:%S.%fZ",
                "%Y%m%d%H:%M:%S.%f",
                "%Y%m%d%H:%M:%S",
            ],
            target_format="%Y-%m-%dT%H:%M:%S.%fZ",
        )
        task = join_date_and_time.JoinDateAndTimeFormat(
            name="join-date-and-time", params=params
        )
        outcome_df = task.execute(
            source_frame=source_dataframe_nanoseconds, params=params
        )
        expected_data = pd.DataFrame(
            {
                "target_date_time": [
                    "2021-11-09T07:47:05.402675Z",
                    "2021-09-22T07:53:59.801387Z",
                    "2021-11-09T16:55:00.000000Z",
                ]
            }
        )
        assert not pd.testing.assert_frame_equal(
            outcome_df, expected_data, check_dtype=False
        )

    def test_invalid_source_data(self, invalid_date_source_dataframe):
        params = join_date_and_time.Params(
            source_date_attribute="source_date",
            source_time_attribute="source_time",
            target_attribute="target_date_time",
            source_formats=["%Y%m%d%H:%M:%S.%f %zs", "%Y%m%d%H:%M:%S.%f %z"],
            target_format="%Y-%m-%dT%H:%M:%S.%fZ",
        )
        task = join_date_and_time.JoinDateAndTimeFormat(
            name="join-date-and-time", params=params
        )
        outcome_df = task.execute(
            source_frame=invalid_date_source_dataframe, params=params
        )
        expected_data = pd.DataFrame({"target_date_time": [pd.NA, pd.NA, pd.NA]})
        assert outcome_df.equals(expected_data)
