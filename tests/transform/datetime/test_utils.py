import pandas as pd

from swarm_tasks.transform.datetime.utils import add_delta_to_epoch_time
from swarm_tasks.transform.datetime.utils import format_call_duration


class TestUtilities:
    def test_add_delta_to_epoch_time(
        self,
        time_data: pd.Series,
        time_delta: pd.Series,
        expected_result_add_delta_to_epoch: pd.Series,
    ):
        result = add_delta_to_epoch_time(
            time_data=time_data, time_delta=time_delta, time_unit="ms", delta_unit="ms"
        )
        assert result.equals(expected_result_add_delta_to_epoch)

    def test_format_call_duration(
        self,
        time_delta_series: pd.Series,
        expected_result_format_call_duration: pd.Series,
    ):
        result = format_call_duration(time_delta_series=time_delta_series)
        assert result.equals(expected_result_format_call_duration)
