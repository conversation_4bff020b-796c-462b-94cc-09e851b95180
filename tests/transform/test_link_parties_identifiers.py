import numpy as np
import pandas as pd
import pytest
from prefect import context
from prefect.utilities.collections import DotDict
from se_elasticsearch.repository import get_repository_by_cluster_version
from swarm.schema.flow.bundle.components import ResourceConfig

from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.party_identifiers import (
    PartyIdentifiers,
)


@pytest.mark.skip(reason="needs realignment")
def test_link_se_trade_blotter_party_identifiers():
    df = pd.DataFrame(
        {
            "Client ID": ["id-test-001", "213800JB6ZEHVXNATW28"],
            "Trading Capacity": ["prop", np.nan],
            "Counterparty ID": ["abc", "def"],
            "Executing Entity ID": ["asd", "qwe"],
            "Trader ID": ["tree", "try"],
            "Investment Decision Maker": ["tert", np.nan],
            "transactionDetails.buySellIndicator": ["BUYI", "SELL"],
        }
    )

    # classes
    params = PartyIdentifiers.params_class()

    resources = PartyIdentifiers.resources_class(es_client_key="tenant-data")

    data_types_task = PartyIdentifiers(
        name="se_trade_blotter_party_identifiers", params=params
    )

    # clients
    resource_config = ResourceConfig(
        client_type="elasticsearch",
        host="elasticsearch.dev-blue.steeleye.co",
        port=9200,
    )
    clients = {
        "tenant-data": get_repository_by_cluster_version(
            resource_config=resource_config
        )
    }

    data_types_task.clients = clients
    identifier = DotDict({"tenant": "schroders"})
    context.swarm.identifier = identifier

    # Execute
    result = data_types_task.execute(
        source_frame=df, params=params, resources=resources
    )
    result
