import importlib
import os
from pathlib import Path
from typing import Type

import pandas as pd
import pytest
import yaml
from prefect import context
from swarm.schema.task.components import PrefectParams
from swarm.task.io.read.result import FrameProducerResult
from swarm.task.transform.base import TransformBaseTask
from swarm.task.transform.result import TransformResult

specs_path = Path(__file__).parent.joinpath("specs")


def read_specs():
    includes = os.environ.get("INCLUDE_SPECS")
    if includes:
        includes = [f"{inc.strip()}.yaml" for inc in includes.split(",")]

    for root, directory_names, file_names in os.walk(specs_path):
        for f in file_names:
            if includes and f not in includes:
                continue
            with Path(root).joinpath(f).open(encoding="utf-8") as spec_file:
                spec = yaml.load(spec_file, Loader=yaml.SafeLoader)
                spec_file.close()

            yield spec


def read_tests():
    for spec in read_specs():
        module = importlib.import_module(name=spec["task"]["package"])
        task_class: Type[TransformBaseTask] = getattr(module, spec["task"]["class"])

        for test in spec.get("tests"):
            yield [task_class, test.get("name"), test]


@pytest.mark.skip(reason="needs realignment")
@pytest.mark.parametrize("task_class, name, test", read_tests())
def test_spec(task_class: Type[TransformBaseTask], name: str, test: dict):
    # extract params to pass task constructor
    test_params = test.get("test_params")
    params = (
        task_class.params_class(**test_params.get("params"))
        if test_params.get("params")
        else None
    )
    params_list = (
        [task_class.params_class(**x) for x in test_params.get("params_list")]
        if test_params.get("params_list")
        else None
    )
    config = PrefectParams(**test_params.get("config", {}))

    # initialise task
    task = task_class(name=name, params=params, params_list=params_list, config=config)

    # construct input frame
    frame = pd.DataFrame(**test.get("input"))

    # construct frame producer result
    producer_result = FrameProducerResult(frame=frame)

    # set audits_dir in prefect context
    audits_dir = Path().resolve().parents[1].joinpath("audits_dir")
    audits_dir.mkdir(exist_ok=True)
    context["audits_dir"] = audits_dir

    # run task
    actual_response = task.run(producer_result=producer_result)

    # compose expected response for comparison
    expected_response = TransformResult(
        target=pd.DataFrame(**test["expected"]["target"]),
    )

    # assert actual response target equals expected response target
    assert actual_response.target.equals(expected_response.target)
