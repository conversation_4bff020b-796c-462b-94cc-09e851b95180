import pandas as pd
import prefect.engine.signals as signals
import pytest
from prefect import context
from prefect.utilities.collections import DotDict
from se_elasticsearch.repository import get_repository_by_cluster_version
from swarm.schema.flow.bundle.components import ResourceConfig

from swarm_tasks.transform.steeleye.mymarket import deduplicate_counterparties as dc


class Counterparties:

    TYPE = "Type"
    INTERNAL_REFERENCE_NUMBER = "Internal Reference Number"
    NAME = "Name"
    LEI = "LEI"
    CLIENT_MANDATE = "Client Mandate"
    COUNTRY_OF_BRANCH = "Country of Branch"
    TRADE_FILE_IDENTIFIERS = "sinkIdentifiers.tradeFileIdentifiers"
    UNIQUE_PROPS = "&uniqueProps"


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    result = pd.DataFrame()

    return result


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            Counterparties.TYPE: ["Counterparty", "Counterparty"],
            Counterparties.INTERNAL_REFERENCE_NUMBER: ["REF1", "REF2"],
            Counterparties.NAME: ["Firm1", "Firm2"],
            Counterparties.LEI: ["0123456789TEST_TEST1", "0123456789TEST_TEST2"],
            Counterparties.CLIENT_MANDATE: [pd.NA, pd.NA],
            Counterparties.COUNTRY_OF_BRANCH: [pd.NA, pd.NA],
        }
    )
    return df


@pytest.fixture()
def missing_some_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            Counterparties.TYPE: ["Counterparty", "Counterparty"],
            Counterparties.INTERNAL_REFERENCE_NUMBER: ["REF1", "REF2"],
            Counterparties.NAME: ["Firm1", "Firm2"],
            Counterparties.CLIENT_MANDATE: [pd.NA, pd.NA],
            Counterparties.COUNTRY_OF_BRANCH: [pd.NA, pd.NA],
        }
    )
    return df


@pytest.mark.skip(reason="skip CI")
class TestDeduplicateCounterparties(object):
    """
    Test cases for "DeduplicateCounterParties" class
    """

    def test_empty_input_df_without_source_columns(self, empty_source_df: pd.DataFrame):
        task, params, resources = self._init_task()
        task = dc.DeduplicateCounterParties(
            name="test-deduplicate", params=params, resources=resources
        )
        with pytest.raises(signals.SKIP):
            task.execute(empty_source_df, params, resources)

    def test_all_col_in_source_df(self, all_col_in_source_df: pd.DataFrame):
        data = {
            Counterparties.TYPE: ["Counterparty", "Counterparty"],
            Counterparties.INTERNAL_REFERENCE_NUMBER: ["REF1", "REF2"],
            Counterparties.NAME: ["Firm1", "Firm2"],
            Counterparties.LEI: ["0123456789TEST_TEST1", "0123456789TEST_TEST2"],
            Counterparties.CLIENT_MANDATE: [pd.NA, pd.NA],
            Counterparties.COUNTRY_OF_BRANCH: [pd.NA, pd.NA],
            Counterparties.TRADE_FILE_IDENTIFIERS: [
                [{"id": "0123456789TEST_TEST1", "label": "lei"}],
                [{"id": "0123456789TEST_TEST2", "label": "lei"}],
            ],
            Counterparties.UNIQUE_PROPS: [
                ["lei:0123456789test_test1"],
                ["lei:0123456789test_test2"],
            ],
        }

        expected_result = pd.DataFrame(data)
        task, params, resources = self._init_task()
        result = task.execute(all_col_in_source_df, params, resources)
        assert result.equals(expected_result)

    def test_missing_some_col_in_source_df(
        self, missing_some_col_in_source_df: pd.DataFrame
    ):

        task, params, resources = self._init_task()
        with pytest.raises(signals.SKIP):
            task.execute(missing_some_col_in_source_df, params, resources)

    def _init_task(self):

        resources = dc.DeduplicateCounterParties.resources_class(
            es_client_key="tenant-data"
        )
        resource_config = ResourceConfig(
            host="elasticsearch.dev-blue.steeleye.co", port=9401
        )
        clients = {
            "tenant-data": get_repository_by_cluster_version(
                resource_config=resource_config
            )
        }
        identifier = DotDict({"tenant": "pinafore"})
        context.swarm = DotDict()
        context.swarm.identifier = identifier
        context.swarm.tenant_configuration = None
        params = dc.Params(identifiers_path="LEI")
        task = dc.DeduplicateCounterParties(
            name="deduplicate-counterparties", params=params, resources=resources
        )
        task.clients = clients
        return task, params, resources
