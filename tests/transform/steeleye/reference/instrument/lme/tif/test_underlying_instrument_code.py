import numpy as np
import pandas as pd
import pytest

from swarm_tasks.transform.steeleye.reference.instrument.lme.tif.underlying_instrument_code import (
    NESTED_KEY,
)
from swarm_tasks.transform.steeleye.reference.instrument.lme.tif.underlying_instrument_code import (
    SOURCE_ATTRIBUTE,
)
from swarm_tasks.transform.steeleye.reference.instrument.lme.tif.underlying_instrument_code import (
    TARGET_ATTRIBUTE,
)
from swarm_tasks.transform.steeleye.reference.instrument.lme.tif.underlying_instrument_code import (
    UnderlyingInstrumentIdCode,
)


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    """
    Represents a source dataframe parsed from input file
    """
    df = pd.DataFrame({SOURCE_ATTRIBUTE: ["TEST_CC", np.nan]})
    return df


class TestTransactionRefNo:
    def test_alternative_instrument_identifier(self, source_frame: pd.DataFrame):
        mapping = UnderlyingInstrumentIdCode(name="test-underlying-instrument-id-code")
        outcome_df = mapping.execute(source_frame)
        expected_df = pd.DataFrame(
            {TARGET_ATTRIBUTE: [[{NESTED_KEY: "TEST_CC"}], np.nan]}
        )
        assert outcome_df.equals(expected_df)
