import numpy as np
import pandas as pd
import pytest

from swarm_tasks.transform.steeleye.reference.instrument.cfi_attributes_from_cfi_code import (
    CFI,
)
from swarm_tasks.transform.steeleye.reference.instrument.cfi_attributes_from_cfi_code import (
    CFIAttributesFromCFICode,
)
from swarm_tasks.transform.steeleye.reference.instrument.cfi_attributes_from_cfi_code import (
    Params,
)


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    """
    Represents a source dataframe parsed from input file
    """
    df = pd.DataFrame({"CFI": ["ESVTFB", "ESVTFA", "not-a-valid-cfi-code", np.nan]})
    return df


class TestTransactionRefNo:
    def test_alternative_instrument_identifier(self, source_frame: pd.DataFrame):
        params = Params(source_attribute="CFI")
        mapping = CFIAttributesFromCFICode(
            name="test-cfi-attributes-from-cfi-code", params=params
        )
        outcome_df = mapping.execute(source_frame, params=params)
        expected_df = pd.DataFrame(
            {
                CFI.CATEGORY: ["Equity", "Equity", np.nan, np.nan],
                CFI.GROUP: [
                    "Common / Ordinary shares",
                    "Common / Ordinary shares",
                    np.nan,
                    np.nan,
                ],
                CFI.ATTR1: ["Voting", "Voting", np.nan, np.nan],
                CFI.ATTR2: ["Restrictions", "Restrictions", np.nan, np.nan],
                CFI.ATTR3: ["Fully Paid", "Fully Paid", np.nan, np.nan],
                CFI.ATTR4: ["Bearer", np.nan, np.nan, np.nan],
            }
        )
        assert outcome_df.equals(expected_df)
