import pandas as pd
import pytest
from se_elastic_schema.components.market.identifier import Identifier
from se_elastic_schema.static.market import IdentifierType

from swarm_tasks.transform.steeleye.orders.data_source.adss.mt4.mifir.identifiers import (
    instrument_identifiers,
)
from swarm_tasks.transform.steeleye.orders.data_source.adss.mt4.mifir.identifiers.static import (
    ADSSMifirMT4Columns,
)


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    df = pd.DataFrame()
    return df


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            ADSSMifirMT4Columns.InstrumentFullName: ["TEST1.SB", "TEST2", pd.NA],
            ADSSMifirMT4Columns.UnderlyingInstrumentCode: ["test1", "test2", pd.NA],
        }
    )
    return df


@pytest.fixture()
def missing_some_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "InstrumentClassification": ["NONJESTEST", "JESTEST", "NONJESTTEST"],
            "UnderlyingIndexName": ["UK100.SEP", "USNDX.SEP", pd.NA],
        }
    )
    return df


class TestInstrumentIdentifiers(object):
    """
    Test cases for "TestInstrumentIdentifiers" class
    """

    def test_empty_input_df_without_source_columns(self, empty_source_df: pd.DataFrame):
        task = instrument_identifiers.InstrumentIdentifiers(name="test-instrument")
        result = task.execute(empty_source_df)
        assert result.empty

    def test_all_col_in_source_df(self, all_col_in_source_df: pd.DataFrame):
        task = instrument_identifiers.InstrumentIdentifiers(name="test-instrument")
        result = task.execute(all_col_in_source_df)
        expected_data = pd.DataFrame({"target": ["XXXXtest1SB", "XXXXtest2CFD", pd.NA]})
        expected_result = pd.DataFrame(index=all_col_in_source_df.index)
        expected_result["marketIdentifiers.instrument"] = (
            expected_data["target"]
            .dropna()
            .apply(
                lambda x: [
                    Identifier(
                        labelId=x,
                        path="instrumentDetails.instrument",
                        type=IdentifierType.OBJECT,
                    ).dict(by_alias=True)
                ]
            )
        )
        assert result.equals(expected_result)

    def test_missing_some_col_in_source_df(
        self, missing_some_col_in_source_df: pd.DataFrame
    ):
        task = instrument_identifiers.InstrumentIdentifiers(name="test-instrument")
        result = task.execute(missing_some_col_in_source_df)
        assert result.dropna().empty
