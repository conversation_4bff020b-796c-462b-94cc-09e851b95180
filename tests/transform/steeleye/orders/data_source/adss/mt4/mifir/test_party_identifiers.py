import pandas as pd
import pytest

from swarm_tasks.transform.steeleye.orders.data_source.adss.mt4.mifir.identifiers import (
    party_identifiers,
)


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    df = pd.DataFrame()
    return df


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "ExecutingEntityIdentificationCode": ["test1"],
            "InvestmentDecisionWithinFirm": ["test1"],
            "BuyerNPcode": ["test1"],
            "SellerNPcode": ["test1"],
            "ExecutionDecisionWithinFirm": ["test1"],
            "transactionDetails.buySellIndicator": ["test1"],
        }
    )
    return df


@pytest.fixture()
def missing_some_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "ExecutingEntityIdentificationCode": ["test1"],
            "InvestmentDecisionWithinFirm": ["test1"],
            "BuyerNPcode": ["test1"],
            "SellerNPcode": ["test1"],
            "transactionDetails.buySellIndicator": ["test1"],
        }
    )
    return df


class TestParties(object):
    """
    Test cases for "TestParties" class
    """

    def test_empty_input_df_without_source_columns(self, empty_source_df):
        task = party_identifiers.PartyIdentifiers(name="test-parties")
        result = task.execute(empty_source_df)
        assert result.empty

    def test_all_col_in_source_df(self, all_col_in_source_df: pd.DataFrame):
        task = party_identifiers.PartyIdentifiers(name="test-parties")
        expected_result = pd.DataFrame(
            {
                "marketIdentifiers.parties": [
                    "[{'labelId': 'id:test1', 'path': 'parties.buyer', "
                    "'type': <IdentifierType.ARRAY>}, {'labelId': 'lei:test1', "
                    "'path': 'parties.executingEntity', "
                    "'type': <IdentifierType.OBJECT>}, {'labelId': 'id:test1', "
                    "'path': 'parties.executionWithinFirm', "
                    "'type': <IdentifierType.OBJECT>}, {'labelId': 'id:test1', "
                    "'path': 'parties.investmentDecisionWithinFirm', "
                    "'type': <IdentifierType.OBJECT>}, {'labelId': 'id:test1', "
                    "'path': 'parties.seller', 'type': <IdentifierType.ARRAY>}, "
                    "{'labelId': 'id:test1', 'path': 'parties.trader', "
                    "'type': <IdentifierType.ARRAY>}]"
                ],
                "executingEntityFileIdentifier": "lei:test1",
                "buyerFileIdentifier": "id:test1",
                "sellerFileIdentifier": "id:test1",
                "traderFileIdentifier": "id:test1",
                "counterpartyFileIdentifier": "id:test1",
                "tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier": "id"
                ":test1",
                "tradersAlgosWaiversIndicators"
                ".investmentDecisionWithinFirmFileIdentifier": "id:test1",
            }
        )

        result = task.execute(all_col_in_source_df)
        assert result.astype("str").equals(expected_result.astype("str"))

    def test_missing_some_col_in_source_df(
        self, missing_some_col_in_source_df: pd.DataFrame
    ):
        task = party_identifiers.PartyIdentifiers(name="test-parties")
        expected_result = pd.DataFrame(
            {
                "marketIdentifiers.parties": [
                    "[{'labelId': 'id:test1', 'path': 'parties.buyer', "
                    "'type': <IdentifierType.ARRAY>}, {'labelId': 'lei:test1', "
                    "'path': 'parties.executingEntity', "
                    "'type': <IdentifierType.OBJECT>}, {'labelId': 'id:test1', "
                    "'path': 'parties.investmentDecisionWithinFirm', "
                    "'type': <IdentifierType.OBJECT>}, {'labelId': 'id:test1', "
                    "'path': 'parties.seller', 'type': <IdentifierType.ARRAY>}, "
                    "{'labelId': 'id:test1', 'path': 'parties.trader', "
                    "'type': <IdentifierType.ARRAY>}]"
                ],
                "executingEntityFileIdentifier": "lei:test1",
                "buyerFileIdentifier": "id:test1",
                "sellerFileIdentifier": "id:test1",
                "traderFileIdentifier": "id:test1",
                "counterpartyFileIdentifier": "id:test1",
                "tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier": "nan",
                "tradersAlgosWaiversIndicators."
                "investmentDecisionWithinFirmFileIdentifier": "id:test1",
            }
        )

        result = task.execute(missing_some_col_in_source_df)
        assert result.astype("str").equals(expected_result.astype("str"))
