import pandas as pd
import pytest
from swarm.task.io.read.result import FrameProducerResult

from swarm_tasks.transform.steeleye.orders.data_source.adss.mt4.emir import (
    skip_non_fx_rows,
)


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    df = pd.DataFrame()

    return df


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "Common Data Delegated": ["N", "N", "N", "N"],
            "Reporting Firm ID": [
                "213800FPNEZWDW42FZ90",
                "213800FPNEZWDW42FZ90",
                "213800FPNEZWDW42FZ90",
                "213800FPNEZWDW42FZ90",
            ],
            "Other Counterparty ID": ["603939", "603939", "603940", "603941"],
            "Beneficiary ID": [
                "213800FPNEZWDW42FZ90",
                "213800FPNEZWDW42FZ90",
                "213800FPNEZWDW42FZ90",
                "213800FPNEZWDW42FZ90",
            ],
            "UTI": ["test1", "test2", "test3", "test4"],
            "Notional Currency 1": ["USD", "GBP", "USD", "GBP"],
            "Instrument ID": ["CU", "CX", "CU", "CU"],
            "action": ["N", "N", "N", "Y"],
            "Action Type": [pd.NA, pd.NA, pd.NA, "N"],
        }
    )

    return df


@pytest.fixture()
def missing_some_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "Common Data Delegated": ["N", "N", "N"],
            "Reporting Firm ID": [
                "213800FPNEZWDW42FZ90",
                "213800FPNEZWDW42FZ90",
                "213800FPNEZWDW42FZ90",
            ],
            "Other Counterparty ID": ["603939", "603939", "603940"],
            "Beneficiary ID": [
                "213800FPNEZWDW42FZ90",
                "213800FPNEZWDW42FZ90",
                "213800FPNEZWDW42FZ90",
            ],
            "UTI": ["test1", "test2", "test3"],
            "Notional Currency 1": ["USD", "GBP", "USD"],
            "Instrument ID": ["CU", "CX", "CU"],
        }
    )

    return df


class TestSkipNonFXRows(object):
    """
    Test cases for "TestParties" class
    """

    def test_empty_input_df_without_source_columns(
        self, empty_source_df: FrameProducerResult
    ):
        task = skip_non_fx_rows.SkipNonFXRows(name="test-skip-non-fx-rows")
        result = task.execute(empty_source_df)
        assert result.empty

    def test_all_col_in_source_df(self, all_col_in_source_df: FrameProducerResult):
        task = skip_non_fx_rows.SkipNonFXRows(name="test-skip-non-fx-rows")
        result = task.execute(all_col_in_source_df)
        expected_result = pd.DataFrame(
            {
                "Common Data Delegated": ["N", "N", "N"],
                "Reporting Firm ID": [
                    "213800FPNEZWDW42FZ90",
                    "213800FPNEZWDW42FZ90",
                    "213800FPNEZWDW42FZ90",
                ],
                "Other Counterparty ID": ["603939", "603940", "603941"],
                "Beneficiary ID": [
                    "213800FPNEZWDW42FZ90",
                    "213800FPNEZWDW42FZ90",
                    "213800FPNEZWDW42FZ90",
                ],
                "UTI": ["test1", "test3", "test4"],
                "Notional Currency 1": ["USD", "USD", "GBP"],
                "Instrument ID": ["CU", "CU", "CU"],
                "action": ["N", "N", "Y"],
                "Action Type": [pd.NA, pd.NA, "N"],
            },
            index=[0, 2, 3],
        )
        assert result.equals(expected_result)

    def test_missing_some_col_in_source_df(
        self, missing_some_col_in_source_df: FrameProducerResult
    ):
        task = skip_non_fx_rows.SkipNonFXRows(name="test-skip-non-fx-rows")
        result = task.execute(missing_some_col_in_source_df)
        assert result.empty
