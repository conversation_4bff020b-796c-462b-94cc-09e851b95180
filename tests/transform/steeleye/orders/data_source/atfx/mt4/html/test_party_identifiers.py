import pandas as pd
import pytest

from swarm_tasks.transform.steeleye.orders.data_source.atfx.mt4.html.identifiers import (
    party_identifiers,
)


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    df = pd.DataFrame()
    return df


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "Login": ["test1"],
            "transactionDetails.buySellIndicator": ["test1"],
            "__lei__": "lei:123",
        }
    )
    return df


@pytest.fixture()
def missing_some_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {"transactionDetails.buySellIndicator": ["test1"], "__lei__": ["lei:123"]}
    )
    return df


class TestParties(object):
    """
    Test cases for "TestParties" class
    """

    def test_empty_input_df_without_source_columns(self, empty_source_df):
        params = party_identifiers.Params(lei_col="__lei__")
        task = party_identifiers.PartyIdentifiers(params=params, name="test-parties")
        result = task.execute(empty_source_df, params)
        assert result.empty

    def test_all_col_in_source_df(self, all_col_in_source_df: pd.DataFrame):
        params = party_identifiers.Params(lei_col="__lei__")
        task = party_identifiers.PartyIdentifiers(params=params, name="test-parties")
        expected_result = pd.DataFrame(
            {
                "marketIdentifiers.parties": [
                    "[{'labelId': 'id:test1', 'path': 'buyer', "
                    "'type': <IdentifierType.ARRAY>}, {'labelId': 'id:test1', "
                    "'path': 'buyerDecisionMaker', 'type': "
                    "<IdentifierType.ARRAY>}, {'labelId': 'lei:123', "
                    "'path': 'reportDetails.executingEntity', "
                    "'type': <IdentifierType.OBJECT>}, {'labelId': 'id:test1', "
                    "'path': 'seller', 'type': <IdentifierType.ARRAY>}, "
                    "{'labelId': 'id:test1', "
                    "'path': 'sellerDecisionMaker', 'type': "
                    "<IdentifierType.ARRAY>}]"
                ],
                "reportDetails.executingEntity.fileIdentifier": "lei:123",
                "buyerFileIdentifier": "id:test1",
                "sellerFileIdentifier": "id:test1",
                "counterpartyFileIdentifier": "id:test1",
                "sellerDecisionMakerFileIdentifier": "id:test1",
                "buyerDecisionMakerFileIdentifier": "id:test1",
            }
        )

        result = task.execute(all_col_in_source_df, params)
        assert result.astype("str").equals(expected_result.astype("str"))

    def test_missing_some_col_in_source_df(
        self, missing_some_col_in_source_df: pd.DataFrame
    ):
        params = party_identifiers.Params(lei_col="__lei__")
        task = party_identifiers.PartyIdentifiers(params=params, name="test-parties")
        expected_result = pd.DataFrame(
            {
                "marketIdentifiers.parties": [
                    "[{'labelId': 'lei:123', "
                    "'path': 'reportDetails.executingEntity', "
                    "'type': <IdentifierType.OBJECT>}]"
                ],
                "reportDetails.executingEntity.fileIdentifier": "lei:123",
                "buyerFileIdentifier": "<NA>",
                "sellerFileIdentifier": "<NA>",
                "counterpartyFileIdentifier": "<NA>",
                "sellerDecisionMakerFileIdentifier": "<NA>",
                "buyerDecisionMakerFileIdentifier": "<NA>",
            }
        )

        result = task.execute(missing_some_col_in_source_df, params)
        assert result.astype("str").equals(expected_result.astype("str"))
