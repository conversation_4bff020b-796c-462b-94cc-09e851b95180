��N
      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�	Tx.TradDt��
Tx.TradgCpcty��Tx.DerivNtnlChng��	Tx.TradVn��Tx.CtryOfBrnch��Tx.TradPlcMtchgId��Tx.CmplxTradCmpntId��Tx.Qty.Unit��Tx.Qty.NmnlVal.#text��Tx.Qty.NmnlVal.@Ccy��Tx.Qty.MntryVal.#text��Tx.Qty.MntryVal.@Ccy��	Tx.NetAmt��Tx.UpFrntPmt.Amt.#text��Tx.UpFrntPmt.Amt.@Ccy��Tx.Pric.Pric.MntryVal.Amt.#text��Tx.Pric.Pric.MntryVal.Amt.@Ccy��Tx.Pric.Pric.Pctg��Tx.Pric.Pric.Yld��Tx.Pric.Pric.BsisPts�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C        �t�bh:Nu��R�e]�(hhK ��h��R�(KKK��h!�]��2022-07-05T11:19:14Z�at�bhhK ��h��R�(KKK��h!�]��AOTC�at�bhhK ��h��R�(KKK��h!�]�Nat�bhhK ��h��R�(KKK��h!�]��XOFF�at�bhhK ��h��R�(KKK��h!�]�Nat�bhhK ��h��R�(KKK��h!�]�Nat�bhhK ��h��R�(KKK��h!�]�Nat�bhhK ��h��R�(KKK��h�f8�����R�(KhHNNNJ����J����K t�b�C    ��A�t�bhhK ��h��R�(KKK��h!�]�G�      at�bhhK ��h��R�(KKK��h!�]�G�      at�bhhK ��h��R�(KKK��h!�]�G�      at�bhhK ��h��R�(KKK��h!�]�G�      at�bhhK ��h��R�(KKK��h!�]�G�      at�bhhK ��h��R�(KKK��h!�]�G�      at�bhhK ��h��R�(KKK��h!�]�Nat�bhhK ��h��R�(KKK��h!�]��0.000005�at�bhhK ��h��R�(KKK��h!�]��EUR�at�bhhK ��h��R�(KKK��h��C      ��t�bhhK ��h��R�(KKK��h��C      ��t�bhhK ��h��R�(KKK��h��C      ��t�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h0at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bh:Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hQ�mgr_locs��builtins��slice���K KK��R�u}�(j�  hXj�  j�  KKK��R�u}�(j�  h_j�  j�  KKK��R�u}�(j�  hej�  j�  KKK��R�u}�(j�  hlj�  j�  KKK��R�u}�(j�  hrj�  j�  KKK��R�u}�(j�  hxj�  j�  KKK��R�u}�(j�  h~j�  j�  KKK��R�u}�(j�  h�j�  j�  KK	K��R�u}�(j�  h�j�  j�  K	K
K��R�u}�(j�  h�j�  j�  K
KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KK
K��R�u}�(j�  h�j�  j�  K
KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.