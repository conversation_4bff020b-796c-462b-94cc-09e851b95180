from pathlib import Path

import pandas as pd

from swarm_tasks.transform.steeleye.tr.iso20022.map_transaction import MapTransaction
from swarm_tasks.transform.steeleye.tr.iso20022.map_transaction import Out

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")

# The source file is a pickle of the df which should be used as the source dataframe
INPUT_PATH = TEST_FILES_DIR.joinpath("input_df.pkl")
EXPECTED_PATH = TEST_FILES_DIR.joinpath("expected_df.pkl")


class TestMapTransaction:
    def test_map_transaction_ensure_float_as_positional(self):
        task = MapTransaction(name="MapTransaction")

        expected = pd.read_pickle(EXPECTED_PATH)
        result = task.execute(source_frame=pd.read_pickle(INPUT_PATH))

        assert not pd.testing.assert_frame_equal(result, expected)
        assert result[Out.PRICE_MONETARY_VAL][0] == "0.000005"
