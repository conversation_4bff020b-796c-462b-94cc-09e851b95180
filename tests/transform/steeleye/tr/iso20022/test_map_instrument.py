from pathlib import Path
from typing import Optional

import pandas as pd
import pytest

from swarm_tasks.transform.steeleye.tr.iso20022.map_instrument import (
    calculate_underlying_index_term_value,
)
from swarm_tasks.transform.steeleye.tr.iso20022.map_instrument import (
    format_underlying_index_term,
)
from swarm_tasks.transform.steeleye.tr.iso20022.map_instrument import (
    MapInstrument,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")

# The source file is a pickle of the df which should be used as the source dataframe
INPUT_PATH = TEST_FILES_DIR.joinpath("map_instrument_input_df.pkl")
EXPECTED_PATH = TEST_FILES_DIR.joinpath("map_instrument_expected_df.pkl")

INPUT_PATH_SWAPS = TEST_FILES_DIR.joinpath("map_instrument_source_frame_with_swaps.pkl")
EXPECTED_PATH_SWAPS = TEST_FILES_DIR.joinpath("expected_frame_with_swaps.pkl")


@pytest.fixture()
def partial_pndg_source_frame() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "workflow.status": ["REPORTABLE", "REPORTABLE"],
            "instrumentDetails.instrument.instrumentFullName": [
                "Instrument Full Name",
                "Instrument Full Name",
            ],
            "parties.buyer": [
                [{"firmIdentifiers": {"lei": "BUYER_TEST_LEI"}}],
                [{"firmIdentifiers": {"lei": "BUYER_TEST_LEI"}}],
            ],
            "parties.executingEntity.firmIdentifiers.lei": [
                "EXECUTING_ENTITY_LEI",
                "EXECUTING_ENTITY_LEI",
            ],
            "workflow.eligibility.eligible": [True, True],
            "instrumentDetails.instrument.ext.underlyingInstruments": [
                [
                    {"instrumentIdCode": "ISIN_ONE"},
                    {"instrumentIdCode": "ISIN_TWO"},
                    {"instrumentIdCode": "ISIN_THREE"},
                ],
                [
                    {"instrumentIdCode": "ISIN_ONE"},
                    {"instrumentIdCode": "ISIN_TWO"},
                    {"instrumentIdCode": "ISIN_THREE"},
                ],
            ],
            "instrumentDetails.instrument.ext.strikePriceType": ["PNDG", "PNDG"],
            "instrumentDetails.instrument.derivative.strikePriceCurrency": [
                pd.NA,
                pd.NA,
            ],
            "instrumentDetails.instrument.ext.onFIRDS": [False, False],
            "instrumentDetails.instrument.derivative.expiryDate": [
                "2027-12-31",
                "2027-12-31",
            ],
            "instrumentDetails.instrument.derivative.priceMultiplier": [1, 1],
            "instrumentDetails.instrument.notionalCurrency1": ["USD", "USD"],
            "instrumentDetails.instrument.instrumentClassification": [
                "SEBPXC",
                "SEBPXC",
            ],
            "instrumentDetails.instrument.derivative.deliveryType": ["CASH", "CASH"],
            "workflow.eligibility.totv": [False, False],
            "transactionDetails.venue": ["XXXX", "XXXX"],
            "reportDetails.reportStatus": ["NEWT", "NEWT"],
        }
    )


@pytest.fixture()
def partial_pndg_expected_df() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "FinInstrm.Id": [pd.NA, pd.NA],
            "FinInstrm.Othr.FinInstrmGnlAttrbts.FullNm": [
                "Instrument Full Name",
                "Instrument Full Name",
            ],
            "FinInstrm.Othr.FinInstrmGnlAttrbts.ClssfctnTp": ["SEBPXC", "SEBPXC"],
            "FinInstrm.Othr.FinInstrmGnlAttrbts.NtnlCcy": ["USD", "USD"],
            "FinInstrm.Othr.DerivInstrmAttrbts.DlvryTp": ["CASH", "CASH"],
            "FinInstrm.Othr.DerivInstrmAttrbts.XpryDt": ["2027-12-31", "2027-12-31"],
            "FinInstrm.Othr.DerivInstrmAttrbts.PricMltplr": [1, 1],
            "FinInstrm.Othr.DerivInstrmAttrbts.OptnTp": [None, None],
            "FinInstrm.Othr.DerivInstrmAttrbts.OptnExrcStyle": [None, None],
            "FinInstrm.Othr.DebtInstrmAttrbts.MtrtyDt": [None, None],
            "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.Indx": [
                pd.NA,
                pd.NA,
            ],
            "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Sngl.ISIN": [
                pd.NA,
                pd.NA,
            ],
            "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Sngl.Indx": [
                pd.NA,
                pd.NA,
            ],
            "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Bskt.Indx": [
                pd.NA,
                pd.NA,
            ],
            "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Bskt": [
                [{"ISIN": ["ISIN_ONE", "ISIN_TWO", "ISIN_THREE"]}],
                [{"ISIN": ["ISIN_ONE", "ISIN_TWO", "ISIN_THREE"]}],
            ],
            "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Sngl.ISIN": [
                pd.NA,
                pd.NA,
            ],
            "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Sngl.Indx": [
                pd.NA,
                pd.NA,
            ],
            "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Bskt.Indx": [
                pd.NA,
                pd.NA,
            ],
            "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Bskt": [
                pd.NA,
                pd.NA,
            ],
            "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.ISIN": [
                pd.NA,
                pd.NA,
            ],
            "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.Indx.Nm.RefRate.Nm": [
                pd.NA,
                pd.NA,
            ],
            "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.Indx.Term.Unit": [
                pd.NA,
                pd.NA,
            ],
            "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.Indx.Term.Value": [
                pd.NA,
                pd.NA,
            ],
            "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Sngl.Indx": [pd.NA, pd.NA],
            "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Bskt": [
                [{"ISIN": ["ISIN_ONE", "ISIN_TWO", "ISIN_THREE"]}],
                [{"ISIN": ["ISIN_ONE", "ISIN_TWO", "ISIN_THREE"]}],
            ],
            "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Bskt.Indx": [
                pd.NA,
                pd.NA,
            ],
            "FinInstrm.Othr.DerivInstrmAttrbts.AsstClssSpcfcAttrbts.FX.OthrNtnlCcy": [
                pd.NA,
                pd.NA,
            ],
            "FinInstrm.Othr.DerivInstrmAttrbts.StrkPric.NoPric.Pdg": ["PNDG", "PNDG"],
            "FinInstrm.Othr.DerivInstrmAttrbts.StrkPric.NoPric.Ccy": [pd.NA, pd.NA],
        }
    )


class TestMapInstrument:
    """
    Test cases for the MapInstrument task, and it's utility funtions
    """

    def test_map_istrument_end_to_end_without_swaps(self) -> None:
        ...
        task = MapInstrument(name="TestMapInstrument")

        expected = pd.read_pickle(EXPECTED_PATH)
        result = task.execute(source_frame=pd.read_pickle(INPUT_PATH))
        assert not pd.testing.assert_frame_equal(
            left=result,
            right=expected,
            check_dtype=False,
        )

    def test_map_instrument_end_to_end_with_swaps(self) -> None:
        task = MapInstrument(name="TestMapInstrument")

        expected = pd.read_pickle(EXPECTED_PATH_SWAPS)
        result = task.execute(source_frame=pd.read_pickle(INPUT_PATH_SWAPS))
        assert not pd.testing.assert_frame_equal(
            left=result,
            right=expected,
            check_dtype=False,
        )

    def test_map_instrument_with_pndg_data(
        self,
        partial_pndg_source_frame: pd.DataFrame,
        partial_pndg_expected_df: pd.DataFrame,
    ) -> None:
        ...
        task = MapInstrument(name="TestMapInstrument")
        result = task.execute(source_frame=partial_pndg_source_frame)
        assert not pd.testing.assert_frame_equal(
            left=result,
            right=partial_pndg_expected_df,
            check_dtype=False,
        )

    @pytest.mark.parametrize(
        "input_underlying_index_term_value, input_underlying_index_term, expected_value",
        [
            ("1ABC", None, "1ABC"),
            (None, "22TEST25", "22"),
            (None, "TEST", pd.NA),
            (None, None, pd.NA),
            (pd.NA, pd.NA, pd.NA),
        ],
    )
    def test_calculate_underlying_index_term_value_end_to_end(
        self,
        input_underlying_index_term_value: Optional[str],
        input_underlying_index_term: Optional[str],
        expected_value: Optional[str],
    ) -> None:
        """
        First Test-Case:
            UnderlyingIndexTermValue is provided so it should return that as it's
        Second Test-Case:
            UnderlyingIndexTermValue is None but UnderlyingIndexTerm is there so it
            should return first digit group, i.e, 22
        Third Test-Case:
            UnderlyingIndexTermValue is None but UnderlyingIndexTerm is there, but it
            has no numeric group in it so should return pd.NA
        Fourth Test-Case:
            UnderlyingIndexTermValue and UnderlyingIndexTerm both None so should return
            pd.NA

        :param input_underlying_index_term_value: UnderlyingIndexTermValue if any
        :param input_underlying_index_term: UnderlyingIndexTerm if any
        :param expected_value: Expected value, i.e, result if any
        """
        result = calculate_underlying_index_term_value(
            underlying_index_term_value=input_underlying_index_term_value,
            underlying_index_term=input_underlying_index_term,
        )

        if pd.isna(expected_value):
            assert pd.isna(result)
        else:
            assert expected_value == result

    @pytest.mark.parametrize(
        "input_underlying_index_term, expected_value",
        [
            ("2abc2", "ABC"),
            ("12345", pd.NA),
            (None, pd.NA),
            ("abc", "ABC"),
            (pd.NA, pd.NA),
        ],
    )
    def test_format_underlying_index_term_end_to_end(
        self,
        input_underlying_index_term: Optional[str],
        expected_value: Optional[str],
    ) -> None:
        """
        First Test-Case:
            UnderlyingIndexTerm is provided, so it should return ABC by filtering numbers
        Second Test-Case:
            UnderlyingIndexTerm doesn't contain any alpha char so pd.NA should be returned
        Third Test-Case:
            None value should give pd.NA
        Fourth Test-Case:
            UnderlyingIndexTerm is provided, so it should return ABC in upper-case
        """
        result = format_underlying_index_term(
            underlying_index_term=input_underlying_index_term
        )

        if pd.isna(expected_value):
            assert pd.isna(result)
        else:
            assert expected_value == result
