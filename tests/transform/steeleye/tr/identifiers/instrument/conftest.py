from datetime import datetime

import pandas as pd
import pytest
from se_trades_tasks.order_and_tr.static import DFColumns


# TODO consolidate the input and expected fixtures into fewer fixtures.
#   After each test realized I needed more data in the fixtures,
#   but did not want to break existing tests, hence the additional fixtures.


@pytest.fixture()
def input_asset_class_params_1() -> dict:
    """Minimum required param attributes for an asset class."""
    return dict(
        asset_class_attribute="Asset Class",
        bbg_figi_id_attribute="Bloomberg FIGI ID",
        eurex_id_attribute="Eurex ID",
        currency_attribute="Price Currency",
        expiry_date_attribute="Maturity Date",
        isin_attribute="ISIN",
        notional_currency_2_attribute="Notional Currency 2",
        option_strike_price_attribute="Option Strike Price",
        option_type_attribute="Option Type",
        underlying_index_name_attribute="Underlying Index Name",
        underlying_index_term_attribute="Underlying Index Term",
        underlying_isin_attribute="Underlying Instrument ISIN/s",
        underlying_symbol_attribute="Symbol",
        venue_attribute="Exchange MIC",
    )


@pytest.fixture()
def input_source_df_1() -> pd.DataFrame:
    """Simulates a source dataframe parsed from an input csv file.

    A minimalist data set.
    """
    df = pd.DataFrame(
        {
            "Asset Class": ["Cash", "cfd", pd.NA, "Bond", "Future"],
            "Price Currency": ["USD", "JPY", "AUD", "GBP", pd.NA],
            "Notional Currency 2": ["USD", "USD", "USD", "GBP", pd.NA],
            "Maturity Date": [
                "20200108",
                "20210730",
                "20201201",
                pd.NA,
                pd.NA,
            ],  # maturity date ie. expiry date
            "ISIN": [
                " US0378331005",
                "US0000000115 ",
                "US0000000125",
                "US0000000135 ",
                "GB0000000145",
            ],
            "Option Strike Price": [pd.NA, pd.NA, pd.NA, pd.NA, pd.NA],
            "Option Type": [pd.NA, pd.NA, pd.NA, pd.NA, pd.NA],
            "Underlying Index Name": ["XAUUSD", "EURUSD", "XAUUSD", "EURUSD", pd.NA],
            "Underlying Index Term": ["XAUUSD", "EURUSD", "XAUUSD", "EURUSD", pd.NA],
            "Underlying Instrument ISIN/s": [pd.NA, pd.NA, pd.NA, pd.NA, pd.NA],
            # "Symbol": ["ABC", "DEF", "GHI", "JKL", pd.NA],
            "Exchange MIC": ["XNYS", "XNAS", "xNCM", "Xlon", pd.NA],
        }
    )
    return df


@pytest.fixture()
def expected_transform_process_data_df_1() -> pd.DataFrame:
    """Represents the dataframe expected to be returned by InstrumentIdentifiers._process_data."""
    df = pd.DataFrame(
        {
            "Asset Class": ["Cash", "cfd", pd.NA, "Bond", "Future"],
            "Price Currency": ["USD", "JPY", "AUD", "GBP", pd.NA],
            "Notional Currency 2": ["USD", "USD", "USD", "GBP", pd.NA],
            "Maturity Date": ["20200108", "20210730", "20201201", pd.NA, pd.NA],
            "ISIN": [
                "US0378331005",
                "US0000000115",
                "US0000000125",
                "US0000000135",
                "GB0000000145",
            ],
            "Option Strike Price": pd.NA,
            "Option Type": pd.NA,
            "Underlying Index Name": ["XAUUSD", "EURUSD", "XAUUSD", "EURUSD", pd.NA],
            "Underlying Index Term": ["XAUUSD", "EURUSD", "XAUUSD", "EURUSD", pd.NA],
            "Underlying Instrument ISIN/s": pd.NA,
            "Exchange MIC": ["XNYS", "XNAS", "XNCM", "XLON", pd.NA],
            "Bloomberg FIGI ID": pd.NA,
            "Eurex ID": pd.NA,
            "__interest_rate_start_date__": pd.NA,
            "__notional_currency_1__": pd.NA,
            "__swap_near_leg_date__": pd.NA,
            "__und_index_name_leg_2__": pd.NA,
            "__underlying_index_series__": pd.NA,
            "__und_index_term_value__": pd.NA,
            "__underlying_index_version__": pd.NA,
            "Symbol": [pd.NA, pd.NA, pd.NA, pd.NA, pd.NA],
            "__und_index_term_leg_2__": pd.NA,
            "__und_index_term_value_leg_2__": pd.NA,
            "__venue_financial_instrument_short_name__": pd.NA,
            "__instrument_classification__": pd.NA,
            "__df_columns_currency__": ["USD", "JPY", "AUD", "GBP", pd.NA],
            "__df_columns_notional_currency_2__": ["USD", "USD", "USD", "GBP", pd.NA],
            "__df_columns_notional_currency_2_not_equal_to_notional_currency_1__": pd.NA,
            "__df_columns_notional_currency_1_plus_2_conditional__": pd.NA,
            "__df_columns_asset_class__": ["Cash", "cfd", pd.NA, "Bond", "Future"],
            "__df_columns_expiry_date__": [
                "2020-01-08",
                "2021-07-30",
                "2020-12-01",
                pd.NA,
                pd.NA,
            ],
            "__df_columns_expiry_date_timestamp__": [
                "2020-01-08 00:00:00",
                "2021-07-30 00:00:00",
                "2020-12-01 00:00:00",
                pd.NA,
                pd.NA,
            ],
            "__df_columns_expiry_month__": [
                "2020-01",
                "2021-07",
                "2020-12",
                pd.NA,
                pd.NA,
            ],
            "__df_columns_expiry_month_timestamp__": [
                "2020-01 00:00:00",
                "2021-07 00:00:00",
                "2020-12 00:00:00",
                pd.NA,
                pd.NA,
            ],
            "__df_columns_days_to_expiry_term__": pd.NA,
            "__df_columns_swap_near_leg_date__": pd.NA,
            "__df_columns_option_strike_price__": pd.NA,
            "__df_columns_option_strike_price_div_100__": pd.NA,
            "__df_columns_option_strike_price_div_1000__": pd.NA,
            "__df_columns_option_strike_price_div_10000__": pd.NA,
            "__df_columns_option_type__": pd.NA,
            "__df_columns_underlying_index_term__": [
                "XAUUSD",
                "EURUSD",
                "XAUUSD",
                "EURUSD",
                pd.NA,
            ],
            "__df_columns_underlying_index_term_value__": pd.NA,
            "__df_columns_underlying_index_term_leg_2__": pd.NA,
            "__df_columns_underlying_index_term_value_leg_2__": pd.NA,
            "__df_columns_underlying_symbol__": [pd.NA, pd.NA, pd.NA, pd.NA, pd.NA],
            "__df_columns_underlying_index_name__": [
                "XAUUSD",
                "EURUSD",
                "XAUUSD",
                "EURUSD",
                pd.NA,
            ],
            "__df_columns_underlying_index_name_for_interest_rate_index__": [
                "XAUUSD",
                "EURUSD",
                "XAUUSD",
                "EURUSD",
                pd.NA,
            ],
            "__df_columns_underlying_index_name_for_iri_first_3_chars__": [
                "XAU",
                "EUR",
                "XAU",
                "EUR",
                pd.NA,
            ],
            "__df_columns_underlying_index_name_not_curr_1__": pd.NA,
            "__df_columns_underlying_index_name_leg_2__": pd.NA,
        }
    )
    df["ISIN"] = df["ISIN"].astype("string")
    return df


@pytest.fixture()
def input_source_df_2(input_source_df_1) -> pd.DataFrame:
    """Source dataframe with additional currency columns"""
    input_source_df_1.loc[:, "Notional Currency 1"] = [
        "USD",
        "USD",
        "USD",
        "USD",
        pd.NA,
    ]
    input_source_df_1.loc[:, "Quantity Currency"] = ["EUR", "EUR", "EUR", "GBP", pd.NA]
    return input_source_df_1


@pytest.fixture()
def input_asset_class_params_2(input_asset_class_params_1) -> dict:
    """Input param object specifies currency attributes as lists.

    (The list items refer to the currency columns of the source df).
    """
    input_asset_class_params_1["currency_attribute"] = [
        "Price Currency",
        "Notional Currency 1",
    ]
    input_asset_class_params_1["notional_currency_2_attribute"] = [
        "Notional Currency 2",
        "Quantity Currency",
    ]
    return input_asset_class_params_1


@pytest.fixture()
def expected_transform_process_data_df_2() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "Asset Class": ["Cash", "cfd", pd.NA, "Bond", "Future"],
            "Price Currency": ["USD", "JPY", "AUD", "GBP", pd.NA],
            "Notional Currency 2": ["USD", "USD", "USD", "GBP", pd.NA],
            "Maturity Date": ["20200108", "20210730", "20201201", pd.NA, pd.NA],
            "ISIN": [
                "US0378331005",
                "US0000000115",
                "US0000000125",
                "US0000000135",
                "GB0000000145",
            ],
            "Option Strike Price": pd.NA,
            "Option Type": pd.NA,
            "Underlying Index Name": ["XAUUSD", "EURUSD", "XAUUSD", "EURUSD", pd.NA],
            "Underlying Index Term": ["XAUUSD", "EURUSD", "XAUUSD", "EURUSD", pd.NA],
            "Underlying Instrument ISIN/s": pd.NA,
            "Exchange MIC": ["XNYS", "XNAS", "XNCM", "XLON", pd.NA],
            "Notional Currency 1": ["USD", "USD", "USD", "USD", pd.NA],
            "Quantity Currency": ["EUR", "EUR", "EUR", "GBP", pd.NA],
            "Bloomberg FIGI ID": pd.NA,
            "Eurex ID": pd.NA,
            "__interest_rate_start_date__": pd.NA,
            "__notional_currency_1__": pd.NA,
            "__swap_near_leg_date__": pd.NA,
            "__und_index_name_leg_2__": pd.NA,
            "__underlying_index_series__": pd.NA,
            "__und_index_term_value__": pd.NA,
            "__underlying_index_version__": pd.NA,
            "Symbol": [pd.NA, pd.NA, pd.NA, pd.NA, pd.NA],
            "__und_index_term_leg_2__": pd.NA,
            "__und_index_term_value_leg_2__": pd.NA,
            "__venue_financial_instrument_short_name__": pd.NA,
            "__instrument_classification__": pd.NA,
            "__df_columns_currency__": ["USD", "JPY", "AUD", "GBP", pd.NA],
            "__df_columns_notional_currency_2__": ["USD", "USD", "USD", "GBP", pd.NA],
            "__df_columns_notional_currency_2_not_equal_to_notional_currency_1__": pd.NA,
            "__df_columns_notional_currency_1_plus_2_conditional__": pd.NA,
            "__df_columns_asset_class__": ["Cash", "cfd", pd.NA, "Bond", "Future"],
            "__df_columns_expiry_date__": [
                "2020-01-08",
                "2021-07-30",
                "2020-12-01",
                pd.NA,
                pd.NA,
            ],
            "__df_columns_expiry_date_timestamp__": [
                "2020-01-08 00:00:00",
                "2021-07-30 00:00:00",
                "2020-12-01 00:00:00",
                pd.NA,
                pd.NA,
            ],
            "__df_columns_expiry_month__": [
                "2020-01",
                "2021-07",
                "2020-12",
                pd.NA,
                pd.NA,
            ],
            "__df_columns_expiry_month_timestamp__": [
                "2020-01 00:00:00",
                "2021-07 00:00:00",
                "2020-12 00:00:00",
                pd.NA,
                pd.NA,
            ],
            "__df_columns_days_to_expiry_term__": pd.NA,
            "__df_columns_swap_near_leg_date__": pd.NA,
            "__df_columns_option_strike_price__": pd.NA,
            "__df_columns_option_strike_price_div_100__": pd.NA,
            "__df_columns_option_strike_price_div_1000__": pd.NA,
            "__df_columns_option_strike_price_div_10000__": pd.NA,
            "__df_columns_option_type__": pd.NA,
            "__df_columns_underlying_index_term__": [
                "XAUUSD",
                "EURUSD",
                "XAUUSD",
                "EURUSD",
                pd.NA,
            ],
            "__df_columns_underlying_index_term_value__": pd.NA,
            "__df_columns_underlying_index_term_leg_2__": pd.NA,
            "__df_columns_underlying_index_term_value_leg_2__": pd.NA,
            "__df_columns_underlying_symbol__": [pd.NA, pd.NA, pd.NA, pd.NA, pd.NA],
            "__df_columns_underlying_index_name__": [
                "XAUUSD",
                "EURUSD",
                "XAUUSD",
                "EURUSD",
                pd.NA,
            ],
            "__df_columns_underlying_index_name_for_interest_rate_index__": [
                "XAUUSD",
                "EURUSD",
                "XAUUSD",
                "EURUSD",
                pd.NA,
            ],
            "__df_columns_underlying_index_name_for_iri_first_3_chars__": [
                "XAU",
                "EUR",
                "XAU",
                "EUR",
                pd.NA,
            ],
            "__df_columns_underlying_index_name_not_curr_1__": pd.NA,
            "__df_columns_underlying_index_name_leg_2__": pd.NA,
        }
    )
    df["ISIN"] = df["ISIN"].astype("string")
    return df


@pytest.fixture()
def input_transform_process_data_df_3() -> pd.DataFrame:
    """Represents the dataframe expected to be returned by InstrumentIdentifiers._process_data."""
    df = pd.DataFrame(
        {
            "Asset Class": [
                "sb",
                "fx option other",
                pd.NA,
                "fx sb",
                "equity forward",
                "equity swap",
                "fx option barrier",
                "fx option barrier digital",
                "fx option lookback",
                "fx option",
                "fx swap",
                "option",
                "future",
            ],
            "Price Currency": [
                "USD",
                "JPY",
                "AUD",
                "GBP",
                "GBP",
                pd.NA,
                "USD",
                "JPY",
                "AUD",
                "GBP",
                "USD",
                "JPY",
                "AUD",
            ],
            "Notional Currency 2": [
                "USD",
                "USD",
                "USD",
                "GBP",
                "GBP",
                pd.NA,
                "USD",
                "JPY",
                "AUD",
                "GBP",
                "USD",
                "USD",
                "USD",
            ],
            "Maturity Date": [
                "20200108",
                "20210730",
                "20201201",
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
            ],
            "ISIN": [
                "US0378331005",
                "US0000000115",
                "US0000000125",
                "US0000000135",
                "GB0000000145",
                "GB0000000245",
                "US0478331005",
                "US0000000225",
                "US0000000325",
                "US0000000425",
                "GB0000000525",
                "US0000000625",
                "GB0000000725",
            ],
            "Option Strike Price": [
                pd.NA,
                12.0,
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                13.0,
                14.0,
                15.0,
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
            ],
            "Option Type": [
                pd.NA,
                "typ1",
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                "typ2",
                "typ3",
                "typ4",
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
            ],
            "Underlying Index Name": [
                "XAUUSD",
                "EURUSD",
                "XAUUSD",
                "EURUSD",
                pd.NA,
                pd.NA,
                "XAUUSD",
                "EURUSD",
                "XAUUSD",
                "EURUSD",
                "EURUSD",
                "EURUSD",
                "XAUUSD",
            ],
            "Underlying Index Term": [
                "",
                "",
                "",
                "",
                pd.NA,
                pd.NA,
                "",
                "",
                "",
                "",
                pd.NA,
                "",
                "",
            ],
            "Underlying Instrument ISIN/s": pd.NA,
            "Symbol": [
                "ABC",
                "DEF",
                "GHI",
                "JKL",
                pd.NA,
                pd.NA,
                "ABC",
                "DEF",
                "GHI",
                "JKL",
                "ABC",
                "ABC",
                "DEF",
            ],
            "Exchange MIC": [
                "XNYS",
                "XNAS",
                "XNCM",
                "XLON",
                pd.NA,
                pd.NA,
                "XNYS",
                "XNAS",
                "XNCM",
                "XLON",
                "XNYS",
                "XNAS",
                "XNCM",
            ],
            "__swap_near_leg_date__": pd.NA,
            "__underlying_index_series__": pd.NA,
            "__underlying_index_version__": pd.NA,
            "__df_columns_currency__": [
                "USD",
                "JPY",
                "AUD",
                "GBP",
                pd.NA,
                pd.NA,
                "USD",
                "JPY",
                "AUD",
                "GBP",
                "USD",
                "JPY",
                "AUD",
            ],
            "__df_columns_notional_currency_2__": [
                "USD",
                "USD",
                "USD",
                "GBP",
                pd.NA,
                pd.NA,
                "USD",
                "JPY",
                "AUD",
                "GBP",
                "USD",
                "USD",
                "USD",
            ],
            "__df_columns_asset_class__": [
                "sb",
                "fx option other",
                pd.NA,
                "fx sb",
                "equity forward",
                "equity swap",
                "fx option barrier",
                "fx option barrier digital",
                "fx option lookback",
                "fx option",
                "fx swap",
                "option",
                "future",
            ],
            "__df_columns_expiry_date__": [
                "2020-01-08",
                "2021-07-30",
                "2020-12-01",
                pd.NA,
                "2020-12-01",
                "2020-12-01",
                "2020-01-08",
                "2021-07-30",
                "2020-12-01",
                pd.NA,
                pd.NA,
                "2021-07-30",
                "2020-12-01",
            ],
            "__df_columns_expiry_date_timestamp__": [
                "2020-01-08 00:00:00",
                "2021-07-30 00:00:00",
                "2020-12-01 00:00:00",
                pd.NA,
                "2020-12-01 00:00:00",
                "2020-12-01 00:00:00",
                "2020-01-08 00:00:00",
                "2021-07-30 00:00:00",
                "2020-12-01 00:00:00",
                pd.NA,
                pd.NA,
                "2021-07-30 00:00:00",
                "2020-12-01 00:00:00",
            ],
            "__df_columns_expiry_month__": [
                "2020-01",
                "2021-07",
                "2020-12",
                pd.NA,
                "2020-12",
                "2020-12",
                "2020-01",
                "2021-07",
                "2020-12",
                pd.NA,
                pd.NA,
                "2021-07",
                "2020-12",
            ],
            "__df_columns_expiry_month_timestamp__": [
                "2020-01 00:00:00",
                "2021-07 00:00:00",
                "2020-12 00:00:00",
                pd.NA,
                "2020-12 00:00:00",
                "2020-12 00:00:00",
                "2020-01 00:00:00",
                "2021-07 00:00:00",
                "2020-12 00:00:00",
                pd.NA,
                pd.NA,
                "2021-07 00:00:00",
                "2020-12 00:00:00",
            ],
            "__df_columns_days_to_expiry_term__": pd.NA,
            "__df_columns_swap_near_leg_date__": pd.NA,
            "__df_columns_option_strike_price__": [
                pd.NA,
                "12.00000000",
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                "13.00000000",
                "14.00000000",
                "15.00000000",
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
            ],
            "__df_columns_option_strike_price_div_100__": [
                pd.NA,
                "0.12000000",
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                "0.13000000",
                "0.14000000",
                "0.15000000",
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
            ],
            "__df_columns_option_strike_price_div_1000__": [
                pd.NA,
                "0.01200000",
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                "0.01300000",
                "0.01400000",
                "0.01500000",
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
            ],
            "__df_columns_option_strike_price_div_10000__": [
                pd.NA,
                "0.00120000",
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                "0.00130000",
                "0.00140000",
                "0.00150000",
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
            ],
            "__df_columns_option_type__": [
                pd.NA,
                "TYP1",
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                "TYP2",
                "TYP3",
                "TYP4",
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
            ],
            None: ["", "", "", "", pd.NA, pd.NA, "", "", "", "", pd.NA, "", ""],
        }
    )
    return df


@pytest.fixture()
def input_df_get_isin_identifiers(expected_transform_process_data_df_1) -> pd.DataFrame:
    return expected_transform_process_data_df_1


@pytest.fixture()
def expected_df_get_isin_identifiers() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "isin": [
                "US0378331005",  # non CFD asset class
                pd.NA,
                "US0000000125",  # non CFD asset class
                "US0000000135",  # non CFD asset class
                "GB0000000145",  # non CFD asset class
            ],
            "isin_ccy_venue": [pd.NA, pd.NA, pd.NA, pd.NA, pd.NA],
            "cfd_isin": [
                pd.NA,
                "XXXXUS0000000115CFD",  # CFD asset class
                pd.NA,
                pd.NA,
                pd.NA,
            ],
            "cfd_isin_ccy": [
                pd.NA,
                "XXXXUS0000000115JPYCFD",  # CFD asset class
                pd.NA,
                pd.NA,
                pd.NA,
            ],
        }
    )
    return df.astype(object)


@pytest.fixture()
def input_df_underlying_isin_identifiers(
    expected_transform_process_data_df_1,
) -> pd.DataFrame:
    """Source data with various different asset classes
    and the same arbitrary underlying `underlying_isin_attribute`.

    This simulates data that has already gone through the `_process_data` method.
    """
    expected_transform_process_data_df_1.loc[:, "Underlying Instrument ISIN/s"] = [
        "US0378330099",
        "US0378330099",
        "US0378330099",
        "US0378330099",
        "US0378330099",
    ]
    expected_transform_process_data_df_1.loc[:, "__df_columns_asset_class__"] = [
        "equity forward",
        "cfd",
        "option",
        "equity swap",
        "future",
    ]
    expected_transform_process_data_df_1.loc[:, "__df_columns_expiry_date__"] = [
        "2020-01-08",
        "2021-07-30",
        "2020-12-01",
        "2020-12-01",
        "2020-12-01",
    ]
    expected_transform_process_data_df_1.loc[:, "__df_columns_option_type__"] = [
        pd.NA,
        pd.NA,
        "Call",
        pd.NA,
        pd.NA,
    ]
    expected_transform_process_data_df_1.loc[
        :, "__df_columns_option_strike_price__"
    ] = [
        pd.NA,
        pd.NA,
        "300.1",
        pd.NA,
        pd.NA,
    ]
    # TODO add some futures data
    return expected_transform_process_data_df_1


@pytest.fixture()
def expected_df_underlying_isin_identifiers() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "cfd_underlying_isin_ccy": [
                pd.NA,
                "XXXXUS0378330099JPYCFD",  # CFD asset class
                pd.NA,
                pd.NA,
                pd.NA,
            ],
            "equity_forward_under_isin_exp_date": [
                "XXXXUS0378330099EQFWD2020-01-08",
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
            ],
            "equity_swap_under_isin_exp_date": [
                pd.NA,
                pd.NA,
                pd.NA,
                "XXXXUS0378330099EQSWAP2020-12-01",
                pd.NA,
            ],
            "future_venue_under_isin_exp_date": [pd.NA, pd.NA, pd.NA, pd.NA, pd.NA],
            "future_venue_under_isin_exp_month": [pd.NA, pd.NA, pd.NA, pd.NA, pd.NA],
            "option_venue_under_isin_option_type_exp_date": [
                pd.NA,
                pd.NA,
                "XNCMUS0378330099OCall2020-12-01 00:00:00",
                pd.NA,
                pd.NA,
            ],
            "option_venue_under_isin_option_type_exp_month": [
                pd.NA,
                pd.NA,
                "XNCMUS0378330099OCall2020-12 00:00:00",
                pd.NA,
                pd.NA,
            ],
            "option_venue_under_isin_option_type_exp_date_price": [
                pd.NA,
                pd.NA,
                "XNCMUS0378330099OCall2020-12-01 00:00:00300.1",
                pd.NA,
                pd.NA,
            ],
            "option_venue_under_isin_option_type_exp_month_price": [
                pd.NA,
                pd.NA,
                "XNCMUS0378330099OCall2020-12 00:00:00300.1",
                pd.NA,
                pd.NA,
            ],
        }
    )
    return df


@pytest.fixture()
def input_df_get_non_isin_identifiers(
    input_df_underlying_isin_identifiers,
) -> pd.DataFrame:
    input_df_underlying_isin_identifiers.loc[:, "__df_columns_asset_class__"] = [
        "cds index",
        "fx forward",
        "option",
        "fx cfd",
        "future",
    ]
    input_df_underlying_isin_identifiers.loc[:, "Symbol"] = [
        "ABC",
        "DEF",
        "GHI",
        "JKL",
        "MNO",
    ]
    input_df_underlying_isin_identifiers.loc[:, "Exchange MIC"] = [
        "XNYS",
        "XNAS",
        "XNCM",
        "XLON",
        "XHKF",
    ]
    input_df_underlying_isin_identifiers.loc[
        :, "__df_columns_expiry_date_timestamp__"
    ] = [
        "2020-01-08 00:00:00",
        "2021-07-30 00:00:00",
        "2020-12-01 00:00:00",
        "2020-12-02 00:00:00",
        "2020-12-03 00:00:00",
    ]
    input_df_underlying_isin_identifiers.loc[
        :, "__df_columns_expiry_month_timestamp__"
    ] = [
        "2020-01 00:00:00",
        "2021-07 00:00:00",
        "2020-12 00:00:00",
        "2020-12 00:00:00",
        "2020-12 00:00:00",
    ]
    # TODO input data for: FX Spot, FX Swap, FX Option
    return input_df_underlying_isin_identifiers


@pytest.fixture()
def expected_df_get_non_isin_identifiers() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "future_venue_under_symbol_exp_date": [
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                "XHKFMNOFF2020-12-03 00:00:00",
            ],
            "future_venue_under_symbol_exp_month": [
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                "XHKFMNOFF2020-12 00:00:00",
            ],
            "fx_cfd_ccy_notional_ccy": [pd.NA, pd.NA, pd.NA, "XXXXGBPGBPFXCFD", pd.NA],
            "fx_forward_ccy_notional_ccy_exp_date": [
                pd.NA,
                "XXXXJPYUSDFXFWD2021-07-30",
                pd.NA,
                pd.NA,
                pd.NA,
            ],
            "option_venue_under_symbol_option_type_exp_date": [
                pd.NA,
                pd.NA,
                "XNCMGHIOCall2020-12-01 00:00:00",
                pd.NA,
                pd.NA,
            ],
            "option_venue_under_symbol_option_type_exp_month": [
                pd.NA,
                pd.NA,
                "XNCMGHIOCall2020-12 00:00:00",
                pd.NA,
                pd.NA,
            ],
            "option_venue_under_symbol_option_type_exp_date_price": [
                pd.NA,
                pd.NA,
                "XNCMGHIOCall2020-12-01 00:00:00300.1",
                pd.NA,
                pd.NA,
            ],
            "option_venue_under_symbol_option_type_exp_month_price": [
                pd.NA,
                pd.NA,
                "XNCMGHIOCall2020-12 00:00:00300.1",
                pd.NA,
                pd.NA,
            ],
        }
    )
    return df


@pytest.fixture()
def expected_result_isin_carrying_non_cfd_ccy_venue() -> pd.Series:
    result = pd.Series(
        ["US0378331005USDXNYS", "US0000000125AUDXNCM", "US0000000135GBPXLON"],
        index=[0, 2, 3],
    )
    return result


@pytest.fixture()
def expected_result_isin_carrying_non_cfd() -> pd.Series:
    result = pd.Series(
        ["US0378331005", "US0000000125", "US0000000135", "GB0000000145"],
        index=[0, 2, 3, 4],
    ).astype("string")
    return result


@pytest.fixture()
def expected_result_isin_carrying_cfd_isin() -> pd.Series:
    result = pd.Series(
        ["XXXXUS0000000115CFD"],
        index=[1],
    )
    return result


@pytest.fixture()
def expected_result_isin_carrying_cfd_isin_ccy() -> pd.Series:
    result = pd.Series(
        ["XXXXUS0000000115JPYCFD"],
        index=[1],
    )
    return result


@pytest.fixture()
def expected_result_underlying_isin_carrying_cfd() -> pd.Series:
    result = pd.Series(
        ["XXXXUS0378330099JPYCFD"],
        index=[1],
    )
    return result


@pytest.fixture()
def expected_result_isin_carrying_sb_isin() -> pd.Series:
    result = pd.Series(["XXXXUS0378331005SB"])
    return result


@pytest.fixture()
def expected_result_isin_carrying_sb_isin_ccy() -> pd.Series:
    result = pd.Series(["XXXXUS0378331005USDSB"])
    return result


@pytest.fixture()
def input_df_underlying_isin_identifiers_df_3(
    input_transform_process_data_df_3,
) -> pd.DataFrame:
    input_transform_process_data_df_3.loc[:, "Underlying Instrument ISIN/s"] = [
        "US0378331005",
        "US0000000115",
        "US0000000125",
        "US0000000135",
        "GB0000000145",
        "GB0000000245",
        "US0478331005",
        "US0000000225",
        "US0000000325",
        "US0000000425",
        "GB0000000525",
        "US0000000625",
        "GB0000000725",
    ]
    return input_transform_process_data_df_3


@pytest.fixture()
def expected_result_equity_forward() -> pd.Series:
    result = pd.Series(["XXXXGB0000000145EQFWD2020-12-01"], index=[4])
    return result


@pytest.fixture()
def expected_result_equity_swap() -> pd.Series:
    result = pd.Series(["XXXXGB0000000245EQSWAP2020-12-01"], index=[5])
    return result


@pytest.fixture()
def expected_result_fx_option_other() -> pd.Series:
    result = pd.Series(["XXXXJPYUSDOTYP1|OTHER|2021-07-30"], index=[1])
    return result


@pytest.fixture()
def expected_result_fx_option_other_sp() -> pd.Series:
    result = pd.Series(["XXXXJPYUSDOTYP1|OTHER|2021-07-3012.00000000"], index=[1])
    return result


@pytest.fixture()
def expected_result_fx_option_barrier() -> pd.Series:
    result = pd.Series(["XXXXUSDUSDOTYP2|BARRIER|2020-01-08"], index=[6])
    return result


@pytest.fixture()
def expected_result_fx_option_barrier_sp() -> pd.Series:
    result = pd.Series(["XXXXUSDUSDOTYP2|BARRIER|2020-01-0813.00000000"], index=[6])
    return result


@pytest.fixture()
def expected_result_fx_option_barrier_digital() -> pd.Series:
    result = pd.Series(["XXXXJPYJPYOTYP3|BARRIERDIGITAL|2021-07-30"], index=[7])
    return result


@pytest.fixture()
def expected_result_fx_option_barrier_digital_sp() -> pd.Series:
    result = pd.Series(
        ["XXXXJPYJPYOTYP3|BARRIERDIGITAL|2021-07-3014.00000000"], index=[7]
    )
    return result


@pytest.fixture()
def expected_result_fx_option_lookback() -> pd.Series:
    result = pd.Series(["XXXXAUDAUDOTYP4|LOOKBACK|2020-12-01"], index=[8])
    return result


@pytest.fixture()
def expected_result_fx_option_lookback_sp() -> pd.Series:
    result = pd.Series(["XXXXAUDAUDOTYP4|LOOKBACK|2020-12-0115.00000000"], index=[8])
    return result


@pytest.fixture()
def input_df_for_funcs_using_option_strike_price() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "__df_columns_asset_class__": ["option", "option", "option", "option"],
            "Exchange MIC": ["XNYM", "XOFF", "XNYM", "XNYM"],
            "Symbol": ["LO", "LO", "LO", pd.NA],
            "__df_columns_option_type__": ["C", "P", pd.NA, "C"],
            "__df_columns_expiry_month_timestamp__": [
                "2021-04 00:00:00",
                "2021-04 00:00:00",
                "2021-04 00:00:00",
                "2021-04 00:00:00",
            ],
            "__df_columns_expiry_date_timestamp__": [
                "2021-04-15 00:00:00",
                "2021-04-15 00:00:00",
                "2021-04-15 00:00:00",
                "2021-04-15 00:00:00",
            ],
            "Underlying Instrument ISIN/s": [
                pd.NA,
                "GB00H1JDWV54",
                pd.NA,
                "GB00H1JDWV54",
            ],
            "__df_columns_option_strike_price__": [
                "5900.00000000",
                pd.NA,
                "5200.00000000",
                "5200.00000000",
            ],
            "__df_columns_option_strike_price_div_100__": [
                "59.00000000",
                pd.NA,
                "52.00000000",
                "52.00000000",
            ],
            "__df_columns_option_strike_price_div_1000__": [
                "5.90000000",
                pd.NA,
                "5.20000000",
                "5.20000000",
            ],
            "__df_columns_option_strike_price_div_10000__": [
                "0.59000000",
                pd.NA,
                "0.52000000",
                "0.52000000",
            ],
        }
    )
    return df


@pytest.fixture()
def expected_result_option_underlying_symb_exp_dt_price() -> pd.Series:
    result = pd.Series(
        [
            [
                "XNYMLOOC2021-04-15 00:00:005900.00000000",
                "XNYMLOOC2021-04-15 00:00:0059.00000000",
                "XNYMLOOC2021-04-15 00:00:005.90000000",
                "XNYMLOOC2021-04-15 00:00:000.59000000",
            ],
        ],
        index=[0],
    )
    return result


@pytest.fixture()
def expected_result_option_underlying_symb_exp_month_price() -> pd.Series:
    result = pd.Series(
        [
            [
                "XNYMLOOC2021-04 00:00:005900.00000000",
                "XNYMLOOC2021-04 00:00:0059.00000000",
                "XNYMLOOC2021-04 00:00:005.90000000",
                "XNYMLOOC2021-04 00:00:000.59000000",
            ],
        ],
        index=[0],
    )
    return result


@pytest.fixture()
def expected_result_option_underlying_isin_exp_dt_price() -> pd.Series:
    result = pd.Series(
        [
            [
                "XNYMGB00H1JDWV54OC2021-04-15 00:00:005200.00000000",
                "XNYMGB00H1JDWV54OC2021-04-15 00:00:0052.00000000",
                "XNYMGB00H1JDWV54OC2021-04-15 00:00:005.20000000",
                "XNYMGB00H1JDWV54OC2021-04-15 00:00:000.52000000",
                "XXXXGB00H1JDWV54OC2021-04-15 00:00:005200.00000000",
                "XXXXGB00H1JDWV54OC2021-04-15 00:00:0052.00000000",
                "XXXXGB00H1JDWV54OC2021-04-15 00:00:005.20000000",
                "XXXXGB00H1JDWV54OC2021-04-15 00:00:000.52000000",
            ],
        ],
        index=[3],
    )
    return result


@pytest.fixture()
def expected_result_option_underlying_isin_exp_month_price() -> pd.Series:
    result = pd.Series(
        [
            [
                "XNYMGB00H1JDWV54OC2021-04 00:00:005200.00000000",
                "XNYMGB00H1JDWV54OC2021-04 00:00:0052.00000000",
                "XNYMGB00H1JDWV54OC2021-04 00:00:005.20000000",
                "XNYMGB00H1JDWV54OC2021-04 00:00:000.52000000",
                "XXXXGB00H1JDWV54OC2021-04 00:00:005200.00000000",
                "XXXXGB00H1JDWV54OC2021-04 00:00:0052.00000000",
                "XXXXGB00H1JDWV54OC2021-04 00:00:005.20000000",
                "XXXXGB00H1JDWV54OC2021-04 00:00:000.52000000",
            ],
        ],
        index=[3],
    )
    return result


@pytest.fixture()
def xlme_input_dataframe() -> pd.DataFrame:
    """Input data frame for testing identifiers for xlme options and futures"""
    df = pd.DataFrame(
        {
            "__df_columns_asset_class__": ["option", "option", "future", "future"],
            "Exchange MIC": ["XLME", "XLME", "XLME", "XLME"],
            "Symbol": ["LO", pd.NA, "PB", pd.NA],
            "__df_columns_currency__": ["EUR", "EUR", "EUR", "EUR"],
            "__df_columns_option_type__": ["C", "P", "P", "C"],
            "__df_columns_expiry_month_timestamp__": [
                "2021-04 00:00:00",
                "2021-04 00:00:00",
                "2021-04 00:00:00",
                "2021-04 00:00:00",
            ],
            "__df_columns_expiry_date_timestamp__": [
                "2021-04-15 00:00:00",
                "2021-04-15 00:00:00",
                "2021-04-15 00:00:00",
                "2021-04-15 00:00:00",
            ],
            "__df_columns_option_strike_price__": [
                "5900.00000000",
                "5900.00000000",
                pd.NA,
                pd.NA,
            ],
        }
    )
    return df


@pytest.fixture()
def fx_forward_source_frame() -> pd.DataFrame:
    """Source dataframe for fx_forward test"""
    df = pd.DataFrame(
        {
            "__df_columns_asset_class__": ["fx forward", "fx forward"],
            "__df_columns_currency__": ["USD", "USD"],
            "__df_columns_notional_currency_2__": ["CHF", pd.NA],
            "__df_columns_expiry_date__": ["2021-03-17", "2021-03-17"],
        }
    )
    return df


@pytest.fixture()
def expected_result_option_underlying_symb_currency_exp_dt_xlme() -> pd.Series:
    result = pd.Series(
        ["XLMELOEUROC2021-04-15 00:00:00"],
        index=[0],
    )
    return result


@pytest.fixture()
def expected_result_option_underlying_symb_currency_exp_month_xlme() -> pd.Series:
    result = pd.Series(
        ["XLMELOEUROC2021-04 00:00:00"],
        index=[0],
    )
    return result


@pytest.fixture()
def expected_result_option_underlying_symb_currency_exp_dt_price_xlme() -> pd.Series:
    result = pd.Series(
        ["XLMELOEUROC2021-04-15 00:00:005900.00000000"],
        index=[0],
    )
    return result


@pytest.fixture()
def expected_result_option_underlying_symb_currency_exp_month_price_xlme() -> pd.Series:
    result = pd.Series(
        ["XLMELOEUROC2021-04 00:00:005900.00000000"],
        index=[0],
    )
    return result


@pytest.fixture()
def expected_result_future_underlying_symb_currency_exp_dt_xlme() -> pd.Series:
    result = pd.Series(
        ["XLMEPBEURFF2021-04-15 00:00:00"],
        index=[2],
    )
    return result


@pytest.fixture()
def expected_result_future_underlying_symb_currency_exp_month_xlme() -> pd.Series:
    result = pd.Series(
        ["XLMEPBEURFF2021-04 00:00:00"],
        index=[2],
    )
    return result


@pytest.fixture()
def expected_result_fx_forward_ccy_notional_ccy_exp_date() -> pd.Series:
    result = pd.Series(
        ["XXXXUSDCHFFXFWD2021-03-17"],
        index=[0],
    )
    return result


@pytest.fixture()
def expected_result_fx_forward_notional_ccy_ccy_exp_date() -> pd.Series:
    result = pd.Series(
        ["XXXXCHFUSDFXFWD2021-03-17"],
        index=[0],
    )
    return result


@pytest.fixture()
def future_multiple_venues_symb_expiry_month_year():
    """Source dataframe for test_future_venues_symb_expiry_month_year test"""
    df = pd.DataFrame(
        {
            "__asset_class__": ["future", "future"],
            "__df_columns_expiry_month_timestamp__": [
                "2021-12 00:00:00",
                "2025-05 00:00:00",
            ],
            "__symbol__": ["HG", "SI"],
            "__option_type__": [pd.NA, pd.NA],
            "__option_strike_price__": [pd.NA, pd.NA],
        }
    )
    return df


@pytest.fixture()
def option_multiple_venues_symb_expiry_month_strike_price():
    """
    Source dataframe for ***** test
    :return:
    """
    df = pd.DataFrame(
        {
            "__asset_class__": ["option"],
            "__expiry_date__": ["2022-03"],
            "__symbol__": ["OZB"],
            "__option_type__": ["CALL"],
            "__option_strike_price__": [1710],
        }
    )
    return df


@pytest.fixture()
def input_asset_class_params_3() -> dict:
    return {
        "asset_class_attribute": "__asset_class__",
        "expiry_date_attribute": "__expiry_date__",
        "underlying_symbol_attribute": "__symbol__",
        "option_type_attribute": "__option_type__",
        "option_strike_price_attribute": "__option_strike_price__",
        "expiry_date_format": "%Y-%m",
        "venues_list": ["XCEC", "XCBT", "XNYM", "XCME", "XCBF", "GLBX", "XPBT"],
    }


@pytest.fixture()
def expected_result_future_venues_symb_expiry_month_year() -> pd.Series:
    result = pd.Series(
        [
            [
                "XCECHGFF2021-12 00:00:00",
                "XCBTHGFF2021-12 00:00:00",
                "XNYMHGFF2021-12 00:00:00",
                "XCMEHGFF2021-12 00:00:00",
                "XCBFHGFF2021-12 00:00:00",
                "GLBXHGFF2021-12 00:00:00",
                "XPBTHGFF2021-12 00:00:00",
            ],
            [
                "XCECSIFF2025-05 00:00:00",
                "XCBTSIFF2025-05 00:00:00",
                "XNYMSIFF2025-05 00:00:00",
                "XCMESIFF2025-05 00:00:00",
                "XCBFSIFF2025-05 00:00:00",
                "GLBXSIFF2025-05 00:00:00",
                "XPBTSIFF2025-05 00:00:00",
            ],
        ],
        index=[0, 1],
    )
    return result


@pytest.fixture()
def expected_option_multiple_venues_symb_expiry_month_strike_price() -> pd.Series:
    result = pd.Series(
        [
            [
                "XCECOZBOC2022-03 00:00:001710.00000000",
                "XCBTOZBOC2022-03 00:00:001710.00000000",
                "XNYMOZBOC2022-03 00:00:001710.00000000",
                "XCMEOZBOC2022-03 00:00:001710.00000000",
                "XCBFOZBOC2022-03 00:00:001710.00000000",
                "GLBXOZBOC2022-03 00:00:001710.00000000",
                "XPBTOZBOC2022-03 00:00:001710.00000000",
                "XCECOZBOC2022-03 00:00:0017.10000000",
                "XCBTOZBOC2022-03 00:00:0017.10000000",
                "XNYMOZBOC2022-03 00:00:0017.10000000",
                "XCMEOZBOC2022-03 00:00:0017.10000000",
                "XCBFOZBOC2022-03 00:00:0017.10000000",
                "GLBXOZBOC2022-03 00:00:0017.10000000",
                "XPBTOZBOC2022-03 00:00:0017.10000000",
                "XCECOZBOC2022-03 00:00:001.71000000",
                "XCBTOZBOC2022-03 00:00:001.71000000",
                "XNYMOZBOC2022-03 00:00:001.71000000",
                "XCMEOZBOC2022-03 00:00:001.71000000",
                "XCBFOZBOC2022-03 00:00:001.71000000",
                "GLBXOZBOC2022-03 00:00:001.71000000",
                "XPBTOZBOC2022-03 00:00:001.71000000",
                "XCECOZBOC2022-03 00:00:000.17100000",
                "XCBTOZBOC2022-03 00:00:000.17100000",
                "XNYMOZBOC2022-03 00:00:000.17100000",
                "XCMEOZBOC2022-03 00:00:000.17100000",
                "XCBFOZBOC2022-03 00:00:000.17100000",
                "GLBXOZBOC2022-03 00:00:000.17100000",
                "XPBTOZBOC2022-03 00:00:000.17100000",
            ]
        ],
        index=[0],
    )
    return result


@pytest.fixture()
def input_transform_process_data_df_4(input_source_df_1) -> pd.DataFrame:
    """Represents the dataframe expected to be returned by InstrumentIdentifiers._process_data."""
    input_source_df_1.loc[:, "Maturity Date"] = [
        "20200108",
        "20210730",
        "20201201",
        "99991231",
        pd.NA,
    ]

    return input_source_df_1


@pytest.fixture()
def expected_transform_process_data_df_4() -> pd.DataFrame:
    """Represents the dataframe expected to be returned by InstrumentIdentifiers._process_data."""
    df = pd.DataFrame(
        {
            "Asset Class": ["Cash", "cfd", pd.NA, "Bond", "Future"],
            "Price Currency": ["USD", "JPY", "AUD", "GBP", pd.NA],
            "Notional Currency 2": ["USD", "USD", "USD", "GBP", pd.NA],
            "Maturity Date": ["20200108", "20210730", "20201201", "99991231", pd.NA],
            "ISIN": [
                "US0378331005",
                "US0000000115",
                "US0000000125",
                "US0000000135",
                "GB0000000145",
            ],
            "Option Strike Price": pd.NA,
            "Option Type": pd.NA,
            "Underlying Index Name": ["XAUUSD", "EURUSD", "XAUUSD", "EURUSD", pd.NA],
            "Underlying Index Term": ["XAUUSD", "EURUSD", "XAUUSD", "EURUSD", pd.NA],
            "Underlying Instrument ISIN/s": pd.NA,
            "Exchange MIC": ["XNYS", "XNAS", "XNCM", "XLON", pd.NA],
            "Bloomberg FIGI ID": pd.NA,
            "Eurex ID": pd.NA,
            "__interest_rate_start_date__": pd.NA,
            "__notional_currency_1__": pd.NA,
            "__swap_near_leg_date__": pd.NA,
            "__und_index_name_leg_2__": pd.NA,
            "__underlying_index_series__": pd.NA,
            "__und_index_term_value__": pd.NA,
            "__underlying_index_version__": pd.NA,
            "Symbol": [pd.NA, pd.NA, pd.NA, pd.NA, pd.NA],
            "__und_index_term_leg_2__": pd.NA,
            "__und_index_term_value_leg_2__": pd.NA,
            "__venue_financial_instrument_short_name__": pd.NA,
            "__instrument_classification__": pd.NA,
            "__df_columns_currency__": ["USD", "JPY", "AUD", "GBP", pd.NA],
            "__df_columns_notional_currency_2__": ["USD", "USD", "USD", "GBP", pd.NA],
            "__df_columns_notional_currency_2_not_equal_to_notional_currency_1__": pd.NA,
            "__df_columns_notional_currency_1_plus_2_conditional__": pd.NA,
            "__df_columns_asset_class__": ["Cash", "cfd", pd.NA, "Bond", "Future"],
            "__df_columns_expiry_date__": [
                "2020-01-08",
                "2021-07-30",
                "2020-12-01",
                "9999-12-31",
                pd.NA,
            ],
            "__df_columns_expiry_date_timestamp__": [
                "2020-01-08 00:00:00",
                "2021-07-30 00:00:00",
                "2020-12-01 00:00:00",
                "9999-12-31 00:00:00",
                pd.NA,
            ],
            "__df_columns_expiry_month__": [
                "2020-01",
                "2021-07",
                "2020-12",
                "9999-12",
                pd.NA,
            ],
            "__df_columns_expiry_month_timestamp__": [
                "2020-01 00:00:00",
                "2021-07 00:00:00",
                "2020-12 00:00:00",
                "9999-12 00:00:00",
                pd.NA,
            ],
            "__df_columns_days_to_expiry_term__": pd.NA,
            "__df_columns_swap_near_leg_date__": pd.NA,
            "__df_columns_option_strike_price__": pd.NA,
            "__df_columns_option_strike_price_div_100__": pd.NA,
            "__df_columns_option_strike_price_div_1000__": pd.NA,
            "__df_columns_option_strike_price_div_10000__": pd.NA,
            "__df_columns_option_type__": pd.NA,
            "__df_columns_underlying_index_term__": [
                "XAUUSD",
                "EURUSD",
                "XAUUSD",
                "EURUSD",
                pd.NA,
            ],
            "__df_columns_underlying_index_term_value__": pd.NA,
            "__df_columns_underlying_index_term_leg_2__": pd.NA,
            "__df_columns_underlying_index_term_value_leg_2__": pd.NA,
            "__df_columns_underlying_symbol__": [pd.NA, pd.NA, pd.NA, pd.NA, pd.NA],
            "__df_columns_underlying_index_name__": [
                "XAUUSD",
                "EURUSD",
                "XAUUSD",
                "EURUSD",
                pd.NA,
            ],
            "__df_columns_underlying_index_name_for_interest_rate_index__": [
                "XAUUSD",
                "EURUSD",
                "XAUUSD",
                "EURUSD",
                pd.NA,
            ],
            "__df_columns_underlying_index_name_for_iri_first_3_chars__": [
                "XAU",
                "EUR",
                "XAU",
                "EUR",
                pd.NA,
            ],
            "__df_columns_underlying_index_name_not_curr_1__": pd.NA,
            "__df_columns_underlying_index_name_leg_2__": pd.NA,
        }
    )
    df["ISIN"] = df["ISIN"].astype("string")
    return df


@pytest.fixture()
def input_date_1() -> str:
    return "20221114"


@pytest.fixture()
def input_convert_format_1() -> str:
    return "%Y/%m/%d"


@pytest.fixture()
def expected_date_1() -> datetime:
    return datetime(2022, 11, 14)


@pytest.fixture()
def input_date_2() -> str:
    return "9999/09/19"


@pytest.fixture()
def input_convert_format_2() -> str:
    return "%Y/%m/%d"


@pytest.fixture()
def expected_date_2() -> datetime:
    return datetime(9999, 9, 19)


#  ------------------------------------------ Interest rate swaps ------------------------------------------


@pytest.fixture()
def input_params_interest_rate() -> dict:
    """Params for interest rate forwards and swaps"""
    return dict(
        asset_class_attribute="__asset_class__",
        currency_attribute="__currency__",
        expiry_date_attribute="EXPIRYDATE/MATURITYDATE(YYYYMMDD)",
        instrument_classification_attribute="INSTRUMENTCLASSIFICATION",
        interest_rate_start_date_attribute="INTERESTRATESTARTDATE",
        interest_rate_start_date_format="%Y%m%d",
        notional_currency_1_attribute="NOTIONALCURRENCY1",
        notional_currency_2_attribute="__notional_currency_2__",
        option_strike_price_attribute="__option_strike_price__",
        option_type_attribute="OPTIONTYPE",
        swap_near_leg_date_attribute="TRADEDATE(YYYYMMDD)",
        underlying_index_name_attribute="UNDERLYINGINDEXNAME",
        underlying_index_name_leg_2_attribute="UNDERLYINGINDEXNAMELEG2",
        underlying_index_series_attribute="UNDERLYINGINDEXSERIES",
        underlying_index_term_attribute="UNDERLYINGINDEXTERM",
        underlying_index_term_leg_2_attribute="UNDERLYINGINDEXTERMLEG2",
        underlying_index_term_value_attribute="UNDERLYINGINDEXTERM",
        underlying_index_term_value_leg_2_attribute="UNDERLYINGINDEXTERMLEG2",
        underlying_index_version_attribute="UNDERLYINGINDEXVERSION",
        underlying_isin_attribute="UNDERLYINGINSTRUMENTISIN/S",
    )


@pytest.fixture()
def interest_rate_basis_swap_input_dataframe() -> pd.DataFrame:
    """Input data frame for testing identifiers for interest rate swaps basis swaps"""
    df = pd.DataFrame(
        {
            DFColumns.ASSET_CLASS: [
                "interest rate swaps - basis swaps",
                "interest rate swaps - basis swaps",
                "future",
            ],
            DFColumns.UNDERLYING_INDEX_NAME_FOR_INTEREST_RATE_INDEX: [
                "USDLIBORBBA",
                "EURLIBORBBA",
                "EURLIBORBBA",
            ],
            DFColumns.NOTIONAL_CURRENCY_1_PLUS_2_CONDITIONAL: ["USD|ZAR", "EUR", "EUR"],
            DFColumns.UNDERLYING_INDEX_TERM_VALUE: ["3", "3", "3"],
            DFColumns.UNDERLYING_INDEX_TERM: ["MNTH", "MNTH", "MNTH"],
            DFColumns.UNDERLYING_INDEX_NAME_LEG_2: [
                "ZARJIBARSAFEX",
                "USDLIBORBBA",
                "USDLIBORBBA",
            ],
            DFColumns.UNDERLYING_INDEX_TERM_VALUE_LEG_2: ["3", "3", "3"],
            DFColumns.UNDERLYING_INDEX_TERM_LEG_2: ["MNTH", "MNTH", "MNTH"],
            DFColumns.DAYS_TO_EXPIRY_TERM: ["1|YEAR", "18|MNTH", "18|MNTH"],
            DFColumns.EXPIRY_DATE: ["2021-12-21", "2021-12-21", "2021-12-21"],
        }
    )
    return df


@pytest.fixture()
def expected_result_interest_rate_basis_swap() -> pd.Series:
    result = pd.Series(
        [
            "XXXX|USDLIBORBBA|USD|ZAR|3|MNTH|ZARJIBARSAFEX|3|MNTH|1|YEAR|IRSWAP|FLOATINGFLOATING|BASISSWAP|2021-12-21",
            "XXXX|EURLIBORBBA|EUR|3|MNTH|USDLIBORBBA|3|MNTH|18|MNTH|IRSWAP|FLOATINGFLOATING|BASISSWAP|2021-12-21",
        ],
        index=[0, 1],
    )
    return result


@pytest.fixture()
def interest_rate_swap_ois_input_dataframe() -> pd.DataFrame:
    """Input data frame for testing identifiers for interest rate swap OIS"""
    df = pd.DataFrame(
        {
            DFColumns.ASSET_CLASS: [
                "interest rate swaps - ois",
                "future",
                "interest rate swaps - ois",
            ],
            DFColumns.NOTIONAL_CURRENCY_1_PLUS_2_CONDITIONAL: ["EURUSD", "EUR", "EUR"],
            DFColumns.UNDERLYING_INDEX_TERM_VALUE: ["1", "3", "2"],
            DFColumns.UNDERLYING_INDEX_TERM: ["DAYS", "DAYS", "DAYS"],
            DFColumns.UNDERLYING_INDEX_NAME_FOR_INTEREST_RATE_INDEX: [
                "EUREONIAOISCOMPOUND",
                "EUREONIAOISCOMPOUND",
                "EUREONIAOISCOMPOUND",
            ],
            DFColumns.DAYS_TO_EXPIRY_TERM: ["1|MNTH", "18|MNTH", "1|MNTH"],
            DFColumns.EXPIRY_DATE: ["2022-06-18", "2022-06-18", "2022-06-18"],
        }
    )
    return df


@pytest.fixture()
def expected_result_interest_rate_swap_ois() -> pd.Series:
    result = pd.Series(
        [
            "XXXX|EUREONIAOISCOMPOUND|EURUSD|1|DAYS|1|MNTH|IRSWAP|FIXEDFLOATING|OIS|2022-06-18",
            "XXXX|EUREONIAOISCOMPOUND|EUR|2|DAYS|1|MNTH|IRSWAP|FIXEDFLOATING|OIS|2022-06-18",
        ],
        index=[0, 2],
    )
    return result


@pytest.fixture()
def interest_rate_swap_fixed_fixed_dataframe() -> pd.DataFrame:
    """Input data frame for testing identifiers for interest rate swaps fixed fixed"""
    df = pd.DataFrame(
        {
            DFColumns.ASSET_CLASS: [
                "interest rate swaps - fixed fixed",
                "interest rate swaps - fixed fixed",
                "future",
            ],
            DFColumns.UNDERLYING_INDEX_NAME_FOR_INTEREST_RATE_INDEX: [
                "USDLIBORBBA",
                "USDLIBORBBA",
                "EURLIBORBBA",
            ],
            DFColumns.NOTIONAL_CURRENCY_1_PLUS_2_CONDITIONAL: ["CAD", "CAD|EUR", "EUR"],
            DFColumns.UNDERLYING_INDEX_TERM_VALUE: ["3", "3", "3"],
            DFColumns.UNDERLYING_INDEX_TERM: ["MNTH", "MNTH", "MNTH"],
            DFColumns.DAYS_TO_EXPIRY_TERM: ["1|YEAR", "12|MNTH", "1|YEAR"],
            DFColumns.EXPIRY_DATE: ["2021-06-01", "2021-06-14", "2021-12-21"],
        }
    )
    return df


@pytest.fixture()
def expected_result_interest_rate_swap_fixed_fixed() -> pd.Series:
    result = pd.Series(
        [
            "XXXX|CAD|3|MNTH|IRSWAP|FIXEDFIXED|2021-06-01",
            "XXXX|CAD|EUR|3|MNTH|IRSWAP|FIXEDFIXED|2021-06-14",
        ],
        index=[0, 1],
    )
    return result


@pytest.fixture()
def interest_rate_swap_fixed_floating_dataframe() -> pd.DataFrame:
    """Input data frame for testing identifiers for interest rate swaps fixed floating"""
    df = pd.DataFrame(
        {
            DFColumns.ASSET_CLASS: [
                "interest rate swaps - fixed float",
                "interest rate swaps - fixed float",
                "future",
            ],
            DFColumns.UNDERLYING_INDEX_NAME_FOR_INTEREST_RATE_INDEX: [
                "USDLIBORBBA",
                "USDLIBORBBA",
                "EURLIBORBBA",
            ],
            DFColumns.NOTIONAL_CURRENCY_1_PLUS_2_CONDITIONAL: ["AUD|USD", "CZK", "EUR"],
            DFColumns.UNDERLYING_INDEX_TERM_VALUE: ["3", "3", "3"],
            DFColumns.UNDERLYING_INDEX_TERM: ["MNTH", "MNTH", "MNTH"],
            DFColumns.DAYS_TO_EXPIRY_TERM: ["1|YEAR", "12|MNTH", "1|YEAR"],
            DFColumns.EXPIRY_DATE: ["2021-06-10", "2021-06-01", "2021-12-21"],
        }
    )
    return df


@pytest.fixture()
def expected_result_interest_rate_swap_fixed_floating() -> pd.Series:
    result = pd.Series(
        [
            "XXXX|USDLIBORBBA|AUD|USD|3|MNTH|1|YEAR|IRSWAP|FIXEDFLOATING|2021-06-10",
            "XXXX|USDLIBORBBA|CZK|3|MNTH|12|MNTH|IRSWAP|FIXEDFLOATING|2021-06-01",
        ],
        index=[0, 1],
    )
    return result


@pytest.fixture()
def interest_rate_swap_fixed_floating_zero_coupon_dataframe() -> pd.DataFrame:
    """Input data frame for testing identifiers for interest rate swaps fixed floating zero coupon"""
    df = pd.DataFrame(
        {
            DFColumns.ASSET_CLASS: [
                "interest rate swaps - fixed float - zero coupon",
                "interest rate swaps - fixed float - zero coupon",
                "future",
            ],
            DFColumns.CURRENCY: ["GBP", "GBP", "GBP"],
            DFColumns.UNDERLYING_INDEX_NAME_FOR_INTEREST_RATE_INDEX: [
                "AUDBBRBBSW",
                "USDLIBORBBA",
                "EURLIBORBBA",
            ],
            DFColumns.NOTIONAL_CURRENCY_1_PLUS_2_CONDITIONAL: ["AUD", "RUB|USD", "EUR"],
            DFColumns.UNDERLYING_INDEX_TERM_VALUE: ["3", "3", "3"],
            DFColumns.UNDERLYING_INDEX_TERM: ["MNTH", "MNTH", "MNTH"],
            DFColumns.DAYS_TO_EXPIRY_TERM: ["3|MNTH", "1|YEAR", "1|YEAR"],
            DFColumns.EXPIRY_DATE: ["2021-06-01", "2021-06-17", "2021-12-21"],
        }
    )
    return df


@pytest.fixture()
def expected_result_interest_rate_swap_fixed_floating_zero_coupon() -> pd.Series:
    result = pd.Series(
        [
            "XXXX|AUDBBRBBSW|AUD|3|MNTH|GBP|3|MNTH|IRSWAP|FIXEDFLOATING|ZEROCOUPON|2021-06-01",
            "XXXX|USDLIBORBBA|RUB|USD|3|MNTH|GBP|1|YEAR|IRSWAP|FIXEDFLOATING|ZEROCOUPON|2021-06-17",
        ],
        index=[0, 1],
    )
    return result


@pytest.fixture()
def interest_rate_swap_inflation_rate_index_dataframe() -> pd.DataFrame:
    """Input data frame for testing identifiers for interest rate swaps inflation_rate_index"""
    df = pd.DataFrame(
        {
            DFColumns.ASSET_CLASS: [
                "interest rate swaps - inflation rate index",
                "interest rate swaps - inflation rate index",
                "future",
            ],
            "NOTIONALCURRENCY1": ["EUR", "MXV", "MXV"],
            DFColumns.UNDERLYING_INDEX_NAME_FOR_INTEREST_RATE_INDEX: [
                "ESPCPI",
                "MXNUDI",
                "MXNUDI",
            ],
            DFColumns.UNDERLYING_INDEX_TERM_VALUE: ["3", "6", "3"],
            DFColumns.UNDERLYING_INDEX_TERM: ["MNTH", "MNTH", "MNTH"],
            DFColumns.DAYS_TO_EXPIRY_TERM: ["1|YEAR", "6|MNTH", "1|YEAR"],
            DFColumns.EXPIRY_DATE: ["2021-06-24", "2021-06-07", "2021-12-21"],
        }
    )
    return df


@pytest.fixture()
def expected_result_interest_rate_swap_inflation_rate_index() -> pd.Series:
    result = pd.Series(
        [
            "XXXX|ESPCPI|EUR|3|MNTH|1|YEAR|IRSWAP|FIXEDFLOATING|2021-06-24",
            "XXXX|MXNUDI|MXV|6|MNTH|6|MNTH|IRSWAP|FIXEDFLOATING|2021-06-07",
        ],
        index=[0, 1],
    )
    return result


@pytest.fixture()
def interest_rate_forwards_dataframe() -> pd.DataFrame:
    """Input data frame for testing identifiers for interest rate forwards"""
    df = pd.DataFrame(
        {
            DFColumns.ASSET_CLASS: [
                "interest rate forward",
                "interest rate forward",
                "future",
            ],
            DFColumns.UNDERLYING_INDEX_NAME_PLUS_NOT_CURR_1: [
                "ZARJIBARSAFEX",
                "EURIBORREUTERS",
                "EURIBORREUTERS",
            ],
            DFColumns.UNDERLYING_INDEX_TERM_VALUE: ["3", "1", "3"],
            DFColumns.UNDERLYING_INDEX_TERM: ["MNTH", "MNTH", "MNTH"],
            DFColumns.DAYS_TO_EXPIRY_TERM: ["3|MNTH", "1|YEAR", "1|YEAR"],
            DFColumns.EXPIRY_DATE: ["2021-05-30", "2021-06-03", "2021-12-21"],
        }
    )
    return df


@pytest.fixture()
def expected_result_interest_rate_forwards() -> pd.Series:
    result = pd.Series(
        [
            "XXXX|ZARJIBARSAFEX|3|MNTH|3|MNTH|IRFWRD|2021-05-30",
            "XXXX|EURIBORREUTERS|1|YEAR|1|MNTH|IRFWRD|2021-06-03",
        ],
        index=[0, 1],
    )
    return result


@pytest.fixture()
def interest_rate_forwards_process_data_source_frame():
    df = pd.DataFrame(
        [
            # Record 1
            {
                "EXPIRYDATE/MATURITYDATE(YYYYMMDD)": "20220530",
                "INSTRUMENTCLASSIFICATION": pd.NA,
                "INTERESTRATESTARTDATE": "20220228",
                "NOTIONALCURRENCY1": "ZAR",
                "UNDERLYINGINDEXNAME": "JIBAR-SAFEX",
                "UNDERLYINGINDEXTERM": "3MONTH",
                "OPTIONTYPE": pd.NA,
                "UNDERLYINGINDEXNAMELEG2": pd.NA,
                "UNDERLYINGINDEXTERMLEG2": pd.NA,
                "UNDERLYINGINDEXSERIES": pd.NA,
                "UNDERLYINGINDEXVERSION": pd.NA,
                "UNDERLYINGINSTRUMENTISIN/S": pd.NA,
                "TRADEDATE(YYYYMMDD)": pd.NA,
                "__instrument_venue__": "XLON",
                "__instrument_symbol": pd.NA,
                "__notional_currency_2__": pd.NA,
                "__currency__": "GBP",
                "__option_strike_price__": pd.NA,
                "__isin__": pd.NA,
                "__asset_class__": "interest rate forward",
                "__bbg_figi_id__": pd.NA,
                "__eurex_id__": pd.NA,
                "__venue__": pd.NA,
                "__underlying_symbol__": pd.NA,
            },
            # Record 2: Interest rate forward, but with asset class = null, identified by the INSTRUMENTCLASSIFICATION
            {
                "EXPIRYDATE/MATURITYDATE(YYYYMMDD)": "20210603",
                "INSTRUMENTCLASSIFICATION": "JRIXXX",
                "INTERESTRATESTARTDATE": "20210503",
                "NOTIONALCURRENCY1": "EUR",
                "UNDERLYINGINDEXNAME": "EURIBOR-Reuters",
                "UNDERLYINGINDEXTERM": "1MONTH",
                "OPTIONTYPE": pd.NA,
                "UNDERLYINGINDEXNAMELEG2": pd.NA,
                "UNDERLYINGINDEXTERMLEG2": pd.NA,
                "UNDERLYINGINDEXSERIES": pd.NA,
                "UNDERLYINGINDEXVERSION": pd.NA,
                "UNDERLYINGINSTRUMENTISIN/S": pd.NA,
                "TRADEDATE(YYYYMMDD)": pd.NA,
                "__instrument_venue__": "XLON",
                "__instrument_symbol": pd.NA,
                "__notional_currency_2__": pd.NA,
                "__currency__": "GBP",
                "__option_strike_price__": pd.NA,
                "__isin__": pd.NA,
                "__asset_class__": pd.NA,
                "__bbg_figi_id__": pd.NA,
                "__eurex_id__": pd.NA,
                "__venue__": pd.NA,
                "__underlying_symbol__": pd.NA,
            },
        ]
    )
    return df


@pytest.fixture()
def expected_result_interest_rate_forwards_process_data():
    df = pd.DataFrame(
        [
            # Record 1
            {
                "EXPIRYDATE/MATURITYDATE(YYYYMMDD)": "20220530",
                "INSTRUMENTCLASSIFICATION": pd.NA,
                "INTERESTRATESTARTDATE": "20220228",
                "NOTIONALCURRENCY1": "ZAR",
                "UNDERLYINGINDEXNAME": "JIBAR-SAFEX",
                "UNDERLYINGINDEXTERM": "3MONTH",
                "OPTIONTYPE": pd.NA,
                "UNDERLYINGINDEXNAMELEG2": pd.NA,
                "UNDERLYINGINDEXTERMLEG2": pd.NA,
                "UNDERLYINGINDEXSERIES": pd.NA,
                "UNDERLYINGINDEXVERSION": pd.NA,
                "UNDERLYINGINSTRUMENTISIN/S": pd.NA,
                "TRADEDATE(YYYYMMDD)": pd.NA,
                "__instrument_venue__": "XLON",
                "__instrument_symbol": pd.NA,
                "__notional_currency_2__": pd.NA,
                "__currency__": "GBP",
                "__option_strike_price__": pd.NA,
                "__isin__": pd.NA,
                "__asset_class__": "interest rate forward",
                "__bbg_figi_id__": pd.NA,
                "__eurex_id__": pd.NA,
                "__venue__": pd.NA,
                "__underlying_symbol__": pd.NA,
                "__venue_financial_instrument_short_name__": pd.NA,
                DFColumns.CURRENCY: "GBP",
                DFColumns.NOTIONAL_CURRENCY_2: pd.NA,
                DFColumns.NOTIONAL_CURRENCY_2_NOT_EQUAL_NOTIONAL_CURRENCY_1: pd.NA,
                DFColumns.NOTIONAL_CURRENCY_1_PLUS_2_CONDITIONAL: "ZAR",
                DFColumns.ASSET_CLASS: "interest rate forward",
                DFColumns.EXPIRY_DATE: "2022-05-30",
                DFColumns.EXPIRY_DATE_TIMESTAMP: "2022-05-30 00:00:00",
                DFColumns.EXPIRY_MONTH: "2022-05",
                DFColumns.EXPIRY_MONTH_TIMESTAMP: "2022-05 00:00:00",
                DFColumns.DAYS_TO_EXPIRY_TERM: "3|MNTH",
                DFColumns.SWAP_NEAR_LEG_DATE: pd.NA,
                DFColumns.OPTION_STRIKE_PRICE: pd.NA,
                DFColumns.OPTION_STRIKE_PRICE_DIV_100: pd.NA,
                DFColumns.OPTION_STRIKE_PRICE_DIV_1000: pd.NA,
                DFColumns.OPTION_STRIKE_PRICE_DIV_10000: pd.NA,
                DFColumns.OPTION_TYPE: pd.NA,
                DFColumns.UNDERLYING_INDEX_TERM: "MNTH",
                DFColumns.UNDERLYING_INDEX_TERM_VALUE: "3",
                DFColumns.UNDERLYING_INDEX_TERM_LEG_2: pd.NA,
                DFColumns.UNDERLYING_INDEX_TERM_VALUE_LEG_2: pd.NA,
                DFColumns.UNDERLYING_SYMBOL: pd.NA,
                DFColumns.UNDERLYING_INDEX_NAME: "JIBAR-SAFEX",
                DFColumns.UNDERLYING_INDEX_NAME_FOR_INTEREST_RATE_INDEX: "JIBARSAFEX",
                DFColumns.UNDERLYING_INDEX_NAME_FOR_IRI_FIRST_3_CHARS: "JIB",
                DFColumns.UNDERLYING_INDEX_NAME_PLUS_NOT_CURR_1: "ZARJIBARSAFEX",
                DFColumns.UNDERLYING_INDEX_NAME_LEG_2: pd.NA,
            },
            # Record 2: Interest rate forward, but with asset class = null, identified by the INSTRUMENTCLASSIFICATION
            {
                "EXPIRYDATE/MATURITYDATE(YYYYMMDD)": "20210603",
                "INSTRUMENTCLASSIFICATION": "JRIXXX",
                "INTERESTRATESTARTDATE": "20210503",
                "NOTIONALCURRENCY1": "EUR",
                "UNDERLYINGINDEXNAME": "EURIBOR-Reuters",
                "UNDERLYINGINDEXTERM": "1MONTH",
                "OPTIONTYPE": pd.NA,
                "UNDERLYINGINDEXNAMELEG2": pd.NA,
                "UNDERLYINGINDEXTERMLEG2": pd.NA,
                "UNDERLYINGINDEXSERIES": pd.NA,
                "UNDERLYINGINDEXVERSION": pd.NA,
                "UNDERLYINGINSTRUMENTISIN/S": pd.NA,
                "TRADEDATE(YYYYMMDD)": pd.NA,
                "__instrument_venue__": "XLON",
                "__instrument_symbol": pd.NA,
                "__notional_currency_2__": pd.NA,
                "__currency__": "GBP",
                "__option_strike_price__": pd.NA,
                "__isin__": pd.NA,
                "__asset_class__": pd.NA,
                "__bbg_figi_id__": pd.NA,
                "__eurex_id__": pd.NA,
                "__venue__": pd.NA,
                "__underlying_symbol__": pd.NA,
                "__venue_financial_instrument_short_name__": pd.NA,
                DFColumns.CURRENCY: "GBP",
                DFColumns.NOTIONAL_CURRENCY_2: pd.NA,
                DFColumns.NOTIONAL_CURRENCY_2_NOT_EQUAL_NOTIONAL_CURRENCY_1: pd.NA,
                DFColumns.NOTIONAL_CURRENCY_1_PLUS_2_CONDITIONAL: "EUR",
                DFColumns.ASSET_CLASS: "interest rate forward",
                DFColumns.EXPIRY_DATE: "2021-06-03",
                DFColumns.EXPIRY_DATE_TIMESTAMP: "2021-06-03 00:00:00",
                DFColumns.EXPIRY_MONTH: "2021-06",
                DFColumns.EXPIRY_MONTH_TIMESTAMP: "2021-06 00:00:00",
                DFColumns.DAYS_TO_EXPIRY_TERM: "1|MNTH",
                DFColumns.SWAP_NEAR_LEG_DATE: pd.NA,
                DFColumns.OPTION_STRIKE_PRICE: pd.NA,
                DFColumns.OPTION_STRIKE_PRICE_DIV_100: pd.NA,
                DFColumns.OPTION_STRIKE_PRICE_DIV_1000: pd.NA,
                DFColumns.OPTION_STRIKE_PRICE_DIV_10000: pd.NA,
                DFColumns.OPTION_TYPE: pd.NA,
                DFColumns.UNDERLYING_INDEX_TERM: "MNTH",
                DFColumns.UNDERLYING_INDEX_TERM_VALUE: "1",
                DFColumns.UNDERLYING_INDEX_TERM_LEG_2: pd.NA,
                DFColumns.UNDERLYING_INDEX_TERM_VALUE_LEG_2: pd.NA,
                DFColumns.UNDERLYING_SYMBOL: pd.NA,
                DFColumns.UNDERLYING_INDEX_NAME: "EURIBOR-REUTERS",
                DFColumns.UNDERLYING_INDEX_NAME_FOR_INTEREST_RATE_INDEX: "EURIBORREUTERS",
                DFColumns.UNDERLYING_INDEX_NAME_FOR_IRI_FIRST_3_CHARS: "EUR",
                DFColumns.UNDERLYING_INDEX_NAME_PLUS_NOT_CURR_1: "EURIBORREUTERS",
                DFColumns.UNDERLYING_INDEX_NAME_LEG_2: pd.NA,
            },
        ]
    )
    return df


@pytest.fixture()
def interest_rate_swaps_process_data_source_frame():
    df = pd.DataFrame(
        [
            # Record 1
            {
                "EXPIRYDATE/MATURITYDATE(YYYYMMDD)": "20211221",
                "INSTRUMENTCLASSIFICATION": "SRACCP",
                "INTERESTRATESTARTDATE": "20201221",
                "UNDERLYINGINDEXNAME": "USD-LIBOR-BBA",
                "UNDERLYINGINDEXNAMELEG2": "ZAR-JIBAR-SAFEX",
                "UNDERLYINGINDEXTERM": "3MONTH",
                "UNDERLYINGINDEXTERMLEG2": "3MONTH",
                "NOTIONALCURRENCY1": "USD",
                "TRADEDATE(YYYYMMDD)": "20210408",
                "OPTIONTYPE": pd.NA,
                "UNDERLYINGINDEXSERIES": pd.NA,
                "UNDERLYINGINDEXVERSION": pd.NA,
                "UNDERLYINGINSTRUMENTISIN/S": pd.NA,
                "__currency__": "GBP",
                "__option_strike_price__": pd.NA,
                "__isin__": pd.NA,
                "__instrument_venue__": "XLON",
                "__instrument_symbol": pd.NA,
                "__notional_currency_2__": "ZAR",
                "__asset_class__": "interest rate swaps - basis swaps",
                "__bbg_figi_id__": pd.NA,
                "__eurex_id__": pd.NA,
                "__underlying_symbol__": pd.NA,
                "__venue__": pd.NA,
            },
            {
                "EXPIRYDATE/MATURITYDATE(YYYYMMDD)": "20220618",
                "INSTRUMENTCLASSIFICATION": "SRHCSP",
                "INTERESTRATESTARTDATE": "20220518",
                "UNDERLYINGINDEXNAME": "EUR-EONIA-OIS-COMPOUND",
                "UNDERLYINGINDEXNAMELEG2": pd.NA,
                "UNDERLYINGINDEXTERM": "1DAY",
                "UNDERLYINGINDEXTERMLEG2": pd.NA,
                "NOTIONALCURRENCY1": "EUR",
                "TRADEDATE(YYYYMMDD)": "20210408",
                "OPTIONTYPE": pd.NA,
                "UNDERLYINGINDEXSERIES": pd.NA,
                "UNDERLYINGINDEXVERSION": pd.NA,
                "UNDERLYINGINSTRUMENTISIN/S": pd.NA,
                "__currency__": "GBP",
                "__option_strike_price__": pd.NA,
                "__isin__": pd.NA,
                "__instrument_venue__": "XLON",
                "__instrument_symbol": pd.NA,
                "__notional_currency_2__": pd.NA,
                "__asset_class__": "interest rate swaps - ois",
                "__bbg_figi_id__": pd.NA,
                "__eurex_id__": pd.NA,
                "__underlying_symbol__": pd.NA,
                "__venue__": pd.NA,
            },
        ]
    )
    return df


@pytest.fixture()
def expected_result_interest_rate_swaps_process_data():
    df = pd.DataFrame(
        [
            # Record 1
            {
                "EXPIRYDATE/MATURITYDATE(YYYYMMDD)": "20211221",
                "INSTRUMENTCLASSIFICATION": "SRACCP",
                "INTERESTRATESTARTDATE": "20201221",
                "UNDERLYINGINDEXNAME": "USD-LIBOR-BBA",
                "UNDERLYINGINDEXNAMELEG2": "ZAR-JIBAR-SAFEX",
                "UNDERLYINGINDEXTERM": "3MONTH",
                "UNDERLYINGINDEXTERMLEG2": "3MONTH",
                "NOTIONALCURRENCY1": "USD",
                "TRADEDATE(YYYYMMDD)": "20210408",
                "OPTIONTYPE": pd.NA,
                "UNDERLYINGINDEXSERIES": pd.NA,
                "UNDERLYINGINDEXVERSION": pd.NA,
                "UNDERLYINGINSTRUMENTISIN/S": pd.NA,
                "__currency__": "GBP",
                "__option_strike_price__": pd.NA,
                "__isin__": pd.NA,
                "__instrument_venue__": "XLON",
                "__instrument_symbol": pd.NA,
                "__notional_currency_2__": "ZAR",
                "__asset_class__": "interest rate swaps - basis swaps",
                "__bbg_figi_id__": pd.NA,
                "__eurex_id__": pd.NA,
                "__underlying_symbol__": pd.NA,
                "__venue__": pd.NA,
                "__venue_financial_instrument_short_name__": pd.NA,
                DFColumns.CURRENCY: "GBP",
                DFColumns.NOTIONAL_CURRENCY_2: "ZAR",
                DFColumns.NOTIONAL_CURRENCY_2_NOT_EQUAL_NOTIONAL_CURRENCY_1: "ZAR",
                DFColumns.NOTIONAL_CURRENCY_1_PLUS_2_CONDITIONAL: "USD|ZAR",
                DFColumns.ASSET_CLASS: "interest rate swaps - basis swaps",
                DFColumns.EXPIRY_DATE: "2021-12-21",
                DFColumns.EXPIRY_DATE_TIMESTAMP: "2021-12-21 00:00:00",
                DFColumns.EXPIRY_MONTH: "2021-12",
                DFColumns.EXPIRY_MONTH_TIMESTAMP: "2021-12 00:00:00",
                DFColumns.DAYS_TO_EXPIRY_TERM: "1|YEAR",
                DFColumns.SWAP_NEAR_LEG_DATE: "2021-04-08",
                DFColumns.OPTION_STRIKE_PRICE: pd.NA,
                DFColumns.OPTION_STRIKE_PRICE_DIV_100: pd.NA,
                DFColumns.OPTION_STRIKE_PRICE_DIV_1000: pd.NA,
                DFColumns.OPTION_STRIKE_PRICE_DIV_10000: pd.NA,
                DFColumns.OPTION_TYPE: pd.NA,
                DFColumns.UNDERLYING_INDEX_TERM: "MNTH",
                DFColumns.UNDERLYING_INDEX_TERM_VALUE: "3",
                DFColumns.UNDERLYING_INDEX_TERM_LEG_2: "MNTH",
                DFColumns.UNDERLYING_INDEX_TERM_VALUE_LEG_2: "3",
                DFColumns.UNDERLYING_SYMBOL: pd.NA,
                DFColumns.UNDERLYING_INDEX_NAME: "USD-LIBOR-BBA",
                DFColumns.UNDERLYING_INDEX_NAME_FOR_INTEREST_RATE_INDEX: "USDLIBORBBA",
                DFColumns.UNDERLYING_INDEX_NAME_FOR_IRI_FIRST_3_CHARS: "USD",
                DFColumns.UNDERLYING_INDEX_NAME_PLUS_NOT_CURR_1: "USDLIBORBBA",
                DFColumns.UNDERLYING_INDEX_NAME_LEG_2: "ZARJIBARSAFEX",
            },
            # Record 2: Interest rate forward, but with asset class = null, identified by the INSTRUMENTCLASSIFICATION
            {
                "EXPIRYDATE/MATURITYDATE(YYYYMMDD)": "20220618",
                "INSTRUMENTCLASSIFICATION": "SRHCSP",
                "INTERESTRATESTARTDATE": "20220518",
                "UNDERLYINGINDEXNAME": "EUR-EONIA-OIS-COMPOUND",
                "UNDERLYINGINDEXNAMELEG2": pd.NA,
                "UNDERLYINGINDEXTERM": "1DAY",
                "UNDERLYINGINDEXTERMLEG2": pd.NA,
                "NOTIONALCURRENCY1": "EUR",
                "TRADEDATE(YYYYMMDD)": "20210408",
                "OPTIONTYPE": pd.NA,
                "UNDERLYINGINDEXSERIES": pd.NA,
                "UNDERLYINGINDEXVERSION": pd.NA,
                "UNDERLYINGINSTRUMENTISIN/S": pd.NA,
                "__currency__": "GBP",
                "__option_strike_price__": pd.NA,
                "__isin__": pd.NA,
                "__instrument_venue__": "XLON",
                "__instrument_symbol": pd.NA,
                "__notional_currency_2__": pd.NA,
                "__asset_class__": "interest rate swaps - ois",
                "__bbg_figi_id__": pd.NA,
                "__eurex_id__": pd.NA,
                "__underlying_symbol__": pd.NA,
                "__venue__": pd.NA,
                "__venue_financial_instrument_short_name__": pd.NA,
                DFColumns.CURRENCY: "GBP",
                DFColumns.NOTIONAL_CURRENCY_2: pd.NA,
                DFColumns.NOTIONAL_CURRENCY_2_NOT_EQUAL_NOTIONAL_CURRENCY_1: pd.NA,
                DFColumns.NOTIONAL_CURRENCY_1_PLUS_2_CONDITIONAL: "EUR",
                DFColumns.ASSET_CLASS: "interest rate swaps - ois",
                DFColumns.EXPIRY_DATE: "2022-06-18",
                DFColumns.EXPIRY_DATE_TIMESTAMP: "2022-06-18 00:00:00",
                DFColumns.EXPIRY_MONTH: "2022-06",
                DFColumns.EXPIRY_MONTH_TIMESTAMP: "2022-06 00:00:00",
                DFColumns.DAYS_TO_EXPIRY_TERM: "1|MNTH",
                DFColumns.SWAP_NEAR_LEG_DATE: "2021-04-08",
                DFColumns.OPTION_STRIKE_PRICE: pd.NA,
                DFColumns.OPTION_STRIKE_PRICE_DIV_100: pd.NA,
                DFColumns.OPTION_STRIKE_PRICE_DIV_1000: pd.NA,
                DFColumns.OPTION_STRIKE_PRICE_DIV_10000: pd.NA,
                DFColumns.OPTION_TYPE: pd.NA,
                DFColumns.UNDERLYING_INDEX_TERM: "DAYS",
                DFColumns.UNDERLYING_INDEX_TERM_VALUE: "1",
                DFColumns.UNDERLYING_INDEX_TERM_LEG_2: pd.NA,
                DFColumns.UNDERLYING_INDEX_TERM_VALUE_LEG_2: pd.NA,
                DFColumns.UNDERLYING_SYMBOL: pd.NA,
                DFColumns.UNDERLYING_INDEX_NAME: "EUR-EONIA-OIS-COMPOUND",
                DFColumns.UNDERLYING_INDEX_NAME_FOR_INTEREST_RATE_INDEX: "EUREONIAOISCOMPOUND",
                DFColumns.UNDERLYING_INDEX_NAME_FOR_IRI_FIRST_3_CHARS: "EUR",
                DFColumns.UNDERLYING_INDEX_NAME_PLUS_NOT_CURR_1: "EUREONIAOISCOMPOUND",
                DFColumns.UNDERLYING_INDEX_NAME_LEG_2: pd.NA,
            },
        ]
    )
    return df


@pytest.fixture()
def format_expiry_date_input_df() -> pd.DataFrame:
    return pd.DataFrame({"EXPIRYDATE": ["20000000", "20230101", pd.NA]})


@pytest.fixture()
def format_expiry_date_expected_df() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "EXPIRYDATE": ["20000000", "20230101", pd.NA],
            "__df_columns_expiry_date__": [pd.NA, "2023-01-01", pd.NA],
            "__df_columns_expiry_date_timestamp__": [
                pd.NA,
                "2023-01-01 00:00:00",
                pd.NA,
            ],
            "__df_columns_expiry_month__": [pd.NA, "2023-01", pd.NA],
            "__df_columns_expiry_month_timestamp__": [pd.NA, "2023-01 00:00:00", pd.NA],
        }
    )
