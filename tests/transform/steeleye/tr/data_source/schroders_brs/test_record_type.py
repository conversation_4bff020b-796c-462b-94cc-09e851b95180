import pandas as pd
import pytest

from swarm_tasks.transform.steeleye.tr.data_source.schroders_brs import record_type


@pytest.fixture()
def source_dataframe() -> pd.DataFrame:
    """Represents a source dataframe parsed from an input csv file."""
    df = pd.DataFrame(
        {
            "Buyer Id": ["intc", "INTC", "intc", pd.NA, "intc", pd.NA],
            "Buyer Decision Maker Id": [
                pd.NA,
                pd.NA,
                "liujwueyrtghuiopslke",
                pd.NA,
                pd.NA,
                pd.NA,
            ],
            "Record Type": [pd.NA, pd.NA, pd.NA, pd.NA, "F", "A"],
            "Seller Id": [pd.NA, pd.NA, "intc", "intc", "intc", pd.NA],
            "Seller Decision Maker Id": [
                "liujwueyrtghuiopslke",
                "liujwu<PERSON>rtgh<PERSON><PERSON>lk<PERSON>",
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
            ],
        }
    )
    return df


class TestSchrodersRecordType:
    def test_empty_source_frame(self):
        source_frame = pd.DataFrame(
            {
                "Buyer Id": [pd.NA],
                "Record Type": [pd.NA],
                "Buyer Decision Maker Id": [pd.NA],
                "Seller Id": [pd.NA],
                "Seller Decision Maker Id": [pd.NA],
            }
        )

        result = record_type.RecordType._get_record_type(df=source_frame)

        expected_series = pd.Series(pd.NA, index=source_frame.index)

        assert result.equals(expected_series)

    def test_record_type_correct_target_creation(self, source_dataframe):
        outcome_df = record_type.RecordType(name="test-record-type").execute(
            source_frame=source_dataframe
        )

        assert outcome_df.columns.tolist() == ["transactionDetails.recordType"]

    def test_correct_record_types(self, source_dataframe):

        outcome_series = record_type.RecordType._get_record_type(df=source_dataframe)
        expected_df = pd.Series(
            ["Client Side", "Client Side", "Client Side", pd.NA, "Market Side", pd.NA]
        )
        assert outcome_series.equals(expected_df)
