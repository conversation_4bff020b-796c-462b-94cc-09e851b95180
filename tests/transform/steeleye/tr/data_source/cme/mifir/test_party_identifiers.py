import pandas as pd
import pytest

from swarm_tasks.transform.steeleye.tr.data_source.cme.mifir import party_identifiers
from swarm_tasks.transform.steeleye.tr.data_source.cme.mifir.static import (
    CmeMifirColumns,
)


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    df = pd.DataFrame()
    return df


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            CmeMifirColumns.BUYER_ID: ["test1"],
            CmeMifirColumns.BUYER_DECISION_MAKER_ID: ["test1"],
            CmeMifirColumns.EXECUTING_ENTITY_ID: ["test1"],
            CmeMifirColumns.SELLER_ID: ["test1"],
            CmeMifirColumns.SELLER_DECISION_MAKER_ID: ["test1"],
            CmeMifirColumns.FIRM_EXEC: ["test1"],
            CmeMifirColumns.INV_DEC_ID: ["test1"],
            CmeMifirColumns.EXECUTION_WITHIN_FIRM_TYPE: ["algo"],
            CmeMifirColumns.INVESTMENT_DECISION_WITHIN_FIRM_TYPE: ["algo"],
            CmeMifirColumns.BUYER_IDENTIFICATION_CODE_TYPE: ["algo"],
            CmeMifirColumns.BUYER_DECISION_MAKER_CODE_TYPE: ["algo"],
            CmeMifirColumns.SELLER_IDENTIFICATION_CODE_TYPE: ["algo"],
            CmeMifirColumns.SELLER_DECISION_MAKER_CODE_TYPE: ["algo"],
        }
    )
    return df


@pytest.fixture()
def missing_some_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            CmeMifirColumns.BUYER_ID: ["test1"],
            CmeMifirColumns.BUYER_DECISION_MAKER_ID: ["test1"],
            CmeMifirColumns.SELLER_DECISION_MAKER_ID: ["test1"],
            CmeMifirColumns.FIRM_EXEC: ["test1"],
            CmeMifirColumns.INV_DEC_ID: ["test1"],
            CmeMifirColumns.EXECUTION_WITHIN_FIRM_TYPE: ["algo"],
            CmeMifirColumns.INVESTMENT_DECISION_WITHIN_FIRM_TYPE: ["algo"],
            CmeMifirColumns.BUYER_IDENTIFICATION_CODE_TYPE: ["algo"],
            CmeMifirColumns.BUYER_DECISION_MAKER_CODE_TYPE: ["algo"],
            CmeMifirColumns.SELLER_IDENTIFICATION_CODE_TYPE: ["algo"],
            CmeMifirColumns.SELLER_DECISION_MAKER_CODE_TYPE: ["algo"],
        }
    )
    return df


class TestParties(object):
    """
    Test cases for "TestParties" class
    """

    def test_empty_input_df_without_source_columns(self, empty_source_df):
        task = party_identifiers.PartyIdentifiers(name="test-parties")
        result = task.execute(empty_source_df)
        assert result.empty

    def test_all_col_in_source_df(self, all_col_in_source_df: pd.DataFrame):
        task = party_identifiers.PartyIdentifiers(name="test-parties")
        expected_result = pd.DataFrame(
            {
                "marketIdentifiers.parties": [
                    "[{'labelId': 'algo:test1', 'path': 'parties.buyer', "
                    "'type': <IdentifierType.ARRAY>}, {'labelId': 'algo:test1', 'path': "
                    "'parties.buyerDecisionMaker', 'type': <IdentifierType.ARRAY>}, "
                    "{'labelId': 'lei:test1', 'path': 'parties.executingEntity', "
                    "'type': <IdentifierType.OBJECT>}, {'labelId': 'algo:test1', "
                    "'path': 'parties.executionWithinFirm', "
                    "'type': <IdentifierType.OBJECT>}, {'labelId': 'algo:test1', "
                    "'path': 'parties.investmentDecisionWithinFirm', "
                    "'type': <IdentifierType.OBJECT>}, "
                    "{'labelId': 'algo:test1', 'path': 'parties.seller', "
                    "'type': <IdentifierType.ARRAY>}, "
                    "{'labelId': 'algo:test1', 'path': 'parties.sellerDecisionMaker', "
                    "'type': <IdentifierType.ARRAY>}]"
                ]
            }
        )

        result = task.execute(all_col_in_source_df)
        assert result.astype("str").equals(expected_result.astype("str"))

    def test_missing_some_col_in_source_df(
        self, missing_some_col_in_source_df: pd.DataFrame
    ):
        task = party_identifiers.PartyIdentifiers(name="test-parties")
        expected_result = pd.DataFrame(
            {
                "marketIdentifiers.parties": [
                    "[{'labelId': 'algo:test1', 'path': 'parties.buyer', "
                    "'type': <IdentifierType.ARRAY>}, {'labelId': 'algo:test1', 'path': "
                    "'parties.buyerDecisionMaker', 'type': <IdentifierType.ARRAY>}, "
                    "{'labelId': 'algo:test1', "
                    "'path': 'parties.executionWithinFirm', "
                    "'type': <IdentifierType.OBJECT>}, {'labelId': 'algo:test1', "
                    "'path': 'parties.investmentDecisionWithinFirm', "
                    "'type': <IdentifierType.OBJECT>}, "
                    "{'labelId': 'algo:test1', 'path': 'parties.sellerDecisionMaker', "
                    "'type': <IdentifierType.ARRAY>}]"
                ]
            }
        )

        result = task.execute(missing_some_col_in_source_df)
        assert result.astype("str").equals(expected_result.astype("str"))
