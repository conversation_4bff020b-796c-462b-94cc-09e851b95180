import pandas as pd
import pytest

from swarm_tasks.transform.steeleye.tr.data_source.cme.mifir import venue_override as vo
from swarm_tasks.transform.steeleye.tr.data_source.cme.mifir.venue_override import (
    INSTRUMENT_PATH,
)
from swarm_tasks.transform.steeleye.tr.data_source.cme.mifir.venue_override import (
    TranactionFields,
)
from swarm_tasks.transform.steeleye.tr.data_source.cme.mifir.venue_override import (
    WORKFLOW_ELIGIBILITY_PATH,
)


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    result = pd.DataFrame()

    return result


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            INSTRUMENT_PATH: [
                pd.NA,
                {
                    "derivative.deliveryType": "CASH",
                    "instrumentClassification": "ESQYC",
                    "notionalCurrency1": "USD",
                    "ext.mifirEligible": True,
                },
                {
                    "derivative.deliveryType": "CASH",
                    "instrumentClassification": "ESQYC",
                    "notionalCurrency1": "USD",
                    "ext.mifirEligible": True,
                },
            ],
            WORKFLOW_ELIGIBILITY_PATH: [
                pd.NA,
                {
                    "eligible": True,
                    "onFirds": False,
                    "totv": False,
                    "utotv": True,
                    "executionVenue": "XOFF",
                },
                {
                    "eligible": True,
                    "onFirds": False,
                    "totv": True,
                    "utotv": True,
                    "executionVenue": "XOFF",
                },
            ],
            TranactionFields.TRANSACTION_VENUE: [pd.NA, "XOFF", "XOFF"],
            TranactionFields.TRANSACTION_ULTIMATE_VENUE: ["XOFF", "XOFF", "XOFF"],
        }
    )
    return df


@pytest.fixture()
def missing_some_col_in_source_df() -> pd.DataFrame:
    """Misses required column INSTRUMENT_PATH"""
    df = pd.DataFrame(
        {
            WORKFLOW_ELIGIBILITY_PATH: [
                pd.NA,
                {
                    "eligible": True,
                    "onFirds": False,
                    "totv": False,
                    "utotv": True,
                    "executionVenue": "XOFF",
                },
                {
                    "eligible": True,
                    "onFirds": False,
                    "totv": True,
                    "utotv": True,
                    "executionVenue": "XOFF",
                },
            ],
            TranactionFields.TRANSACTION_VENUE: [pd.NA, "XOFF", "XOFF"],
            TranactionFields.TRANSACTION_ULTIMATE_VENUE: ["XOFF", pd.NA, "XOFF"],
        }
    )
    return df


class TestVenueOverride(object):
    """
    Test cases for "VenueOverride" class
    """

    def test_empty_input_df_without_source_columns(self, empty_source_df: pd.DataFrame):
        params = vo.Params(override_venue=True)
        task = vo.VenueOverride(name="test-venue-override", params=params)
        result = task.execute(empty_source_df, params)
        assert result.empty

    def test_all_col_in_source_df(self, all_col_in_source_df: pd.DataFrame):
        expected_result = pd.DataFrame(
            {
                INSTRUMENT_PATH: [
                    pd.NA,
                    {
                        "derivative.deliveryType": "CASH",
                        "instrumentClassification": "ESQYC",
                        "notionalCurrency1": "USD",
                        "ext.mifirEligible": True,
                    },
                    {
                        "derivative.deliveryType": "CASH",
                        "instrumentClassification": "ESQYC",
                        "notionalCurrency1": "USD",
                        "ext.mifirEligible": True,
                    },
                ],
                WORKFLOW_ELIGIBILITY_PATH: [
                    pd.NA,
                    {
                        "eligible": True,
                        "onFirds": False,
                        "totv": False,
                        "utotv": True,
                        "executionVenue": "XXXX",
                    },
                    {
                        "eligible": True,
                        "onFirds": False,
                        "totv": True,
                        "utotv": True,
                        "executionVenue": "XOFF",
                    },
                ],
                TranactionFields.TRANSACTION_VENUE: [pd.NA, "XXXX", "XOFF"],
                TranactionFields.TRANSACTION_ULTIMATE_VENUE: ["XOFF", "XXXX", "XOFF"],
            }
        )
        params = vo.Params(override_venue=True)
        task = vo.VenueOverride(name="test-venue-override", params=params)
        result = task.execute(all_col_in_source_df, params)
        assert result.equals(expected_result)

    def test_missing_some_col_in_source_df(
        self, missing_some_col_in_source_df: pd.DataFrame
    ):
        params = vo.Params(override_venue=True)
        task = vo.VenueOverride(name="test-venue-override", params=params)
        result = task.execute(missing_some_col_in_source_df, params)
        assert result.equals(missing_some_col_in_source_df)

    def test_all_col_in_source_df_with_param_false(
        self, all_col_in_source_df: pd.DataFrame
    ):
        params = vo.Params(override_venue=False)
        task = vo.VenueOverride(name="test-venue-override", params=params)
        result = task.execute(all_col_in_source_df, params)
        assert result.equals(all_col_in_source_df)
