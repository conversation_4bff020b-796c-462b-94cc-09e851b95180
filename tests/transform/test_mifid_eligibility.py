import numpy as np
import pandas as pd
import pytest
from prefect import context
from prefect.utilities.collections import DotDict
from se_elasticsearch.repository import get_repository_by_cluster_version
from swarm.schema.flow.bundle.components import ResourceConfig

from swarm_tasks.transform.steeleye.tr.mifir.eligibility_assessor import (
    EligibilityAssessor,
)


@pytest.mark.skip(reason="needs realignment")
def test_mifid_eligibility():
    df = pd.DataFrame(
        {
            "instrumentDetails.instrument": [
                {"instrumentIdCode": "DE000C02MJD7"},
                {"instrumentIdCode": "DE000C02MJD7"},
                {"instrumentIdCode": "EZCKG2HSY0R6"},
                {"instrumentIdCode": np.nan},
                {"instrumentIdCode": "EZP2B9JRTL16"},
                np.nan,
            ],
            "transactionDetails.ultimateVenue": [
                "XOFF",
                "XOFF",
                "XOFF",
                "XOFF",
                "XOFF",
                "XOFF",
            ],
            "transactionDetails.tradingDateTime": [
                "2019-05-10T12:12:12",
                "2019-10-10T12:12:12",
                "2019-10-10T12:12:12",
                "2019-10-10T12:12:12",
                "2019-10-10T12:12:12",
                "2019-10-10T12:12:12",
            ],
        }
    )

    df["transactionDetails.tradingDateTime"] = pd.to_datetime(
        df["transactionDetails.tradingDateTime"]
    )

    # classes
    resources = EligibilityAssessor.resources_class(es_client_key="srp")

    data_types_task = EligibilityAssessor(name="eligibility_assessor")

    # clients
    resource_config = ResourceConfig(
        host="localhost",
        port=9201,
    )
    clients = {
        "srp": get_repository_by_cluster_version(resource_config=resource_config)
    }

    data_types_task.clients = clients
    identifier = DotDict({"tenant": "schroders"})
    context.swarm = DotDict()
    context.swarm.identifier = identifier

    # Execute
    result = data_types_task.execute(source_frame=df, resources=resources)
    result
