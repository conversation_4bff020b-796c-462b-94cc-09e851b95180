import datetime
from unittest.mock import patch

import pytest
from prefect.engine import signals
from se_core_tasks.controllers import single_file_input_controller
from swarm.conf import SettingsCls

from swarm_tasks.control_flow.single_file_input_controller import Params
from swarm_tasks.control_flow.single_file_input_controller import (
    SingleFileInputController,
)


class TestSingleFileInputController:
    """Test suite for SingleFileInputController"""

    @patch.object(single_file_input_controller, "datetime")
    @patch(
        "se_core_tasks.controllers.single_file_input_controller.SingleFileInputController._write_csv"
    )
    def test_file_url_does_not_match_pattern_and_doesnt_trigger_target_flow(
        self, mock_write_csv, mock_datetime, mocker
    ):
        """
        Test for the case where the file_url does not match any of the patterns in
        params.list_of_files_regex. It also does not match the patterns in
        list_of_files_regex_for_which_empty_frame_returned
        """

        mock_write_csv.return_value = None
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "test.dev.steeleye.co"
        mock_datetime.utcnow.return_value = datetime.datetime(
            2022, 3, 9, 11, 33, 52, 30977
        )
        params = Params(
            list_of_files_regex=["test_(df1)", "test_(df2)", "test_(df3)"],
            target_cloud_key_prefix="/flows/flow-which-is-triggered/",
            list_of_files_regex_for_which_empty_frame_returned=["test_(df4)"],
            output_column_name="output_col",
        )

        task = SingleFileInputController(
            params=params, name="test-single-file-input-controller"
        )

        with pytest.raises(signals.FAIL):
            task.execute(
                params=params,
                file_url="s3://test.dev.steeleye.co/flows/single-file-input-controller/test_df5_2022-02-25_foobar.csv",
            )
