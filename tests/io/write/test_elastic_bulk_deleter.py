from pathlib import Path
from typing import Optional

import addict
import pandas as pd
import pytest
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.conf import SettingsCls
from swarm.task.io.write.elastic.result import ElasticBulkWriterResult

from swarm_tasks.io.write import elastic_bulk_deleter
from swarm_tasks.io.write.elastic_bulk_deleter import Params
from swarm_tasks.io.write.elastic_bulk_deleter import Resources


INPUT_PATH = Path(__file__).parent.joinpath("data").joinpath("delete")
INPUT_EXTRACTOR_PATH_RESULT = ExtractPathResult(path=INPUT_PATH)


@pytest.fixture()
def params_fixture():
    return Params(**{"payload_size": 1000000})


@pytest.fixture()
def resources_fixture() -> Resources:
    resources = Resources(**{"es_client_key": "tenant-data"})
    return resources


@pytest.fixture()
def valid_bulk_api_response() -> dict:
    """Simulates a valid es.bulk response"""
    dict_ = {
        "took": 3,
        "errors": False,
        "items": [
            {
                "delete": {
                    "found": True,
                    "_index": "ashwath_dev_steeleye_co_accountperson",
                    "_id": "007a459d-d615-43b2-9076-dfa1d9fcf12f",
                    "_version": 2,
                    "result": "deleted",
                    "forced_refresh": True,
                    "_shards": {"total": 2, "successful": 2, "failed": 0},
                    "status": 200,
                }
            },
            {
                "delete": {
                    "found": True,
                    "_index": "ashwath_dev_steeleye_co_accountperson",
                    "_id": "5164928a-8525-4c1f-8386-07c76e51e06e",
                    "_version": 2,
                    "result": "deleted",
                    "forced_refresh": True,
                    "_shards": {"total": 2, "successful": 2, "failed": 0},
                    "status": 200,
                }
            },
        ],
    }
    return dict_


@pytest.fixture()
def expected_status_frame():
    df = pd.DataFrame(
        {
            "file_index": [0, 0],
            "raw_index": [0, 1],
            "model": ["AccountPerson", "AccountPerson"],
            "hash": [
                "a91b0eb92cd4ec2c7aeb0c5a9ab7bad9386949544e730ec8ce3ba293560db142",
                "248a153e96352d5bdba09f3c45ecc2de621fd9a263dce3c9115055676501b086",
            ],
            "id": [
                "007a459d-d615-43b2-9076-dfa1d9fcf12f",
                "5164928a-8525-4c1f-8386-07c76e51e06e",
            ],
            "es_index": [
                "ashwath_dev_steeleye_co_accountperson",
                "ashwath_dev_steeleye_co_accountperson",
            ],
            "status": ["updated", "updated"],
            "error_type": [None, None],
            "error_reason": [None, None],
            "error_caused_by_type": [None, None],
            "error_caused_by_reason": [None, None],
        }
    )
    return df


class TestElasticBulkDeleter:
    """Test suite for ElasticBulkDeleter"""

    def test_input_path_null(self, mocker, params_fixture, resources_fixture):
        """Test for the case where the input path is None"""
        task = self._init_task(
            mocker=mocker,
            params=params_fixture,
        )
        result = task.execute(
            result=ExtractPathResult(path=None),
            params=params_fixture,
            resources=resources_fixture,
        )
        assert result.frame.empty

    def test_valid_input_path_with_two_records_to_delete(
        self,
        mocker,
        params_fixture,
        resources_fixture,
        valid_bulk_api_response,
        expected_status_frame,
    ):
        """Test for the case where the input file has 2 records which have to be deleted"""
        task = self._init_task(
            mocker=mocker,
            params=params_fixture,
            elastic_bulk_response=valid_bulk_api_response,
        )
        result = task.execute(
            result=INPUT_EXTRACTOR_PATH_RESULT,
            params=params_fixture,
            resources=resources_fixture,
        )
        expected_result = ElasticBulkWriterResult(
            frame=expected_status_frame, quarantined=False, total_bytes=231
        )
        assert result.frame.equals(expected_result.frame)

    @staticmethod
    def _init_task(mocker, params, elastic_bulk_response: Optional[dict] = None):
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )

        mock_connections.return_value = addict.Dict(
            {
                "tenant-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    },
                    "MAX_TERMS_SIZE": 1024,
                }
            }
        )

        mocker.patch.object(
            SettingsCls, "tenant", new_callable=mocker.PropertyMock, return_value="test"
        )
        mocker.patch.object(
            elastic_bulk_deleter.ElasticBulkDeleter,
            "_bulk_api",
            return_value=elastic_bulk_response,
        )
        task = elastic_bulk_deleter.ElasticBulkDeleter(
            name="ElasticBulkDeleter", params=params
        )

        return task
