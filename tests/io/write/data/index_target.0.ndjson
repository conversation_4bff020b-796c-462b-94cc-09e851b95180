0|RicLookup|cf0a91993244760198291b2b38202f8c9b406aba94a589149a7b2b79554b22e5|{"index":{"_id":"GB00H24Q0T09USDXLME","_index":".ricLookup","_type":"RicLookup"}}
0|RicLookup|cf0a91993244760198291b2b38202f8c9b406aba94a589149a7b2b79554b22e5|{"&hash": "cf0a91993244760198291b2b38202f8c9b406aba94a589149a7b2b79554b22e5", "&id": "GB00H24Q0T09USDXLME", "&key": "RicLookup:GB00H24Q0T09USDXLME:1623320048832", "&model": "RicLookup", "&timestamp": 1623320048832, "&user": "system", "&version": 1, "cfi": {"attribute1": "Extraction Resources", "attribute2": "Physical", "attribute3": "Standardized", "attribute4": "Not Applicable/Undefined", "category": "Futures", "code": "FCEPSX", "group": "Commodities futures"}, "instrumentListId": "Un", "instrumentModel": "FcaFirdsInstrument", "instrumentUniqueIdentifier": "GB00H24Q0T09USDXLME", "preferredRic": "CMCU3", "preferredRicCurrency": "USD", "preferredRicVenue": "XLME", "preferredRicVenueRefinitiv": "LME", "ric": "CMCU3", "timestamps": {"coverageFrom": "2020-06-01T00:00:00", "coverageTo": "2021-06-11T00:00:00"}}
1|RicLookup|cf0a91993244760198291b2b38202f8c9b406aba94a589149a7b2b79554b22e5|{"index":{"_id":"GB00H24Q0T09USDXLME","_index":".ricLookup","_type":"RicLookup"}}
1|RicLookup|cf0a91993244760198291b2b38202f8c9b406aba94a589149a7b2b79554b22e5|{"&hash": "cf0a91993244760198291b2b38202f8c9b406aba94a589149a7b2b79554b22e5", "&id": "GB00H24Q0T09USDXLME", "&key": "RicLookup:GB00H24Q0T09USDXLME:1623320048832", "&model": "RicLookup", "&timestamp": 1623320048832, "&user": "system", "&version": 1, "cfi": {"attribute1": "Extraction Resources", "attribute2": "Physical", "attribute3": "Standardized", "attribute4": "Not Applicable/Undefined", "category": "Futures", "code": "FCEPSX", "group": "Commodities futures"}, "instrumentListId": "Un", "instrumentModel": "FcaFirdsInstrument", "instrumentUniqueIdentifier": "GB00H24Q0T09USDXLME", "preferredRic": "CMCU3", "preferredRicCurrency": "1.0", "preferredRicVenue": "XLME", "preferredRicVenueRefinitiv": "LME", "ric": "CMCU3", "timestamps": {"coverageFrom": "2020-06-01T00:00:00", "coverageTo": "2021-06-11T00:00:00"}}