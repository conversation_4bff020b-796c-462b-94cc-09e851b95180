��      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�
file_index��	raw_index��model��hash��id��es_index��status��
error_type��error_reason��error_caused_by_type��error_caused_by_reason�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C               �t�bh1Nu��R�e]�(hhK ��h��R�(KKK��h>�C                                �t�bhhK ��h��R�(KK	K��h!�]�(�
OrderState��
OrderState��@c67e28fa868382c8eb09a18d486fa0076259d0492ca0b81097d634ecf5abedd3��@c67e28fa868382c8eb09a18d486fa0076259d0492ca0b81097d634ecf5abedd3��V3437457:1:3437457432191419IA07713437457F1134542320220216BUYI:PARF:2022-02-16T20:46:47Z��V3437457:1:3437457432191419IA07713437457F1134542320220216BUYI:PARF:2022-02-16T20:46:47Z��jose_dev_steeleye_co_order��jose_dev_steeleye_co_order��version_conflict�hY�!version_conflict_engine_exception��!version_conflict_engine_exception���[OrderState][3437457:1:3437457432191419IA07713437457F1134542320220216BUYI:PARF:2022-02-16T20:46:47Z]: version conflict, document already exists (current version [1])���[OrderState][3437457:1:3437457432191419IA07713437457F1134542320220216BUYI:PARF:2022-02-16T20:46:47Z]: version conflict, document already exists (current version [1])�NNNNet�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&et�bh1Nu��R�h
h}�(hhhK ��h��R�(KK	��h!�]�(h'h(h)h*h+h,h-h.h/et�bh1Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hH�mgr_locs��builtins��slice���K KK��R�u}�(hyhNhzh}KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.