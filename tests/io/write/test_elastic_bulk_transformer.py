import filecmp
import os
from pathlib import Path
from unittest.mock import patch

import addict
import pandas as pd
import pytest
from prefect import context
from prefect.utilities.collections import DotDict
from se_core_tasks.core.auditor import parse_audit_messages
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_elastic_schema.models import Order
from se_elasticsearch.repository import get_repository_by_cluster_version
from se_schema.all_models import SteelEyeInstrument
from swarm.conf import SettingsCls
from swarm.schema.flow.bundle.components import ResourceConfig
from swarm.schema.task.auditor.model import AGGREGATION_DELIMITER
from swarm.task.auditor import Auditor
from swarm.task.io.write.elastic.result import ElasticBulkWriterResult
from swarm.task.transform.result import TransformResult

from swarm_tasks.io.write.elastic_bulk_transformer import ElasticBulkTransformer
from swarm_tasks.io.write.elastic_bulk_transformer import Params
from swarm_tasks.utilities.static import MetaModel

file_path = Path(__file__).parent.joinpath("data")


@pytest.fixture()
def transform_result() -> TransformResult:
    data = [
        # Valid <PERSON>
        {
            "&model": "RicLookup",
            "&timestamp": 1623320048832,
            "&user": "system",
            "&version": 1.0,
            "&id": "GB00H24Q0T09USDXLME",
            "&key": "RicLookup:GB00H24Q0T09USDXLME:1623320048832",
            "&hash": "cf0a91993244760198291b2b38202f8c9b406aba94a589149a7b2b79554b22e5",
            "&validationErrors": None,
            "cfi.attribute1": "Extraction Resources",
            "cfi.attribute2": "Physical",
            "cfi.attribute3": "Standardized",
            "cfi.attribute4": "Not Applicable/Undefined",
            "cfi.category": "Futures",
            "cfi.code": "FCEPSX",
            "cfi.group": "Commodities futures",
            "compositeRic": pd.NA,
            "instrumentListId": "Un",
            "instrumentModel": "FcaFirdsInstrument",
            "instrumentUniqueIdentifier": "GB00H24Q0T09USDXLME",
            "primaryTradingRic": pd.NA,
            "refinitivExchangeCode": pd.NA,
            "ric": "CMCU3",
            "preferredRic": "CMCU3",
            "timestamps.coverageFrom": "2020-06-01T00:00:00",
            "timestamps.coverageTo": "2021-06-11T00:00:00",
            "preferredRicCurrency": "USD",
            "preferredRicVenueRefinitiv": "LME",
            "preferredRicVenue": "XLME",
            "__swarm_raw_index__": "0",
        },
        # Invalid scenario
        {
            "&model": "RicLookup",
            "&timestamp": 1623320048832,
            "&user": "system",
            "&version": 1.0,
            "&id": "GB00H24Q0T09USDXLME",
            "&key": "RicLookup:GB00H24Q0T09USDXLME:1623320048832",
            "&hash": "cf0a91993244760198291b2b38202f8c9b406aba94a589149a7b2b79554b22e5",
            "&validationErrors": None,
            "cfi.attribute1": "Extraction Resources",
            "cfi.attribute2": "Physical",
            "cfi.attribute3": "Standardized",
            "cfi.attribute4": "Not Applicable/Undefined",
            "cfi.category": "Futures",
            "cfi.code": "FCEPSX",
            "cfi.group": "Commodities futures",
            "compositeRic": pd.NA,
            "instrumentListId": "Un",
            "instrumentModel": "FcaFirdsInstrument",
            "instrumentUniqueIdentifier": "GB00H24Q0T09USDXLME",
            "primaryTradingRic": pd.NA,
            "refinitivExchangeCode": pd.NA,
            "ric": "CMCU3",
            "preferredRic": "CMCU3",
            "timestamps.coverageFrom": "2020-06-01T00:00:00",
            "timestamps.coverageTo": "2021-06-11T00:00:00",
            "preferredRicCurrency": 1.0,
            "preferredRicVenueRefinitiv": "LME",
            "preferredRicVenue": "XLME",
            "__swarm_raw_index__": "1",
        },
        # Empty Scenario
        {},
    ]

    return TransformResult(target=pd.DataFrame(data), batch_index=0)


@pytest.fixture()
def empty_transform_result() -> TransformResult:
    data = [{"__swarm_raw_index__": 0}]

    return TransformResult(target=pd.DataFrame(data), batch_index=0)


@pytest.fixture()
def sample_dateframe():
    df = pd.DataFrame(
        {"col1": ["val1", "val2", pd.NA], "col2": ["val1", pd.NA, "val3"]}
    )
    return df


@pytest.fixture()
def expected_flattened_df_allow_empty():
    df = pd.DataFrame(
        {
            "identifiers": {
                0: "test-identifiers",
                1: "test-identifiers",
                2: "test-identifiers",
            },
            "data": {
                0: {"col1": "val1", "col2": "val1"},
                1: {"col1": "val2", "col2": None},
                2: {"col1": None, "col2": "val3"},
            },
        }
    )
    return df


@pytest.fixture()
def expected_action_data_to_json():
    series = pd.Series(
        {
            0: 'test-identifiers|{"data":{"col1":"val1","col2":"val1"},"identifiers":"test-identifiers"}',
            1: 'test-identifiers|{"data":{"col1":"val2"},"identifiers":"test-identifiers"}',
            2: 'test-identifiers|{"data":{"col2":"val3"},"identifiers":"test-identifiers"}',
        }
    )
    return series


@pytest.fixture()
def expected_flatened_df_without_allow_empty():
    df = pd.DataFrame(
        {
            "identifiers": {
                0: "test-identifiers",
                1: "test-identifiers",
                2: "test-identifiers",
            },
            "data": {
                0: {"col1": "val1", "col2": "val1"},
                1: {"col1": "val2"},
                2: {"col2": "val3"},
            },
        }
    )
    return df


@pytest.fixture()
def expected_format_bulk_result_when_dump_raw_json():
    data = [
        '{"create":{"_id":"GB00H24Q0T09USDXLME","_index":".ricLookup","_type":"RicLookup"}}',
        '{"&hash": "cf0a91993244760198291b2b38202f8c9b406aba94a589149a7b2b79554b22e5", "&id": "GB00H24Q0T09USDXLME", "&key": "RicLookup:GB00H24Q0T09USDXLME:1623320048832", "&model": "RicLookup", "&timestamp": 1623320048832, "&user": "system", "&version": 1, "cfi": {"attribute1": "Extraction Resources", "attribute2": "Physical", "attribute3": "Standardized", "attribute4": "Not Applicable/Undefined", "category": "Futures", "code": "FCEPSX", "group": "Commodities futures"}, "instrumentListId": "Un", "instrumentModel": "FcaFirdsInstrument", "instrumentUniqueIdentifier": "GB00H24Q0T09USDXLME", "preferredRic": "CMCU3", "preferredRicCurrency": "USD", "preferredRicVenue": "XLME", "preferredRicVenueRefinitiv": "LME", "ric": "CMCU3", "timestamps": {"coverageFrom": "2020-06-01T00:00:00", "coverageTo": "2021-06-11T00:00:00"}}',
        '{"create":{"_id":"GB00H24Q0T09USDXLME","_index":".ricLookup","_type":"RicLookup"}}',
        '{"&hash": "cf0a91993244760198291b2b38202f8c9b406aba94a589149a7b2b79554b22e5", "&id": "GB00H24Q0T09USDXLME", "&key": "RicLookup:GB00H24Q0T09USDXLME:1623320048832", "&model": "RicLookup", "&timestamp": 1623320048832, "&user": "system", "&version": 1, "cfi": {"attribute1": "Extraction Resources", "attribute2": "Physical", "attribute3": "Standardized", "attribute4": "Not Applicable/Undefined", "category": "Futures", "code": "FCEPSX", "group": "Commodities futures"}, "instrumentListId": "Un", "instrumentModel": "FcaFirdsInstrument", "instrumentUniqueIdentifier": "GB00H24Q0T09USDXLME", "preferredRic": "CMCU3", "preferredRicCurrency": "1.0", "preferredRicVenue": "XLME", "preferredRicVenueRefinitiv": "LME", "ric": "CMCU3", "timestamps": {"coverageFrom": "2020-06-01T00:00:00", "coverageTo": "2021-06-11T00:00:00"}}',
    ]
    return data


@pytest.fixture()
def invalid_order_record():
    records = [
        {
            "records": {
                "buySell": "SELL",
                "date": "2022-04-29",
                "executionDetails": {
                    "buySellIndicator": "SELL",
                    "orderStatus": "NEWO",
                },
                "id": "8210424527490|21436",
                "orderIdentifiers": {
                    "internalOrderIdCode": "1650823225557",
                    "orderIdCode": "8210424527490|21436",
                },
                "timestamps": {
                    "orderReceived": "2022-04-29T06:48:54.669000Z",
                    "orderSubmitted": "2022-04-29T06:48:54.669000Z",
                    "tradingDateTime": "2022-04-29T06:48:54.669000Z",
                },
                "traderFileIdentifier": "id:glane",
                "transactionDetails": {
                    "buySellIndicator": "SELL",
                    "tradingDateTime": "2022-04-29T06:48:54.669000Z",
                },
                "some_extra_field": "not_allowed",
            },
            "__swarm_raw_index__": 0,
            "&model": "Order",
        },
    ]
    return pd.DataFrame(records)


@pytest.fixture()
def partial_update_df() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "&id": ["TESTEST:2023-02-13:NEWT"],
            "&model": ["RTS22Transaction"],
            "&hash": [
                "5884eb7066353a149810ae297334a69a6d51162bc8ecb3502145246d01aa996e"
            ],
            "reconciliation.se.match": [True],
            "reconciliation.fieldBreaks": [
                [
                    {
                        "field": "parties.executionWithinFirm.officialIdentifiers.mifirId",
                        "value": {"arm": "TESTEST", "se": "NOT_POPULATED"},
                    }
                ]
            ],
            "reconciliation.arm.fieldBreak": [True],
            "reconciliation.arm.match": [True],
            "reconciliation.arm.sourceKey": ["SOURCE_KEY"],
            "__swarm_raw_index__": [0],
            "&timestamp": [1681752078060],
        }
    )


@pytest.fixture()
def partial_update_expected_result() -> pd.Series:
    return pd.Series(
        {
            0: '0|RTS22Transaction|5884eb7066353a149810ae297334a69a6d51162bc8ecb3502145246d01aa996e|{"doc":{"&hash":"5884eb7066353a149810ae297334a69a6d51162bc8ecb3502145246d01aa996e","&id":"TESTEST:2023-02-13:NEWT","&model":"RTS22Transaction","&timestamp":1681752078060,"reconciliation":{"arm":{"fieldBreak":true,"match":true,"sourceKey":"SOURCE_KEY"},"fieldBreaks":[{"field":"parties.executionWithinFirm.officialIdentifiers.mifirId","value":{"arm":"TESTEST","se":"NOT_POPULATED"}}],"se":{"match":true}}}}'
        }
    )


@pytest.mark.skipif(os.environ.get("CI") == "true", reason="cannot run in CI")
class TestElasticBulkTransformer:
    def test_valid_create_execution(self, mocker, transform_result):
        params = Params(**{"action_type": "create"})
        resources = ElasticBulkTransformer.resources_class(
            es_client_key="reference-data"
        )

        task = ElasticBulkTransformer(
            name="test_elastic_bulk_transformer", params=params, resources=resources
        )

        resource_config = ResourceConfig(
            host="elasticsearch.srp.steeleye.co", port=9801
        )

        clients = {
            "reference-data": get_repository_by_cluster_version(
                resource_config=resource_config
            )
        }
        from prefect import context

        context.swarm = DotDict()
        context.swarm.identifier = DotDict({"tenant": "platform"})
        context.swarm.targets_dir = Path("/tmp")

        mocker.patch.object(
            SettingsCls,
            "tenant",
            new_callable=mocker.PropertyMock,
            return_value="platform",
        )

        task.clients = clients

        result = task.execute(
            transform_result=transform_result, params=params, resources=resources
        )

        assert filecmp.cmp(
            file_path.joinpath("create_target.0.ndjson"),
            result.path.joinpath("target.0.ndjson"),
        )

    def test_valid_update_execution(self, mocker, transform_result):
        params = Params(**{"action_type": "update"})
        resources = ElasticBulkTransformer.resources_class(
            es_client_key="reference-data"
        )

        task = ElasticBulkTransformer(
            name="test_elastic_bulk_transformer", params=params, resources=resources
        )

        resource_config = ResourceConfig(
            host="elasticsearch.srp.steeleye.co", port=9801
        )

        clients = {
            "reference-data": get_repository_by_cluster_version(
                resource_config=resource_config
            )
        }
        from prefect import context

        context.swarm = DotDict()
        context.swarm.identifier = DotDict({"tenant": "platform"})
        context.swarm.targets_dir = Path("/tmp")

        task.clients = clients

        mocker.patch.object(
            SettingsCls,
            "tenant",
            new_callable=mocker.PropertyMock,
            return_value="platform",
        )

        result = task.execute(
            transform_result=transform_result, params=params, resources=resources
        )

        assert filecmp.cmp(
            file_path.joinpath("update_target.0.ndjson"),
            result.path.joinpath("target.0.ndjson"),
        )

    def test_valid_index_execution(self, mocker, transform_result):
        params = Params(**{"action_type": "index"})
        resources = ElasticBulkTransformer.resources_class(
            es_client_key="reference-data"
        )

        task = ElasticBulkTransformer(
            name="test_elastic_bulk_transformer", params=params, resources=resources
        )

        resource_config = ResourceConfig(
            host="elasticsearch.srp.steeleye.co", port=9801
        )

        clients = {
            "reference-data": get_repository_by_cluster_version(
                resource_config=resource_config
            )
        }
        from prefect import context

        context.swarm = DotDict()
        context.swarm.identifier = DotDict({"tenant": "platform"})
        context.swarm.targets_dir = Path("/tmp")

        task.clients = clients

        mocker.patch.object(
            SettingsCls,
            "tenant",
            new_callable=mocker.PropertyMock,
            return_value="platform",
        )

        result = task.execute(
            transform_result=transform_result, params=params, resources=resources
        )

        assert filecmp.cmp(
            file_path.joinpath("index_target.0.ndjson"),
            result.path.joinpath("target.0.ndjson"),
        )


def test_unflatten_data_allow_empty(
    sample_dateframe, expected_flattened_df_allow_empty
):
    params = Params(**{"action_type": "index", "allow_empty": True})
    resources = ElasticBulkTransformer.resources_class(es_client_key="reference-data")
    task = ElasticBulkTransformer(
        name="test_elastic_bulk_transformer", params=params, resources=resources
    )
    df = task._unflatten_data(
        df=sample_dateframe, records_identifiers="test-identifiers", params=params
    )
    assert df.equals(expected_flattened_df_allow_empty)


def test_unflatten_data_without_allow_empty(
    sample_dateframe, expected_flatened_df_without_allow_empty
):
    params = Params(**{"action_type": "index"})
    resources = ElasticBulkTransformer.resources_class(es_client_key="reference-data")
    task = ElasticBulkTransformer(
        name="test_elastic_bulk_transformer", params=params, resources=resources
    )
    df = task._unflatten_data(
        df=sample_dateframe, records_identifiers="test-identifiers", params=params
    )
    assert df.equals(expected_flatened_df_without_allow_empty)


def test_action_data_to_json(
    expected_flatened_df_without_allow_empty, expected_action_data_to_json
):
    params = Params(**{"action_type": "index"})
    resources = ElasticBulkTransformer.resources_class(es_client_key="reference-data")
    task = ElasticBulkTransformer(
        name="test_elastic_bulk_transformer", params=params, resources=resources
    )
    record_identifiers = pd.Series(
        ["test-identifiers", "test-identifiers", "test-identifiers"]
    )
    series = task._action_data_to_json(
        df=expected_flatened_df_without_allow_empty,
        records_identifiers=record_identifiers,
        params=params,
    )
    assert series.equals(expected_action_data_to_json)


@pytest.fixture()
def transform_quarantined_result() -> TransformResult:
    data = [
        # Valid Scenario
        {
            "&model": MetaModel.ORDER,
            "&timestamp": 1623320048832,
            "&user": "system",
            "&version": 1,
            "&id": "GB00H24Q0T09USDXLME",
            "&key": "Order:GB00H24Q0T09USDXLME:1623320048832",
            "&hash": "cf0a91993244760198291b2b38202f8c9b406aba94a589149a7b2b79554b22e5",
            "&validationErrors": None,
            "__swarm_raw_index__": 0,
        },
        {
            "&model": MetaModel.ORDER,
            "&timestamp": 1623320048832,
            "&user": "system",
            "&version": 1,
            "&id": "GB00H24Q0T09USDXLME",
            "&key": "Order:GB00H24Q0T09USDXLME:1623320048832",
            "&hash": "cf0a91993244760198291b2b38202f8c9b406aba94a589149a7b2b79554b22e5",
            "&validationErrors": None,
            "__swarm_raw_index__": 1,
        },
        {
            "&model": MetaModel.ORDER,
            "&timestamp": 1623320048832,
            "&user": "system",
            "&version": 1,
            "&id": "GB00H24Q0T09USDXLME",
            "&key": "Order:GB00H24Q0T09USDXLME:1623320048832",
            "&hash": "cf0a91993244760198291b2b38202f8c9b406aba94a589149a7b2b79554b22e5",
            "&validationErrors": None,
            "__swarm_raw_index__": 2,
        },
        {
            "&model": MetaModel.ORDER,
            "&timestamp": 1623320048832,
            "&user": "system",
            "&version": 1,
            "&id": "GB00H24Q0T09USDXLME",
            "&key": "OrderState:GB00H24Q0T09USDXLME:1623320048832",
            "&hash": "cf0a91993244760198291b2b38202f8c9b406aba94a589149a7b2b79554b22e5",
            "&validationErrors": None,
            "__swarm_raw_index__": 3,
        },
    ]

    return TransformResult(target=pd.DataFrame(data), batch_index=0)


@pytest.fixture()
def bulk_writer_result() -> ElasticBulkWriterResult:
    data = [
        {"file_index": 0, "raw_index": 0, "model": "Order", "status": "duplicate"},
        {
            "file_index": 0,
            "raw_index": 1,
            "model": "Order",
            "status": "version_conflict",
        },
        {"file_index": 0, "raw_index": 2, "model": "Order", "status": "created"},
    ]
    return ElasticBulkWriterResult(
        frame=pd.DataFrame(data), total_bytes=10, quarantined=False
    )


@pytest.fixture()
def expected_data_make_sink_rec_audit() -> pd.DataFrame:
    data = {
        "__swarm_raw_index__": [0, 1, 2, 3],
        "bucket": [
            "test.steeleye.co",
            "test.steeleye.co",
            "test.steeleye.co",
            "test.steeleye.co",
        ],
        "key": ["test", "test", "test", "test"],
        "dataSourceName": [
            "dummy_bundle",
            "dummy_bundle",
            "dummy_bundle",
            "dummy_bundle",
        ],
        "taskStarted": [
            "2022-01-0112:00:00",
            "2022-01-0112:00:00",
            "2022-01-0112:00:00",
            "2022-01-0112:00:00",
        ],
        "processed": [
            "2022-04-14T09:00:36Z",
            "2022-04-14T09:00:36Z",
            "2022-04-14T09:00:36Z",
            "2022-04-14T09:00:36Z",
        ],
        "index": [0, 1, 2, 3],
        "recordType": ["Order", "Order", "Order", "Order"],
        "recordKey": [
            "QuarantinedOrder:GB00H24Q0T09USDXLME:1623320048832",
            "QuarantinedOrder:GB00H24Q0T09USDXLME:1623320048832",
            "QuarantinedOrder:GB00H24Q0T09USDXLME:1623320048832",
            "QuarantinedOrder:GB00H24Q0T09USDXLME:1623320048832",
        ],
        "matchedKey": [pd.NA, pd.NA, pd.NA, pd.NA],
        "status": ["QUARANTINED", "QUARANTINED", "QUARANTINED", "QUARANTINED"],
        "&user": ["system", "system", "system", "system"],
        "&model": [
            "SinkRecordAudit",
            "SinkRecordAudit",
            "SinkRecordAudit",
            "SinkRecordAudit",
        ],
        "&id": [
            "test.steeleye.co:test:2022-01-0112:00:00:0:quarantinedorder:gb00h24q0t09usdxlme:1623320048832",
            "test.steeleye.co:test:2022-01-0112:00:00:1:quarantinedorder:gb00h24q0t09usdxlme:1623320048832",
            "test.steeleye.co:test:2022-01-0112:00:00:2:quarantinedorder:gb00h24q0t09usdxlme:1623320048832",
            "test.steeleye.co:test:2022-01-0112:00:00:2:quarantinedorder:gb00h24q0t09usdxlme:1623320048832",
        ],
        "&timestamp": [1649926841288, 1649926841288, 1649926841288, 1649926841288],
        "&key": [
            "SinkRecordAudit:test.steeleye.co:test:2022-01-0112:00:00:0:quarantinedorder:gb00h24q0t09usdxlme:1623320048832:1649926841288",
            "SinkRecordAudit:test.steeleye.co:test:2022-01-0112:00:00:1:quarantinedorder:gb00h24q0t09usdxlme:1623320048832:1649926841288",
            "SinkRecordAudit:test.steeleye.co:test:2022-01-0112:00:00:2:quarantinedorder:gb00h24q0t09usdxlme:1623320048832:1649926841288",
            "SinkRecordAudit:test.steeleye.co:test:2022-01-0112:00:00:2:quarantinedorder:gb00h24q0t09usdxlme:1623320048832:1649926841288",
        ],
        "&hash": [
            "c4d3e4c57110d1781c2d9b21d9dcc1e6814951a3701c32acef3580962dee1b50",
            "2e0ecb699e2f33d7b809c5eb0484870d08988ab95b5f3dd9cdd93d33b2c2166f",
            "43f9707ee600c603d776f0aa47c766e3f4b42538377bf4eefcff6767893a3939",
            "6b37b1c57d13ef2ea41970f8df7ce88cac070f56f0730ae9c44fb9fae4a63e9c",
        ],
    }
    return pd.DataFrame(data)


class TestElasticBulkTransformerQuarantineHandling:
    def test_handle_quarantine(self, transform_quarantined_result, bulk_writer_result):
        params = Params(**{"action_type": "index"})
        resources = ElasticBulkTransformer.resources_class(
            es_client_key="reference-data"
        )

        task = ElasticBulkTransformer(
            name="test_elastic_bulk_transformer", params=params, resources=resources
        )

        result = task._get_conflict_data(
            transform_frame=transform_quarantined_result.target,
            bulk_writer_frame=bulk_writer_result.frame,
            es=DotDict({"meta": DotDict({"model": "&model"})}),
        )

        # Assert that the method returns only the version conflict record
        assert result.loc[:, "__swarm_raw_index__"].values.tolist() == [1]

    @staticmethod
    @patch.object(ElasticBulkTransformer, "_task_start_time_from_context")
    @patch.object(ElasticBulkTransformer, "_fetch_quarantine_match_data")
    def test_make_sink_record_audit(
        mock_fetch_quarantine_data,
        mock_start_time,
        transform_quarantined_result,
        expected_flattened_df_allow_empty,
        expected_data_make_sink_rec_audit,
        mocker,
    ):
        mock_fetch_quarantine_data.return_value = pd.DataFrame()
        params = Params(**{"action_type": "index", "allow_empty": True})
        resources = ElasticBulkTransformer.resources_class(
            es_client_key="reference-data"
        )
        task = ElasticBulkTransformer(
            name="test_elastic_bulk_transformer", params=params, resources=resources
        )
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "test.steeleye.co"
        mock_bundle = mocker.patch.object(
            SettingsCls, "bundle", new_callable=mocker.PropertyMock
        )
        mock_bundle.return_value = "dummy_bundle"
        mock_start_time.return_value = "2022-01-0112:00:00"
        context.parameters = DotDict()
        context.parameters.file_url = "test"
        es = addict.Dict(
            {
                "meta": {
                    "id": "&id",
                    "hash": "&hash",
                    "model": "&model",
                    "key": "&key",
                    "user": "&user",
                    "timestamp": "&timestamp",
                }
            }
        )
        df = task._make_sink_record_audit(
            data=transform_quarantined_result.target, es=es
        )
        columns_to_drop = ["&timestamp", "processed", "&hash", "&key"]
        df = df.drop(columns=columns_to_drop)
        expected_data_make_sink_rec_audit = expected_data_make_sink_rec_audit.drop(
            columns=columns_to_drop
        )
        assert (
            df.dropna()
            .astype(str)
            .equals(expected_data_make_sink_rec_audit.dropna().astype(str))
        )

    @staticmethod
    @patch.object(ElasticBulkTransformer, "_task_start_time_from_context")
    @patch.object(ElasticBulkTransformer, "_fetch_quarantine_match_data")
    def test_make_sink_record_audit_without_file_url(
        mock_fetch_quarantine_data,
        mock_start_time,
        transform_quarantined_result,
        expected_flattened_df_allow_empty,
        expected_data_make_sink_rec_audit,
        mocker,
    ):
        mock_fetch_quarantine_data.return_value = pd.DataFrame()
        params = Params(**{"action_type": "index", "allow_empty": True})
        resources = ElasticBulkTransformer.resources_class(
            es_client_key="reference-data"
        )
        task = ElasticBulkTransformer(
            name="test_elastic_bulk_transformer", params=params, resources=resources
        )
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "test.steeleye.co"
        mock_bundle = mocker.patch.object(
            SettingsCls, "bundle", new_callable=mocker.PropertyMock
        )
        mock_bundle.return_value = "dummy_bundle"
        expected_data_make_sink_rec_audit["key"] = "dummy_bundle"
        mock_start_time.return_value = "2022-01-0112:00:00"
        context.parameters = DotDict()
        es = addict.Dict(
            {
                "meta": {
                    "id": "&id",
                    "hash": "&hash",
                    "model": "&model",
                    "key": "&key",
                    "user": "&user",
                    "timestamp": "&timestamp",
                }
            }
        )
        df = task._make_sink_record_audit(
            data=transform_quarantined_result.target, es=es
        )
        columns_to_drop = ["&timestamp", "processed", "&hash", "&key"]
        df = df.drop(columns=columns_to_drop)
        expected_data_make_sink_rec_audit = expected_data_make_sink_rec_audit.drop(
            columns=columns_to_drop
        )
        assert (
            df.dropna()
            .astype(str)
            .equals(expected_data_make_sink_rec_audit.dropna().astype(str))
        )

    def test_invalid_record_pydantic_validation_audit(self, invalid_order_record):
        params = Params(**{"action_type": "index", "allow_empty": True})
        record_identifier = pd.Series({0: "0|Order|1234567"})
        resources = ElasticBulkTransformer.resources_class(
            es_client_key="reference-data"
        )
        auditor = Auditor(task_name="assign meta")

        task = ElasticBulkTransformer(
            name="test_elastic_bulk_transformer", params=params, resources=resources
        )
        task._auditor = auditor
        task._records_data_to_json(
            df=invalid_order_record,
            params=params,
            records_identifiers=record_identifier,
            target_cluster_is_srp=True,
        )
        assert (
            parse_audit_messages(task.auditor.to_dataframe().loc[0, "ctx.error"])
            == f"Order {AGGREGATION_DELIMITER} ['buySell - field required', 'date - field required', "
            f"'id - field required', 'records - extra fields not permitted'] {AGGREGATION_DELIMITER} Index: 0"
        )

    def test_partial_update_records_data_to_json(
        self, partial_update_df, partial_update_expected_result
    ):
        params = Params(**{"action_type": "update", "allow_empty": True})
        record_identifier = pd.Series(
            {
                0: "0|RTS22Transaction|5884eb7066353a149810ae297334a69a6d51162bc8ecb3502145246d01aa996e"
            }
        )

        resources = ElasticBulkTransformer.resources_class(
            es_client_key="reference-data"
        )
        auditor = Auditor(task_name="assign meta")

        task = ElasticBulkTransformer(
            name="test_elastic_bulk_transformer", params=params, resources=resources
        )
        task._auditor = auditor
        result = task._records_data_to_json(
            df=partial_update_df,
            params=params,
            records_identifiers=record_identifier,
            target_cluster_is_srp=True,
        )

        assert result.equals(partial_update_expected_result)


class TestElasticBulkTransformerCISafe:
    def test_valid_create_execution(self, empty_transform_result, mocker):
        params = Params(**{"action_type": "create"})
        resources = ElasticBulkTransformer.resources_class(
            es_client_key="reference-data"
        )
        task = ElasticBulkTransformer(
            name="test_elastic_bulk_transformer", params=params, resources=resources
        )

        mock_clients = mocker.patch.object(ElasticBulkTransformer, "clients")
        mock_clients.return_value = {"reference-data": {"id": "&id"}}
        result = task.execute(
            transform_result=empty_transform_result, params=params, resources=resources
        )
        expected_result = ExtractPathResult(None)

        assert result == expected_result

    def test_valid_create_execution_with_batch_index(self):
        batch_no = 2
        context.swarm = DotDict()
        context.swarm.targets_dir = Path("/tmp")
        params = Params(**{"action_type": "create"})

        resources = ElasticBulkTransformer.resources_class(
            es_client_key="reference-data"
        )
        task = ElasticBulkTransformer(
            name="test_elastic_bulk_transformer", params=params, resources=resources
        )

        result = task.write_content_locally(
            content='{"a": "b"}',
            params=params,
            batch_index=batch_no,
            producer_result=None,
        )

        assert f"batch-{batch_no}" in str(result)

    def test_dump_raw_ndjson_execution(
        self, mocker, transform_result, expected_format_bulk_result_when_dump_raw_json
    ):
        params = Params(**{"action_type": "create", "dump_raw_ndjson": True})
        resources = ElasticBulkTransformer.resources_class(
            es_client_key="reference-data"
        )
        task = ElasticBulkTransformer(
            name="test_elastic_bulk_transformer", params=params, resources=resources
        )

        es_client = addict.Dict(
            {"meta": {"id": "&id", "model": "&model", "hash": "&hash"}}
        )
        mock_clients = mocker.patch.object(ElasticBulkTransformer, "clients")
        mock_clients.return_value = {"reference-data": es_client}
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "test.steeleye.co"
        mock_bundle = mocker.patch.object(
            SettingsCls, "bundle", new_callable=mocker.PropertyMock
        )
        mock_bundle.return_value = "dummy_bundle"
        input_df = transform_result.target
        input_df = input_df.dropna(how="all")
        result = task._format_bulk(
            params=params,
            target_frame=input_df,
            es_client=es_client,
            target_cluster_is_srp=True,
        )

        assert result.split("\n") == expected_format_bulk_result_when_dump_raw_json

    def test_get_elastic_index_srp(self):

        model = SteelEyeInstrument
        masked_target_frame = pd.DataFrame({"foo": ["bar1", "bar2"]}, index=[5, 6])
        es_client = None
        target_cluster_is_srp = True

        resources = ElasticBulkTransformer.resources_class(
            es_client_key="reference-data"
        )
        params = Params(**{"action_type": "create"})
        task = ElasticBulkTransformer(
            name="test_elastic_bulk_transformer", params=params, resources=resources
        )

        result = task._get_elastic_index(
            model=model,
            masked_target_frame=masked_target_frame,
            es_client=es_client,
            target_cluster_is_srp=target_cluster_is_srp,
        )

        expected_result = pd.Series(data=[".steeleyeInstrument"] * 2, index=[5, 6])

        assert result.equals(expected_result)

    def test_get_elastic_index_es8(self, mocker):

        mocker.patch.object(
            SettingsCls,
            "tenant",
            new_callable=mocker.PropertyMock,
            return_value="test_tenant",
        )

        model = Order
        masked_target_frame = pd.DataFrame(
            {
                "&id": [
                    "fresh_order_1",
                    "fresh_order_2",
                    "duplicate_order_3",
                ]
            },
            index=[5, 6, 9],  # random index to test that it is always preserved
        )

        class MockEsClient:
            @staticmethod
            def search(*args, **kwargs):
                return {
                    "hits": {
                        "hits": [
                            {
                                "_index": "mock_order_es_index",
                                "_id": "duplicate_order_3",
                            },
                        ]
                    }
                }

        es_client = MockEsClient()
        target_cluster_is_srp = False
        resources = ElasticBulkTransformer.resources_class(es_client_key="tenant-data")
        params = Params(**{"action_type": "create"})
        task = ElasticBulkTransformer(
            name="test_elastic_bulk_transformer", params=params, resources=resources
        )

        result = task._get_elastic_index(
            model=model,
            masked_target_frame=masked_target_frame,
            es_client=es_client,
            target_cluster_is_srp=target_cluster_is_srp,
        )

        # The first 2 aliases are auto-generated by se-elastic-schema
        # for the model `Order` and tenant `test_tenant`.
        # The last index name is the one that we fetch from Elastic
        # This simulates the case where we have a duplicate record
        # in a specific index, which is not the latest, and we want to ensure
        # that whatever ES operation we do, we do against the right index
        # i.e. we do not want to create a duplicate Order in a separate index,
        # we want to provoke a version conflict in the index that contains the dupe
        expected_result = pd.Series(
            data=[
                "test_tenant-order-alias",
                "test_tenant-order-alias",
                "mock_order_es_index",
            ],
            index=[5, 6, 9],
        )

        assert result.equals(expected_result)
