from pathlib import Path
from typing import List

import pytest

from swarm_tasks.io.read import remove_file_section
from swarm_tasks.io.read.remove_file_section import Params

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data/txt")
TEST_FILE_PATH = TEST_FILES_DIR.joinpath(r"test_file_remove_file_section.txt")


@pytest.fixture()
def params_fixture() -> Params:
    params = Params(**{"target_regex": "^Two"})
    return params


@pytest.fixture()
def lines() -> List[str]:
    with open(file=TEST_FILE_PATH, mode="r") as f:
        lines = f.readlines()
    return lines


@pytest.fixture()
def failed_regex() -> str:
    return "^Failed"


class TestRemoveFileSection(object):
    """
    Test cases for "TestRemoveFileSection"
    """

    def test_get_target_index_success(self, lines: List[str], params_fixture: Params):

        task = remove_file_section.RemoveFileSection(
            name="remove-file-section", params=params_fixture
        )
        expected_result = 1

        test_result_index = task._get_target_index(lines=lines, params=params_fixture)

        assert expected_result == test_result_index

    def test_get_target_index_failed(self, lines: List[str], params_fixture: Params):
        task = remove_file_section.RemoveFileSection(
            name="remove-file-section", params=params_fixture
        )
        expected_result = None

        params_fixture.target_regex = "^Failed"

        test_result_index = task._get_target_index(lines=lines, params=params_fixture)

        assert expected_result == test_result_index

    def test_splice_lines(self, lines: List[str], params_fixture: Params):
        task = remove_file_section.RemoveFileSection(
            name="remove-file-section", params=params_fixture
        )
        expected_result = "One, Two, Three, Four, This is the first line\n"

        test_result_index = task._slice_file_lines(
            lines=lines, index=1, params=params_fixture
        )

        assert expected_result == test_result_index[-1]

    def test_splice_lines_upwards(self, lines: List[str], params_fixture: Params):
        task = remove_file_section.RemoveFileSection(
            name="remove-file-section", params=params_fixture
        )
        expected_result = "Three, Four, One, Two, This is the third line\n"

        params_fixture.remove_upwards = True

        test_result_index = task._slice_file_lines(
            lines=lines, index=1, params=params_fixture
        )

        assert expected_result == test_result_index[0]

    def test_splice_lines_include_target(
        self, lines: List[str], params_fixture: Params
    ):
        task = remove_file_section.RemoveFileSection(
            name="remove-file-section", params=params_fixture
        )
        expected_result = "Two, Three, Four, One, This is the second line\n"

        params_fixture.include_target_line = True

        test_result_index = task._slice_file_lines(
            lines=lines, index=1, params=params_fixture
        )

        assert expected_result == test_result_index[-1]

    def test_splice_lines_upwards_include_target(
        self, lines: List[str], params_fixture: Params
    ):
        task = remove_file_section.RemoveFileSection(
            name="remove-file-section", params=params_fixture
        )
        expected_result = "Two, Three, Four, One, This is the second line\n"

        params_fixture.remove_upwards = True
        params_fixture.include_target_line = True

        test_result_index = task._slice_file_lines(
            lines=lines, index=1, params=params_fixture
        )

        assert expected_result == test_result_index[0]
