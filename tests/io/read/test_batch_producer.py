import pandas as pd
import pytest
from se_core_tasks.io.read.batch_producer import Batch<PERSON>roducer as SeCoreBatchProducer

from swarm_tasks.io.read.batch_producer import BatchProducer


@pytest.fixture()
def df_string_dtype_before_casting_to_float() -> pd.DataFrame:
    return pd.DataFrame(
        {"Up-Front Payment": [pd.NA, "0.00000", pd.NA, "21431.75", pd.NA]},
        dtype="string",
    )


@pytest.fixture()
def df_string_dtype_after_casting_to_float() -> pd.DataFrame:
    return pd.DataFrame({"Up-Front Payment": [pd.NA, 0.0, pd.NA, 21431.75, pd.NA]})


class TestBatchProducer:
    def test_import(self):
        assert BatchProducer

    def test_safe_cast_string_to_float(
        self,
        df_string_dtype_before_casting_to_float: pd.DataFrame,
        df_string_dtype_after_casting_to_float: pd.DataFrame,
    ):
        """
        This test is specific to Swarm and Pandas 1.1.5. When we have a StringDtype
        column with nulls and decimal values, we get a 'ValueError: must provide strings'
        exception while trying to safe-cast each of the non-null values. To fix this,
        we cast to object and then cast to float/int (this is only done for floats and int)
        """
        task = SeCoreBatchProducer()

        task._cast_df_to_type(
            dataframe=df_string_dtype_before_casting_to_float,
            columns_datatype={"Up-Front Payment": "float"},
        )
        # The function mutates the data frame in the argument
        pd.testing.assert_frame_equal(
            left=df_string_dtype_before_casting_to_float,
            right=df_string_dtype_after_casting_to_float,
        )
