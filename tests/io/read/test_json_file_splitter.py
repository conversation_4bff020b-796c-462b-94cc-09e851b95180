from pathlib import Path

import addict
import pandas as pd
import pytest
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from swarm.task.auditor import Auditor

from swarm_tasks.io.read import json_file_splitter
from swarm_tasks.io.read.json_file_splitter import JsonFileSplitter
from swarm_tasks.io.read.json_file_splitter import Params


SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data/json")

EMPTY_FILE_PATH = ExtractPathResult(path=TEST_FILES_DIR.joinpath(r"empty-file.json"))

SINGLE_ITEM_FILE_PATH = ExtractPathResult(
    path=TEST_FILES_DIR.joinpath(r"single-item-file.json")
)

TWO_ITEMS_FILE_PATH = ExtractPathResult(
    path=TEST_FILES_DIR.joinpath(r"two-items-file.json")
)

FIVE_ITEM_FILE_PATH = ExtractPathResult(
    path=TEST_FILES_DIR.joinpath(r"5-item-file.json")
)

NON_EXISTENT_FILE_PATH = ExtractPathResult(
    path=TEST_FILES_DIR.joinpath(r"non-existent-file.json")
)


class TestJsonFileSplitter:
    @staticmethod
    def teardown_method():
        for i in range(3):
            if SCRIPT_PATH.joinpath(f"batch_{i}.csv").exists():
                SCRIPT_PATH.joinpath(f"batch_{i}.csv").unlink()

    def test_empty_file_raises_value_error(self):
        params = Params()
        task = JsonFileSplitter(name="JsonFileSplitter", params=params)

        with pytest.raises(ValueError) as e:
            task.execute(params=params, extractor_result=EMPTY_FILE_PATH)

        assert str(e.value).startswith("empty input file")

    def test_non_existent_file_raises_file_not_found_exception(self):
        params = Params()
        task = JsonFileSplitter(name="JsonFileSplitter", params=params)

        with pytest.raises(FileNotFoundError):
            task.execute(params=params, extractor_result=NON_EXISTENT_FILE_PATH)

    def test_file_with_single_item_returns_single_row_dataframe(self):
        json_file_splitter.context = addict.Dict(
            {"swarm": {"sources_dir": SCRIPT_PATH}}
        )

        params = Params()
        task = JsonFileSplitter(name="JsonFileSplitter", params=params)
        result = task.execute(params, extractor_result=SINGLE_ITEM_FILE_PATH)

        assert len(result) == 1

        df = pd.read_csv(result[0].path)
        assert len(df) == 1

    def test_chunk_size_returns_expected_number_of_batch_files(self):
        json_file_splitter.context = addict.Dict(
            {"swarm": {"sources_dir": SCRIPT_PATH}}
        )
        expected = [
            FileSplitterResult(path=SCRIPT_PATH.joinpath("batch_0.csv"), batch_index=0),
            FileSplitterResult(path=SCRIPT_PATH.joinpath("batch_1.csv"), batch_index=1),
            FileSplitterResult(path=SCRIPT_PATH.joinpath("batch_2.csv"), batch_index=2),
        ]

        params = Params(chunksize=2)
        task = JsonFileSplitter(name="JsonFileSplitter", params=params)
        result = task.execute(params, extractor_result=FIVE_ITEM_FILE_PATH)

        assert len(result) == len(expected)
        assert result == expected

    def test_single_file_with_single_item_returns_single_row_dataframe_for_list_input(
        self,
    ):
        json_file_splitter.context = addict.Dict(
            {"swarm": {"sources_dir": SCRIPT_PATH}}
        )

        params = Params()
        task = JsonFileSplitter(name="JsonFileSplitter", params=params)
        result = task.execute(params, extractor_result=[SINGLE_ITEM_FILE_PATH])

        assert len(result) == 1

        df = pd.read_csv(result[0].path)
        assert len(df) == 1

    def test_two_files_with_items_returns_dataframe_accordingly_for_list_input(self):
        json_file_splitter.context = addict.Dict(
            {"swarm": {"sources_dir": SCRIPT_PATH}}
        )

        params = Params()
        task = JsonFileSplitter(name="JsonFileSplitter", params=params)
        result = task.execute(
            params, extractor_result=[SINGLE_ITEM_FILE_PATH, TWO_ITEMS_FILE_PATH]
        )

        assert len(result) == 1

        df = pd.read_csv(result[0].path)
        assert len(df) == 3

    def test_audit_message(self):
        auditor = Auditor(task_name="Dummy")
        json_file_splitter.context = addict.Dict(
            {"swarm": {"sources_dir": SCRIPT_PATH}}
        )

        params = Params(audit_input_rows=True)
        task = JsonFileSplitter(name="JsonFileSplitter", params=params)
        task._auditor = auditor
        result = task.execute(
            params, extractor_result=[SINGLE_ITEM_FILE_PATH, TWO_ITEMS_FILE_PATH]
        )
        df = pd.read_csv(result[0].path)
        assert auditor.to_dataframe().loc[0, "ctx.input_total_count"] == df.shape[0]
