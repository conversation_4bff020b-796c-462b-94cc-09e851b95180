from abc import ABC
from datetime import datetime
from datetime import timedelta

import pandas as pd
import pytest
from moto import mock_aws
from prefect.engine.signals import SKIP
from se_core_tasks.abstractions.abstract_mock_s3 import AbstractMockS3
from swarm.conf import SettingsCls

from swarm_tasks.io.read.aws.fetch_and_merge_text_files import FetchAndMergeTextFiles
from swarm_tasks.io.read.aws.fetch_and_merge_text_files import Params

DATE_VALUE = (datetime.today() - timedelta(days=1)).strftime("%Y%m%d")


@pytest.fixture
def valid_flow_args():
    return '{"prefix":"ingress/raw/flow-name", "delta_in_days":1}'


@pytest.fixture
def prefix_does_not_exist_flow_args():
    return '{"prefix":"this/dir/does/not/exist", "delta_in_days":1}'


@pytest.fixture
def empty_params():
    return Params()


@mock_aws
class TestFetchAndMergeTextFiles:

    task_was_initialized = False

    def test_end_to_end(self, mocker, valid_flow_args, empty_params):

        self.init_task(mocker)
        task = FetchAndMergeTextFiles(name="test")
        result = task.execute(flow_args=valid_flow_args, params=empty_params)

        expected_result = pd.DataFrame(
            {
                "file_path": [
                    f"s3://test.dev.steeleye.co/ingress/raw/flow-name/{DATE_VALUE}/test_file_0.txt",
                    f"s3://test.dev.steeleye.co/ingress/raw/flow-name/{DATE_VALUE}/test_file_1.txt",
                    f"s3://test.dev.steeleye.co/ingress/raw/flow-name/{DATE_VALUE}/test_file_2.txt",
                    f"s3://test.dev.steeleye.co/ingress/raw/flow-name/{DATE_VALUE}/test_file_3.txt",
                    f"s3://test.dev.steeleye.co/ingress/raw/flow-name/{DATE_VALUE}/test_file_batch.txt",
                    f"s3://test.dev.steeleye.co/ingress/raw/flow-name/{DATE_VALUE}/test_file_batch.txt",
                ],
                "file_content": [
                    "crazy kiwi 0",
                    "crazy kiwi 1",
                    "crazy kiwi 2",
                    "crazy kiwi 3",
                    "crazy kiwi batch 1",
                    "crazy kiwi batch 2",
                ],
            }
        )
        assert result.equals(expected_result)

    def test_end_to_end_no_line_split(self, mocker, valid_flow_args):

        self.init_task(mocker)
        task = FetchAndMergeTextFiles(name="test")
        result = task.execute(
            flow_args=valid_flow_args, params=Params(line_split=False)
        )

        expected_result = pd.DataFrame(
            {
                "file_path": [
                    f"s3://test.dev.steeleye.co/ingress/raw/flow-name/{DATE_VALUE}/test_file_0.txt",
                    f"s3://test.dev.steeleye.co/ingress/raw/flow-name/{DATE_VALUE}/test_file_1.txt",
                    f"s3://test.dev.steeleye.co/ingress/raw/flow-name/{DATE_VALUE}/test_file_2.txt",
                    f"s3://test.dev.steeleye.co/ingress/raw/flow-name/{DATE_VALUE}/test_file_3.txt",
                    f"s3://test.dev.steeleye.co/ingress/raw/flow-name/{DATE_VALUE}/test_file_batch.txt",
                ],
                "file_content": [
                    "crazy kiwi 0",
                    "crazy kiwi 1",
                    "crazy kiwi 2",
                    "crazy kiwi 3",
                    "crazy kiwi batch 1\ncrazy kiwi batch 2\n",
                ],
            }
        )
        assert result.equals(expected_result)

    def test_no_files_with_given_prefix(
        self, mocker, prefix_does_not_exist_flow_args, empty_params
    ):

        self.init_task(mocker=mocker)
        task = FetchAndMergeTextFiles(name="test")
        with pytest.raises(SKIP):
            task.execute(flow_args=prefix_does_not_exist_flow_args, params=empty_params)

    def init_task(self, mocker):

        fake_bucket_name = "test.dev.steeleye.co"

        if not self.task_was_initialized:
            mock_s3_instance = TestFetchAndMergeTextFilesMockS3()
            mock_s3_instance.create_mock_bucket(bucket=fake_bucket_name)
            mock_s3_instance.load_data_into_mock_s3()

        self.task_was_initialized = True

        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = fake_bucket_name


class TestFetchAndMergeTextFilesMockS3(AbstractMockS3, ABC):
    def load_data_into_mock_s3(self):

        for i in range(4):

            file_name = f"test_file_{i}.txt"
            data = f"crazy kiwi {i}"

            self.s3.put_object(
                Bucket=self.bucket,
                Key=f"ingress/raw/flow-name/{DATE_VALUE}/{file_name}",
                Body=data,
            )

        # add batch file
        self.s3.put_object(
            Bucket=self.bucket,
            Key=f"ingress/raw/flow-name/{DATE_VALUE}/test_file_batch.txt",
            Body="crazy kiwi batch 1\ncrazy kiwi batch 2\n",
        )
