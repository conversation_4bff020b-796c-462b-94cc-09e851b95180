import datetime

import pytest
from pydantic import ValidationError

from swarm_tasks.io.read.aws.copy import s3_to_s3_copy_prefix


@pytest.fixture
def default_params():
    return s3_to_s3_copy_prefix.Params(
        **{
            "s3_target_tenant": "target_tenant",
            "s3_source_folder": "source_folder/",
            "file_prefix": "dummy_prefix",
            "override_if_exists": True,
        }
    )


class MockBoto3:
    class get_paginator:
        def __init__(self, function):
            self.function = function

        def paginate(self, Bucket, Prefix):
            if (
                Bucket == "dummy.dev.steeleye.co"
                or Bucket == "target_tenant.dev.steeleye.co"
            ):
                return [
                    {
                        "Contents": [
                            {
                                "Key": "folder1/dummy_test_key.csv",
                                "LastModified": datetime.datetime.now(),
                            },
                            {
                                "Key": "folder2/dummy_test_key.csv",
                                "LastModified": datetime.datetime.now(),
                            },
                        ]
                    }
                ]
            else:
                return [{}]

    def keys(self):
        return ["Contents"]

    def list_objects(self, <PERSON><PERSON>, Prefix):
        if Bucket == "dummy.dev.steeleye.co":
            return {
                "Contents": [
                    {
                        "Key": "folder1/dummy_test_key.csv",
                        "LastModified": datetime.datetime.now(),
                    },
                    {
                        "Key": "folder2/dummy_test_key.csv",
                        "LastModified": datetime.datetime.now(),
                    },
                ]
            }
        elif Bucket == "target_tenant.dev.steeleye.co":
            return {
                "Contents": [
                    {
                        "Key": "folder1/dummy_test_key.csv",
                        "LastModified": datetime.datetime.now(),
                    },
                    {
                        "Key": "folder2/dummy_test_key.csv",
                        "LastModified": datetime.datetime.now(),
                    },
                ]
            }
        else:
            return {}


class TestS3ToS3CopyPrefix(object):
    """
    Test cases for "S3ToS3CopyPrefix" class
    """

    def test_initialization(self, default_params):
        s3_to_s3_copy_prefix.S3ToS3CopyPrefix(name="TestCopy", params=default_params)

    def test_raises_with_empty_params(self):
        with pytest.raises(ValidationError) as e:
            s3_to_s3_copy_prefix.Params()
        assert (
            str(e.value)
            == "1 validation error for Params\n__root__\n  s3_target_tenant, "
            "s3_source_folder and the file_prefix params should be populated (type=value_error)"
        )

    def test_regular_get_files_to_copy(self, default_params):
        task = s3_to_s3_copy_prefix.S3ToS3CopyPrefix(
            name="test-s3-copy-to-s3", params=default_params
        )

        fake_boto3 = MockBoto3()
        result = task._get_files_to_copy(
            s3_client=fake_boto3,
            s3_source_bucket="dummy.dev.steeleye.co",
            s3_source_folder=default_params.s3_source_folder,
            s3_target_bucket="target_tenant.dev.steeleye.co",
            prefix_file=default_params.file_prefix,
            params=default_params,
        )
        assert result == ["folder1/dummy_test_key.csv", "folder2/dummy_test_key.csv"]

    def test_error_get_files_to_copy(self, default_params):
        task = s3_to_s3_copy_prefix.S3ToS3CopyPrefix(
            name="test-s3-copy-to-s3", params=default_params
        )

        fake_boto3 = MockBoto3()
        result = task._get_files_to_copy(
            s3_client=fake_boto3,
            s3_source_bucket="error_dummy.dev.steeleye.co",
            s3_source_folder=default_params.s3_source_folder,
            s3_target_bucket="target_tenant.dev.steeleye.co",
            prefix_file=default_params.file_prefix,
            params=default_params,
        )
        assert not result
