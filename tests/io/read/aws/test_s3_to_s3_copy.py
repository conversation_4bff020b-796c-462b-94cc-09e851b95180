from pathlib import Path
from typing import NoReturn
from urllib.parse import urlparse

import boto3
import pytest
from moto import mock_aws
from prefect.engine import signals
from swarm.conf import SettingsCls

from swarm_tasks.io.read.aws.copy import s3_to_s3_copy

DATA_PATH = Path(__file__).parent.joinpath("data")
S3_PATH_REGEX_TEST = (
    "s3://test.dev.steeleye.co/flows/order-feed-aladdin/"
    "AladdinTCA.cfs_EQ.Broker.20231109.csv"
)
S3_PATH_REGEX_TEST_NO_MATCH = (
    "s3://test.dev.steeleye.co/flows/"
    "order-feed-aladdin/AladdinTCA.cfs_EQ.Employee.20231109.csv"
)


@pytest.fixture
def url_params():
    return s3_to_s3_copy.Params(
        **{"s3_target_tenant": "dummy2", "s3_target_folder": "target_folder/"}
    )


@pytest.fixture
def only_params():
    return s3_to_s3_copy.Params(
        **{
            "s3_source_tenant": "dummy1",
            "s3_source_key": "folder/file.csv",
            "s3_target_tenant": "dummy2",
            "s3_target_folder": "target_folder/",
        }
    )


@pytest.fixture
def file_url():
    return "s3://dummy_url.dev.steeleye.co/folder/file.csv"


class TestS3ToS3Copy(object):
    """
    Test cases for "S3ToS3Copy" class
    """

    def test_initialization(self, url_params):
        s3_to_s3_copy.S3ToS3Copy(name="TestCopy", params=url_params)

    def test_run_with_file_url(self, mocker, url_params, file_url):
        task = s3_to_s3_copy.S3ToS3Copy(name="TestCopy", params=url_params)

        # mock realm and env from swarm.conf:SettingsCls
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "dummy1.dev.steeleye.co"
        mock_env = mocker.patch.object(
            SettingsCls, "env", new_callable=mocker.PropertyMock
        )
        mock_env.return_value = "dev"

        data_dict = task.get_parameters_to_process(url_params, file_url)

        assert data_dict.source_bucket == "dummy_url.dev.steeleye.co"
        assert data_dict.source_key == "folder/file.csv"
        assert data_dict.target_bucket == "dummy2.dev.steeleye.co"
        assert data_dict.target_key == "target_folder/file.csv"

    def test_run_file_url_no_folder(self, mocker, url_params, file_url):
        task = s3_to_s3_copy.S3ToS3Copy(name="TestCopy", params=url_params)

        # mock realm and env from swarm.conf:SettingsCls
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "dummy1.dev.steeleye.co"
        mock_env = mocker.patch.object(
            SettingsCls, "env", new_callable=mocker.PropertyMock
        )
        mock_env.return_value = "dev"

        url_params.s3_target_folder = None
        data_dict = task.get_parameters_to_process(url_params, file_url)

        assert data_dict.source_bucket == "dummy_url.dev.steeleye.co"
        assert data_dict.source_key == "folder/file.csv"
        assert data_dict.target_bucket == "dummy2.dev.steeleye.co"
        assert data_dict.target_key == "folder/file.csv"

    def test_run_file_url_no_tenant(self, mocker, url_params, file_url):
        task = s3_to_s3_copy.S3ToS3Copy(name="TestCopy", params=url_params)

        # mock realm and env from swarm.conf:SettingsCls
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "dummy1.dev.steeleye.co"
        mock_env = mocker.patch.object(
            SettingsCls, "env", new_callable=mocker.PropertyMock
        )
        mock_env.return_value = "dev"

        url_params.s3_target_tenant = None
        data_dict = task.get_parameters_to_process(url_params, file_url)

        assert data_dict.source_bucket == "dummy_url.dev.steeleye.co"
        assert data_dict.source_key == "folder/file.csv"
        assert data_dict.target_bucket == "dummy_url.dev.steeleye.co"
        assert data_dict.target_key == "target_folder/file.csv"

    def test_run_with_only_params(self, mocker, only_params):
        task = s3_to_s3_copy.S3ToS3Copy(name="TestCopy", params=only_params)

        # mock realm and env from swarm.conf:SettingsCls
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "dummy1.dev.steeleye.co"
        mock_env = mocker.patch.object(
            SettingsCls, "env", new_callable=mocker.PropertyMock
        )
        mock_env.return_value = "dev"

        data_dict = task.get_parameters_to_process(only_params)

        assert data_dict.source_bucket == "dummy1.dev.steeleye.co"
        assert data_dict.source_key == "folder/file.csv"
        assert data_dict.target_bucket == "dummy2.dev.steeleye.co"
        assert data_dict.target_key == "target_folder/file.csv"

    @mock_aws
    def test_copy_with_regex_param_matched(self, mocker):
        """
        Test for the case where we pass a regex, and it matches
        the input file. The expectation is that the file will
        be copied
        :param mocker:
        :return:
        """
        create_and_add_objects_to_s3_bucket()
        params_with_regex = s3_to_s3_copy.Params(
            **{
                "s3_target_folder": "flows/mymarket-aladdin-broker-firm/",
                "regex_for_file_match": ".*\\.Broker\\..*",
            }
        )
        task = s3_to_s3_copy.S3ToS3Copy(name="TestCopy", params=params_with_regex)
        # mock realm and env from swarm.conf:SettingsCls
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "test.dev.steeleye.co"
        mock_env = mocker.patch.object(
            SettingsCls, "env", new_callable=mocker.PropertyMock
        )
        mock_env.return_value = "dev"
        task.execute(params=params_with_regex, file_url=S3_PATH_REGEX_TEST)
        # Get head object of source file and copied (target) file
        s3 = boto3.client("s3")
        source_head = s3.head_object(
            Bucket="test.dev.steeleye.co",
            Key="flows/order-feed-aladdin/AladdinTCA.cfs_EQ.Broker.20231109.csv",
        )
        target_head = s3.head_object(
            Bucket="test.dev.steeleye.co",
            Key="flows/mymarket-aladdin-broker-firm/AladdinTCA.cfs_EQ.Broker.20231109.csv",
        )
        assert source_head["ContentLength"] == target_head["ContentLength"]

    @mock_aws
    def test_copy_with_regex_param_unmatched(self, mocker):
        """
        Test for the case where we pass a regex, but it doesn't match
        the input file. The expectation is that the task will be skipped.
        :param mocker:
        :return:
        """
        create_and_add_objects_to_s3_bucket()
        params_with_regex = s3_to_s3_copy.Params(
            **{
                "s3_target_folder": "flows/mymarket-aladdin-broker-firm/",
                "regex_for_file_match": ".*\\.Broker\\..*",
            }
        )
        task = s3_to_s3_copy.S3ToS3Copy(name="TestCopy", params=params_with_regex)
        # mock realm and env from swarm.conf:SettingsCls
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "test.dev.steeleye.co"
        mock_env = mocker.patch.object(
            SettingsCls, "env", new_callable=mocker.PropertyMock
        )
        mock_env.return_value = "dev"
        with pytest.raises(signals.SKIP):
            task.execute(params=params_with_regex, file_url=S3_PATH_REGEX_TEST_NO_MATCH)


def create_and_add_objects_to_s3_bucket() -> NoReturn:
    """
    Create mock S3 bucket and add a file to it.

    :return: None
    """

    # Create bucket
    s3 = boto3.client("s3", region_name="us-east-1")
    parsed_url = urlparse(S3_PATH_REGEX_TEST)
    s3_bucket = parsed_url.netloc
    # [1:] to remove leading slash from parsed_url.path
    s3_key = parsed_url.path[1:]
    input_file_name = Path(s3_key).name
    s3.create_bucket(Bucket=s3_bucket)
    input_file = DATA_PATH.joinpath(input_file_name).as_posix()

    with open(input_file, "rb") as f:
        s3.put_object(
            Bucket=s3_bucket,
            Key=s3_key,
            Body=f.read(),
        )
