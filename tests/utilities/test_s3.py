import datetime

from botocore.exceptions import ClientError

from swarm_tasks.utilities import s3


def test_get_last_modified_ts(mocker):

    response = {
        "ResponseMetadata": {
            "RequestId": "234X5FB9JXEMAJ2Y",
            "HTTPStatusCode": 200,
            "HTTPHeaders": {
                "content-type": "application/zip",
                "server": "AmazonS3",
                "content-length": "1665071",
            },
            "RetryAttempts": 0,
        },
        "AcceptRanges": "bytes",
        "LastModified": datetime.datetime(2022, 4, 11, 15, 38, 24),
        "ContentLength": 1665071,
        "Metadata": {},
    }
    mock_s3_head_object = mocker.patch.object(s3, "boto3")
    mock_s3_head_object.client().head_object.side_effect = [
        ClientError(
            error_response={"Error": {"Code": "NoSuchFile"}},
            operation_name="HeadObjects",
        ),
        response,
    ]

    time_stamp = s3.get_last_modified_ts(
        s3_bucket="test.steeleye.co", s3_key="path/file_name.extension"
    )
    assert time_stamp == response.get("LastModified")
