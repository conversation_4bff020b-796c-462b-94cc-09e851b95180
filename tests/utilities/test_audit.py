import pandas as pd
import pytest
from pydantic.error_wrappers import ValidationError as PydanticValidationError
from se_core_tasks.core.auditor import parse_audit_messages
from se_elastic_schema.models import Order
from swarm.schema.task.auditor.model import AGGREGATION_DELIMITER
from swarm.task.auditor import Auditor
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.utilities.audit import audit_pydantic_validation_errors


@pytest.fixture
def auditor() -> Auditor:
    return Auditor(task_name="DummyTask")


@pytest.fixture
def record() -> dict:
    return {
        "buySell": "SELL",
        "date": "2022-04-29",
        "executionDetails": {
            "buySellIndicator": "SELL",
            "orderStatus": "NEWO",
        },
        "id": "8210424527490|21436",
        "orderIdentifiers": {
            "internalOrderIdCode": "1650823225557",
            "orderIdCode": "8210424527490|21436",
        },
        "timestamps": {
            "orderReceived": "2022-04-29T06:48:54.669000Z",
            "orderSubmitted": "2022-04-29T06:48:54.669000Z",
            "tradingDateTime": "2022-04-29T06:48:54.669000Z",
        },
        "traderFileIdentifier": "id:glane",
        "transactionDetails": {
            "buySellIndicator": "SELL",
            "tradingDateTime": "2022-04-29T06:48:54.669000Z",
        },
        "some_extra_field": "not_allowed",
        "sourceIndex": "999",
    }


class DummyTask(TransformBaseTask):
    def execute(
        self,
        record: dict,
        source_frame: pd.DataFrame = None,
        auditor: Auditor = None,
        **kwargs,
    ) -> pd.DataFrame:
        record_model = Order
        try:
            record_model(**record)
        except PydanticValidationError as error:
            audit_pydantic_validation_errors(
                record=record,
                error=error,
                raw_index=1,
                auditor=auditor,
                logger=self.logger,
            )
        return source_frame


def test_audit_pydantic_validation_errors(record, auditor):
    task = DummyTask("DummyTask")
    task.execute(source_frame=pd.DataFrame(), auditor=auditor, record=record)
    print(auditor.to_dataframe().loc[0, "message"])
    assert (
        parse_audit_messages(auditor.to_dataframe().loc[0, "message"])
        == "An error occurred during validation of a record"
    )
    assert (
        parse_audit_messages(auditor.to_dataframe().loc[0, "ctx.error"])
        == f"Order {AGGREGATION_DELIMITER} "
        f"['some_extra_field - extra fields not permitted'] {AGGREGATION_DELIMITER} id: 8210424527490|21436"
    )


def test_audit_pydantic_validation_errors_when_id_props_not_present(record, auditor):
    task = DummyTask("DummyTask")
    non_id_prop_record = record.copy()
    del non_id_prop_record["id"]

    task.execute(
        source_frame=pd.DataFrame(), auditor=auditor, record=non_id_prop_record
    )
    print(auditor.to_dataframe().loc[0, "message"])
    assert (
        parse_audit_messages(auditor.to_dataframe().loc[0, "message"])
        == "An error occurred during validation of a record"
    )
    assert (
        parse_audit_messages(auditor.to_dataframe().loc[0, "ctx.error"])
        == f"Order {AGGREGATION_DELIMITER} "
        f"['id - field required', 'some_extra_field - extra fields not permitted'] {AGGREGATION_DELIMITER} "
        f"Index: 999"
    )
