import addict
import pytest

from swarm_tasks.utilities.es.query_utils import get_terms_query
from swarm_tasks.utilities.es.query_utils import get_terms_query_multiple_lookup_fields


@pytest.fixture()
def expected_result_single_terms():
    query = {
        "query": {
            "bool": {
                "filter": [{"term": {"&model": "Attachment"}}],
                "minimum_should_match": 1,
                "must_not": {"exists": {"field": "&expiry"}},
                "should": [{"terms": {"xyz": ["1", "2", "3", "4", "5"]}}],
            }
        }
    }
    return query


@pytest.fixture()
def expected_result_single_terms_with_source():
    query = {
        "query": {
            "bool": {
                "filter": [{"term": {"&model": "Attachment"}}],
                "minimum_should_match": 1,
                "must_not": {"exists": {"field": "&expiry"}},
                "should": [
                    {"terms": {"xyz": ["1", "2", "3", "4", "5", "6"]}},
                    {"terms": {"xyz": ["7"]}},
                ],
            }
        },
        "_source": "abc",
    }
    return query


@pytest.fixture()
def expected_result_multiple_terms():
    query = {
        "query": {
            "bool": {
                "filter": [{"term": {"&model": "Attachment"}}],
                "minimum_should_match": 1,
                "must_not": {"exists": {"field": "&expiry"}},
                "should": [
                    {"terms": {"xyz": ["1", "2", "3", "4", "5", "6"]}},
                    {"terms": {"xyz": ["7"]}},
                ],
            }
        }
    }
    return query


@pytest.fixture()
def expected_result_multiple_terms_with_source():
    query = {
        "query": {
            "bool": {
                "filter": [{"term": {"&model": "Attachment"}}],
                "minimum_should_match": 1,
                "must_not": {"exists": {"field": "&expiry"}},
                "should": [
                    {"terms": {"xyz": ["1", "2", "3", "4", "5", "6"]}},
                    {"terms": {"xyz": ["7"]}},
                ],
            }
        },
        "_source": "abc",
    }
    return query


@pytest.fixture()
def expected_result_multiple_terms_with_multiple_source_fields():
    query = {
        "query": {
            "bool": {
                "filter": [{"term": {"&model": "Attachment"}}],
                "minimum_should_match": 1,
                "must_not": {"exists": {"field": "&expiry"}},
                "should": [
                    {"terms": {"xyz": ["1", "2", "3", "4", "5", "6"]}},
                    {"terms": {"xyz": ["7"]}},
                ],
            }
        },
        "_source": ["abc", "def"],
    }
    return query


@pytest.fixture()
def expected_result_multiple_terms_multiple_lookup_fields():
    return {
        "query": {
            "bool": {
                "filter": [{"term": {"&model": "Attachment"}}],
                "minimum_should_match": 1,
                "must_not": {"exists": {"field": "&expiry"}},
                "should": [
                    {"terms": {"xyz": ["1", "2", "3", "4", "5", "6"]}},
                    {"terms": {"abc": ["1", "2", "3", "4", "5", "6"]}},
                    {"terms": {"xyz": ["7"]}},
                    {"terms": {"abc": ["7"]}},
                ],
            }
        }
    }


@pytest.fixture()
def expected_result_single_terms_multiple_lookup_fields():
    return {
        "query": {
            "bool": {
                "filter": [{"term": {"&model": "Attachment"}}],
                "minimum_should_match": 1,
                "must_not": {"exists": {"field": "&expiry"}},
                "should": [
                    {"terms": {"xyz": ["1", "2", "3", "4", "5"]}},
                    {"terms": {"abc": ["1", "2", "3", "4", "5"]}},
                ],
            }
        }
    }


class TestESQueryUtils:
    """
    Test cases for Elasticsearch query utils
    """

    def test_get_terms_query_single_terms(self, expected_result_single_terms):
        # Simulate the es_client param with MAX_TERMS_SIZE = 6
        es_client = addict.Dict({"MAX_TERMS_SIZE": 6})
        result = get_terms_query(
            ids=["1", "2", "3", "4", "5"],
            es_client=es_client,
            lookup_field="xyz",
            model_field="Attachment",
        )
        assert result == expected_result_single_terms

    def test_get_terms_query_single_terms_with_source(
        self, expected_result_single_terms_with_source
    ):
        # Simulate the es_client param with MAX_TERMS_SIZE = 6
        es_client = addict.Dict({"MAX_TERMS_SIZE": 6})
        result = get_terms_query(
            ids=["1", "2", "3", "4", "5", "6", "7"],
            es_client=es_client,
            lookup_field="xyz",
            model_field="Attachment",
            source_field="abc",
        )
        assert result == expected_result_single_terms_with_source

    def test_get_terms_query_multiple_terms(self, expected_result_multiple_terms):
        # Simulate the es_client param with MAX_TERMS_SIZE = 6
        es_client = addict.Dict({"MAX_TERMS_SIZE": 6})
        result = get_terms_query(
            ids=["1", "2", "3", "4", "5", "6", "7"],
            es_client=es_client,
            lookup_field="xyz",
            model_field="Attachment",
        )
        assert result == expected_result_multiple_terms

    def test_get_terms_query_multiple_terms_with_source(
        self, expected_result_multiple_terms_with_source
    ):
        # Simulate the es_client param with MAX_TERMS_SIZE = 6
        es_client = addict.Dict({"MAX_TERMS_SIZE": 6})
        result = get_terms_query(
            ids=["1", "2", "3", "4", "5", "6", "7"],
            es_client=es_client,
            lookup_field="xyz",
            model_field="Attachment",
            source_field="abc",
        )
        assert result == expected_result_multiple_terms_with_source

    def test_get_terms_query_multiple_terms_with_multiple_source_fields(
        self, expected_result_multiple_terms_with_multiple_source_fields
    ):
        # Simulate the es_client param with MAX_TERMS_SIZE = 6
        es_client = addict.Dict({"MAX_TERMS_SIZE": 6})
        result = get_terms_query(
            ids=["1", "2", "3", "4", "5", "6", "7"],
            es_client=es_client,
            lookup_field="xyz",
            model_field="Attachment",
            source_field=["abc", "def"],
        )
        assert result == expected_result_multiple_terms_with_multiple_source_fields

    def test_get_single_terms_multiple_lookup_fields(
        self, expected_result_single_terms_multiple_lookup_fields
    ):
        """Tests get_terms_query_multiple_lookup_fields when len(ids) < chunksize"""
        es_client = addict.Dict({"MAX_TERMS_SIZE": 6})
        result = get_terms_query_multiple_lookup_fields(
            ids=["1", "2", "3", "4", "5"],
            es_client=es_client,
            lookup_fields=["xyz", "abc"],
            model_field="Attachment",
        )
        assert result == expected_result_single_terms_multiple_lookup_fields

    def test_get_multiple_terms_multiple_lookup_fields(
        self, expected_result_multiple_terms_multiple_lookup_fields
    ):
        """Tests get_terms_query_multiple_lookup_fields when there are multiple terms (i.e., len(ids) > chunksize)"""
        es_client = addict.Dict({"MAX_TERMS_SIZE": 6})
        result = get_terms_query_multiple_lookup_fields(
            ids=["1", "2", "3", "4", "5", "6", "7"],
            es_client=es_client,
            lookup_fields=["xyz", "abc"],
            model_field="Attachment",
        )
        assert result == expected_result_multiple_terms_multiple_lookup_fields
