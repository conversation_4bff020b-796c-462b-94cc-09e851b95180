from swarm_tasks.utilities.string_util import string


class TestStringUtil:
    def test_string_bytes_japanese_input(self):
        """Tests the string method for bytes input"""
        iso_2022_jp_bytes = b"\x1b$B%1%S%s\x1b(B"
        expected_result = "ケビン"
        result = string(obj=iso_2022_jp_bytes, encoding="iso-2022-jp")
        assert result == expected_result

    def test_string_utf8_french_german_mixed_input(self):
        """Tests the string method for bytes input"""
        french_german_mix_bytes = b"l\xc3\xb6sung pr\xc3\xa9f\xc3\xa9r\xc3\xa9"
        expected_result = "lösung préféré"
        # encoding=None. chardet defaults to utf-8
        result = string(obj=french_german_mix_bytes)
        assert result == expected_result

    def test_string_int_input(self):
        """Tests the string method for bytes input"""
        int_value = 13
        expected_result = "13"
        assert string(int_value) == expected_result
