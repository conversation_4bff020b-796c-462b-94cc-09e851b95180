from swarm_tasks.utilities.decimal import enforce_significant_figures


class TestDecimal(object):
    def test_no_amends(self):
        expected = "123.456"
        actual = enforce_significant_figures(value=expected, precision=6, scale=3)
        assert actual == expected

    def test_less_precision(self):
        expected = "123.456"
        actual = enforce_significant_figures(value=expected, precision=5, scale=3)
        assert actual == expected[:-1]

    def test_less_scale(self):
        expected = "123.456"
        actual = enforce_significant_figures(value=expected, precision=6, scale=2)
        assert actual == expected[:-1]

    def test_whole_number(self):
        expected = "123456"
        actual = enforce_significant_figures(value=expected, precision=6, scale=2)
        assert actual == expected

    def test_whole_number_less_precision(self):
        expected = "123456"
        actual = enforce_significant_figures(value=expected, precision=5, scale=2)
        assert actual == expected[:-1]

    def test_whole_number_large_precision_and_large_scale(self):
        input_val = "1"
        actual = enforce_significant_figures(value=input_val, precision=10, scale=9)
        assert actual == "1.00"

    def test_fractional_number_large_precision_and_large_scale(self):
        input_val = "1.1122334455667788"
        actual = enforce_significant_figures(value=input_val, precision=10, scale=9)
        assert actual == "1.112233445"
