import pandas as pd
import pytest

from swarm_tasks.utilities.market.instruments.instrument_utils import (
    get_expiry_date_from_desc,
)
from swarm_tasks.utilities.market.instruments.instrument_utils import (
    get_option_type_from_desc,
)
from swarm_tasks.utilities.market.instruments.instrument_utils import (
    get_strike_price_from_desc,
)
from swarm_tasks.utilities.market.instruments.instrument_utils import (
    get_symbol_from_desc,
)


class TempCols:
    TRADE_DATE_COLUMN = "__trade_date__"
    DESC_COLUMN = "__desc__"
    ASSET_CLASS_COLUMN = "__asset_class__"
    TARGET_EXPIRY_DATE = "__target_expiry_date__"
    TARGET_SYMBOL = "__target_symbol__"
    TARGET_OPTION_TYPE = "__target_option_type__"
    TARGET_STRIKE_PRICE = "__target_strike_price__"


@pytest.fixture()
def input_for_expiry_date() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            TempCols.TRADE_DATE_COLUMN: [
                "2021-01-01",
                "2021-01-01",
                "2023-06-17",
                "2021-01-01",
                "2021-01-01",
                pd.NA,
            ],
            TempCols.DESC_COLUMN: [
                "GDF1",
                "ZK3",
                "ZK3",
                "Long text with no context",
                pd.NA,
                "ABCDE EFGHI",
            ],
        }
    )
    df = df.assign(
        **{TempCols.TRADE_DATE_COLUMN: pd.to_datetime(df[TempCols.TRADE_DATE_COLUMN])}
    )
    return df


@pytest.fixture()
def input_for_symbol() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            TempCols.DESC_COLUMN: [
                "GDF1",
                "ZK3",
                "HGZ31",
                "Long text with no context",
                pd.NA,
                "ABCDE EFGHI",
            ],
        }
    )
    return df


@pytest.fixture()
def expected_result_for_expiry_date() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            TempCols.TARGET_EXPIRY_DATE: [
                "2021-01-01",
                "2023-05-01",
                "2033-05-01",
                pd.NA,
                pd.NA,
                pd.NA,
            ]
        }
    )
    return df


@pytest.fixture()
def expected_result_symbol() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            TempCols.TARGET_SYMBOL: [
                "GD",
                "Z",
                "HGZ",
                pd.NA,
                pd.NA,
                pd.NA,
            ]
        }
    )
    return df


@pytest.fixture()
def input_for_option_type_and_strike_price() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            TempCols.DESC_COLUMN: [
                "OZBH2 C1710",
                "OZBH2 P1710",
                "OZBH2",
                "OZBH2 P1710",
            ],
            TempCols.ASSET_CLASS_COLUMN: ["option", "option", "option", "x"],
        }
    )
    return df


@pytest.fixture()
def expected_result_option_type() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            TempCols.TARGET_SYMBOL: [
                "CALL",
                "PUT",
                pd.NA,
                pd.NA,
            ]
        }
    )
    return df


@pytest.fixture()
def expected_result_strike_price() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            TempCols.TARGET_STRIKE_PRICE: [
                "1710",
                "1710",
                pd.NA,
                pd.NA,
            ]
        }
    )
    return df


class TestInstrumentUtilities:
    """
    Tests functionalities defined in instrument_utils
    """

    def test_instrument_expiry_date_logic(
        self,
        input_for_expiry_date,
        expected_result_for_expiry_date,
    ):
        result = get_expiry_date_from_desc(
            data=input_for_expiry_date,
            trade_date_column=TempCols.TRADE_DATE_COLUMN,
            desc_column=TempCols.DESC_COLUMN,
            target_expiry_date=TempCols.TARGET_EXPIRY_DATE,
        )

        assert not pd.testing.assert_frame_equal(
            result.fillna(pd.NA), expected_result_for_expiry_date
        )

    def test_instrument_symbol_logic(
        self,
        input_for_symbol,
        expected_result_symbol,
    ):
        result = get_symbol_from_desc(
            data=input_for_symbol,
            desc_column=TempCols.DESC_COLUMN,
            target_symbol=TempCols.TARGET_SYMBOL,
        )

        assert not pd.testing.assert_frame_equal(
            result.fillna(pd.NA), expected_result_symbol
        )

    def test_instrument_option_type_logic(
        self,
        input_for_option_type_and_strike_price,
        expected_result_option_type,
    ):
        result = get_option_type_from_desc(
            data=input_for_option_type_and_strike_price,
            desc_column=TempCols.DESC_COLUMN,
            asset_class_column=TempCols.ASSET_CLASS_COLUMN,
            target_option=TempCols.TARGET_SYMBOL,
        )

        assert not pd.testing.assert_frame_equal(
            result.fillna(pd.NA), expected_result_option_type
        )

    def test_instrument_strike_price_logic(
        self,
        input_for_option_type_and_strike_price,
        expected_result_strike_price,
    ):
        result = get_strike_price_from_desc(
            data=input_for_option_type_and_strike_price,
            desc_column=TempCols.DESC_COLUMN,
            asset_class_column=TempCols.ASSET_CLASS_COLUMN,
            target_strike_price=TempCols.TARGET_STRIKE_PRICE,
        )

        assert not pd.testing.assert_frame_equal(
            result.fillna(pd.NA), expected_result_strike_price
        )
