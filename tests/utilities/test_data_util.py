import csv
import logging
from pathlib import Path

import pandas as pd
import pytest
import pytz
from se_core_tasks.utils.data_manipulation import csv_has_header
from se_core_tasks.utils.data_manipulation import fetch_column_names_from_csv
from se_trades_tasks.order_and_tr.instrument.link.static import InstrumentField

from swarm_tasks.tr.feed.ice.fix.ice_fix_generate_trades import AuxiliaryData
from swarm_tasks.utilities.data_util import dtm
from swarm_tasks.utilities.data_util import formatted_date_time_string
from swarm_tasks.utilities.data_util import frame_categorical_sort

logger = logging.getLogger(__name__)
TEST_FILE_PATH = Path(__file__).parent.joinpath("data")
ONLY_HEADER_PATH = Path.joinpath(TEST_FILE_PATH, "only_header.csv")
NO_HEADER_PATH = Path.joinpath(TEST_FILE_PATH, "no_header.csv")
WITH_HEADER_PATH = Path.joinpath(TEST_FILE_PATH, "with_header.csv")
EMPTY_FILE_WITH_NEWLINES_PATH = Path.joinpath(
    TEST_FILE_PATH, "empty-file-with-newline-chars.csv"
)


class TestDataUtil:
    """Test suite for data_util.py"""

    def test_csv_has_header_for_file_with_header(self):
        result = csv_has_header(csv_file_path=WITH_HEADER_PATH)
        assert result is True

    def test_csv_has_header_for_file_without_header(self):
        result = csv_has_header(csv_file_path=NO_HEADER_PATH)
        assert result is False

    def test_csv_has_header_for_empty_file(self):
        with pytest.raises(csv.Error):
            csv_has_header(csv_file_path=EMPTY_FILE_WITH_NEWLINES_PATH)

    def test_fetch_column_names_from_csv(self):
        result = fetch_column_names_from_csv(file_path=ONLY_HEADER_PATH)
        assert result == ["number", "year", "quantity", "price"]

    def test_frame_categorical_sort(
        self,
        source_instrument_df_sort_venue: pd.DataFrame,
    ) -> None:
        """Test method frame_categorical_sort"""
        result = frame_categorical_sort(
            df=source_instrument_df_sort_venue,
            sort_column=InstrumentField.TRADING_VENUE,
            sort_order=[
                "XLON",
                "ARCX",
                "FRAB",
                "BATS",
                "XPAR",
                "360T",
                "BGCO",
                "FXRQ",
                "GFBO",
            ],
            logger=logger,
        )
        result_instrument_df_sort_venue = source_instrument_df_sort_venue.reindex(
            [0, 4, 2, 1, 3, 5]
        )
        result_instrument_df_sort_venue.pop("TEMP_CATEGORICAL_COLUMN")
        assert result.equals(result_instrument_df_sort_venue)

    def test_frame_categorical_sort_exception(
        self,
        source_instrument_df_sort_venue: pd.DataFrame,
    ) -> None:
        """Test method _preferential_sort_venue"""
        source_instrument_df_sort_venue.pop(InstrumentField.TRADING_VENUE)
        result = frame_categorical_sort(
            df=source_instrument_df_sort_venue,
            sort_column=InstrumentField.TRADING_VENUE,
            sort_order=[
                "XLON",
                "ARCX",
                "FRAB",
                "BATS",
                "XPAR",
                "360T",
                "BGCO",
                "FXRQ",
                "GFBO",
            ],
            logger=logger,
        )
        assert result.equals(source_instrument_df_sort_venue)

    def test_dtm_returns_correct_datetimes_according_to_provided_timezones(
        self,
    ) -> None:
        input_datetime = "2022/06/1407:54:40.000000"
        expected_datetime = "2022/06/1412:54:40"
        target_fmt = "%Y/%m/%d%H:%M:%S"
        timezone = pytz.timezone("CST6CDT")
        source_format_list = ["%Y/%m/%d%H:%M:%S"]

        output_datetime = dtm(
            dt_str=input_datetime,
            dt_fmt=source_format_list,
            timezone=timezone,
            target_format=target_fmt,
        )
        assert output_datetime == expected_datetime

    def test_dtm_returns_correct_datetimes_if_timezone_naive(self) -> None:
        input_datetime = "2022/06/1407:54:40.000000"
        expected_datetime = "2022/06/1407:54:40"
        target_fmt = "%Y/%m/%d%H:%M:%S"
        source_format_list = ["%Y/%m/%d%H:%M:%S"]

        output_datetime = dtm(
            dt_str=input_datetime,
            dt_fmt=source_format_list,
            target_format=target_fmt,
        )
        assert output_datetime == expected_datetime

    def test_dtm_returns_correct_datetime_if_timezone_aware(self) -> None:
        input_datetime = "2022/06/1407:54:40.000000+01:00"
        expected_datetime = "2022/06/1406:54:40"
        target_fmt = "%Y/%m/%d%H:%M:%S"
        source_format_list = ["%Y/%m/%d%H:%M:%S.%f%z"]

        output_datetime = dtm(
            dt_str=input_datetime,
            dt_fmt=source_format_list,
            target_format=target_fmt,
        )
        assert output_datetime == expected_datetime

    def test_formatted_date_time_string(self) -> None:
        input_datetime = "2022/06/14 07:54:40.001230"
        expected_datetime_string = "54:40.001230"
        target_fmt = "%M:%S.%f"

        output_datetime_string = formatted_date_time_string(
            dt_str=input_datetime,
            dt_fmt=AuxiliaryData.YEAR_FIRST,
            target_fmt=target_fmt,
        )
        assert output_datetime_string == expected_datetime_string
