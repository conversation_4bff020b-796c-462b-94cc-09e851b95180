from datetime import datetime

import pandas as pd

from swarm.audit.flow.flow_auditor import AuditResult
from swarm.audit.result.elastic import ElasticResult
from swarm.audit.result.elastic import ModelStats
from swarm.audit.result.elastic import WriteCounts
from swarm.audit.result.task_audit import TaskAuditResult
from swarm.audit.sink.sink_file_auditor import SinkFileAuditor
from swarm.schema.task.auditor.model import AGGREGATION_DELIMITER


class MockEsRepo:
    class MockEsClient:
        @staticmethod
        def info():
            return {"cluster_name": "test", "version": {"number": "8.2.0"}}

    client = MockEsClient()


class TestSinkFileAudit:
    def test_on_processed(self, mocker):
        """
        Simple test to check if counts are being computed successfully
        """

        mock_es_client = mocker.patch(
            "swarm.audit.sink.sink_file_auditor.get_repository_by_cluster_version"
        )
        mock_es_client.return_value = MockEsRepo()
        mock_duration = mocker.patch.object(
            SinkFileAuditor,
            "calculate_duration",
        )
        mock_duration.return_value = ("1234", datetime.utcnow())

        mock_update = mocker.patch.object(
            SinkFileAuditor,
            "update_record",
        )
        mock_update.return_value = 0
        obj = SinkFileAuditor(config={}, tenant=None, audit_id="123")
        obj.on_processed(
            result=AuditResult(
                task_audit_result=TaskAuditResult(
                    errors=pd.DataFrame(
                        {
                            "task_name": ["A"],
                            "ctx.error": [""],
                            "ctx.error_count": [10],
                            "ctx.skip_count": [5],
                            "ctx.duplicate_count": [0],
                            "ctx.input_total_count": [20],
                        }
                    )
                ),
                elastic_result=ElasticResult(
                    model_stats=[ModelStats(model_name="ABC", counts=WriteCounts())],
                    total_counts=WriteCounts(created=5, duplicate=1),
                ),
                state="PROCESSED",
            )
        )
        assert mock_update.call_args_list[0][1].get("imported") == 5
        assert mock_update.call_args_list[0][1].get("errored") == 10
        assert mock_update.call_args_list[0][1].get("skipped") == 5
        assert mock_update.call_args_list[0][1].get("duplicate") == 1
        assert mock_update.call_args_list[0][1].get("iterable") == 20

    def test_on_processed_with_aggregations(self, mocker):
        """
        Simple test to check if counts are being computed successfully.
        Aggregates messages
        """
        mock_es_client = mocker.patch(
            "swarm.audit.sink.sink_file_auditor.get_repository_by_cluster_version"
        )
        mock_es_client.return_value = MockEsRepo()
        mock_duration = mocker.patch.object(
            SinkFileAuditor,
            "calculate_duration",
        )
        mock_duration.return_value = ("1234", datetime.utcnow())

        mock_update = mocker.patch.object(
            SinkFileAuditor,
            "update_record",
        )
        mock_update.return_value = 0
        obj = SinkFileAuditor(config={}, tenant=None, audit_id="123")
        obj.on_processed(
            result=AuditResult(
                task_audit_result=TaskAuditResult(
                    errors=pd.DataFrame(
                        {
                            "task_name": ["AssignMeta"],
                            "ctx.error": [
                                f"A {AGGREGATION_DELIMITER} B {AGGREGATION_DELIMITER} C"
                            ],
                            "message": ["Error happened"],
                            "ctx.error_count": [10],
                            "ctx.skip_count": [5],
                            "ctx.duplicate_count": [0],
                            "ctx.input_total_count": [20],
                        }
                    )
                ),
                elastic_result=ElasticResult(
                    model_stats=[ModelStats(model_name="ABC", counts=WriteCounts())],
                    total_counts=WriteCounts(created=5, duplicate=1),
                ),
                state="PROCESSED",
            )
        )
        assert mock_update.call_args_list[0][1].get("imported") == 5
        assert mock_update.call_args_list[0][1].get("errored") == 10
        assert mock_update.call_args_list[0][1].get("skipped") == 5
        assert mock_update.call_args_list[0][1].get("duplicate") == 1
        assert mock_update.call_args_list[0][1].get("iterable") == 20
        assert mock_update.call_args_list[0][1].get("errorClassStats") == [
            {"errorClass": "Error happened", "values": ["A : B : ['C']"]}
        ]

    def test_on_processed_without_certain_fields(self, mocker):
        """
        Simple test to check if counts are being computed successfully if certain fields are not present.
        """
        mock_es_client = mocker.patch(
            "swarm.audit.sink.sink_file_auditor.get_repository_by_cluster_version"
        )
        mock_es_client.return_value = MockEsRepo()
        mock_duration = mocker.patch.object(
            SinkFileAuditor,
            "calculate_duration",
        )
        mock_duration.return_value = ("1234", datetime.utcnow())

        mock_update = mocker.patch.object(
            SinkFileAuditor,
            "update_record",
        )
        mock_update.return_value = 0
        obj = SinkFileAuditor(config={}, tenant=None, audit_id="123")
        obj.on_processed(
            result=AuditResult(
                task_audit_result=TaskAuditResult(
                    errors=pd.DataFrame(
                        {
                            "task_name": ["AssignMeta"],
                        }
                    )
                ),
                elastic_result=ElasticResult(
                    model_stats=[ModelStats(model_name="ABC", counts=WriteCounts())],
                    total_counts=WriteCounts(created=5, duplicate=1),
                ),
                state="PROCESSED",
            )
        )
        assert mock_update.call_args_list[0][1].get("imported") == 5
        assert mock_update.call_args_list[0][1].get("errored") == 0
        assert mock_update.call_args_list[0][1].get("skipped") == 0
        assert mock_update.call_args_list[0][1].get("duplicate") == 1
        assert mock_update.call_args_list[0][1].get("iterable") == 0
