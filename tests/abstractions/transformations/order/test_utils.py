import pandas as pd
import pytest
from se_trades_tasks.abstractions.abstract_order_transformations import (
    generic_logic_for_timestamps_order_status_updated,
)
from se_trades_tasks.order.static import OrderColumns


@pytest.fixture()
def source_frame():
    data = {
        OrderColumns.TIMESTAMPS_ORDER_SUBMITTED: [
            "2022-01-01 12:30",
            pd.NA,
            pd.NA,
            "2022-04-01 12:30",
            "2022-05-03 12:30",
        ],
        OrderColumns.TIMESTAMPS_TRADING_DATE_TIME: [
            "2022-01-02 12:30",
            "2022-02-01 12:30",
            pd.NA,
            "2022-04-02 12:30",
            "2022-04-03 12:30",
        ],
        OrderColumns.TIMESTAMPS_ORDER_RECEIVED: [
            "2022-01-03 12:30",
            pd.NA,
            pd.NA,
            "2022-04-03 12:30",
            "2022-04-03 12:30",
        ],
        OrderColumns.EXECUTION_DETAILS_ORDER_STATUS: [
            "NEWO",
            pd.NA,
            "NEWO",
            "FILL",
            "CAME",
        ],
    }
    return pd.DataFrame(data)


class TestUtils:
    """
    Test for Utils
    """

    def test_order_status_updated(self, source_frame: pd.DataFrame):
        result = generic_logic_for_timestamps_order_status_updated(df=source_frame)
        expected_result = pd.DataFrame(index=source_frame.index)
        expected_result.loc[:, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED] = [
            "2022-01-03 12:30",
            "2022-02-01 12:30",
            pd.NA,
            "2022-04-02 12:30",
            "2022-05-03 12:30",
        ]
        assert result.equals(
            expected_result[OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED]
        )
