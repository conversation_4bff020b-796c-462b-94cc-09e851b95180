# Swarm Tasks releases

## v3.5.19.0 <Badge text="beta" type="success" />
Released on February 2, 2021.

- Update to swarm-sdk 3.5.19 for Flow Router changes

## v3.5.18.0 <Badge text="beta" type="success" />
Released on February 2, 2021.
### :boom: Features

- [DE-406] LinkInstrument: add FcaFirdsInstrument support - [#548](https://github.com/swarmhub/swarm-tasks/pull/548)
- Update Swarm SDK to 3.5.18 - [#561](https://github.com/swarmhub/swarm-tasks/pull/561)

### :muscle: Enhancements

- [DE-462] Refinitiv refactor out OrderAnalyticsPending - [#547](https://github.com/swarmhub/swarm-tasks/pull/547)

### :bug: Fixes

- [ON-1731] InstrumentIdentifiers Bug fix - [#542](https://github.com/swarmhub/swarm-tasks/pull/542)
- [ON-1874] Use unquote plus in attachment_from_s3_metadata - [#562](https://github.com/swarmhub/swarm-tasks/pull/562)

### :pray: Contributors

- [Diogo-B-Lima](https://github.com/Diogo-B-Lima)
- [Luis Braga](https://github.com/microft)
- [MaddieMclean](https://github.com/MaddieMclean)
- [Puneeth R](https://github.com/puneeth-r)

## v3.5.16.7 <Badge text="beta" type="success" />
Released on January 26, 2021.
### :boom: Features

- [ON-1745] dtrading mt4 html flow - [#468](https://github.com/swarmhub/swarm-tasks/pull/468)
- Add map from csv task - [#468](https://github.com/swarmhub/swarm-tasks/pull/468)
- Add generic task for converting a column into instrument identifiers - [#468](https://github.com/swarmhub/swarm-tasks/pull/468)

### :pray: Contributors

- [Maddie Mclean](https://github.com/MaddieMclean)

## v3.5.16.6 <Badge text="beta" type="success" />
Released on January 26, 2021.
### :muscle: Enhancements

- [ON-1867] Orders - Freetrade - Logic to populate OrderType - [#535](https://github.com/swarmhub/swarm-tasks/pull/535)

### :bug: Fixes

- [DE-455] Kyte Price Data force price to float - [#536](https://github.com/swarmhub/swarm-tasks/pull/536)

### :pray: Contributors

- [John Higgins](https://github.com/redking1)
- [Puneeth R](https://github.com/puneeth-r)

## v3.5.16.5 <Badge text="beta" type="success" />
Released on January 25, 2021.
### :boom: Features

- [ON-1861] Tweak bulkwriter to handle multiple models at once - [#530](https://github.com/swarmhub/swarm-tasks/pull/530)

### :pray: Contributors

- [MaddieMclean](https://github.com/MaddieMclean)

## v3.5.16.3 <Badge text="beta" type="success" />
Released on January 21, 2021.
### :boom: Features

- [ON-1782] Emir universal CME Add New IRIS fields - [#491](https://github.com/swarmhub/swarm-tasks/pull/491)
- [DE-444] Assign Meta Task Test for real data - [#515](https://github.com/swarmhub/swarm-tasks/pull/515)
- [ON-1789] Orders - FreeTrade LiquidMetrix [#517] (https://github.com/swarmhub/swarm-tasks/pull/517)
- [ON-1864] Update se-schema to 3.3.13 - [#522](https://github.com/swarmhub/swarm-tasks/pull/522)

### :muscle: Enhancements

- [DE-448] Refinitiv .ricLookup update coverage dates from S3 file - [#519](https://github.com/swarmhub/swarm-tasks/pull/519)
- [ON-1503] Selwood Transcription and Translation - [#453](https://github.com/swarmhub/swarm-tasks/pull/453)

### :bug: Fixes

- [ON-1864] InstrumentIdentifiers: Shenkman Ingestion Issues - [#518](https://github.com/swarmhub/swarm-tasks/pull/518)
- [DE-451] Changing market price depending on buySell & Audit Streamline [#529](https://github.com/swarmhub/swarm-tasks/pull/529)

### :pray: Contributors

- [Diogo-B-Lima](https://github.com/Diogo-B-Lima)
- [John Higgins](https://github.com/redking1)
- [Luis Braga](https://github.com/microft)
- [Puneeth R](https://github.com/puneeth-r)
- [Sandro Lourenco](https://github.com/lzefyrus)

## v3.5.16.2 <Badge text="beta" type="success" />
Released on January 18, 2021.
### :bug: Fixes

- [DE-443] OrderAnalytics Pending Drain Flow datetime comparison - [#512](https://github.com/swarmhub/swarm-tasks/pull/512)

### :pray: Contributors

- [Luis Braga](https://github.com/microft)

## v3.5.16.1 <Badge text="beta" type="success" />
Released on January 18, 2021.
### :bug: Fixes

- Fix es_client.search() response check in best_execution_fx_rates (https://github.com/swarmhub/swarm-tasks/pull/506)
- [PF-742] Fix `BroadestCoverageByPreferredRic` task & added `AssignPreferredRic` task - [#507](https://github.com/swarmhub/swarm-tasks/pull/507)
- [ON-1862] InstrumentFallback: fix instrumentIdCodeType in wrong field (https://github.com/swarmhub/swarm-tasks/pull/508
- [PF-742] BroadestCoverageByPreferredRic: handle None value in coverage - [#509](https://github.com/swarmhub/swarm-tasks/pull/509)

### :pray: Contributors

- [Diogo-B-Lima](https://github.com/Diogo-B-Lima)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.5.16.0 <Badge text="beta" type="success" />
Released on January 15, 2021.
### :bug: Fixes

- [ON-1580] Thornbridge Luna Third Financial trades - [#486](https://github.com/swarmhub/swarm-tasks/pull/486)
- [DE-436] TCA Update for data inconsistencies and Kafka Args correction [#498](https://github.com/swarmhub/swarm-tasks/pull/498)

### :pray: Contributors

- [Diogo-B-Lima](https://github.com/Diogo-B-Lima)
- [John Higgins](https://github.com/redking1)

## v3.5.13.2 <Badge text="beta" type="success" />
Released on January 15, 2021.
### :muscle: Enhancements

- [ON-1717, ON-1783, ON-1784] SE Blotter Orders Revision - [#480](https://github.com/swarmhub/swarm-tasks/pull/480)
- [DE-430] Emir Universal CME Validations fix - bump se-schema to 3.3.12- [#501](https://github.com/swarmhub/swarm-tasks/pull/501)

### :bug: Fixes

- [DE-434] Bulk-Transformer bug when handling multiple Models [#495](https://github.com/swarmhub/swarm-tasks/pull/495)

### :pray: Contributors

- [Diogo-B-Lima](https://github.com/Diogo-B-Lima)
- [John Higgins](https://github.com/redking1)
- [Puneeth R](https://github.com/puneeth-r)

## v3.5.13.1 <Badge text="beta" type="success" />
Released on January 11, 2021.
### :boom: Features

- [PF-462] FCA Firds Spider - [#489](https://github.com/swarmhub/swarm-tasks/pull/489)

### :muscle: Enhancements

- [SC-31] Feature market data tca metrics - [#276](https://github.com/swarmhub/swarm-tasks/pull/276)

### :bug: Fixes

- hotfix: se-dv-106 and Order Parties fields - [#487](https://github.com/swarmhub/swarm-tasks/pull/487)
- Added safety checks to eligibility assessor - [#488](https://github.com/swarmhub/swarm-tasks/pull/488)
- [ON-1825] Fix eligibility assessor and change mask logic to avoid nan values - [#490](https://github.com/swarmhub/swarm-tasks/pull/490)
- added update_changelog.py script- [#492](https://github.com/swarmhub/swarm-tasks/pull/492)

### :pray: Contributors

- [Diogo-B-Lima](https://github.com/Diogo-B-Lima)
- [John Higgins](https://github.com/redking1)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.5.13.0 <Badge text="beta" type="success" />
Released on January 5, 2021.
### :muscle: Enhancements

- added task for SQS message sending - [#478](https://github.com/swarmhub/swarm-tasks/pull/478)
- [PF-590] UnavistaResponseHandler: email notification upon response from unavista - [#479](https://github.com/swarmhub/swarm-tasks/pull/479)

### :bug: Fixes

- [ON-1707] TR CME MIFIR: party_fallback meta key - [#459](https://github.com/swarmhub/swarm-tasks/pull/459)

### :pray: Contributors

- [Diogo Lima](https://github.com/Diogo-B-Lima)
- [Luis Braga](https://github.com/microft)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.5.12.0 <Badge text="beta" type="success" />
Released on December 31, 2020.
### :muscle: Enhancements

- se-schema 3.3.4 - [#469](https://github.com/swarmhub/swarm-tasks/pull/469)
- Refactor Using SeBatchJobSubmitter from SDK - [#471](https://github.com/swarmhub/swarm-tasks/pull/471)
- Using new market data sdk 0.0.23 and adjusting parquet_days.py - [#476](https://github.com/swarmhub/swarm-tasks/pull/476)

### :bug: Fixes

- [SC-31, DE-305] Refinitiv Poller S3 Folder refactoring and bugfix - [#294](https://github.com/swarmhub/swarm-tasks/pull/294)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [John Higgins](https://github.com/redking1)
- [Luis Braga](https://github.com/microft)

## v3.5.11.1 <Badge text="beta" type="success" />
Released on December 22, 2020.
### :boom: Features

- [ON-1747][ON-1748] SE Blotter Orders Enhancements - [#462](https://github.com/swarmhub/swarm-tasks/pull/462)
- [ON-1131] assign_meta: Change meta id logic in the absence of ID_PROPERTIES - [#466](https://github.com/swarmhub/swarm-tasks/pull/466)

### :muscle: Enhancements

- [ON-1659] Schroders_brs/instrument_fallback: Change Strike Price Currency mapping - [#433](https://github.com/swarmhub/swarm-tasks/pull/433)
- [PF-544] Refinitiv - Backload should use Async [#464](https://github.com/swarmhub/swarm-tasks/pull/464)

### :bug: Fixes

- [DE-465] Eligibility Assessor: Accomodate cases when no ISIN in instrument - [#465](https://github.com/swarmhub/swarm-tasks/pull/465)
- [ON-1779] Orders Best-EX bug fix - [#470](https://github.com/swarmhub/swarm-tasks/pull/470)

### :pray: Contributors

- [Diogo Lima](https://github.com/Diogo-B-Lima)
- [Diogo-B-Lima](https://github.com/Diogo-B-Lima)
- [Luis Braga](https://github.com/microft)
- [Puneeth R](https://github.com/puneeth-r)

## v3.5.10.5 <Badge text="beta" type="success" />
Released on December 14, 2020.
### :boom: Features

- [ON-1665] bug fixes for mt4-html - [#460](https://github.com/swarmhub/swarm-tasks/pull/460)

### :muscle: Enhancements

- [ON-1695] TR CME MIFIR Workflow Status update in Instrument Fallback - [#440](https://github.com/swarmhub/swarm-tasks/pull/440)

### :bug: Fixes

- protections to handle ric not in .ricLookup - [#457](https://github.com/swarmhub/swarm-tasks/pull/457)

### :pray: Contributors

- [Luis Braga](https://github.com/microft)
- [MaddieMclean](https://github.com/MaddieMclean)
- [Puneeth R](https://github.com/puneeth-r)

## v3.5.10.4 <Badge text="beta" type="success" />
Released on December 9, 2020.
### :muscle: Enhancements

- [PF-449] added task to load broadest ricLookup coverage - [#455](https://github.com/swarmhub/swarm-tasks/pull/455)

### :pray: Contributors

- [Luis Braga](https://github.com/microft)

## v3.5.10.3 <Badge text="beta" type="success" />
Released on December 8, 2020.
### :muscle: Enhancements

- [PF-449] Update the preferredRic field in RicLookup with the preferred RIC - [#441](https://github.com/swarmhub/swarm-tasks/pull/441)
- Kafka - new format for the generic producer, with backwards compatibility - [#447](https://github.com/swarmhub/swarm-tasks/pull/447)
- [PF-236] Map Ric Backload messages task - [#448](https://github.com/swarmhub/swarm-tasks/pull/448)

### :bug: Fixes

- [ON-1671] TR InstrumentIdentifers BugFix - [#444](https://github.com/swarmhub/swarm-tasks/pull/444)

### :pray: Contributors

- [Luis Braga](https://github.com/microft)
- [Puneeth R](https://github.com/puneeth-r)

## v3.5.10.2 <Badge text="beta" type="success" />
Released on December 8, 2020.
### :boom: Features

- [ON-1310] SE Trade Blotter - Orders - [#420](https://github.com/swarmhub/swarm-tasks/pull/420)

### :bug: Fixes

- [PF-467] Patch logic to parse RIC from file url in ParquetRollup - [#443](https://github.com/swarmhub/swarm-tasks/pull/443)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)
- [Puneeth R](https://github.com/puneeth-r)

## v3.5.10.1 <Badge text="beta" type="success" />
Released on December 4, 2020.
### :muscle: Enhancements

- Refactor UnavistaDownloader to use SDK Secrets Store - [#426](https://github.com/swarmhub/swarm-tasks/pull/426)
- [PF-237] DownloadTickData: Refactor Annual tick data download to produce single backload file - [#429](https://github.com/swarmhub/swarm-tasks/pull/429)
- [PF-237] DownloadTickData: added audit for quarterly API requests - [#434](https://github.com/swarmhub/swarm-tasks/pull/434)
- [DE-264] Update Swarm SDK to Support SinkFileAudit Duplicate Record Storage - [#436](https://github.com/swarmhub/swarm-tasks/pull/436)
- [DE-399] New feature on column manipulator to allow dots in columns - [#435](https://github.com/swarmhub/swarm-tasks/pull/435)

### :bug: Fixes

- [TS-77, TS-131] update mapping for strike price type and add Underlying ISIN in instrument fallback ' - [#317](https://github.com/swarmhub/swarm-tasks/pull/317)
- [CS-1748] EMIR Unavista Generate Report: fixed error handling in FilterAssetClass - [#432](https://github.com/swarmhub/swarm-tasks/pull/432)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)
- [Fabio Ramalho](https://github.com/fsramalho)
- [Luis Braga](https://github.com/microft)
- [Maddie Mclean](https://github.com/maddiemclean)
- [Sandro Lourenco](https://github.com/lzefyrus)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.5.9.12 <Badge text="beta" type="success" />
Released on December 1, 2020.
### :bug: Fixes

- typo and .lower on comms/voice/callcabinet/add_transcription - [#423](https://github.com/swarmhub/swarm-tasks/pull/423)
- escape spaces on transcription jobe name - [#423](https://github.com/swarmhub/swarm-tasks/pull/423)

### :pray: Contributors

- [Sandro Lourenco](https://github.com/lzefyrus)

## v3.5.9.11 <Badge text="beta" type="success" />
Released on November 30, 2020.
### :boom: Features

- [ON-1666] Add ffmpeg binnary - [#409](https://github.com/swarmhub/swarm-tasks/pull/409)
- [ON-1450] Transcription scheduler and consumer and translation - [#413](https://github.com/swarmhub/swarm-tasks/pull/413)

### :muscle: Enhancements

- [ON-1641] TR-CME-MIFIR Capstone override reportable status - [#415](https://github.com/swarmhub/swarm-tasks/pull/415)

### :pray: Contributors

- [Diogo-B-Lima](https://github.com/Diogo-B-Lima)
- [Sandro Lourenco](https://github.com/lzefyrus)

## v3.5.6.11 <Badge text="beta" type="success" />
Released on November 27, 2020.
### :bug: Fixes

- [ON-1665] - Capital Index - HTML Flows feed - [#412](https://github.com/swarmhub/swarm-tasks/pull/412)

### :pray: Contributors

- [Maddie Mclean](https://github.com/MaddieMclean)

## v3.5.9.10 <Badge text="beta" type="success" />
Released on November 27, 2020.
### :boom: Features

- [ON-1402] TR BAIN BRS - [#369](https://github.com/swarmhub/swarm-tasks/pull/369)
- [PF-237] Annual Tick Data: added tasks to download annual tick data for given ric - [#411](https://github.com/swarmhub/swarm-tasks/pull/411)

### :bug: Fixes

- [ON-1667] - TR Instrument Identifiers FX Options BugFix - [#410](https://github.com/swarmhub/swarm-tasks/pull/410)

### :pray: Contributors

- [Puneeth R](https://github.com/puneeth-r)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.5.9.9 <Badge text="beta" type="success" />
Released on November 24, 2020.
### :muscle: Enhancements

- [ON-1461] Callcabinet scrapper - [#392](https://github.com/swarmhub/swarm-tasks/pull/404)

## v3.5.9.8 <Badge text="beta" type="success" />
Released on November 23, 2020.
### :muscle: Enhancements

- [ON-1630] TR CME Mifir Venue Override - [#392](https://github.com/swarmhub/swarm-tasks/pull/392)
- [ON-1608] Selerity tasks: Code cleanup - [#396](https://github.com/swarmhub/swarm-tasks/pull/396)

### :pray: Contributors

- [Puneeth R](https://github.com/puneeth-r)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.5.9.7 <Badge text="beta" type="success" />
Released on November 20, 2020.
### :boom: Features

- [ON-1608] added selerity tasks - [#382](https://github.com/swarmhub/swarm-tasks/pull/382)

### :muscle: Enhancements

- [DE-283] Adding Options Option to MapRic - [#367](https://github.com/swarmhub/swarm-tasks/pull/367)
- [DE-282] Map Ric Support CFD Products - [#385](https://github.com/swarmhub/swarm-tasks/pull/385)
- [ON-1601] order analytics message producer task - [#389](https://github.com/swarmhub/swarm-tasks/pull/389)
- se-schema 3.1.23 - [#390](https://github.com/swarmhub/swarm-tasks/pull/390)

### :bug: Fixes

- [ON-1629] TR Unavista Report Generator Underlying Fields BugFix - [#388](https://github.com/swarmhub/swarm-tasks/pull/388)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [John Higgins](https://github.com/redking1)
- [Luis Braga](https://github.com/microft)
- [Puneeth R](https://github.com/puneeth-r)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.5.9.6 <Badge text="beta" type="success" />
Released on November 18, 2020.
### :muscle: Enhancements

- se-schema 3.1.21 - [#379](https://github.com/swarmhub/swarm-tasks/pull/379)
- [ON-1620] TR Unavista Generate Report Commodity Contracts - [#380](https://github.com/swarmhub/swarm-tasks/pull/380)
- [ON-1628] TR CME MIFIR Instrument Fallback Mifir Eligible - [#380](https://github.com/swarmhub/swarm-tasks/pull/380)

### :bug: Fixes

- [ON-1617] EMIR Universal CME: nationality as list of strings - [#379](https://github.com/swarmhub/swarm-tasks/pull/379)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [Puneeth R](https://github.com/puneeth-r)

## v3.5.9.5 <Badge text="beta" type="success" />
Released on November 18, 2020.


## v3.5.9.4 <Badge text="beta" type="success" />
Released on November 15, 2020.
### :muscle: Enhancements

- se-schema 3.1.20 - [#368](https://github.com/swarmhub/swarm-tasks/pull/368)
- [ON-1605] EMIR Unavista Generate Report Update MapParties - [#370](https://github.com/swarmhub/swarm-tasks/pull/370)
- [ON-1578] TR CME MIFIR Strike Price Currency Update - [#372](https://github.com/swarmhub/swarm-tasks/pull/372)
- [ON-1617] EMIR UNIVERSAL CME PARTIES FALLBACK Updates - [#374](https://github.com/swarmhub/swarm-tasks/pull/374)
- [ON-1618] MapBuyerSeller: Added mapping for `Scheme Name` and `Country` - [#375](https://github.com/swarmhub/swarm-tasks/pull/375)

### :bug: Fixes

- [ON-1611] MapBuyerSeller: fixed issue with buyer and seller mapping - [#373](https://github.com/swarmhub/swarm-tasks/pull/373)
- [PR-1902] TR Workflow Map Instrument Repeated ISIN Tag - [#376](https://github.com/swarmhub/swarm-tasks/pull/376)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)
- [Fabio Ramalho](https://github.com/fsramalho)
- [Puneeth R](https://github.com/puneeth-r)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.5.9.3 <Badge text="beta" type="success" />
Released on November 10, 2020.
### :muscle: Enhancements

- [ON-1579] TR Cme Mifir Instrument Details - [#355](https://github.com/swarmhub/swarm-tasks/pull/355)
- [ON-1506] tr-cme-mifir parties fallback updates - [#360](https://github.com/swarmhub/swarm-tasks/pull/360)

### :bug: Fixes

- [ON-1592] TR-CME-MIFIR InstrumentIdentifiers Bugfix - [#351](https://github.com/swarmhub/swarm-tasks/pull/351)
- Link Instrument: fixed prioritisation on ccy and/or venue - [#364](https://github.com/swarmhub/swarm-tasks/pull/364)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [Micah Paul](https://github.com/micahpp)
- [Puneeth R](https://github.com/puneeth-r)

## v3.5.9.2 <Badge text="beta" type="success" />
Released on November 6, 2020.
### :muscle: Enhancements

- [SC-196] Route .orderAnalyticsPending to TCA Topic - [#301](https://github.com/swarmhub/swarm-tasks/pull/301)
- [ON-1262] Link instrument: fx options - [#346](https://github.com/swarmhub/swarm-tasks/pull/346)
- Clean up map ric code - (https://github.com/swarmhub/swarm-tasks/pull/356)

### :bug: Fixes

- EMIR CME Instrument Identifiers: fixed check valid isin - [#361](https://github.com/swarmhub/swarm-tasks/pull/361)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [Luis Braga](https://github.com/microft)

## v3.5.9.1 <Badge text="beta" type="success" />
Released on November 6, 2020.
### :muscle: Enhancements

- [ON-1590] EMIR Generate Report: action type valuation - [#349](https://github.com/swarmhub/swarm-tasks/pull/349)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)

## v3.5.6.6 <Badge text="beta" type="success" />
Released on November 4, 2020.
### :muscle: Enhancements

- [TS-125] update swarm and se schema - [#352](https://github.com/swarmhub/swarm-tasks/pull/352)

### :pray: Contributors

- [Maddie Mclean](https://github.com/MaddieMclean)

## v3.5.6.4 <Badge text="beta" type="success" />
Released on November 3, 2020.
### :muscle: Enhancements

- [TS-125] add functionality to handle zip files for html_file_splitter - [#337](https://github.com/swarmhub/swarm-tasks/pull/337)

### :pray: Contributors

- [Maddie Mclean](https://github.com/MaddieMclean)

## v3.5.6.3 <Badge text="beta" type="success" />
Released on November 2, 2020.
### :boom: Features

- [ON-1506] PartyFallback: add PartyFallback task implementation - [#300](https://github.com/swarmhub/swarm-tasks/pull/300)
- [ON-1574] Added EMIR CME map action task - [#342](https://github.com/swarmhub/swarm-tasks/pull/342)

### :bug: Fixes

- [SC-23] FilterInvalidRics: replace apply function with mask - [#343](https://github.com/swarmhub/swarm-tasks/pull/343)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [Micah Paul](https://github.com/micahpp)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.5.6.2 <Badge text="beta" type="success" />
Released on October 30, 2020.
### :muscle: Enhancements

- [ON-1556][ON-1558][ON-1560][ON-1562][ON-1566][ON-1570][ON-1573][ON-1575] Unavista Submit Report fixes - [#332](https://github.com/swarmhub/swarm-tasks/pull/332)
- [DE-281] Map Attribute, Map Value: handle missing source attribute(s) - [#335](https://github.com/swarmhub/swarm-tasks/pull/335)
- se-schema 3.1.16 - [#336](https://github.com/swarmhub/swarm-tasks/pull/336)
- [SC-23] GetOnDemandData: fixed issue with saving file for rics with '/' - [#339](https://github.com/swarmhub/swarm-tasks/pull/339)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.5.6.1 <Badge text="beta" type="success" />
Released on October 26, 2020.
### :bug: Fixes

- LinkParties: process parties fetched - [#328](https://github.com/swarmhub/swarm-tasks/pull/328)
- [PR-1831] EMIR Unavista Report: changed report template - [#331](https://github.com/swarmhub/swarm-tasks/pull/331)

### :fire: Breaking Changes

- Unavista workflow tasks into emir.workflow - [#319](https://github.com/swarmhub/swarm-tasks/pull/319)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)

## v3.5.6.0 <Badge text="beta" type="success" />
Released on October 23, 2020.
### :boom: Features

- [ON-1349] Emir unavista response - [#249](https://github.com/swarmhub/swarm-tasks/pull/249)

### :bug: Fixes

- [SC-23] MapRicCode: added null value check - [#324](https://github.com/swarmhub/swarm-tasks/pull/324)
- [ON-1479] make beneficiary_identifier optional - [#327](https://github.com/swarmhub/swarm-tasks/pull/327)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [Maddie Mclean](https://github.com/maddiemclean)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.5.5.3 <Badge text="beta" type="success" />
Released on October 23, 2020.
### :muscle: Enhancements

- [ON-1535] EMIR CME Instrument Fallback - [#304](https://github.com/swarmhub/swarm-tasks/pull/304)
- [ON-1552] seller/buyer not linking up to myMarket data - [#320](https://github.com/swarmhub/swarm-tasks/pull/320)

### :bug: Fixes

- [SC-23] MapInstrumentList: added sanity check in map_instrument list - [#314](https://github.com/swarmhub/swarm-tasks/pull/314)
- [ON-1486] fix for swarm raw index if not present in the flow - [#321](https://github.com/swarmhub/swarm-tasks/pull/321)

### :pray: Contributors

- [Maddie Mclean](https://github.com/maddiemclean)
- [Micah Paul](https://github.com/micahpp)
- [Puneeth R](https://github.com/puneeth-r)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.5.5.1 <Badge text="beta" type="success" />
Released on October 22, 2020.
### :muscle: Enhancements

- [ON-1540] - PartiesFallback task - add task implementation

### :bug: Fixes

- [SC-23] UpdateInstrumentListInElastic: fix ric parsing logic - [#310](https://github.com/swarmhub/swarm-tasks/pull/310)
- Refinitiv Bulk Backload: changed end_date to 'today' - [#311](https://github.com/swarmhub/swarm-tasks/pull/311)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [Micah Paul](https://github.com/micahpp)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.5.5.0 <Badge text="beta" type="success" />
Released on October 21, 2020.
### :muscle: Enhancements

- [SC-23] MapInstrumentList: added DSS API request to fetch Refinitiv Exchange Code - [#305](https://github.com/swarmhub/swarm-tasks/pull/305)
- [SC-23] ProcessSpotInstruments: added `BTG` currency - [#307](https://github.com/swarmhub/swarm-tasks/pull/307)
- [SC-63] ProcessSpotInstruments: include precious metal currencies - [#292](https://github.com/swarmhub/swarm-tasks/pull/292)

### :pray: Contributors

- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.5.4.0 <Badge text="beta" type="success" />
Released on October 20, 2020.
### :boom: Features

- [ON-1325] Feature EMIR CME TR - [#283](https://github.com/swarmhub/swarm-tasks/pull/283)

### :muscle: Enhancements

- [SC-63] ProcessSpotInstruments: include precious metal currencies - [#292](https://github.com/swarmhub/swarm-tasks/pull/292)

### :pray: Contributors

- [Puneeth R](https://github.com/puneeth-r)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.5.3.1 <Badge text="beta" type="success" />
Released on October 15, 2020.
### :muscle: Enhancements

- [SC-55] GetMicExchangeMap: update 'mic-refinitiv-exchange' map - [#293](https://github.com/swarmhub/swarm-tasks/pull/293)

### :bug: Fixes

- [SC-199] ComposeRicStats: Patch Date Extraction - [#295](https://github.com/swarmhub/swarm-tasks/pull/295)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)
- [Suresh Babu Angadi](https://github.com/sureshab)

## 3.5.3.0 <Badge text="beta" type="success" />
Released on October 14, 2020.
### :boom: Features

- [ON-1324] Getbux TRV2 Trades - [#197](https://github.com/swarmhub/swarm-tasks/pull/197)
- [ON-1217] ATFX MT4 HTML Orders - [#234](https://github.com/swarmhub/swarm-tasks/pull/234)

### :muscle: Enhancements

- [SC-59] 'GetFuturesProductCodeRicRootMap': updated 'Product Code' to 'Ric Root' map for futures - [#288](https://github.com/swarmhub/swarm-tasks/pull/288)
- swarm-sdk 3.5.3 - [#289](https://github.com/swarmhub/swarm-tasks/pull/289)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [Puneeth R](https://github.com/puneeth-r)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.5.0.2 <Badge text="beta" type="success" />
Released on October 13, 2020.
### :bug: Fixes

- [SC-23] ParseUnderlyingInstrumentIdCode: handle empty frame input - [#284](https://github.com/swarmhub/swarm-tasks/pull/284)
- [SC-199] BulkBackloadExecutor: Added Support for Backload RICS via Flow Args - [#285](https://github.com/swarmhub/swarm-tasks/pull/285)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.5.0.1 <Badge text="beta" type="success" />
Released on October 11, 2020.
### :boom: Features

- [SC-23] Map Ric Tasks - [#131](https://github.com/swarmhub/swarm-tasks/pull/131)

### :muscle: Enhancements

- [SC-20] Feature Poll Refinitiv for market data - [#242](https://github.com/swarmhub/swarm-tasks/pull/242)
- [SC-191] New Task - Kafka Message Picker to pd.DataFrame - [#269](https://github.com/swarmhub/swarm-tasks/pull/269)
- [SC-20] Kafka s3 key producer - [#272](https://github.com/swarmhub/swarm-tasks/pull/272)
- [ON-1403] FrameQuery task - [#275](https://github.com/swarmhub/swarm-tasks/pull/275)
- [SC-199] Refinitiv Bulk Backload - [#277](https://github.com/swarmhub/swarm-tasks/pull/277)
- [SC-192] Refinitiv Processing Tweaks - [#280](https://github.com/swarmhub/swarm-tasks/pull/280)

### :bug: Fixes

- [ON-1486] EMIR - Currency - Convert CNH to CNY - [#252](https://github.com/swarmhub/swarm-tasks/pull/252)
- [SC-20] Bulk topic naming refactor - [#274](https://github.com/swarmhub/swarm-tasks/pull/274)
- [SC-23] fix 'GetOnDemandData' task - [#278](https://github.com/swarmhub/swarm-tasks/pull/278)
- [SC-23] GetOnDemandData: fix validation error - [#279](https://github.com/swarmhub/swarm-tasks/pull/279)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)
- [John Higgins](https://github.com/redking1)
- [Luis Braga](https://github.com/microft)
- [Maddie Mclean](https://github.com/maddiemclean)
- [Sandro Lourenco](https://github.com/lzefyrus)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.5.0.0 <Badge text="beta" type="success" />
Released on October 6, 2020.
### :boom: Features

- [SC-192] Refinitiv Flows - [#268](https://github.com/swarmhub/swarm-tasks/pull/268)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)

## v3.4.45.14 <Badge text="beta" type="success" />
Released on October 4, 2020.
### :muscle: Enhancements

- [SC-192] Refinitiv Daily Extract Flow - [#265](https://github.com/swarmhub/swarm-tasks/pull/265)

### :bug: Fixes

- [DE-258] Bugfix/s3 upload typo - [#261](https://github.com/swarmhub/swarm-tasks/pull/261)
- [DE-258] feature s3Meta to support unquote keys - [#264](https://github.com/swarmhub/swarm-tasks/pull/264)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)
- [Sandro Lourenco](https://github.com/lzefyrus)

## v3.4.45.13 <Badge text="beta" type="success" />
Released on October 2, 2020.
### :bug: Fixes

- [DE-258] Bugfix/s3 upload typo - [#261](https://github.com/swarmhub/swarm-tasks/pull/261)

### :pray: Contributors

- [Sandro Lourenco](https://github.com/lzefyrus)

## v3.4.45.12 <Badge text="beta" type="success" />
Released on October 1, 2020.
### :bug: Fixes

- [DE-261] ElasticBulkTransformer: support optional producer result and batch index - [#258](https://github.com/swarmhub/swarm-tasks/pull/258)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)

## v3.4.45.11 <Badge text="beta" type="success" />
Released on October 1, 2020.
### :muscle: Enhancements

- [DE-258] Bugfix MapValue [#255](https://github.com/swarmhub/swarm-tasks/pull/255)

### :pray: Contributors

- [Sandro Lourenco](https://github.com/lzefyrus)

## v3.4.45.10 <Badge text="beta" type="success" />
Released on October 1, 2020.
### :muscle: Enhancements

- [ON-1397] Link instruments preference - [#220](https://github.com/swarmhub/swarm-tasks/pull/220)
- [ON-1403] Chat Mapping - Txtsmarter - Whatsapp - Attachments / Voice Rec / Mymarket mappings [#245](https://github.com/swarmhub/swarm-tasks/pull/245)
- [DE-258] MapValue: support replace pattern by empty string [#250](https://github.com/swarmhub/swarm-tasks/pull/250)

### :bug: Fixes

- [ON-1409] Unavista-generate-report Modify Elastic Query - [#231](https://github.com/swarmhub/swarm-tasks/pull/231)
- [ON-1350] Bugfix for Unavista Poller - [#246](https://github.com/swarmhub/swarm-tasks/pull/246)
- [SC-19] Process Refinitiv Tick Data - [#251](https://github.com/swarmhub/swarm-tasks/pull/251)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)
- [Fabio Ramalho](https://github.com/fsramalho)
- [John Higgins](https://github.com/redking1)
- [Micah Paul](https://github.com/micahpp)
- [Ozan Ozgur](https://github.com/arq101)
- [Sandro Lourenco](https://github.com/lzefyrus)

## v3.4.45.9 <Badge text="beta" type="success" />
Released on September 29, 2020.
### :muscle: Enhancements

- [ON-1403] Chat Mapping - Txtsmarter - Whatsapp - Attachments / Voice Rec / Mymarket mappings [#245](https://github.com/swarmhub/swarm-tasks/pull/245)

### :bug: Fixes

- [ON-1409] Unavista-generate-report Modify Elastic Query - [#231](https://github.com/swarmhub/swarm-tasks/pull/231)

### :pray: Contributors

- [Micah Paul](https://github.com/micahpp)
- [Ozan Ozgur](https://github.com/arq101)
- [Sandro Lourenco](https://github.com/lzefyrus)

## v3.4.45.8 <Badge text="beta" type="success" />
Released on September 25, 2020.
### :muscle: Enhancements

- [ON-1348] Unavista submit report - [#192](https://github.com/swarmhub/swarm-tasks/pull/192)
- [ON-1350] Emir workflow Unavista Poller [#228](https://github.com/swarmhub/swarm-tasks/pull/228)

### :bug: Fixes

- [DE-235] Elastic Bulk Transformer Index Error - [#240](https://github.com/swarmhub/swarm-tasks/pull/240)
- [DE-249] tr_pi_enrichment_enabled update to trPIEnrichmentEnabled [#241](https://steeleye.atlassian.net/browse/DE-249)

### :pray: Contributors

- [John Higgins](https://github.com/redking1)
- [Puneeth R](https://github.com/puneeth-r)
- [Sandro Lourenco](https://github.com/lzefyrus)

## v3.4.45.7 <Badge text="beta" type="success" />
Released on September 22, 2020.
### :boom: Features

- [ON-1372] Add SE Trade Blotter Branch Membership Country task - [#233](https://github.com/swarmhub/swarm-tasks/pull/233)
- [ON-1431] Populate `workflow.reportId` - [#229](https://github.com/swarmhub/swarm-tasks/pull/229)

### :muscle: Enhancements

- [ON-1409] Unavista generate report add logic for Action Type - [#215](https://github.com/swarmhub/swarm-tasks/pull/215)
- [DE-244] RegexGroupDict - add string_parser param - [#216](https://github.com/swarmhub/swarm-tasks/pull/216)
- [ON-1441] ADSS EMIR MT4 Support for field ActionType - [#222](https://github.com/swarmhub/swarm-tasks/pull/222)
- se-schema 3.0.14 - [#223](https://github.com/swarmhub/swarm-tasks/pull/223)

### :bug: Fixes

- [ON-1436] Instrument Identifier bugfix & Emir Quarantine fix - [#221](https://github.com/swarmhub/swarm-tasks/pull/221)
- [ON-1436] Dropping parent for quarantined emir position - [#224](https://github.com/swarmhub/swarm-tasks/pull/224)
- [ON-1436] Bugfix duplicate version conflicts - [#227](https://github.com/swarmhub/swarm-tasks/pull/227)
- [ON-1451] CME Poll unlimited retrying for UV reports - [#232](https://github.com/swarmhub/swarm-tasks/pull/232)

### :fire: Breaking Changes

- [DE-216] added fixes for FrameConcatenator task - [#217](https://github.com/swarmhub/swarm-tasks/pull/217)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [John Higgins](https://github.com/redking1)
- [Micah Paul](https://github.com/micahpp)
- [Ozan Ozgur](https://github.com/arq101)
- [Puneeth R](https://github.com/puneeth-r)
- [Suresh Babu Angadi](https://github.com/sureshab)
- [Stephen Salamida](https://github.com/sdsalamida)

## v3.4.45.6 <Badge text="beta" type="success" />
Released on September 14, 2020.
### :boom: Features

- [DE-239] add GetRowsByCondition task - [#206](https://github.com/swarmhub/swarm-tasks/pull/206)

### :muscle: Enhancements

- [ON-1424] se-schema 3.0.11 - [#213](https://github.com/swarmhub/swarm-tasks/pull/213)

### :bug: Fixes

- [ON-1432] MT4 instrument Symbol take everything before character - [#214](https://github.com/swarmhub/swarm-tasks/pull/214)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [John Higgins](https://github.com/redking1)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.4.45.5 <Badge text="beta" type="success" />
Released on September 10, 2020.
### :muscle: Enhancements

- se-schema 3.0.10 - [#208](https://github.com/swarmhub/swarm-tasks/pull/208)
- [ON-1426] Assign termination date only when action != N - [#210](https://github.com/swarmhub/swarm-tasks/pull/210)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)
- [Fabio Ramalho](https://github.com/fsramalho)

## v3.4.45.4 <Badge text="beta" type="success" />
Released on September 10, 2020.
### :bug: Fixes

- [ON-1410] Bugfix on update action (https://github.com/swarmhub/swarm-tasks/pull/203)

### :pray: Contributors

- [John Higgins](https://github.com/redking1)

## v3.4.45.3 <Badge text="beta" type="success" />
Released on September 9, 2020.
### :muscle: Enhancements

- se-schema 3.0.8 - [#199](https://github.com/swarmhub/swarm-tasks/pull/199)

### :bug: Fixes

- [ON-1400] EMIR version conflicts bug - [#198](https://github.com/swarmhub/swarm-tasks/pull/198)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [John Higgins](https://github.com/redking1)

## v3.4.45.2 <Badge text="beta" type="success" />
Released on September 8, 2020.
### :boom: Features

- [ON-1202] ADSS-Trades and Orders - [#143](https://github.com/swarmhub/swarm-tasks/pull/143)
- [ON-1363] Thornbridge Luna Advance Comms - [#186](https://github.com/swarmhub/swarm-tasks/pull/186)
- [DE-234] MapConditional cases - [#191](https://github.com/swarmhub/swarm-tasks/pull/191)

### :muscle: Enhancements

- [ON-1373] CME report gen remap target field action - [#185](https://github.com/swarmhub/swarm-tasks/pull/185)
- [ON-1330] Emir Unavista generate report: adjust mappings - [#188](https://github.com/swarmhub/swarm-tasks/pull/188)
- [ON-1393] se-schema 3.0.5 - [#189](https://github.com/swarmhub/swarm-tasks/pull/189)
- [PR-1720] se-schema 3.0.6 - [#194](https://github.com/swarmhub/swarm-tasks/pull/194)

### :bug: Fixes

- [ON-1261] Schroders Instrument Fallback: added isin check for non eligible - [#190](https://github.com/swarmhub/swarm-tasks/pull/190)
- [ON-1365] Fixed Is CFD logic - [#193](https://github.com/swarmhub/swarm-tasks/pull/193)

### :fire: Breaking Changes

- [ON-1342] Generic Short Selling Indicator task - [#187](https://github.com/swarmhub/swarm-tasks/pull/187)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [Ozan Ozgur](https://github.com/arq101)
- [Puneeth R](https://github.com/puneeth-r)

## v3.4.45.1 <Badge text="beta" type="success" />
Released on September 2, 2020.
### :muscle: Enhancements

- [ON-1341] LinkInstrument: Added VenueInstrument - [#177](https://github.com/swarmhub/swarm-tasks/pull/177)
- [ON-1384] se-schema 3.0.3 - [#178](https://github.com/swarmhub/swarm-tasks/pull/178)
- [PR-1699] se-schema 3.0.4 - [#179](https://github.com/swarmhub/swarm-tasks/pull/179)

### :bug: Fixes

- [ON-1306] SE-Trade-Blotter TRX Ref No to uppercase - [#180](https://github.com/swarmhub/swarm-tasks/pull/180)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)

## v3.4.45.0 <Badge text="beta" type="success" />
Released on September 1, 2020.
### :muscle: Enhancements

- [DE-232] se-schema 3.0.2 and necessary changes - [#171](https://github.com/swarmhub/swarm-tasks/pull/171)

### :bug: Fixes

- [ON-1354] CME-report-gen fix priceMultiplier decimal places - [#170](https://github.com/swarmhub/swarm-tasks/pull/170)
- [DE-212] ElasticBulkTransform index MISSING - [#172](https://github.com/swarmhub/swarm-tasks/pull/172)
- [ON-1260] Fix Schroders record type - [#173](https://github.com/swarmhub/swarm-tasks/pull/173)
- [DE-231] ConcatAttributes: fix prefix in empty columns - [#174](https://github.com/swarmhub/swarm-tasks/pull/174)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [Ozan Ozgur](https://github.com/arq101)

## v3.4.44.4 <Badge text="beta" type="success" />
Released on August 28, 2020.
### :muscle: Enhancements

- [ON-1359] CME-report-generator change logic for Underlying & UnderlyingType - [#163](https://github.com/swarmhub/swarm-tasks/pull/163)

### :bug: Fixes

- [ON-1331] Ensure 'workflow.internal.validation' is added - [#158](https://github.com/swarmhub/swarm-tasks/pull/158)
- [ON-1261][ON-1351] InstrumentFallback fix and Schroders TransactionDetailsVenue task - [#159](https://github.com/swarmhub/swarm-tasks/pull/159)
- [DE-230] fix ConvertDatetime units & format params clash - [#162](https://github.com/swarmhub/swarm-tasks/pull/162)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [Micah Paul](https://github.com/micahpp)
- [Ozan Ozgur](https://github.com/arq101)

## v3.4.44.3 <Badge text="beta" type="success" />
Released on August 26, 2020.
### :boom: Features

- [DE-155] LinkInstrument: Audit non linked instruments records only - [#115](https://github.com/swarmhub/swarm-tasks/pull/115)
- [DE-228] JsonFileSplitter task - [#153](https://github.com/swarmhub/swarm-tasks/pull/153)

### :muscle: Enhancements

- [ON-1319] Steeleye trade blotter issues - [#145](https://github.com/swarmhub/swarm-tasks/pull/145)
- [DE-227] convertDatetime from epoch - [#152](https://github.com/swarmhub/swarm-tasks/pull/152)

### :bug: Fixes

- [ON-1112] update RegexSearchGroupDict param name - [#148](https://github.com/swarmhub/swarm-tasks/pull/148)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [Micah Paul](https://github.com/micahpp)

## v3.4.44.2 <Badge text="beta" type="success" />
Released on August 25, 2020.
### :muscle: Enhancements

- [DE-215] MapToNested: Handled flatten nested path when already exists - [#140](https://github.com/swarmhub/swarm-tasks/pull/140)

### :bug: Fixes

- [ON-1329] Fix AttributeError preventing successful MapInstrument - [#129](https://github.com/swarmhub/swarm-tasks/pull/129)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [Ozan Ozgur](https://github.com/arq101)

## v3.4.44.1 <Badge text="beta" type="success" />
Released on August 22, 2020.
### :muscle: Enhancements

- [DE-214] se-schema 3.0.0b149 - [#137](https://github.com/swarmhub/swarm-tasks/pull/137)

### :bug: Fixes

- [ON-1302] Patch PI Enrichment Reference and MiFIR N Mask - [#136](https://github.com/swarmhub/swarm-tasks/pull/136)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)
- [Fabio Ramalho](https://github.com/fsramalho)

## v3.4.44.0 <Badge text="beta" type="success" />
Released on August 21, 2020.
### :boom: Features

- [ON-1112] add S3ObjectMetadata, AttachmentFromS3Metadata & RegexSearchGroupDict tasks - [#126](https://github.com/swarmhub/swarm-tasks/pull/126)
- [ON-1260] Schroders BRS Record Type task: fixed mask - [#132](https://github.com/swarmhub/swarm-tasks/pull/132)

### :bug: Fixes

- [DE-194] unquote_plus to unquote the s3key - [#133](https://github.com/swarmhub/swarm-tasks/pull/133)

### :pray: Contributors

- [Luis BRaga](https://github.com/microft)
- [Micah Paul](https://github.com/micahpp)
- [Sandro Lourenco](https://github.com/lzefyrus)

## v3.4.43.2 <Badge text="beta" type="success" />
Released on August 19, 2020.
### :boom: Features

- [ON-1196] ADSS-Trades and Orders - [#112](https://github.com/swarmhub/swarm-tasks/pull/112)

### :bug: Fixes

- [ON-1302] integrate pi enrich in tr report generation - [#127](https://github.com/swarmhub/swarm-tasks/pull/127)

### :pray: Contributors

- [Puneeth R](https://github.com/puneeth-r)
- [Sandro L](https://github.com/lzefyrus)

## v3.4.43.1 <Badge text="beta" type="success" />
Released on August 17, 2020.
### :boom: Features

- Bespoke plugins for lme tif instrument feed
- XMLFileSplitter plugin
- Regex replace functionality for MapValue
- Map using attributes functionality for MapConditional

### :muscle: Enhancements

- [DE-196] Update SDK to v3.4.43 - [#120](https://github.com/swarmhub/swarm-sdk/pull/120)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)
- [Madeline Mclean](https://github.com/MaddieMclean)

## v3.4.42.0 <Badge text="beta" type="success" />
Released on August 11, 2020.
### :boom: Features

- New InstrumentIdentifiers for Emir-OneZero - [#107](https://github.com/swarmhub/swarm-tasks/pull/107)
- DE-154  setting the sftp retry count from Params - [#111](https://github.com/swarmhub/swarm-tasks/pull/111)
- [DE-131] Added MapToNested task - [#114](https://github.com/swarmhub/swarm-tasks/pull/114)
- [DE-179] Batch Producer DTypes - [#116](https://github.com/swarmhub/swarm-tasks/pull/116)

### :bug: Fixes

- [DE-129] Fixed MapFromNested task - [#113](https://github.com/swarmhub/swarm-tasks/pull/113)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [Luis Braga](https://github.com/microft)
- [Ozan Ozgur](https://github.com/arq101)

## v3.4.41.0 <Badge text="beta" type="success" />
Released on August 3, 2020.
### :boom: Features

- ON-1231 make transactionRefNo alphanumeric only (https://github.com/swarmhub/swarm-tasks/pull/106)
- Added TradingVenue for mg [ON-1145] - [#99](https://github.com/swarmhub/swarm-tasks/pull/99)

### :muscle: Enhancements

- [PR-153] Updated swarm-sdk - [#108](https://github.com/swarmhub/swarm-tasks/pull/108)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [Maddie McLean](https://github.com/MaddieMclean)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.4.40.1 <Badge text="beta" type="success" />
Released on July 29, 2020.
### :bug: Fixes

- [DE-147] EMIR Submit and Process Report : Add Z to submitted date and reset pending - [#102](https://github.com/swarmhub/swarm-tasks/pull/102)

### :pray: Contributors

- [Luis Braga](https://github.com/microft)

## v3.4.40.0 <Badge text="beta" type="success" />
Released on July 28, 2020.
### :boom: Features

- Patch EMIR CME Report Generator Tasks [#75](https://github.com/swarmhub/swarm-tasks/pull/75)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)

## v3.4.39.2 <Badge text="beta" type="success" />
Released on July 28, 2020.
### :bug: Fixes

- LinkPositions: pass meta parent and uncomment workflow tr errors - [#95](https://github.com/swarmhub/swarm-tasks/pull/95)
- EMIR ReportDriver: Enforce validations passed - [#96](https://github.com/swarmhub/swarm-tasks/pull/96)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)

## v3.4.39.1 <Badge text="beta" type="success" />
Released on July 27, 2020.
### :bug: Fixes

- EMIR Process Response & TR Generate Report Patches - [#90](https://github.com/swarmhub/swarm-tasks/pull/90)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)

## v3.4.39.0 <Badge text="beta" type="success" />
Released on July 26, 2020.
### :boom: Features

- UpdateReport: This changes enables updating of the responseReceived EmirReport field [#68](https://github.com/swarmhub/swarm-tasks/pull/68)
- ElasticsearchClient: replaced hardcoded elastic meta with es client property - [#71](https://github.com/swarmhub/swarm-tasks/pull/71)

### :muscle: Enhancements

- Added unit tests for instrument identifiers - [#63](https://github.com/swarmhub/swarm-tasks/pull/63)
- EMIR CME Report LinkPosition: cast to float before int to increment safely - [#84](https://github.com/swarmhub/swarm-tasks/pull/84)

### :bug: Fixes

- Fix MapAttribute cast to absolule - [#65](https://github.com/swarmhub/swarm-tasks/pull/65)
- FrameConcatenator: remove unnecessary audit from task - [#66](https://github.com/swarmhub/swarm-tasks/pull/66)
- S3BucketMatcher - Accounting for EmirReport.filename not including the file extension. - [#81](https://github.com/swarmhub/swarm-tasks/pull/81)
- Fixed transform and mask logic in TrxDtlUpFront task - [#83](https://github.com/swarmhub/swarm-tasks/pull/83)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)
- [Fabio Ramalho](https://github.com/fsramalho)
- [Luis Braga](https://github.com/microft)
- [Ozan Ozgur](https://github.com/arq101)
- [Suresh Babu Angadi](https://github.com/sureshab)


## v3.4.38.1 <Badge text="beta" type="success" />
Released on July 23, 2020.
### :boom: Features

- TR > MapInstrument: expand criteria for report isin mask - [#80](https://github.com/swarmhub/swarm-tasks/pull/80)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)
