version: 2.1
orbs:
  slack: circleci/slack@3.4.2
#  codecov: codecov/codecov@3.0.0

commands:
  setup-and-validate:
    description: "Checkout Code, Validate, and Setup Docker Image"
    steps:
      - setup_remote_docker
      - run:
          name: Install poetry
          command: |
            curl -sSL https://raw.githubusercontent.com/python-poetry/install.python-poetry.org/main/install-poetry.py | python - --version 1.5.1
      - restore_cache:
          keys:
            - v2-poetry-deps-{{ checksum "poetry.lock" }}
      - run:
          name: Install Validation Dependencies
          command: |
            pip install --upgrade pip
            poetry config repositories.steeleye-pypi ${PYPI_HOST}
            poetry config http-basic.steeleye-pypi ${PYPI_USERNAME} ${PYPI_PASSWORD}
            poetry config virtualenvs.in-project true
            poetry install --sync --no-cache --no-ansi --no-interaction
      - save_cache:
          key: v2-poetry-deps-{{ checksum "poetry.lock" }}
          paths: .venv
      - run:
          name: Validate
          command: |
            poetry run pre-commit run --all --show-diff-on-failure
      - run:
          name: Run Tests
          command: |
            poetry run pytest tests
#            --cov=swarm_tasks --cov-report=xml
#      - codecov/upload

  update-version:
    description: "Update Version in pkg_info.json and pyproject.toml"
    steps:
      - run:
          name: Update Version
          command: |
            package_name=$(sed -n 's/name = "\(.*\)"/\1/p' pyproject.toml | head -1 | tr '-' '_')
            sed -i -e 's/latest/'${CIRCLE_TAG:1}'/g' $package_name/pkg_info.json
            poetry version ${CIRCLE_TAG:1}
            cat $package_name/pkg_info.json

  publish-package:
    description: "Publish the package to artifactory"
    steps:
      - run:
          name: Publish Package
          command: |
            poetry publish --build -r steeleye-pypi

  publish-image-to-ecr:
    description: Publish Image to Amazon AWS ECR
    steps:
      - run:
          name: Set Up AWS Credentials and Login
          command: |
            pip install awscli
            aws configure set aws_access_key_id $PROD_AWS_ACCESS_KEY_ID --profile prod
            aws configure set aws_secret_access_key $PROD_AWS_SECRET_ACCESS_KEY --profile prod
            aws configure set region $PROD_AWS_REGION --profile prod
            $(aws ecr get-login --no-include-email --profile prod --registry-ids 698897937809)

      - run:
          name: Build Docker Image
          command: |
            docker build --build-arg PYPI_USERNAME=${PYPI_USERNAME} \
                         --build-arg PYPI_PASSWORD=${PYPI_PASSWORD} \
                         -t local/swarm-tasks -f Dockerfile .
            
            docker tag local/swarm-tasks 698897937809.dkr.ecr.$PROD_AWS_REGION.amazonaws.com/${CIRCLE_PROJECT_REPONAME}:${CIRCLE_TAG:1}
            docker tag local/swarm-tasks 698897937809.dkr.ecr.$PROD_AWS_REGION.amazonaws.com/${CIRCLE_PROJECT_REPONAME}

      - run:
          name: Install dependencies for vulnerability check
          command: |
            sudo apt-get update --allow-releaseinfo-change
            sudo apt-get install -y wget jq
            wget https://github.com/aquasecurity/trivy/releases/download/v0.52.2/trivy_0.52.2_Linux-64bit.deb
            sudo dpkg -i trivy_0.52.2_Linux-64bit.deb
            sudo mv trivy_0.52.2_Linux-64bit.deb /usr/local/bin/trivy
            
            wget https://github.com/CycloneDX/cyclonedx-cli/releases/download/v0.25.1/cyclonedx-linux-x64
            sudo chmod +x cyclonedx-linux-x64
            sudo mv cyclonedx-linux-x64 /usr/local/bin/cyclonedx

      - run:
          name: Run Vulnerability Check
          command: |
            sudo trivy image --exit-code 0 --format cyclonedx --ignorefile ./.trivyignore.yaml --scanners vuln --output sbom-image.json --severity CRITICAL 698897937809.dkr.ecr.$PROD_AWS_REGION.amazonaws.com/${CIRCLE_PROJECT_REPONAME}:${CIRCLE_TAG:1}
            
            contains_vulnerabilities=$(jq 'select(.vulnerabilities != null and .vulnerabilities != []) | length > 0' "sbom-image.json")
            
            aws s3 cp sbom-image.json s3://se-security-operations/sbom/${CIRCLE_PROJECT_REPONAME}/${CIRCLE_PROJECT_REPONAME}/amd64/${CIRCLE_TAG:1}.json
            
            if [ "$contains_vulnerabilities" == "true" ]; then
              jq '.vulnerabilities' sbom-image.json
              echo "Found python package vulnerabilities"
              exit 1
            else
              echo "No vulnerabilities found"
            fi

      - run:
          name: Push Docker Image
          command: |
            docker push 698897937809.dkr.ecr.$PROD_AWS_REGION.amazonaws.com/${CIRCLE_PROJECT_REPONAME} --all-tags

jobs:
  build:
    docker:
      - image: cimg/python:3.9
    steps:
      - checkout
      - setup-and-validate
      - slack/status

  deploy:
    docker:
      - image: cimg/python:3.9
    steps:
      - checkout
      - setup-and-validate
      - update-version
      - publish-package
      - publish-image-to-ecr
      - slack/status

workflows:
  version: 2
  build:
    jobs:
      - build:
          context: swarm-global
          filters:
            branches:
              ignore:
                 - master
                 - gh-pages
  build_n_deploy:
    jobs:
      - build:
          context: swarm-global
          filters:
            branches:
              only:
                - master
              ignore:
                - gh-pages
      - deploy:
          context: swarm-global
          filters:
            tags:
              only: /v[0-9]+\.[0-9]+\.[0-9]+(-(post|dev|rc)\.[0-9]+)*/
            branches:
              ignore:
                - /.*/
                - gh-pages
