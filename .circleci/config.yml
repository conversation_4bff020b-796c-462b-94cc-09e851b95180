version: 2.1
commands:
  create-venv:
    description: "Create venv and installs requirements.txt"
    steps:
      - checkout
      - run:
          name: Create venv
          command: |
            python3 -m venv venv
            . venv/bin/activate
            mkdir ~/.pip
            echo "[global]" >> ~/.pip/pip.conf
            echo $PYPI_INDEX_URL >> ~/.pip/pip.conf
            echo "extra-index-url = https://pypi.org/simple" >> ~/.pip/pip.conf
      - run:
          name: Install requirements.txt
          command: |
            . venv/bin/activate
            pip install -r requirements.txt --no-cache-dir

jobs:
  test:
    docker:
      - image: circleci/python:3.6.1
        environment:
          CI_TEST_MODE: true
    steps:
      - checkout
      # Validate
      - run:
          name: Validate
          command: |
            python3 -m venv venv_validate
            . venv_validate/bin/activate
            pip install pre-commit
            pre-commit install
            pre-commit run --all-files

      # Test
      - create-venv
      - run:
          name: Test
          command: |
            . venv/bin/activate
            pip install -r requirements-test.txt
            package_name=$(sed -n 's/^ *name="\(.*\)",/\1/p' setup.py)
            pytest tests -rw --html=test-report.html
#          this belongs to the upper line: --cov=$package_name --cov-report term --cov-report html
  deploy:
    docker:
      - image: circleci/python:3.6.1
    steps:
      - checkout
      - create-venv
      # Update package version
      - run:
          name: Update package version
          command: |
            sed -i -e 's/PKG_VERSION/'${CIRCLE_TAG:1}'/g' setup.py
            cat setup.py
      # Publish
      - run:
          name: Publish
          command: |
            if [ -z "$CI" ]; then
              echo "Will only continue on CI"
              exit
            fi
            # build package and upload to private pypi index
            echo "[distutils]" >> ~/.pypirc
            echo "index-servers = pypi-private" >> ~/.pypirc
            echo "[pypi-private]" >> ~/.pypirc
            echo "repository=$PYPI_HOST" >> ~/.pypirc
            echo "username=$PYPI_USERNAME" >> ~/.pypirc
            echo "password=$PYPI_PASSWORD" >> ~/.pypirc
            python setup.py sdist upload -r pypi-private

workflows:
  test_n_deploy:
    jobs:
      - test:
          context: org-global
          branches:
            ignore:
              - gh-pages
      - deploy:
          context: org-global
          filters:
            tags:
              only: /v[0-9]+(\.[0-9]+)*/
            branches:
              ignore:
                - /.*/
                - gh-pages