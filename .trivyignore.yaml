vulnerabilities:
  - id: CVE-2024-33663
    statement: "CRITICAL: A vulnerability in python-jose@3.3.0 which is currently the latest stable version, has 'algorithm confusion' with OpenSSH ECDSA keys and other key formats"
    expired_at: 2025-12-31
  - id: CVE-2024-10096
    statement: "CRITICAL: Dask versions <=2024.8.2 contain a vulnerability in the Dask Distributed Server where the use of pickle serialization allows attackers to craft malicious objects"
    expired_at: 2025-12-31
  - id: CVE-2025-43859
    statement: "h11 is a Python implementation of HTTP/1.1. Prior to version 0.16.0, a leniency in h11's parsing of line terminators in chunked-coding message bodies can lead to request smuggling vulnerabilities under certain conditions. This issue has been patched in version 0.16.0."
    expired_at: 2025-12-31