name: Pull Request Update 

on:
  pull_request:
    types: [opened, edited, reopened, synchronize, ready_for_review]

jobs:
  pr-labeler:
    runs-on: ubuntu-latest
    steps:
      - uses: TimonVS/pr-labeler-action@v3
        env:
          GITHUB_TOKEN: ${{ secrets.GH_SE_BOT_TOKEN }}
  pr-assign:
    runs-on: ubuntu-latest
    steps:
      - uses: kentaro-m/auto-assign-action@v1.1.2
        with:
          configuration-path: .github/pr-assign.yml
