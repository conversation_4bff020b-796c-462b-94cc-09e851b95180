name: Release Management

on:
  push:
    branches:
      - master
    tags:
      - "v[0-9]+.[0-9]+.[0-9]+"

jobs:
  create_update_release:
    runs-on: ubuntu-latest
    if: ${{ github.ref == 'refs/heads/master'}}

    steps:
      - uses: actions/checkout@v2
      - uses: release-drafter/release-drafter@master
        id: create_update_release
        env:
          GITHUB_TOKEN: ${{ secrets.GH_SE_BOT_TOKEN }}
        with:
          prerelease: true
          publish: false

  publish_release:
    runs-on: ubuntu-latest
    if: ${{ contains(github.ref, 'refs/tags')  }}
    steps:
      - uses: actions/checkout@v2

      - name: Set env
        run: echo "RELEASE_VERSION=${GITHUB_REF#refs/*/}" >> $GITHUB_ENV

      - uses: release-drafter/release-drafter@master
        id: publish_release
        env:
          GITHUB_TOKEN: ${{ secrets.GH_SE_BOT_TOKEN }}
        with:
          publish: true
          version: ${{ env.RELEASE_VERSION }}

      - name: Final validation for Publish
        run: |
          echo A release with tag ${{ steps.publish_release.outputs.name }} was published
