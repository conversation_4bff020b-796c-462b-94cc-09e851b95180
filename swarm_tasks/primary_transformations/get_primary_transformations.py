import pandas as pd
from se_trades_tasks.order.transformations import (
    order_transform_maps as se_trades_order_transform_maps,
)
from se_trades_tasks.tr.transformations import (
    rts22_transform_maps as sett_rts22_transform_maps,
)
from swarm.conf import Settings
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.order.transformations import order_transform_maps
from swarm_tasks.position.transformations import position_transform_maps
from swarm_tasks.steeleye.market_counterparty.transformations import (
    market_counterparty_kyte_transform_maps,
)
from swarm_tasks.steeleye.mymarket.transformations import market_transform_maps
from swarm_tasks.steeleye.reference.instrument.steeleye.transformations import (
    reference_transform_maps,
)
from swarm_tasks.steeleye.tr.transformations import rts22_transform_maps


class GetPrimaryTransformations(TransformBaseTask):
    """
    This task serves to call the appropriate Transformation class' process method.
    The correct Transformation is fetched using Settings.bundle and Settings.tenant
    through the bundle_id_to_map dictionary and the TransformMap Class instances respectively
    """

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        **kwargs,
    ) -> pd.DataFrame:

        bundle_id_to_map = {
            # MyMarket Flows
            "mymarket-kytebroking-firm": [
                market_counterparty_kyte_transform_maps.market_counterparty_transform_map
            ],
            "mymarket-aladdin-broker-firm": [
                market_transform_maps.aladdin_broker_transform_map
            ],
            "mymarket-bp-person-processor": [
                market_transform_maps.bp_person_transform_map
            ],
            "mymarket-slack-suggestions-person-updater": [
                market_transform_maps.slack_suggestions_transform_map
            ],
            # Order Flows
            "order-bbg-emsi-processor": [
                order_transform_maps.bbg_audt_emsi_transform_map
            ],
            "order-feed-aladdin-processor": [
                order_transform_maps.aladdin_transform_map
            ],
            "order-feed-bbg-audt-processor": [
                se_trades_order_transform_maps.bbg_audt_map
            ],
            "order-feed-bbg-toms": [se_trades_order_transform_maps.bbg_toms_map],
            "order-feed-beacon-lighthouse": [
                order_transform_maps.beacon_lighthouse_transform_map
            ],
            "order-feed-charles-river-allocation-processor": [
                order_transform_maps.charles_river_transform_map
            ],
            "order-feed-charles-river-order-processor": [
                order_transform_maps.charles_river_transform_map
            ],
            "order-feed-cme-fix": [order_transform_maps.cme_fix_transform_map],
            "order-feed-cme-stp-fix": [order_transform_maps.cme_stp_transform_map],
            "order-feed-emsi-legacy-base": [
                se_trades_order_transform_maps.emsi_base_map
            ],
            "order-feed-emsx-fix": [order_transform_maps.emsx_fix_transform_map],
            "order-feed-enfusion-v2": [order_transform_maps.enfusion_v2_transform_map],
            "order-feed-expersoft-ubp": [
                order_transform_maps.expersoft_ubp_transform_map
            ],
            "order-feed-fidessa-front-office-trades": [
                order_transform_maps.fidessa_front_office_trades_transform_map
            ],
            "order-feed-fidessa-order-progress": [
                order_transform_maps.fidessa_order_progress_transform_map
            ],
            "order-feed-flextrade-cxl-processor": [
                order_transform_maps.flextrade_order_cxl_transform_map
            ],
            "order-feed-flextrade-exe-processor": [
                order_transform_maps.flextrade_order_exe_transform_map
            ],
            "order-feed-flextrade-controller-prim-rpl-processor": [
                order_transform_maps.flextrade_order_prim_rpl_transform_map
            ],
            "order-feed-ice-pof-fix": [order_transform_maps.ice_pof_transform_map],
            "order-feed-kooltra-monsas": [
                order_transform_maps.kooltra_monsas_transform_map,
            ],
            "order-feed-lme-eod": [order_transform_maps.lme_eod_transform_map],
            "order-feed-lme-fix": [order_transform_maps.lme_fix_transform_map],
            "order-feed-lme-select-fix": [
                se_trades_order_transform_maps.lme_select_fix_transform_map
            ],
            "order-feed-neovest-fix": [
                se_trades_order_transform_maps.neovest_fix_transform_map
            ],
            "order-feed-oanda-v20": [order_transform_maps.oanda_v20_transform_map],
            "order-feed-pershing-ubp": [
                order_transform_maps.pershing_ubp_transform_map
            ],
            "order-feed-reddeer-processor": [
                order_transform_maps.red_deer_transform_map
            ],
            "order-feed-saxo-bank": [
                order_transform_maps.saxo_bank_orders_transform_map
            ],
            "order-feed-samco-bbg-audt-processor": [
                order_transform_maps.shell_orders_transform_map
            ],
            "order-feed-thinkfolio": [order_transform_maps.thinkfolio_transform_map],
            "order-feed-tsox-fix": [order_transform_maps.ibp_tsox_fix_transform_map],
            "order-metatrader-mt4-trades": [
                order_transform_maps.metatrader_mt4_trades_transform_map
            ],
            "order-metatrader-mt5-deals": [
                order_transform_maps.metatrader_mt5_deals_transform_map
            ],
            "order-metatrader-mt5-orders": [
                order_transform_maps.metatrader_mt5_orders_transform_map
            ],
            "order-feed-eze-eclipse": [order_transform_maps.eze_eclipse_transform_map],
            "order-universal-steeleye-trade-blotter": [
                se_trades_order_transform_maps.order_blotter_map
            ],
            # Position Flows
            "position-universal-steeleye-blotter": [
                position_transform_maps.universal_positions_transform_map
            ],
            # Reference Flows
            "reference-instrument-se-cfdsb-commodity-transformer": [
                reference_transform_maps.cfdsb_commodity_transformer_transform_map
            ],
            "reference-instrument-se-cfdsb-index-transformer": [
                reference_transform_maps.cfdsb_index_transformer_transform_map
            ],
            # TR Flows
            "tr-feed-bbg-emsi-routes": [
                sett_rts22_transform_maps.bbg_emsi_routes_transform_map
            ],
            "tr-feed-emsi-legacy-base": [
                sett_rts22_transform_maps.emsi_base_transform_map
            ],
            "tr-feed-enfusion-v2-processor": [
                rts22_transform_maps.enfusion_v2_transform_map
            ],
            "tr-feed-julius-baer": [rts22_transform_maps.julius_baer_transform_map],
            "tr-feed-lme-processor": [rts22_transform_maps.lme_transform_map],
            "tr-feed-saxo-bank": [rts22_transform_maps.saxo_bank_transform_map],
            "tr-feed-thornbridge-crims": [
                sett_rts22_transform_maps.thornbridge_crims_rts22_transform_map
            ],
            "tr-feed-thornbridge-enfusion": [
                sett_rts22_transform_maps.thornbridge_enfusion_transform_map
            ],
            "tr-feed-thornbridge-interactive-brokers": [
                sett_rts22_transform_maps.thornbridge_interactive_brokers_transform_map
            ],
            "tr-feed-thornbridge-universum": [
                sett_rts22_transform_maps.thornbridge_universum_transform_map
            ],
            "tr-feed-unavista": [rts22_transform_maps.una_vista_transform_map],
            "tr-fix-universal": [sett_rts22_transform_maps.fix_universal_map],
        }

        transformations = bundle_id_to_map.get(Settings.bundle)
        results = list()
        for each in transformations:
            transformation = each.transformation(tenant=Settings.tenant)(
                source_frame=source_frame,
                auditor=self.auditor,
                realm=Settings.realm,
                **kwargs,
            )
            results.append(transformation.process())

        result = pd.concat(results, axis=1)
        return result
