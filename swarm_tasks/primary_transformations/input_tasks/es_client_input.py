from swarm.conf import Settings
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


class Resources(BaseResources):
    es_client_key: str


class EsClientInput(BaseTask):

    """
    this task serves to be used as an input to GetPrimaryTransformations whenever the
    transformations file sits outside swarm-tasks
    """

    resources_class = Resources

    def execute(self, resources: Resources, **kwargs):
        return self.process(resources)

    @classmethod
    def process(
        cls,
        resources: Resources,
    ):
        return Settings.connections.get(resources.es_client_key)
