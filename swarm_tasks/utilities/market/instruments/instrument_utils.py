from typing import Dict

import numpy as np
import pandas as pd
from se_trades_tasks.order_and_tr.static import AssetClass


class SecurityDescDelimiter:
    OPTIONS_DELIMITER = " "
    FUTURES_DELIMITER = "-"


class MapMonth:
    mapping: Dict = {
        "F": "01",
        "G": "02",
        "H": "03",
        "J": "04",
        "K": "05",
        "M": "06",
        "N": "07",
        "Q": "08",
        "U": "09",
        "V": "10",
        "X": "11",
        "Z": "12",
    }


class TempColumns:
    expiry_month_code = "expiry_month_code"
    expiry_year = "expiry_year"
    traded_month = "traded_month"
    traded_year = "traded_year"
    expiry_year_digit = "expiry_year_digit"


def get_expiry_year_digit(security_desc: str) -> str:
    expiry_year_digit = pd.NA
    if len(security_desc) >= 3:
        if security_desc[-2:].isdigit():
            expiry_year_digit = security_desc[-2:]
        elif security_desc[-1].isdigit():
            expiry_year_digit = security_desc[-1]

    return expiry_year_digit


def get_expiry_year(
    traded_year: int, traded_month: int, expiry_year_digit: str, expiry_month: str
) -> str:
    """
    :param traded_year: the year of the trade e.g. 2021
    :param traded_month: the month of the trade e.g. 06 (for June)
    :param expiry_year_digit: last or last two digits from security desc field
    :param expiry_month: expiry month mapped from Security Desc
    :return: returns the expiry year as a string. Example 2025
    Example: Trade date = 17-6-2023, security description (107) = ZK3
    Symbol     = Z
    Month (K)  = May
    Year       = 2033
    """
    if len(expiry_year_digit) == 2:
        probable_expiry_year = int(str(traded_year)[:-2] + expiry_year_digit)
    else:
        probable_expiry_year = int(str(traded_year)[:-1] + expiry_year_digit)
    offset = 0

    if (probable_expiry_year < traded_year) or (
        probable_expiry_year == traded_year and int(traded_month) > int(expiry_month)
    ):
        offset = 10 - (traded_year - probable_expiry_year)

    return str(probable_expiry_year + offset)


def get_expiry_date_from_desc(
    data: pd.DataFrame,
    trade_date_column: str,
    desc_column: str,
    target_expiry_date: str,
) -> pd.DataFrame:
    """
    This method calculates the expiry date based on the 107_SecurityDesc column
    specs: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2192408577/Task+Contract+Month+Codes+for+Symbols

    :param data: the dataframe received
    :param trade_date_column: The traded date column name. Should be converted to pd.Datetime
    :param desc_column: Name for the description column
    :param target_expiry_date: Name for the resultant expiry date column
    :return: returns the received dataframe with an additional expiry date column in the format YY-MM-DD
    """
    not_null_mask = ~(
        data[trade_date_column].isnull()
        | data[desc_column].isnull()
        | (data[desc_column].str.len() < 2)
    )
    data.loc[not_null_mask, TempColumns.expiry_month_code] = data.loc[
        not_null_mask, desc_column
    ].apply(
        lambda x: MapMonth.mapping.get(
            x[-2] if not x[-2].isdigit() else x[-3] if len(x) >= 3 else ""
        )
    )

    data.loc[not_null_mask, TempColumns.expiry_year_digit] = data.loc[
        not_null_mask, desc_column
    ].apply(lambda x: get_expiry_year_digit(x))

    # Skipping if all the data in the desc column did not produce
    # any valid month code or expiry year digit
    if all(
        data[TempColumns.expiry_month_code].isnull()
        | data[TempColumns.expiry_year_digit].isnull()
    ):
        temp_df = pd.DataFrame(index=data.index)
        temp_df = temp_df.assign(**{target_expiry_date: pd.NA})
        return temp_df

    data[TempColumns.traded_year] = (
        data[trade_date_column].fillna(pd.NaT).dt.year.fillna(0).astype(int)
    )
    data[TempColumns.traded_month] = (
        data[trade_date_column].fillna(pd.NaT).dt.month.fillna(0).astype(int)
    )

    cols = [
        TempColumns.traded_year,
        TempColumns.traded_month,
        TempColumns.expiry_year_digit,
        TempColumns.expiry_month_code,
    ]

    cols_mask = data.columns.isin(cols)
    data.loc[not_null_mask, TempColumns.expiry_year] = (
        data.loc[not_null_mask, cols_mask]
        .dropna()
        .apply(
            lambda x: get_expiry_year(
                traded_year=x[TempColumns.traded_year],
                traded_month=x[TempColumns.traded_month],
                expiry_year_digit=x[TempColumns.expiry_year_digit],
                expiry_month=x[TempColumns.expiry_month_code],
            ),
            axis=1,
        )
    )

    data.loc[not_null_mask, target_expiry_date] = (
        data.loc[
            not_null_mask, [TempColumns.expiry_year, TempColumns.expiry_month_code]
        ]
        .dropna()
        .apply(
            lambda x: (
                str(x[TempColumns.expiry_year])
                + "-"
                + str(x[TempColumns.expiry_month_code])
                + "-"
                + "01"
            ),
            axis=1,
        )
    )
    return data.loc[:, [target_expiry_date]]


def get_symbol_from_desc(
    data: pd.DataFrame,
    desc_column: str,
    target_symbol: str,
) -> pd.DataFrame:
    """
    This method calculates the symbol based on the 107_SecurityDesc column
    specs: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2192408577/Task+Contract+Month+Codes+for+Symbols

    :param data: the dataframe received
    :param desc_column: Name for the description column.
    :param target_symbol: Name for the resultant symbol column.
    :return: returns the received dataframe with an additional symbol column.
    """
    if all(data[desc_column].isnull()):
        temp_df = pd.DataFrame(index=data.index)
        temp_df = temp_df.assign(**{target_symbol: pd.NA})
        return temp_df

    data[target_symbol] = np.where(
        (data[desc_column].notnull() & (data[desc_column].str.len() >= 3)),
        np.select(
            [
                data[desc_column].str[-1:].str.isdigit().fillna(False).to_list(),
                data[desc_column].str[-2:].str.isdigit().fillna(False).to_list(),
            ],
            [data[desc_column].str[:-2], data[desc_column].str[:-3]],
            default=pd.NA,
        ),
        pd.NA,
    )

    return data.loc[:, [target_symbol]]


def get_option_type_from_desc(
    data: pd.DataFrame, desc_column: str, asset_class_column: str, target_option: str
) -> pd.DataFrame:
    """
    The option type to be determined in case asset class is option.
    The desc in case of option is expected to be " " seperated
    where the first character after the " " indicates the option-type
    Example:
        desc = OZBH2 C1710, asset_class="option"
        option_type = CALL (from the "C")

    :param data: the dataframe received
    :param desc_column: Name for the description column.
    :param asset_class_column: Name for the column containing the asset class information
    :param target_option: Name for the resultant option column.
    :return: returns the received dataframe with an additional option column.
    """
    if all(data[desc_column].isnull()):
        temp_df = pd.DataFrame(index=data.index)
        temp_df = temp_df.assign(**{target_option: pd.NA})
        return temp_df

    option_series = (
        data[desc_column]
        .str.split(SecurityDescDelimiter.OPTIONS_DELIMITER)
        .str.get(1)
        .fillna(pd.NA)
        .str.get(0)
    )
    data[target_option] = np.where(
        (
            data[desc_column].notnull()
            & (data[desc_column].str.len() >= 3)
            & (data[asset_class_column].str.lower() == AssetClass.OPTION)
        ),
        np.where(option_series.isin(["P", "C"]), option_series, pd.NA),
        pd.NA,
    )
    data[target_option] = data[target_option].map({"P": "PUT", "C": "CALL"})

    return data.loc[:, [target_option]]


def get_strike_price_from_desc(
    data: pd.DataFrame,
    desc_column: str,
    asset_class_column: str,
    target_strike_price: str,
) -> pd.DataFrame:
    """
    The strike price to be determined in case asset class is option.
    The desc_column in case of option is expected to be " " seperated
    where the number post the first character after the " " indicates the strike price
    Example:
    SecurityDesc = OZBH2 C1710, asset_class="option"
    Strike Price = 1710

    :param data: the dataframe received
    :param desc_column: Name for the description column.
    :param asset_class_column: Name for the column containing the asset class information
    :param target_strike_price: Name for the resultant strike price column.
    :return: returns the received dataframe with an additional strike price column.
    """
    if all(data[desc_column].isnull()):
        temp_df = pd.DataFrame(index=data.index)
        temp_df = temp_df.assign(**{target_strike_price: pd.NA})
        return temp_df

    option_series = (
        data[desc_column]
        .str.split(SecurityDescDelimiter.OPTIONS_DELIMITER)
        .str.get(1)
        .fillna(pd.NA)
        .str.get(0)
    )
    strike_price_series = (
        data[desc_column]
        .str.split(SecurityDescDelimiter.OPTIONS_DELIMITER)
        .str.get(1)
        .fillna(pd.NA)
        .str[1:]
    )
    data[target_strike_price] = np.where(
        (
            data[desc_column].notnull()
            & (data[desc_column].str.len() >= 3)
            & (data[asset_class_column].str.lower() == AssetClass.OPTION)
        ).fillna(False),
        np.where(
            (option_series.isin(["P", "C"]) & strike_price_series.str.isdigit()),
            strike_price_series,
            pd.NA,
        ),
        pd.NA,
    )

    return data.loc[:, [target_strike_price]]
