import json
from collections import namedtuple
from datetime import datetime
from typing import List
from typing import Op<PERSON>
from typing import Union
from urllib.parse import unquote_plus
from urllib.parse import urlparse

import boto3
from botocore.exceptions import ClientError
from prefect.engine.signals import SKIP
from se_core_tasks.utils.cloud import get_s3_client
from swarm.conf import Settings

S3Data = namedtuple("S3Data", ["s3_bucket", "s3_key"])


def s3_data_from_file_url(file_url: str) -> tuple:
    """
    :param file_url: a s3 url in the format s3://bucket/s3_key
    :return: a S3Data namedtuple with the bucket and key values
    """
    # we drop the first 5 characters (s3://)
    # then split once by / and assume the firs to be the bucket and
    # second to be the key
    s3_bucket, s3_key = file_url[5:].split("/", 1)
    return S3Data(s3_bucket=s3_bucket, s3_key=s3_key)


def does_key_exists(bucket: str, key: str) -> bool:
    """
    check if the key exists in given s3 bucket
    """
    s3_client = get_s3_client()
    try:
        s3_client.head_object(Bucket=bucket, Key=key)
        return True
    except ClientError as e:
        if e.response["Error"]["Code"] == "404":
            return False
        raise e


def get_last_modified_ts(s3_bucket: str, s3_key: str) -> Optional[datetime]:
    """
    :param s3_bucket: str
    :param s3_key: str
    :return: Optional[datetime]
    Returns the LastModified timestamp of the s3 object
    """
    s3_client = boto3.client("s3")
    try:
        s3_obj = s3_client.head_object(Bucket=s3_bucket, Key=s3_key)
    except ClientError:
        s3_obj = s3_client.head_object(Bucket=s3_bucket, Key=unquote_plus(s3_key))
    return s3_obj.get("LastModified")


def s3_bucket_from_url(s3_url: str) -> str:
    """
    :param s3_url: str
    :return: str
    Returns s3_bucket using the input s3_url
    """
    parsed_url = urlparse(s3_url)
    s3_bucket = parsed_url.netloc
    return s3_bucket


def s3_key_from_url(s3_url: str) -> str:
    """
    :param s3_url: str
    :return: str
    Returns s3_key using the input s3_url
    """
    parsed_url = urlparse(s3_url)
    # [1:] to remove leading slash from parsed_url.path
    s3_key = parsed_url.path[1:]
    return s3_key


def get_files_last_modified_timestamp(*file_keys: str) -> List[datetime]:
    """
    :param file_key: str
    :return: List with files last modified datetime
    """
    result = []
    for key in file_keys:
        s3_bucket = s3_bucket_from_url(s3_url=key)
        s3_key = s3_key_from_url(s3_url=key)
        file_last_modified = get_last_modified_ts(s3_bucket=s3_bucket, s3_key=s3_key)
        result.append(file_last_modified)

    return result


def load_json_from_s3(bucket: str, key: str) -> Union[dict, list]:
    """
    :param bucket: str
    :param key: str
    :return: Union[dict, list]
    Read json data from s3 bucket
    """
    s3 = boto3.client("s3")
    response = s3.get_object(Bucket=bucket, Key=key)
    config = response["Body"].read()
    return json.loads(config)


def write_json_data_to_s3(bucket: str, key: str, data: dict) -> None:
    """
    :param bucket: str
    :param key: str
    :param data: dict
    :return: None
    Writes json data to s3 bucket
    """
    s3 = boto3.resource("s3")
    s3object = s3.Object(bucket_name=bucket, key=key)
    s3object.put(Body=(bytes(json.dumps(data).encode("UTF-8"))))


def check_file_timestamps(
    file_url: str, pair_file_key: str, allow_equal: bool = False
) -> Optional[bool]:
    """
    :param file_url: str
    :param pair_file_key: str
    :param allow_equal: bool
    :return: None
    Check the LastModified timestamps of the source file and its pair and raise
    SKIP signal if the source file LastModified is  < pair file LastModified.
    Basically we continue processing only if the pair file was received before the
    source file.
    If allow equal and timestamps are the same, returns a bool
    """
    (
        source_file_last_modified,
        pair_file_last_modified,
    ) = get_files_last_modified_timestamp(file_url, pair_file_key)

    if (
        all([source_file_last_modified, pair_file_last_modified])
        and source_file_last_modified < pair_file_last_modified
    ):
        raise SKIP(
            f"Source file timestamp {source_file_last_modified} < than pair "
            f"file timestamp {pair_file_last_modified}"
        )
    elif allow_equal and (
        all([source_file_last_modified, pair_file_last_modified])
        and source_file_last_modified == pair_file_last_modified
    ):
        return True


def check_pair_file_on_s3(s3: boto3.resource, pair_file_key: str) -> None:
    """
    :param s3: s3 resource
    :param pair_file_key: str
    :return: None
    Tries to load the pair file using the pair file key.
    """
    s3.Object(Settings.realm, pair_file_key).load()
