import requests
from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter
from urllib3 import Re<PERSON>


def get_request_session(retry_count=3, back_off=0.5):
    if not isinstance(retry_count, int):
        retry_count = 3
    if not isinstance(back_off, float):
        back_off = 0.5
    retry_strategy = Retry(
        total=retry_count,
        backoff_factor=back_off,
        status_forcelist=[429, 500, 502, 503, 504],
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session = requests.Session()
    session.mount("https://", adapter)
    session.mount("http://", adapter)

    return session


def get(url, headers=None, session=None, **kwargs):
    if session is None:
        session = get_request_session()
    response = session.get(url, headers=headers, **kwargs)
    response.raise_for_status()
    return response


def post(
    url: str,
    headers: dict = None,
    session: requests.sessions.Session = None,
    **kwargs,
):
    if session is None:
        session = get_request_session()
    response = session.post(url, headers=headers, **kwargs)
    response.raise_for_status()
    return response
