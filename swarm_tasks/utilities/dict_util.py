from typing import Any
from typing import List

from swarm_tasks.utilities.data_util import abs_or_none
from swarm_tasks.utilities.data_util import double
from swarm_tasks.utilities.data_util import integer
from swarm_tasks.utilities.string_util import string


def strtobool(val):
    """Convert a string representation of truth to true (1) or false (0).

    True values are 'y', 'yes', 't', 'true', 'on', and '1'; false values
    are 'n', 'no', 'f', 'false', 'off', and '0'.  Raises ValueError if
    'val' is anything else.
    """
    val = val.lower()
    if val in ("y", "yes", "t", "true", "on", "1"):
        return 1
    elif val in ("n", "no", "f", "false", "off", "0"):
        return 0
    else:
        raise ValueError("invalid truth value %r" % (val,))


def cast_to_type(val: Any, typ: str) -> Any:
    """
    Cast value "val" to type "typ"
    :param val: value to be converted
    :param typ: string indicating data type
    :return: value "val" converted to "typ" data type
    """
    if typ == "str":
        return string(val)
    elif typ == "int":
        return integer(val)
    elif typ == "double":
        return double(val)
    elif typ == "abs":
        return abs_or_none(val)
    elif typ == "boolean":
        return bool(strtobool(val))
    else:
        return val


def stripper_list(v: List) -> List:
    """
    Remove empty iterables or None values from list
    :param v: List of values
    :return: list of values without empty iterables or None values
    """
    new_list = list()
    for item in v:
        if isinstance(item, dict):
            item = stripper(item)
        if item not in ("", None, {}):
            new_list.append(item)
    return new_list


def stripper(data: dict) -> dict:
    """
    Remove empty iterables or None values from dictionary
    :param data: dictionary
    :return: dictionary without empty iterables or None values from dictionary
    """
    new_data = {}
    for k, v in data.items():
        if isinstance(v, dict):
            v = stripper(v)
        if isinstance(v, list):
            v = stripper_list(v)
        if v not in ("", {}, []):
            new_data[k] = v
    return new_data
