import itertools
from typing import Callable
from typing import List
from typing import Optional
from typing import <PERSON>V<PERSON>
from typing import Union

import backoff
import pandas as pd
from elasticsearch6.exceptions import ConnectionError as ConnectionErrorES6
from elasticsearch7.exceptions import ConnectionError as ConnectionErrorES7
from elasticsearch8.exceptions import ConnectionError as ConnectionErrorES8


# Return type
R = TypeVar("R")


def get_terms_query(
    ids: List[str],
    es_client,
    lookup_field: str,
    model_field: str,
    source_field: Optional[Union[str, list]] = None,
) -> dict:
    """
    Returns a valid Elasticsearch terms query using the list of ids and a lookup field.
    It takes the max terms size into account and splits the query accordingly. It also
    takes an optional source field to restrict the results, and uses the schema model
    in the query as well

    :param ids: List of ids which are used in the query
    :param es_client: ElasticsearchClient
    :param source_field: Optional source field(s) used to restrict the results
    :param lookup_field: field which needs to be looked up
    :param model_field: the schema model
    :returns: Valid elasticsearch query which fetches <PERSON><PERSON>erson and/or
              <PERSON><PERSON><PERSON><PERSON><PERSON> records for the given ids.
    :rtype: dict
    """
    query = {
        "query": {
            "bool": {
                "filter": [{"term": {"&model": model_field}}],
                "minimum_should_match": 1,
                "must_not": {"exists": {"field": "&expiry"}},
            }
        }
    }

    if len(ids) > es_client.MAX_TERMS_SIZE:
        ids_chunks = [
            ids[ix : ix + es_client.MAX_TERMS_SIZE]
            for ix in range(0, len(ids), es_client.MAX_TERMS_SIZE)
        ]
        shoulds = [{"terms": {lookup_field: chunk}} for chunk in ids_chunks]
    else:
        shoulds = [{"terms": {lookup_field: ids}}]

    query["query"]["bool"]["should"] = shoulds
    if source_field:
        query["_source"] = source_field
    return query


def get_terms_query_multiple_lookup_fields(
    ids: List[str],
    es_client,
    lookup_fields: List[str],
    model_field: str,
    source_field: Optional[Union[str, list]] = None,
) -> dict:
    """
    Returns a valid Elasticsearch terms query using the list of ids and multiple lookup
    fields.
    It takes the max terms size into account and splits the query accordingly. It also
    takes an optional source field to restrict the results, and uses the schema model
    in the query as well

    :param ids: List of ids which are used in the query
    :param es_client: ElasticsearchClient
    :param source_field: Optional source field(s) used to restrict the results
    :param lookup_fields: list of fields which needs to be looked up
    :param model_field: the schema model
    :returns: Valid elasticsearch query which fetches MarketPerson and/or
              AccountPerson records for the given ids.
    :rtype: dict
    """
    query = {
        "query": {
            "bool": {
                "filter": [{"term": {"&model": model_field}}],
                "minimum_should_match": 1,
                "must_not": {"exists": {"field": "&expiry"}},
            }
        }
    }

    shoulds = []
    if len(ids) > es_client.MAX_TERMS_SIZE:
        ids_chunks = [
            ids[ix : ix + es_client.MAX_TERMS_SIZE]
            for ix in range(0, len(ids), es_client.MAX_TERMS_SIZE)
        ]
        for chunk, field in itertools.product(ids_chunks, lookup_fields):
            shoulds.append({"terms": {field: chunk}})
    else:
        shoulds.extend({"terms": {field: ids}} for field in lookup_fields)
    query["query"]["bool"]["should"] = shoulds
    if source_field:
        query["_source"] = source_field
    return query


def es_scroll(es_client, query: dict, index: str) -> pd.DataFrame:  # pragma: no cover
    """
    This method does an es_client.scroll for a given query on a given index
    and returns a dataframe with the result of the query

    :param es_client: the ES Client
    :param query: the ES query
    :param index: ES index
    :return: returns DataFrame upon query execution
    """
    df = es_client.scroll(query=query, index=index)
    return df


@backoff.on_exception(
    backoff.expo,
    (ConnectionErrorES6, ConnectionErrorES7, ConnectionErrorES8),
    max_tries=3,
)
def es_api_retriable_call(
    func: Callable[..., R], *args, **kwargs
) -> R:  # pragma: no cover
    """Calls passed ES function.

    :param func: The function to call
    :param args:
    :param kwargs:
        Additional arguments to pass to the function
    """
    return func(*args, **kwargs)
