from datetime import date
from datetime import datetime
from typing import Union

import pandas as pd
from pydantic import Field
from pydantic import root_validator
from pydantic import validator

from swarm_tasks.generic.flow_args_validator import AbstractFlowArgs


class FlowArgsModel(AbstractFlowArgs):
    es_start_time: Union[date, datetime] = Field(
        None,
        description="Start time to query records from",
    )

    es_end_time: Union[date, datetime] = Field(
        None,
        description="End time to query records to",
    )

    time_difference: str = Field(
        None,
        description="Time difference window since current time as a string literal "
        "that can be converted to a Pandas Timedelta object",
    )

    @root_validator(pre=True)
    def time_fields_validator(cls, values):
        if (
            not values.get("es_start_time")
            and values.get("es_end_time")
            or (values.get("es_start_time") and not values.get("es_end_time"))
        ):
            raise AttributeError(
                "For the pair or arguments: 'es_start_time', 'es_end_time';"
                "both arguments must be populated or none at all"
            )

        # static interval or time difference
        if (
            values.get("es_start_time")
            and values.get("es_end_time")
            and values.get("time_difference")
        ):
            raise AttributeError(
                "Flow args should only pass the pair of arguments: 'es_start_time', 'es_end_time';"
                "or the `time_difference`, not both"
            )

        if (
            not values.get("es_start_time")
            and not values.get("es_end_time")
            and not values.get("time_difference")
        ):
            raise AttributeError(
                "Flow args should pass the arguments: 'es_start_time', 'es_end_time' "
                "or `time_difference`"
            )
        return values

    @validator("time_difference")
    def validate_time_difference(cls, value):

        if value is not None:
            try:
                pd.Timedelta(value)
            except ValueError:
                raise ValueError(
                    "'time_difference' flow argument must be convertible to a Pandas Timedelta object (ex: '2 hours')"
                )
        return value
