import json
from datetime import date
from datetime import datetime
from pathlib import Path
from typing import Dict
from typing import List
from typing import Tu<PERSON>
from typing import Union

import pandas as pd
from pydantic import Field
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_core_tasks.utils.create_batches import create_batches
from swarm.conf import Settings
from swarm.schema.static import SwarmColumns
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask

from swarm_tasks.utilities.es.fetch_record_time_window.flow_args_model import (
    FlowArgsModel,
)


class Params(BaseParams):
    body: str = Field(
        ...,
        description="Query body with '%gte%' or '%lte%' to replace with timestamps",
    )
    # example body:
    # "{
    #     'sort': [{'&timestamp': {'order': 'asc'}}],
    #     'query': {
    #         'bool': {
    #             'must': [
    #                 {'wildcard': {'instrumentClassification.text': 'fc*'}},
    #                 {'terms': {'&model': ['FirdsInstrument', 'FcaFirdsInstrument']}}
    #                 ],
    #             'filter': {
    #                 'range': {
    #                     '&timestamp': {
    #                         'gte': '%gte%',
    #                         'lte': '%lte%',
    #                         'format': 'epoch_second'
    #                         }
    #                     }
    #                 }
    #             }
    #         }
    #     }"
    index: str = Field(
        ...,
        description="ES index to query. example index: '.firdsInstrument,.fcaFirdsInstrument'",
    )
    chunksize: int = Field(
        1000, description="Chunksize of the batches for downstream processing"
    )
    target_delimiter: str = Field(
        ",", description="Delimiter of the CSV created for batch processing"
    )


class Resources(BaseResources):
    es_key: str = "reference-data"


class FetchRecordTimeWindow(BaseTask):
    """
    This task will fetch records from elasticsearch based on a time interval
    """

    resources_class = Resources
    params_class = Params

    def execute(
        self,
        params: Params = None,
        resources: Resources = None,
        flow_args: FlowArgsModel = None,
        **kwargs,
    ) -> List[FileSplitterResult]:

        es_client = Settings.connections.get(resources.es_key)

        query = self.build_query(params=params, flow_args=flow_args)
        records = self.run_query(params=params, es=es_client, query=query)

        source_dir = Path(Settings.context.sources_dir).resolve()

        return create_batches(
            df=records,
            chunksize=params.chunksize,
            target_delimiter=params.target_delimiter,
            source_dir=source_dir,
            index_name=SwarmColumns.SWARM_RAW_INDEX,
        )

    def build_query(self, params: Params, flow_args: FlowArgsModel) -> Dict:
        """
        Creates the query to fetch the records, generating the correct time interval
        and replacing it in the `params.body`
        :param params: Params instance with `body`
        :param flow_args: FlowArgsModel instance with `time_difference` or `es_start_time` and `es_end_time`
        :return: query as a dictionary
        """
        start, end = self.set_start_end_time(flow_args=flow_args)

        replace_map = {
            "%gte%": start.strftime("%s%f")[:-3],
            "%lte%": end.strftime("%s%f")[:-3],
        }

        for key, val in replace_map.items():
            params.body = params.body.replace(key, val)
        es_query = json.loads(params.body.replace("'", '"'))

        return es_query

    @staticmethod
    def run_query(params: Params, es, query: Dict) -> pd.DataFrame:
        """
        Runs the built query to fetch all the intended records from elasticsearch
        :param params: Params instance with `index`
        :param es: ElasticsearchClient
        :param query: query to fetch the records by
        :return: Dataframe of the records for the respective query
        """
        return es.scroll(query=query, index=params.index)

    @staticmethod
    def set_start_end_time(
        flow_args: FlowArgsModel,
    ) -> Tuple[Union[date, datetime], Union[date, datetime]]:
        """
        Sets the start and end time for the query
        :param flow_args: FlowArgsModel instance with `time_difference` or `es_start_time` and `es_end_time`
        :return: start and end times
        """
        if flow_args.time_difference:
            end = pd.Timestamp.utcnow()
            start = end - pd.Timedelta(flow_args.time_difference)
            return start, end

        return flow_args.es_start_time, flow_args.es_end_time
