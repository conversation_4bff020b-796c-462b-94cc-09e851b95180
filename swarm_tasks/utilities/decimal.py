import numpy as np
import pandas as pd


def enforce_significant_figures(value: str, precision: int, scale: int) -> str:
    if value is None or pd.isnull(value):
        return np.nan

    whole_number, decimal = value.split(".") if "." in value else (value, "")

    decimal = decimal[:scale]

    if len(whole_number) >= precision:
        result = whole_number[:precision]
    else:
        # arbitrary 2dp if no decimal part was found
        decimal = "00" if not decimal else decimal
        result = ".".join([whole_number, decimal])
        result = result[: precision + 1]

    return result
