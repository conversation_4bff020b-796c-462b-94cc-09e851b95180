from typing import Any
from typing import Optional

import chardet


class Encodings:
    UTF8 = "utf-8"


# noinspection PyBroadException
def object2string(obj: Optional[Any], encoding="utf-8") -> Optional[str]:
    """Cast Any object to string, unless it's None

    :param obj: thing to stringify
    :param encoding: Optional, Defaults to 'utf-8'.

    :return: stringified object or None
    """
    if obj is None:
        return None

    try:
        val = str(obj, encoding)
    except Exception:
        try:
            encoding = chardet.detect(obj).get("encoding", "utf-8")
            val = str(obj, encoding)
        except Exception:
            val = str(obj)
    return val


# noinspection PyBroadException
def string(obj: Any, encoding: Optional[str] = None) -> Optional[str]:
    """Casts an object of any type to a string. If obj is of
    type bytes, it uses the encoding in the second parameter to
    decode the bytes to a string.

    :param obj: Any object: bytes, int, str, None, etc.
    :param encoding: name of encoding. If None, it uses the chardet module
                      to 'guess' the encoding (default: utf8)
    :return: the string version of the object
    """
    if obj is None:
        return None
    if isinstance(obj, bytearray) or isinstance(obj, bytes):
        if not encoding:
            encoding = chardet.detect(obj).get("encoding", Encodings.UTF8)
        try:
            val = str(obj, encoding)
        except Exception:
            val = str(obj)
        return val
    else:
        return str(obj)
