from typing import Any
from typing import Callable
from typing import Optional


class MultilegList(list):
    """
    Class to override index operator of builtin Python lists
    Indexing a MultilegList with a non-existent index will
    return None instead of raising an IndexError
    """

    def __getitem__(self, index: int) -> Optional[Any]:
        """
        Override MultiLegList index operator
        :param index: int index
        :return: get item at list index
        """
        # the repeating fix blocks may or may not contain entries for each field
        # so index errors need to be ignored in those cases
        try:
            return super(MultilegList, self).__getitem__(index)
        except IndexError:
            return None


class MultilegHandler:
    """
    Class to access Fix values of a given tag for MultiLeg messages.
    The Tags are stored in a MultilegList, thus calling a MultilegHandler object with a non-existent tag
    returns None instead of raising an IndexError.
    __index attribute defines which of the multiLeg instances will be accessed
    """

    def __init__(self, fget: Callable, *tags: int) -> None:
        """
        MultilegHandler constructor
        :param fget: Callable function to access fix fields
        :param tags: varying number of int params - fix field tags
        """
        self.__index = 0
        self.__tags = {t: MultilegList(fget(t, all_vals=True)) for t in tags}

    def __call__(self, tag: int) -> Optional[Any]:
        """
        MultilegHandler instance can be called as as function to return the fix field values of a param fix field tag
        :param tag: int fix field tag
        :return: fix field values of a param fix field tag
        """
        return self.__tags[tag][self.__index]

    def set_index(self, index: int) -> None:
        """
        Set class attribute's "__index" value equal to param "index"
        :param index: int
        """
        self.__index = index
