from copy import copy
from typing import Dict
from typing import List
from typing import Optional


def dictify(r, root=True) -> dict:
    """
    Convert an XML root Element to a dictionary
    :param r: XML Etree Element
    :param root:
    :return: XML as dict
    """
    if root:
        return {r.tag: dictify(r, False)}
    d = copy(r.attrib)
    if r.text:
        d["_text"] = r.text
    for x in r.findall("./*"):
        if x.tag not in d:
            d[x.tag] = []
        d[x.tag].append(dictify(x, False))
    return d


def parse_nested_xml(data_path: str, xml_content: Dict) -> List[Dict]:
    """
    Parse the contents of a nested XML file by flattening all nested tags (concatenate parent tags by "." separator)
    Example:
        data_path = "REPORT.DATA.ROW"
        xml_content = Dictionary from raw data:

        <REPORT xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <ISSUE_CODE>N</ISSUE_CODE>
            <DATA>
                <ROW>
                    <ISIN>GB00H1111111</ISIN>
                    <CONTRACT_TYPE>ALUM</CONTRACT_TYPE>
                </ROW>
            <ROW>
                <ISIN>GB00H2222222</ISIN>
                <CONTRACT_TYPE>ALUM</CONTRACT_TYPE>
            </ROW>

    Returns:
        [{"REPORT.DATA.ROW.ISIN":"GB00H1111111", "REPORT.DATA.ROW.CONTRACT_TYPE":"ALUM"},
        {"REPORT.DATA.ROW.ISIN":"GB00H2222222", "REPORT.DATA.ROW.CONTRACT_TYPE":"ALUM"}]

    :param data_path: Dot separated nested path towards the XML tag whose elements will be parsed
    :param xml_content: XML content as dictionary
    :return: List of flattened dictionaries, where each key:value pair is the flattened nested XML tag name
    (dot separated) and the associated value. The number of dictionaries is the same as the number of `data_path`
    XML Tag occurrences
    """

    data_path_nested_fields = data_path.split(".")
    nested_data = xml_content.get(data_path_nested_fields.pop(0), {})

    for item in data_path_nested_fields:
        nested_data = nested_data.get(item, {})

    if not isinstance(nested_data, list):
        nested_data = [nested_data]

    data = [
        flatten_dict(
            nested_xml_element, parent_key=data_path, xml_category_text_suffix=True
        )
        for nested_xml_element in nested_data
    ]

    return data


def flatten_dict(
    input_dict: Dict,
    output_dict: Optional[Dict] = None,
    parent_key: Optional[str] = None,
    separator=".",
    xml_category_text_suffix=False,
) -> Dict:
    """
    Flattens a nested dictionary - nested keys will always be prefixed by the parent key separated by `separator`

    :param input_dict: Nested dictionary to be flattened
    :param output_dict: Recursive call to one of the input_dict nested dictionaries
    :param parent_key: Parent key prefix that will be present in `output_dict`
    :param separator: Separator between parent and child dictionary keys
    :param xml_category_text_suffix: Hack to remove the #text suffix for XML elements with a specified category
    example - <NmnlVal Ccy="EUR">10159500</NmnlVal> (parsing this element generates the #text suffix for `NmlVal`)
    :return: Flattened dictionary
    """

    output_dict_result = {} if output_dict is None else output_dict

    for key, value in input_dict.items():
        key = f"{parent_key}{separator}{key}" if parent_key else key

        if isinstance(value, dict):
            flatten_dict(
                input_dict=value,
                output_dict=output_dict_result,
                parent_key=key,
                xml_category_text_suffix=xml_category_text_suffix,
            )
            continue

        key = key.replace(f"{separator}#text", "") if xml_category_text_suffix else key
        output_dict_result[key] = value

    return output_dict_result
