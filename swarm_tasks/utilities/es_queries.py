from typing import List


def add_terms_to_query_by_max_terms_size(
    query: dict,
    max_terms_size: int,
    terms_field: str,
    terms_list: List[str],
) -> dict:
    """
    Add the `terms` parameter to a ES query, accounting for the ES max terms size

    :param query: Dictionary with ElasticSearch query which will be updated to include the `terms` parameters
    :param max_terms_size: Max query `terms` size
    :param terms_field: Name of the field, which the elements of `terms_list` will be matched against
    :param terms_list: List of values which will be matched against `terms_field` on a `terms` parameter
    :return: `query` dictionary including the `terms` parameters
    """

    if len(terms_list) > max_terms_size:

        transaction_ids_chunks = [
            terms_list[ix : ix + max_terms_size]
            for ix in range(0, len(terms_list), max_terms_size)
        ]

        shoulds = [{"terms": {terms_field: chunk}} for chunk in transaction_ids_chunks]

        query["query"]["bool"]["should"] = shoulds
        query["query"]["bool"]["minimum_should_match"] = 1

    else:
        query["query"]["bool"]["filter"].append({"terms": {terms_field: terms_list}})

    return query
