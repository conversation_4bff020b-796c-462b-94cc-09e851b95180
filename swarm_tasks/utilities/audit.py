import logging
import re

from indict import Indict
from pydantic.error_wrappers import ValidationError as PydanticValidationError
from se_elastic_schema.models import Order
from se_elastic_schema.models import Position
from se_elastic_schema.models import RTS22Transaction
from se_trades_tasks.order.static import OrderColumns
from swarm.schema.task.auditor.model import AGGREGATION_DELIMITER
from swarm.task.auditor import Auditor

from swarm_tasks.position.static import PositionColumns
from swarm_tasks.steeleye.tr.static import RTS22Transaction as RTS22TransactionColumns

# Identifier mappings of each model.
# This would be propagated to the user in case the record fails model validation
# Note: All entries over here should be a list

ID_MAPPINGS = {
    Order.__name__: [OrderColumns.ID],
    RTS22Transaction.__name__: [
        RTS22TransactionColumns.REPORT_DETAILS_TRANSACTION_REF_NO
    ],
    Position.__name__: [
        "parties.value.&id",
        "instrumentDetails.instrument.instrumentIdCode",
        PositionColumns.SOURCE_INDEX,
    ],
}


def audit_pydantic_validation_errors(
    error: PydanticValidationError,
    raw_index: int,
    record: dict,
    auditor: Auditor,
    logger: logging.Logger,
):
    """
    This method audits the Pydantic Validation Errors for the record
    :param error: the actual error
    :param raw_index: index of the record
    :param record: Record from which certain id props might be fetched for audit purpose
    :param auditor: auditor object
    :param logger: logger object
    """
    errors = [
        # concatenate loc into field name (dot notation)
        f"{'.'.join([str(loc_val) for loc_val in x['loc']])} - {x['msg']}"
        for x in error.errors()
    ]
    audit_values = dict()
    if record:
        id_key_list = ID_MAPPINGS.get(error.model.__name__, [])
        for id_key in id_key_list:
            id_value = Indict(record).flatten().to_dict().get(id_key)
            if id_value:
                audit_values.update({id_key: id_value})

        # In case we have to fall back to index when id prop is not populated, giving first priority
        # to sourceIndex and then to raw index
        # as swarm raw index is not indicative of where the record originally was in the input file.
        if not audit_values:
            audit_values.update({"Index": record.get("sourceIndex", raw_index)})

    id_props_string = re.sub(r"[{}']", "", str(audit_values))
    auditor_ctx = {
        "error": f"{error.model.__name__} {AGGREGATION_DELIMITER} {errors} {AGGREGATION_DELIMITER} {id_props_string}",
        "raw_index": raw_index,
        "error_count": 1,
    }
    auditor.add(
        message="An error occurred during validation of a record",
        ctx=auditor_ctx,
    )
    logger.error(f"An error occurred during validation of a record: {auditor_ctx}")
