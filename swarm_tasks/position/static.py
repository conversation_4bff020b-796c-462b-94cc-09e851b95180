class PositionColumns:
    ADDITIONAL_INFORMATION = "additionalInformation"

    # Amounts
    AMOUNT_NATIVE = "amount.native"
    AMOUNT_NATIVE_CURRENCY = "amount.nativeCurrency"
    AMOUNT_CHF = "amount.amountInChf"
    AMOUNT_EUR = "amount.amountInEur"
    AMOUNT_GBP = "amount.amountInGbp"
    AMOUNT_JPY = "amount.amountInJpy"
    AMOUNT_USD = "amount.amountInUsd"
    DATA_SOURCE_NAME = "dataSourceName"
    DATE = "date"
    DIRECTION = "direction"
    # INSTRUMENT_DETAILS = "instrumentDetails"
    LEVEL = "level"
    MARKET_IDENTIFIERS = "marketIdentifiers"
    # The foll. 2 columns are temp columns which are populated in InstrumentIdentifiers ans PartyIdentifiers
    MARKET_IDENTIFIERS_INSTRUMENT = "marketIdentifiers.instrument"
    MARKET_IDENTIFIERS_PARTIES = "marketIdentifiers.parties"
    META_MODEL = "__meta_model__"
    PARTY_FILE_IDENTIFIER = "parties.fileIdentifier"
    PARTY_TYPE = "parties.type"
    PARTY_VALUE = "parties.value"

    # PNL Amounts
    PNL_AMOUNT_NATIVE = "pnlAmount.native"
    PNL_AMOUNT_NATIVE_CURRENCY = "pnlAmount.nativeCurrency"
    PNL_AMOUNT_CHF = "pnlAmount.amountInChf"
    PNL_AMOUNT_EUR = "pnlAmount.amountInEur"
    PNL_AMOUNT_GBP = "pnlAmount.amountInGbp"
    PNL_AMOUNT_JPY = "pnlAmount.amountInJpy"
    PNL_AMOUNT_USD = "pnlAmount.amountInUsd"

    QUANTITY = "quantity"
    QUANTITY_NOTATION = "quantityNotation"
    SOURCE_INDEX = "sourceIndex"
    SOURCE_KEY = "sourceKey"
