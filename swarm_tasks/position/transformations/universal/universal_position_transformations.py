import os

import pandas as pd
from se_elastic_schema.static.mifid2 import <PERSON>sitionLevel
from se_elastic_schema.static.mifid2 import PositionType
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_trades_tasks.order.best_execution.plugins.best_ex_fx_rates import (
    BestExFxRatesPlugin,
)
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.abstractions.transformations.position.abstract_position_transformations import (
    AbstractPositionTransformations,
)
from swarm_tasks.position.identifiers.position_party_identifiers import (
    Params as ParamsPositionPartyIdentifiers,
)
from swarm_tasks.position.identifiers.position_party_identifiers import (
    PositionPartyIdentifiers,
)
from swarm_tasks.position.static import PositionColumns
from swarm_tasks.position.transformations.universal.static import Currencies
from swarm_tasks.position.transformations.universal.static import SourceColumns
from swarm_tasks.position.transformations.universal.static import TempColumns
from swarm_tasks.position.transformations.universal.static import (
    UNIVERSAL_POSITION_BLOTTER,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.best_execution.utilities import fx_conversion
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)
from swarm_tasks.utilities.static import MetaModel


class UniversalPositionTransformations(AbstractPositionTransformations):
    def _pre_process(self):
        self.pre_process_df.loc[:, TempColumns.DATE] = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.DATE,
                target_attribute=TempColumns.DATE,
                convert_to="date",
            ),
        )
        ecb_rates_df = self._get_ecb_rates()
        # PNL Amount are supposed to be absolute values
        self.pre_process_df.loc[
            :, TempColumns.CONVERSIONS
        ] = self._get_amount_conversions(
            ecb_rates_df=ecb_rates_df,
            amount_col=SourceColumns.AMOUNT,
            convert_to_abs=True,
        )
        self.pre_process_df.loc[
            :, TempColumns.PNL_CONVERSIONS
        ] = self._get_amount_conversions(
            ecb_rates_df=ecb_rates_df, amount_col=SourceColumns.PNL_AMOUNT
        )

    def process(self):
        self.pre_process()
        self.additional_information()
        self.amount_native()
        self.amount_native_currency()
        self.date()
        self.amount_in_chf()
        self.amount_in_eur()
        self.amount_in_gbp()
        self.amount_in_jpy()
        self.amount_in_usd()
        self.data_source_name()
        self.direction()
        self.level()
        self.market_identifiers_instrument()
        self.market_identifiers_parties()
        self.market_identifiers()
        self.meta_model()
        self.party_type()
        self.pnl_amount_native()
        self.pnl_amount_native_currency()
        self.pnl_amount_in_chf()
        self.pnl_amount_in_eur()
        self.pnl_amount_in_gbp()
        self.pnl_amount_in_jpy()
        self.pnl_amount_in_usd()
        self.quantity()
        self.quantity_notation()
        self.source_index()
        self.source_key()
        self.post_process()
        return self.target_df

    def _post_process(self):
        # Get the parties names for PartyFallback from the marketIdentifiers.parties
        # Exploding since MARKET_IDENTIFIERS_PARTIES is a list with one element
        self.target_df.loc[:, TempColumns.PARTY] = (
            self.target_df.loc[:, PositionColumns.MARKET_IDENTIFIERS_PARTIES]
            .explode()
            .str.get("labelId")
        ).str.lstrip(f"^{PartyPrefix.ID}|{PartyPrefix.LEI}")
        self.target_df.loc[:, TempColumns.RIC] = self.source_frame.loc[
            :, SourceColumns.RIC
        ]
        self.target_df.loc[:, TempColumns.ISIN] = self.source_frame.loc[
            :, SourceColumns.ISIN
        ]

    def _additional_information(self) -> pd.DataFrame:
        """Returns a dataframe with additionalInfo column"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ADDITIONAL_INFORMATION].values,
            columns=[PositionColumns.ADDITIONAL_INFORMATION],
            index=self.source_frame.index,
        )

    def _amount_native(self) -> pd.DataFrame:
        """Returns a dataframe with amount.native column"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.CONVERSIONS]
            .str.get("native")
            .values,
            columns=[PositionColumns.AMOUNT_NATIVE],
            index=self.pre_process_df.index,
        )

    def _amount_native_currency(self) -> pd.DataFrame:
        """Returns a dataframe with amount.nativeCurrency column"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.CURRENCY].values,
            columns=[PositionColumns.AMOUNT_NATIVE_CURRENCY],
            index=self.source_frame.index,
        )

    def _amount_in_chf(self) -> pd.DataFrame:
        """Returns a dataframe with amount.amountInChf column"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.CONVERSIONS]
            .str.get("ecbRefRate")
            .str.get(Currencies.CHF)
            .values,
            columns=[PositionColumns.AMOUNT_CHF],
            index=self.pre_process_df.index,
        )

    def _amount_in_eur(self) -> pd.DataFrame:
        """Returns a dataframe with amount.amountInEur column"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.CONVERSIONS]
            .str.get("ecbRefRate")
            .str.get(Currencies.EUR)
            .values,
            columns=[PositionColumns.AMOUNT_EUR],
            index=self.pre_process_df.index,
        )

    def _amount_in_gbp(self) -> pd.DataFrame:
        """Returns a dataframe with amount.amountInGbp column"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.CONVERSIONS]
            .str.get("ecbRefRate")
            .str.get(Currencies.GBP)
            .values,
            columns=[PositionColumns.AMOUNT_GBP],
            index=self.pre_process_df.index,
        )

    def _amount_in_jpy(self) -> pd.DataFrame:
        """Returns a dataframe with amount.amountInJpy column"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.CONVERSIONS]
            .str.get("ecbRefRate")
            .str.get(Currencies.JPY)
            .values,
            columns=[PositionColumns.AMOUNT_JPY],
            index=self.pre_process_df.index,
        )

    def _amount_in_usd(self) -> pd.DataFrame:
        """Returns a dataframe with amount.amountInUsd column"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.CONVERSIONS]
            .str.get("ecbRefRate")
            .str.get(Currencies.USD)
            .values,
            columns=[PositionColumns.AMOUNT_USD],
            index=self.pre_process_df.index,
        )

    def _data_source_name(self) -> pd.DataFrame:
        """Returns a dataframe with dataSourceName column"""
        return pd.DataFrame(
            data=UNIVERSAL_POSITION_BLOTTER,
            columns=[PositionColumns.DATA_SOURCE_NAME],
            index=self.target_df.index,
        )

    def _date(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.DATE].values,
            columns=[PositionColumns.DATE],
            index=self.pre_process_df.index,
        )

    def _direction(self) -> pd.DataFrame:
        """Returns a data frame containing direction column"""
        value_map = {
            "L": PositionType.LONG.value,
            "LONG": PositionType.LONG.value,
            "SHORT": PositionType.SHORT.value,
            "S": PositionType.SHORT.value,
        }
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.DIRECTION,
                target_attribute=PositionColumns.DIRECTION,
                case_insensitive=True,
                value_map=value_map,
            ),
            auditor=self.auditor,
        )

    def _level(self) -> pd.DataFrame:
        """Returns a data frame containing level column"""
        value_map = {
            "account": PositionLevel.ACCOUNT.value,
            "account id": PositionLevel.ACCOUNT.value,
            "client": PositionLevel.CLIENT.value,
            "client id": PositionLevel.CLIENT.value,
            "fund": PositionLevel.FUND.value,
            "portfolio": PositionLevel.PORTFOLIO.value,
            "risk entity": PositionLevel.RISK_ENTITY.value,
            "risk_entity": PositionLevel.RISK_ENTITY,
            "trader": PositionLevel.TRADER.value,
            "risk-entity": PositionLevel.RISK_ENTITY,
        }
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.LEVEL,
                target_attribute=PositionColumns.LEVEL,
                case_insensitive=True,
                value_map=value_map,
            ),
            auditor=self.auditor,
        )

    def _market_identifiers(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_instrument() and _market_identifiers_parties() have been
        called earlier"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=PositionColumns.MARKET_IDENTIFIERS,
                instrument_path=PositionColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=PositionColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        return InstrumentIdentifiers.process(
            source_frame=self.source_frame,
            params=ParamsInstrumentIdentifiers(
                isin_attribute=SourceColumns.ISIN,
                retain_task_inputs=True,
            ),
            auditor=self.auditor,
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        temp_parties_df = pd.concat(
            [
                self.source_frame.loc[
                    :,
                    [
                        SourceColumns.ACCOUNT_ID,
                        SourceColumns.CLIENT_ID,
                        SourceColumns.FUND_ID,
                        SourceColumns.PORTFOLIO_MANAGER_ID,
                        SourceColumns.RISK_ENTITY_ID,
                        SourceColumns.TRADER_ID,
                    ],
                ],
                self.target_df.loc[:, PositionColumns.LEVEL],
            ],
            axis=1,
        )

        return PositionPartyIdentifiers.process(
            source_frame=temp_parties_df,
            params=ParamsPositionPartyIdentifiers(
                account_identifier=SourceColumns.ACCOUNT_ID,
                client_identifier=SourceColumns.CLIENT_ID,
                fund_identifier=SourceColumns.FUND_ID,
                portfolio_manager_identifier=SourceColumns.PORTFOLIO_MANAGER_ID,
                risk_entity_identifier=SourceColumns.RISK_ENTITY_ID,
                trader_identifier=SourceColumns.TRADER_ID,
                target_attribute=PositionColumns.MARKET_IDENTIFIERS_PARTIES,
                level=PositionColumns.LEVEL,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=MetaModel.POSITION,
            index=self.source_frame.index,
            columns=[PositionColumns.META_MODEL],
        )

    def _party_type(self):
        return pd.DataFrame(
            data=self.target_df.loc[:, PositionColumns.LEVEL].values,
            columns=[PositionColumns.PARTY_TYPE],
            index=self.source_frame.index,
        )

    def _pnl_amount_native(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.PNL_CONVERSIONS]
            .str.get("native")
            .values,
            columns=[PositionColumns.PNL_AMOUNT_NATIVE],
            index=self.pre_process_df.index,
        )

    def _pnl_amount_native_currency(self) -> pd.DataFrame:
        """Populates pnlAmount.nativeCurrency. But this should only be populated when
        a pnlAmount is provided, else it'd lead to schema validation error"""
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=PositionColumns.PNL_AMOUNT_NATIVE_CURRENCY,
                cases=[
                    {
                        "query": f"`{SourceColumns.PNL_AMOUNT}`.notnull()",
                        "attribute": SourceColumns.CURRENCY,
                    },
                ],
            ),
        )

    def _pnl_amount_in_chf(self) -> pd.DataFrame:
        """Returns a dataframe with pnlAmount.amountInChf column"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.PNL_CONVERSIONS]
            .str.get("ecbRefRate")
            .str.get(Currencies.CHF)
            .values,
            columns=[PositionColumns.PNL_AMOUNT_CHF],
            index=self.pre_process_df.index,
        )

    def _pnl_amount_in_eur(self) -> pd.DataFrame:
        """Returns a dataframe with pnlAmount.amountInEur column"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.PNL_CONVERSIONS]
            .str.get("ecbRefRate")
            .str.get(Currencies.EUR)
            .values,
            columns=[PositionColumns.PNL_AMOUNT_EUR],
            index=self.pre_process_df.index,
        )

    def _pnl_amount_in_gbp(self) -> pd.DataFrame:
        """Returns a dataframe with pnlAmount.amountInGbp column"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.PNL_CONVERSIONS]
            .str.get("ecbRefRate")
            .str.get(Currencies.GBP)
            .values,
            columns=[PositionColumns.PNL_AMOUNT_GBP],
            index=self.pre_process_df.index,
        )

    def _pnl_amount_in_jpy(self) -> pd.DataFrame:
        """Returns a dataframe with pnlAmount.amountInJpy column"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.PNL_CONVERSIONS]
            .str.get("ecbRefRate")
            .str.get(Currencies.JPY)
            .values,
            columns=[PositionColumns.PNL_AMOUNT_JPY],
            index=self.pre_process_df.index,
        )

    def _pnl_amount_in_usd(self) -> pd.DataFrame:
        """Returns a dataframe with pnlAmount.amountInUsd column"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.PNL_CONVERSIONS]
            .str.get("ecbRefRate")
            .str.get(Currencies.USD)
            .values,
            columns=[PositionColumns.PNL_AMOUNT_USD],
            index=self.pre_process_df.index,
        )

    def _quantity(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.QUANTITY].abs().values,
            columns=[PositionColumns.QUANTITY],
            index=self.source_frame.index,
        )

    def _quantity_notation(self) -> pd.DataFrame:
        value_map = {
            "UNIT": QuantityNotation.UNIT,
            "NOML": QuantityNotation.NOML,
            "MONE": QuantityNotation.MONE,
        }
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.QUANTITY_NOTATION,
                target_attribute=PositionColumns.QUANTITY_NOTATION,
                case_insensitive=True,
                value_map=value_map,
            ),
        )

    def _source_index(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceIndex column"""
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[PositionColumns.SOURCE_INDEX],
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceKey column from the SWARM_FILE_URL"""
        return pd.DataFrame(
            data=os.getenv("SWARM_FILE_URL"),
            index=self.source_frame.index,
            columns=[PositionColumns.SOURCE_KEY],
        )

    def _get_ecb_rates(self):
        min_date = self.pre_process_df.loc[:, TempColumns.DATE].dropna().min()
        prev_5_days_from_min = BestExFxRatesPlugin.previous_5_working_days(min_date)
        max_date = self.pre_process_df.loc[:, TempColumns.DATE].dropna().max()
        ecb_rates = BestExFxRatesPlugin.get_ecb_rates(
            from_date=prev_5_days_from_min, to_date=max_date
        ).content.get("results")
        result = BestExFxRatesPlugin._process_ecb_data(ecb_data=ecb_rates)
        return result

    def _get_amount_conversions(
        self, ecb_rates_df: pd.DataFrame, amount_col: str, convert_to_abs: bool = False
    ) -> pd.Series:
        if convert_to_abs:
            currency_series = self.source_frame.loc[:, amount_col].abs()
        else:
            currency_series = self.source_frame.loc[:, amount_col]
        if currency_series.dropna().empty:
            return pd.Series(data=pd.NA, index=self.pre_process_df.index)
        temp_df = pd.concat(
            [
                self.source_frame.loc[:, SourceColumns.CURRENCY],
                currency_series,
                self.pre_process_df.loc[:, TempColumns.DATE],
            ],
            axis=1,
        )
        return temp_df.dropna().apply(
            lambda x: fx_conversion(
                native=float(x[amount_col]),
                currency=x[SourceColumns.CURRENCY],
                date=x[TempColumns.DATE],
                fx=ecb_rates_df,
            ),
            axis=1,
        )
