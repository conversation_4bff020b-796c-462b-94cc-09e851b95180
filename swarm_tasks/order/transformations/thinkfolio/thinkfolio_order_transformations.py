import os

import pandas as pd
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_attribute import CastTo
from se_core_tasks.map.map_conditional import Case
from se_core_tasks.utils.datetime import DatetimeFormat
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import HierarchyEnum
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PassiveAggressiveStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_elastic_schema.static.mifid2 import ValidityPeriod
from se_elastic_schema.static.reference import OrderRecordType
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import AssetClass
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ConvertMinorToMajorParams,
)
from swarm_tasks.order.feed.thinkfolio.static import EquityColumns
from swarm_tasks.order.feed.thinkfolio.static import FileType
from swarm_tasks.order.feed.thinkfolio.static import FixedIncomeColumns
from swarm_tasks.order.feed.thinkfolio.static import FXColumns
from swarm_tasks.order.feed.thinkfolio.static import SharedColumns
from swarm_tasks.order.feed.thinkfolio.static import TempColumns
from swarm_tasks.order.feed.thinkfolio.utils import get_file_type
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.generic.get_tenant_lei import Params as GetTenantLEIParams
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ConcatAttributesParams,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ConvertDatetimeParams,
)
from swarm_tasks.transform.datetime.join_date_and_time import JoinDateAndTimeFormat
from swarm_tasks.transform.datetime.join_date_and_time import (
    Params as ParamsJoinDateAndTimeFormat,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as MapAttributeParams
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as MapConditionalParams
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as MapValueParams
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as MergeMarketIdentifiersParams,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as PartyIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as InstrumentIdentifiersParams,
)
from swarm_tasks.utilities.static import Delimiters
from swarm_tasks.utilities.static import MetaModel
from swarm_tasks.utilities.static import SWARM_FILE_URL


class ThinkfolioOrderTransformations(AbstractOrderTransformations):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.file_url = os.getenv(SWARM_FILE_URL)
        self.file_name = os.path.basename(self.file_url)
        self.file_type, _ = get_file_type(file_name=self.file_name)

    def _pre_process(self):
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_asset_class(),
                self._temp_trade_date(),
                self._temp_order_status_updated_parf(),
                self._temp_order_status_updated_newo(),
                self._temp_source_index(),
                self._temp_trader_parties(),
                self._temp_counterparty(),
                self._temp_full_name(),
                self._temp_instrument_classification(),
            ],
            axis=1,
        )
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_derivative_expiry_date(),
                self._temp_swap_near_leg_date(),
            ],
            axis=1,
        )

    def process(self):
        self.pre_process()
        self.buy_sell()
        self.data_source_name()
        self.date()
        self.execution_details_buy_sell_indicator()
        self.execution_details_limit_price()
        self.execution_details_order_status()
        self.execution_details_order_type()
        self.execution_details_passive_aggressive_indicator()
        self.execution_details_settlement_amount()
        self.execution_details_trading_capacity()
        self.execution_details_validity_period()
        self.hierarchy()
        self.order_identifiers_order_id_code()
        self.id()
        self.meta_model()
        self.order_identifiers_parent_order_id()
        self.order_identifiers_transaction_ref_no()
        self.price_forming_data_price()
        self.transaction_details_price_currency()
        self.transaction_details_quantity_currency()
        self.price_forming_data_traded_quantity()
        self.price_forming_data_initial_quantity()
        self.report_details_transaction_ref_no()
        self.source_index()
        self.source_key()
        self.timestamps_order_received()
        self.timestamps_order_status_updated()
        self.timestamps_order_submitted()
        self.timestamps_trading_date_time()
        self.transaction_details_buy_sell_indicator()
        self.transaction_details_commission_amount()
        self.transaction_details_commission_amount_currency()
        self.transaction_details_price()
        self.transaction_details_price_notation()
        self.transaction_details_quantity()
        self.transaction_details_quantity_notation()
        self.transaction_details_record_type()
        self.transaction_details_settlement_amount()
        self.transaction_details_trading_capacity()
        self.transaction_details_trading_date_time()
        self.transaction_details_ultimate_venue()
        self.transaction_details_venue()
        self.market_identifiers_instrument()
        self.market_identifiers_parties()
        self.market_identifiers()
        self.post_process()
        return self.target_df

    def _post_process(self):
        self.target_df = pd.concat(
            [
                self.target_df,
                pd.Series(
                    data=True,
                    index=self.source_frame.index,
                    name=TempColumns.IS_CREATED_THROUGH_FALLBACK,
                ),
                pd.Series(
                    data=False,
                    index=self.source_frame.index,
                    name=TempColumns.NEWO_IN_FILE,
                ),
                self._temp_full_name(),
                self._temp_instrument_classification(),
            ],
            axis=1,
        )

    def _buy_sell(self) -> pd.DataFrame:
        """
        Populates form `Side`
        """
        buy_sell_value_map = {"B": "1", "Buy": "1", "S": "2", "Sell": "2"}
        return pd.concat(
            [
                MapValue.process(
                    source_frame=self.source_frame,
                    params=MapValueParams(
                        source_attribute=SharedColumns.SIDE,
                        target_attribute=add_prefix(
                            ModelPrefix.ORDER, OrderColumns.BUY_SELL
                        ),
                        case_insensitive=True,
                        value_map=buy_sell_value_map,
                    ),
                ),
                MapValue.process(
                    source_frame=self.source_frame,
                    params=MapValueParams(
                        source_attribute=SharedColumns.SIDE,
                        target_attribute=add_prefix(
                            ModelPrefix.ORDER_STATE, OrderColumns.BUY_SELL
                        ),
                        case_insensitive=True,
                        value_map=buy_sell_value_map,
                    ),
                ),
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        """
        Populates with `Thinkfolio`
        """
        return pd.DataFrame(
            data="Thinkfolio",
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """
        Populates from:
            `Execution Time` for Equity files
            `Broker Date` for Fixed Income files
            'Trade Date' + 'Timestamp (GMT) for FX files
        """
        if self.file_type == FileType.FX:
            return pd.DataFrame(
                data=self.pre_process_df.loc[:, TempColumns.TRADE_DATE].values,
                index=self.pre_process_df.index,
                columns=[OrderColumns.DATE],
            )
        else:
            return ConvertDatetime.process(
                source_frame=self.pre_process_df,
                params=ConvertDatetimeParams(
                    source_attribute=TempColumns.ORDER_STATUS_UPDATED_PARF,
                    convert_to=ConvertTo.DATE,
                    target_attribute=OrderColumns.DATE,
                ),
            )

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Populates form `Side`
        """
        return MapValue.process(
            source_frame=self.target_df,
            params=MapValueParams(
                source_attribute=add_prefix(ModelPrefix.ORDER, OrderColumns.BUY_SELL),
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "1": BuySellIndicator.BUYI.value,
                    "2": BuySellIndicator.SELL.value,
                },
            ),
        )

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """
        Populates from `Execution Limit Price` using `Currency` as currency for Equity files
        """
        if self.file_type == FileType.EQUITY:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ConvertMinorToMajorParams(
                    source_ccy_attribute=EquityColumns.CURRENCY,
                    source_price_attribute=EquityColumns.ORDER_LIMIT_PRICE,
                    target_price_attribute=OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE,
                ),
            )

    def _execution_details_order_status(self) -> pd.DataFrame:
        """
        Populates with `NEWO` for Orders and `PARF` for OrderStates
        """
        return pd.DataFrame(
            data=[[OrderStatus.NEWO.value, OrderStatus.FILL.value]],
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    ModelPrefix.ORDER, OrderColumns.EXECUTION_DETAILS_ORDER_STATUS
                ),
                add_prefix(
                    ModelPrefix.ORDER_STATE, OrderColumns.EXECUTION_DETAILS_ORDER_STATUS
                ),
            ],
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """
        Populates with Market for FX, else from `Order Type`
        """
        if self.file_type == FileType.FX:
            return pd.DataFrame(
                data="MARKET",
                index=self.source_frame.index,
                columns=[OrderColumns.EXECUTION_DETAILS_ORDER_TYPE],
            )
        else:
            return pd.DataFrame(
                data=self.source_frame.loc[:, SharedColumns.ORDER_TYPE].values,
                index=self.source_frame.index,
                columns=[OrderColumns.EXECUTION_DETAILS_ORDER_TYPE],
            )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        """
        Populates from `Liquidity Flag` for Equity files
        """
        if self.file_type == FileType.EQUITY:
            return MapValue.process(
                source_frame=self.source_frame,
                params=MapValueParams(
                    source_attribute=EquityColumns.LIQUIDITY_FLAG,
                    value_map={
                        "1": PassiveAggressiveStatus.PASV.value,
                        "2": PassiveAggressiveStatus.AGRE.value,
                    },
                    target_attribute=OrderColumns.EXECUTION_DETAILS_PASSIVE_AGGRESSIVE_INDICATOR,
                ),
            )

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        """
        Populates from `Settlement Value Local CCY` for Fixed Income files
        Converts to absolute
        """
        if self.file_type == FileType.FIXED_INCOME:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=MapAttributeParams(
                    source_attribute=FixedIncomeColumns.SETTLEMENT_VALUE_LOCAL_CCY,
                    target_attribute=OrderColumns.EXECUTION_DETAILS_SETTLEMENT_AMOUNT,
                    cast_to=CastTo.NUMERIC_ABSOLUTE,
                ),
                auditor=self.auditor,
            )

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populates from `Capacity` or AOTC if FX
        else Capacity
        """
        if self.file_type == FileType.FX:
            return pd.DataFrame(
                data=TradingCapacity.AOTC.value,
                index=self.source_frame.index,
                columns=[OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY],
            )
        else:
            return pd.DataFrame(
                data=self.source_frame.loc[:, SharedColumns.CAPACITY].values,
                index=self.source_frame.index,
                columns=[OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY],
            )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """
        Populate from:
            `Time in Force` for Equity files
            `Time In Force` for Fixed Income files
        """
        if self.file_type == FileType.EQUITY:
            time_in_force_column = EquityColumns.TIME_IN_FORCE
        elif self.file_type == FileType.FIXED_INCOME:
            time_in_force_column = FixedIncomeColumns.TIME_IN_FORCE
        else:
            return pd.DataFrame()

        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=time_in_force_column,
                target_attribute=OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD,
                case_insensitive=True,
                value_map={
                    "Day": [ValidityPeriod.DAVY.value],
                    "GTC": [ValidityPeriod.GTCV.value],
                    "GTT": [ValidityPeriod.GTTV.value],
                    "GTD": [ValidityPeriod.GTDV.value],
                    "GTS": [ValidityPeriod.GTSV.value],
                    "GTX": [ValidityPeriod.GTXV.value],
                    "GAT": [ValidityPeriod.GATV.value],
                    "GAD": [ValidityPeriod.GADV.value],
                    "GAS": [ValidityPeriod.GASV.value],
                    "IOC": [ValidityPeriod.IOCV.value],
                    "FOK": [ValidityPeriod.FOKV.value],
                },
            ),
        )

    def _hierarchy(self) -> pd.DataFrame:
        """
        Populates with `Standalone`
        """
        return pd.DataFrame(
            data=HierarchyEnum.STANDALONE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.HIERARCHY],
        )

    def _id(self) -> pd.DataFrame:
        """
        Populates from:
            `Placement ID` for Equity files
            `Parent Order ID` for Fixed Income files
            'orderIdentifiers.orderIdCode' for FX
        """
        if self.file_type == FileType.EQUITY:
            return pd.concat(
                [
                    pd.DataFrame(
                        data=self.source_frame.loc[
                            :, SharedColumns.PLACEMENT_ID
                        ].values,
                        index=self.source_frame.index,
                        columns=[add_prefix(ModelPrefix.ORDER, OrderColumns.ID)],
                    ),
                    pd.DataFrame(
                        data=self.source_frame.loc[
                            :, SharedColumns.PLACEMENT_ID
                        ].values,
                        index=self.source_frame.index,
                        columns=[add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID)],
                    ),
                ],
                axis=1,
            )
        elif self.file_type == FileType.FIXED_INCOME:
            return pd.concat(
                [
                    pd.DataFrame(
                        data=self.source_frame.loc[
                            :, FixedIncomeColumns.ORDER_ID
                        ].values,
                        index=self.source_frame.index,
                        columns=[add_prefix(ModelPrefix.ORDER, OrderColumns.ID)],
                    ),
                    pd.DataFrame(
                        data=self.source_frame.loc[
                            :, FixedIncomeColumns.ORDER_ID
                        ].values,
                        index=self.source_frame.index,
                        columns=[add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID)],
                    ),
                ],
                axis=1,
            )
        elif self.file_type == FileType.FX:
            return pd.concat(
                [
                    pd.DataFrame(
                        data=self.target_df.loc[
                            :, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE
                        ].values,
                        index=self.source_frame.index,
                        columns=[add_prefix(ModelPrefix.ORDER, OrderColumns.ID)],
                    ),
                    pd.DataFrame(
                        data=self.target_df.loc[
                            :, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE
                        ].values,
                        index=self.source_frame.index,
                        columns=[add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID)],
                    ),
                ],
                axis=1,
            )

    def _market_identifiers(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_instrument() and _market_identifiers_parties() have been
        called earlier"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=MergeMarketIdentifiersParams(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """"""
        temp_df = pd.DataFrame(
            data=pd.NA,
            index=self.source_frame.index,
            columns=[
                TempColumns.TEMP_COL_1,
                TempColumns.TEMP_COL_2,
                TempColumns.ULTIMATE_VENUE,
                TempColumns.CLASSIFICATION,
                TempColumns.EXP_DATE,
                TempColumns.SWAP_NEAR_LEG_DATE,
            ],
        )

        if self.file_type == FileType.FX:
            temp_df.loc[:, TempColumns.TEMP_COL_2] = self.target_df.loc[
                :, OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY
            ]
            temp_df.loc[:, TempColumns.ULTIMATE_VENUE] = "XXXX"

            temp_df.loc[:, TempColumns.CLASSIFICATION] = self.source_frame.loc[
                :, FXColumns.CFI_CODE
            ].values
            temp_df.loc[:, TempColumns.EXP_DATE] = self.pre_process_df.loc[
                :, TempColumns.EXP_DATE
            ]
            temp_df.loc[:, TempColumns.SWAP_NEAR_LEG_DATE] = self.pre_process_df.loc[
                :, TempColumns.SWAP_NEAR_LEG_DATE
            ]
        else:
            temp_df.loc[:, TempColumns.TEMP_COL_1] = self.pre_process_df.loc[
                :, TempColumns.TEMP_COL_1
            ]
            temp_df.loc[:, TempColumns.ULTIMATE_VENUE] = self.target_df.loc[
                :, OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE
            ].values

        instruments_source_frame = pd.concat(
            [
                temp_df,
                self.target_df.loc[
                    :,
                    [
                        OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                        OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                    ],
                ],
                self.pre_process_df.loc[:, [TempColumns.ASSET_CLASS]],
            ],
            axis=1,
        )

        return InstrumentIdentifiers.process(
            source_frame=instruments_source_frame,
            params=InstrumentIdentifiersParams(
                asset_class_attribute=TempColumns.ASSET_CLASS,
                currency_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                notional_currency_2_attribute=TempColumns.TEMP_COL_2,
                venue_attribute=TempColumns.ULTIMATE_VENUE,
                isin_attribute=TempColumns.TEMP_COL_1,
                retain_task_inputs=True,
                instrument_classification_attribute=TempColumns.CLASSIFICATION,
                expiry_date_attribute=TempColumns.EXP_DATE,
                swap_near_leg_date_attribute=TempColumns.SWAP_NEAR_LEG_DATE,
            ),
            auditor=self.auditor,
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.parties by calling PartyIdentifiers"""

        parties_source_frame = pd.DataFrame()

        if self.file_type == FileType.EQUITY:
            parties_source_frame = pd.concat(
                [
                    self._get_executing_entity(),
                    pd.Series(
                        data=(
                            PartyPrefix.ID
                            + self.source_frame.loc[:, EquityColumns.TRADER]
                        ).values,
                        index=self.source_frame.index,
                        name=TempColumns.TRADER,
                    ),
                    pd.Series(
                        data=(
                            PartyPrefix.ID
                            + self.source_frame.loc[:, SharedColumns.BROKER]
                        ).values,
                        index=self.source_frame.index,
                        name=TempColumns.BROKER,
                    ),
                    pd.Series(
                        data=(
                            PartyPrefix.ID + self.source_frame.loc[:, EquityColumns.PM]
                        ).values,
                        index=self.source_frame.index,
                        name=TempColumns.CLIENT,
                    ),
                    pd.Series(
                        data=(
                            PartyPrefix.ID + self.source_frame.loc[:, EquityColumns.PM]
                        ).values,
                        index=self.source_frame.index,
                        name=TempColumns.INVESTMENT_DECISION,
                    ),
                    self.target_df.loc[
                        :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
                    ],
                ],
                axis=1,
            )

        elif self.file_type == FileType.FIXED_INCOME:
            parties_source_frame = pd.concat(
                [
                    self._get_executing_entity(),
                    pd.Series(
                        data=(
                            PartyPrefix.ID
                            + self.source_frame.loc[:, FixedIncomeColumns.TRADERID]
                        ).values,
                        index=self.source_frame.index,
                        name=TempColumns.TRADER,
                    ),
                    pd.Series(
                        data=(
                            PartyPrefix.ID
                            + self.source_frame.loc[:, FixedIncomeColumns.BROKER_ID]
                        ).values,
                        index=self.source_frame.index,
                        name=TempColumns.BROKER,
                    ),
                    pd.Series(
                        data=(
                            PartyPrefix.ID
                            + self.source_frame.loc[
                                :, FixedIncomeColumns.AUTHORISING_PM
                            ]
                        ).values,
                        index=self.source_frame.index,
                        name=TempColumns.CLIENT,
                    ),
                    pd.Series(
                        data=(
                            PartyPrefix.ID
                            + self.source_frame.loc[
                                :, FixedIncomeColumns.AUTHORISING_PM
                            ]
                        ).values,
                        index=self.source_frame.index,
                        name=TempColumns.INVESTMENT_DECISION,
                    ),
                    self.target_df.loc[
                        :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
                    ],
                ],
                axis=1,
            )
        elif self.file_type == FileType.FX:
            parties_source_frame = pd.concat(
                [
                    self._get_executing_entity(),
                    pd.Series(
                        data=(
                            PartyPrefix.ID
                            + self.pre_process_df.loc[:, TempColumns.FX_TRADER]
                        ).values,
                        index=self.pre_process_df.index,
                        name=TempColumns.TRADER,
                    ),
                    pd.Series(
                        data=(
                            PartyPrefix.ID
                            + self.pre_process_df.loc[:, TempColumns.FX_COUNTERPARTY]
                        ).values,
                        index=self.source_frame.index,
                        name=TempColumns.BROKER,
                    ),
                    pd.Series(
                        data=(
                            PartyPrefix.ID
                            + self.source_frame.loc[:, FXColumns.ACCOUNT_FUND]
                        ).values,
                        index=self.pre_process_df.index,
                        name=TempColumns.CLIENT,
                    ),
                    pd.Series(
                        data=(
                            PartyPrefix.ID
                            + self.pre_process_df.loc[:, TempColumns.FX_TRADER]
                        ).values,
                        index=self.pre_process_df.index,
                        name=TempColumns.INVESTMENT_DECISION,
                    ),
                    self.target_df.loc[
                        :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
                    ],
                ],
                axis=1,
            )

        return GenericOrderPartyIdentifiers.process(
            source_frame=parties_source_frame,
            params=PartyIdentifiersParams(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=TempColumns.EXECUTING_ENTITY_WITH_LEI,
                execution_within_firm_identifier=TempColumns.TRADER,
                trader_identifier=TempColumns.TRADER,
                client_identifier=TempColumns.CLIENT,
                counterparty_identifier=TempColumns.BROKER,
                investment_decision_within_firm_identifier=TempColumns.INVESTMENT_DECISION,
                buy_sell_side_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                buyer_identifier=TempColumns.EXECUTING_ENTITY_WITH_LEI,
                seller_identifier=TempColumns.BROKER,
                use_buy_mask_for_buyer_seller=True,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """
        Populates with `Order` and `OrderState`
        """
        return pd.DataFrame(
            data=[[MetaModel.ORDER, MetaModel.ORDER_STATE]],
            index=self.source_frame.index,
            columns=[
                add_prefix(ModelPrefix.ORDER, OrderColumns.META_MODEL),
                add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.META_MODEL),
            ],
        )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """Populates from `Parent Order ID` for Equity and Fixed Income
            For FX files, the Order ID code is a concatenation of `Trade ID`, `Account Fund`, and `Trade Date`,
        separated by underscores."""
        if self.file_type == FileType.EQUITY:
            return pd.DataFrame(
                data=self.source_frame.loc[:, SharedColumns.PLACEMENT_ID].values,
                index=self.source_frame.index,
                columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
            )
        elif self.file_type == FileType.FIXED_INCOME:
            return pd.DataFrame(
                data=self.source_frame.loc[:, FixedIncomeColumns.ORDER_ID].values,
                index=self.source_frame.index,
                columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
            )
        elif self.file_type == FileType.FX:
            return ConcatAttributes.process(
                source_frame=self.source_frame,
                params=ConcatAttributesParams(
                    source_attributes=[
                        FXColumns.TRADE_ID,
                        FXColumns.ACCOUNT_FUND,
                        SharedColumns.TRADE_DATE,
                    ],
                    delimiter=Delimiters.UNDERSCORE,
                    target_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
                ),
            )

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        """Populates from `Parent Order ID`"""
        if self.file_type == FileType.FX:
            return pd.DataFrame(
                data=FXColumns.TRADE_ID,
                index=self.source_frame.index,
                columns=[OrderColumns.ORDER_IDENTIFIERS_PARENT_ORDER_ID],
            )
        else:
            return pd.DataFrame(
                data=self.source_frame.loc[:, SharedColumns.PARENT_ORDER_ID].values,
                index=self.source_frame.index,
                columns=[OrderColumns.ORDER_IDENTIFIERS_PARENT_ORDER_ID],
            )

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """
        Populates from:
            `Placement ID` + `_` + `Execution Time` + `_` + `__source_index__` for Equity files
            `Order ID` + `_` + `Execution Time` + `_` + `__source_index__` for Fixed Income files
            `Trade ID` + `_` + `Timestamp` + `_` + `__source_index__` for FX files
        """
        temp_df = pd.DataFrame(
            data=pd.NA,
            index=self.source_frame.index,
            columns=[TempColumns.TEMP_COL_1, TempColumns.TEMP_COL_2],
        )

        if self.file_type == FileType.EQUITY:
            temp_df.loc[:, TempColumns.TEMP_COL_1] = self.source_frame.loc[
                :, SharedColumns.PLACEMENT_ID
            ]
            temp_df.loc[:, TempColumns.TEMP_COL_2] = self.source_frame.loc[
                :, SharedColumns.EXECUTION_TIME
            ]

        elif self.file_type == FileType.FIXED_INCOME:
            temp_df.loc[:, TempColumns.TEMP_COL_1] = self.source_frame.loc[
                :, FixedIncomeColumns.ORDER_ID
            ]
            temp_df.loc[:, TempColumns.TEMP_COL_2] = self.source_frame.loc[
                :, SharedColumns.EXECUTION_TIME
            ]

        elif self.file_type == FileType.FX:
            temp_df.loc[:, TempColumns.TEMP_COL_1] = self.source_frame.loc[
                :, FXColumns.TRADE_ID
            ]
            temp_df.loc[:, TempColumns.TEMP_COL_2] = self.source_frame.loc[
                :, FXColumns.TIMESTAMP
            ]

        return ConcatAttributes.process(
            source_frame=pd.concat(
                [temp_df, self.pre_process_df.loc[:, TempColumns.SOURCE_INDEX]],
                axis=1,
            ),
            params=ConcatAttributesParams(
                source_attributes=[
                    TempColumns.TEMP_COL_1,
                    TempColumns.TEMP_COL_2,
                    TempColumns.SOURCE_INDEX,
                ],
                delimiter=Delimiters.UNDERSCORE,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
            ),
        )

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """
        Populates from:
            `Placement Shares` for Equity files. Converts to absolute
            `Parent Order Size` for Fixed Income files. Converts to absolute
            `Buy Amount` or `Sell Amount` for FX files
        """
        if self.file_type == FileType.EQUITY:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=MapAttributeParams(
                    source_attribute=EquityColumns.PLACEMENT_SHARES,
                    target_attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                    cast_to=CastTo.NUMERIC_ABSOLUTE,
                ),
                auditor=self.auditor,
            )
        elif self.file_type == FileType.FIXED_INCOME:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=MapAttributeParams(
                    source_attribute=FixedIncomeColumns.PARENT_ORDER_SIZE,
                    target_attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                    cast_to=CastTo.NUMERIC_ABSOLUTE,
                ),
                auditor=self.auditor,
            )
        elif self.file_type == FileType.FX:
            return pd.DataFrame(
                data=self.target_df.loc[
                    :,
                    add_prefix(
                        ModelPrefix.ORDER_STATE,
                        OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                    ),
                ].values,
                index=self.source_frame.index,
                columns=[OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY],
            )

    def _price_forming_data_price(self) -> pd.DataFrame:
        """
        Populates from:
            `Last Px` using `Currency` as currency for Equity files
            'Local Unit Price` using `Local Currency Code` as currency for Fixed Income files
            'Outright Rate' using 'Ccy Intent' as currency for FX
        """
        if self.file_type == FileType.EQUITY:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ConvertMinorToMajorParams(
                    source_ccy_attribute=EquityColumns.CURRENCY,
                    source_price_attribute=EquityColumns.LAST_PX,
                    target_price_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                ),
            )
        elif self.file_type == FileType.FIXED_INCOME:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ConvertMinorToMajorParams(
                    source_ccy_attribute=FixedIncomeColumns.LOCAL_CURRENCY_CODE,
                    source_price_attribute=FixedIncomeColumns.LOCAL_UNIT_PRICE,
                    target_price_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                ),
            )
        elif self.file_type == FileType.FX:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ConvertMinorToMajorParams(
                    source_ccy_attribute=FXColumns.CCY_INTENT,
                    source_price_attribute=FXColumns.OUTRIGHT_RATE,
                    target_price_attribute=add_prefix(
                        ModelPrefix.ORDER_STATE, OrderColumns.PRICE_FORMING_DATA_PRICE
                    ),
                ),
            )

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """
        Populates from:
            `Last Qty` for Equity files. Converted to Absolute
            `Quantity Traded` for Fixed Income files. Converted to Absolute
            `Buy Amount` or `Sell Amount` for FX files
        """
        if self.file_type == FileType.EQUITY:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=MapAttributeParams(
                    source_attribute=EquityColumns.LAST_QTY,
                    target_attribute=add_prefix(
                        ModelPrefix.ORDER_STATE,
                        OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                    ),
                    cast_to=CastTo.NUMERIC_ABSOLUTE,
                ),
                auditor=self.auditor,
            )
        elif self.file_type == FileType.FIXED_INCOME:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=MapAttributeParams(
                    source_attribute=FixedIncomeColumns.QUANTITY_TRADED,
                    target_attribute=add_prefix(
                        ModelPrefix.ORDER_STATE,
                        OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                    ),
                    cast_to=CastTo.NUMERIC_ABSOLUTE,
                ),
                auditor=self.auditor,
            )
        elif self.file_type == FileType.FX:
            buy_ccy_mask = (
                self.target_df[OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY]
                == self.source_frame[FXColumns.BUY_CCY]
            )
            column_name_with_prefix = add_prefix(
                ModelPrefix.ORDER_STATE,
                OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
            )
            quantity_df = pd.DataFrame(
                index=self.target_df.index, columns=[column_name_with_prefix]
            )
            quantity_df.loc[
                buy_ccy_mask, column_name_with_prefix
            ] = self.source_frame.loc[buy_ccy_mask, FXColumns.BUY_AMOUNT]
            quantity_df.loc[
                ~buy_ccy_mask, column_name_with_prefix
            ] = self.source_frame.loc[~buy_ccy_mask, FXColumns.SELL_AMOUNT]
            return quantity_df

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """
        Populates from `Placement ID` + `Execution Time` + `|` + `__source_index__`
        """
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO],
        )

    def _source_index(self) -> pd.DataFrame:
        """Returns a dataframe containing the sourceIndex for each record"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.SOURCE_INDEX].values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_INDEX],
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceKey column from the SWARM_FILE_URL"""
        return pd.DataFrame(
            data=self.file_url,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_KEY],
        )

    def _timestamps_order_received(self) -> pd.DataFrame:
        """
        Populates from:
            `Execution Decision Time` for Equity files
            `Sent to ExtSys TS` for Fixed Income files
            `Trade Date` + `Timestamp (GMT) for FX files
        """
        if self.file_type == FileType.EQUITY:
            return pd.DataFrame(
                data=self.pre_process_df.loc[
                    :, TempColumns.ORDER_STATUS_UPDATED_NEWO
                ].values,
                index=self.source_frame.index,
                columns=[OrderColumns.TIMESTAMPS_ORDER_RECEIVED],
            )
        elif self.file_type == FileType.FIXED_INCOME:
            return ConvertDatetime.process(
                source_frame=self.source_frame,
                params=ConvertDatetimeParams(
                    source_attribute=FixedIncomeColumns.SENT_TO_EXTSYS_TS,
                    source_attribute_format="%Y-%m-%dT%H:%M:%S",
                    convert_to=ConvertTo.DATETIME,
                    target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                ),
            )
        elif self.file_type == FileType.FX:
            return JoinDateAndTimeFormat.process(
                source_frame=pd.concat(
                    [self.source_frame, self.pre_process_df], axis=1
                ),
                params=ParamsJoinDateAndTimeFormat(
                    source_date_attribute=TempColumns.TRADE_DATE,
                    source_time_attribute=FXColumns.TIMESTAMP,
                    source_format="%Y-%m-%d%H:%M:%S:%f",
                    target_format=DatetimeFormat.DATETIME,
                    target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                ),
            )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """
        Populates with:
            For Equity files:
                `Execution Decision Time` for NEWO
                `Execution Time` for PARF
            For Fixed Income files:
                `PM Order Creation TS` for NEWO
                `Broker Date` for PARF
            For FX files: Trade Date + timestamp (GMT)
        """
        if self.file_type == FileType.FX:
            return pd.DataFrame(
                data=self.target_df.loc[
                    :, OrderColumns.TIMESTAMPS_ORDER_RECEIVED
                ].values,
                index=self.target_df.index,
                columns=[OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED],
            )
        else:
            return pd.concat(
                [
                    pd.DataFrame(
                        data=self.pre_process_df.loc[
                            :, TempColumns.ORDER_STATUS_UPDATED_NEWO
                        ].values,
                        index=self.source_frame.index,
                        columns=[
                            add_prefix(
                                ModelPrefix.ORDER,
                                OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
                            )
                        ],
                    ),
                    pd.DataFrame(
                        data=self.pre_process_df.loc[
                            :, TempColumns.ORDER_STATUS_UPDATED_PARF
                        ].values,
                        index=self.source_frame.index,
                        columns=[
                            add_prefix(
                                ModelPrefix.ORDER_STATE,
                                OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
                            )
                        ],
                    ),
                ],
                axis=1,
            )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """
        Populates from:
            `PM Entry Time` for Equity files
            `PM Order Creation TS` for Fixed Income files
             For FX files: Trade Date + timestamp (GMT)

        """
        if self.file_type == FileType.EQUITY:
            return ConvertDatetime.process(
                source_frame=self.source_frame,
                params=ConvertDatetimeParams(
                    source_attribute=EquityColumns.PM_ENTRY_TIME,
                    source_attribute_format="%Y-%m-%dT%H:%M:%S",
                    convert_to=ConvertTo.DATETIME,
                    target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                ),
            )
        elif self.file_type == FileType.FIXED_INCOME:
            return pd.DataFrame(
                data=self.pre_process_df.loc[
                    :, TempColumns.ORDER_STATUS_UPDATED_NEWO
                ].values,
                index=self.source_frame.index,
                columns=[OrderColumns.TIMESTAMPS_ORDER_RECEIVED],
            )
        elif self.file_type == FileType.FX:
            return pd.DataFrame(
                data=self.target_df.loc[
                    :, OrderColumns.TIMESTAMPS_ORDER_RECEIVED
                ].values,
                index=self.target_df.index,
                columns=[OrderColumns.TIMESTAMPS_ORDER_SUBMITTED],
            )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """
        Populates from
            `Execution Time` for Equity files
            `Broker Date` for Fixed Income files
            `Trade Date + timestamp (GMT)` for FX files

        """
        if self.file_type == FileType.FX:
            return pd.DataFrame(
                data=self.target_df.loc[
                    :, OrderColumns.TIMESTAMPS_ORDER_RECEIVED
                ].values,
                index=self.target_df.index,
                columns=[
                    add_prefix(
                        ModelPrefix.ORDER_STATE,
                        OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
                    )
                ],
            )
        else:
            return pd.DataFrame(
                data=self.pre_process_df.loc[
                    :, TempColumns.ORDER_STATUS_UPDATED_PARF
                ].values,
                index=self.source_frame.index,
                columns=[
                    add_prefix(
                        ModelPrefix.ORDER_STATE,
                        OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
                    )
                ],
            )

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Populates form `Side`
        """
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        """
        Populates from `Commission` for Equity files
        """
        if self.file_type == FileType.EQUITY:
            return pd.DataFrame(
                data=self.source_frame.loc[:, EquityColumns.COMMISSION].values,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_COMMISSION_AMOUNT],
            )

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        """
        Populates from `Currency` for Equity files
        """
        if self.file_type == FileType.EQUITY:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ConvertMinorToMajorParams(
                    source_ccy_attribute=EquityColumns.CURRENCY,
                    target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_COMMISSION_AMOUNT_CURRENCY,
                ),
            )

    def _transaction_details_price(self) -> pd.DataFrame:
        """
        Populates from:
            `Last Px` using `Currency` as currency for Equity files
            'Local Unit Price` using `Local Currency Code` as currency for Fixed Income files
        """
        if self.file_type == FileType.FX:
            return pd.DataFrame(
                data=self.target_df.loc[
                    :,
                    add_prefix(
                        ModelPrefix.ORDER_STATE, OrderColumns.PRICE_FORMING_DATA_PRICE
                    ),
                ].values,
                index=self.source_frame.index,
                columns=[
                    add_prefix(
                        ModelPrefix.ORDER_STATE, OrderColumns.TRANSACTION_DETAILS_PRICE
                    )
                ],
            )
        else:
            return pd.DataFrame(
                data=self.target_df.loc[
                    :, OrderColumns.PRICE_FORMING_DATA_PRICE
                ].values,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_PRICE],
            )

    def _transaction_details_price_average(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """
        Populates from:
            `Currency` for Equity files
            `Local Currency Code` for Fixed Income files
            Populates from the first three characters of the `Rate Direction` column and performs minor-to-major currency conversion for FX files

        """
        if self.file_type == FileType.EQUITY:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ConvertMinorToMajorParams(
                    source_ccy_attribute=EquityColumns.CURRENCY,
                    target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                ),
            )
        elif self.file_type == FileType.FIXED_INCOME:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ConvertMinorToMajorParams(
                    source_ccy_attribute=FixedIncomeColumns.LOCAL_CURRENCY_CODE,
                    target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                ),
            )
        elif self.file_type == FileType.FX:
            temp_df = pd.DataFrame(
                data=self.source_frame[FXColumns.RATE_DIRECTION].str[0:3].values,
                index=self.source_frame.index,
                columns=[TempColumns.RATE_DIRECTION_LEFT],
            ).replace(to_replace="CNH", value="CNY", regex=True)
            return ConvertMinorToMajor.process(
                source_frame=temp_df,
                params=ConvertMinorToMajorParams(
                    source_ccy_attribute=TempColumns.RATE_DIRECTION_LEFT,
                    target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                ),
            )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """
        Populates with:
            'MONE' for Equity files and FX files
            'PERC' for Fixed Income Files
        """
        if self.file_type == FileType.EQUITY:
            return pd.DataFrame(
                data=PriceNotation.MONE.value,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION],
            )
        elif self.file_type == FileType.FIXED_INCOME:
            return pd.DataFrame(
                data=PriceNotation.PERC.value,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION],
            )
        elif self.file_type == FileType.FX:
            return pd.DataFrame(
                data=PriceNotation.MONE.value,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION],
            )

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """
        Populates from:
            `Last Qty` for Equity files. Converted to Absolute
            `Quantity Traded` for Fixed Income files. Converted to Absolute
            `Buy Amount` or `Sell Amount` for FX files
        """
        if self.file_type == FileType.FX:
            return pd.DataFrame(
                data=self.target_df.loc[
                    :,
                    add_prefix(
                        ModelPrefix.ORDER_STATE,
                        OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                    ),
                ].values,
                index=self.source_frame.index,
                columns=[
                    add_prefix(
                        ModelPrefix.ORDER_STATE,
                        OrderColumns.TRANSACTION_DETAILS_QUANTITY,
                    )
                ],
            )
        else:
            return pd.DataFrame(
                data=self.target_df.loc[
                    :,
                    add_prefix(
                        ModelPrefix.ORDER_STATE,
                        OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                    ),
                ].values,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY],
            )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """
        Populates with:
            'Rate Direction' Left for FX files
            `Currency` for Equity files
            `Local Currency Code` for Fixed Income files
        """
        if self.file_type == FileType.FX:
            temp_df = pd.DataFrame(
                data=self.source_frame[FXColumns.RATE_DIRECTION].str[-3:].values,
                index=self.source_frame.index,
                columns=[TempColumns.RATE_DIRECTION_RIGHT],
            ).replace(to_replace="CNH", value="CNY", regex=True)
            return ConvertMinorToMajor.process(
                source_frame=temp_df,
                params=ConvertMinorToMajorParams(
                    source_ccy_attribute=TempColumns.RATE_DIRECTION_RIGHT,
                    target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
                ),
            )
        else:
            return pd.DataFrame(
                data=self.target_df.loc[
                    :, OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
                ].values,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY],
            )

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """
        Populates with:
            "UNIT" for Equity files
            "NOML" for Fixed Income files
            "MONE" for FX files
        """
        if self.file_type == FileType.EQUITY:
            return pd.DataFrame(
                data=QuantityNotation.UNIT.value,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION],
            )
        elif self.file_type == FileType.FIXED_INCOME:
            return pd.DataFrame(
                data=QuantityNotation.NOML.value,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION],
            )
        elif self.file_type == FileType.FX:
            return pd.DataFrame(
                data=PriceNotation.MONE.value,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION],
            )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """
        Populates with `Market Side`
        """
        return pd.DataFrame(
            data=OrderRecordType.MARKET_SIDE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE],
        )

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        """
        Populates from `Settlement Value Local CCY` for Fixed Income files
        Converts to absolute
        """
        if self.file_type == FileType.FIXED_INCOME:
            return pd.DataFrame(
                data=self.target_df.loc[
                    :, OrderColumns.EXECUTION_DETAILS_SETTLEMENT_AMOUNT
                ].values,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_SETTLEMENT_AMOUNT],
            )

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populates form `Capacity`
        """
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """
        Populates from:
            'Trade Date' + 'Timestamp (GMT)' for FX
            `Execution Time` for Equity files
            `Broker Date` for Fixed Income files
        """
        if self.file_type == FileType.FX:
            return pd.DataFrame(
                data=self.target_df.loc[
                    :, OrderColumns.TIMESTAMPS_ORDER_RECEIVED
                ].values,
                index=self.target_df.index,
                columns=[
                    add_prefix(
                        ModelPrefix.ORDER_STATE,
                        OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
                    )
                ],
            )

        else:
            return pd.DataFrame(
                data=self.target_df.loc[
                    :,
                    add_prefix(
                        ModelPrefix.ORDER_STATE,
                        OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
                    ),
                ].values,
                index=self.source_frame.index,
                columns=[
                    add_prefix(
                        ModelPrefix.ORDER_STATE,
                        OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
                    )
                ],
            )

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """
        Populates with:
            `Last Market` for Equity files (hardcoded to XOFF when `Last Market` == 'OTC'
            Maps from `Venue` for Fixed Income files
            'Execution Type' for FX
        """

        if self.file_type == FileType.EQUITY:
            return MapConditional.process(
                source_frame=self.source_frame,
                params=MapConditionalParams(
                    target_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                    cases=[
                        Case(
                            query=f"~(`{EquityColumns.LAST_MARKET}`.str.fullmatch('OTC', case=False, na=False))",
                            attribute=EquityColumns.LAST_MARKET,
                        ),
                        Case(
                            query=f"`{EquityColumns.LAST_MARKET}`.str.fullmatch('OTC', case=False, na=False)",
                            value="XOFF",
                        ),
                    ],
                ),
            )
        elif self.file_type == FileType.FIXED_INCOME:
            return MapValue.process(
                source_frame=self.source_frame,
                params=MapValueParams(
                    source_attribute=FixedIncomeColumns.VENUE,
                    target_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                    case_insensitive=True,
                    value_map={
                        "ASH_TSOX_MAP": "BMTF",
                        "ASH_MARKET_AXESS": "MAEL",
                        "MANUAL TRADE": "XOFF",
                    },
                ),
            )
        elif self.file_type == FileType.FX:
            return pd.DataFrame(
                data=self.source_frame.loc[:, FXColumns.EXECUTION_TYPE].values,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE],
            )

    def _transaction_details_venue(self) -> pd.DataFrame:
        """
        Populates with:
            'Execution Type' for FX
            `Last Market` for Equity files
            Maps from `Venue` for Fixed Income files
        """
        if self.file_type == FileType.FX:
            return pd.DataFrame(
                data=self.source_frame.loc[:, FXColumns.EXECUTION_TYPE].values,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_VENUE],
            )
        else:
            return pd.DataFrame(
                data=self.target_df.loc[
                    :, OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE
                ].values,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_VENUE],
            )

    # Auxiliary methods
    def _get_executing_entity(self):
        """Gets the executing entity value to be used in PartyIdentifiers from the
        AccountFirm record"""
        return GetTenantLEI.process(
            source_frame=self.source_frame,
            params=GetTenantLEIParams(
                target_column_prefix=PartyPrefix.LEI,
                target_lei_column=TempColumns.EXECUTING_ENTITY_WITH_LEI,
            ),
        )

    def _temp_asset_class(self) -> pd.DataFrame:
        """Returns a dataframe containing the `__asset_class__`"""
        if self.file_type == FileType.EQUITY:
            return pd.DataFrame(
                data=AssetClass.EQUITY,
                index=self.source_frame.index,
                columns=[TempColumns.ASSET_CLASS],
            )
        elif self.file_type == FileType.FIXED_INCOME:
            return pd.DataFrame(
                data=AssetClass.BOND,
                index=self.source_frame.index,
                columns=[TempColumns.ASSET_CLASS],
            )
        elif self.file_type == FileType.FX:
            return MapConditional.process(
                source_frame=self.source_frame,
                params=MapConditionalParams(
                    target_attribute=TempColumns.ASSET_CLASS,
                    cases=[
                        Case(
                            query=f"(`{FXColumns.ASSET_CLASS_FX}`.str.fullmatch('FX SPOT', case=False, na=False)) and (`{FXColumns.CFI_CODE}`.str.startswith('sf',na=False))",
                            value=AssetClass.FX_SPOT,
                        ),
                        Case(
                            query=f"(`{FXColumns.ASSET_CLASS_FX}`.str.fullmatch('FX FORWARD',case=False, na=False))",
                            value=AssetClass.FX_FORWARD,
                        ),
                        Case(
                            query=f"(`{FXColumns.CFI_CODE}`.str.lower().str.startswith('sf',na=False))",
                            value=AssetClass.FX_SWAP,
                        ),
                    ],
                ),
            )

    def _temp_trade_date(self) -> pd.DataFrame:
        """
        returns 'Trade Date' + 'Timestamp (GMT' for FX files:
        """
        if self.file_type == FileType.FX:
            return ConvertDatetime.process(
                source_frame=self.source_frame,
                params=ConvertDatetimeParams(
                    source_attribute=SharedColumns.TRADE_DATE,
                    source_attribute_format="%Y-%m-%dT%H:%M:%S",
                    convert_to=ConvertTo.DATE,
                    target_attribute=TempColumns.TRADE_DATE,
                ),
            )

    def _temp_swap_near_leg_date(self) -> pd.DataFrame:
        """
            For FX files, populates the Swap Near Leg Date based on the Trade Date.

        :return:
        """
        if self.file_type == FileType.FX:
            return MapConditional.process(
                source_frame=pd.concat(
                    [self.pre_process_df, self.source_frame], axis=1
                ),
                params=MapConditionalParams(
                    target_attribute=TempColumns.SWAP_NEAR_LEG_DATE,
                    cases=[
                        Case(
                            query=f"(`{TempColumns.ASSET_CLASS}`.str.fullmatch('FX SWAP', case=False, na=False))",
                            attribute=SharedColumns.TRADE_DATE,
                        ),
                    ],
                ),
            )

    def _temp_derivative_expiry_date(self) -> pd.DataFrame:
        """
        For FX files, populates the derivative expiry date based on the value date and value date forward.
        """
        if self.file_type == FileType.FX:
            temp_df = pd.concat(
                objs=[
                    ConvertDatetime.process(
                        source_frame=self.source_frame,
                        params=ConvertDatetimeParams(
                            source_attribute=FXColumns.VALUE_DATE,
                            source_attribute_format="%Y-%m-%dT%H:%M:%S",
                            convert_to=ConvertTo.DATE,
                            target_attribute=TempColumns.VALUE_DATE,
                        ),
                    ),
                    ConvertDatetime.process(
                        source_frame=self.source_frame,
                        params=ConvertDatetimeParams(
                            source_attribute=FXColumns.VALUE_DATE_FWD,
                            source_attribute_format="%d/%m/%Y",
                            convert_to=ConvertTo.DATE,
                            target_attribute=TempColumns.VALUE_DATE_FWD,
                        ),
                    ),
                ],
                axis=1,
            )
            return MapConditional.process(
                source_frame=pd.concat([self.pre_process_df, temp_df], axis=1),
                params=MapConditionalParams(
                    target_attribute=TempColumns.EXP_DATE,
                    cases=[
                        Case(
                            query=f"(`{TempColumns.ASSET_CLASS}`.str.fullmatch('FX FORWARD', case=False, na=False))",
                            attribute=TempColumns.VALUE_DATE,
                        ),
                        Case(
                            query=f"(`{TempColumns.ASSET_CLASS}`.str.fullmatch('FX SWAP', case=False, na=False))",
                            attribute=TempColumns.VALUE_DATE_FWD,
                        ),
                    ],
                ),
            )

    def _temp_order_status_updated_parf(self) -> pd.DataFrame:
        """
        Populates for:
            transactionDetails.tradingDateTime
            timestamps.tradingDateTime
            date
        """
        if self.file_type == FileType.EQUITY:
            return ConvertDatetime.process(
                source_frame=self.source_frame,
                params=ConvertDatetimeParams(
                    source_attribute=SharedColumns.EXECUTION_TIME,
                    source_attribute_format="%Y-%m-%dT%H:%M:%S.%f",
                    convert_to=ConvertTo.DATETIME,
                    target_attribute=TempColumns.ORDER_STATUS_UPDATED_PARF,
                ),
            )
        elif self.file_type == FileType.FIXED_INCOME:
            return ConvertDatetime.process(
                source_frame=self.source_frame,
                params=ConvertDatetimeParams(
                    source_attribute=FixedIncomeColumns.BROKER_DATE,
                    source_attribute_format="%Y-%m-%dT%H:%M:%S",
                    convert_to=ConvertTo.DATETIME,
                    target_attribute=TempColumns.ORDER_STATUS_UPDATED_PARF,
                ),
            )

    def _temp_order_status_updated_newo(self) -> pd.DataFrame:
        """
        Populates for:
            timestamps.orderReceived for Equity files
            timestamps.orderSubmitted for Fixed Income files
            _order.timestamps.orderStatusUpdated
        """
        if self.file_type == FileType.EQUITY:
            return ConvertDatetime.process(
                source_frame=self.source_frame,
                params=ConvertDatetimeParams(
                    source_attribute=EquityColumns.EXECUTION_DECISION_TIME,
                    source_attribute_format="%Y-%m-%dT%H:%M:%S",
                    convert_to=ConvertTo.DATETIME,
                    target_attribute=TempColumns.ORDER_STATUS_UPDATED_NEWO,
                ),
            )
        elif self.file_type == FileType.FIXED_INCOME:
            return ConvertDatetime.process(
                source_frame=self.source_frame,
                params=ConvertDatetimeParams(
                    source_attribute=FixedIncomeColumns.PM_ORDER_CREATION_TS,
                    source_attribute_format="%Y-%m-%dT%H:%M:%S",
                    convert_to=ConvertTo.DATETIME,
                    target_attribute=TempColumns.ORDER_STATUS_UPDATED_NEWO,
                ),
            )

    def _temp_trader_parties(self) -> pd.DataFrame:
        """
        For FX files, populates the FX Trader attribute based on conditions.

        """
        if self.file_type == FileType.FX:
            return MapConditional.process(
                source_frame=self.source_frame.loc[
                    :, [FXColumns.TRADER_ID, SharedColumns.DEALING_DESK]
                ],
                params=MapConditionalParams(
                    target_attribute=TempColumns.FX_TRADER,
                    cases=[
                        Case(
                            query=f"`{FXColumns.TRADER_ID}`.notna()",
                            attribute=FXColumns.TRADER_ID,
                        ),
                        Case(
                            query=f"`{FXColumns.TRADER_ID}`.isna()",
                            attribute=SharedColumns.DEALING_DESK,
                        ),
                    ],
                ),
            )

    def _temp_counterparty(self) -> pd.DataFrame:
        """
        For FX files, populates the FX Counterparty attribute based on conditions.
        """
        if self.file_type == FileType.FX:
            return MapConditional.process(
                source_frame=self.source_frame.loc[
                    :, [SharedColumns.BROKER, FXColumns.CUSTODIAN]
                ],
                params=MapConditionalParams(
                    target_attribute=TempColumns.FX_COUNTERPARTY,
                    cases=[
                        Case(
                            query=f"`{SharedColumns.BROKER}`.notna()",
                            attribute=SharedColumns.BROKER,
                        ),
                        Case(
                            query=f"`{SharedColumns.BROKER}`.isna()",
                            attribute=FXColumns.CUSTODIAN,
                        ),
                    ],
                ),
            )

    def _temp_source_index(self) -> pd.DataFrame:
        """Returns a dataframe containing the sourceIndex for each record"""
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[TempColumns.SOURCE_INDEX],
        )

    def _temp_instrument_classification(self) -> pd.DataFrame:
        """
        For FX files, populates from `CFI code`. For other file types, the attribute is set to NA.
        """
        if self.file_type == FileType.FX:
            return pd.DataFrame(
                data=self.source_frame.loc[:, FXColumns.CFI_CODE].values,
                index=self.source_frame.index,
                columns=[TempColumns.CLASSIFICATION],
            )
        else:
            return pd.DataFrame(
                data=pd.NA,
                index=self.source_frame.index,
                columns=[TempColumns.CLASSIFICATION],
            )

    def _temp_full_name(self) -> pd.DataFrame:
        """
        Populates the Full Name attribute based on the file type and available data.

        For FX files, the attribute is a concatenation of Rate Direction and Asset Class separated by underscores.
        For Equity files, the attribute is populated from SISIN.
        For Fixed Income files, the attribute is populated from ISIN.
        """
        if self.file_type == FileType.FX:
            return ConcatAttributes.process(
                source_frame=pd.concat(
                    [
                        self.source_frame,
                        self.pre_process_df,
                    ],
                    axis=1,
                ),
                params=ConcatAttributesParams(
                    source_attributes=[
                        FXColumns.RATE_DIRECTION,
                        TempColumns.ASSET_CLASS,
                    ],
                    delimiter=Delimiters.UNDERSCORE,
                    target_attribute=TempColumns.FULL_NAME,
                ),
            )
        elif self.file_type == FileType.EQUITY:
            self.pre_process_df.loc[:, TempColumns.TEMP_COL_1] = self.source_frame.loc[
                :, EquityColumns.SISIN
            ]
        elif self.file_type == FileType.FIXED_INCOME:
            self.pre_process_df.loc[:, TempColumns.TEMP_COL_1] = self.source_frame.loc[
                :, FixedIncomeColumns.ISIN
            ]

    # Currently unused fields
    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_stop_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _financing_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_discretionary(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_iceberg(self):
        """Not Implemented"""

    def _is_repo(self):
        """Not Implemented"""

    def _is_synthetic(self):
        """Not Implemented"""

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        """Not Implemented"""

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""

    def _order_class(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_validity_period(self) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """Not Implemented"""
