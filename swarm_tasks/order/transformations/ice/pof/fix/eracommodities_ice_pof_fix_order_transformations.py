import pandas as pd
from se_core_tasks.feeds.generic.get_tenant_lei import Params as ParamsGetTenantLEI
from se_core_tasks.feeds.generic.get_tenant_lei import run_get_tenant_lei
from se_core_tasks.map.map_conditional import Case
from se_core_tasks.map.map_conditional import Params as ParamsMapConditional
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.order.transformations.ice.pof.fix.ice_pof_fix_order_transformations import (
    IcePofFixOrderTransformations,
)
from swarm_tasks.order.transformations.ice.pof.fix.static import SourceColumns
from swarm_tasks.order.transformations.ice.pof.fix.static import TempColumns
from swarm_tasks.transform.map.map_conditional import MapConditional


class EraCommoditiesIcePofFixOrderTransformations(IcePofFixOrderTransformations):
    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY"""
        temp_df = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY,
                cases=[
                    Case(
                        query=f"(`{SourceColumns.TRD_TYPE}`.astype('str').str.fullmatch('K', case=False, na=False))",
                        attribute=SourceColumns.FF_9066,
                    )
                ],
            ),
        )

        # Keep only 50 characters
        temp_df[OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY] = (
            temp_df[OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY]
            .astype("str")
            .str[:50]
        )
        return temp_df

    def _get_party_identifiers_source_fields(self) -> pd.DataFrame:
        """Gets the fields to be used for PartyIdentifiers (with prefix).
        PartyIdentifiers is called downstream after splitting multi-legs into
        single legs in the FetchVDIAndSplitMultiLegTrades task. The reason for
        this is that multi-legs are split and the final multi-leg buySellIndicator
        is obtained from the Venue Direct instrument in this task.
        This buySellIndicator is then used in PartyIdentifiers to flip the buyer
        and seller in Sell trades.
        """
        seller = pd.DataFrame(
            data=f"{PartyPrefix.ID}ICE",
            index=self.source_frame.index,
            columns=[TempColumns.SELLER],
        )
        buyer = run_get_tenant_lei(
            source_frame=self.source_frame,
            params=ParamsGetTenantLEI(
                target_lei_column=TempColumns.BUYER,
                target_column_prefix=PartyPrefix.LEI,
            ),
            tenant=self.tenant,
            es_client=self.es_client,
        )
        executing_entity = pd.DataFrame(
            data=PartyPrefix.ID
            + self.source_frame.loc[:, SourceColumns.TARGET_SUB_ID].values,
            index=self.source_frame.index,
            columns=[TempColumns.EXECUTING_ENTITY],
        )
        investment_decision = pd.DataFrame(
            data=PartyPrefix.ID
            + self.source_frame.loc[:, SourceColumns.FF_9704].values,
            index=self.source_frame.index,
            columns=[TempColumns.INVESTMENT_DECISION],
        )
        trader = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TRADER,
                cases=[
                    {
                        "query": f"`{SourceColumns.ON_BEHALF_OF_SUB_ID}`.notnull()",
                        "attribute": SourceColumns.ON_BEHALF_OF_SUB_ID,
                        "attribute_prefix": PartyPrefix.ID,
                    },
                    {
                        "query": f"`{SourceColumns.ON_BEHALF_OF_SUB_ID}`.isnull()",
                        "attribute": SourceColumns.FF_9139,
                        "attribute_prefix": PartyPrefix.ID,
                    },
                ],
            ),
        )
        execution_within_firm = pd.DataFrame(
            data=trader.loc[:, TempColumns.TRADER].values,
            index=trader.index,
            columns=[TempColumns.EXECUTION_WITHIN_FIRM],
        )
        parties_source_frame = pd.concat(
            [
                buyer,
                seller,
                executing_entity,
                execution_within_firm,
                investment_decision,
                trader,
            ],
            axis=1,
        )
        return parties_source_frame
