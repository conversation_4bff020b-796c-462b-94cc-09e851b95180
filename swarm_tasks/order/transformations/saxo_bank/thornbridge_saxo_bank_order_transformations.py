import pandas as pd
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.order.transformations.saxo_bank.saxo_bank_order_transformations import (
    SaxoBankOrderTransformations,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)


class ThornbridgeTempColumns:
    THORNBRIDGE = "__thornbridge__"
    SAXO_BANK = "__saxo_bank__"
    CROSSROADS = "__crossroads__"
    LARS_WIND = "__lars_wind__"


class ThornbridgePartyIDs:
    THORNBRIDGE = "2138008NEORY2DQV4R24"
    SAXO_BANK = "549300TL5406IC1XKD09"
    CROSSROADS = "254900LFCDLGD2NHGM50"
    LARS_WIND = "Lars Wind"


class ThornbridgeSaxoBankOrderTransformations(SaxoBankOrderTransformations):
    """
    Thornbridge overrides for SaxoBank Order transformations
    Only PartyIdentifiers are changed
    """

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """
        Creates PartyIdentifiers results for Thornbridge
        Assumes _transaction_details_buy_sell_indicator() has run successfully
        """
        parties_df = pd.DataFrame(index=self.source_frame.index)
        parties_df.loc[
            :, ThornbridgeTempColumns.THORNBRIDGE
        ] = f"{PartyPrefix.LEI}{ThornbridgePartyIDs.THORNBRIDGE}"
        parties_df.loc[
            :, ThornbridgeTempColumns.SAXO_BANK
        ] = f"{PartyPrefix.LEI}{ThornbridgePartyIDs.SAXO_BANK}"
        parties_df.loc[
            :, ThornbridgeTempColumns.CROSSROADS
        ] = f"{PartyPrefix.LEI}{ThornbridgePartyIDs.CROSSROADS}"
        parties_df.loc[
            :, ThornbridgeTempColumns.LARS_WIND
        ] = f"{PartyPrefix.ID}{ThornbridgePartyIDs.LARS_WIND}"

        return GenericOrderPartyIdentifiers.process(
            source_frame=pd.concat(
                [
                    parties_df,
                    self.target_df.loc[
                        :, [OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR]
                    ],
                ],
                axis=1,
            ),
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=ThornbridgeTempColumns.THORNBRIDGE,
                counterparty_identifier=ThornbridgeTempColumns.SAXO_BANK,
                buy_sell_side_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                buyer_identifier=ThornbridgeTempColumns.CROSSROADS,
                seller_identifier=ThornbridgeTempColumns.SAXO_BANK,
                buyer_decision_maker_identifier=ThornbridgeTempColumns.THORNBRIDGE,
                investment_decision_within_firm_identifier=ThornbridgeTempColumns.LARS_WIND,
                execution_within_firm_identifier=ThornbridgeTempColumns.LARS_WIND,
                use_buy_mask_for_buyer_seller=True,
            ),
        )
