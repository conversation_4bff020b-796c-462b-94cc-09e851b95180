import os

import pandas as pd
from prefect.engine import signals
from se_core_tasks.currency.convert_minor_to_major import CastTo
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_conditional import Case
from se_core_tasks.map.map_value import RegexReplaceMap
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import HierarchyEnum
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.party.utils import add_id_or_lei
from se_trades_tasks.order_and_tr.static import AssetClass

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)
from swarm_tasks.utilities.static import SWARM_FILE_URL


class SourceColumns:
    ACCOUNT_CURRENCY = "AccountCurrency"
    BUY_SELL = "BuySell"
    CALL_PUT = "CallPut"
    COUNTERPART_ID = "CounterpartID"
    DIRTY_PRICE = "DirtyPrice"
    EXECUTING_ENTITY_IDENTIFICATION_CODE = "ExecutingEntityIdentificationCode"
    EXPIRY_DATE = "ExpiryDate"
    FXTYPE = "FXType"
    ISIN = "ISIN"
    ISIN_CODE = "ISINCode"
    ISO_MIC = "ISOMic"
    INSTRUMENT_CODE = "InstrumentCode"
    INSTRUMENT_CURRENCY = "InstrumentCurrency"
    INSTRUMENT_ISIN_CODE = "InstrumentISINCode"
    INSTRUMENT_TYPE = "InstrumentType"
    NET_AMOUNT = "NetAmount"
    ORDER_NUMBER = "OrderNumber"
    ORDER_TYPE = "OrderType"
    PRICE = "Price"
    STRIKE = "Strike"
    TERM_OF_UNDERLYING_INDEX = "TermOfUnderlyingIndex"
    TRADE_EXECUTION_TIME = "TradeExecutionTime"
    TRADE_NUMBER = "TradeNumber"
    TRADE_TIME = "TradeTime"
    TRADED_AMOUNT = "TradedAmount"
    UNDERLYING_INSTRUMENT_CODE = "UnderlyingInstrumentCode"
    UNDERLYING_INSTRUMENT_ISIN_CODE = "UnderlyingInstrumentISINCode"
    USER_ID_ON_TRADE = "UserIdOnTrade"
    VENUE = "Venue"


class TempColumns:
    ASSET_CLASS = "__asset_class__"
    COUNTERPARTY = "__counterparty__"
    EXECUTING_ENTITY = "__exec_entity__"
    EXPIRY_DATE = "__expiry_date__"
    ISIN = "__isin__"
    OPTION_STRIKE_PRICE = "__option_strike_price__"
    OPTION_TYPE = "__option_type__"
    UNDERLYING_INDEX_NAME = "__underlying_index_name__"
    UNDERLYING_INDEX_TERM = "__underlying_index_term__"
    UNDERLYING_ISIN = "__underlying_isin__"
    UNDERLYING_SYMBOL = "__underlying_symbol__"
    VENUE = "__venue__"
    TRADER = "__trader__"


class FileTypes:
    BONDS = "BondsTradesExecuted"
    CASH = "CashTransactions"
    CFD = "CFDTradesExecuted"
    CFD_OPTIONS = "CFDOptionTradesExecuted"
    FUTURES = "FuturesTradesExecuted"
    FX = "FXTradesExecuted"
    FX_OPTION = "FXOptionTradesExecuted"
    MUTUAL_FUNDS = "MutualFundsTradesExecuted"
    ORDER_ACTIVITY = "OrderActivity"
    SHARE = "ShareTradesExecuted"
    SP = "SPTradesExecuted"


class SaxoBankOrderTransformations(AbstractOrderTransformations):
    """Transformations class for Saxo Bank Orders"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.file_url = os.getenv(SWARM_FILE_URL)
        self.file_name = os.path.basename(self.file_url)
        self.file_type = self.get_file_type(file_name=self.file_name)

        if not self.file_type:
            raise signals.FAIL(
                message=f"File type not recognised. File name: {self.file_name} "
                f"should start with one of: [BondsTrades, CashTransactions, CFDTrades, "
                "CFDOptionTradesExecuted, FuturesTrades, FXTrades, FXOptionsTrades, "
                "MutalFundsTrades, OrderActivity, ShareTrades, SPTrades]"
            )
        if self.file_type in {
            FileTypes.CASH,
            FileTypes.CFD_OPTIONS,
            FileTypes.ORDER_ACTIVITY,
        }:
            # These files do not need to be processed
            raise signals.SKIP(message=f"Flow skipped as file type = {self.file_type}")

    @staticmethod
    def get_file_type(file_name: str) -> str:
        """
        Returns the appropriate file_type
        :param file_name: name of the file
        :return: file_type
        """
        for file_type in [
            v for k, v in FileTypes.__dict__.items() if isinstance(v, str)
        ]:
            if file_name.startswith(file_type):
                return file_type

    def _pre_process(self):
        """Not Implemented"""

    def process(self):
        self.pre_process()
        self.buy_sell()
        self.data_source_name()
        self.date()
        self.execution_details_buy_sell_indicator()
        self.execution_details_order_status()
        self.execution_details_order_type()
        self.execution_details_trading_capacity()
        self.hierarchy()
        self.id()
        self.meta_model()
        self.order_identifiers_order_id_code()
        self.order_identifiers_transaction_ref_no()
        self.price_forming_data_price()
        self.report_details_transaction_ref_no()
        self.source_index()
        self.source_key()
        self.timestamps_trading_date_time()
        self.timestamps_order_received()
        self.timestamps_order_submitted()
        self.timestamps_order_status_updated()
        self.transaction_details_buy_sell_indicator()
        self.transaction_details_price()
        self.transaction_details_price_currency()
        self.transaction_details_price_notation()
        self.transaction_details_quantity_currency()
        self.price_forming_data_traded_quantity()
        self.price_forming_data_initial_quantity()
        self.transaction_details_quantity()
        self.transaction_details_quantity_notation()
        self.transaction_details_trading_capacity()
        self.transaction_details_trading_date_time()
        self.transaction_details_ultimate_venue()
        self.transaction_details_venue()
        self.market_identifiers_instrument()
        self.market_identifiers_parties()
        self.market_identifiers()
        self.post_process()
        return self.target_df

    def _post_process(self):
        """Not Implemented"""

    def _buy_sell(self) -> pd.DataFrame:
        """Returns a data frame containing _order.buySell and _orderState.buySell.
        This is populated from SourceColumns.BUY_SELL"""
        return pd.concat(
            [
                MapValue.process(
                    source_frame=self.source_frame,
                    params=ParamsMapValue(
                        source_attribute=SourceColumns.BUY_SELL,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.BUY_SELL,
                        ),
                        case_insensitive=True,
                        value_map={"buy": "1", "sell": "2"},
                    ),
                    auditor=self.auditor,
                ),
                MapValue.process(
                    source_frame=self.source_frame,
                    params=ParamsMapValue(
                        source_attribute=SourceColumns.BUY_SELL,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.BUY_SELL,
                        ),
                        case_insensitive=True,
                        value_map={"buy": "1", "sell": "2"},
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        """Populates dataSourceName"""
        return pd.DataFrame(
            data="Saxobank",
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """Populates date from"""
        if self.file_type == FileTypes.MUTUAL_FUNDS:
            return ConvertDatetime.process(
                source_frame=self.source_frame,
                params=ParamsConvertDatetime(
                    source_attribute=SourceColumns.TRADE_TIME,
                    target_attribute=OrderColumns.DATE,
                    convert_to=ConvertTo.DATE.value,
                ),
            )
        else:
            return ConvertDatetime.process(
                source_frame=self.source_frame,
                params=ParamsConvertDatetime(
                    source_attribute=SourceColumns.TRADE_EXECUTION_TIME,
                    target_attribute=OrderColumns.DATE,
                    convert_to=ConvertTo.DATE.value,
                ),
            )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.buySellIndicator.
        This is populated from SourceColumns.BUY_SELL"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.BUY_SELL,
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "buy": BuySellIndicator.BUYI.value,
                    "sell": BuySellIndicator.SELL.value,
                },
            ),
            auditor=self.auditor,
        )

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_order_status(self) -> pd.DataFrame:
        """Returns a data frame containing _order.executionDetails.orderStatus and
        _orderState.executionDetails.orderStatus.
        This is populated with the static values NEWO and FILL respectively"""
        return pd.DataFrame(
            data=[[OrderStatus.NEWO.value, OrderStatus.FILL.value]],
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                ),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                ),
            ],
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """Populates OrderColumns.EXECUTION_DETAILS_ORDER_TYPE with the
        static value 'MARKET'"""
        return pd.DataFrame(
            data="MARKET",
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_ORDER_TYPE],
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_stop_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """Hardcodes OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY to AOTC"""
        return pd.DataFrame(
            data=TradingCapacity.AOTC.value,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY],
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _hierarchy(self) -> pd.DataFrame:
        """Returns a data frame containing hierarchy.
        This is populated with the static value 'Standalone'"""
        return pd.DataFrame(
            data=HierarchyEnum.STANDALONE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.HIERARCHY],
        )

    def _id(self) -> pd.DataFrame:
        """Populates OrderColumns.ID from SourceColumns.ORDER_NUMBER for Orders and OrderStates"""
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.source_frame.loc[:, SourceColumns.ORDER_NUMBER].values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID)
                    ],
                ),
                pd.DataFrame(
                    data=self.source_frame.loc[:, SourceColumns.ORDER_NUMBER].values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER_STATE, attribute=OrderColumns.ID
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _financing_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_discretionary(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_iceberg(self):
        """Not Implemented"""

    def _is_repo(self):
        """Not Implemented"""

    def _is_synthetic(self):
        """Not Implemented"""

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        """Not Implemented"""

    def _meta_model(self) -> pd.DataFrame:
        """Returns a data frame containing _order.__meta_model__ and _orderState.__meta_model__.
        The columns are populated with the static values Order and OrderState respectively"""
        return pd.DataFrame(
            data=[["Order", "OrderState"]],
            index=self.source_frame.index,
            columns=[
                add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.META_MODEL),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.META_MODEL,
                ),
            ],
        )

    def _order_class(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """Populates OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE from SourceColumns.ORDER_NUMBER"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORDER_NUMBER].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
        )

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """Populates OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO from SourceColumns.TRADE_NUMBER"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.TRADE_NUMBER].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO],
        )

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY.
        Assumes that _price_forming_data_traded_quantity() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY],
        )

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price(self) -> pd.DataFrame:
        """
        Populates from DirtyPrice for Bonds and Price for the rest
        """
        if self.file_type == FileTypes.BONDS:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ParamsConvertMinorToMajor(
                    source_price_attribute=SourceColumns.DIRTY_PRICE,
                    source_ccy_attribute=SourceColumns.INSTRUMENT_CURRENCY,
                    target_price_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                    cast_to=CastTo.ABS,
                ),
            )
        else:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ParamsConvertMinorToMajor(
                    source_price_attribute=SourceColumns.PRICE,
                    source_ccy_attribute=SourceColumns.INSTRUMENT_CURRENCY,
                    target_price_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                    cast_to=CastTo.ABS,
                ),
            )

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing priceFormingData.tradedQuantity.
        from SourceColumns.TRADED_AMOUNT. Requires transaction_details_quantity_currency to have been
        called earlier. Note that the currency attribute
        OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY is populated for 3 types of files, and null for others.
        When it is null, ConvertMinorToMajor will not do any conversion
        """
        return ConvertMinorToMajor.process(
            source_frame=pd.concat(
                [
                    self.source_frame,
                    self.target_df.loc[
                        :, [OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY]
                    ],
                ],
                axis=1,
            ),
            params=ParamsConvertMinorToMajor(
                source_price_attribute=SourceColumns.TRADED_AMOUNT,
                source_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
                target_price_attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                cast_to=CastTo.ABS,
            ),
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """Populates OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO from SourceColumns.TRADE_NUMBER"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.TRADE_NUMBER].values,
            index=self.source_frame.index,
            columns=[OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO],
        )

    def _source_index(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceIndex column"""
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_INDEX],
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceKey column from the SWARM_FILE_URL"""
        return pd.DataFrame(
            data=os.getenv(SWARM_FILE_URL),
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_KEY],
        )

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_order_received(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderReceived.
        Assumes that _timestamps_trading_date_time() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.TIMESTAMPS_TRADING_DATE_TIME
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_RECEIVED],
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderStatusUpdated.
        Assumes that _timestamps_trading_date_time() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.TIMESTAMPS_TRADING_DATE_TIME
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED],
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderSubmitted.
        Assumes that _timestamps_trading_date_time() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.TIMESTAMPS_TRADING_DATE_TIME
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_SUBMITTED],
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """Populates OrderColumns.TIMESTAMPS_TRADING_DATE_TIME"""
        if self.file_type == FileTypes.MUTUAL_FUNDS:
            return ConvertDatetime.process(
                source_frame=self.source_frame,
                params=ParamsConvertDatetime(
                    source_attribute=SourceColumns.TRADE_TIME,
                    target_attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
                    convert_to=ConvertTo.DATETIME.value,
                ),
            )
        else:
            return ConvertDatetime.process(
                source_frame=self.source_frame,
                params=ParamsConvertDatetime(
                    source_attribute=SourceColumns.TRADE_EXECUTION_TIME,
                    target_attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
                    convert_to=ConvertTo.DATETIME.value,
                ),
            )

    def _timestamps_validity_period(self) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.buySellIndicator.
        Assumes that _execution_details_buy_sell_indicator() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.price.
        Assumes that _price_forming_data_price() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.PRICE_FORMING_DATA_PRICE].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE],
        )

    def _transaction_details_price_average(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY from
        SourceColumns.INSTRUMENT_CURRENCY"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.INSTRUMENT_CURRENCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
            ),
        )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.priceNotation.
        This is populated with the static value 'MONE' (for all asset classes except
        bonds) or 'PERC' (for Bonds)"""
        if self.file_type == FileTypes.BONDS:
            return pd.DataFrame(
                data=PriceNotation.PERC.value,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION],
            )
        else:
            return pd.DataFrame(
                data=PriceNotation.MONE.value,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION],
            )

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.quantity.
        Assumes that _price_forming_data_traded_quantity() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY],
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """Populated from ACCOUNT_CURRENCY for 5 file types (set to null for others)"""
        if self.file_type in [FileTypes.FX, FileTypes.FX_OPTION, FileTypes.BONDS]:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ParamsConvertMinorToMajor(
                    source_ccy_attribute=SourceColumns.ACCOUNT_CURRENCY,
                    target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
                ),
            )
        else:
            return pd.DataFrame(
                data=pd.NA,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY],
            )

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.quantityNotation.
        This is populated with the static value 'MONE' or 'UNIT'"""
        if self.file_type in [
            FileTypes.FX,
            FileTypes.FX_OPTION,
            FileTypes.BONDS,
        ]:
            return pd.DataFrame(
                data=QuantityNotation.MONE.value,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION],
            )
        else:
            return pd.DataFrame(
                data=QuantityNotation.UNIT.value,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION],
            )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY from the already-populated
        EXECUTION_DETAILS_TRADING_CAPACITY
        """
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME from the already-populated
        TIMESTAMPS_TRADING_DATE_TIME
        """
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.TIMESTAMPS_TRADING_DATE_TIME
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME],
        )

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """Not Implemented"""
        if self.file_type in {
            FileTypes.FUTURES,
            FileTypes.CFD,
            FileTypes.SHARE,
            FileTypes.SP,
            FileTypes.BONDS,
        }:
            return pd.DataFrame(
                data=self.source_frame.loc[:, SourceColumns.ISO_MIC].values,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE],
            )
        else:
            return pd.DataFrame(
                data=pd.NA,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE],
            )

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_venue(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.venue.
        This is populated with the static value 'XOFF'"""
        return pd.DataFrame(
            data="XOFF",
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_VENUE],
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.instrument by calling InstrumentIdentifiers.
        Assumes that _transaction_details_price_currency() has been called earlier"""
        instruments_df = pd.DataFrame(index=self.source_frame.index)
        instruments_df[TempColumns.ASSET_CLASS] = self._get_asset_class()
        instruments_df[TempColumns.EXPIRY_DATE] = self._get_expiry_date()
        instruments_df[TempColumns.ISIN] = self._get_isin()
        instruments_df[
            TempColumns.OPTION_STRIKE_PRICE
        ] = self._get_option_strike_price()
        instruments_df[TempColumns.OPTION_TYPE] = self._get_option_type()
        instruments_df[
            TempColumns.UNDERLYING_INDEX_NAME
        ] = self._get_underlying_index_name()
        instruments_df[
            TempColumns.UNDERLYING_INDEX_TERM
        ] = self._get_underlying_index_term()
        instruments_df[TempColumns.UNDERLYING_ISIN] = self._get_underlying_isin()
        instruments_df[TempColumns.UNDERLYING_SYMBOL] = self._get_underlying_symbol()

        return InstrumentIdentifiers.process(
            pd.concat(
                [
                    instruments_df,
                    self.target_df.loc[
                        :,
                        [
                            OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                            OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
                            OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                        ],
                    ],
                ],
                axis=1,
            ),
            params=ParamsInstrumentIdentifiers(
                asset_class_attribute=TempColumns.ASSET_CLASS,
                currency_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                expiry_date_attribute=TempColumns.EXPIRY_DATE,
                isin_attribute=TempColumns.ISIN,
                notional_currency_2_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
                option_strike_price_attribute=TempColumns.OPTION_STRIKE_PRICE,
                option_type_attribute=TempColumns.OPTION_TYPE,
                underlying_index_name_attribute=TempColumns.UNDERLYING_INDEX_NAME,
                underlying_index_term_attribute=TempColumns.UNDERLYING_INDEX_TERM,
                underlying_isin_attribute=TempColumns.UNDERLYING_ISIN,
                underlying_symbol_attribute=TempColumns.UNDERLYING_SYMBOL,
                venue_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
            ),
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """
        Creates PartyIdentifiers results
        Assumes _transaction_details_buy_sell_indicator() has run successfully
        """
        parties_df = pd.DataFrame(index=self.source_frame.index)
        parties_df.loc[:, TempColumns.EXECUTING_ENTITY] = add_id_or_lei(
            dataframe=self.source_frame,
            source_attribute=SourceColumns.EXECUTING_ENTITY_IDENTIFICATION_CODE,
        ).loc[:, SourceColumns.EXECUTING_ENTITY_IDENTIFICATION_CODE]
        parties_df.loc[:, TempColumns.TRADER] = add_id_or_lei(
            dataframe=self.source_frame, source_attribute=SourceColumns.USER_ID_ON_TRADE
        ).loc[:, SourceColumns.USER_ID_ON_TRADE]
        parties_df.loc[:, TempColumns.COUNTERPARTY] = add_id_or_lei(
            dataframe=self.source_frame, source_attribute=SourceColumns.COUNTERPART_ID
        ).loc[:, SourceColumns.COUNTERPART_ID]

        return GenericOrderPartyIdentifiers.process(
            source_frame=pd.concat(
                [
                    parties_df,
                    self.target_df.loc[
                        :, [OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR]
                    ],
                ],
                axis=1,
            ),
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=TempColumns.EXECUTING_ENTITY,
                counterparty_identifier=TempColumns.COUNTERPARTY,
                buy_sell_side_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                buyer_identifier=TempColumns.EXECUTING_ENTITY,
                seller_identifier=TempColumns.COUNTERPARTY,
                trader_identifier=TempColumns.TRADER,
                use_buy_mask_for_buyer_seller=True,
            ),
        )

    def _market_identifiers(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_instrument() and _market_identifiers_parties() have been
        called earlier"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _get_asset_class(self) -> pd.Series:
        """
        Get the asset class based on the file name and FXType column (for FX forwards and spots)
        :return: Series containing the asset class
        """

        asset_class_map = {
            FileTypes.BONDS: AssetClass.BOND,
            FileTypes.CFD: AssetClass.CFD,
            FileTypes.FUTURES: AssetClass.FUTURE,
            FileTypes.FX_OPTION: AssetClass.FX_OPTION,
        }
        if self.file_type == FileTypes.FX:
            return MapConditional.process(
                source_frame=self.source_frame,
                params=ParamsMapConditional(
                    target_attribute=TempColumns.ASSET_CLASS,
                    cases=[
                        Case(
                            query=f"`{SourceColumns.FXTYPE}`.str.match('Spot', case=False, na=False)",
                            value=AssetClass.FX_SPOT,
                        ),
                        Case(
                            query=f"`{SourceColumns.FXTYPE}`.str.match('Forward', case=False, na=False)",
                            value=AssetClass.FX_FORWARD,
                        ),
                    ],
                ),
            ).loc[:, TempColumns.ASSET_CLASS]
        else:
            return pd.Series(
                data=asset_class_map.get(self.file_type, pd.NA),
                index=self.source_frame.index,
                name=TempColumns.ASSET_CLASS,
            )

    def _get_expiry_date(self) -> pd.Series:
        """
        Gets the expiry date for Futures and FX Options
        :return: Series containing the expiry date
        """
        if self.file_type in [FileTypes.FX_OPTION, FileTypes.FUTURES]:
            return ConvertDatetime.process(
                self.source_frame,
                params=ParamsConvertDatetime(
                    source_attribute=SourceColumns.EXPIRY_DATE,
                    source_attribute_format="%Y%m%d",
                    target_attribute=TempColumns.EXPIRY_DATE,
                    convert_to=ConvertTo.DATE.value,
                ),
            ).loc[:, TempColumns.EXPIRY_DATE]
        else:
            # column needed downstream in InstrumentOverridesExpiryDate
            return pd.Series(
                data=pd.NA,
                index=self.source_frame.index,
                name=TempColumns.EXPIRY_DATE,
            )

    def _get_isin(self) -> pd.Series:
        """
        Assigns ISIN from  InstrumentISINCode, ISIN or ISINCode according to file type
        :return: Series containing the ISIN
        """
        if self.file_type in [FileTypes.FX, FileTypes.FX_OPTION]:
            return self.source_frame.loc[:, SourceColumns.INSTRUMENT_ISIN_CODE]

        elif self.file_type == FileTypes.FUTURES:
            instrument_isin_code_not_null_query = (
                f"`{SourceColumns.INSTRUMENT_ISIN_CODE}`.notnull()"
            )
            return MapConditional.process(
                source_frame=self.source_frame,
                params=ParamsMapConditional(
                    target_attribute=TempColumns.ISIN,
                    cases=[
                        Case(
                            query=instrument_isin_code_not_null_query,
                            attribute=SourceColumns.INSTRUMENT_ISIN_CODE,
                        ),
                        Case(
                            query=f"~{instrument_isin_code_not_null_query}",
                            attribute=SourceColumns.INSTRUMENT_CODE,
                        ),
                    ],
                ),
            ).loc[:, TempColumns.ISIN]

        elif self.file_type == FileTypes.BONDS:
            return self.source_frame.loc[:, SourceColumns.ISIN]

        elif self.file_type in [
            FileTypes.CFD,
            FileTypes.MUTUAL_FUNDS,
            FileTypes.SHARE,
            FileTypes.ORDER_ACTIVITY,
        ]:
            return self.source_frame.loc[:, SourceColumns.ISIN_CODE]

        elif self.file_type == FileTypes.SP:
            isin_code_not_null_query = f"`{SourceColumns.ISIN_CODE}`.notnull()"
            return MapConditional.process(
                source_frame=self.source_frame,
                params=ParamsMapConditional(
                    target_attribute=TempColumns.ISIN,
                    cases=[
                        Case(
                            query=isin_code_not_null_query,
                            attribute=SourceColumns.ISIN_CODE,
                        ),
                        Case(
                            query=f"~{isin_code_not_null_query}",
                            attribute=SourceColumns.INSTRUMENT_ISIN_CODE,
                        ),
                    ],
                ),
            ).loc[:, TempColumns.ISIN]
        else:
            return pd.Series(
                data=pd.NA,
                index=self.source_frame.index,
                name=TempColumns.ISIN,
            )

    def _get_option_strike_price(self) -> pd.Series:
        """
        Populates the option strike price from Strike only for FXOptions
        :return Series containing the Option Strike Price
        """
        if self.file_type == FileTypes.FX_OPTION:
            return self.source_frame.loc[:, SourceColumns.STRIKE]
        else:
            return pd.Series(
                data=pd.NA,
                index=self.source_frame.index,
                name=TempColumns.OPTION_STRIKE_PRICE,
            )

    def _get_option_type(self) -> pd.Series:
        """
        Populates the option type from CallPut only for FXOptions
        :return Series containing the Option type
        """
        if self.file_type == FileTypes.FX_OPTION:
            return self.source_frame.loc[:, SourceColumns.CALL_PUT]
        else:
            return pd.Series(
                data=pd.NA,
                index=self.source_frame.index,
                name=TempColumns.OPTION_TYPE,
            )

    def _get_underlying_index_name(self):
        """
        Populates the underlying index name from UnderlyingIndexCode only for FXOptions and FX
        :return Series containing the underlying index name
        """
        if self.file_type in [FileTypes.FUTURES, FileTypes.MUTUAL_FUNDS, FileTypes.SP]:
            return self.source_frame.loc[:, SourceColumns.UNDERLYING_INSTRUMENT_CODE]

        else:
            return pd.Series(
                data=pd.NA,
                index=self.source_frame.index,
                name=TempColumns.UNDERLYING_INDEX_NAME,
            )

    def _get_underlying_index_term(self) -> pd.Series:
        """
        Populated from TermOfUnderlyingIndex only for Futures
        :return Series containing the underlying index term
        """
        if self.file_type in {FileTypes.CFD, FileTypes.FUTURES}:
            return self.source_frame.loc[:, SourceColumns.TERM_OF_UNDERLYING_INDEX]
        else:
            return pd.Series(
                data=pd.NA,
                index=self.source_frame.index,
                name=TempColumns.UNDERLYING_INDEX_TERM,
            )

    def _get_underlying_isin(self) -> pd.Series:
        """
        Populated from UnderlyingInstrumentISINCode only for SP Trades
        :return Series containing the underlying ISIN
        """
        if self.file_type == FileTypes.SP:
            return self.source_frame.loc[
                :, SourceColumns.UNDERLYING_INSTRUMENT_ISIN_CODE
            ]

        else:
            return pd.Series(
                data=pd.NA,
                index=self.source_frame.index,
                name=TempColumns.UNDERLYING_ISIN,
            )

    def _get_underlying_symbol(self):
        """
        Populated from InstrumentCode for Futures and CFDs with the respective logic
        :return Series containing the underlying symbol
        """
        if self.file_type == FileTypes.FUTURES:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.INSTRUMENT_CODE,
                    target_attribute=TempColumns.UNDERLYING_SYMBOL,
                    end_index=-2,
                ),
                auditor=self.auditor,
            ).loc[:, TempColumns.UNDERLYING_SYMBOL]

        elif self.file_type == FileTypes.CFD:
            return MapValue.process(
                source_frame=self.source_frame,
                params=ParamsMapValue(
                    source_attribute=SourceColumns.INSTRUMENT_CODE,
                    target_attribute=TempColumns.UNDERLYING_SYMBOL,
                    regex_replace_map=[RegexReplaceMap(regex=r"\..*$", drop=True)],
                    regex_replace_only=True,
                ),
                auditor=self.auditor,
            ).loc[:, TempColumns.UNDERLYING_SYMBOL]

        else:
            return pd.Series(
                data=pd.NA,
                index=self.source_frame.index,
                name=TempColumns.UNDERLYING_SYMBOL,
            )

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""
