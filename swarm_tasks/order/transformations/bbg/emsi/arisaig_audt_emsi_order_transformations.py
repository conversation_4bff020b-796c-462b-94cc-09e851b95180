import pandas as pd
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.order.feed.bbg.emsi.static import DATE_TIME_FORMAT_CAXTON
from swarm_tasks.order.feed.bbg.emsi.static import SourceColumns
from swarm_tasks.order.feed.bbg.emsi.static import TempColumns
from swarm_tasks.order.transformations.bbg.emsi.audt_emsi_order_transformations import (
    BbgEmsiOrderTransformations,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)


class ArisaigBbgEmsiOrderTransformations(BbgEmsiOrderTransformations):
    @staticmethod
    def _set_timezone_info() -> str:
        """
        Sets the timezone info for the ConvertDateTime tasks \n
        Using London timezone for Arisaig
        """
        return "Asia/Singapore"

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE
        """
        temp_df = self.source_frame.loc[
            :, [TempColumns.LAST_FILL_LIMIT_PRICE, SourceColumns.PRICE_CURRENCY]
        ]

        # we're converting to float as needed here instead of BatchProducer to
        # avoid flagging down `Limit Price (Tag 44)` being populated with `MKT`
        # as an error, since it muddles the data provenance screen.
        temp_df.loc[:, TempColumns.LIMIT_PRICE] = (
            temp_df.loc[:, TempColumns.LAST_FILL_LIMIT_PRICE]
            .fillna(pd.NA)
            .apply(lambda x: self._convert_if_float(x))
        )

        return ConvertMinorToMajor.process(
            source_frame=temp_df,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.PRICE_CURRENCY,
                source_price_attribute=TempColumns.LAST_FILL_LIMIT_PRICE,
                target_price_attribute=OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE,
            ),
        )

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """
        Maps OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO
        from SourceColumns.FILL_ID
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, TempColumns.LAST_FILL_FILL_ID].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                )
            ],
        )

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        """
        Populates OrderColumns.PRICE_FORMING_DATA_MODIFIED_QUANTITY
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, TempColumns.LAST_FILL_QUANTITY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_MODIFIED_QUANTITY],
        )

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """
        Populates OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
        """
        target_field_with_prefix = add_prefix(
            prefix=ModelPrefix.ORDER_STATE,
            attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
        )

        temp_df = pd.DataFrame(
            data=self.source_frame.loc[:, TempColumns.LAST_FILL_FILLED_QUANTITY].values,
            index=self.source_frame.index,
            columns=[target_field_with_prefix],
        )
        return self._map_field_for_non_cancels_replaces(
            df_with_target_field=temp_df, target_field=target_field_with_prefix
        )

    def _price_forming_data_price(self) -> pd.DataFrame:
        """
        Populates OrderColumns.PRICE_FORMING_DATA_PRICE
        """
        target_field_with_prefix = add_prefix(
            prefix=ModelPrefix.ORDER_STATE,
            attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
        )
        temp_df = ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.PRICE_CURRENCY,
                source_price_attribute=TempColumns.LAST_FILL_PRICE,
                target_price_attribute=target_field_with_prefix,
            ),
        )
        return self._map_field_for_non_cancels_replaces(
            df_with_target_field=temp_df, target_field=target_field_with_prefix
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """
        Maps OrderColumns.TIMESTAMPS_TRADING_DATE_TIME
        """
        target_field_with_prefix = add_prefix(
            prefix=ModelPrefix.ORDER_STATE,
            attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
        )
        temp_df = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=TempColumns.LAST_FILL_RECEIVE_DATE_TIME,
                source_attribute_format=DATE_TIME_FORMAT_CAXTON,
                target_attribute=target_field_with_prefix,
                convert_to=ConvertTo.DATETIME,
                timezone_info=self.timezone_info,
            ),
        )

        return self._map_field_for_non_cancels_replaces(
            df_with_target_field=temp_df, target_field=target_field_with_prefix
        )
