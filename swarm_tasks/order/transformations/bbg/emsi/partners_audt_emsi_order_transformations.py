import pandas as pd
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_trades_tasks.order.static import OrderColumns

from swarm_tasks.order.transformations.bbg.emsi.audt_emsi_order_transformations import (
    BbgEmsiOrderTransformations,
)


class PartnersBbgEmsiOrderTransformations(BbgEmsiOrderTransformations):
    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY with "AOTC"
        """
        return pd.DataFrame(
            data=TradingCapacity.AOTC.value,
            index=self.pre_process_df.index,
            columns=[OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY WITH "AOTC"
        """
        return pd.DataFrame(
            data=TradingCapacity.AOTC.value,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    @staticmethod
    def _set_timezone_info() -> str:
        """
        Sets the timezone info for the ConvertDateTime tasks \n
        Using London timezone for Partners
        """
        return "Europe/London"
