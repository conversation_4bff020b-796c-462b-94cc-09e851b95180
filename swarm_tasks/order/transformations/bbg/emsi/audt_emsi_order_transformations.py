import os
import re
from typing import Union

import numpy as np
import pandas as pd
from pandas._libs.missing import NAType
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_attribute import CastTo
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import Hierarchy<PERSON>num
from se_elastic_schema.static.mifid2 import OptionType
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import ShortSellingIndicator
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_elastic_schema.static.mifid2 import ValidityPeriod
from se_elastic_schema.static.reference import OrderRecordType
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order.static import OrderType
from se_trades_tasks.order_and_tr.party.utils import add_id_or_lei
from se_trades_tasks.order_and_tr.static import AssetClass
from se_trades_tasks.order_and_tr.static import (
    InstrumentIdentifierParamColumns,
)
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.order.feed.bbg.emsi.static import DATE_TIME_FORMAT_CAXTON
from swarm_tasks.order.feed.bbg.emsi.static import DATE_TIME_FORMAT_PARTNERS
from swarm_tasks.order.feed.bbg.emsi.static import EXPIRY_DATE_FORMAT_CAXTON
from swarm_tasks.order.feed.bbg.emsi.static import EXPIRY_DATE_FORMAT_PARTNERS
from swarm_tasks.order.feed.bbg.emsi.static import OPTION_EXPIRY_DATE_FORMAT
from swarm_tasks.order.feed.bbg.emsi.static import SourceColumns
from swarm_tasks.order.feed.bbg.emsi.static import TempColumns
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.generic.get_tenant_lei import Params as ParamsGetTenantLEI
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_static import MapStatic
from swarm_tasks.transform.map.map_static import Params as ParamsMapStatic
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)
from swarm_tasks.utilities.market.instruments.instrument_utils import MapMonth
from swarm_tasks.utilities.static import Delimiters


class BbgEmsiOrderTransformations(AbstractOrderTransformations):
    """
    Specs: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2745696257/Order+BBG+EMSI+EOD+File+V2
    """

    def __init__(self, source_frame: pd.DataFrame, auditor, **kwargs):
        super().__init__(source_frame=source_frame, auditor=auditor)

        # Some tenants send their data on a specific timezone instead of UTC. This method
        #   is used to enforce that timezone in datetime conversions.
        # Please make sure all ConvertDateTimeTasks (Datetime only) use this variable as
        #   timezone_info param.
        self.timezone_info = self._set_timezone_info()

    def _pre_process(self):
        """
        Temporary columns needed for multiple fields
        """

        # Convert SourceColumns.ORDER_HASH to integer
        self.source_frame.loc[:, SourceColumns.ORDER_HASH] = (
            np.floor(
                pd.to_numeric(
                    self.source_frame.loc[:, SourceColumns.ORDER_HASH],
                    errors="coerce",
                    downcast="integer",
                )
            )
            .astype("Int64")
            .astype("string")
        )

        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_buy_sell(),
                self._temp_currency_code(),
                self._temp_date(),
                self._temp_date_time(),
                self._temp_trading_date_time(),
                self._temp_instr_ids_asset_class(),
                self._temp_instr_ids_underlying_symbol(),
                self._temp_instruction_parsed(),
                self._temp_order_id(),
                self._temp_price(),
                self._temp_trading_capacity(),
                self._temp_security_name_converted(),
            ],
            axis=1,
        )

        # These columns need access to others defined above
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_buy_sell_indicator(),  # needs buy_sell
                self._temp_instr_fallback_instrument_unique_identifier(),  # needs currency code
                self._temp_newo_in_file(),  # needs order status
                self._temp_traded_quantity(),  # needs asset_class
                self._temp_venue(),  # needs trading date time
            ],
            axis=1,
        )

    def process(self) -> pd.DataFrame:
        self.pre_process()
        self.buy_sell()
        self.data_source_name()
        self.date()
        self.execution_details_buy_sell_indicator()
        self.execution_details_liquidity_provision_activity()
        self.execution_details_order_status()
        self.execution_details_order_type()
        self.execution_details_outgoing_order_addl_info()
        self.execution_details_routing_strategy()
        self.execution_details_short_selling_indicator()
        self.execution_details_stop_price()
        self.execution_details_trading_capacity()
        self.execution_details_validity_period()
        self.hierarchy()
        self.id()
        self.meta_model()
        self.order_identifiers_internal_order_id_code()
        self.order_identifiers_order_id_code()
        self.order_identifiers_parent_order_id()
        self.order_identifiers_order_routing_code()
        self.price_forming_data_initial_quantity()
        self.price_forming_data_modified_quantity()
        self.price_forming_data_price()
        self.price_forming_data_remaining_quantity()
        self.price_forming_data_traded_quantity()
        self.source_index()
        self.source_key()
        self.order_identifiers_transaction_ref_no()
        self.report_details_transaction_ref_no()
        self.timestamps_order_received()
        self.timestamps_order_status_updated()
        self.timestamps_order_submitted()
        self.timestamps_trading_date_time()
        self.traders_algos_waivers_indicators_short_selling_indicator()
        self.transaction_details_buy_sell_indicator()
        self.transaction_details_complex_trade_component_id()
        self.transaction_details_price()
        self.transaction_details_price_average()
        self.transaction_details_price_currency()
        self.transaction_details_price_notation()
        self.transaction_details_quantity()
        self.transaction_details_quantity_currency()
        self.transaction_details_quantity_notation()
        self.transaction_details_record_type()

        # Needs OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE
        self.execution_details_limit_price()

        self.transaction_details_trading_capacity()
        self.transaction_details_trading_date_time()
        self.transaction_details_ultimate_venue()
        self.transaction_details_venue()
        self.market_identifiers_instrument()
        self.market_identifiers_parties()
        self.market_identifiers()
        self.post_process()
        return self.target_df

    def _post_process(self):
        inst_fallback_columns = [
            TempColumns.CURRENCY_CODE,
            TempColumns.INSTR_UNIQUE_IDENTIFIER,
        ]

        source_columns_for_instr_fallback = [
            SourceColumns.FIGI,
            SourceColumns.TICKER,
            SourceColumns.ISIN_INSTRUMENT,
            SourceColumns.LAST_MARKET_INSTRUMENT,
        ]

        self.target_df = pd.concat(
            [
                self.target_df,
                self.source_frame.loc[:, source_columns_for_instr_fallback],
                self.pre_process_df.loc[:, inst_fallback_columns],
                self.pre_process_df.loc[:, TempColumns.NEWO_IN_FILE],
                self._get_temp_link_instrument_venue(),
                self._get_instrument_name_columns(),
            ],
            axis=1,
        )
        self.target_df.loc[:, TempColumns.IS_CREATED_THROUGH_FALLBACK] = True

    def _get_instrument_name_columns(self):
        """
        Returns a data frame with the full name and the short name
        :return: Dataframe containing 2 instrument name columns
        """
        return pd.concat(
            [
                MapConditional.process(
                    source_frame=self.source_frame,
                    params=ParamsMapConditional(
                        target_attribute=TempColumns.INSTRUMENT_FULL_NAME,
                        cases=[
                            Case(
                                query=f"(`{SourceColumns.SECURITY_NAME}`.notnull())",
                                attribute=SourceColumns.SECURITY_NAME,
                            ),
                            Case(
                                query=f"(`{SourceColumns.SECURITY_NAME}`.isnull())",
                                attribute=SourceColumns.TICKER,
                            ),
                        ],
                    ),
                ),
                pd.DataFrame(
                    data=self.source_frame.loc[:, SourceColumns.TICKER].values,
                    index=self.source_frame.index,
                    columns=[TempColumns.INSTRUMENT_SHORT_NAME],
                ),
            ],
            axis=1,
        )

    def _buy_sell(self) -> pd.DataFrame:
        """
        Populates from TempColumns.BUY_SELL
        Adds '.order' and '.orderState' prefixes
        """
        return pd.concat(
            [
                MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.BUY_SELL,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.BUY_SELL,
                        ),
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.BUY_SELL,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.BUY_SELL,
                        ),
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        """
        Static value: EMSI-SS
        """
        return pd.DataFrame(
            data="EMSI-EOD",
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """
        Maps OrderColumns.DATE deriving its value from TempColumns.SYS_DATE.
        Where-ever TempColumns.SYS_DATE is blank TempColumns.SETTLE_DT is used.
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.DATE].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.DATE],
        )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
        from TempColumns.BUY_SELL_INDICATOR temporary column
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.BUY_SELL_INDICATOR].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE from SourceColumns.LIMIT_PRICE
        """
        temp_df = pd.concat(
            [
                self.target_df.loc[:, OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE],
                self.source_frame.loc[
                    :, [SourceColumns.LIMIT_PRICE, SourceColumns.PRICE_CURRENCY]
                ],
            ],
            axis=1,
        )

        # we're converting to float as needed here instead of BatchProducer to
        # avoid flagging down `Limit Price (Tag 44)` being populated with `MKT`
        # as an error, since it muddles the data provenance screen.
        temp_df.loc[:, TempColumns.LIMIT_PRICE] = (
            temp_df.loc[:, SourceColumns.LIMIT_PRICE]
            .fillna(pd.NA)
            .apply(lambda x: self._convert_if_float(x))
        )

        return ConvertMinorToMajor.process(
            source_frame=temp_df,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.PRICE_CURRENCY,
                source_price_attribute=TempColumns.LIMIT_PRICE,
                target_price_attribute=OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE,
            ),
        )

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_order_status(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_ORDER_STATUS from SourceColumns.EVENT
        """
        return pd.concat(
            [
                MapConditional.process(
                    source_frame=self.source_frame,
                    params=ParamsMapConditional(
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        ),
                        cases=[
                            Case(
                                query=f"`{TempColumns.ORDER_STATUS}` != '{OrderStatus.NEWO.value}'",
                                attribute=TempColumns.ORDER_STATUS,
                            ),
                        ],
                    ),
                ),
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=ParamsMapStatic(
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        ),
                        target_value=OrderStatus.NEWO.value,
                    ),
                ),
            ],
            axis=1,
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_ORDER_TYPE from SourceColumns.ORD_TYPE
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.ORDER_TYPE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
                case_insensitive=True,
                value_map={
                    "MKT": OrderType.MARKET,
                    "LMT": OrderType.LIMIT,
                },
            ),
            auditor=self.auditor,
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO
        It will concatenate the values, separated by a comma (',')
        """
        return ConcatAttributes.process(
            source_frame=self.pre_process_df,
            params=ParamsConcatAttributes(
                source_attributes=[
                    TempColumns.INSTRUCTION_PARSED,
                ],
                delimiter=Delimiters.COMMA_SPACE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
            ),
        )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY
        from SourceColumns.STRATEGY_NAME column
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.STRATEGY_NAME].values,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY],
        )

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.SIDE column
        """
        side_short_query = (
            f"`{SourceColumns.SIDE}`.str.fullmatch('SHORT', case=False, na=False)"
        )

        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.EXECUTION_DETAILS_SHORT_SELLING_INDICATOR,
                cases=[
                    Case(
                        query=f"{side_short_query}",
                        value=ShortSellingIndicator.SESH.value,
                    ),
                    Case(
                        query=f"~({side_short_query})",
                        value=ShortSellingIndicator.SELL.value,
                    ),
                ],
            ),
        )

    def _execution_details_stop_price(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_STOP_PRICE from SourceColumns.STOP_PRICE
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.PRICE_CURRENCY,
                source_price_attribute=SourceColumns.STOP_PRICE,
                target_price_attribute=OrderColumns.EXECUTION_DETAILS_STOP_PRICE,
            ),
        )

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY from TempColumns.TRADING_CAPACITY
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.TRADING_CAPACITY].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY],
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD from SourceColumns.TIF
        """
        temp_df = MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.TIF,
                target_attribute=TempColumns.EXECUTION_DETAILS_VALIDITY_PERIOD,
                case_insensitive=True,
                value_map={
                    "DAY": ValidityPeriod.DAVY.value,
                    "FOK": ValidityPeriod.FOKV.value,
                    "GTC": ValidityPeriod.GTCV.value,
                    "GTD": ValidityPeriod.GTDV.value,
                    "GTX": ValidityPeriod.GTXV.value,
                    "IOC": ValidityPeriod.IOCV.value,
                },
            ),
            auditor=self.auditor,
        )

        return MapAttribute.process(
            source_frame=temp_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.EXECUTION_DETAILS_VALIDITY_PERIOD,
                target_attribute=OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD,
                cast_to=CastTo.STRING_LIST.value,
                list_delimiter=Delimiters.SEMI_COLON,
            ),
            auditor=self.auditor,
        )

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _hierarchy(self) -> pd.DataFrame:
        """
        Populates with "Standalone"
        """
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_value=HierarchyEnum.STANDALONE.value,
                target_attribute=OrderColumns.HIERARCHY,
            ),
        )

    def _financing_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _id(self) -> pd.DataFrame:
        """
        Populates OrderColumns.ID from TempColumns.ORDER_ID
        """
        return pd.concat(
            [
                MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.ORDER_ID,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.ID,
                        ),
                    ),
                ),
                MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.ORDER_ID,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.ID,
                        ),
                    ),
                ),
            ],
            axis=1,
        )

    def _is_discretionary(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_iceberg(self):
        """Not Implemented"""

    def _is_repo(self):
        """Not Implemented"""

    def _is_synthetic(self):
        """Not Implemented"""

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        """Not Implemented"""

    def _market_identifiers(self) -> pd.DataFrame:
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """
        Populates from the following temporary columns:
            TempColumns.ASSET_CLASS
            TempColumns.INSTR_IDS_CURRENCY
            TempColumns.EXPIRY_DATE
            TempColumns.ISIN
            TempColumns.NOTIONAL_CURRENCY_2
            TempColumns.OPTION_STRIKE_PRICE
            TempColumns.OPTION_TYPE
            TempColumns.UNDERLYING_ISIN
            TempColumns.UNDERLYING_SYMBOL
            OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE
        """
        instrument_identifiers_df = pd.concat(
            [
                self.pre_process_df,
                pd.DataFrame(
                    data=self.source_frame.loc[
                        :, SourceColumns.LAST_MARKET_INSTRUMENT
                    ].values,
                    index=self.source_frame.index,
                    columns=[SourceColumns.LAST_MARKET_INSTRUMENT],
                ).replace(to_replace="^GLBX$", value="XCME", regex=True),
                self.source_frame.loc[
                    :,
                    [
                        SourceColumns.CONTRACT_EXPIRATION,
                        SourceColumns.ISIN_INSTRUMENT,
                        SourceColumns.OCC_SYMBOL,
                        SourceColumns.LOCAL_EXCH_SYMBOL,
                        SourceColumns.FIGI,
                    ],
                ],
            ],
            axis=1,
        )

        # Expiry date
        instrument_identifiers_df[TempColumns.TEMP_COL_1] = ConvertDatetime.process(
            source_frame=instrument_identifiers_df,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.CONTRACT_EXPIRATION,
                source_attribute_format=EXPIRY_DATE_FORMAT_CAXTON,
                target_attribute=TempColumns.TEMP_COL_1,
                convert_to=ConvertTo.DATE,
            ),
        )

        instrument_identifiers_df[TempColumns.EXPIRY_DATE] = ConvertDatetime.process(
            source_frame=instrument_identifiers_df,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.CONTRACT_EXPIRATION,
                source_attribute_format=EXPIRY_DATE_FORMAT_PARTNERS,
                target_attribute=TempColumns.EXPIRY_DATE,
                convert_to=ConvertTo.DATE,
            ),
        )[TempColumns.EXPIRY_DATE].fillna(
            instrument_identifiers_df[TempColumns.TEMP_COL_1]
        )

        # SourceColumns.OCC_SYMBOL logic which populates option_type and option_strike_price and updates expiry date
        instrument_identifiers_df[SourceColumns.OCC_SYMBOL] = instrument_identifiers_df[
            SourceColumns.OCC_SYMBOL
        ].str.replace(r"\s+", "")
        instrument_identifiers_df[
            TempColumns.TEMP_COL_1
        ] = instrument_identifiers_df.fillna("").apply(
            lambda x: re.sub(
                x[SourceColumns.LOCAL_EXCH_SYMBOL], "", x[SourceColumns.OCC_SYMBOL]
            ),
            axis=1,
        )
        instrument_identifiers_df[TempColumns.OPTION_TYPE] = (
            instrument_identifiers_df[TempColumns.TEMP_COL_1]
            .str.upper()
            .str[6]
            .map(
                {
                    "P": OptionType.PUTO.value,
                    "C": OptionType.CALL.value,
                    "O": OptionType.OTHR.value,
                }
            )
            .fillna(pd.NA)
        )
        not_options = (
            self.pre_process_df.loc[:, TempColumns.ASSET_CLASS] != AssetClass.OPTION
        )
        instrument_identifiers_df.loc[not_options, SourceColumns.OCC_SYMBOL] = pd.NA

        instrument_identifiers_df[TempColumns.OPTION_STRIKE_PRICE] = np.where(
            instrument_identifiers_df[TempColumns.TEMP_COL_1] == "",
            -999,
            instrument_identifiers_df[TempColumns.TEMP_COL_1].str[7:],
        ).astype(int)
        instrument_identifiers_df[
            TempColumns.OPTION_STRIKE_PRICE
        ] = instrument_identifiers_df[TempColumns.OPTION_STRIKE_PRICE].replace(
            -999, pd.NA
        )
        instrument_identifiers_df[
            TempColumns.OPTION_STRIKE_PRICE
        ] = ConvertMinorToMajor.process(
            source_frame=instrument_identifiers_df,
            params=ParamsConvertMinorToMajor(
                source_price_attribute=TempColumns.OPTION_STRIKE_PRICE,
                source_ccy_attribute=TempColumns.CURRENCY_CODE,
                target_price_attribute=TempColumns.OPTION_STRIKE_PRICE,
            ),
        )

        instrument_identifiers_df[TempColumns.TEMP_COL_2] = instrument_identifiers_df[
            TempColumns.TEMP_COL_1
        ].str[:6]
        instrument_identifiers_df[TempColumns.TEMP_COL_2] = ConvertDatetime.process(
            source_frame=instrument_identifiers_df,
            params=ParamsConvertDatetime(
                source_attribute=TempColumns.TEMP_COL_2,
                source_attribute_format=OPTION_EXPIRY_DATE_FORMAT,
                target_attribute=TempColumns.TEMP_COL_2,
                convert_to=ConvertTo.DATE,
            ),
        )
        instrument_identifiers_df[TempColumns.EXPIRY_DATE] = np.where(
            instrument_identifiers_df[TempColumns.ASSET_CLASS] == AssetClass.OPTION,
            instrument_identifiers_df[TempColumns.TEMP_COL_2],
            instrument_identifiers_df[TempColumns.EXPIRY_DATE],
        )

        return InstrumentIdentifiers.process(
            source_frame=instrument_identifiers_df,
            params=ParamsInstrumentIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                asset_class_attribute=TempColumns.ASSET_CLASS,
                currency_attribute=TempColumns.CURRENCY_CODE,
                expiry_date_attribute=TempColumns.EXPIRY_DATE,
                isin_attribute=SourceColumns.ISIN_INSTRUMENT,
                bbg_figi_id_attribute=SourceColumns.FIGI,
                option_strike_price_attribute=TempColumns.OPTION_STRIKE_PRICE,
                option_type_attribute=TempColumns.OPTION_TYPE,
                underlying_symbol_attribute=TempColumns.UNDERLYING_SYMBOL,
                venue_attribute=SourceColumns.LAST_MARKET_INSTRUMENT,
                venue_financial_instrument_short_name_attribute=TempColumns.SECURITY_NAME_CONVERTED,
                exchange_symbol_attribute=SourceColumns.OCC_SYMBOL,
                retain_task_inputs=True,
            ),
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """
        Populates from the following temporary columns:
            TempColumns.CLIENT
            TempColumns.EXECUTING_ENTITY
            TempColumns.TRADER
            TempColumns.EXECUTION_WITHIN_FIRM
            TempColumns.COUNTERPARTY
            TempColumns.BUYER
            TempColumns.SELLER
        """
        party_ids_df = pd.concat(
            [
                self._temp_party_ids_executing_entity(),
                self._temp_party_ids_client(),
                self._temp_party_ids_trader(),
                self._temp_party_inv_dm(),
                self._temp_party_ids_counterparty(),
            ],
            axis=1,
        )
        return GenericOrderPartyIdentifiers.process(
            source_frame=pd.concat(
                [
                    party_ids_df,
                    self.target_df.loc[
                        :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
                    ],
                ],
                axis=1,
            ),
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=TempColumns.EXECUTING_ENTITY,
                trader_identifier=TempColumns.TRADER,
                execution_within_firm_identifier=TempColumns.TRADER,
                investment_decision_within_firm_identifier=TempColumns.INVESTMENT_DECISION_MAKER,
                client_identifier=TempColumns.CLIENT,
                counterparty_identifier=TempColumns.COUNTERPARTY,
                buyer_identifier=TempColumns.EXECUTING_ENTITY,
                seller_identifier=TempColumns.COUNTERPARTY,
                use_buy_mask_for_buyer_seller=True,
                buy_sell_side_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                create_fallback_fields=True,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """
        Populates with Orders and OrderState values for OrderColumns.META_MODEL,
        """
        return pd.concat(
            [
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=ParamsMapStatic(
                        target_value="Order",
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.META_MODEL,
                        ),
                    ),
                ),
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=ParamsMapStatic(
                        target_value="OrderState",
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.META_MODEL,
                        ),
                    ),
                ),
            ],
            axis=1,
        )

    def _order_class(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """
        Maps OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE from TempColumns.INTERNAL_ORDER_ID
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.INTERNAL_ORDER_ID].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE],
        )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """
        Maps OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE from TempColumns.ORDER_ID
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.ORDER_ID].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
        )

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        """
        Maps OrderColumns.ORDER_IDENTIFIERS_ORDER_ROUTING_CODE from SourceColumns.ORDER_HASH
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORDER_HASH].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ROUTING_CODE],
        )

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        """
        Maps OrderColumns.ORDER_IDENTIFIERS_PARENT_ORDER_ID from SourceColumns.ORDER_ID
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORDER_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_PARENT_ORDER_ID],
        )

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """
        Maps OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO
        from SourceColumns.FILL_ID OR SourceColumns.ROUTE_ID and source index
        """
        temp_df = (
            self.source_frame.loc[:, SourceColumns.ROUTE_ID]
            + "."
            + self.target_df.loc[:, OrderColumns.SOURCE_INDEX].astype(str)
        ).to_frame(name=TempColumns.ROUTE_ID_WITH_SOURCE_INDEX)
        return MapConditional.process(
            source_frame=pd.concat([self.source_frame, temp_df], axis=1),
            params=ParamsMapConditional(
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                ),
                cases=[
                    Case(
                        query=f"(`{SourceColumns.FILL_ID}`.notnull())",
                        attribute=SourceColumns.FILL_ID,
                    ),
                    Case(
                        query=f"(`{SourceColumns.FILL_ID}`.isnull())",
                        attribute=TempColumns.ROUTE_ID_WITH_SOURCE_INDEX,
                    ),
                ],
            ),
        )

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """
        Populates OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY from
        TempColumns.QUANTITY_NEWO for non-NEWO records and SourceColumns.QUANTITY
        for NEWO records.
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                cases=[
                    Case(
                        query=f"`{TempColumns.ORDER_STATUS}`.str.fullmatch('{OrderStatus.NEWO.value}', case=False, na=False)",
                        attribute=SourceColumns.QUANTITY,
                    ),
                    Case(
                        query=f"~`{TempColumns.ORDER_STATUS}`.str.fullmatch('{OrderStatus.NEWO.value}', case=False, na=False)",
                        attribute=TempColumns.QUANTITY_NEWO,
                    ),
                ],
            ),
        )

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.QUANTITY
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.QUANTITY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_MODIFIED_QUANTITY],
        )

    def _price_forming_data_price(self) -> pd.DataFrame:
        """
        Populates OrderColumns.PRICE_FORMING_DATA_PRICE from temporary column TempColumns.PRICE
        """
        target_field_with_prefix = add_prefix(
            prefix=ModelPrefix.ORDER_STATE,
            attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
        )
        temp_df = pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.PRICE].values,
            index=self.pre_process_df.index,
            columns=[target_field_with_prefix],
        )
        return self._map_field_for_non_cancels_replaces(
            df_with_target_field=temp_df, target_field=target_field_with_prefix
        )

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """
        Assign static value zero for CAME records
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
                cases=[
                    Case(
                        query=f"(`{TempColumns.ORDER_STATUS}` == '{OrderStatus.CAME.value}')",
                        value=0,
                    ),
                ],
            ),
        )

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """
        Populates OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY from TempColumns.TRADED_QUANTITY
        """
        target_field_with_prefix = add_prefix(
            prefix=ModelPrefix.ORDER_STATE,
            attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
        )
        temp_df = pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.TRADED_QUANTITY].values,
            index=self.pre_process_df.index,
            columns=[target_field_with_prefix],
        )
        return self._map_field_for_non_cancels_replaces(
            df_with_target_field=temp_df, target_field=target_field_with_prefix
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """
        Maps OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO
        from TempColumns.TRANSACTION_REFERENCE_NUMBER temporary column
        """
        return pd.DataFrame(
            data=self.target_df.loc[
                :,
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                ),
            ].values,
            index=self.pre_process_df.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO,
                )
            ],
        )

    def _source_index(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceIndex column"""
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                from_index=True, target_attribute=OrderColumns.SOURCE_INDEX
            ),
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceKey column from the SWARM_FILE_URL"""
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_value=os.getenv("SWARM_FILE_URL"),
                target_attribute=OrderColumns.SOURCE_KEY,
            ),
        )

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _timestamps_order_received(self) -> pd.DataFrame:
        """
        Maps OrderColumns.TIMESTAMPS_ORDER_RECEIVED
        deriving its value from TempColumns.DATE_TIME
        """
        temp_df = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=TempColumns.ORDER_RECEIVED,
                source_attribute_format=DATE_TIME_FORMAT_CAXTON,
                target_attribute=TempColumns.TEMP_COL_1,
                convert_to=ConvertTo.DATETIME,
                timezone_info=self.timezone_info,
            ),
        )
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=TempColumns.ORDER_RECEIVED,
                source_attribute_format=DATE_TIME_FORMAT_PARTNERS,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                convert_to=ConvertTo.DATETIME,
                timezone_info=self.timezone_info,
            ),
        )[OrderColumns.TIMESTAMPS_ORDER_RECEIVED].fillna(
            temp_df[TempColumns.TEMP_COL_1]
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """
        Maps OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED
        deriving its value from TempColumns.ORDER_STATUS_UPDATED
        """
        temp_df = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=TempColumns.ORDER_STATUS_UPDATED,
                source_attribute_format=DATE_TIME_FORMAT_CAXTON,
                target_attribute=TempColumns.TEMP_COL_1,
                convert_to=ConvertTo.DATETIME,
                timezone_info=self.timezone_info,
            ),
        )
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=TempColumns.ORDER_STATUS_UPDATED,
                source_attribute_format=DATE_TIME_FORMAT_PARTNERS,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
                convert_to=ConvertTo.DATETIME,
                timezone_info=self.timezone_info,
            ),
        )[OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED].fillna(
            temp_df[TempColumns.TEMP_COL_1]
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """
        Maps OrderColumns.TIMESTAMPS_ORDER_SUBMITTED
        deriving its value from TempColumns.ORDER_SUBMITTED
        """
        temp_df = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=TempColumns.ORDER_SUBMITTED,
                source_attribute_format=DATE_TIME_FORMAT_CAXTON,
                target_attribute=TempColumns.TEMP_COL_1,
                convert_to=ConvertTo.DATETIME,
                timezone_info=self.timezone_info,
            ),
        )
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=TempColumns.ORDER_SUBMITTED,
                source_attribute_format=DATE_TIME_FORMAT_PARTNERS,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                convert_to=ConvertTo.DATETIME,
                timezone_info=self.timezone_info,
            ),
        )[OrderColumns.TIMESTAMPS_ORDER_SUBMITTED].fillna(
            temp_df[TempColumns.TEMP_COL_1]
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """
        Maps OrderColumns.TIMESTAMPS_ORDER_SUBMITTED
        deriving its value from TempColumns.TRADING_DATE_TIME_CONVERTED
        """
        target_field_with_prefix = add_prefix(
            prefix=ModelPrefix.ORDER_STATE,
            attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
        )
        temp_df = pd.DataFrame(
            data=self.pre_process_df.loc[
                :, TempColumns.TRADING_DATE_TIME_CONVERTED
            ].values,
            index=self.pre_process_df.index,
            columns=[target_field_with_prefix],
        )
        return self._map_field_for_non_cancels_replaces(
            df_with_target_field=temp_df, target_field=target_field_with_prefix
        )

    def _timestamps_validity_period(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.SIDE column
        Assumes self.execution_details_short_selling_indicator() ran prior
        """
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.EXECUTION_DETAILS_SHORT_SELLING_INDICATOR
            ].values,
            index=self.target_df.index,
            columns=[
                OrderColumns.TRADERS_ALGOS_WAIVER_INDICATORS_SHORT_SELLING_INDICATOR
            ],
        )

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _traders_algos_waivers_indicators_waiver_indicator(
        self,
    ) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
        from TempColumns.BUY_SELL_INDICATOR temporary column
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.BUY_SELL_INDICATOR].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID
        from SourceColumns.ORDER_ID
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORDER_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID],
        )

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_position_id(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_price(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_PRICE from the already-populated
        OrderColumns.PRICE_FORMING_DATA_PRICE
        """
        target_field_with_prefix = add_prefix(
            prefix=ModelPrefix.ORDER_STATE,
            attribute=OrderColumns.TRANSACTION_DETAILS_PRICE,
        )
        temp_df = pd.DataFrame(
            data=self.target_df.loc[
                :,
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                ),
            ].values,
            index=self.target_df.index,
            columns=[target_field_with_prefix],
        )

        return self._map_field_for_non_cancels_replaces(
            df_with_target_field=temp_df, target_field=target_field_with_prefix
        )

    def _transaction_details_price_average(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_PRICE_AVERAGE from SourceColumns.PRICE_CURRENCY
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.PRICE_CURRENCY,
                source_price_attribute=SourceColumns.AVERAGE_PRICE,
                target_price_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_AVERAGE,
            ),
        )

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY from TempColumns.CURRENCY_CODE
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.CURRENCY_CODE].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY],
        )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION with static value MONE
        """
        return pd.DataFrame(
            data=PriceNotation.MONE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION],
        )

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_QUANTITY from the already-populated
        OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
        """
        target_field_with_prefix = add_prefix(
            prefix=ModelPrefix.ORDER_STATE,
            attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY,
        )
        temp_df = pd.DataFrame(
            data=self.target_df.loc[
                :,
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                ),
            ].values,
            index=self.target_df.index,
            columns=[target_field_with_prefix],
        )

        return self._map_field_for_non_cancels_replaces(
            df_with_target_field=temp_df, target_field=target_field_with_prefix
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY from TempColumns.CURRENCY_CODE
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.CURRENCY_CODE].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY],
        )

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION with static value UNIT
        """
        return pd.DataFrame(
            data=QuantityNotation.UNIT.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION],
        )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """
        Populated from conditional logic on TempColumns.ORDER_STATUS
        """
        return MapConditional.process(
            source_frame=self.source_frame.loc[:, [TempColumns.ORDER_STATUS]],
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE,
                cases=[
                    Case(
                        query=f"(`{TempColumns.ORDER_STATUS}` == '{OrderStatus.FILL.value}') | "
                        f"(`{TempColumns.ORDER_STATUS}` == '{OrderStatus.PARF.value}')",
                        value=OrderRecordType.MARKET_SIDE.value,
                    ),
                    Case(
                        query=f"~((`{TempColumns.ORDER_STATUS}` == '{OrderStatus.FILL.value}') | "
                        f"(`{TempColumns.ORDER_STATUS}` == '{OrderStatus.PARF.value}'))",
                        value=OrderRecordType.CLIENT_SIDE.value,
                    ),
                ],
            ),
        )

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY from TempColumns.TRADING_CAPACITY
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.TRADING_CAPACITY].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """
        Maps OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME
        from the already-populated OrderColumns.TIMESTAMPS_TRADING_DATE_TIME

        Prefixing is done so that synthetic NEWOs do not get this field populated
        """
        target_field_with_prefix = add_prefix(
            prefix=ModelPrefix.ORDER_STATE,
            attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
        )
        temp_df = pd.DataFrame(
            data=self.target_df.loc[
                :,
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
                ),
            ].values,
            index=self.target_df.index,
            columns=[target_field_with_prefix],
        )
        return self._map_field_for_non_cancels_replaces(
            df_with_target_field=temp_df, target_field=target_field_with_prefix
        )

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE from SourceColumns.LAST_MARKET
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.VENUE].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE],
        )

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_venue(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_VENUE from SourceColumns.LAST_MARKET
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.VENUE].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_VENUE],
        )

    # Aux methods
    @staticmethod
    def _convert_if_float(input_str: str) -> Union[float, NAType]:
        """
        Convert to float if possible.
        Return pd.NA if not
        :return: converted value or NA
        """
        try:
            return float(input_str)
        except Exception:
            return pd.NA

    # Temporary Fields
    def _temp_buy_sell(self) -> pd.DataFrame:
        """
        Temporary Column for:
            OrderColumns.BUY_SELL
            TempColumns.BUY_SELL_INDICATOR
        Populates from SourceColumns.SIDE
        following map:
            "BUY": "1"
            "COVER": "1"
            "SELL": "2"
            "SHORT": "2"

        if not populated then default value BUY is populated
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.SIDE,
                target_attribute=TempColumns.BUY_SELL,
                case_insensitive=True,
                value_map={
                    # Buys
                    "BY": "1",
                    "B": "1",
                    "BUY": "1",
                    "BUY/C": "1",
                    "BUY/O": "1",
                    # Sells
                    "SL": "2",
                    "S": "2",
                    "SS": "2",
                    "SELL": "2",
                    "SELL/O": "2",
                    "SELL/C": "2",
                    "SHRT": "2",
                },
            ),
            auditor=self.auditor,
        )

    def _temp_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Temporary column for:
            OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
            OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
        Populates from TempColumns.BUY_SELL temporary column according to
        the map {"1": "BUYI", "2": "SELL"}
        Contains data from SourceColumns.SIDE
        """
        return MapValue.process(
            source_frame=self.pre_process_df,
            params=ParamsMapValue(
                source_attribute=TempColumns.BUY_SELL,
                target_attribute=TempColumns.BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "1": BuySellIndicator.BUYI.value,
                    "2": BuySellIndicator.SELL.value,
                },
            ),
            auditor=self.auditor,
        )

    def _temp_currency_code(self) -> pd.DataFrame:
        """
        Temporary column used for:
            OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
            OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY
            and Instrument Identifiers 'input-currency'
        Populates from SourceColumns.CURRENCY
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.PRICE_CURRENCY,
                target_ccy_attribute=TempColumns.CURRENCY_CODE,
            ),
        )

    def _temp_price(self) -> pd.DataFrame:
        """
        Temporary column for:
            OrderColumns.PRICE_FORMING_DATA_PRICE
            OrderColumns.TRANSACTION_DETAILS_PRICE
        Populates from SourceColumns.FILL_PRICE
        """
        temp_df = self.source_frame.loc[
            :,
            [
                TempColumns.ORDER_STATUS,
                SourceColumns.PRICE_CURRENCY,
                SourceColumns.EXECUTION_PRICE,
                SourceColumns.AVERAGE_PRICE,
            ],
        ]
        temp_df[TempColumns.TEMP_COL_1] = ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.PRICE_CURRENCY,
                source_price_attribute=SourceColumns.EXECUTION_PRICE,
                target_price_attribute=TempColumns.TEMP_COL_1,
            ),
        )

        return MapConditional.process(
            source_frame=temp_df,
            params=ParamsMapConditional(
                target_attribute=TempColumns.PRICE,
                cases=[
                    Case(
                        query=f"(`{TempColumns.ORDER_STATUS}`.isin(['{OrderStatus.PARF.value}','{OrderStatus.FILL.value}']))",
                        attribute=TempColumns.TEMP_COL_1,
                    ),
                ],
            ),
        )

    def _temp_newo_in_file(self) -> pd.DataFrame:
        """
        Temporary column for RemoveDuplicateNEWO
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.NEWO_IN_FILE,
                cases=[
                    Case(
                        query=f"`{TempColumns.ORDER_STATUS}` == '{OrderStatus.NEWO.value}'",
                        value=True,
                    ),
                    Case(
                        query=f"`{TempColumns.ORDER_STATUS}` != '{OrderStatus.NEWO.value}'",
                        value=False,
                    ),
                ],
            ),
        )

    def _temp_trading_capacity(self) -> pd.DataFrame:
        """
        Temporary column used in:
            OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY
            OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.LAST_CAPACITY,
                target_attribute=TempColumns.TRADING_CAPACITY,
                case_insensitive=True,
                value_map={
                    # New mappings
                    "A": TradingCapacity.AOTC.value,
                    "G": TradingCapacity.AOTC.value,
                    "I": TradingCapacity.AOTC.value,
                    "P": TradingCapacity.DEAL.value,
                    "R": TradingCapacity.MTCH.value,
                    "W": TradingCapacity.AOTC.value,
                    # Legacy non-conflicting mappings
                    "CP": TradingCapacity.DEAL.value,
                    "CA": TradingCapacity.AOTC.value,
                    "5": TradingCapacity.DEAL.value,
                },
                default_value=TradingCapacity.AOTC.value,
            ),
            auditor=self.auditor,
        )

    def _temp_date_time(self) -> pd.DataFrame:
        """
        Temporary column used in timestamps columns
        Derived from SourceColumns.RECEIVE_DATE_TIME
        """
        temp_df = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.RECEIVE_DATE_TIME,
                source_attribute_format=DATE_TIME_FORMAT_PARTNERS,
                target_attribute=TempColumns.TEMP_COL_1,
                convert_to=ConvertTo.DATETIME,
                timezone_info=self.timezone_info,
            ),
        )

        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.RECEIVE_DATE_TIME,
                source_attribute_format=DATE_TIME_FORMAT_CAXTON,
                target_attribute=TempColumns.DATE_TIME,
                convert_to=ConvertTo.DATETIME,
                timezone_info=self.timezone_info,
            ),
        )[TempColumns.DATE_TIME].fillna(temp_df[TempColumns.TEMP_COL_1])

    def _temp_date(self) -> pd.DataFrame:
        """
        Temporary column used in OrderColumns.DATE
        and derived from SourceColumns.RECEIVE_DATE_TIME
        """
        temp_df = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.RECEIVE_DATE_TIME,
                source_attribute_format=DATE_TIME_FORMAT_PARTNERS,
                target_attribute=TempColumns.TEMP_COL_1,
                convert_to=ConvertTo.DATE,
            ),
        )

        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.RECEIVE_DATE_TIME,
                source_attribute_format=DATE_TIME_FORMAT_CAXTON,
                target_attribute=TempColumns.DATE,
                convert_to=ConvertTo.DATE,
            ),
        )[TempColumns.DATE].fillna(temp_df[TempColumns.TEMP_COL_1])

    def _temp_trading_date_time(self) -> pd.DataFrame:
        """
        Temporary column used in the following
            - OrderColumns.TIMESTAMPS_TRADING_DATE_TIME
            - OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME
        and derived from TempColumns.TRADING_DATE_TIME
        """
        temp_df = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=TempColumns.TRADING_DATE_TIME,
                source_attribute_format=DATE_TIME_FORMAT_PARTNERS,
                target_attribute=TempColumns.TEMP_COL_1,
                convert_to=ConvertTo.DATETIME,
                timezone_info=self.timezone_info,
            ),
        )

        temp_df = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=TempColumns.TRADING_DATE_TIME,
                source_attribute_format=DATE_TIME_FORMAT_CAXTON,
                target_attribute=TempColumns.TRADING_DATE_TIME_CONVERTED,
                convert_to=ConvertTo.DATETIME,
                timezone_info=self.timezone_info,
            ),
        )[TempColumns.TRADING_DATE_TIME_CONVERTED].fillna(
            temp_df[TempColumns.TEMP_COL_1]
        )

        return MapConditional.process(
            source_frame=pd.concat(
                [temp_df, self.source_frame.loc[:, TempColumns.ORDER_STATUS]],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=TempColumns.TRADING_DATE_TIME_CONVERTED,
                cases=[
                    Case(
                        query=f"`{TempColumns.ORDER_STATUS}` == '{OrderStatus.NEWO.value}'",
                        value=pd.NA,
                    ),
                    Case(
                        query=f"~(`{TempColumns.ORDER_STATUS}` == '{OrderStatus.NEWO.value}')",
                        attribute=TempColumns.TRADING_DATE_TIME_CONVERTED,
                    ),
                ],
            ),
        )

    def _temp_order_id(self) -> pd.DataFrame:
        """
        Temporary column used in:
            OrderColumns.ID
            OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE
        and derived from SourceColumns.ROUTE_ID or SourceColumns.ORDER_ID
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.ORDER_ID,
                cases=[
                    Case(
                        query=f"`{SourceColumns.ROUTE_ID}`.notnull()",
                        attribute=SourceColumns.ROUTE_ID,
                    ),
                    Case(
                        query=f"`{SourceColumns.ROUTE_ID}`.isnull()",
                        attribute=SourceColumns.ORDER_ID,
                    ),
                ],
            ),
        )

    def _temp_traded_quantity(self) -> pd.DataFrame:
        """
        Temporary column for:
            OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
            OrderColumns.TRANSACTION_DETAILS_QUANTITY
        Populated from SourceColumns.FILL_QUANTITY
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.FILL_QUANTITY].values,
            index=self.source_frame.index,
            columns=[TempColumns.TRADED_QUANTITY],
        )

    def _temp_instruction_parsed(self):
        """
        Get (/d) numeric text from SourceColumns.INSTRUCTION and create the following TempColumns from it:
            TempColumns.INSTRUCTION_PARSED
            TempColumns.INTERNAL_ORDER_ID
        """
        temp_df = self.source_frame.loc[:, [SourceColumns.INSTRUCTION]]
        temp_df[TempColumns.INTERNAL_ORDER_ID] = (
            temp_df[SourceColumns.INSTRUCTION]
            .str.extract(r"(\(\d+\))", expand=False)
            .fillna(pd.NA)
        )
        temp_df[TempColumns.INSTRUCTION_PARSED] = (
            temp_df.fillna("")
            .apply(
                lambda x: x[SourceColumns.INSTRUCTION].replace(
                    str(x[TempColumns.INTERNAL_ORDER_ID]), ""
                )
                if x[SourceColumns.INSTRUCTION]
                else x[SourceColumns.INSTRUCTION],
                axis=1,
            )
            .str.strip()
        )

        # Replace () string
        temp_df[TempColumns.INTERNAL_ORDER_ID] = temp_df[
            TempColumns.INTERNAL_ORDER_ID
        ].str.replace(r"\(|\)", "")

        return temp_df.loc[
            :, [TempColumns.INTERNAL_ORDER_ID, TempColumns.INSTRUCTION_PARSED]
        ]

    def _temp_security_name_converted(self) -> pd.Series:
        """
        Creates series with a converted `Security Name` field. This is only done where
        venue is `XOSE` or `OSE`
        It converts TOPIX INDX FUTR Mar23 >> TOPIX MAR3
        Takes the first string and the last one, uppercased and removing the second to last character
        :return: series with converted security name
        """
        xose_ose_mask = self.source_frame.loc[
            :, SourceColumns.LAST_MARKET_INSTRUMENT
        ].isin(["XOSE", "OSE"])

        split_series = self.source_frame.loc[
            xose_ose_mask, SourceColumns.SECURITY_NAME
        ].str.split()
        first_part = split_series.str[0]
        second_part = (
            split_series.str[-1].str[:-2].str.upper() + split_series.str[-1].str[-1]
        )
        converted_security_name = first_part + " " + second_part
        converted_security_name.name = TempColumns.SECURITY_NAME_CONVERTED

        return converted_security_name

    def _get_temp_link_instrument_venue(self) -> pd.Series:
        """
        Auxiliary method to determine the venue used in LinkInstrument
        Uses OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE
        except for `XOSE` or `OSE` venues where it uses the venue attribute used in InstrumentIdentifiers
        :return: series with venue information
        """
        xose_ose_mask = self.target_df.loc[
            :, InstrumentIdentifierParamColumns.VENUE_ATTRIBUTE
        ].isin(["XOSE", "OSE"])

        link_instrument_venue = pd.Series(
            data=pd.NA,
            index=self.target_df.index,
            name=TempColumns.LINK_INSTRUMENT_VENUE,
        )

        link_instrument_venue.loc[~xose_ose_mask] = self.target_df.loc[
            ~xose_ose_mask, OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE
        ]
        link_instrument_venue.loc[xose_ose_mask] = self.target_df.loc[
            xose_ose_mask, InstrumentIdentifierParamColumns.VENUE_ATTRIBUTE
        ]

        return link_instrument_venue

    def _temp_venue(self) -> pd.Series:
        """
        Generates the venue by swapping any 'OSE' for 'XOSE' and
        adding any missing venue for a canceled order from its closest fill
        :return: venue series
        """

        venue_df = pd.concat(
            objs=[
                pd.DataFrame(
                    data=self.source_frame.loc[:, SourceColumns.LAST_MARKET].values,
                    index=self.source_frame.index,
                    columns=[TempColumns.VENUE],
                )
                .replace(to_replace="^OSE$", value="XOSE", regex=True)
                .replace(to_replace="^GLBX$", value="XCME", regex=True),
                self.source_frame.loc[
                    :, [SourceColumns.ORDER_ID, TempColumns.ORDER_STATUS]
                ],
                self.pre_process_df.loc[:, TempColumns.TRADING_DATE_TIME_CONVERTED],
            ],
            axis=1,
        )

        cancels = venue_df.loc[:, TempColumns.ORDER_STATUS] == OrderStatus.CAME.value
        null_last_market = venue_df.loc[:, TempColumns.VENUE].isnull()

        cancels_without_venue = cancels & null_last_market

        latest_venue_per_order_id = (
            venue_df.loc[~null_last_market, :]
            .sort_values(by=TempColumns.TRADING_DATE_TIME_CONVERTED, ascending=False)
            .drop_duplicates(subset=[SourceColumns.ORDER_ID], keep="first")
            .set_index(keys=SourceColumns.ORDER_ID)
            .loc[:, TempColumns.VENUE]
            .to_dict()
        )

        venue_df.loc[cancels_without_venue, TempColumns.VENUE] = venue_df.loc[
            cancels_without_venue, SourceColumns.ORDER_ID
        ].map(latest_venue_per_order_id)

        return venue_df.loc[:, TempColumns.VENUE].fillna("XOFF")

    @staticmethod
    def _set_timezone_info() -> None:
        """
        Sets the timezone info for the ConvertDateTime tasks
        Default is None
        """
        return None

    # Party Identifiers Fields
    def _temp_party_ids_executing_entity(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Adds lei prefix to the column
        """
        temp_df = pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORIGINATOR_LEI].values,
            index=self.source_frame.index,
            columns=[TempColumns.EXECUTING_ENTITY],
        )

        temp_df = self._get_non_null_party_from_group(
            df=temp_df, party_col_name=TempColumns.EXECUTING_ENTITY
        )

        lei_df = GetTenantLEI.process(
            source_frame=self.source_frame,
            params=ParamsGetTenantLEI(
                target_lei_column=TempColumns.ACCOUNT_FIRM_LEI,
            ),
        )
        final_df = MapConditional.process(
            source_frame=pd.concat(
                [
                    temp_df,
                    lei_df,
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=TempColumns.EXECUTING_ENTITY,
                cases=[
                    Case(
                        query=f"`{TempColumns.EXECUTING_ENTITY}`.notnull()",
                        attribute=TempColumns.EXECUTING_ENTITY,
                    ),
                    Case(
                        query=f"`{TempColumns.EXECUTING_ENTITY}`.isnull()",
                        attribute=TempColumns.ACCOUNT_FIRM_LEI,
                    ),
                ],
            ),
        )
        return add_id_or_lei(final_df, source_attribute=TempColumns.EXECUTING_ENTITY)

    def _temp_party_ids_trader(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from SourceColumns.TRADER_UUID
        Adds id prefix to the column
        """
        temp_df = pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.TRADER_NAME].values,
            index=self.source_frame.index,
            columns=[TempColumns.TRADER],
        )
        return add_id_or_lei(temp_df, source_attribute=TempColumns.TRADER)

    def _temp_party_ids_client(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from SourceColumns.ACCOUNT
        Adds id prefix to the column
        """
        temp_df = pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ACCOUNT].values,
            index=self.source_frame.index,
            columns=[TempColumns.CLIENT],
        )

        is_na_mask = temp_df[TempColumns.CLIENT].isna()
        temp_df.loc[~is_na_mask, TempColumns.CLIENT] = (
            temp_df.loc[~is_na_mask, TempColumns.CLIENT]
            .astype(str)
            .str.replace(r"\.0$", "", regex=True)
        )
        temp_df = self._get_non_null_party_from_group(
            df=temp_df, party_col_name=TempColumns.CLIENT
        )
        return add_id_or_lei(temp_df, source_attribute=TempColumns.CLIENT)

    def _temp_party_ids_counterparty(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from SourceColumns.BROKER
        Adds id prefix to the column.
        """
        temp_df = pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.BROKER].values,
            index=self.source_frame.index,
            columns=[TempColumns.COUNTERPARTY],
        )
        temp_df = self._get_non_null_party_from_group(
            df=temp_df, party_col_name=TempColumns.COUNTERPARTY
        )
        return add_id_or_lei(temp_df, source_attribute=TempColumns.COUNTERPARTY)

    def _temp_party_inv_dm(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from SourceColumns.BROKER
        Adds id prefix to the column.
        """
        temp_df = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.INVESTMENT_DECISION_MAKER,
                cases=[
                    Case(
                        query=f"`{SourceColumns.PM_NAME}`.notnull()",
                        attribute=SourceColumns.PM_NAME,
                    ),
                    Case(
                        query=f"`{SourceColumns.PM_NAME}`.isnull()",
                        attribute=SourceColumns.TRADER_NAME,
                    ),
                ],
            ),
        )
        return PartyPrefix.ID + temp_df

    def _get_non_null_party_from_group(self, df: pd.DataFrame, party_col_name: str):
        """
        This function looks for null values in df[party_col_name] where party_col_name
        might be the temp column for the Client/Counterparty/Exec Entity. If there are null
        values, it gets the corresponding order ids, and gets the first non-null
        client/counterparty/exec entity from other rows with the same order IDs.

        NOTE: If there is a null value for client/counterparty, and there are no other
        rows with the same order ID, it will remain null.

        :param df: Data Frame containing 2 columns:
        1. required party (client/counterparty/exec entity for example)
        2. orderIdentifiers.orderIdCode
        :param party_col_name: Name of the party column in the df which is replaced
        with non-null values with the same order id
        :return:
        """
        # If there are empty values, we need to group on the order id and get the client
        # or counterparty id from any element in this group where the respective party
        # ID is not null

        # Add the order id to the df
        df = pd.concat(
            [
                df,
                self.target_df.loc[:, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
            ],
            axis=1,
        )
        # Get the corresponding order ids for cases where the respective party is null
        is_na_mask = df.loc[:, party_col_name].isna()
        null_party_order_ids = df.loc[
            is_na_mask, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE
        ].tolist()
        # Get all the rows in temp_df with the order ids from the previous step
        matching_rows_df = df.loc[
            df[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE].isin(null_party_order_ids)
        ]
        # Keep only those rows where the respective party ID is NOT NULL
        non_null_party_rows = matching_rows_df.loc[
            matching_rows_df[party_col_name].notnull()
        ]
        # Create a series with the Order id as the index and the FIRST non-null
        # respective party ID as the data
        party_replacement_series = non_null_party_rows.groupby(
            OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE
        )[party_col_name].first()
        # Assign the fetched party value back to temp_df for the rows which had null party
        # Add fillna() in case there are np.nans (which can't be assigned to a String column)
        df.loc[is_na_mask, party_col_name] = (
            df.loc[is_na_mask, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE]
            .map(party_replacement_series.to_dict())
            .fillna(pd.NA)
        )
        # Drop order id column
        df = df.drop(columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE])
        return df

    # Instrument Identifiers Fields
    def _temp_instr_ids_asset_class(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates based on values of SourceColumns.PRODUCT
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.ASSET_CLASS,
                cases=[
                    Case(
                        query=f"`{SourceColumns.PRODUCT}`.str.fullmatch('Equity', case=False, na=False)",
                        value=AssetClass.EQUITY,
                    ),
                    Case(
                        query=f"`{SourceColumns.PRODUCT}`.str.contains('Future|Index', case=False, na=False)",
                        value=AssetClass.FUTURE,
                    ),
                    Case(
                        query=f"`{SourceColumns.PRODUCT}`.str.fullmatch('Option', case=False, na=False)",
                        value=AssetClass.OPTION,
                    ),
                ],
            ),
        )

    def _temp_instr_ids_underlying_symbol(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates based on values of SourceColumns.LOCAL_EXCH_SYMBOL or SourceColumns.FULL_EXCH_SYMBOL
        """
        temp_df = self.source_frame.loc[
            :,
            [
                SourceColumns.LAST_MARKET,
                SourceColumns.FULL_EXCH_SYMBOL,
                SourceColumns.LOCAL_EXCH_SYMBOL,
            ],
        ]
        temp_df[TempColumns.MONTH] = (
            temp_df[SourceColumns.FULL_EXCH_SYMBOL]
            .str[3:5]
            .map({v: k for k, v in MapMonth.mapping.items()})
        )
        temp_df[TempColumns.YEAR] = temp_df[SourceColumns.FULL_EXCH_SYMBOL].str[-1]
        temp_df[TempColumns.TEMP_COL_1] = (
            temp_df[SourceColumns.FULL_EXCH_SYMBOL].str[:3]
            + temp_df[TempColumns.MONTH]
            + temp_df[TempColumns.YEAR]
        )

        return MapConditional.process(
            source_frame=temp_df,
            params=ParamsMapConditional(
                target_attribute=TempColumns.UNDERLYING_SYMBOL,
                cases=[
                    Case(
                        query=f"`{SourceColumns.LAST_MARKET}`.str.fullmatch('XHKF', case=False, na=False)",
                        attribute=TempColumns.TEMP_COL_1,
                    ),
                    Case(
                        query=f"~(`{SourceColumns.LAST_MARKET}`.str.fullmatch('XHKF', case=False, na=False))",
                        attribute=SourceColumns.LOCAL_EXCH_SYMBOL,
                    ),
                ],
            ),
        )

    def _temp_instr_fallback_instrument_unique_identifier(self) -> pd.DataFrame:
        """
        Temp column used in Instrument Fallback
        Populates from SourceColumns.ISIN and SourceColumns.BASE_CURRENCY
        """
        return pd.DataFrame(
            data=(
                self.source_frame.loc[:, SourceColumns.ISIN_INSTRUMENT]
                + self.pre_process_df.loc[:, TempColumns.CURRENCY_CODE]
                + self.source_frame.loc[:, SourceColumns.LAST_MARKET]
            ).values,
            index=self.source_frame.index,
            columns=[TempColumns.INSTR_UNIQUE_IDENTIFIER],
        )

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""

    def _map_field_for_non_cancels_replaces(
        self, df_with_target_field: pd.DataFrame, target_field: str
    ):
        """
        For a df df_with_target_field with field target_field, this function concatenates the df
        with the TempColumns.ORDER_STATUS column in self.source_frame. It returns a df with
        target_field, which is only populated if the TempColumns.ORDER_STATUS column is
        CAME or REME.

        :param df_with_target_field:
        :param target_field:
        :return:
        """
        cancel_or_replace_query = f"`{TempColumns.ORDER_STATUS}`.str.fullmatch('{OrderStatus.CAME.value}|{OrderStatus.REME.value}', case=False, na=False)"  # noqa E501
        return MapConditional.process(
            source_frame=pd.concat(
                [
                    df_with_target_field,
                    self.source_frame.loc[:, [TempColumns.ORDER_STATUS]],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=target_field,
                cases=[
                    Case(
                        query=cancel_or_replace_query,
                        as_empty=True,
                    ),
                    Case(
                        query=f"~{cancel_or_replace_query}",
                        attribute=target_field,
                    ),
                ],
            ),
        )
