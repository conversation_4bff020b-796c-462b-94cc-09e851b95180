import json
import os
from json import JSO<PERSON><PERSON>ode<PERSON>rror
from typing import Any
from typing import List
from typing import NoReturn

import pandas as pd
from prefect.engine import signals
from se_core_tasks.map.map_conditional import Case
from se_core_tasks.utils.datetime import DatetimeFormat
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import DeliveryType
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_elastic_schema.static.reference import InstrumentIdCodeType
from se_elastic_schema.static.reference import OrderRecordType
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order.static import Venue
from se_trades_tasks.order_and_tr.static import AssetClass
from se_trades_tasks.order_and_tr.static import PartyPrefix
from swarm.task.auditor import Auditor
from swarm.task.io.read.result import ExtractPathResult

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ConvertMinorToMajorParams,
)
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ConcatAttributesParams,
)
from swarm_tasks.transform.concat.concat_attributes import Prefix
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ConvertDatetimeParams,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as MapAttributeParams
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as MapConditionalParams
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as MapValueParams
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as MergeMarketIdentifiersParams,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as PartyIdentifiersParams,
)
from swarm_tasks.utilities.static import MetaModel
from swarm_tasks.utilities.static import SWARM_FILE_URL


class Columns:
    @classmethod
    def get_all_columns(cls):
        fields = []
        for name, value in vars(cls).items():
            if not (name.startswith("__") or isinstance(value, classmethod)):
                fields.append(value)
        return fields


class SourceColumns(Columns):
    ACCOUNT_ID = "account_id"
    CELL_SEQ_ID = "cell_seq_id"
    CLIENTREQUESTPRICINGDATA = "clientRequestPricingData"
    CLIENT_PRICE = "client_price"
    CREATE_TIME = "create_time"
    DIVISION_ID = "division_id"
    DIVISION_NAME_C = "division_name_c"
    EFFECTIVE = "effective"
    INSTRUMENT = "instrument"
    ORDER_ID = "order_id"
    PRICE_DETAILS = "price_details"
    REASON = "reason"
    REQUEST_ID = "request_id"
    REQUEST_TIMESTAMP = "request_timestamp"
    TRADE_OPENED = "trade_opened"
    TRADE_REDUCED = "trade_reduced"
    TRADES_CLOSED = "trades_closed"
    TYPE = "type"
    UNITS = "units"
    USER_ID = "user_id"
    USERNAME = "username"

    @classmethod
    def dict_columns(cls):
        return [
            cls.CLIENTREQUESTPRICINGDATA,
            cls.CLIENT_PRICE,
            cls.EFFECTIVE,
            cls.PRICE_DETAILS,
            cls.TRADES_CLOSED,
            cls.TRADE_OPENED,
            cls.TRADE_REDUCED,
        ]


class TempColumns:
    ALL_TRADES = "__all_trades__"
    ASSET_CLASS = "__asset_class__"
    C_NAME_UPPER = "__c_name_upper__"
    BEST_EXECUTION_DATA_EFFECTIVE_SPREAD = "bestExecutionData.effectiveSpread"
    CFD_OR_SB = "__cfd_or_sb__"
    CLIENT_PRICE_ASKS_PRICE = "client_price.asks.price"
    CLIENT_PRICE_ASKS_PRICE_CONVERTED = "client_price.asks.price_converted"
    CLIENT_PRICE_BIDS_PRICE = "client_price.bids.price"
    CLIENT_PRICE_BIDS_PRICE_CONVERTED = "client_price.bids.price_converted"
    CLIENTREQUESTPRICINGDATA_VWAP = "clientRequestPricingData.vwap"
    CLIENTREQUESTPRICINGDATA_VWAP_ADJUSTED = "clientRequestPricingData.vwap_adjusted"
    CLIENTREQUESTPRICINGDATA_VWAP_CONVERTED = "clientRequestPricingData.vwap_converted"
    COMMODITIES_RATES_TICKER = "__commodities_rates_ticker__"
    CREATE_TIME = "__create_time__"
    DERIVATIVE_DELIVERY_TYPE = "__derivative_delivery_type__"
    DERIVATIVE_PRICE_MULTIPLIER = "__derivative_price_multiplier__"
    DERIVATIVE_UNERLYING_INDEX_NAME = "__derivative_underlying_index_name__"
    DIVISION_NAME_OR_ID = "__division_name_or_id__"
    EFFECTIVE_TIME = "effective.time"
    EFFECTIVE_TIMESTAMP = "effective.timestamp"
    EFFECTIVE_TIME_CONVERTED = "__effective_time_converted__"
    EFFECTIVE_TIMESTAMP_CONVERTED = "__effective_timestamp_converted__"
    EXT_INSTRUMENT_ID_CODE = "__ext_instrument_id_code__"
    EXT_INSTRUMENT_ID_CODE_TYPE = "__ext_instrument_id_code_type__"
    EXT_INSTRUMENT_UNIQUE_IDENTIFIER = "__ext_instrument_unique_identifier__"
    EXT_ALTERNATIVE_INSTRUMENT_IDENTIFIER = "__ext_alternative_instrument_identifier__"
    EXT_EXCHANGE_SYMBOL = "__ext_exchange_symbol__"
    EXT_PRICE_NOTATION = "__ext_price_notation__"
    IS_CREATED_THROUGH_FALLBACK = "__is_created_through_fallback__"
    FV_ADJUSTMENT = "fv_adjustment"
    INSTRUMENT = "__instrument__"
    INSTRUMENT_FULL_NAME = "__instrument_full_name__"
    EXT_UL_INSTRUMENTS_ISIN = "__ext_ul_instruments_isin__"
    NEWO_IN_FILE = "__newo_in_file__"
    OANDA = "__Oanda__"
    PRICE_ADJUSTED = "__price_adjusted__"
    PRICE_CCY = "__price_ccy__"
    PRICE_CCY_CONVERTED = "__price_ccy_converted__"
    PRICE_CONVERTED = "_price_converted__"
    PRICE_DETAILS_SKEW_APPLIED = "price_details.skew.applied"
    PRICE_DETAILS_SKEW_DYNAMIC = "price_details.skew.dynamic"
    PRICE_TYPE_MAPPED = "__price_type_mapped__"
    QUANTITY_CCY = "__quantity_ccy__"
    QUANTITY_CCY_CONVERTED = "__quantity_ccy_converted__"
    REQUEST_TIMESTAMP = "__request_timestamp__"
    SOURCE_INDEX = "__source_index__"
    TICKER_EXPIRY_DATE = "__ticker_expiry_date__"
    TIMESTAMP = "__timestamp__"
    TIMESTAMP_DATE = "__timestamp_date__"
    TRADE_TYPE = "__trade_type__"
    TRADES_TRADE_ID = "trades_trade_id"
    TRADES_UNITS = "trades_units"
    TRADES_UNITS_DIVIDED = "trades_units_divided"
    TRADES_PRICE = "trades_price"
    UNDERLIER_SYMBOL = "_underlier_symbol"
    UNITS_DIVIDED = "__units_divided__"
    V20 = "__V20__"


class InstrumentDataColumns(Columns):
    C_CFI = "c_CFI"
    C_UNDERLYING_EXPIRY_DATE = "c_underlying_expiry_date"
    C_NAME = "c_name"
    C_ID = "c_id"
    C_PRODUCT = "c_product"
    C_V20_ID2 = "c_v20_id2"
    C_V20_LOT_SIZE = "c_v20_lot_size"
    M_ID_EXCH_SYMBOL = "m_ID_Exch_Symbol"
    M_ISIN = "m_ISIN"
    M_ASSET = "m_asset"
    M_BBG_TICKER = "m_BBG_Ticker"
    M_GENERIC_BBG = "m_Generic_BBG"
    M_NAME = "m_name"
    M_PRICE_TYPE = "m_price_type"
    M_PRIMARY_MIC = "m_primary_mic"
    T_ISIN = "t_isin"
    T_TICKER = "t_ticker"


class OandaV20OrderTransformations(AbstractOrderTransformations):
    def __init__(
        self,
        source_frame: pd.DataFrame,
        auditor: Auditor,
        instrument_data: ExtractPathResult = None,
        **kwargs,
    ):
        self.instrument_data = self.read_instrument_data(instrument_data)

        source_frame = source_frame.loc[:, SourceColumns.get_all_columns()]
        source_frame.loc[:, SourceColumns.dict_columns()] = source_frame.loc[
            :, SourceColumns.dict_columns()
        ].applymap(self.convert_str_to_dicts)
        source_frame.loc[:, TempColumns.SOURCE_INDEX] = source_frame.index

        # get one row per trade
        source_frame = self.join_trade_info(source_frame=source_frame)

        super().__init__(source_frame=source_frame, auditor=auditor)

    def _pre_process(self):
        self.explode_dict_fields()
        self.get_instrument_data()
        self.get_fv_adjustment()
        self.process_timestamps()

        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self.get_currencies_converted(),
                self.get_price_type_mapped(),
                self.get_counterparty(),
                self.get_asset_class(),
            ],
            axis=1,
        )

        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self.get_prices_converted(),  # needs price ccy converted
            ],
            axis=1,
        )

    def _post_process(self):
        self.target_df = pd.concat(
            [
                self.target_df,
                self.pre_process_df.loc[
                    :,
                    [
                        InstrumentDataColumns.C_CFI,
                        InstrumentDataColumns.C_NAME,
                    ],
                ],
                pd.DataFrame(
                    data=True,
                    index=self.source_frame.index,
                    columns=[TempColumns.IS_CREATED_THROUGH_FALLBACK],
                ),
                pd.DataFrame(
                    data=True,
                    index=self.source_frame.index,
                    columns=[add_prefix(ModelPrefix.ORDER, TempColumns.NEWO_IN_FILE)],
                ),
                pd.DataFrame(
                    data=self.pre_process_df.loc[
                        :, TempColumns.CLIENT_PRICE_ASKS_PRICE_CONVERTED
                    ]
                    - self.pre_process_df.loc[
                        :, TempColumns.CLIENT_PRICE_BIDS_PRICE_CONVERTED
                    ],
                    index=self.source_frame.index,
                    columns=[TempColumns.BEST_EXECUTION_DATA_EFFECTIVE_SPREAD],
                ),
                self.get_instrument_fallback_fields(),
            ],
            axis=1,
        )

    def _buy_sell(self) -> pd.DataFrame:
        """
        Populates with `1` or `2` depending on if TempColumns.TRADES_UNITS is bigger/equal or smaller than zero
        """
        return pd.concat(
            [
                MapConditional.process(
                    source_frame=self.source_frame,
                    params=MapConditionalParams(
                        target_attribute=add_prefix(
                            ModelPrefix.ORDER, OrderColumns.BUY_SELL
                        ),
                        cases=[
                            Case(query=f"`{TempColumns.TRADES_UNITS}` >= 0", value="1"),
                            Case(query=f"`{TempColumns.TRADES_UNITS}` < 0", value="2"),
                        ],
                    ),
                ),
                MapConditional.process(
                    source_frame=self.source_frame,
                    params=MapConditionalParams(
                        target_attribute=add_prefix(
                            ModelPrefix.ORDER_STATE, OrderColumns.BUY_SELL
                        ),
                        cases=[
                            Case(query=f"`{TempColumns.TRADES_UNITS}` >= 0", value="1"),
                            Case(query=f"`{TempColumns.TRADES_UNITS}` < 0", value="2"),
                        ],
                    ),
                ),
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        """
        Populates with 'Order Oanda V20
        """
        return pd.DataFrame(
            data="V20",
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """
        Populates from these columns in this priority:
            SourceColumns.REQUEST_TIMESTAMP,
            SourceColumns.CREATE_TIME,
            TempColumns.EFFECTIVE_TIME,
            TempColumns.EFFECTIVE_TIMESTAMP
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.TIMESTAMP_DATE].values,
            index=self.source_frame.index,
            columns=[OrderColumns.DATE],
        )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Populates with `BUYI` or `SELL` depending on if TempColumns.TRADES_UNITS is bigger/equal or smaller than zero
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                cases=[
                    Case(
                        query=f"`{TempColumns.TRADES_UNITS}` >= 0",
                        value=BuySellIndicator.BUYI.value,
                    ),
                    Case(
                        query=f"`{TempColumns.TRADES_UNITS}` < 0",
                        value=BuySellIndicator.SELL.value,
                    ),
                ],
            ),
        )

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """
        Populates with the difference between TempColumns.CLIENTREQUESTPRICINGDATA_VWAP and TempColumns.FV_ADJUSTMENT
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=MapAttributeParams(
                source_attribute=TempColumns.CLIENTREQUESTPRICINGDATA_VWAP_CONVERTED,
                target_attribute=OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE,
            ),
            auditor=self.auditor,
        )

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_order_status(self) -> pd.DataFrame:
        """
        Populates `NEWO` for Order and `FILL` for OrderState
        """
        return pd.DataFrame(
            data=[[OrderStatus.NEWO.value, OrderStatus.FILL.value]],
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                ),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                ),
            ],
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """
        Populates with `LIMIT` or `MARKET` depending on if OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE is populated
        Assumes self._execution_details_limit_price() was executed before
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.REASON].values,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_ORDER_TYPE],
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """
        Populates with a concatenation of the following fields, prefixed with heir column name:
            SourceColumns.TYPE,
            SourceColumns.REQUEST_ID,
            SourceColumns.ACCOUNT_ID,
            InstrumentDataColumns.C_V20_ID2,
            SourceColumns.REASON,
            TempColumns.FV_ADJUSTMENT,
            SourceColumns.USERNAME,
        """
        return ConcatAttributes.process(
            source_frame=pd.concat([self.source_frame, self.pre_process_df], axis=1),
            params=ConcatAttributesParams(
                source_attributes=[
                    SourceColumns.TYPE,
                    SourceColumns.REQUEST_ID,
                    SourceColumns.ACCOUNT_ID,
                    InstrumentDataColumns.C_V20_ID2,
                    SourceColumns.REASON,
                    TempColumns.FV_ADJUSTMENT,
                    SourceColumns.USERNAME,
                ],
                delimiter=", ",
                prefix=Prefix.attribute_name.value,
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
            ),
        )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_stop_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populates with `AOTC`
        """
        return pd.DataFrame(
            data=TradingCapacity.AOTC.value,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY],
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _financing_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _hierarchy(self) -> pd.DataFrame:
        """Not Implemented"""

    def _id(self) -> pd.DataFrame:
        """
        Populates by concatenating:
            SourceColumns.ORDER_ID,
            SourceColumns.CELL_SEQ_ID,
            SourceColumns.USER_ID,
        """
        return pd.concat(
            [
                ConcatAttributes.process(
                    source_frame=self.source_frame,
                    params=ConcatAttributesParams(
                        delimiter="_",
                        source_attributes=[
                            SourceColumns.ORDER_ID,
                            SourceColumns.CELL_SEQ_ID,
                            SourceColumns.USER_ID,
                        ],
                        target_attribute=add_prefix(ModelPrefix.ORDER, OrderColumns.ID),
                    ),
                ),
                ConcatAttributes.process(
                    source_frame=self.source_frame,
                    params=ConcatAttributesParams(
                        delimiter="_",
                        source_attributes=[
                            SourceColumns.ORDER_ID,
                            SourceColumns.CELL_SEQ_ID,
                            SourceColumns.USER_ID,
                        ],
                        target_attribute=add_prefix(
                            ModelPrefix.ORDER_STATE, OrderColumns.ID
                        ),
                    ),
                ),
            ],
            axis=1,
        )

    def _is_discretionary(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_iceberg(self):
        """Not Implemented"""

    def _is_repo(self):
        """Not Implemented"""

    def _is_synthetic(self):
        """Not Implemented"""

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        """Not Implemented"""

    def _market_identifiers(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_instrument and _market_identifiers_parties have been
        called earlier"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=MergeMarketIdentifiersParams(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """not implemented
        flow does not use instrument identifiers, uses fallback instead"""

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """
        Populates party identifiers from:
            SourceColumns.USER_ID,
            TempColumns.DIVISION_NAME_OR_ID,
            OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
        """
        parties_df = pd.concat(
            [
                PartyPrefix.ID
                + self.source_frame.loc[:, SourceColumns.USER_ID].astype(str),
                PartyPrefix.ID
                + self.pre_process_df.loc[:, TempColumns.DIVISION_NAME_OR_ID],
                self.target_df.loc[
                    :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
                ],
            ],
            axis=1,
        )
        return GenericOrderPartyIdentifiers.process(
            source_frame=parties_df,
            params=PartyIdentifiersParams(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                trader_identifier=SourceColumns.USER_ID,
                client_identifier=SourceColumns.USER_ID,
                counterparty_identifier=TempColumns.DIVISION_NAME_OR_ID,
                executing_entity_identifier=TempColumns.DIVISION_NAME_OR_ID,
                use_buy_mask_for_buyer_seller=True,
                buy_sell_side_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                buyer_identifier=SourceColumns.USER_ID,
                seller_identifier=TempColumns.DIVISION_NAME_OR_ID,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=[[MetaModel.ORDER, MetaModel.ORDER_STATE]],
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.META_MODEL,
                ),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.META_MODEL,
                ),
            ],
        )

    def _order_class(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """
        Populates by concatenating:
            SourceColumns.ORDER_ID,
            SourceColumns.CELL_SEQ_ID,
            SourceColumns.USER_ID,
        """
        return ConcatAttributes.process(
            source_frame=self.source_frame,
            params=ConcatAttributesParams(
                delimiter="_",
                source_attributes=[
                    SourceColumns.ORDER_ID,
                    SourceColumns.CELL_SEQ_ID,
                    SourceColumns.USER_ID,
                ],
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
            ),
        )

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """
        Populates from TempColumns.TRADES_TRADE_ID
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, TempColumns.TRADES_TRADE_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO],
        )

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.UNITS divided by InstrumentDataColumns.C_V20_LOT_SIZE
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.UNITS_DIVIDED].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY],
        )

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price(self) -> pd.DataFrame:
        """
        Populates with the difference between TempColumns.TRADES_PRICE and TempColumns.FV_ADJUSTMENT
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.PRICE_CONVERTED].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_PRICE],
        )

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """
        Populates from TempColumns.TRADES_UNITS divided by InstrumentDataColumns.C_V20_LOT_SIZE
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.TRADES_UNITS_DIVIDED].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    ModelPrefix.ORDER_STATE,
                    OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                )
            ],
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """
        Populates from TempColumns.TRADES_TRADE_ID
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, TempColumns.TRADES_TRADE_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO],
        )

    def _source_index(self) -> pd.DataFrame:
        """
        Populates from TempColumns.SOURCE_INDEX
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, TempColumns.SOURCE_INDEX].astype(str).values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_INDEX],
        )

    def _source_key(self) -> pd.DataFrame:
        """
        Populates from SWARM_FILE_URL environment variable
        """
        return pd.DataFrame(
            data=os.getenv(SWARM_FILE_URL),
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_KEY],
        )

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_order_received(self) -> pd.DataFrame:
        """
        Populates from these columns in this priority:
            SourceColumns.REQUEST_TIMESTAMP,
            SourceColumns.CREATE_TIME,
            TempColumns.EFFECTIVE_TIME,
            TempColumns.EFFECTIVE_TIMESTAMP
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.TIMESTAMP].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_RECEIVED],
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """
        Populates from these columns in this priority:
            SourceColumns.REQUEST_TIMESTAMP,
            SourceColumns.CREATE_TIME,
            TempColumns.EFFECTIVE_TIME,
            TempColumns.EFFECTIVE_TIMESTAMP
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.TIMESTAMP].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_SUBMITTED],
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """
        Populates from TempColumns.EFFECTIVE_TIME
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[
                :, TempColumns.EFFECTIVE_TIME_CONVERTED
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TIMESTAMPS_TRADING_DATE_TIME],
        )

    def _timestamps_validity_period(self) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Populates with `BUYI` or `SELL` depending on if TempColumns.TRADES_UNITS is bigger/equal or smaller than zero
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                cases=[
                    Case(
                        query=f"`{TempColumns.TRADES_UNITS}` >= 0",
                        value=BuySellIndicator.BUYI.value,
                    ),
                    Case(
                        query=f"`{TempColumns.TRADES_UNITS}` < 0",
                        value=BuySellIndicator.SELL.value,
                    ),
                ],
            ),
        )

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price(self) -> pd.DataFrame:
        """
        Populates with the difference between TempColumns.TRADES_PRICE and TempColumns.FV_ADJUSTMENT
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.PRICE_CONVERTED].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    ModelPrefix.ORDER_STATE, OrderColumns.TRANSACTION_DETAILS_PRICE
                )
            ],
        )

    def _transaction_details_price_average(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """
        Populated from the second value of InstrumentDataColumns.C_V20_ID2
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.PRICE_CCY_CONVERTED].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY],
        )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """
        Populated with `MONE` if InstrumentDataColumns.M_ASSET == 'indices',
        else from InstrumentDataColumns.M_PRICE_TYPE, with the following mapping:
            "Monetary value": `MONE`,
            "Basis Points": `BAPO`,
            "Percentage": `PERC`,
        """
        monetary_query = f"`{InstrumentDataColumns.M_ASSET}`.str.fullmatch('indices', case=False, na=False)"
        return MapConditional.process(
            source_frame=self.pre_process_df,
            params=MapConditionalParams(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION,
                cases=[
                    Case(query=f"{monetary_query}", value=PriceNotation.MONE.value),
                    Case(
                        query=f"~({monetary_query})",
                        attribute=TempColumns.PRICE_TYPE_MAPPED,
                    ),
                ],
            ),
        )

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """
        Populates from TempColumns.TRADES_UNITS divided by InstrumentDataColumns.C_V20_LOT_SIZE
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.TRADES_UNITS_DIVIDED].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    ModelPrefix.ORDER_STATE, OrderColumns.TRANSACTION_DETAILS_QUANTITY
                )
            ],
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """
        Populates from first value of InstrumentDataColumns.C_V20_ID
        if InstrumentDataColumns.M_ASSET == 'currency' or 'cryptocurrency'
        """
        currency_query = f"`{InstrumentDataColumns.M_ASSET}`.str.fullmatch('currency', case=False, na=False)"
        crypto_query = f"`{InstrumentDataColumns.M_ASSET}`.str.fullmatch('cryptocurrency', case=False, na=False)"
        return MapConditional.process(
            source_frame=self.pre_process_df,
            params=MapConditionalParams(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
                cases=[
                    Case(
                        query=f"({currency_query}) | ({crypto_query})",
                        attribute=TempColumns.QUANTITY_CCY_CONVERTED,
                    ),
                ],
            ),
        )

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """
        Populates with `MONE` or `UNIT` depending on if InstrumentDataColumns.M_ASSET == 'currency'
        :return:
        """
        asset_query = f"`{InstrumentDataColumns.M_ASSET}`.str.fullmatch('currency', case=False, na=False)"
        return MapConditional.process(
            source_frame=self.pre_process_df,
            params=MapConditionalParams(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
                cases=[
                    Case(query=f"{asset_query}", value=QuantityNotation.MONE.value),
                    Case(query=f"~({asset_query})", value=QuantityNotation.UNIT.value),
                ],
            ),
        )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """
        Populates with `Market Side` or `Client Side` depending on if SourceColumns.REASON == 'MARKET_ORDER'
        """
        reason_query = f"`{SourceColumns.REASON}`.str.fullmatch('MARKET_ORDER', case=False, na=False)"
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE,
                cases=[
                    Case(
                        query=reason_query,
                        value=OrderRecordType.MARKET_SIDE.value,
                    ),
                    Case(
                        query=f"~({reason_query})",
                        value=OrderRecordType.CLIENT_SIDE.value,
                    ),
                ],
            ),
        )

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populates with `AOTC`
        """
        return pd.DataFrame(
            data=TradingCapacity.AOTC.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """
        Populates from TempColumns.EFFECTIVE_TIME
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[
                :, TempColumns.EFFECTIVE_TIME_CONVERTED
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME],
        )

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """
        Populates from InstrumentDataColumns.M_PRIMARY_MIC if available,
        else uses `XOFF`
        """
        primary_mic_query = f"`{InstrumentDataColumns.M_PRIMARY_MIC}`.notnull()"
        return MapConditional.process(
            source_frame=self.pre_process_df,
            params=MapConditionalParams(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                cases=[
                    Case(
                        query=f"{primary_mic_query}",
                        attribute=InstrumentDataColumns.M_PRIMARY_MIC,
                    ),
                    Case(query=f"~({primary_mic_query})", value=Venue.XOFF),
                ],
            ),
        )

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_venue(self) -> pd.DataFrame:
        """
        Populates with `XOFF`
        """
        return pd.DataFrame(
            data=Venue.XOFF,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_VENUE],
        )

    # Auxiliary Functions
    @staticmethod
    def read_instrument_data(instrument_data: ExtractPathResult) -> NoReturn:
        """
        Reads Instrument data CSV into a Dataframe. Raises SKIP if missing path.
        :param instrument_data: path to instrument csv
        """
        if not instrument_data:
            raise signals.SKIP("Missing Instrument Data dataframe")
        return pd.read_csv(filepath_or_buffer=instrument_data.path)

    @staticmethod
    def convert_str_to_dicts(x: Any) -> Any:
        """
        If the field is a string, tries to convert to a dictionary with json.loads.
        Else, returns as is
        :param x: instance to convert to dictionary
        :return: converted dictionary or original variable
        """
        if isinstance(x, str):
            try:
                json_dict = json.loads(x.replace("'", '"'))
                return json_dict
            except JSONDecodeError:
                return x
        return x

    def join_trade_info(self, source_frame: pd.DataFrame) -> pd.DataFrame:
        """
        This method takes the 3 trades columns:
            SourceColumns.TRADE_OPENED,
            SourceColumns.TRADE_REDUCED,
            SourceColumns.TRADES_CLOSED,
        Iterates over each column and creates a dataframe with a new column containing
            the corresponding trades for that row in a list.
        These 3 dataframes are then concatenated vertically.
        The original trades columns are removed and we're left with column per trade field
            of interest isntead
        :param source_frame: pd.Dataframe with trades columns
        :return: pd.Dataframe with one row per trade present in each of the trades columns
        """

        # trade columns to join
        trade_types = [
            SourceColumns.TRADE_OPENED,
            SourceColumns.TRADE_REDUCED,
            SourceColumns.TRADES_CLOSED,
        ]

        all_trades_dfs_list = []
        for trade_type in trade_types:
            # keep only trades from trade type
            trade_not_na_mask = source_frame.loc[:, trade_type].notna()
            trade_not_empty_mask = source_frame.loc[
                trade_not_na_mask, trade_type
            ].astype(bool)
            trade_populated_mask = trade_not_na_mask & trade_not_empty_mask
            if not trade_populated_mask.any():
                continue
            trade_populated_df = source_frame.loc[trade_populated_mask, :]

            # turn to list, if not already, add column with trade type, remove source trade columns
            if trade_type != SourceColumns.TRADES_CLOSED:
                self.turn_non_list_values_to_list(trade_populated_df, trade_type)
            else:
                trade_populated_df.loc[
                    :, TempColumns.ALL_TRADES
                ] = trade_populated_df.loc[:, trade_type]
            trade_populated_df.loc[:, TempColumns.TRADE_TYPE] = trade_type
            trade_populated_df = trade_populated_df.loc[
                :,
                [
                    column
                    for column in trade_populated_df.columns
                    if column not in trade_types
                ],
            ]
            all_trades_dfs_list.append(trade_populated_df)

        # concatenate the three dataframes vertically
        all_trades_df = pd.concat(all_trades_dfs_list, axis=0)
        all_trades_df = all_trades_df.explode(TempColumns.ALL_TRADES).reset_index(
            drop=True
        )

        # generate the three needed columns from the source trade columns
        all_trades_df.loc[:, TempColumns.TRADES_TRADE_ID] = all_trades_df.loc[
            :, TempColumns.ALL_TRADES
        ].str.get("trade_id")
        all_trades_df.loc[:, TempColumns.TRADES_UNITS] = (
            all_trades_df.loc[:, TempColumns.ALL_TRADES]
            .str.get("units")
            .astype("float")
        )
        all_trades_df.loc[:, TempColumns.TRADES_PRICE] = (
            all_trades_df.loc[:, TempColumns.ALL_TRADES]
            .str.get("price")
            .astype("float")
        )

        return all_trades_df.drop(labels=TempColumns.ALL_TRADES, axis=1)

    @staticmethod
    def turn_non_list_values_to_list(
        trade_populated_df: pd.DataFrame, trade_type: str
    ) -> NoReturn:
        """
        Turns the values in the trade column into a list of themselves if they're not already list
        This is done with a similar logic equivalent to np.where found in the specs:
            https://numpy.org/doc/1.19/reference/generated/numpy.where.html
            as I couldn't get np.where to convert a value to list
        :param trade_populated_df: dataframe with trade columns
        :param trade_type: trade type str value, used to fetch the values
        """
        type_is_list = trade_populated_df[trade_type].apply(type) == List
        not_list_trades = trade_populated_df[trade_type].values
        list_trades = [
            val if cond else [val] for cond, val in zip(type_is_list, not_list_trades)
        ]
        trade_populated_df[TempColumns.ALL_TRADES] = list_trades

    def explode_dict_fields(self) -> NoReturn:
        """
        Several columns in the source dataframe contain dicionaries from which we need to
            extract some fields.
        This method aggregates these transformations per field and concatenates it at the end,
            dropping the original columns.
        """
        self.get_client_request_pricing_data_fields()
        self.get_client_price_fields()
        self.get_effective_fields()
        self.get_price_details_fields()

        self.source_frame = self.source_frame.drop(
            labels=[
                SourceColumns.CLIENTREQUESTPRICINGDATA,
                SourceColumns.CLIENT_PRICE,
                SourceColumns.EFFECTIVE,
                SourceColumns.PRICE_DETAILS,
            ],
            axis=1,
        )

    def get_client_request_pricing_data_fields(self):
        """
        This method iterates over the SourceColumns.CLIENTREQUESTPRICINGDATA columns, fetching the values from
            'vwap' values to be inserted into the self.source_frame.
        """
        not_null = self.source_frame.loc[
            :, SourceColumns.CLIENTREQUESTPRICINGDATA
        ].notnull()
        self.source_frame.loc[not_null, TempColumns.CLIENTREQUESTPRICINGDATA_VWAP] = (
            self.source_frame.loc[not_null, SourceColumns.CLIENTREQUESTPRICINGDATA]
            .str.get("vwap")
            .astype("float")
        )
        self.source_frame.loc[
            :, TempColumns.CLIENTREQUESTPRICINGDATA_VWAP
        ] = self.source_frame.loc[:, TempColumns.CLIENTREQUESTPRICINGDATA_VWAP].fillna(
            pd.NA
        )

    def get_client_price_fields(self):
        """
        This method iterates over the SourceColumns.CLIENT_PRICE columns, fetching the values for
            'asks.price' and 'bids.price' values to be inserted into the self.pre_process_df.
        The structure for SourceColumns.CLIENT_PRICE is a dictionary containing, among other keys, an
            "asks" and a "bids" key. The values for these are a list of dictionaries. Each of these dictionaries
            contains, among others, a "price" key, which is a string
        There's several "price" values for each field, we take the lowest from `asks` and the highest from `bids`.
        """
        temp_index_column = "client_price_index"
        not_null = self.source_frame.loc[:, SourceColumns.CLIENT_PRICE].notnull()

        for source, target, ascending in zip(
            ["asks", "bids"],
            [TempColumns.CLIENT_PRICE_ASKS_PRICE, TempColumns.CLIENT_PRICE_BIDS_PRICE],
            [True, False],
        ):
            # create index column to sort by later
            client_price_df = pd.DataFrame(index=self.source_frame.index)
            client_price_df[temp_index_column] = client_price_df.index.values
            # get asks/bids value
            client_price_df.loc[not_null, source] = self.source_frame.loc[
                not_null, SourceColumns.CLIENT_PRICE
            ].str.get(source)
            # explode each dictionary in asks/bids into each own column
            client_price_df = client_price_df.explode(source)
            # get price and convert to float
            client_price_df.loc[not_null, source] = (
                client_price_df.loc[not_null, source].str.get("price").astype("float")
            )
            # sort the values either ascending (asks) or descending (bids), drop duplicates while keeping the first
            # value, aka lowest for asks, highest for bids
            client_price_df = (
                client_price_df.sort_values(source, ascending=ascending)
                .drop_duplicates(temp_index_column, keep="first")
                .sort_values(temp_index_column)
            )
            self.source_frame.loc[not_null, target] = client_price_df.loc[
                not_null, source
            ]

        self.source_frame.loc[
            :,
            [TempColumns.CLIENT_PRICE_ASKS_PRICE, TempColumns.CLIENT_PRICE_BIDS_PRICE],
        ] = self.source_frame.loc[
            :,
            [TempColumns.CLIENT_PRICE_ASKS_PRICE, TempColumns.CLIENT_PRICE_BIDS_PRICE],
        ].fillna(
            pd.NA
        )

    def get_effective_fields(self):
        """
        This method iterates over the SourceColumns.EFFECTIVE columns, fetching the values from
            'time' and 'timestamp' values to be inserted into the self.source_frame.
        """
        not_null = self.source_frame.loc[:, SourceColumns.EFFECTIVE].notnull()
        self.source_frame.loc[
            not_null, TempColumns.EFFECTIVE_TIME
        ] = self.source_frame.loc[not_null, SourceColumns.EFFECTIVE].str.get("time")
        self.source_frame.loc[
            not_null, TempColumns.EFFECTIVE_TIMESTAMP
        ] = self.source_frame.loc[not_null, SourceColumns.EFFECTIVE].str.get(
            "timestamp"
        )

        self.source_frame.loc[
            :, [TempColumns.EFFECTIVE_TIME, TempColumns.EFFECTIVE_TIMESTAMP]
        ] = self.source_frame.loc[
            :, [TempColumns.EFFECTIVE_TIME, TempColumns.EFFECTIVE_TIMESTAMP]
        ].fillna(
            pd.NA
        )

    def get_price_details_fields(self):
        """
        This method iterates over the SourceColumns.PRICE_DETAILS columns, fetching the values from
            'skew.applied' and 'skew.dynamic' values to be inserted into the self.source_frame.
        """
        not_null = self.source_frame.loc[:, SourceColumns.PRICE_DETAILS].notnull()
        self.source_frame.loc[not_null, TempColumns.PRICE_DETAILS_SKEW_APPLIED] = (
            self.source_frame.loc[not_null, SourceColumns.PRICE_DETAILS]
            .str.get("skew")
            .str.get("applied")
            .astype(float)
        )
        self.source_frame.loc[not_null, TempColumns.PRICE_DETAILS_SKEW_DYNAMIC] = (
            self.source_frame.loc[not_null, SourceColumns.PRICE_DETAILS]
            .str.get("skew")
            .str.get("dynamic")
            .astype(float)
        )
        self.source_frame.loc[
            :,
            [
                TempColumns.PRICE_DETAILS_SKEW_APPLIED,
                TempColumns.PRICE_DETAILS_SKEW_DYNAMIC,
            ],
        ].fillna(pd.NA)

    def get_instrument_data(self) -> NoReturn:
        """
        This method cleans up the instrument csv from S3,
            generates some auxiliary fields and merges it with self.preprocess_df
        """
        instruments_df = self.instrument_df_cleanup(self.instrument_data)

        # get underlier symbol and currency fields
        self.get_auxiliary_fields(instruments_df)

        # merging with pre_process
        self.pre_process_df.loc[:, TempColumns.INSTRUMENT] = self.source_frame.loc[
            :, SourceColumns.INSTRUMENT
        ]
        self.pre_process_df = self.pre_process_df.merge(
            instruments_df,
            how="left",
            left_on=TempColumns.INSTRUMENT,
            right_on=InstrumentDataColumns.C_V20_ID2,
        ).fillna(pd.NA)

    @staticmethod
    def instrument_df_cleanup(instruments_df: pd.DataFrame) -> pd.DataFrame:
        """
        Performs some clean-up on the dataframe; some replacements, drop duplicates
            and fill NAs
        :param instruments_df: "raw" dataframe
        :return: ready to use dataframe
        """
        instruments_df = instruments_df.loc[:, InstrumentDataColumns.get_all_columns()]
        instruments_df.loc[:, InstrumentDataColumns.C_V20_ID2] = instruments_df.loc[
            :, InstrumentDataColumns.C_V20_ID2
        ].str.replace("/", "_")
        instruments_df = instruments_df.replace({"TBC": pd.NA, "-": pd.NA}).fillna(
            pd.NA
        )
        instruments_df = instruments_df.drop_duplicates(InstrumentDataColumns.C_V20_ID2)
        instruments_df = instruments_df.loc[
            instruments_df.loc[:, InstrumentDataColumns.C_V20_ID2].notnull(), :
        ].reset_index(drop=True)
        lot_size_zero_mask_or_null = (
            instruments_df.loc[:, InstrumentDataColumns.C_V20_LOT_SIZE] == 0
        ) | (instruments_df.loc[:, InstrumentDataColumns.C_V20_LOT_SIZE].isnull())
        instruments_df.loc[
            lot_size_zero_mask_or_null, InstrumentDataColumns.C_V20_LOT_SIZE
        ] = float(1)
        return instruments_df

    @staticmethod
    def get_auxiliary_fields(instruments_df: pd.DataFrame) -> NoReturn:
        """
        Generates the TempColumns.UNDERLIER_SYMBOL, TempColumns.QUANTITY_CCY, and TempColumns.PRICE_CCY
            from the instrument data
        :param instruments_df: cleaned-up instruments dataframe
        :return: instruments dataframe with auxiliary fields
        """
        symbol_mapping = {
            "SGP": "SG30XXXXXXXX",
            "PL": "PLATINUM",
            "GC": "GOLD",
            "BRN": "BRENTOIL",
            "WTICO_USD": "USOIL",
            "CL": "USOIL",
            "HG": "COPER",
            "TWIX_USD": "TWIX",
            "CH20_CHF": "CH20",
            "JP225Y_JPY": "JP225",
            "ZW": "WHEAT",
            "ZC": "CORN",
            "SB": "SUGAR",
            "SI": "SILVER",
            "ZS": "SOYBEANS",
            "RTY": "US2000",
        }
        instruments_df[TempColumns.UNDERLIER_SYMBOL] = pd.NA

        # commodity or index symbol
        commodity_or_index_mask = (
            instruments_df.loc[:, InstrumentDataColumns.M_ASSET] == "commodities"
        ) | (instruments_df.loc[:, InstrumentDataColumns.M_ASSET] == "indices")
        instruments_df.loc[
            commodity_or_index_mask, TempColumns.UNDERLIER_SYMBOL
        ] = instruments_df.loc[
            commodity_or_index_mask, InstrumentDataColumns.M_ID_EXCH_SYMBOL
        ]

        # crypto symbol
        crypto_mask = (
            instruments_df.loc[:, InstrumentDataColumns.M_ASSET] == "cryptocurrency"
        )
        instruments_df.loc[
            crypto_mask, TempColumns.UNDERLIER_SYMBOL
        ] = instruments_df.loc[crypto_mask, InstrumentDataColumns.C_V20_ID2]

        instruments_df.loc[:, TempColumns.UNDERLIER_SYMBOL] = (
            instruments_df.loc[:, TempColumns.UNDERLIER_SYMBOL]
            .replace(symbol_mapping)
            .str.replace("_", "")
        )

        # price and quantity currencies
        instruments_df[
            [TempColumns.QUANTITY_CCY, TempColumns.PRICE_CCY]
        ] = instruments_df.loc[:, InstrumentDataColumns.C_V20_ID2].str.split(
            "_", n=2, expand=True
        )

        # update metals
        precious_metals = ["PLATINUM", "PA", "GOLD", "SILVER"]
        precious_metals_mask = instruments_df.loc[:, TempColumns.UNDERLIER_SYMBOL].isin(
            precious_metals
        )
        instruments_df.loc[precious_metals_mask, TempColumns.UNDERLIER_SYMBOL] = (
            instruments_df[TempColumns.UNDERLIER_SYMBOL]
            + instruments_df[TempColumns.PRICE_CCY]
        )

    def get_fv_adjustment(self: pd.DataFrame) -> NoReturn:
        """
        Generates TempColumns.FV_ADJUSTMENT to be used in calculations later on.
        """
        # commodities and indices masks
        commodities_mask = self.pre_process_df.loc[
            :, InstrumentDataColumns.M_ASSET
        ].str.fullmatch("commodities", case=False, na=False)
        indices_mask = self.pre_process_df.loc[
            :, InstrumentDataColumns.M_ASSET
        ].str.fullmatch("indices", case=False, na=False)

        # assigning fv_adjustment
        self.pre_process_df.loc[
            commodities_mask, TempColumns.FV_ADJUSTMENT
        ] = self.source_frame.loc[
            commodities_mask, TempColumns.PRICE_DETAILS_SKEW_DYNAMIC
        ]
        self.pre_process_df.loc[
            indices_mask, TempColumns.FV_ADJUSTMENT
        ] = self.source_frame.loc[indices_mask, TempColumns.PRICE_DETAILS_SKEW_APPLIED]
        self.pre_process_df.loc[
            ~(commodities_mask | indices_mask), TempColumns.FV_ADJUSTMENT
        ] = 0

    def process_timestamps(self) -> NoReturn:
        """
        Converts several timestamp fields:
            SourceColumns.REQUEST_TIMESTAMP,
            SourceColumns.CREATE_TIME,
            TempColumns.EFFECTIVE_TIME,
            TempColumns.EFFECTIVE_TIMESTAMP
        Creates TempColumns.TIMESTAMP with only one of the above values in order of priority.
        Creates TempColumns.TIMESTAMP_DATE, keeping the date from the above values.
        Keeps TempColumns.EFFECTIVE_TIME as it is used on its own as mapping
        """
        datetimes_df = pd.concat(
            [
                ConvertDatetime.process(
                    source_frame=self.source_frame,
                    params=ConvertDatetimeParams(
                        source_attribute=SourceColumns.REQUEST_TIMESTAMP,
                        source_attribute_format="%Y-%m-%d %H:%M:%S.%f UTC",
                        convert_to="datetime",
                        target_attribute=TempColumns.REQUEST_TIMESTAMP,
                    ),
                ),
                ConvertDatetime.process(
                    source_frame=self.source_frame,
                    params=ConvertDatetimeParams(
                        source_attribute=SourceColumns.CREATE_TIME,
                        source_attribute_format="%Y-%m-%dT%H:%M:%S.%fZ",
                        convert_to="datetime",
                        target_attribute=TempColumns.CREATE_TIME,
                    ),
                ),
                ConvertDatetime.process(
                    source_frame=self.source_frame,
                    params=ConvertDatetimeParams(
                        source_attribute=TempColumns.EFFECTIVE_TIME,
                        source_attribute_format="%Y-%m-%dT%H:%M:%S.%fZ",
                        convert_to="datetime",
                        target_attribute=TempColumns.EFFECTIVE_TIME_CONVERTED,
                    ),
                ),
                ConvertDatetime.process(
                    source_frame=self.source_frame,
                    params=ConvertDatetimeParams(
                        source_attribute=TempColumns.EFFECTIVE_TIMESTAMP,
                        source_attribute_format="%Y-%m-%d %H:%M:%S UTC",
                        convert_to="datetime",
                        target_attribute=TempColumns.EFFECTIVE_TIMESTAMP_CONVERTED,
                    ),
                ),
            ],
            axis=1,
        )

        self.pre_process_df.loc[
            :, TempColumns.EFFECTIVE_TIME_CONVERTED
        ] = datetimes_df.loc[:, TempColumns.EFFECTIVE_TIME_CONVERTED]
        self.pre_process_df.loc[:, TempColumns.TIMESTAMP] = datetimes_df.fillna(
            method="bfill", axis=1
        ).iloc[:, 0]
        self.pre_process_df.loc[
            :, TempColumns.TIMESTAMP_DATE
        ] = ConvertDatetime.process(
            source_frame=self.pre_process_df,
            params=ConvertDatetimeParams(
                source_attribute=TempColumns.TIMESTAMP,
                source_attribute_format=DatetimeFormat.DATETIME,
                convert_to="date",
                target_attribute=TempColumns.TIMESTAMP_DATE,
            ),
        )

    def get_currencies_converted(self) -> pd.DataFrame:
        """
        Convert currency fields:
            TempColumns.PRICE_CCY,
            TempColumns.QUANTITY_CCY
        :return: Dataframe with TempColumns.PRICE_CCY_CONVERTED and TempColumns.PRICE_CCY_CONVERTED
        """
        return pd.concat(
            [
                ConvertMinorToMajor.process(
                    source_frame=self.pre_process_df,
                    params=ConvertMinorToMajorParams(
                        source_ccy_attribute=TempColumns.PRICE_CCY,
                        target_ccy_attribute=TempColumns.PRICE_CCY_CONVERTED,
                    ),
                ),
                ConvertMinorToMajor.process(
                    source_frame=self.pre_process_df,
                    params=ConvertMinorToMajorParams(
                        source_ccy_attribute=TempColumns.QUANTITY_CCY,
                        target_ccy_attribute=TempColumns.QUANTITY_CCY_CONVERTED,
                    ),
                ),
            ],
            axis=1,
        )

    def get_price_type_mapped(self) -> pd.DataFrame:
        """
        Map InstrumentDataColumns.M_PRICE_TYPE according to:
            'Monetary value': 'MONE',
            'Basis Points': 'BAPO',
            'Percentage': 'PERC',
        :return: Dataframe with TempColumns.PRICE_TYPE_MAPPED
        """
        return MapValue.process(
            source_frame=self.pre_process_df,
            params=MapValueParams(
                source_attribute=InstrumentDataColumns.M_PRICE_TYPE,
                target_attribute=TempColumns.PRICE_TYPE_MAPPED,
                case_insensitive=True,
                value_map={
                    "Monetary value": PriceNotation.MONE.value,
                    "Basis Points": PriceNotation.BAPO.value,
                    "Percentage": PriceNotation.PERC.value,
                },
            ),
            auditor=self.auditor,
        )

    def get_counterparty(self) -> pd.DataFrame:
        """
        Map TempColumns.DIVISION_NAME_OR_ID with SourceColumns.DIVISION_NAME_C or SourceColumns.DIVISION_ID
            depending on if SourceColumns.DIVISION_NAME_C si populated
        :return: Dataframe with TempColumns.DIVISION_NAME_OR_ID
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=TempColumns.DIVISION_NAME_OR_ID,
                cases=[
                    Case(
                        query=f"`{SourceColumns.DIVISION_NAME_C}`.notnull()",
                        attribute=SourceColumns.DIVISION_NAME_C,
                    ),
                    Case(
                        query=f"`{SourceColumns.DIVISION_NAME_C}`.isnull()",
                        attribute=SourceColumns.DIVISION_ID,
                    ),
                ],
            ),
        )

    def get_asset_class(self) -> pd.DataFrame:
        """
        Maps TempColumns.ASSET_CLASS with `fx cfd` or `cfd` depending on
            if InstrumentDataColumns.M_ASSET is 'currency'
        :return: Dataframe with TempColumns.ASSET_CLASS
        """
        currency_query = f"`{InstrumentDataColumns.M_ASSET}`.str.fullmatch('currency', case=False, na=False)"
        return MapConditional.process(
            source_frame=self.pre_process_df,
            params=MapConditionalParams(
                target_attribute=TempColumns.ASSET_CLASS,
                cases=[
                    Case(query=f"{currency_query}", value=AssetClass.FX_CFD),
                    Case(query=f"~({currency_query})", value=AssetClass.CFD),
                ],
            ),
        )

    def get_prices_converted(self):
        """
        Convert price and quantity values:
            TempColumns.TRADES_PRICE,
            TempColumns.CLIENTREQUESTPRICINGDATA_VWAP,
            SourceColumns.UNITS,
            TempColumns.TRADES_UNITS,
            TempColumns.CLIENT_PRICE_ASKS_PRICE,
            TempColumns.CLIENT_PRICE_BIDS_PRICE
        Some fields go through subtractions/divisions as well
        :return: Dataframe with:
            TempColumns.PRICE_CONVERTED
            TempColumns.CLIENT_PRICE_ASKS_PRICE_CONVERTED
            TempColumns.CLIENT_PRICE_BIDS_PRICE_CONVERTED
            TempColumns.CLIENTREQUESTPRICINGDATA_VWAP_CONVERTED
            TempColumns.UNITS_DIVIDED
            TempColumns.TRADES_UNITS_DIVIDED
        """
        self.pre_process_df.loc[:, TempColumns.PRICE_ADJUSTED] = (
            self.source_frame.loc[:, TempColumns.TRADES_PRICE]
            - self.pre_process_df.loc[:, TempColumns.FV_ADJUSTMENT]
        )
        self.pre_process_df.loc[
            :, TempColumns.CLIENTREQUESTPRICINGDATA_VWAP_ADJUSTED
        ] = (
            self.source_frame.loc[:, TempColumns.CLIENTREQUESTPRICINGDATA_VWAP]
            - self.pre_process_df.loc[:, TempColumns.FV_ADJUSTMENT]
        )
        self.pre_process_df.loc[:, TempColumns.UNITS_DIVIDED] = (
            self.source_frame.loc[:, SourceColumns.UNITS].abs()
            / self.pre_process_df.loc[:, InstrumentDataColumns.C_V20_LOT_SIZE]
        )
        self.pre_process_df.loc[:, TempColumns.TRADES_UNITS_DIVIDED] = (
            self.source_frame.loc[:, TempColumns.TRADES_UNITS].abs()
            / self.pre_process_df.loc[:, InstrumentDataColumns.C_V20_LOT_SIZE]
        )

        return pd.concat(
            [
                ConvertMinorToMajor.process(
                    source_frame=self.pre_process_df,
                    params=ConvertMinorToMajorParams(
                        source_price_attribute=TempColumns.PRICE_ADJUSTED,
                        source_ccy_attribute=TempColumns.PRICE_CCY,
                        target_price_attribute=TempColumns.PRICE_CONVERTED,
                    ),
                ),
                ConvertMinorToMajor.process(
                    source_frame=pd.concat(
                        [
                            self.source_frame.loc[
                                :, TempColumns.CLIENT_PRICE_ASKS_PRICE
                            ],
                            self.pre_process_df.loc[:, TempColumns.PRICE_CCY],
                        ],
                        axis=1,
                    ),
                    params=ConvertMinorToMajorParams(
                        source_price_attribute=TempColumns.CLIENT_PRICE_ASKS_PRICE,
                        source_ccy_attribute=TempColumns.PRICE_CCY,
                        target_price_attribute=TempColumns.CLIENT_PRICE_ASKS_PRICE_CONVERTED,
                    ),
                ),
                ConvertMinorToMajor.process(
                    source_frame=pd.concat(
                        [
                            self.source_frame.loc[
                                :, TempColumns.CLIENT_PRICE_BIDS_PRICE
                            ],
                            self.pre_process_df.loc[:, TempColumns.PRICE_CCY],
                        ],
                        axis=1,
                    ),
                    params=ConvertMinorToMajorParams(
                        source_price_attribute=TempColumns.CLIENT_PRICE_BIDS_PRICE,
                        source_ccy_attribute=TempColumns.PRICE_CCY,
                        target_price_attribute=TempColumns.CLIENT_PRICE_BIDS_PRICE_CONVERTED,
                    ),
                ),
                ConvertMinorToMajor.process(
                    source_frame=self.pre_process_df,
                    params=ConvertMinorToMajorParams(
                        source_price_attribute=TempColumns.CLIENTREQUESTPRICINGDATA_VWAP_ADJUSTED,
                        source_ccy_attribute=TempColumns.QUANTITY_CCY,
                        target_price_attribute=TempColumns.CLIENTREQUESTPRICINGDATA_VWAP_CONVERTED,
                    ),
                ),
            ],
            axis=1,
        )

    def get_instrument_fallback_fields(self) -> pd.DataFrame:
        """
        Generates the instrument unique identifier and alternative instrument identifier
            to be used downstream in instrument fallback
        :return: dataframe with multiple instrument fallback columns
        """

        return pd.concat(
            [
                pd.DataFrame(
                    data=DeliveryType.CASH.value,
                    columns=[TempColumns.DERIVATIVE_DELIVERY_TYPE],
                    index=self.source_frame.index,
                ),
                pd.DataFrame(
                    data=InstrumentIdCodeType.OTHR.value,
                    columns=[TempColumns.EXT_INSTRUMENT_ID_CODE_TYPE],
                    index=self.source_frame.index,
                ),
                MapValue.process(
                    source_frame=self.pre_process_df,
                    params=MapValueParams(
                        source_attribute=InstrumentDataColumns.M_PRICE_TYPE,
                        target_attribute=TempColumns.EXT_PRICE_NOTATION,
                        case_insensitive=True,
                        value_map={
                            "Monetary value": PriceNotation.MONE.value,
                            "Yield": PriceNotation.YIEL.value,
                            "Basis Points": PriceNotation.YIEL.value,
                            "Percentage": PriceNotation.PERC.value,
                        },
                    ),
                    auditor=self.auditor,
                ),
                pd.DataFrame(
                    data=self.pre_process_df.loc[
                        :, InstrumentDataColumns.C_V20_LOT_SIZE
                    ].values,
                    columns=[TempColumns.DERIVATIVE_PRICE_MULTIPLIER],
                    index=self.pre_process_df.index,
                ),
                pd.DataFrame(
                    data=self.pre_process_df.loc[
                        :, InstrumentDataColumns.T_ISIN
                    ].values,
                    columns=[TempColumns.EXT_UL_INSTRUMENTS_ISIN],
                    index=self.pre_process_df.index,
                ),
                pd.DataFrame(
                    data=self.pre_process_df.loc[
                        :, InstrumentDataColumns.M_NAME
                    ].values,
                    columns=[TempColumns.EXT_EXCHANGE_SYMBOL],
                    index=self.pre_process_df.index,
                ),
                MapConditional.process(
                    source_frame=self.pre_process_df,
                    params=MapConditionalParams(
                        target_attribute=TempColumns.DERIVATIVE_UNERLYING_INDEX_NAME,
                        cases=[
                            Case(
                                query=f"`{InstrumentDataColumns.C_CFI}`.str.match('JEI*', case=False, na=False)",
                                attribute=InstrumentDataColumns.C_NAME,
                            )
                        ],
                    ),
                ),
                ConcatAttributes.process(
                    source_frame=self.pre_process_df,
                    params=ConcatAttributesParams(
                        delimiter=" ",
                        source_attributes=[
                            InstrumentDataColumns.C_NAME,
                            InstrumentDataColumns.C_PRODUCT,
                        ],
                        target_attribute=TempColumns.INSTRUMENT_FULL_NAME,
                    ),
                ),
                self._get_instrument_unique_identifier_(),
            ],
            axis=1,
        )

    def _get_instrument_unique_identifier_(self) -> pd.DataFrame:
        ticker_expiry_date_df = pd.concat(
            [
                ConcatAttributes.process(
                    source_frame=self.pre_process_df,
                    params=ConcatAttributesParams(
                        delimiter="|",
                        source_attributes=[
                            InstrumentDataColumns.M_BBG_TICKER,
                            InstrumentDataColumns.C_UNDERLYING_EXPIRY_DATE,
                        ],
                        target_attribute=TempColumns.TICKER_EXPIRY_DATE,
                    ),
                ),
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, InstrumentDataColumns.C_NAME]
                    .str.replace("\W", "")  # noqa W605
                    .str.upper()
                    .values,
                    columns=[TempColumns.C_NAME_UPPER],
                    index=self.pre_process_df.index,
                ),
            ],
            axis=1,
        )
        temp_df = pd.concat(
            [
                MapConditional.process(
                    source_frame=pd.concat(
                        [self.pre_process_df, ticker_expiry_date_df], axis=1
                    ),
                    params=MapConditionalParams(
                        target_attribute=TempColumns.COMMODITIES_RATES_TICKER,
                        cases=[
                            Case(
                                query="index == index",
                                attribute=TempColumns.C_NAME_UPPER,
                            ),
                            Case(
                                query=f"`{InstrumentDataColumns.M_ASSET}`.str.fullmatch('commodities|rates', case=False, na=False) & (`{InstrumentDataColumns.T_TICKER}`.str.len()>0)",
                                attribute=InstrumentDataColumns.T_TICKER,
                            ),
                            Case(
                                query=f"`{InstrumentDataColumns.M_ASSET}`.str.fullmatch('indices', case=False, na=False) & (`{InstrumentDataColumns.M_GENERIC_BBG}`.str.len()>0)",
                                attribute=TempColumns.TICKER_EXPIRY_DATE,
                            ),
                            Case(
                                query=f"`{InstrumentDataColumns.M_ASSET}`.str.fullmatch('equity_shares', case=False, na=False) & (`{InstrumentDataColumns.T_ISIN}`.str.len()>0)",
                                attribute=InstrumentDataColumns.T_ISIN,
                            ),
                        ],
                    ),
                ),
                MapConditional.process(
                    source_frame=self.pre_process_df,
                    params=MapConditionalParams(
                        target_attribute=TempColumns.CFD_OR_SB,
                        cases=[
                            Case(
                                query=f"`{InstrumentDataColumns.C_PRODUCT}`.str.fullmatch('cfd', case=False, na=False)",
                                value="CFD",
                            ),
                            Case(
                                query=f"`{InstrumentDataColumns.C_PRODUCT}`.str.fullmatch('spreadbet', case=False, na=False)",
                                value="SB",
                            ),
                        ],
                    ),
                ),
                pd.DataFrame(
                    data="Oanda",
                    index=self.source_frame.index,
                    columns=[TempColumns.OANDA],
                ),
                pd.DataFrame(
                    data="V20", index=self.source_frame.index, columns=[TempColumns.V20]
                ),
            ],
            axis=1,
        )

        return ConcatAttributes.process(
            source_frame=pd.concat([self.pre_process_df, temp_df], axis=1),
            params=ConcatAttributesParams(
                delimiter="|",
                source_attributes=[
                    TempColumns.OANDA,
                    InstrumentDataColumns.C_ID,
                    TempColumns.V20,
                    TempColumns.COMMODITIES_RATES_TICKER,
                    TempColumns.CFD_OR_SB,
                ],
                target_attribute=TempColumns.EXT_INSTRUMENT_UNIQUE_IDENTIFIER,
            ),
        )

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""
