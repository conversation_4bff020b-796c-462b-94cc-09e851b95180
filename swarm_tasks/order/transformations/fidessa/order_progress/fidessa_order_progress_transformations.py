import os

import pandas as pd
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import HierarchyEnum
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_elastic_schema.static.mifid2 import <PERSON>id<PERSON><PERSON>eri<PERSON>
from se_elastic_schema.static.reference import CommissionAmountType
from se_elastic_schema.static.reference import OrderRecordType
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.generic.string.extract_part_from_delimited_text import (
    ExtractPartFromDelimitedText,
)
from swarm_tasks.generic.string.extract_part_from_delimited_text import (
    Params as ParamsExtractPartFromDelimitedText,
)
from swarm_tasks.io.read.aws.df_from_s3_csv import DfFromS3Csv
from swarm_tasks.io.read.aws.df_from_s3_csv import Params as ParamsDfFromS3Csv
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ConvertDatetimeParams,
)
from swarm_tasks.transform.datetime.join_date_and_time import JoinDateAndTimeFormat
from swarm_tasks.transform.datetime.join_date_and_time import (
    Params as ParamsJoinDateAndTimeFormat,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_from_mapping_table import MapFromMappingTable
from swarm_tasks.transform.map.map_from_mapping_table import (
    Params as ParamsMapFromMappingTable,
)
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)

DEFAULT_TIMEZONE = "Europe/London"


class MappingTableColumns:
    CODE = "Code"
    MIC = "MIC"


class TempColumns:
    CONCAT_COUNTERPARTY_DESC_AND_CODE = "__concat_counterparty_desc_and_code__"
    CONCAT_COUNTERPARTY_DESC_AND_CODE_WITH_ID = (
        "__concat_counterparty_desc_and_code_with_id__"
    )
    COUNTERPARTY_CODE_IN_PARENTHESES = "__counterparty_code_in_parentheses__"

    EXEC_DETAILS_VALIDITY_PERIOD = "__exec_details_validity_period__"
    EXECUTING_BOOK_ID_WITH_PREFIX = "__executing_book_id_with_prefix__"
    LOOKED_UP_VENUE = "__looked_up_venue__"
    MIC_FROM_MARKET_ID = "__mic_from_market_id__"
    ORDER_NOTES_WITH_PREFIX = "__order_notes__with_prefix__"
    TS_VALIDITY_PERIOD = "__ts_validity_period__"
    RECORD_TYPE_WITH_PREFIX = "__record_type_with_prefix__"

    # Parties columns with prefix
    COUNTERPARTY_WITH_PREFIX = "__counterparty_with_prefix__"
    ENTERED_BY_WITH_PREFIX = "__entered_by_with_prefix__"
    EXECUTING_ENTITY_WITH_PREFIX = "__executing_entity_with_prefix__"
    EXECUTOR_WITH_PREFIX = "__executor_with_prefix__"
    INVESTMENT_DECISION_VALUE_WITH_PREFIX = "__investment_decision_value_with_prefix__"

    # Columns to be dropped downstream in the bundle
    COUNTERPARTY_WITHOUT_PREFIX = "__counterparty_without_prefix__"
    FALLBACK_ID_CODE = "__fallback_id_code__"
    FALLBACK_INSTRUMENT_FULL_NAME = "__fallback_instrument_full_name__"
    IS_CREATED_THROUGH_FALLBACK = "__is_created_through_fallback__"


class SourceColumns:
    AMENDED_DATE = "AMENDED_DATE"
    AMENDED_TIME = "AMENDED_TIME"
    BUY_SELL = "BUY_SELL"
    CFI_CODE = "CFI_CODE"
    COMMISSION_TYPE = "COMMISSION_TYPE"
    COMMISSION_VALUE = "COMMISSION_VALUE"
    COMPOSITE_ORDER_ID = "COMPOSITE_ORDER_ID"
    COUNTERPARTY_CODE = "COUNTERPARTY_CODE"
    COUNTERPARTY_DESCRIPTION = "COUNTERPARTY_DESCRIPTION"
    DEALING_CAPACITY = "DEALING_CAPACITY"
    DEALT_CCY = "DEALT_CCY"
    ENTERED_BY = "ENTERED_BY"
    ENTERED_BY_GROUP = "ENTERED_BY_GROUP"
    ENTERED_DATE = "ENTERED_DATE"
    ENTERED_TIME = "ENTERED_TIME"
    EPIC_CODE = "EPIC_CODE"
    EXCHANGE_CONTRACT_CODE = "EXCHANGE_CONTRACT_CODE"
    EXECUTING_BOOK_ID = "EXECUTING_BOOK_ID"
    EXECUTING_ENTITY = "EXECUTING_ENTITY"
    EXECUTOR = "EXECUTOR"
    EXPIRY_DATE = "EXPIRY_DATE"
    EXPIRY_TIME = "EXPIRY_TIME"
    EXPIRY_TYPE = "EXPIRY_TYPE"
    GLOBAL_ORDER_ID = "GLOBAL_ORDER_ID"
    GROSS_FILL_PRICE = "GROSS_FILL_PRICE"
    HOUSE_BOOK_ID = "HOUSE_BOOK_ID"
    INSTRUMENT_CODE = "INSTRUMENT_CODE"
    INSTRUMENT_DESCRIPTION = "INSTRUMENT_DESCRIPTION"
    INVESTMENT_DECISION_VALUE = "INVESTMENT_DECISION_VALUE"
    ISIN_CODE = "ISIN_CODE"
    LIMIT_PRICE = "LIMIT_PRICE"
    MARKET_ID = "MARKET_ID"
    OPTION_TYPE = "OPTION_TYPE"
    ORDER_ID = "ORDER_ID"
    ORDER_NOTES = "ORDER_NOTES"
    ORDER_PRICE_TYPE = "ORDER_PRICE_TYPE"
    ORIGINATOR_ORDER_ID = "ORIGINATOR_ORDER_ID"
    PARENT_ORDER_ID = "PARENT_ORDER_ID"
    QUANTITY_AVAILABLE = "QUANTITY_AVAILABLE"
    RECEIVED_DATE = "RECEIVED_DATE"
    RECEIVED_TIME = "RECEIVED_TIME"
    ROUTED_ORDER_CODE = "ROUTED_ORDER_CODE"
    SETTLEMENT_CURRENCY = "SETTLEMENT_CCY"
    STRIKE_PRICE = "STRIKE_PRICE"
    TOP_LEVEL = "TOP_LEVEL"
    TOTAL_QUANTITY = "TOTAL_QUANTITY"
    TRADING_VENUE_TRANSACTION_ID = "TRADING_VENUE_TRANSACTION_ID"
    UNDERLYING_ISIN = "UNDERLYING_ISIN"
    VERSION = "VERSION"
    VERSION_NUMBER = "VERSION_NUMBER"


class FidessaOrderProgressTransformations(AbstractOrderTransformations):
    """Transformations class for Fidessa Order Progress. Only NEWO records are ingested
    using this flow, so no prefixes are used.
    """

    def __init__(self, source_frame: pd.DataFrame, auditor, **kwargs):
        super().__init__(source_frame=source_frame, auditor=auditor)

    def _pre_process(self):
        """This pre-processing method is used to populate columns in pre_process_df. The only
        column populated is __counterparty_without_prefix__"""
        self.pre_process_df = pd.concat(
            [self.pre_process_df, self._get_counterparty()], axis=1
        )

    def process(self):
        """All the schema target columns which need to be populated are populated in
        self.target_df by the public methods called by process(). 3 temp columns are
        populated here as well as these are present in all Order flows: __meta_model__,
        marketIdentifiers.parties and marketIdentifiers.instrument.
        """
        self.pre_process()
        self.buy_sell()
        self.data_source_name()
        self.execution_details_buy_sell_indicator()
        self.execution_details_limit_price()
        self.execution_details_order_status()
        self.execution_details_order_type()
        self.transaction_details_record_type()
        self.execution_details_outgoing_order_addl_info()
        self.execution_details_routing_strategy()
        self.execution_details_trading_capacity()
        self.execution_details_validity_period()
        self.hierarchy()
        self.id()
        self.meta_model()
        self.order_identifiers_aggregated_order_id_code()
        self.order_identifiers_initial_order_designation()
        self.order_identifiers_internal_order_id_code()
        self.order_identifiers_order_id_code()
        self.order_identifiers_order_routing_code()
        self.order_identifiers_sequence_number()
        self.order_identifiers_trading_venue_transaction_id_code()
        self.price_forming_data_initial_quantity()
        self.price_forming_data_remaining_quantity()
        self.source_index()
        self.source_key()
        self.timestamps_order_received()
        self.date()
        self.timestamps_order_submitted()
        self.timestamps_order_status_updated()
        self.timestamps_validity_period()
        self.transaction_details_basket_id()
        self.transaction_details_buy_sell_indicator()
        self.transaction_details_commission_amount()
        self.transaction_details_commission_amount_currency()
        self.transaction_details_commission_amount_type()
        self.transaction_details_price_average()
        self.transaction_details_price_currency()
        self.transaction_details_price_notation()
        self.transaction_details_quantity_notation()
        self.transaction_details_trading_capacity()
        self.transaction_details_ultimate_venue()
        self.transaction_details_venue()
        self.transaction_details_complex_trade_component_id()
        self.market_identifiers_instrument()
        self.market_identifiers_parties()
        self.market_identifiers()
        self.post_process()
        return self.target_df

    def _post_process(self):
        """All the temp columns which are needed for downstream tasks are populated
        in self.target_df inside the _post_process() method. The temp
        columns included here are __counterparty_without_prefix__,
        """
        self.target_df.loc[
            :, TempColumns.COUNTERPARTY_WITHOUT_PREFIX
        ] = self.pre_process_df.loc[:, TempColumns.COUNTERPARTY_WITHOUT_PREFIX]

        self.target_df = pd.concat(
            [
                self.target_df,
                pd.DataFrame(
                    data="ID",
                    index=self.source_frame.index,
                    columns=[TempColumns.FALLBACK_ID_CODE],
                ),
                pd.DataFrame(
                    data=True,
                    index=self.source_frame.index,
                    columns=[TempColumns.IS_CREATED_THROUGH_FALLBACK],
                ),
                ConcatAttributes.process(
                    source_frame=self.source_frame,
                    params=ParamsConcatAttributes(
                        target_attribute=TempColumns.FALLBACK_INSTRUMENT_FULL_NAME,
                        source_attributes=[
                            SourceColumns.INSTRUMENT_CODE,
                            SourceColumns.INSTRUMENT_DESCRIPTION,
                        ],
                        delimiter=" ",
                    ),
                ),
            ],
            axis=1,
        )

    def _buy_sell(self) -> pd.DataFrame:
        """Returns a data frame containing buySell.
        This is populated from SourceColumns.BUY_SELL"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.BUY_SELL,
                target_attribute=OrderColumns.BUY_SELL,
                case_insensitive=True,
                value_map={"b": "1", "s": "2"},
            ),
            auditor=self.auditor,
        )

    def _data_source_name(self) -> pd.DataFrame:
        """Returns a data frame containing dataSourceName.
        This is populated with the static value 'Fidessa Front Office Trades'"""
        return pd.DataFrame(
            data="Fidessa Front Office Trades",
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """Returns a data frame containing date.
        This is populated from SourceColumns.TRADE_EXECUTION_TIME for Fx Trade files
        and SourceColumns.OPTION_EXECUTION_TIME for Fx Option files"""
        return ConvertDatetime.process(
            self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.RECEIVED_DATE,
                source_attribute_format="%Y%m%d",
                target_attribute=OrderColumns.DATE,
                timezone_info=DEFAULT_TIMEZONE,
                convert_to="date",
            ),
        )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        pass

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.buySellIndicator.
        This is populated from SourceColumns.BUY_SELL"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.BUY_SELL,
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "b": BuySellIndicator.BUYI.value,
                    "s": BuySellIndicator.SELL.value,
                },
            ),
            auditor=self.auditor,
        )

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        pass

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """Returns a dataframe with executionDetails.limitPrice populated.
        Maps SourceColumns.LIMIT_PRICE directly to executionDetails.limitPrice.
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.LIMIT_PRICE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE,
            ),
            auditor=self.auditor,
        )

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        pass

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        pass

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        pass

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        pass

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        pass

    def _execution_details_order_status(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.orderStatus
        As this flow only creates NEWOs, this is populated with the static value 'NEWO'"""
        return pd.DataFrame(
            data=OrderStatus.NEWO.value,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_ORDER_STATUS],
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.orderType.
        This is populated with 'LIMIT', 'MARKET' or 'STOP' based on the value in
        SourceColumns.ORDER_PRICE_TYPE"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.ORDER_PRICE_TYPE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
                case_insensitive=True,
                default_value="MARKET",
                value_map={"lim": "LIMIT", "stp": "STOP"},
            ),
            auditor=self.auditor,
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.outgoingOrderAddlInfo.
        This is populated from SourceColumns.ORDER_NOTES"""
        intermediate_frame = pd.concat(
            [
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=SourceColumns.ORDER_NOTES,
                        target_attribute=TempColumns.ORDER_NOTES_WITH_PREFIX,
                        prefix="Narrative: ",
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=SourceColumns.ENTERED_BY_GROUP,
                        target_attribute=TempColumns.ENTERED_BY_WITH_PREFIX,
                        prefix="Group: ",
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.target_df,
                    params=ParamsMapAttribute(
                        source_attribute=OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE,
                        target_attribute=TempColumns.RECORD_TYPE_WITH_PREFIX,
                        prefix="Record Type: ",
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

        return ConcatAttributes.process(
            source_frame=intermediate_frame,
            params=ParamsConcatAttributes(
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                source_attributes=[
                    TempColumns.ORDER_NOTES_WITH_PREFIX,
                    TempColumns.ENTERED_BY_WITH_PREFIX,
                    TempColumns.RECORD_TYPE_WITH_PREFIX,
                ],
                delimiter=",\n",
            ),
        )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.orderRoutingCode.
        This is populated from SourceColumns.ROUTED_ORDER_CODE"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ENTERED_BY_GROUP,
                target_attribute=OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY,
            ),
            auditor=self.auditor,
        )

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        pass

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_stop_price(self) -> pd.DataFrame:
        pass

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.tradingCapacity.
        This is populated from SourceColumns.DEALING_CAPACITY"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.DEALING_CAPACITY,
                target_attribute=OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY,
                case_insensitive=True,
                value_map={
                    "A": TradingCapacity.AOTC.value,
                    "P": TradingCapacity.DEAL.value,
                    "R": TradingCapacity.DEAL.value,
                },
            ),
            auditor=self.auditor,
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.validityPeriod.
        This is populated from SourceColumns.EXPIRY_TYPE"""
        intermediate_frame = MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.EXPIRY_TYPE,
                target_attribute=TempColumns.EXEC_DETAILS_VALIDITY_PERIOD,
                case_insensitive=True,
                value_map={
                    "GTD": ValidityPeriod.GTDV.value,
                    "GFD": ValidityPeriod.DAVY.value,
                    "IOC": ValidityPeriod.IOCV.value,
                    "FOK": ValidityPeriod.FOKV.value,
                    "GTC": ValidityPeriod.GTCV.value,
                    "GTT": ValidityPeriod.GTTV.value,
                    "GTS": ValidityPeriod.GTSV.value,
                    "GTX": ValidityPeriod.GTXV.value,
                    "GAT": ValidityPeriod.GATV.value,
                    "GAD": ValidityPeriod.GADV.value,
                    "GAS": ValidityPeriod.GASV.value,
                },
            ),
            auditor=self.auditor,
        )

        return MapAttribute.process(
            source_frame=intermediate_frame,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.EXEC_DETAILS_VALIDITY_PERIOD,
                target_attribute=OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD,
                cast_to="string.list",
                list_delimiter=";",
            ),
            auditor=self.auditor,
        )

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _financing_type(self) -> pd.DataFrame:
        pass

    def _hierarchy(self) -> pd.DataFrame:
        """Populates hierarchy based with the static value 'Parent'"""
        return pd.DataFrame(
            data=HierarchyEnum.PARENT.value,
            index=self.source_frame.index,
            columns=[OrderColumns.HIERARCHY],
        )

    def _id(self) -> pd.DataFrame:
        """Returns a data frame containing id.
        This is populated from SourceColumns.ORDER_ID"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ORDER_ID,
                target_attribute=OrderColumns.ID,
            ),
            auditor=self.auditor,
        )

    def _is_discretionary(self) -> pd.DataFrame:
        pass

    def _is_iceberg(self):
        pass

    def _is_repo(self):
        pass

    def _is_synthetic(self):
        pass

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        pass

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        pass

    def _jurisdiction_country(self) -> pd.DataFrame:
        pass

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        pass

    def _market_identifiers(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_instrument() and _market_identifiers_parties() have been
        called earlier"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.instrument by calling InstrumentIdentifiers.
        Assumes that _transaction_details_price_currency() and _transaction_details_ultimate_venue() have been
        called earlier"""
        instrument_source_frame = pd.concat(
            [
                self.source_frame,
                self.target_df.loc[
                    :,
                    [
                        OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                        OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                    ],
                ],
            ],
            axis=1,
        )
        return InstrumentIdentifiers.process(
            source_frame=instrument_source_frame,
            params=ParamsInstrumentIdentifiers(
                bbg_figi_id_attribute=SourceColumns.EPIC_CODE,
                currency_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                venue_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                isin_attribute=SourceColumns.ISIN_CODE,
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                retain_task_inputs=True,
            ),
            auditor=self.auditor,
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.parties by calling PartyIdentifiers
        Assumes that execution_details_buy_sell_indicator() has been called earlier, as well as
        pre_process()"""
        parties_source_frame = pd.concat(
            [
                self._party_columns_with_id_prefix(),
                self.target_df.loc[
                    :,
                    [OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR],
                ],
            ],
            axis=1,
        )
        return GenericOrderPartyIdentifiers.process(
            source_frame=parties_source_frame,
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                client_identifier=TempColumns.COUNTERPARTY_WITH_PREFIX,
                counterparty_identifier=TempColumns.COUNTERPARTY_WITH_PREFIX,
                executing_entity_identifier=TempColumns.EXECUTING_ENTITY_WITH_PREFIX,
                buyer_identifier=TempColumns.EXECUTING_ENTITY_WITH_PREFIX,
                seller_identifier=TempColumns.COUNTERPARTY_WITH_PREFIX,
                trader_identifier=TempColumns.ENTERED_BY_WITH_PREFIX,
                execution_within_firm_identifier=TempColumns.EXECUTOR_WITH_PREFIX,
                investment_decision_within_firm_identifier=TempColumns.INVESTMENT_DECISION_VALUE_WITH_PREFIX,
                buy_sell_side_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                use_buy_mask_for_buyer_seller=True,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        return pd.DataFrame(
            data="Order",
            index=self.source_frame.index,
            columns=[OrderColumns.META_MODEL],
        )

    def _order_class(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.aggregatedOrderId.
        This is populated from SourceColumns.COMPOSITE_ORDER_ID"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.COMPOSITE_ORDER_ID,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID,
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.initialOrderDesignation.
        This is populated from SourceColumns.ORIGINATOR_ORDER_ID"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ORIGINATOR_ORDER_ID,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_INITIAL_ORDER_DESIGNATION,
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.internalOrderIdCode.
        This is populated from SourceColumns.GLOBAL_ORDER_ID"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.GLOBAL_ORDER_ID,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE,
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.orderIdCode.
        This is populated from SourceColumns.ORDER_ID"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ORDER_ID,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.orderRoutingCode.
        This is populated from SourceColumns.ROUTED_ORDER_CODE"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ROUTED_ORDER_CODE,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ROUTING_CODE,
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        pass

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.sequenceNumber.
        This is populated from SourceColumns.VERSION"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.VERSION,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_SEQUENCE_NUMBER,
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.tradingVenueTransactionIdCode.
        This is populated from SourceColumns.VERSION"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.TRADING_VENUE_TRANSACTION_ID,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_TRADING_VENUE_TRANSACTION_ID_CODE,
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        pass

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing priceFormingData.initialQuantity.
        This is populated from SourceColumns.TOTAL_QUANTITY"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.TOTAL_QUANTITY,
                target_attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                cast_to="numeric.absolute",
            ),
            auditor=self.auditor,
        )

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        pass

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing priceFormingData.remainingQuantity.
        This is populated from SourceColumns.QUANTITY_AVAILABLE"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.QUANTITY_AVAILABLE,
                target_attribute=OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
                cast_to="numeric.absolute",
            ),
            auditor=self.auditor,
        )

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        pass

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        pass

    def _source_index(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceIndex column"""
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_INDEX],
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceKey column from the SWARM_FILE_URL"""
        return pd.DataFrame(
            data=os.getenv("SWARM_FILE_URL"),
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_KEY],
        )

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_order_received(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderReceived.
        This is populated from SourceColumns.RECEIVED_DATE and SourceColumns.RECEIVED_TIME"""
        return JoinDateAndTimeFormat.process(
            source_frame=self.source_frame,
            params=ParamsJoinDateAndTimeFormat(
                source_date_attribute=SourceColumns.RECEIVED_DATE,
                source_time_attribute=SourceColumns.RECEIVED_TIME,
                source_format="%Y%m%d%H:%M:%S",
                target_format="%Y-%m-%dT%H:%M:%S.%fZ",
                timezone_info=DEFAULT_TIMEZONE,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
            ),
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderStatusUpdated.
        This is populated from SourceColumns.AMENDED_DATE and SourceColumns.AMENDED_TIME"""
        return JoinDateAndTimeFormat.process(
            source_frame=self.source_frame,
            params=ParamsJoinDateAndTimeFormat(
                source_date_attribute=SourceColumns.AMENDED_DATE,
                source_time_attribute=SourceColumns.AMENDED_TIME,
                source_format="%Y%m%d%H:%M:%S",
                target_format="%Y-%m-%dT%H:%M:%S.%fZ",
                timezone_info=DEFAULT_TIMEZONE,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
            ),
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderSubmitted.
        This is populated from SourceColumns.ENTERED_DATE and SourceColumns.ENTERED_TIME"""
        return JoinDateAndTimeFormat.process(
            source_frame=self.source_frame,
            params=ParamsJoinDateAndTimeFormat(
                source_date_attribute=SourceColumns.ENTERED_DATE,
                source_time_attribute=SourceColumns.ENTERED_TIME,
                source_format="%Y%m%d%H:%M:%S",
                target_format="%Y-%m-%dT%H:%M:%S.%fZ",
                timezone_info=DEFAULT_TIMEZONE,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
            ),
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        pass

    def _timestamps_validity_period(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.validityPeriod.
        This is populated from SourceColumns.EXPIRY_DATE and SourceColumns.EXPIRY_TIME"""
        intermediate_frame = JoinDateAndTimeFormat.process(
            source_frame=self.source_frame,
            params=ParamsJoinDateAndTimeFormat(
                source_date_attribute=SourceColumns.EXPIRY_DATE,
                source_time_attribute=SourceColumns.EXPIRY_TIME,
                source_format="%Y%m%d%H:%M:%S",
                target_format="%Y-%m-%dT%H:%M:%S.%fZ",
                timezone_info=DEFAULT_TIMEZONE,
                target_attribute=TempColumns.TS_VALIDITY_PERIOD,
            ),
        )

        return MapAttribute.process(
            source_frame=intermediate_frame,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.TS_VALIDITY_PERIOD,
                target_attribute=OrderColumns.TIMESTAMPS_VALIDITY_PERIOD,
                cast_to="string.list",
                list_delimiter=";",
            ),
            auditor=self.auditor,
        )

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.basketId.
        This is populated from SourceColumns.EXECUTING_BOOK_ID"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.EXECUTING_BOOK_ID,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_BASKET_ID,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        pass

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.buySellIndicator.
        It assumes that execution_details_buy_sell_indicator() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.commissionAmount.
        This is populated from SourceColumns.COMMISSION_VALUE"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_price_attribute=SourceColumns.COMMISSION_VALUE,
                source_ccy_attribute=SourceColumns.SETTLEMENT_CURRENCY,
                target_price_attribute=OrderColumns.TRANSACTION_DETAILS_COMMISSION_AMOUNT,
                cast_to="abs",
            ),
        )

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.commissionAmountCurrency.
        This is populated from SourceColumns.SETTLEMENT_CURRENCY"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.SETTLEMENT_CURRENCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_COMMISSION_AMOUNT_CURRENCY,
            ),
        )

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.commissionAmountType.
        This is populated from SourceColumns.COMMISSION_TYPE"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.COMMISSION_TYPE,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_COMMISSION_AMOUNT_TYPE,
                case_insensitive=True,
                value_map={
                    "PERCENTAGE": CommissionAmountType.PERCENT.value,
                    "BASIS_POINT": CommissionAmountType.BASIS_POINTS.value,
                    "FIXED_AMOUNT": CommissionAmountType.ABSOLUTE.value,
                    "SLIDING_SCALE": CommissionAmountType.AMOUNT_PER_CONTRACT.value,
                    "PER_UNIT": CommissionAmountType.AMOUNT_PER_UNIT.value,
                },
            ),
            auditor=self.auditor,
        )

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """
        :return: Populates TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.INSTRUMENT_CODE,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        pass

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        pass

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        pass

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_price(self) -> pd.DataFrame:
        pass

    def _transaction_details_price_average(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.priceAverage.
        This is populated from SourceColumns.GROSS_FILL_PRICE"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_price_attribute=SourceColumns.GROSS_FILL_PRICE,
                source_ccy_attribute=SourceColumns.DEALT_CCY,
                target_price_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_AVERAGE,
                cast_to="abs",
            ),
        )

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.priceCurrency.
        This is populated from SourceColumns.DEALT_CCY"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.DEALT_CCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
            ),
        )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.priceNotation.
        This is populated with the static value 'MONE'"""
        return pd.DataFrame(
            data=PriceNotation.MONE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION],
        )

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        pass

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        pass

    def _transaction_details_quantity(self) -> pd.DataFrame:
        pass

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.quantityNotation.
        This is populated with the static value 'UNIT'"""
        return pd.DataFrame(
            data=QuantityNotation.UNIT.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION],
        )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.recordType.
        This is populated with the static value 'Client Side' if not null.
        It is populated with the static value 'Allocation' otherwise"""
        counterparty_code_not_null = f"`{SourceColumns.COUNTERPARTY_CODE}`.notnull()"
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE,
                cases=[
                    {
                        "query": f"~{counterparty_code_not_null}",
                        "value": OrderRecordType.ALLOCATION.value,
                    },
                    {
                        "query": counterparty_code_not_null,
                        "value": OrderRecordType.CLIENT_SIDE.value,
                    },
                ],
            ),
        )

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        pass

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        pass

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.tradingCapacity.
        It assumes that execution_details_trading_capacity() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        pass

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """Populates the transactionDetails.ultimateVenue column as per the following logic
        Use the first 3 letters of SourceColumns.MARKET_ID (MapAttribute) to match against
        the mapping table MicCodeMapping (MapFromMapping table task). If there is no match,
        check SourceColumns.InstrumentCode and if it ends with .L, value=XLON, else XOFF.
        """
        # Get mapping table from S3
        mic_mapping_table = self._get_mic_mapping_table_from_s3()

        # Do a lookup of the first part of Market_Id on the mic_mapping_table to get the MIC
        intermediate_df = ExtractPartFromDelimitedText.process(
            source_frame=self.source_frame,
            params=ParamsExtractPartFromDelimitedText(
                source_attribute=SourceColumns.MARKET_ID,
                target_attribute=TempColumns.MIC_FROM_MARKET_ID,
                delimiter="-",
                index_of_part_to_extract=0,
                null_if_no_delimiter=False,
            ),
        )

        df_after_lookup = MapFromMappingTable.process(
            source_frame=intermediate_df,
            mapping_table=mic_mapping_table,
            params=ParamsMapFromMappingTable(
                source_attribute=TempColumns.MIC_FROM_MARKET_ID,
                target_attribute=TempColumns.LOOKED_UP_VENUE,
                matching_column=MappingTableColumns.CODE,
                output_column=MappingTableColumns.MIC,
            ),
        )

        looked_up_venue_not_null = f"{TempColumns.LOOKED_UP_VENUE}.notnull()"
        pattern = r".*\.L$"
        ends_with = f"`{SourceColumns.INSTRUMENT_CODE}`.str.match(r'{pattern}', case=False, na=False)"
        cases = [
            {
                "query": looked_up_venue_not_null,
                "attribute": TempColumns.LOOKED_UP_VENUE,
            },
            {"query": f"~{looked_up_venue_not_null} & {ends_with}", "value": "XLON"},
            {"query": f"~({looked_up_venue_not_null} | {ends_with})", "value": "XOFF"},
        ]
        return MapConditional.process(
            source_frame=pd.concat([self.source_frame, df_after_lookup], axis=1),
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                cases=cases,
            ),
        )

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        pass

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_venue(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.venue.
        It assumes that _transaction_details_ultimate_venue() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_VENUE,
            ),
            auditor=self.auditor,
        )

    @staticmethod
    def _get_mic_mapping_table_from_s3():
        """Gets the MIC Code mapping from the required S3 path (this is required for populating
        the ultimateVenue and venue)"""
        mic_mapping_key = (
            "mapping_tables/order-feed-fidessa-processor/fidessa-mic-code-mapping.csv"
        )
        return DfFromS3Csv.process(params=ParamsDfFromS3Csv(s3_key=mic_mapping_key))

    def _get_counterparty(self) -> pd.DataFrame:
        """Encapsulates the logic to get the counterparty. It returns a dataframe
        with the counterparty column (without a prefix)
        """
        counterparty_code_df = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.COUNTERPARTY_CODE,
                target_attribute=TempColumns.COUNTERPARTY_CODE_IN_PARENTHESES,
                prefix="(",
                suffix=")",
            ),
            auditor=self.auditor,
        )
        concat_attributes_frame = ConcatAttributes.process(
            source_frame=pd.concat([counterparty_code_df, self.source_frame], axis=1),
            params=ParamsConcatAttributes(
                target_attribute=TempColumns.CONCAT_COUNTERPARTY_DESC_AND_CODE,
                source_attributes=[
                    SourceColumns.COUNTERPARTY_DESCRIPTION,
                    TempColumns.COUNTERPARTY_CODE_IN_PARENTHESES,
                ],
                delimiter=" ",
            ),
        )

        counterparty_code_not_null = f"{SourceColumns.COUNTERPARTY_CODE}.notnull()"
        counterparty_code_is_null = f"{SourceColumns.COUNTERPARTY_CODE}.isnull()"

        cases_counterparty_without_prefix = [
            {
                "query": counterparty_code_not_null,
                "attribute": TempColumns.CONCAT_COUNTERPARTY_DESC_AND_CODE,
            },
            {
                "query": counterparty_code_is_null,
                "attribute": SourceColumns.HOUSE_BOOK_ID,
            },
        ]
        return MapConditional.process(
            source_frame=pd.concat(
                [self.source_frame, concat_attributes_frame], axis=1
            ),
            params=ParamsMapConditional(
                target_attribute=TempColumns.COUNTERPARTY_WITHOUT_PREFIX,
                cases=cases_counterparty_without_prefix,
            ),
        )

    def _party_columns_with_id_prefix(self):
        """Adds the 'id:' prefix to all the party columns and returns a data frame"""
        return pd.concat(
            [
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=SourceColumns.EXECUTING_ENTITY,
                        target_attribute=TempColumns.EXECUTING_ENTITY_WITH_PREFIX,
                        prefix=PartyPrefix.ID,
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=SourceColumns.ENTERED_BY,
                        target_attribute=TempColumns.ENTERED_BY_WITH_PREFIX,
                        prefix=PartyPrefix.ID,
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.COUNTERPARTY_WITHOUT_PREFIX,
                        target_attribute=TempColumns.COUNTERPARTY_WITH_PREFIX,
                        prefix=PartyPrefix.ID,
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=SourceColumns.INVESTMENT_DECISION_VALUE,
                        target_attribute=TempColumns.INVESTMENT_DECISION_VALUE_WITH_PREFIX,
                        prefix=PartyPrefix.ID,
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=SourceColumns.EXECUTOR,
                        target_attribute=TempColumns.EXECUTOR_WITH_PREFIX,
                        prefix=PartyPrefix.ID,
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""
