import pandas as pd
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.order.transformations.fidessa.front_office_trades.fidessa_front_office_trades_transformations import (
    FidessaFrontOfficeTradesTransformations,
)
from swarm_tasks.order.transformations.fidessa.front_office_trades.fidessa_front_office_trades_transformations import (
    SourceColumns,
)
from swarm_tasks.order.transformations.fidessa.front_office_trades.fidessa_front_office_trades_transformations import (
    TempColumns,
)
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)


class InvestecFidessaFrontOfficeTradesTransformations(
    FidessaFrontOfficeTradesTransformations
):
    def _buy_sell(self) -> pd.DataFrame:
        """Returns a data frame containing _order.buySell and _orderState.buySell.
        This is populated from SourceColumns.BUY_SELL conditionally based on SourceColumns.ENTERED_BY
        """
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, TempColumns.BUY_SELL].values,
                    index=self.pre_process_df.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.BUY_SELL,
                        )
                    ],
                ),
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, TempColumns.BUY_SELL].values,
                    index=self.pre_process_df.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.BUY_SELL,
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.buySellIndicator.
        This is populated from SourceColumns.BUY_SELL conditionally based on SourceColumns.ENTERED_BY
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.BUY_SELL_INDICATOR].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.parties by calling PartyIdentifiers"""

        # Note: adding prefixes and concatenating Series results in the column names
        # being retained (from the source frame) in the resulting df
        with_prefix_df = pd.concat(
            [
                PartyPrefix.ID
                + self.source_frame.loc[:, SourceColumns.TRADING_ENTITY_ID],
                PartyPrefix.ID + self.source_frame.loc[:, SourceColumns.TRADER],
                PartyPrefix.ID
                + self.source_frame.loc[:, SourceColumns.COUNTERPARTY_DESCRIPTION],
                PartyPrefix.ID
                + self.source_frame.loc[:, SourceColumns.INVESTMENT_DECISION_VALUE],
                PartyPrefix.ID
                + self.source_frame.loc[:, SourceColumns.EXECUTION_DECISION_VALUE],
                "(" + self.source_frame.loc[:, SourceColumns.COUNTERPARTY] + ")",
            ],
            axis=1,
        )

        concat_df = ConcatAttributes.process(
            source_frame=with_prefix_df,
            params=ParamsConcatAttributes(
                source_attributes=[
                    SourceColumns.COUNTERPARTY_DESCRIPTION,
                    SourceColumns.COUNTERPARTY,
                ],
                target_attribute=TempColumns.CLIENT_ID,
                delimiter=" ",
            ),
        )

        return GenericOrderPartyIdentifiers.process(
            source_frame=pd.concat([with_prefix_df, concat_df], axis=1),
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=SourceColumns.TRADING_ENTITY_ID,
                trader_identifier=SourceColumns.TRADER,
                client_identifier=TempColumns.CLIENT_ID,
                counterparty_identifier=TempColumns.CLIENT_ID,
                execution_within_firm_identifier=SourceColumns.EXECUTION_DECISION_VALUE,
                investment_decision_within_firm_identifier=SourceColumns.INVESTMENT_DECISION_VALUE,
                buy_sell_side_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                buyer_identifier=SourceColumns.TRADING_ENTITY_ID,
                seller_identifier=TempColumns.CLIENT_ID,
                use_buy_mask_for_buyer_seller=False,
            ),
        )
