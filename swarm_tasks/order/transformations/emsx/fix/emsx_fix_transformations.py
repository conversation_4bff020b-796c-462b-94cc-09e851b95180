import pandas as pd
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_elastic_schema.static.mifid2 import ValidityPeriod
from se_elastic_schema.static.reference import OrderRecordType
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order.static import OrderType
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.generic.get_tenant_lei import Params as ParamsGetTenantLEI
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)


class SourceColumns:
    ACCOUNT = "Account"
    AVGPX = "AvgPx"
    BEGINSTRING = "BeginString"
    BODYLENGTH = "BodyLength"
    CHECKSUM = "CheckSum"
    CLORDID = "ClOrdID"
    COMMISSIONCOMMTYPE = "Commission"
    COMMISSION = "CommType"
    CUMQTY = "CumQty"
    CURRENCY = "Currency"
    EXECBROKER = "ff_76"
    EXECID = "ExecID"
    EXECTRANSTYPE = "ExecTransType"
    EXECTYPE = "ExecType"
    FF_453 = "ff_453"
    FF_7014 = "ff_7014"
    FF_76 = "ff_76"
    HANDLINST = "HandlInst"
    LASTCAPACITY = "LastCapacity"
    LASTMKT = "LastMkt"
    LASTPX = "LastPx"
    LASTQTY = "LastQty"
    LEAVESQTY = "LeavesQty"
    MSGSEQNUM = "MsgSeqNum"
    MSGTYPE = "MsgType"
    ONBEHALFOFSUBID = "OnBehalfOfSubID"
    ORDERID = "OrderID"
    ORDERQTY = "OrderQty"
    ORDSTATUS = "OrdStatus"
    ORDTYPE = "OrdType"
    ORIGCLORDID = "OrigClOrdID"
    PARTYID = "PartyID"
    PARTYIDSOURCE = "PartyIDSource"
    PARTYROLE = "PartyRole"
    PRICE = "Price"
    S3FILEURL = "S3FileURL"
    SECURITYEXCHANGE = "SecurityExchange"
    SECURITYID = "SecurityID"
    SECURITYIDSOURCE = "SecurityIDSource"
    SENDERCOMPID = "SenderCompID"
    SENDERSUBID = "SenderSubID"
    SENDINGTIME = "SendingTime"
    SETTLCURRENCY = "SettlCurrency"
    SETTLDATE = "SettlDate"
    SETTLTYPE = "SettlType"
    SIDE = "Side"
    STOPPX = "StopPx"
    SYMBOL = "Symbol"
    TARGETCOMPID = "TargetCompID"
    TARGETSUBID = "TargetSubID"
    TEXT = "Text"
    TIMEINFORCE = "TimeInForce"
    TRADEDATE = "TradeDate"
    TRADEREPORTINGINDICATOR = "TradeReportingIndicator"
    TRANSACTTIME = "TransactTime"


class TempColumns:
    BUYSELL = "__buy_sell__"
    EXEC_BROKER_WITH_ID = "__exec_broker_with_id__"
    EXECUTING_ENTITY_WITH_LEI = "__executing_entity_with_lei__"
    IS_CREATED_THROUGH_FALLBACK = "__instrument_created_through_fb__"
    NEWO_IN_FILE = "__newo_in_file__"
    ORDER_ID = "__order_id__"
    PRICE_CONVERTED = "__price_converted__"
    SECURITY_ID = "__security_id__"
    SETTL_DATE_CONVERTED = "__settl_date_converted__"
    TRANSACTTIME_CONVERTED = "__transact_time_converted__"
    TRANSACTTIME_CONVERTED_DATE = "__transact_time_converted_date__"


class EMSXFixTransformations(AbstractOrderTransformations):
    def _pre_process(self):
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self.get_buy_sell(),
                self.get_party_fields(),
                self.get_price(),
                self.get_order_state_id(),
                self.get_settl_date(),
                self.get_transact_time(),
            ],
            axis=1,
        )

    def _post_process(self):
        quantities_columns = [
            OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
            add_prefix(
                ModelPrefix.ORDER_STATE, OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
            ),
            OrderColumns.TRANSACTION_DETAILS_QUANTITY,
        ]
        self.target_df.loc[:, quantities_columns] = (
            self.target_df.loc[:, quantities_columns]
            .replace(to_replace=0, value=pd.NA)
            .fillna(pd.NA)
        )

        self.target_df.loc[:, TempColumns.IS_CREATED_THROUGH_FALLBACK] = True
        self.target_df = pd.concat(
            [
                self.target_df,
                self.newo_in_file(),
            ],
            axis=1,
        )

    def _buy_sell(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.SIDE
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[
                :, [TempColumns.BUYSELL, TempColumns.BUYSELL]
            ].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(ModelPrefix.ORDER, OrderColumns.BUY_SELL),
                add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.BUY_SELL),
            ],
        )

    def _data_source_name(self) -> pd.DataFrame:
        """
        Populates with 'BBG - EMSX - FIX'
        """
        return pd.DataFrame(
            data="BBG - EMSX - FIX",
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.TRANSACTTIME
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[
                :, TempColumns.TRANSACTTIME_CONVERTED_DATE
            ].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.DATE],
        )

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.SIDE
        """
        return MapValue.process(
            source_frame=self.pre_process_df,
            params=ParamsMapValue(
                source_attribute=TempColumns.BUYSELL,
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                value_map={
                    "1": BuySellIndicator.BUYI.value,
                    "2": BuySellIndicator.SELL.value,
                },
            ),
        )

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.PRICE when SourceColumns.ORDTYPE == '2'
        """
        return MapConditional.process(
            source_frame=pd.concat(
                [
                    self.pre_process_df.loc[:, TempColumns.PRICE_CONVERTED],
                    self.source_frame.loc[:, SourceColumns.ORDTYPE],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE,
                cases=[
                    Case(
                        query=f"`{SourceColumns.ORDTYPE}` == '2'",
                        attribute=TempColumns.PRICE_CONVERTED,
                    ),
                ],
            ),
        )

    def _execution_details_order_status(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.EXECTYPE
        """
        return pd.concat(
            [
                MapValue.process(
                    source_frame=self.source_frame,
                    params=ParamsMapValue(
                        source_attribute=SourceColumns.EXECTYPE,
                        target_attribute=add_prefix(
                            ModelPrefix.ORDER_STATE,
                            OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        ),
                        value_map={
                            "1": OrderStatus.PARF.value,
                            "2": OrderStatus.FILL.value,
                            "3": OrderStatus.DNFD.value,
                            "4": OrderStatus.CAME.value,
                            "5": OrderStatus.REME.value,
                            "6": OrderStatus.PNDC.value,
                            "8": OrderStatus.REMO.value,
                            "C": OrderStatus.EXPI.value,
                        },
                    ),
                ),
                pd.DataFrame(
                    data=OrderStatus.NEWO.value,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            ModelPrefix.ORDER,
                            OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.ORDTYPE
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.ORDTYPE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
                value_map={
                    "1": OrderType.MARKET,
                    "2": OrderType.LIMIT,
                    "3": OrderType.STOP,
                },
            ),
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """
        Populates with SourceColumn.ACCOUNT
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ACCOUNT].values,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO],
        )

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.LASTCAPACITY
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.LASTCAPACITY,
                target_attribute=OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY,
                value_map={
                    "1": TradingCapacity.AOTC.value,
                    "2": TradingCapacity.AOTC.value,
                    "3": TradingCapacity.MTCH.value,
                    "4": TradingCapacity.DEAL.value,
                    "5": TradingCapacity.DEAL.value,
                },
            ),
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.TIMEINFORCE
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.TIMEINFORCE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD,
                value_map={
                    "0": [ValidityPeriod.DAVY.value],
                    "1": [ValidityPeriod.GTCV.value],
                    "3": [ValidityPeriod.IOCV.value],
                    "4": [ValidityPeriod.FOKV.value],
                    "5": [ValidityPeriod.GTXV.value],
                    "6": [ValidityPeriod.GTDV.value],
                },
            ),
        )

    def _id(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.ORDERID
        """
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.source_frame.loc[:, SourceColumns.ORDERID].values,
                    index=self.source_frame.index,
                    columns=[add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID)],
                ),
                MapConditional.process(
                    source_frame=pd.concat(
                        [
                            self.source_frame.loc[
                                :, [SourceColumns.ORIGCLORDID, SourceColumns.ORDERID]
                            ],
                            self.pre_process_df.loc[:, TempColumns.ORDER_ID],
                        ],
                        axis=1,
                    ),
                    params=ParamsMapConditional(
                        target_attribute=add_prefix(ModelPrefix.ORDER, OrderColumns.ID),
                        cases=[
                            Case(
                                query=f"(`{SourceColumns.ORIGCLORDID}`.isnull()) | (`{TempColumns.ORDER_ID}`.isnull())",
                                attribute=SourceColumns.ORDERID,
                            ),
                            Case(
                                query=f"(`{SourceColumns.ORIGCLORDID}`.notnull()) & (`{TempColumns.ORDER_ID}`.notnull())",
                                attribute=TempColumns.ORDER_ID,
                            ),
                        ],
                    ),
                ),
            ],
            axis=1,
        )

    def _market_identifiers(self) -> pd.DataFrame:
        """Merges Market Identifiers"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """
        Populates InstrumentIdentifiers
        """
        return InstrumentIdentifiers.process(
            source_frame=self.source_frame,
            params=ParamsInstrumentIdentifiers(
                isin_attribute=SourceColumns.SECURITYID,
                retain_task_inputs=True,
            ),
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """
        Populates PartyIdentifiers
        """
        return GenericOrderPartyIdentifiers.process(
            source_frame=self.pre_process_df,
            params=ParamsPartyIdentifiers(
                executing_entity_identifier=TempColumns.EXECUTING_ENTITY_WITH_LEI,
                client_identifier=TempColumns.EXECUTING_ENTITY_WITH_LEI,
                counterparty_identifier=TempColumns.EXEC_BROKER_WITH_ID,
                use_buy_mask_for_buyer_seller=True,
                buyer_identifier=TempColumns.EXEC_BROKER_WITH_ID,
                seller_identifier=TempColumns.EXECUTING_ENTITY_WITH_LEI,
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """
        Populates with `Order` and `OrderState`
        """
        return pd.DataFrame(
            data=[["Order", "OrderState"]],
            index=self.source_frame.index,
            columns=[
                add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.META_MODEL),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.META_MODEL,
                ),
            ],
        )

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.CLORDID
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.CLORDID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID],
        )

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.EXECID
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.EXECID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE],
        )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.ORDERID
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORDERID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
        )

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.ORDERID
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORDERID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO],
        )

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.ORDERQTY
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORDERQTY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY],
        )

    def _price_forming_data_price(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.LASTPX
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.CURRENCY,
                source_price_attribute=SourceColumns.LASTPX,
                target_price_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
            ),
        )

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.LEAVESQTY
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LEAVESQTY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY],
        )

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.LASTQTY. Only for OrderStates
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LASTQTY].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    ModelPrefix.ORDER_STATE,
                    OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                )
            ],
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.ORDERID
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORDERID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO],
        )

    def _source_index(self) -> pd.DataFrame:
        """
        Populates with index
        """
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_INDEX],
        )

    def _source_key(self) -> pd.DataFrame:
        """
        Populates with SourceColumns.S3FILEURL
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.S3FILEURL].values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_KEY],
        )

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.TRANSACTTIME
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.TRANSACTTIME_CONVERTED].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_RECEIVED],
        )

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.TRANSACTTIME
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.TRANSACTTIME_CONVERTED].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_SUBMITTED],
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.TRANSACTTIME
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.TRANSACTTIME_CONVERTED].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED],
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.TRANSACTTIME
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.TRANSACTTIME_CONVERTED].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TIMESTAMPS_TRADING_DATE_TIME],
        )

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.SIDE
        """
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _transaction_details_price(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.LASTPX
        """
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.PRICE_FORMING_DATA_PRICE].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE],
        )

    def _transaction_details_price_average(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.AVGPX
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.CURRENCY,
                source_price_attribute=SourceColumns.AVGPX,
                target_price_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_AVERAGE,
            ),
        )

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.CURRENCY
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.CURRENCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
            ),
        )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """
        Populates with 'MONE`
        """
        return pd.DataFrame(
            data=PriceNotation.MONE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION],
        )

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.LASTQTY
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LASTQTY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY],
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.CURRENCY
        """
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY],
        )

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """
        Populate with `UNIT`
        """
        return pd.DataFrame(
            data=QuantityNotation.UNIT.value,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION],
        )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """
        Populate with `Client Side`
        """
        return pd.DataFrame(
            data=OrderRecordType.CLIENT_SIDE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE],
        )

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.SETTLCURRENCY
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.SETTLCURRENCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_SETTLEMENT_AMOUNT_CURRENCY,
            ),
        )

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.SETTLDATE
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.SETTL_DATE_CONVERTED].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_SETTLEMENT_DATE],
        )

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.LASTCAPACITY
        """
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.TRANSACTTIME
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.TRANSACTTIME_CONVERTED].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME],
        )

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """
        Populate from SourceColumns.LASTMKT
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LASTMKT].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE],
        )

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """
        Populate from SourceColumns.LASTMKT
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LASTMKT].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_VENUE],
        )

    # Auxiliary Methods
    def get_buy_sell(self) -> pd.DataFrame:
        """
        Converts SourceColumns.SIDE into the expected BuySell format
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.SIDE,
                target_attribute=TempColumns.BUYSELL,
                value_map={
                    "1": "1",
                    "2": "2",
                    "3": "1",
                    "4": "2",
                    "5": "2",
                    "6": "2",
                    "H": "2",
                },
            ),
        )

    def get_order_state_id(self) -> pd.DataFrame:
        """
        Generates values to use as the `_orderState.id` when SourceColumns.ORIGCLORDID is present
        :return: Dataframe with TempColumns.ORDER_ID column
        """
        cl_ord_id_to_order_id_dict = pd.Series(
            data=self.source_frame.loc[:, SourceColumns.ORDERID].values,
            index=self.source_frame.loc[:, SourceColumns.CLORDID].values,
        ).to_dict()
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORIGCLORDID]
            .map(cl_ord_id_to_order_id_dict)
            .values,
            index=self.source_frame.index,
            columns=[TempColumns.ORDER_ID],
        ).fillna(pd.NA)

    def get_party_fields(self) -> pd.DataFrame:
        """
        Generates all the necessary fields to use in PartyIdentifiers
        """
        return pd.concat(
            [
                GetTenantLEI.process(
                    source_frame=self.source_frame,
                    params=ParamsGetTenantLEI(
                        target_column_prefix=PartyPrefix.LEI,
                        target_lei_column=TempColumns.EXECUTING_ENTITY_WITH_LEI,
                    ),
                ),
                pd.DataFrame(
                    data=PartyPrefix.ID
                    + self.source_frame.loc[:, SourceColumns.EXECBROKER].values,
                    index=self.source_frame.index,
                    columns=[TempColumns.EXEC_BROKER_WITH_ID],
                ),
            ],
            axis=1,
        )

    def get_price(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.PRICE
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.CURRENCY,
                source_price_attribute=SourceColumns.PRICE,
                target_price_attribute=TempColumns.PRICE_CONVERTED,
            ),
        )

    def get_settl_date(self) -> pd.DataFrame:
        """
        Converts SourceColumns.SETTLDATE into the expected format
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.SETTLDATE,
                source_attribute_format="%Y%m%d",
                convert_to=ConvertTo.DATE.value,
                target_attribute=TempColumns.SETTL_DATE_CONVERTED,
            ),
        )

    def get_transact_time(self) -> pd.DataFrame:
        """
        Converts SourceColumns.TRANSACTTIME into the expected formats
        """
        return pd.concat(
            [
                ConvertDatetime.process(
                    source_frame=self.source_frame,
                    params=ParamsConvertDatetime(
                        source_attribute=SourceColumns.TRANSACTTIME,
                        source_attribute_format="%Y%m%d-%H:%M:%S.%f",
                        convert_to=ConvertTo.DATETIME.value,
                        target_attribute=TempColumns.TRANSACTTIME_CONVERTED,
                    ),
                ),
                ConvertDatetime.process(
                    source_frame=self.source_frame,
                    params=ParamsConvertDatetime(
                        source_attribute=SourceColumns.TRANSACTTIME,
                        source_attribute_format="%Y%m%d-%H:%M:%S.%f",
                        convert_to=ConvertTo.DATE.value,
                        target_attribute=TempColumns.TRANSACTTIME_CONVERTED_DATE,
                    ),
                ),
            ],
            axis=1,
        )

    def newo_in_file(self) -> pd.DataFrame:
        """
        Temporary column for RemoveDuplicateNEWO
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.NEWO_IN_FILE,
                cases=[
                    Case(
                        query=f"`{SourceColumns.EXECTYPE}` == '0'",
                        value=True,
                    ),
                    Case(
                        query=f"`{SourceColumns.EXECTYPE}` != '0'",
                        value=False,
                    ),
                ],
            ),
        )

    # Not implemented
    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_stop_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _financing_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _hierarchy(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_discretionary(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_iceberg(self):
        """Not Implemented"""

    def _is_repo(self):
        """Not Implemented"""

    def _is_synthetic(self):
        """Not Implemented"""

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        """Not Implemented"""

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""

    def _order_class(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_order_received(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_validity_period(self) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_venue(self) -> pd.DataFrame:
        """Not Implemented"""
