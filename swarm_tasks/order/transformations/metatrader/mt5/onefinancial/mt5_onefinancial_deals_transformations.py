import pandas as pd
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.order.transformations.metatrader.mt5.mt5_deals_transformations import (
    MT5DealsTransformations,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import ONE_FINANCIAL
from swarm_tasks.order.transformations.metatrader.mt5.static import TempPartyIDs


class MT5OneFinancialDealsTransformations(MT5DealsTransformations):
    def populate_party_identifiers(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        :param df: pd.Dataframe
        :return: pd.Dataframe
        Overrides populate_party_identifiers() method from
        MT5OrderTransformations to add EXECUTING_ENTITY and
        INVESTMENT_DEC_WITHIN_FIRM
        """
        df = super().populate_party_identifiers(df=self.pre_process_df)

        # Over-writes EXECUTING_ENTITY from generic flow
        df.loc[:, TempPartyIDs.EXECUTING_ENTITY] = f"{PartyPrefix.ID}{ONE_FINANCIAL}"
        df.loc[:, TempPartyIDs.INVESTMENT_DEC_WITHIN_FIRM] = PartyPrefix.ALGO_1
        return df
