import logging
import os

import pandas as pd
from prefect.engine.signals import SKIP
from se_core_tasks.currency.convert_minor_to_major import CastTo
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.components.market.identifier import Identifier
from se_elastic_schema.static.market import IdentifierType
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import DeliveryType
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import RTS22PositionEffectType
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_elastic_schema.static.reference import InstrumentIdCodeType
from se_elastic_schema.static.reference import OrderRecordType
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order.static import Venue
from se_trades_tasks.order_and_tr.instrument.identifiers.identifiers import (
    InstrumentIdentifiers,
)
from se_trades_tasks.order_and_tr.static import (
    INSTRUMENT_PATH,
)
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.io.read.aws.df_from_s3_csv import DfFromS3Csv
from swarm_tasks.io.read.aws.df_from_s3_csv import Params as ParamsDfFromS3Csv
from swarm_tasks.order.transformations.metatrader.mt5.static import AssetClassIdentifier
from swarm_tasks.order.transformations.metatrader.mt5.static import calculate_qty
from swarm_tasks.order.transformations.metatrader.mt5.static import (
    populate_buyer_seller_decision_maker,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import (
    query_m_asset_fx_currency,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import (
    query_m_asset_not_fx_currency,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import SourceDealsColumns
from swarm_tasks.order.transformations.metatrader.mt5.static import (
    SourceInstrumentColumns,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import TempColumns
from swarm_tasks.order.transformations.metatrader.mt5.static import TempPartyIDs
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.generic.get_tenant_lei import Params as ParamsGetTenantLEI
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_static import MapStatic
from swarm_tasks.transform.map.map_static import Params as ParamsMapStatic
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.utilities.static import Delimiters
from swarm_tasks.utilities.static import MetaModel
from swarm_tasks.utilities.static import SWARM_FILE_URL

DATA_SOURCE_NAME = "MetaTrader5 - Deals"
logger_ = logging.getLogger(__name__)


class MT5DealsTransformations(AbstractOrderTransformations):
    def _pre_process(self):

        # instrument_data from s3 file
        instrument_df = DfFromS3Csv.process(
            params=ParamsDfFromS3Csv(
                s3_key="resource/metatrader/mt5/instrument/InstrumentData.csv"
            ),
        )
        # check for required columns in instrument_data df
        for col in SourceInstrumentColumns().get_columns():
            if col not in instrument_df.columns:
                instrument_df.loc[:, col] = pd.NA

        # merge source_frame with instrument data
        self.source_frame = self.source_frame.merge(
            instrument_df.drop_duplicates(subset=[SourceInstrumentColumns.C_MT5_ID]),
            how="left",
            left_on=SourceDealsColumns.SYMBOL,
            right_on=SourceInstrumentColumns.C_MT5_ID,
        )

        # remove rows where instrument not found
        instrument_na_mask = self.source_frame[
            SourceInstrumentColumns.C_MT5_ID
        ].isnull()

        # Raise SKIP if instruments not found for all rows
        if instrument_na_mask.all():
            msg = "No rows can be linked to instruments. So skipping all rows."
            logger_.error(msg)
            self.auditor.add(msg)
            raise SKIP(msg)

        # Instruments not found for some rows
        if instrument_na_mask.any():
            instrument_na_symbol_list = self.source_frame.loc[
                instrument_na_mask, SourceDealsColumns.SYMBOL
            ].tolist()
            instrument_na_indices = self.source_frame.loc[
                instrument_na_mask
            ].index.tolist()
            msg = (
                f"[!] Skipping rows where instruments not found...\n"
                f"[!] Count of rows skipped: "
                f"[!] {len(self.source_frame.loc[instrument_na_mask])}.\n"
                f"[!] Source Symbols: {set(instrument_na_symbol_list)}.\n"
                f"[!] Source Indices: {instrument_na_indices}"
            )
            self.auditor.add(msg)
            logger_.warning(msg)
            self.source_frame = self.source_frame.loc[~instrument_na_mask]

        # re-align target and pre-process_df to match source_frame
        self.pre_process_df = pd.DataFrame(index=self.source_frame.index)
        self.target_df = pd.DataFrame(index=self.source_frame.index)

        self.pre_process_df.loc[
            :, TempColumns.TEMP_SOURCE_TIMESTAMP
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceDealsColumns.TIME_MSC,
                target_attribute=TempColumns.TEMP_SOURCE_TIMESTAMP,
            ),
            auditor=self.auditor,
        )[
            TempColumns.TEMP_SOURCE_TIMESTAMP
        ]

        self.pre_process_df.loc[:, TempColumns.TEMP_DATE] = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceDealsColumns.TIME_MSC,
                target_attribute=TempColumns.TEMP_DATE,
                convert_to=ConvertTo.DATE.value,
            ),
        )[TempColumns.TEMP_DATE]

        self.pre_process_df.loc[
            :, TempColumns.TEMP_TIMESTAMP
        ] = ConvertDatetime.process(
            source_frame=self.pre_process_df,
            params=ParamsConvertDatetime(
                source_attribute=TempColumns.TEMP_SOURCE_TIMESTAMP,
                target_attribute=TempColumns.TEMP_TIMESTAMP,
                convert_to=ConvertTo.DATETIME.value,
            ),
        )[
            TempColumns.TEMP_TIMESTAMP
        ]

        self.pre_process_df.loc[
            :, TempColumns.TEMP_ORDER_ID
        ] = ConcatAttributes.process(
            source_frame=self.source_frame,
            params=ParamsConcatAttributes(
                source_attributes=[
                    SourceDealsColumns.POSITION_ID,
                    SourceDealsColumns.ORDER,
                    SourceDealsColumns.OANDA_USER_ID,
                ],
                target_attribute=TempColumns.TEMP_ORDER_ID,
                delimiter=Delimiters.PIPE,
            ),
        )[
            TempColumns.TEMP_ORDER_ID
        ]

        self.pre_process_df.loc[
            :, TempColumns.TEMP_STATIC_TEXT_MODIFY_FLAGS
        ] = MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=TempColumns.TEMP_STATIC_TEXT_MODIFY_FLAGS,
                target_value="Modify Flags -",
            ),
        )[
            TempColumns.TEMP_STATIC_TEXT_MODIFY_FLAGS
        ]

        self.pre_process_df.loc[
            :, TempColumns.TEMP_BUY_SELL_INDICATOR
        ] = MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceDealsColumns.ACTION,
                target_attribute=TempColumns.TEMP_BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "0": BuySellIndicator.BUYI.value,
                    "1": BuySellIndicator.SELL.value,
                    "13": BuySellIndicator.BUYI.value,
                    "14": BuySellIndicator.SELL.value,
                },
            ),
            auditor=self.auditor,
        )[
            TempColumns.TEMP_BUY_SELL_INDICATOR
        ]

        self.pre_process_df.loc[:, SourceDealsColumns.LOGIN] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceDealsColumns.LOGIN,
                target_attribute=SourceDealsColumns.LOGIN,
            ),
            auditor=self.auditor,
        )[SourceDealsColumns.LOGIN]

        self.pre_process_df.loc[
            :, SourceDealsColumns.OANDA_USER_ID
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceDealsColumns.OANDA_USER_ID,
                target_attribute=SourceDealsColumns.OANDA_USER_ID,
            ),
            auditor=self.auditor,
        )[
            SourceDealsColumns.OANDA_USER_ID
        ]

        self.pre_process_df.loc[:, TempColumns.TEMP_QTY] = self.get_temp_qty()

        # add party identifier fields to pre_process_df
        self.pre_process_df = self.populate_party_identifiers(df=self.pre_process_df)

        self.query_c_product_cfd = (
            "`c_product`.str.fullmatch('cfd', case=False, na=False)"
        )
        self.query_c_product_not_cfd = (
            "~(`c_product`.str.fullmatch('cfd', case=False, na=False))"
        )
        self.pre_process_df.loc[
            :, TempColumns.TEMP_INST_ASSET_CLASS
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_INST_ASSET_CLASS,
                cases=[
                    Case(
                        query=self.query_c_product_cfd,
                        value="CFD",
                    ),
                    Case(
                        query="`c_product`.str.fullmatch('spreadbet', case=False, na=False)",
                        value="SB",
                    ),
                ],
            ),
        )[
            TempColumns.TEMP_INST_ASSET_CLASS
        ]

        self.pre_process_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_FULL_NAME
        ] = ConcatAttributes.process(
            source_frame=self.source_frame,
            params=ParamsConcatAttributes(
                source_attributes=[
                    SourceInstrumentColumns.C_NAME,
                    SourceInstrumentColumns.M_ASSET,
                    SourceInstrumentColumns.C_PRODUCT,
                ],
                delimiter="-",
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_FULL_NAME,
            ),
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_FULL_NAME
        ]

    def _post_process(self):
        for col in [
            TempPartyIDs.EXECUTING_ENTITY,
            TempPartyIDs.CLIENT,
            TempPartyIDs.INVESTMENT_DEC_WITHIN_FIRM,
        ]:
            if col not in self.pre_process_df:
                self.pre_process_df.loc[:, col] = pd.NA

            self.target_df.loc[:, col] = self.pre_process_df.loc[:, col]

        # Temp colum used in the ConcatAttributes Task downstream
        self.target_df.loc[:, TempColumns.TEMP_XXXX_VENUE] = Venue.XXXX

        self.target_df.loc[
            :, TempColumns.TEMP_BUY_SELL_INDICATOR
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_BUY_SELL_INDICATOR]

        # fields for instrument identifiers downstream
        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_SYMBOL
        ] = InstrumentIdentifiers.format_symbol(
            df=self.source_frame,
            src_col=SourceDealsColumns.SYMBOL,
            dest_col=TempColumns.TEMP_INST_ID_SYMBOL,
        )

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE,
                cases=[
                    Case(
                        query=self.query_c_product_cfd,
                        value="CD",
                    ),
                    Case(
                        query=self.query_c_product_not_cfd,
                        value="OT",
                    ),
                ],
            ),
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_TYPE
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_TYPE,
                cases=[
                    Case(
                        query="`c_product`.str.fullmatch('cfd', case=False, na=False)",
                        value="CFD",
                    )
                ],
            ),
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_TYPE
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceInstrumentColumns.M_TR_ASSET,
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS,
                end_index=2,
            ),
            auditor=self.auditor,
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_BASE_PRODUCT
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceInstrumentColumns.M_COMMODITY_BASE,
                target_attribute=TempColumns.TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_BASE_PRODUCT,
            ),
            auditor=self.auditor,
        )[
            TempColumns.TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_BASE_PRODUCT
        ]

        self.target_df.loc[
            :,
            TempColumns.TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_FURTHER_SUB_PRODUCT,
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceInstrumentColumns.M_COMMODITY_DETAILS,
                target_attribute=TempColumns.TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_FURTHER_SUB_PRODUCT,
            ),
            auditor=self.auditor,
        )[
            TempColumns.TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_FURTHER_SUB_PRODUCT
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_DELIVERY_TYPE
        ] = MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=TempColumns.TEMP_INST_ID_DELIVERY_TYPE,
                target_value=DeliveryType.CASH.value,
            ),
        )[
            TempColumns.TEMP_INST_ID_DELIVERY_TYPE
        ]

        self.target_df.loc[:, TempColumns.TEMP_INST_ID_PRICE_MULTIPLIER] = 1

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_EXT_EXCHANGE_SYMBOL
        ] = self.source_frame.loc[:, SourceInstrumentColumns.M_NAME]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_ID_CODE_TYPE
        ] = MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_ID_CODE_TYPE,
                target_value=InstrumentIdCodeType.OTHR.value,
            ),
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_ID_CODE_TYPE
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_PRICE_NOTATION
        ] = MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=TempColumns.TEMP_INST_ID_PRICE_NOTATION,
                target_value=PriceNotation.MONE.value,
            ),
        )[
            TempColumns.TEMP_INST_ID_PRICE_NOTATION
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceInstrumentColumns.C_CFI,
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION,
            ),
            auditor=self.auditor,
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_FULL_NAME
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_INST_ID_INSTRUMENT_FULL_NAME]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID
        ] = MapConditional.process(
            source_frame=pd.concat([self.source_frame, self.target_df], axis=1),
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID,
                cases=[
                    Case(
                        query="`m_ISIN`.str.len() == 12",
                        attribute=SourceInstrumentColumns.M_ISIN,
                    ),
                    Case(
                        query="`t_isin`.str.len() == 12",
                        attribute=SourceInstrumentColumns.T_ISIN,
                    ),
                ],
            ),
        )[
            TempColumns.TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_UNDERLYING_INDEX_NAME
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_INST_ID_UNDERLYING_INDEX_NAME,
                cases=[
                    Case(
                        query="`c_CFI`.str.upper().str.startswith('JEI', na=False)",
                        attribute=SourceDealsColumns.SYMBOL,
                    )
                ],
            ),
        )[
            TempColumns.TEMP_INST_ID_UNDERLYING_INDEX_NAME
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ASSET_CLASS
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_INST_ASSET_CLASS]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED
        ] = ConcatAttributes.process(
            source_frame=pd.concat([self.target_df, self.source_frame], axis=1),
            params=ParamsConcatAttributes(
                source_attributes=[
                    TempColumns.TEMP_XXXX_VENUE,
                    SourceInstrumentColumns.M_ISIN,
                    OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                    TempColumns.TEMP_INST_ASSET_CLASS,
                ],
                target_attribute=TempColumns.TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED,
            ),
        )[
            TempColumns.TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED
        ]

        self.target_df.loc[:, TempColumns.TEMP_ASSET_CLASS] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_ASSET_CLASS,
                cases=[
                    Case(
                        query=query_m_asset_fx_currency,
                        value=AssetClassIdentifier.FX,
                    ),
                    Case(query=query_m_asset_not_fx_currency, value=""),
                ],
            ),
        )[TempColumns.TEMP_ASSET_CLASS]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED
        ] = ConcatAttributes.process(
            source_frame=pd.concat([self.target_df, self.source_frame], axis=1),
            params=ParamsConcatAttributes(
                source_attributes=[
                    TempColumns.TEMP_XXXX_VENUE,
                    SourceInstrumentColumns.T_ISIN,
                    OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                    TempColumns.TEMP_ASSET_CLASS,
                    TempColumns.TEMP_INST_ASSET_CLASS,
                ],
                target_attribute=TempColumns.TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED,
            ),
        )[
            TempColumns.TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED
        ] = ConcatAttributes.process(
            source_frame=self.target_df,
            params=ParamsConcatAttributes(
                source_attributes=[
                    TempColumns.TEMP_XXXX_VENUE,
                    TempColumns.TEMP_INST_ID_SYMBOL,
                    TempColumns.TEMP_ASSET_CLASS,
                    TempColumns.TEMP_INST_ASSET_CLASS,
                ],
                target_attribute=TempColumns.TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED,
            ),
        )[
            TempColumns.TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED
        ]

        # Temp column for Cash Equity instruments
        self.target_df.loc[
            :, TempColumns.TEMP_INST_UNIQUE_ID_CASH_EQUITY
        ] = ConcatAttributes.process(
            source_frame=pd.concat(
                [
                    self.source_frame.loc[:, SourceInstrumentColumns.T_ISIN],
                    self.target_df.loc[
                        :,
                        [
                            OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                            OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                        ],
                    ],
                ],
                axis=1,
            ),
            params=ParamsConcatAttributes(
                source_attributes=[
                    SourceInstrumentColumns.T_ISIN,
                    OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                    OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                ],
                target_attribute=TempColumns.TEMP_INST_UNIQUE_ID_CASH_EQUITY,
            ),
        )[
            TempColumns.TEMP_INST_UNIQUE_ID_CASH_EQUITY
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_UNIQUE_ID
        ] = MapConditional.process(
            source_frame=pd.concat([self.source_frame, self.target_df], axis=1),
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_UNIQUE_ID,
                cases=[
                    Case(
                        query="index == index",
                        attribute=TempColumns.TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED,
                    ),
                    Case(
                        query="`m_ISIN`.str.len() == 12",
                        attribute=TempColumns.TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED,
                    ),
                    Case(
                        query="`t_isin`.str.len() == 12",
                        attribute=TempColumns.TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED,
                    ),
                    Case(
                        query="`c_product`.str.lower().str.fullmatch('cash/spot', na=False) & `m_asset`.str.lower().str.fullmatch('equity shares', na=False)",
                        attribute=TempColumns.TEMP_INST_UNIQUE_ID_CASH_EQUITY,
                    ),
                ],
            ),
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_UNIQUE_ID
        ]

        self.target_df.loc[:, TempColumns.TEMP_PARENT_META_MODEL] = MetaModel.ORDER

        # Note From specs: Although this mechanism is not ‘strictly’ fallback,
        # it does help to differentiate SRP vs. Non SRP by setting to “true”
        # which will be useful when assessing any market data issues stemming
        # from Instrument Reference Data integrity
        self.target_df.loc[:, TempColumns.TEMP_IS_CREATED_THROUGH_FALLBACK] = True

        # Passing along SourceOrderColumns.CONTRACT_SIZE which is to be used downstream
        self.target_df.loc[:, SourceDealsColumns.CONTRACT_SIZE] = self.source_frame.loc[
            :, SourceDealsColumns.CONTRACT_SIZE
        ]

    def _buy_sell(self) -> pd.DataFrame:
        """
        :return: pd.Dataframe
        Populates BUY_SELL
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceDealsColumns.ACTION,
                target_attribute=OrderColumns.BUY_SELL,
                case_insensitive=True,
                value_map={"0": "1", "1": "2", "13": "1", "14": "2"},
            ),
            auditor=self.auditor,
        )

    def _data_source_name(self) -> None:
        self.target_df.loc[:, OrderColumns.DATA_SOURCE_NAME] = DATA_SOURCE_NAME

    def _date(self) -> None:
        """
        :return: None
        Populate DATE
        """
        self.target_df.loc[:, OrderColumns.DATE] = self.pre_process_df.loc[
            :, TempColumns.TEMP_DATE
        ]

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        pass

    def _execution_details_buy_sell_indicator(self) -> None:
        """
        :return: None
        Populates EXECUTION_DETAILS_BUY_SELL_INDICATOR
        """
        self.target_df.loc[
            :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_BUY_SELL_INDICATOR]

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        pass

    def _execution_details_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        pass

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        pass

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        pass

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        pass

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        pass

    def _execution_details_order_status(self) -> pd.DataFrame:
        """
        :return: pd.Dataframe
        Populates EXECUTION_DETAILS_ORDER_STATUS
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceDealsColumns.ACTION,
                target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                value_map={
                    "0": OrderStatus.FILL.value,
                    "1": OrderStatus.FILL.value,
                    "13": OrderStatus.CAME.value,
                    "14": OrderStatus.CAME.value,
                },
            ),
            auditor=self.auditor,
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        pass

    def _execution_details_outgoing_order_addl_info(self) -> None:
        """
        Populates EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO skipping columns which are not present.
        :return: target_df with EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO column
        """
        gateway = "Gateway - " + self.source_frame.loc[
            :, SourceDealsColumns.GATEWAY
        ].astype("string")

        price = "Price Gateway - " + self.source_frame.loc[
            :, SourceDealsColumns.PRICE_GATEWAY
        ].astype("string")

        login = "Login - " + self.source_frame.loc[:, SourceDealsColumns.LOGIN].astype(
            "string"
        )

        mt5_symbol = "mt5_symbol - " + self.source_frame.loc[
            :, SourceDealsColumns.SYMBOL
        ].astype("string")

        fv_adj = "FV_adj - " + self.source_frame.loc[
            :, SourceDealsColumns.FAIR_VALUE_ADJUSTMENT
        ].astype("string")

        source_frame = pd.concat([gateway, price, login, mt5_symbol, fv_adj], axis=1)

        return ConcatAttributes.process(
            source_frame=source_frame,
            params=ParamsConcatAttributes(
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                source_attributes=[
                    SourceDealsColumns.GATEWAY,
                    SourceDealsColumns.PRICE_GATEWAY,
                    SourceDealsColumns.LOGIN,
                    SourceDealsColumns.SYMBOL,
                    SourceDealsColumns.FAIR_VALUE_ADJUSTMENT,
                ],
                delimiter=Delimiters.COMMA_SPACE,
            ),
        )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        pass

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        pass

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_stop_price(self) -> pd.DataFrame:
        pass

    def _execution_details_trading_capacity(self) -> None:
        """
        :return: None
        Populates EXECUTION_DETAILS_TRADING_CAPACITY
        """
        self.target_df.loc[
            :, OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY
        ] = TradingCapacity.DEAL.value

    def _execution_details_validity_period(self) -> pd.DataFrame:
        pass

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _financing_type(self) -> pd.DataFrame:
        pass

    def _hierarchy(self) -> pd.DataFrame:
        pass

    def _id(self) -> None:
        """
        :return: None
        Populate ID
        """
        self.target_df.loc[:, OrderColumns.ID] = self.pre_process_df.loc[
            :, TempColumns.TEMP_ORDER_ID
        ]

    def _is_discretionary(self) -> pd.DataFrame:
        pass

    def _is_iceberg(self):
        pass

    def _is_repo(self):
        pass

    def _is_synthetic(self):
        pass

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        pass

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        pass

    def _jurisdiction_country(self) -> pd.DataFrame:
        pass

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        pass

    def _market_identifiers(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_instrument() and _market_identifiers_parties() have been
        called earlier"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> None:
        """
        :return: None
        Populates MARKET_IDENTIFIERS_INSTRUMENT
        Note: Although this field is not used in this flow by
        instrument_identifiers task downstream we need value in
        market_identifiers for the instrument_fullname so that it is
        displayed on IRIS on the Orders dashboard.
        """
        self.target_df.loc[
            :, OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_INST_ID_INSTRUMENT_FULL_NAME]
        self.target_df.loc[:, OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT] = (
            self.target_df[OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT]
            .dropna()
            .apply(
                lambda x: [
                    Identifier(
                        labelId=x,
                        path=INSTRUMENT_PATH,
                        type=IdentifierType.OBJECT,
                    ).dict()
                ]
            )
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """
        :return: None
        Populate MARKET_IDENTIFIERS_PARTIES
        """
        self.pre_process_df = populate_buyer_seller_decision_maker(
            df=self.pre_process_df
        )
        return GenericOrderPartyIdentifiers.process(
            source_frame=self.pre_process_df,
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                client_identifier=TempPartyIDs.CLIENT,
                counterparty_identifier=TempPartyIDs.EXECUTING_ENTITY,
                executing_entity_identifier=TempPartyIDs.EXECUTING_ENTITY,
                trader_identifier=TempPartyIDs.CLIENT,
                buyer_identifier=TempPartyIDs.CLIENT,
                seller_identifier=TempPartyIDs.EXECUTING_ENTITY,
                buyer_decision_maker_identifier=TempPartyIDs.BUYER_DECISION_MAKER,
                seller_decision_maker_identifier=TempPartyIDs.SELLER_DECISION_MAKER,
                execution_within_firm_identifier=TempPartyIDs.INVESTMENT_DEC_WITHIN_FIRM,
                investment_decision_within_firm_identifier=TempPartyIDs.INVESTMENT_DEC_WITHIN_FIRM,
                buy_sell_side_attribute=TempColumns.TEMP_BUY_SELL_INDICATOR,
                use_buy_mask_for_buyer_seller=True,
            ),
        )

    def _meta_model(self) -> None:
        self.target_df.loc[:, OrderColumns.META_MODEL] = MetaModel.ORDER_STATE

    def _order_class(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_aggregated_order_id_code(self) -> None:
        """
        :return: None
        Populates ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID
        """
        self.target_df.loc[
            :, OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID
        ] = self.source_frame.loc[:, SourceDealsColumns.POSITION_ID]

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        pass

    def _order_identifiers_internal_order_id_code(self) -> None:
        """
        :return: None
        Populates ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE
        """
        self.target_df.loc[
            :, OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE
        ] = self.source_frame.loc[:, SourceDealsColumns.EXTERNAL_ID]

    def _order_identifiers_order_id_code(self) -> None:
        """
        :return: None
        Populates ORDER_IDENTIFIERS_ORDER_ID_CODE
        """
        self.target_df.loc[
            :, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_ORDER_ID]

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        pass

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        return ConcatAttributes.process(
            source_frame=self.source_frame,
            params=ParamsConcatAttributes(
                source_attributes=[SourceDealsColumns.ORDER, SourceDealsColumns.DEAL],
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                delimiter=Delimiters.PIPE,
            ),
        )

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_initial_quantity(self) -> None:
        """
        :return: None
        Populates PRICE_FORMING_DATA_INITIAL_QUANTITY
        """
        self.target_df.loc[
            :, OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_QTY]

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price(self) -> None:
        """
        Populates PRICE_FORMING_DATA_PRICE from the SourceInstrumentColumns.C_BASE_CCY
        :return: None
        """
        self.target_df.loc[
            :, OrderColumns.PRICE_FORMING_DATA_PRICE
        ] = ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_price_attribute=SourceDealsColumns.PRICE,
                source_ccy_attribute=SourceInstrumentColumns.C_BASE_CCY,
                target_price_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                cast_to=CastTo.FLOAT,
            ),
        )[
            OrderColumns.PRICE_FORMING_DATA_PRICE
        ]

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        pass

    def _price_forming_data_remaining_quantity(self) -> None:
        """
        :return: None
        Populates PRICE_FORMING_DATA_REMAINING_QUANTITY
        """
        self.target_df.loc[
            :, OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
                cases=[Case(query="`ACTION`.isin(['0', '1'])", value=0)],
            ),
        )[
            OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY
        ]

    def _price_forming_data_traded_quantity(self) -> None:
        """
        :return: pd.DataFrame
        Populate PRICE_FORMING_DATA_TRADED_QUANTITY
        """

        self.target_df.loc[
            :, OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
        ] = MapConditional.process(
            source_frame=pd.concat(
                [self.source_frame, self.pre_process_df.loc[:, TempColumns.TEMP_QTY]],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                cases=[
                    Case(
                        query="`ACTION`.isin(['0', '1'])",
                        attribute=TempColumns.TEMP_QTY,
                    )
                ],
            ),
        )[
            OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
        ]

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        return ConcatAttributes.process(
            source_frame=self.source_frame,
            params=ParamsConcatAttributes(
                source_attributes=[SourceDealsColumns.ORDER, SourceDealsColumns.DEAL],
                target_attribute=OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO,
                delimiter=Delimiters.PIPE,
            ),
        )

    def _source_index(self) -> None:
        """
        :return: None
        Populates SOURCE_INDEX
        """
        self.target_df.loc[
            :, OrderColumns.SOURCE_INDEX
        ] = self.source_frame.index.values

    def _source_key(self) -> None:
        """
        :return: None
        Populates SOURCE_KEY
        """
        self.target_df.loc[:, OrderColumns.SOURCE_KEY] = os.getenv(SWARM_FILE_URL)

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_order_status_updated(self) -> None:
        """
        :return: None
        Populates TIMESTAMPS_ORDER_STATUS_UPDATED
        """
        self.target_df.loc[
            :, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_TIMESTAMP]

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """
        :return: pd.DataFrame
        Populates TIMESTAMPS_TRADING_DATE_TIME
        """
        frame_for_trading_date_time = pd.concat(
            [
                self.pre_process_df,
                self.source_frame.loc[:, [SourceDealsColumns.ACTION]],
            ],
            axis=1,
        )
        return MapConditional.process(
            source_frame=frame_for_trading_date_time,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
                cases=[
                    Case(
                        query="`ACTION`.isin(['0', '1'])",
                        attribute=TempColumns.TEMP_TIMESTAMP,
                    )
                ],
            ),
        )

    def _timestamps_validity_period(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        pass

    def _transaction_details_buy_sell_indicator(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_BUY_SELL_INDICATOR
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_BUY_SELL_INDICATOR]

    def _transaction_details_commission_amount(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_COMMISSION_AMOUNT
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_COMMISSION_AMOUNT
        ] = self.source_frame.loc[:, SourceDealsColumns.COMMISSION]

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        pass

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        pass

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        pass

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        pass

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_effect(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_POSITION_EFFECT
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_POSITION_EFFECT
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_POSITION_EFFECT,
                cases=[
                    Case(
                        query="`ENTRY` == '0'",
                        value=RTS22PositionEffectType.OPEN.value,
                    ),
                    Case(
                        query="`ENTRY` == '1'",
                        value=RTS22PositionEffectType.CLOSE.value,
                    ),
                ],
            ),
        )[
            OrderColumns.TRANSACTION_DETAILS_POSITION_EFFECT
        ]

    def _transaction_details_position_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_price(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_PRICE
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_PRICE
        ] = self.target_df.loc[:, OrderColumns.PRICE_FORMING_DATA_PRICE]

    def _transaction_details_price_average(self) -> pd.DataFrame:
        pass

    def _transaction_details_price_currency(self) -> None:
        """
        Map to field, in order of priority, that contains a valid currency:

        instruments.[c_quoted_ccy]
        instruments.[m_price_currency]
        instruments.[c_base_ccy]

        only take a value from the field where it is a currency (i.e., f it’s ‘NULL’ then don’t take it)
        major/minor currency conversion must be applied
        changes raised in ON-3395, ON-4232
        """

        # SPI-1582 --> only take a value from the field where it is a currency
        for col in [
            SourceInstrumentColumns.C_QUOTED_CCY,
            SourceInstrumentColumns.M_PRICE_CURRENCY,
            SourceInstrumentColumns.C_BASE_CCY,
        ]:
            self.source_frame[col] = (
                self.source_frame[col]
                .fillna("")
                .apply(lambda x: x if isinstance(x, str) and len(x) == 3 else None)
            )

        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
        ] = ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attributes=[
                    SourceInstrumentColumns.C_QUOTED_CCY,
                    SourceInstrumentColumns.M_PRICE_CURRENCY,
                    SourceInstrumentColumns.C_BASE_CCY,
                ],
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
            ),
        )[
            OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
        ]

    def _transaction_details_price_notation(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_PRICE_NOTATION
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION
        ] = PriceNotation.MONE.value

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        pass

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        pass

    def _transaction_details_quantity(self) -> None:
        """
        :return: pd.DataFrame
        Populate TRANSACTION_DETAILS_QUANTITY
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_QUANTITY
        ] = MapConditional.process(
            source_frame=pd.concat(
                [self.source_frame, self.pre_process_df.loc[:, TempColumns.TEMP_QTY]],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY,
                cases=[
                    Case(
                        query="`ACTION`.isin(['0', '1'])",
                        attribute=TempColumns.TEMP_QTY,
                    )
                ],
            ),
        )[
            OrderColumns.TRANSACTION_DETAILS_QUANTITY
        ]

    def _transaction_details_quantity_currency(self) -> None:
        """
        if instruments.[m_asset].lower() in (fx, currency):
            instruments.[c_base_ccy]
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY
        ] = ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceInstrumentColumns.C_BASE_CCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
            ),
        )[
            OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY
        ]

        m_asset_mask = (
            self.source_frame.loc[:, SourceInstrumentColumns.M_ASSET]
            .str.lower()
            .isin(["fx", "currency"])
        )
        self.target_df.loc[
            ~m_asset_mask, OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY
        ] = pd.NA

    def _transaction_details_quantity_notation(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_QUANTITY_NOTATION
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
                cases=[
                    Case(
                        query=query_m_asset_fx_currency,
                        value=QuantityNotation.MONE.value,
                    ),
                    Case(
                        query=query_m_asset_not_fx_currency,
                        value=QuantityNotation.UNIT.value,
                    ),
                ],
            ),
        )[
            OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION
        ]

    def _transaction_details_record_type(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_RECORD_TYPE
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE
        ] = OrderRecordType.CLIENT_SIDE.value

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        pass

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        pass

    def _transaction_details_trading_capacity(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_TRADING_DATE_TIME
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY
        ] = TradingCapacity.DEAL.value

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """
        :return: pd.Dataframe
        Populates TRANSACTION_DETAILS_TRADING_DATE_TIME
        """
        frame_for_trading_date_time = pd.concat(
            [
                self.pre_process_df,
                self.source_frame.loc[:, [SourceDealsColumns.ACTION]],
            ],
            axis=1,
        )
        return MapConditional.process(
            source_frame=frame_for_trading_date_time,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
                cases=[
                    Case(
                        query="`ACTION`.isin(['0', '1'])",
                        attribute=TempColumns.TEMP_TIMESTAMP,
                    )
                ],
            ),
        )

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_ultimate_venue(self) -> None:
        """
        Use instrument.[t_id_prim_exch_mic]
        note: if value isn't valid (i.e. not 4 characters then map to “XXXX”)

        if instrument.[t_id_prim_exch_mic] = “NULL” or “OOTC” then: 'XXXX'
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE
        ] = Venue.XXXX

        four_characters_mask = (
            self.source_frame.loc[
                :, SourceInstrumentColumns.T_ID_PRIM_EXCH_MIC
            ].str.len()
            == 4
        )
        not_ootc_mask = (
            ~self.source_frame.loc[:, SourceInstrumentColumns.T_ID_PRIM_EXCH_MIC]
            .str.upper()
            .str.fullmatch("OOTC", na=False)
        )
        combined_mask = four_characters_mask & not_ootc_mask

        if combined_mask.any():
            self.target_df.loc[
                combined_mask, OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE
            ] = self.source_frame.loc[
                combined_mask, SourceInstrumentColumns.T_ID_PRIM_EXCH_MIC
            ]

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        pass

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_venue(self) -> None:
        """
        Populates TRANSACTION_DETAILS_VENUE from TRANSACTION_DETAILS_ULTIMATE_VENUE
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_VENUE
        ] = self.target_df.loc[:, OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE]

    @staticmethod
    def populate_party_identifiers(df: pd.DataFrame) -> pd.DataFrame:
        """
        :param df: pd.Dataframe
        :return: pd.Dataframe
        Add required fields for party identifiers
        """
        # client
        df.loc[:, TempPartyIDs.CLIENT] = (
            PartyPrefix.ID + df.loc[:, SourceDealsColumns.LOGIN]
        )

        # executing entity
        df.loc[:, TempPartyIDs.EXECUTING_ENTITY] = GetTenantLEI.process(
            source_frame=df,
            params=ParamsGetTenantLEI(
                target_column_prefix=PartyPrefix.LEI,
                target_lei_column=TempPartyIDs.EXECUTING_ENTITY,
            ),
        )

        return df

    def get_temp_qty(self) -> pd.Series:
        """
        Auxiliary method; so it can be overwritten on Oanda overrides
        Divides SourceDealsColumns. VOLUME by SourceDealsColumns.CONTRACT_SIZE
        """
        return calculate_qty(
            df=self.source_frame,
            qty_attribute=SourceDealsColumns.CONTRACT_SIZE,
            volume_attribute=SourceDealsColumns.VOLUME,
        )

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""
