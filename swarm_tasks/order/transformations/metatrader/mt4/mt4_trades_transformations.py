import logging
import os

import pandas as pd
from prefect.engine.signals import SKIP
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.components.market.identifier import Identifier
from se_elastic_schema.static.market import IdentifierType
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import DeliveryType
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import RTS22PositionEffectType
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_elastic_schema.static.reference import InstrumentIdCodeType
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order.static import OrderType
from se_trades_tasks.order.static import Venue
from se_trades_tasks.order_and_tr.instrument.identifiers.identifiers import (
    InstrumentIdentifiers,
)
from se_trades_tasks.order_and_tr.static import (
    INSTRUMENT_PATH,
)
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.io.read.aws.df_from_s3_csv import DfFromS3Csv
from swarm_tasks.io.read.aws.df_from_s3_csv import Params as ParamsDfFromS3Csv
from swarm_tasks.order.transformations.metatrader.mt4.static import SourceTradesColumns
from swarm_tasks.order.transformations.metatrader.mt4.static import TempColumns
from swarm_tasks.order.transformations.metatrader.mt5.static import AssetClassIdentifier
from swarm_tasks.order.transformations.metatrader.mt5.static import (
    query_m_asset_fx_currency,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import (
    query_m_asset_not_fx_currency,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import (
    SourceInstrumentColumns,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import TempPartyIDs
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.frame.get_rows_by_condition import GetRowsByCondition
from swarm_tasks.transform.frame.get_rows_by_condition import (
    Params as ParamsGetRowsByCondition,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_static import MapStatic
from swarm_tasks.transform.map.map_static import Params as ParamsMapStatic
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.utilities.static import Delimiters
from swarm_tasks.utilities.static import MetaModel

logger_ = logging.getLogger(__name__)


class MT4TradesTransformations(AbstractOrderTransformations):
    def _pre_process(self):

        # Update source_frame with instrument data
        self._update_source_frame_with_instrument_data()

        # Update source_frame to add additional rows for closed trades
        self._add_closed_trades_df()

        # re-align index of pre_process_df and target_df as per source_frame
        self.pre_process_df = pd.DataFrame(index=self.source_frame.index)
        self.target_df = pd.DataFrame(index=self.source_frame.index)

        # columns for pre_process_df
        self.pre_process_df.loc[
            :, TempColumns.OPEN_TIMESTAMP_STR
        ] = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceTradesColumns.OPEN_TIME,
                target_attribute=TempColumns.OPEN_TIMESTAMP_STR,
                convert_to=ConvertTo.DATETIME.value,
            ),
        )[
            TempColumns.OPEN_TIMESTAMP_STR
        ]

        self.pre_process_df.loc[
            :, TempColumns.CLOSE_TIMESTAMP_STR
        ] = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceTradesColumns.CLOSE_TIME,
                target_attribute=TempColumns.CLOSE_TIMESTAMP_STR,
                convert_to=ConvertTo.DATETIME.value,
            ),
        )[
            TempColumns.CLOSE_TIMESTAMP_STR
        ]

        self.pre_process_df.loc[:, TempColumns.OPEN_DATE] = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceTradesColumns.OPEN_TIME,
                target_attribute=TempColumns.OPEN_DATE,
                convert_to=ConvertTo.DATE.value,
            ),
        )[TempColumns.OPEN_DATE]

        self.pre_process_df.loc[:, TempColumns.CLOSE_DATE] = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceTradesColumns.CLOSE_TIME,
                target_attribute=TempColumns.CLOSE_DATE,
                convert_to=ConvertTo.DATE.value,
            ),
        )[TempColumns.CLOSE_DATE]

        self.pre_process_df.loc[:, TempColumns.POSITION_TYPE] = self._position_type(
            df=self.source_frame
        )

        self.pre_process_df.loc[
            :, TempColumns.TIMESTAMP_ORDER_RECEIVED
        ] = MapConditional.process(
            source_frame=self.pre_process_df,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TIMESTAMP_ORDER_RECEIVED,
                cases=[
                    Case(
                        query=self.query_close_trades,
                        attribute=TempColumns.CLOSE_TIMESTAMP_STR,
                    ),
                    Case(
                        query=self.query_open_trades,
                        attribute=TempColumns.OPEN_TIMESTAMP_STR,
                    ),
                ],
            ),
        )[
            TempColumns.TIMESTAMP_ORDER_RECEIVED
        ]

        self.pre_process_df.loc[
            :, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE
        ] = ConcatAttributes.process(
            source_frame=self.source_frame,
            params=ParamsConcatAttributes(
                source_attributes=[
                    SourceTradesColumns.TICKET,
                    TempColumns.POSITION_TYPE,
                ],
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
                delimiter=Delimiters.PIPE,
            ),
        )[
            OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE
        ]

        self.pre_process_df.loc[:, SourceTradesColumns.LOGIN] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceTradesColumns.LOGIN,
                target_attribute=SourceTradesColumns.LOGIN,
            ),
            auditor=self.auditor,
        )[SourceTradesColumns.LOGIN]

        self.pre_process_df.loc[
            :, SourceTradesColumns.OANDA_USER_ID
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceTradesColumns.OANDA_USER_ID,
                target_attribute=SourceTradesColumns.OANDA_USER_ID,
            ),
            auditor=self.auditor,
        )[
            SourceTradesColumns.OANDA_USER_ID
        ]

        self.pre_process_df.loc[
            :, TempColumns.BUY_SELL_INDICATOR
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.BUY_SELL_INDICATOR,
                cases=[
                    Case(
                        query="(~(`__POSITION_TYPE__` == 'Close') & (`CMD`.isin(['0', '2', '4'])))",
                        value=BuySellIndicator.BUYI.value,
                    ),
                    Case(
                        query="(~(`__POSITION_TYPE__` == 'Close') & (`CMD`.isin(['1', '3', '5'])))",
                        value=BuySellIndicator.SELL.value,
                    ),
                    Case(
                        query="((`__POSITION_TYPE__` == 'Close') & (`CMD`.isin(['0', '2', '4'])))",
                        value=BuySellIndicator.SELL.value,
                    ),
                    Case(
                        query="((`__POSITION_TYPE__` == 'Close') & (`CMD`.isin(['1', '3', '5'])))",
                        value=BuySellIndicator.BUYI.value,
                    ),
                ],
            ),
        )[
            TempColumns.BUY_SELL_INDICATOR
        ]

        # add party identifier fields to pre_process_df
        self.pre_process_df = self.populate_party_identifiers(df=self.pre_process_df)

        self.query_c_product_cfd = (
            "`c_product`.str.fullmatch('cfd', case=False, na=False)"
        )
        self.query_c_product_not_cfd = (
            "~(`c_product`.str.fullmatch('cfd', case=False, na=False))"
        )
        self.pre_process_df.loc[
            :, TempColumns.TEMP_INST_ASSET_CLASS
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_INST_ASSET_CLASS,
                cases=[
                    Case(
                        query=self.query_c_product_cfd,
                        value="CFD",
                    ),
                    Case(
                        query=self.query_c_product_not_cfd,
                        value="SB",
                    ),
                ],
            ),
        )[
            TempColumns.TEMP_INST_ASSET_CLASS
        ]

        self.pre_process_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_FULL_NAME
        ] = ConcatAttributes.process(
            source_frame=pd.concat(
                [
                    self.source_frame,
                    self.pre_process_df.loc[:, [TempColumns.TEMP_INST_ASSET_CLASS]],
                ],
                axis=1,
            ),
            params=ParamsConcatAttributes(
                source_attributes=[
                    SourceInstrumentColumns.C_NAME,
                    TempColumns.TEMP_INST_ASSET_CLASS,
                ],
                delimiter="-",
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_FULL_NAME,
            ),
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_FULL_NAME
        ]

    def _post_process(self):
        # fields for parties_fallback downstream
        for col in [
            TempPartyIDs.EXECUTING_ENTITY,
            TempPartyIDs.CLIENT,
            TempPartyIDs.INVESTMENT_DEC_WITHIN_FIRM,
        ]:
            if col not in self.pre_process_df:
                self.pre_process_df.loc[:, col] = pd.NA

            self.target_df.loc[:, col] = self.pre_process_df.loc[:, col]

        # fields for instrument identifiers downstream
        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_EXT_EXCHANGE_SYMBOL
        ] = InstrumentIdentifiers.format_symbol(
            df=self.source_frame,
            src_col=SourceTradesColumns.SYMBOL,
            dest_col=TempColumns.TEMP_INST_ID_EXT_EXCHANGE_SYMBOL,
        )

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE,
                cases=[
                    Case(
                        query=self.query_c_product_cfd,
                        value="CD",
                    ),
                    Case(
                        query=self.query_c_product_not_cfd,
                        value="OT",
                    ),
                ],
            ),
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_TYPE
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_TYPE,
                cases=[
                    Case(
                        query="`c_product`.str.fullmatch('cfd', case=False, na=False)",
                        value="CFD",
                    )
                ],
            ),
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_TYPE
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceInstrumentColumns.M_TR_ASSET,
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS,
                end_index=2,
            ),
            auditor=self.auditor,
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_BASE_PRODUCT
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceInstrumentColumns.M_COMMODITY_BASE,
                target_attribute=TempColumns.TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_BASE_PRODUCT,
            ),
            auditor=self.auditor,
        )[
            TempColumns.TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_BASE_PRODUCT
        ]

        self.target_df.loc[
            :,
            TempColumns.TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_FURTHER_SUB_PRODUCT,
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceInstrumentColumns.M_COMMODITY_DETAILS,
                target_attribute=TempColumns.TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_FURTHER_SUB_PRODUCT,
            ),
            auditor=self.auditor,
        )[
            TempColumns.TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_FURTHER_SUB_PRODUCT
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_DELIVERY_TYPE
        ] = MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=TempColumns.TEMP_INST_ID_DELIVERY_TYPE,
                target_value=DeliveryType.CASH.value,
            ),
        )[
            TempColumns.TEMP_INST_ID_DELIVERY_TYPE
        ]

        self.target_df.loc[:, TempColumns.TEMP_INST_ID_PRICE_MULTIPLIER] = 1

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_ID_CODE_TYPE
        ] = MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_ID_CODE_TYPE,
                target_value=InstrumentIdCodeType.OTHR.value,
            ),
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_ID_CODE_TYPE
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_PRICE_NOTATION
        ] = MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=TempColumns.TEMP_INST_ID_PRICE_NOTATION,
                target_value=PriceNotation.MONE.value,
            ),
        )[
            TempColumns.TEMP_INST_ID_PRICE_NOTATION
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceInstrumentColumns.C_CFI,
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION,
            ),
            auditor=self.auditor,
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_FULL_NAME
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_INST_ID_INSTRUMENT_FULL_NAME]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID
        ] = MapConditional.process(
            source_frame=pd.concat([self.source_frame, self.target_df], axis=1),
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID,
                cases=[
                    Case(
                        query="`m_ISIN`.str.len() == 12",
                        attribute=SourceInstrumentColumns.M_ISIN,
                    ),
                    Case(
                        query="`t_isin`.str.len() == 12",
                        attribute=SourceInstrumentColumns.T_ISIN,
                    ),
                ],
            ),
        )[
            TempColumns.TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_UNDERLYING_INDEX_NAME
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_INST_ID_UNDERLYING_INDEX_NAME,
                cases=[
                    Case(
                        query="`c_CFI`.str.upper().str.startswith('JEI', na=False)",
                        attribute=SourceTradesColumns.SYMBOL,
                    )
                ],
            ),
        )[
            TempColumns.TEMP_INST_ID_UNDERLYING_INDEX_NAME
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ASSET_CLASS
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_INST_ASSET_CLASS]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED
        ] = ConcatAttributes.process(
            source_frame=pd.concat([self.target_df, self.source_frame], axis=1),
            params=ParamsConcatAttributes(
                source_attributes=[
                    OrderColumns.TRANSACTION_DETAILS_VENUE,
                    SourceInstrumentColumns.M_ISIN,
                    OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                    TempColumns.TEMP_INST_ASSET_CLASS,
                ],
                target_attribute=TempColumns.TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED,
            ),
        )[
            TempColumns.TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED
        ]

        self.target_df.loc[:, TempColumns.TEMP_ASSET_CLASS] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_ASSET_CLASS,
                cases=[
                    Case(
                        query=query_m_asset_fx_currency,
                        value=AssetClassIdentifier.FX,
                    ),
                    Case(query=query_m_asset_not_fx_currency, value=""),
                ],
            ),
        )[TempColumns.TEMP_ASSET_CLASS]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED
        ] = ConcatAttributes.process(
            source_frame=pd.concat([self.target_df, self.source_frame], axis=1),
            params=ParamsConcatAttributes(
                source_attributes=[
                    OrderColumns.TRANSACTION_DETAILS_VENUE,
                    SourceInstrumentColumns.T_ISIN,
                    OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                    TempColumns.TEMP_ASSET_CLASS,
                    TempColumns.TEMP_INST_ASSET_CLASS,
                ],
                target_attribute=TempColumns.TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED,
            ),
        )[
            TempColumns.TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED
        ] = ConcatAttributes.process(
            source_frame=self.target_df,
            params=ParamsConcatAttributes(
                source_attributes=[
                    OrderColumns.TRANSACTION_DETAILS_VENUE,
                    TempColumns.TEMP_INST_ID_EXT_EXCHANGE_SYMBOL,
                    TempColumns.TEMP_INST_ASSET_CLASS,
                ],
                target_attribute=TempColumns.TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED,
            ),
        )[
            TempColumns.TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_UNIQUE_ID
        ] = MapConditional.process(
            source_frame=pd.concat([self.source_frame, self.target_df], axis=1),
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_INST_ASSET_CLASS,
                cases=[
                    Case(
                        query="index == index",
                        attribute=TempColumns.TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED,
                    ),
                    Case(
                        query="`m_ISIN`.str.len() == 12",
                        attribute=TempColumns.TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED,
                    ),
                    Case(
                        query="`t_isin`.str.len() == 12",
                        attribute=TempColumns.TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED,
                    ),
                ],
            ),
        )[
            TempColumns.TEMP_INST_ASSET_CLASS
        ]

        # Note From specs: Although this mechanism is not ‘strictly’ fallback,
        # it does help to differentiate SRP vs. Non SRP by setting to “true”
        # which will be useful when assessing any market data issues stemming
        # from Instrument Reference Data integrity
        self.target_df.loc[:, TempColumns.TEMP_IS_CREATED_THROUGH_FALLBACK] = True

        self.target_df.loc[
            :, add_prefix(ModelPrefix.ORDER_STATE, TempColumns.TEMP_PARENT_META_MODEL)
        ] = MetaModel.ORDER

    def _buy_sell(self) -> None:
        """
        :return: None
        Populates BUY_SELL
        """
        self.target_df.loc[
            :, add_prefix(ModelPrefix.ORDER, OrderColumns.BUY_SELL)
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.BUY_SELL,
                cases=[
                    Case(
                        query="(~(`__POSITION_TYPE__` == 'Close') & (`CMD`.isin(['0', '2', '4'])))",
                        value="1",
                    ),
                    Case(
                        query="(~(`__POSITION_TYPE__` == 'Close') & (`CMD`.isin(['1', '3', '5'])))",
                        value="2",
                    ),
                    Case(
                        query="((`__POSITION_TYPE__` == 'Close') & (`CMD`.isin(['0', '2', '4'])))",
                        value="2",
                    ),
                    Case(
                        query="((`__POSITION_TYPE__` == 'Close') & (`CMD`.isin(['1', '3', '5'])))",
                        value="1",
                    ),
                ],
            ),
        )[
            OrderColumns.BUY_SELL
        ]

        self.target_df.loc[
            :, add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.BUY_SELL)
        ] = self.target_df.loc[:, add_prefix(ModelPrefix.ORDER, OrderColumns.BUY_SELL)]

    def _data_source_name(self) -> None:
        """Populate DATA_SOURCE_NAME with the static value 'MetaTrader4 - Trades'"""
        self.target_df.loc[:, OrderColumns.DATA_SOURCE_NAME] = pd.DataFrame(
            data="MetaTrader4 - Trades",
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> None:
        """
        :return: None
        Populate Date
        """
        self.target_df.loc[:, OrderColumns.DATE] = MapConditional.process(
            source_frame=pd.concat(
                [
                    self.source_frame,
                    self.pre_process_df.loc[
                        :, [TempColumns.CLOSE_DATE, TempColumns.OPEN_DATE]
                    ],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=OrderColumns.DATE,
                cases=[
                    Case(
                        query=self.query_close_trades,
                        attribute=TempColumns.CLOSE_DATE,
                    ),
                    Case(
                        query=self.query_open_trades,
                        attribute=TempColumns.OPEN_DATE,
                    ),
                ],
            ),
        )[OrderColumns.DATE]

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_buy_sell_indicator(self) -> None:
        """
        :return: None
        Populates EXECUTION_DETAILS_BUY_SELL_INDICATOR
        """
        self.target_df.loc[
            :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
        ] = self.pre_process_df.loc[:, TempColumns.BUY_SELL_INDICATOR]

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_order_status(self) -> None:
        """Populates _order.executionDetails.orderStatus and
        _orderState.executionDetails.orderStatus.
        This is populated with the static values NEWO and FILL respectively"""
        self.target_df.loc[
            :,
            add_prefix(ModelPrefix.ORDER, OrderColumns.EXECUTION_DETAILS_ORDER_STATUS),
        ] = OrderStatus.NEWO.value

        self.target_df.loc[
            :,
            add_prefix(
                ModelPrefix.ORDER_STATE, OrderColumns.EXECUTION_DETAILS_ORDER_STATUS
            ),
        ] = OrderStatus.FILL.value

    def _execution_details_order_type(self) -> None:
        """
        :return: None
        Populates EXECUTION_DETAILS_ORDER_TYPE
        """
        self.target_df.loc[
            :, OrderColumns.EXECUTION_DETAILS_ORDER_TYPE
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
                cases=[
                    Case(query="index == index", value=OrderType.MARKET),
                    Case(query="`CMD`.isin(['2', '3'])", value=OrderType.LIMIT),
                    Case(query="`CMD`.isin(['4', '5'])", value=OrderType.STOP),
                ],
            ),
        )[
            OrderColumns.EXECUTION_DETAILS_ORDER_TYPE
        ]

    def _execution_details_outgoing_order_addl_info(self) -> None:
        """
        :return: None
        Populates EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO
        """
        self.target_df.loc[
            :, OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO
        ] = (
            "Magic Number - "
            + self.source_frame.loc[:, SourceTradesColumns.MAGIC]
            + " SL - "
            + self.source_frame.loc[:, SourceTradesColumns.SL]
            + " TP - "
            + self.source_frame.loc[:, SourceTradesColumns.TP]
            + " Margin Rate - "
            + self.source_frame.loc[:, SourceTradesColumns.MARGIN_RATE]
        )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_stop_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_trading_capacity(self) -> None:
        """
        :return: None
        Populates EXECUTION_DETAILS_TRADING_CAPACITY
        """
        self.target_df.loc[
            :, OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY
        ] = TradingCapacity.DEAL.value

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _financing_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _hierarchy(self) -> pd.DataFrame:
        """Not Implemented"""

    def _id(self) -> None:
        """
        :return: None
        Populates ID
        """
        self.target_df.loc[
            :, add_prefix(ModelPrefix.ORDER, OrderColumns.ID)
        ] = self.pre_process_df.loc[:, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE]
        self.target_df.loc[
            :, add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID)
        ] = self.pre_process_df.loc[:, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE]

    def _is_discretionary(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_iceberg(self):
        """Not Implemented"""

    def _is_repo(self):
        """Not Implemented"""

    def _is_synthetic(self):
        """Not Implemented"""

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        """Not Implemented"""

    def _market_identifiers(self) -> None:
        """Populates marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_instrument() and _market_identifiers_parties() have been
        called earlier"""
        self.target_df.loc[
            :, OrderColumns.MARKET_IDENTIFIERS
        ] = MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )[
            OrderColumns.MARKET_IDENTIFIERS
        ]

    def _market_identifiers_instrument(self) -> None:
        """
        :return: None
        Populates MARKET_IDENTIFIERS_INSTRUMENT
        Note: Although this field is not used in this flow by
        instrument_identifiers task downstream we need value in
        market_identifiers for the instrument_fullname so that it is
        displayed on IRIS on the Orders dashboard.
        """
        self.target_df.loc[
            :, OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_INST_ID_INSTRUMENT_FULL_NAME]
        self.target_df.loc[:, OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT] = (
            self.target_df[OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT]
            .dropna()
            .apply(
                lambda x: [
                    Identifier(
                        labelId=x,
                        path=INSTRUMENT_PATH,
                        type=IdentifierType.OBJECT,
                    ).dict()
                ]
            )
        )

    def _market_identifiers_parties(self) -> None:
        """
        :return: None
        Populate Party identifiers
        """
        return GenericOrderPartyIdentifiers.process(
            source_frame=self.pre_process_df,
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                client_identifier=TempPartyIDs.CLIENT,
                counterparty_identifier=TempPartyIDs.EXECUTING_ENTITY,
                executing_entity_identifier=TempPartyIDs.EXECUTING_ENTITY,
                buyer_identifier=TempPartyIDs.CLIENT,
                seller_identifier=TempPartyIDs.EXECUTING_ENTITY,
                seller_decision_maker_identifier=TempPartyIDs.CLIENT,
                execution_within_firm_identifier=TempPartyIDs.INVESTMENT_DEC_WITHIN_FIRM,
                investment_decision_within_firm_identifier=TempPartyIDs.INVESTMENT_DEC_WITHIN_FIRM,
                buy_sell_side_attribute=TempColumns.BUY_SELL_INDICATOR,
                use_buy_mask_for_buyer_seller=True,
                use_buy_mask_for_buyer_seller_decision_maker=True,
            ),
        )

    def _meta_model(self) -> None:
        """Populates _order.__meta_model__ and _orderState.__meta_model__.
        The columns are populated with the static values Order and OrderState respectively"""
        self.target_df.loc[
            :, add_prefix(ModelPrefix.ORDER, OrderColumns.META_MODEL)
        ] = MetaModel.ORDER

        self.target_df.loc[
            :, add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.META_MODEL)
        ] = MetaModel.ORDER_STATE

    def _order_class(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_order_id_code(self) -> None:
        """
        :return: None
        Populates ORDER_IDENTIFIERS_ORDER_ID_CODE
        """
        self.target_df.loc[
            :, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE
        ] = self.pre_process_df.loc[:, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE]

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_transaction_ref_no(self) -> None:
        """
        :return: None
        Populates ORDER_IDENTIFIERS_TRANSACTION_REF_NO
        """
        self.target_df.loc[
            :,
            add_prefix(
                ModelPrefix.ORDER_STATE,
                OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
            ),
        ] = self.pre_process_df.loc[:, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE]

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_initial_quantity(self) -> None:
        """
        :return: None
        Populates PRICE_FORMING_DATA_INITIAL_QUANTITY
        """
        self.target_df.loc[
            :, OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY
        ] = self.source_frame.loc[:, SourceTradesColumns.VOLUME]

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price(self) -> None:
        """
        :return: None
        Populates PRICE_FORMING_DATA_PRICE
        """
        self.target_df.loc[
            :, OrderColumns.PRICE_FORMING_DATA_PRICE
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                cases=[
                    Case(
                        query=self.query_close_trades,
                        attribute=SourceTradesColumns.CLOSE_PRICE,
                    ),
                    Case(
                        query=self.query_open_trades,
                        attribute=SourceTradesColumns.OPEN_PRICE,
                    ),
                ],
            ),
        )[
            OrderColumns.PRICE_FORMING_DATA_PRICE
        ]

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_traded_quantity(self) -> None:
        """
        :return: None
        Populates PRICE_FORMING_DATA_TRADED_QUANTITY
        """
        self.target_df.loc[
            :,
            add_prefix(
                ModelPrefix.ORDER_STATE, OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
            ),
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceTradesColumns.VOLUME,
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                ),
            ),
            auditor=self.auditor,
        )[
            add_prefix(
                ModelPrefix.ORDER_STATE, OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
            )
        ]

    def _report_details_transaction_ref_no(self) -> None:
        """
        :return: None
        Populates REPORT_DETAILS_TRANSACTION_REF_NO
        """
        self.target_df.loc[
            :,
            add_prefix(
                ModelPrefix.ORDER_STATE, OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO
            ),
        ] = self.pre_process_df.loc[:, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE]

    def _source_index(self) -> None:
        """Populates SOURCE_INDEX"""
        self.target_df.loc[
            :, OrderColumns.SOURCE_INDEX
        ] = self.source_frame.index.values

    def _source_key(self) -> None:
        """Populate sourceKey with SWARM_FILE_URL"""
        self.target_df.loc[:, OrderColumns.SOURCE_KEY] = os.getenv("SWARM_FILE_URL")

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_order_received(self) -> None:
        """
        :return: None
        Populates TIMESTAMPS_ORDER_RECEIVED
        """
        self.target_df.loc[
            :, OrderColumns.TIMESTAMPS_ORDER_RECEIVED
        ] = self.pre_process_df.loc[:, TempColumns.TIMESTAMP_ORDER_RECEIVED]

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_order_submitted(self) -> None:
        """
        :return: None
        Populates TIMESTAMPS_ORDER_SUBMITTED
        """
        self.target_df.loc[
            :, OrderColumns.TIMESTAMPS_ORDER_SUBMITTED
        ] = self.pre_process_df.loc[:, TempColumns.TIMESTAMP_ORDER_RECEIVED]

    def _timestamps_trading_date_time(self) -> None:
        """
        :return: None
        Populates TIMESTAMPS_TRADING_DATE_TIME
        """
        self.target_df.loc[
            :, OrderColumns.TIMESTAMPS_TRADING_DATE_TIME
        ] = self.pre_process_df.loc[:, TempColumns.TIMESTAMP_ORDER_RECEIVED]

    def _timestamps_validity_period(self) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_buy_sell_indicator(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_BUY_SELL_INDICATOR
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
        ] = self.pre_process_df.loc[:, TempColumns.BUY_SELL_INDICATOR]

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_effect(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_POSITION_EFFECT
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_POSITION_EFFECT
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_POSITION_EFFECT,
                cases=[
                    Case(
                        query=self.query_close_trades,
                        value=RTS22PositionEffectType.CLOSE.value,
                    ),
                    Case(
                        query=self.query_open_trades,
                        value=RTS22PositionEffectType.OPEN.value,
                    ),
                ],
            ),
        )[
            OrderColumns.TRANSACTION_DETAILS_POSITION_EFFECT
        ]

    def _transaction_details_position_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_PRICE
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_PRICE
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE,
                cases=[
                    Case(
                        query=self.query_close_trades,
                        attribute=SourceTradesColumns.CLOSE_PRICE,
                    ),
                    Case(
                        query=self.query_open_trades,
                        attribute=SourceTradesColumns.OPEN_PRICE,
                    ),
                ],
            ),
        )[
            OrderColumns.TRANSACTION_DETAILS_PRICE
        ]

    def _transaction_details_price_average(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price_currency(self) -> None:
        """
        :return: Populates TRANSACTION_DETAILS_PRICE_CURRENCY
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
        ] = ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceInstrumentColumns.C_BASE_CCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
            ),
        )[
            OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
        ]

    def _transaction_details_price_notation(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_PRICE_NOTATION
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION
        ] = PriceNotation.MONE.value

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_quantity(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_QUANTITY
        """
        self.target_df.loc[
            :,
            add_prefix(
                ModelPrefix.ORDER_STATE, OrderColumns.TRANSACTION_DETAILS_QUANTITY
            ),
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceTradesColumns.VOLUME,
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY,
                ),
            ),
            auditor=self.auditor,
        )[
            add_prefix(
                ModelPrefix.ORDER_STATE, OrderColumns.TRANSACTION_DETAILS_QUANTITY
            )
        ]

    def _transaction_details_quantity_currency(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_QUANTITY_CURRENCY
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY
        ] = ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceInstrumentColumns.C_QUOTED_CCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
            ),
        )[
            OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY
        ]

    def _transaction_details_quantity_notation(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_QUANTITY_NOTATION
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
                cases=[
                    Case(
                        query="`m_asset`.str.lower().isin(['fx', 'currency'])",
                        value=QuantityNotation.MONE.value,
                    ),
                    Case(
                        query="~(`m_asset`.str.lower().isin(['fx', 'currency']))",
                        value=QuantityNotation.UNIT.value,
                    ),
                ],
            ),
        )[
            OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION
        ]

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_trading_capacity(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_TRADING_CAPACITY
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY
        ] = TradingCapacity.DEAL.value

    def _transaction_details_trading_date_time(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_TRADING_DATE_TIME
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME
        ] = self.pre_process_df.loc[:, TempColumns.TIMESTAMP_ORDER_RECEIVED]

    def _transaction_details_trail_id(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_TRAIL_ID
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_TRAIL_ID
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceTradesColumns.TICKET,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_TRAIL_ID,
            ),
            auditor=self.auditor,
        )[
            OrderColumns.TRANSACTION_DETAILS_TRAIL_ID
        ]

    def _transaction_details_ultimate_venue(self) -> None:
        """
        :return: None
        Populate TRANSACTION_DETAILS_ULTIMATE_VENUE
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE
        ] = Venue.XXXX

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_venue(self) -> None:
        """
        :return: None
        Populate TRANSACTION_DETAILS_VENUE
        """
        self.target_df.loc[:, OrderColumns.TRANSACTION_DETAILS_VENUE] = Venue.XXXX

    @staticmethod
    def _position_type(df: pd.DataFrame) -> pd.Series:
        """
        :param df: pd.DataFrame
        :return: pd.Series
        If CLOSE_TIMESTAMP is populated and CLOSE_TIMESTAMP > OPEN_TIMESTAMP:
            POSITION_TYPE = Close
        Else:
            POSITION_TYPE = Open
        """
        df.loc[:, TempColumns.OPEN_TIMESTAMP] = pd.to_datetime(
            df.loc[:, TempColumns.OPEN_TIMESTAMP_STR]
        )
        df.loc[:, TempColumns.CLOSE_TIMESTAMP] = pd.to_datetime(
            df.loc[:, TempColumns.CLOSE_TIMESTAMP_STR]
        )

        close_mask = (
            (df[TempColumns.CLOSE_TIMESTAMP].notnull())
            & (df[TempColumns.OPEN_TIMESTAMP].notnull())
            & (df[TempColumns.CLOSE_TIMESTAMP] > df[TempColumns.OPEN_TIMESTAMP])
        )

        df.loc[
            close_mask, TempColumns.POSITION_TYPE
        ] = RTS22PositionEffectType.CLOSE.value
        df.loc[
            ~close_mask, TempColumns.POSITION_TYPE
        ] = RTS22PositionEffectType.OPEN.value

        return df[TempColumns.POSITION_TYPE]

    @staticmethod
    def populate_party_identifiers(df: pd.DataFrame) -> pd.DataFrame:
        """
        :param df: pd.Dataframe
        :return: pd.Dataframe
        Add required fields for party identifiers
        """
        # client
        df.loc[:, TempPartyIDs.CLIENT] = (
            PartyPrefix.ID + df.loc[:, SourceTradesColumns.LOGIN]
        )

        return df

    def _add_closed_trades_df(self) -> None:
        """
        :return: None
        Updates source_frame by vertically concatenating closed trades as
        we need to create additional NEWO and FILL for closed trades
        """
        temp_df = self.source_frame.copy()
        temp_df.loc[:, TempColumns.OPEN_TIMESTAMP_STR] = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceTradesColumns.OPEN_TIME,
                target_attribute=TempColumns.OPEN_TIMESTAMP_STR,
                convert_to=ConvertTo.DATETIME.value,
            ),
        )[TempColumns.OPEN_TIMESTAMP_STR]

        temp_df.loc[:, TempColumns.CLOSE_TIMESTAMP_STR] = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceTradesColumns.CLOSE_TIME,
                target_attribute=TempColumns.CLOSE_TIMESTAMP_STR,
                convert_to=ConvertTo.DATETIME.value,
            ),
        )[TempColumns.CLOSE_TIMESTAMP_STR]

        temp_df.loc[:, TempColumns.POSITION_TYPE] = self._position_type(df=temp_df)

        self.query_close_trades = "`__POSITION_TYPE__` == 'Close'"
        self.query_open_trades = "~(`__POSITION_TYPE__` == 'Close')"
        close_trades_df = GetRowsByCondition.process(
            source_frame=temp_df,
            params=ParamsGetRowsByCondition(query=self.query_close_trades),
        )

        self.source_frame = pd.concat([self.source_frame, close_trades_df], axis=0)

        self.source_frame = self.source_frame.reset_index()

    def _update_source_frame_with_instrument_data(self) -> None:
        """
        :return: None
        Reads instrument data from a static s3 file as a dataframe and merges
        the dataframe with source_frame.
        If instrument not found then skip such rows and if instruments are not
        found for any rows then raise SKIP.
        """
        # instrument_data from s3 file
        instrument_df = DfFromS3Csv.process(
            params=ParamsDfFromS3Csv(
                s3_key="resource/metatrader/mt5/instrument/InstrumentData.csv"
            ),
        )
        # check for required columns in instrument_data df
        for col in SourceInstrumentColumns().get_columns():
            if col not in instrument_df.columns:
                instrument_df.loc[:, col] = pd.NA

        # merge source_frame with instrument data
        self.source_frame = self.source_frame.merge(
            instrument_df.drop_duplicates(subset=[SourceInstrumentColumns.C_MT5_ID]),
            how="left",
            left_on=SourceTradesColumns.SYMBOL,
            right_on=SourceInstrumentColumns.C_MT5_ID,
        )

        # remove rows where instrument not found
        instrument_na_mask = self.source_frame[
            SourceInstrumentColumns.C_MT5_ID
        ].isnull()

        # Raise SKIP if instruments not found for all rows
        if instrument_na_mask.all():
            msg = "No rows can be linked to instruments. So skipping all rows."
            logger_.error(msg)
            self.auditor.add(msg)
            raise SKIP(msg)

        # Instruments not found for some rows
        if instrument_na_mask.any():
            instrument_na_symbol_list = self.source_frame.loc[
                instrument_na_mask, SourceTradesColumns.SYMBOL
            ].tolist()
            instrument_na_indices = self.source_frame.loc[
                instrument_na_mask
            ].index.tolist()
            msg = (
                f"[!] Skipping rows where instruments not found...\n"
                f"[!] Count of rows skipped: "
                f"[!] {len(self.source_frame.loc[instrument_na_mask])}.\n"
                f"[!] Source Symbols: {set(instrument_na_symbol_list)}.\n"
                f"[!] Source Indices: {instrument_na_indices}"
            )
            self.auditor.add(msg)
            logger_.warning(msg)
            self.source_frame = self.source_frame.loc[~instrument_na_mask]

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""
