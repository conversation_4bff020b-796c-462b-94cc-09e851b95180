import logging
import os

import pandas as pd
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.order.transformations.metatrader.mt4.mt4_trades_transformations import (
    MT4TradesTransformations,
)
from swarm_tasks.order.transformations.metatrader.mt4.static import SourceTradesColumns
from swarm_tasks.order.transformations.metatrader.mt5.static import TempPartyIDs

logger_ = logging.getLogger(__name__)


class MT4OandaTradesTransformations(MT4TradesTransformations):
    def populate_party_identifiers(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        :param df: pd.Dataframe
        :return: pd.Dataframe
        Overrides populate_party_identifiers() method from
        MT4OrderTransformations to add EXECUTING_ENTITY
        """
        df.loc[:, TempPartyIDs.CLIENT] = (
            PartyPrefix.ID + df.loc[:, SourceTradesColumns.OANDA_USER_ID]
        )
        exec_entity = self.executing_entity()
        df.loc[:, TempPartyIDs.EXECUTING_ENTITY] = f"{PartyPrefix.ID}{exec_entity}"
        return df

    def executing_entity(self) -> str:
        """
        :return: str
        Take the first subfield of the input file name delimieted by '-'
        (i.e., mt5-ogm-deals-2022-02-14.csv will be “ogm”)
        """
        source_file_name = os.getenv("SWARM_FILE_URL").split("/")[-1]
        first_name_sub_field = source_file_name.split("-")
        if len(first_name_sub_field) > 2:
            return first_name_sub_field[1]
        else:
            logger_.warning(
                f"Unable to fetch executing_entity from file_name. "
                f"Filename:{source_file_name}"
            )
        return ""
