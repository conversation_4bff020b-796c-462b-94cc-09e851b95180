import pandas as pd
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_trades_tasks.order_and_tr.party.utils import add_id_or_lei

from swarm_tasks.order.feed.enfusion.v2.static import SourceColumns
from swarm_tasks.order.feed.enfusion.v2.static import TempColumns
from swarm_tasks.order.transformations.enfusion.v2.enfusion_v2_order_transformations import (
    EnfusionV2OrderTransformations,
)
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional

MULTI = "MULTI"


class SellarondaEnfusionV2OrderTransformations(EnfusionV2OrderTransformations):
    """
    Sellaronda-specific override for Enfusion V2 Order Transformations.
    
    Override for Sellaronda Only:
    - For BUY orders: if route.[DestinationDisplayName] = "FILM" then use route.[Counterpartycode] for counterparty
    - Seller = counterparty
    """

    def _temp_party_ids_seller(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers with Sellaronda override
        
        Populates, in order of hierarchy from:
        if SELL:
            BUYER_IDENTIFICATION_CODE
        if BUY:
            For Sellaronda: if DESTINATION_DISPLAY_NAME = "FILM" then use COUNTERPARTY_CODE
            Otherwise: SourceColumns.COUNTERPARTY if allocation file else SourceColumns.ORDER_EXECUTION_DESTINATION
            SourceColumns.BUYER_IDENTIFICATION_CODE
            SourceColumns.BUYER_DECISION_MAKER_CODE
        Adds id/lei prefix to the column
        In aggregation this field should be filled with 'MULTI' in BUYI records
        """
        if self.file_type == self.FileTypes.ALLOCATIONS:
            file_type_referenced_column = SourceColumns.COUNTERPARTY
        else:
            file_type_referenced_column = SourceColumns.ORDER_EXECUTION_DESTINATION

        buy_condition = (
            f"`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.BUYI.value}'"
        )
        sell_condition = (
            f"`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.SELL.value}'"
        )

        first_case_field = self.get_buyer_seller_first_case_column()

        # Sellaronda-specific condition for BUY orders with FILM destination
        sellaronda_film_condition = (
            f"{buy_condition} & "
            f"`{SourceColumns.DESTINATION_DISPLAY_NAME}` == 'FILM'"
        )

        seller = MapConditional.process(
            source_frame=pd.concat([self.source_frame, self.pre_process_df], axis=1),
            params=ParamsMapConditional(
                target_attribute=TempColumns.SELLER,
                cases=[
                    Case(
                        query=sell_condition,
                        attribute=first_case_field,
                    ),
                    # Sellaronda override: For BUY orders with FILM destination, use COUNTERPARTY_CODE
                    Case(
                        query=sellaronda_film_condition,
                        attribute=SourceColumns.COUNTERPARTY_CODE,
                    ),
                    Case(
                        query=buy_condition,
                        attribute=file_type_referenced_column,
                    ),
                    Case(
                        query=f"{buy_condition} & `{file_type_referenced_column}`.isnull()",
                        attribute=SourceColumns.BUYER_IDENTIFICATION_CODE,
                    ),
                    Case(
                        query=f"{buy_condition} & `{file_type_referenced_column}`.isnull() & "
                        f"`{SourceColumns.BUYER_IDENTIFICATION_CODE}`.isnull()",
                        attribute=SourceColumns.BUYER_DECISION_MAKER_CODE,
                    ),
                ],
            ),
        )
        seller = add_id_or_lei(seller, TempColumns.SELLER)
        buy_aggregated_mask = (
            self.pre_process_df[TempColumns.AGGREGATION_CODE].notnull()
            & self.pre_process_df[TempColumns.BUY_SELL_INDICATOR]
            == BuySellIndicator.BUYI
        )
        seller.loc[buy_aggregated_mask, TempColumns.SELLER] = MULTI
        return seller

    def _temp_party_ids_counterparty_and_client(self) -> pd.DataFrame:
        """
        Temporary Columns for PartyIdentifiers with Sellaronda override
        
        Counterparty is populated from TempColumn.BUYER or TempColumns.SELLER.
        For Sellaronda BUY orders with FILM destination, counterparty uses COUNTERPARTY_CODE.
        Client is populated from either LE Name or the Counterparty
        Adds id/lei prefix to the column
        In aggregated records this field should be filled with 'MULTI'
        """
        # Sellaronda-specific condition for BUY orders with FILM destination
        sellaronda_film_condition = (
            f"`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.BUYI.value}' & "
            f"`{SourceColumns.DESTINATION_DISPLAY_NAME}` == 'FILM'"
        )

        result_df = MapConditional.process(
            source_frame=pd.concat([self.source_frame, self.pre_process_df], axis=1),
            params=ParamsMapConditional(
                target_attribute=TempColumns.COUNTERPARTY,
                cases=[
                    Case(
                        query=f"`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.BUYI.value}'",
                        attribute=TempColumns.SELLER,
                    ),
                    Case(
                        query=f"`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.SELL.value}'",
                        attribute=TempColumns.BUYER,
                    ),
                    # Sellaronda override: For BUY orders with FILM destination, use COUNTERPARTY_CODE directly
                    Case(
                        query=sellaronda_film_condition,
                        attribute=SourceColumns.COUNTERPARTY_CODE,
                    ),
                    Case(
                        query=f"`{TempColumns.AGGREGATION_CODE}`.notnull()",
                        value=MULTI,
                    ),
                ],
            ),
        )
        
        # Add id/lei prefix to counterparty code for Sellaronda FILM cases
        result_df = add_id_or_lei(result_df, TempColumns.COUNTERPARTY)
        
        client_df = add_id_or_lei(
            dataframe=self.source_frame.loc[:, [SourceColumns.LE_NAME]],
            source_attribute=SourceColumns.LE_NAME,
        )
        result_df[TempColumns.CLIENT] = client_df.loc[:, SourceColumns.LE_NAME].fillna(
            result_df[TempColumns.COUNTERPARTY]
        )
        return result_df
