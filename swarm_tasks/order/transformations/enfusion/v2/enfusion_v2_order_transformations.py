import os
from pathlib import Path

import pandas as pd
import pycountry
from prefect.engine import signals
from se_core_tasks.map.map_conditional import Case
from se_core_tasks.map.map_value import RegexReplaceMap
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import Hiera<PERSON><PERSON>num
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import ShortSellingIndicator
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_elastic_schema.static.reference import CommissionAmountType
from se_elastic_schema.static.reference import OrderRecordType
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import FUTURES_CONTRACT_REGEX
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order.static import OrderType
from se_trades_tasks.order.static import Venue
from se_trades_tasks.order_and_tr.party.utils import add_id_or_lei
from se_trades_tasks.order_and_tr.static import AssetClass

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.order.feed.enfusion.v2.static import AlloMapKeys
from swarm_tasks.order.feed.enfusion.v2.static import EventTypes
from swarm_tasks.order.feed.enfusion.v2.static import ExecMapKeys
from swarm_tasks.order.feed.enfusion.v2.static import FileTypes
from swarm_tasks.order.feed.enfusion.v2.static import RecordType
from swarm_tasks.order.feed.enfusion.v2.static import SourceColumns
from swarm_tasks.order.feed.enfusion.v2.static import TempColumns
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.datetime.join_date_and_time import JoinDateAndTimeFormat
from swarm_tasks.transform.datetime.join_date_and_time import (
    Params as ParamsJoinDateAndTimeFormat,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_static import MapStatic
from swarm_tasks.transform.map.map_static import Params as ParamsMapStatic
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)
from swarm_tasks.utilities.task_utils import match_enum_value

SYNTH_PARENT_PREFIX = "__synth_parent"
MULTI = "MULTI"


class EnfusionV2OrderTransformations(AbstractOrderTransformations):
    """Transformations class for Enfusion with Executions Orders. 2 types of files
    are handled:  Allocations (NEWO) and Executions (PARF) files.
    Fields are created for Order and OrderState models.
    """

    def __init__(self, source_frame: pd.DataFrame, auditor, **kwargs):
        super().__init__(source_frame=source_frame, auditor=auditor)
        self.file_name = Path(os.getenv("SWARM_FILE_URL")).name
        self.file_type = match_enum_value(self.file_name, FileTypes)
        if not self.file_type:
            raise signals.FAIL(
                message="This flow cannot handle this file type. It can only handle"
                " Allocations files (which have 'allocations' in the file name) and "
                "Executions files (which have 'executions' in the file name)"
            )
        self.fill_by_fill_map: dict = kwargs.get("fill_by_fill_map", {})

        # fill_by_fill_flag=False is not allowed in Allocation files
        self.fill_by_fill_flag: bool = (
            self.file_type == FileTypes.ALLOCATIONS or not bool(self.fill_by_fill_map)
        )
        self.allocation_aggregation_map: dict = kwargs.get(
            "allocation_aggregation_map", {}
        )

        # allocation_aggregation_flag=True is not allowed in Execution files
        self.allocation_aggregation_flag: bool = (
            self.file_type == FileTypes.ALLOCATIONS
            and bool(self.allocation_aggregation_map)
        )

    def _pre_process(self):
        """
        Temporary columns needed for multiple fields
        """
        self.source_frame[TempColumns.AGGREGATION_CODE] = self._temp_aggregation_code()
        self.pre_process_df[TempColumns.AGGREGATION_CODE] = self.source_frame[
            TempColumns.AGGREGATION_CODE
        ]
        if self.allocation_aggregation_flag:
            self._aggregate_allocation_records()
            self.pre_process_df = pd.concat(
                [
                    self.pre_process_df,
                    self._temp_aggregated_quantity(),
                    self._temp_aggregated_price(),
                ],
                axis=1,
            )

        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_cancel_newo_indicator(),
                self._temp_currency(),
                self._temp_currency_2(),
                self._temp_quantity_currency(),
                self._temp_description_currencies(),
                self._temp_execution_datetime(),
                self._temp_expiry_date(),
                self._temp_future_expiration_date(),
                self._temp_initial_quantity(),
                self._temp_last_fill_time(),
                self._temp_last_px(),
                self._temp_newo_in_file(),
                self._temp_notional_currency_2(),
                self._temp_option_expiration_date(),
                self._temp_order_date_order_timestamp(),
                self._temp_orderside_or_transactiontype(),
                self._temp_price(),
                self._temp_price_currency(),
                self._temp_quantity_type(),
                self._temp_short_sell_indicator(),
                self._temp_trading_date_time(),
                self._temp_validity_period(),
                self._temp_executing_entity(),
                self._temp_party_ids_execution_within_firm(),
                self._temp_party_ids_trader(),
                self._temp_instr_ids_asset_class(),
                self._temp_instr_ids_bbgid(),
                self._temp_instr_ids_isin(),
                self._temp_instr_ids_option_type(),
                self._temp_instr_ids_options_strike_price(),
                self._temp_instr_ids_underlying_isin(),
                self._temp_instr_ids_venue(),
                self._temp_underlying_symbol_expiry_code(),
            ],
            axis=1,
        )

        # these columns need access to others defined above
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_buy_sell(),  # needs orderside_or_transactiontype
                self._temp_party_ids_executing_entity(),  # need temp_executing_entity
                self._temp_party_ids_investment_decision_maker(),  # needs trader for thornbridge override
                self._temp_instr_ids_underlying_symbol(),  # needs asset_class
                self._temp_description_date(),  # needs asset_class
                self._temp_instr_ids_currency(),  # needs currency, price currency and description_currency1
                self._temp_instr_ids_notional_currency_2(),
                # needs currency_2, notional_currency_2 and description_currency2
                self._temp_timestamps_order_received(),  # needs trading_date_time and order_date_order_timestamp
                self._temp_traded_quantity(),  # needs initial_quantity and aggregated_quantity
            ],
            axis=1,
        )

        # these columns need access to others defined above
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_buy_sell_indicator(),  # needs buy_sell
                self._temp_instr_ids_expiry_date(),  # needs asset class, future exp, option exp and description date
                self._temp_order_received_date(),  # needs order_received
            ],
            axis=1,
        )

        # these columns need access to others defined above
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_order_received_date_stripped(),  # needs order_received_date
            ],
            axis=1,
        )

        # these columns need access to others defined above
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_party_ids_buyer(),  # needs executing_entity
                self._temp_party_ids_seller(),  # needs executing_entity
                self._temp_trans_ref_number_order_date(),  # needs order_received_date_stripped
            ],
            axis=1,
        )

        # these columns need access to others defined above
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_party_ids_buyer_decision_maker(),  # needs buyer, needs executing_entity for thornbridge
                self._temp_party_ids_seller_decision_maker(),  # needs seller, needs executing_entity for thornbridge
                self._temp_party_ids_counterparty_and_client(),  # needs buy_sell_indicator, buyer and seller
            ],
            axis=1,
        )

    def _post_process(self):
        if self.file_type == FileTypes.ALLOCATIONS:
            self.target_df = pd.concat(
                [
                    self.target_df,
                    self.pre_process_df.loc[
                        :, [TempColumns.NEWO_IN_FILE, TempColumns.NEWO_CANCEL_INDICATOR]
                    ],
                ],
                axis=1,
            )

        # These temporary columns are needed for LinkInstruments (invoked on the bundle)
        # and will be discarded downstream on the Flow
        self.target_df = pd.concat(
            [self.target_df, self.pre_process_df.loc[:, TempColumns.ASSET_CLASS]],
            axis=1,
        )
        self.target_df = pd.concat(
            [
                self.target_df,
                self.pre_process_df.loc[:, TempColumns.INSTR_IDS_CURRENCY],
            ],
            axis=1,
        )
        self.target_df = pd.concat(
            [self.target_df, self.pre_process_df.loc[:, TempColumns.VENUE]],
            axis=1,
        )
        self.target_df.loc[:, TempColumns.INSTRUMENT_UNIQUE_IDENTIFIER] = (
            self.target_df.loc[:, OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT]
            .str.get(0)
            .str.get("labelId")
        )
        self.target_df.loc[:, TempColumns.INSTRUMENT_FULL_NAME] = self.source_frame.loc[
            :, SourceColumns.INSTRUMENT_FULL_NAME
        ].fillna(self.target_df.loc[:, TempColumns.INSTRUMENT_UNIQUE_IDENTIFIER])

        if self.file_type == FileTypes.ALLOCATIONS:
            xose_mask = self.source_frame[SourceColumns.EXCHANGE_MIC_CODE] == "XOSE"
            # For XOSE records, use BB_YELLOW as the instrument full name
            self.target_df.loc[
                xose_mask, TempColumns.INSTRUMENT_FULL_NAME
            ] = self.source_frame.loc[xose_mask, SourceColumns.BB_YELLOW]
        self.target_df.loc[:, TempColumns.PRICE_REFERENCE_RIC] = self.source_frame.loc[
            :, SourceColumns.RIC
        ]
        self.target_df.loc[
            :, TempColumns.INSTRUMENT_CLASSIFICATION
        ] = self.source_frame.loc[:, SourceColumns.INSTRUMENT_CLASSIFICATION]
        self.target_df.loc[:, TempColumns.CREATED_THROUGH_FALLBACK] = True

        if not self.fill_by_fill_flag:
            self.target_df = self._keep_a_single_parf_per_order_id()

        if self.allocation_aggregation_flag:
            self.target_df = self._fix_aggregation_values()
            self.target_df = self._drop_invalid_aggregated_records()

    def _temp_aggregation_code(self) -> pd.DataFrame:
        """
        Populates TempColumns.AGGREGATION_CODE for all records that will be aggregated.
        If self.allocation_aggregation=False this all values should be pd.NA. This column
        contains a reference for the self.allocation_aggregation_map.
        """
        if not self.allocation_aggregation_flag:
            return pd.NA

        # Create an unique ID
        temp_aggregation_code = (
            self.source_frame[SourceColumns.INSTRUMENT_IDENTIFICATION_CODE]
            .fillna(self.source_frame[SourceColumns.INSTRUMENT_FULL_NAME])
            .fillna(self.source_frame[SourceColumns.UNDERLYING_INSTRUMENT_CODE])
            .fillna(self.source_frame[SourceColumns.TICKER])
        )
        temp_aggregation_code.loc[temp_aggregation_code.notna()] = (
            temp_aggregation_code
            + self.source_frame[SourceColumns.TRADING_DATE_TIME].str[:10]
        )
        valid_codes = self.allocation_aggregation_map[
            AlloMapKeys.FIRST_TRANSACTION_REF_NUMBER
        ]
        temp_aggregation_code[~temp_aggregation_code.isin(valid_codes)] = pd.NA
        return temp_aggregation_code

    def _aggregate_allocation_records(self) -> pd.DataFrame:
        """
        This function aggregates allocation records, it drops all records but one, with
        the same aggregation code in TempColumns.AGGREGATION_CODE.
        """
        not_aggregated_df = self.source_frame[
            self.source_frame[TempColumns.AGGREGATION_CODE].isna()
        ]
        newo_alloc_df = self.source_frame.loc[
            (self.source_frame[TempColumns.ORDER_STATUS] == OrderStatus.NEWO)
            & (self.source_frame[TempColumns.AGGREGATION_CODE].notna())
        ]

        fill_alloc_df = self.source_frame.loc[
            (self.source_frame[TempColumns.ORDER_STATUS] == OrderStatus.FILL)
            & (self.source_frame[TempColumns.AGGREGATION_CODE].notna())
        ]

        # Agregate records: drop duplicated instruments
        newo_alloc_df = newo_alloc_df.drop_duplicates(
            subset=TempColumns.AGGREGATION_CODE, keep="first"
        )
        fill_alloc_df = fill_alloc_df.drop_duplicates(
            subset=TempColumns.AGGREGATION_CODE, keep="first"
        )
        self.source_frame = pd.concat([not_aggregated_df, newo_alloc_df, fill_alloc_df])
        index_to_keep = self.source_frame.index
        self.pre_process_df = self.pre_process_df.loc[index_to_keep]
        self.post_process_df = self.post_process_df.loc[index_to_keep]
        self.target_df = self.target_df.loc[index_to_keep]

    def _keep_a_single_parf_per_order_id(self):
        """
        This method drops PARFs from target_df and keeps only one by OrderId.
        """
        parf_df = self.target_df[
            self.target_df[OrderColumns.EXECUTION_DETAILS_ORDER_STATUS]
            == OrderStatus.PARF
        ]
        non_parf_df = self.target_df[
            self.target_df[OrderColumns.EXECUTION_DETAILS_ORDER_STATUS]
            != OrderStatus.PARF
        ]
        unique_parf_df = parf_df.drop_duplicates(subset=OrderColumns.ID, keep="first")
        return pd.concat([unique_parf_df, non_parf_df])

    def _fix_aggregation_values(self):
        """
        This function force an empty value for a list of columns in case the record was
        aggregated.
        """
        aggregated_mask = self.pre_process_df[TempColumns.AGGREGATION_CODE].notnull()
        # Columns that should be empty if aggregated
        empty_aggregated_cols = [
            OrderColumns.TRANSACTION_DETAILS_COMMISSION_AMOUNT,
            OrderColumns.TRANSACTION_DETAILS_COMMISSION_AMOUNT_CURRENCY,
            OrderColumns.TRANSACTION_DETAILS_COMMISSION_AMOUNT_TYPE,
            OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE,
            OrderColumns.TRANSACTION_DETAILS_BASKET_ID,
            OrderColumns.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID,
            OrderColumns.EXECUTION_DETAILS_SHORT_SELLING_INDICATOR,
            OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE,
            OrderColumns.EXECUTION_DETAILS_STOP_PRICE,
            OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
            OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE,
            OrderColumns.ORDER_IDENTIFIERS_PARENT_ORDER_ID,
            OrderColumns.HIERARCHY,
            OrderColumns.TRADERS_ALGOS_WAIVER_INDICATORS_SHORT_SELLING_INDICATOR,
            OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
        ]
        self.target_df.loc[aggregated_mask, empty_aggregated_cols] = pd.NA
        return self.target_df

    def _drop_invalid_aggregated_records(self):
        aggregated_zero_quantity_mask = (
            self.target_df[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                )
            ]
            == 0.0
        )
        return self.target_df.loc[~aggregated_zero_quantity_mask]

    def _buy_sell(self) -> pd.DataFrame:
        """
        Populates from TempColumns.BUY_SELL
        Adds '__synth_parent.buySell' for Executions
        Adds '.order' and '.orderState' prefixes for Allocations
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return pd.concat(
                [
                    MapAttribute.process(
                        source_frame=self.pre_process_df,
                        params=ParamsMapAttribute(
                            source_attribute=TempColumns.BUY_SELL,
                            target_attribute=add_prefix(
                                prefix=ModelPrefix.ORDER,
                                attribute=OrderColumns.BUY_SELL,
                            ),
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=self.pre_process_df,
                        params=ParamsMapAttribute(
                            source_attribute=TempColumns.BUY_SELL,
                            target_attribute=add_prefix(
                                prefix=ModelPrefix.ORDER_STATE,
                                attribute=OrderColumns.BUY_SELL,
                            ),
                        ),
                        auditor=self.auditor,
                    ),
                ],
                axis=1,
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            return pd.concat(
                [
                    MapAttribute.process(
                        source_frame=self.pre_process_df,
                        params=ParamsMapAttribute(
                            source_attribute=TempColumns.BUY_SELL,
                            target_attribute=OrderColumns.BUY_SELL,
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=self.pre_process_df,
                        params=ParamsMapAttribute(
                            source_attribute=TempColumns.BUY_SELL,
                            target_attribute=add_prefix(
                                prefix=SYNTH_PARENT_PREFIX,
                                attribute=OrderColumns.BUY_SELL,
                            ),
                        ),
                        auditor=self.auditor,
                    ),
                ],
                axis=1,
            )

    def _data_source_name(self) -> pd.DataFrame:
        """
        Static value: Enfusion
        """
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_value="Enfusion", target_attribute=OrderColumns.DATA_SOURCE_NAME
            ),
        )

    def _date(self) -> pd.DataFrame:
        """
        Populates from TempColumns.ORDER_RECEIVED temporary column, which contains
        the date from SourceColumns.TRADING_DATE_TIME for Allocations
        and SourceColumns.ORDER_DATE for Executions
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.ORDER_RECEIVED_DATE,
                target_attribute=OrderColumns.DATE,
            ),
            auditor=self.auditor,
        )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        pass

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Populates from TempColumns.BUY_SELL_INDICATOR temporary column
        Contains data from SourceColumns.TRANSACTION_TYPE for Allocations
        and SourceColumns.ORDER_SIDE for Executions
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.BUY_SELL_INDICATOR,
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
            ),
            auditor=self.auditor,
        )

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        pass

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.ORDER_LIMIT_PRICE
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE,
                cases=[
                    Case(
                        query=f"`{SourceColumns.EXECUTION_ORDER_TYPE}`.str.upper() == 'LIMIT'",
                        attribute=SourceColumns.ORDER_LIMIT_PRICE,
                    )
                ],
            ),
        )

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        pass

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        pass

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        pass

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        pass

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        pass

    def _execution_details_order_status(self) -> pd.DataFrame:
        """
        Populates with "NEWO" or "FILL" for Allocations,
        for Executions it's mapped against:
            New: PARF
            Correct: REME
            Cancel: CAME
        with PARF as default
        Adds '__synth_parent.executionDetails.orderStatus' for Executions
        Adds '.order' and '.orderState' prefixes for Allocations
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return pd.concat(
                [
                    MapConditional.process(
                        source_frame=self.source_frame,
                        params=ParamsMapConditional(
                            target_attribute=add_prefix(
                                prefix=ModelPrefix.ORDER_STATE,
                                attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                            ),
                            cases=[
                                Case(
                                    query=f"`{TempColumns.ORDER_STATUS}` != '{OrderStatus.NEWO.value}'",
                                    attribute=TempColumns.ORDER_STATUS,
                                ),
                            ],
                        ),
                    ),
                    MapStatic.process(
                        source_frame=self.source_frame,
                        params=ParamsMapStatic(
                            target_attribute=add_prefix(
                                prefix=ModelPrefix.ORDER,
                                attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                            ),
                            target_value=OrderStatus.NEWO.value,
                        ),
                    ),
                ],
                axis=1,
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            # Creating a view to mark records as REME where EXEC_ID is present in
            # another record's EXEC_REF_ID and that record's EVENT_TYPE is Correct
            # (caters for scenario when an execution is booked then subsequently corrected)
            view_cols = [
                SourceColumns.EXEC_ID,
                SourceColumns.EXEC_REF_ID,
                SourceColumns.EVENT_TYPE,
            ]
            view_df = self.source_frame.loc[:, view_cols]

            view_df.loc[:, TempColumns.FLAG] = (
                (
                    view_df[SourceColumns.EXEC_REF_ID].notnull()
                    & view_df[SourceColumns.EXEC_REF_ID].isin(
                        view_df[SourceColumns.EXEC_ID]
                    )
                    & view_df[SourceColumns.EVENT_TYPE].str.fullmatch(
                        EventTypes.CORRECT, case=False, na=False
                    )
                )
                .fillna(False)
                .astype(int)
            )

            view_df.loc[:, TempColumns.REME_FLAG] = (
                (
                    view_df[SourceColumns.EXEC_ID].notnull()
                    & view_df[SourceColumns.EXEC_ID].isin(
                        view_df[view_df[TempColumns.FLAG] == 1][
                            SourceColumns.EXEC_REF_ID
                        ].unique()
                    )
                )
                .fillna(False)
                .astype(int)
            )

            return pd.concat(
                [
                    MapConditional.process(
                        source_frame=view_df,
                        params=ParamsMapConditional(
                            target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                            cases=[
                                Case(
                                    query="index == index",
                                    value=OrderStatus.PARF.value,
                                ),
                                # Below condition not required but adding it for parity
                                # as this condition is mentioned in specs
                                Case(
                                    query=f"`{SourceColumns.EXEC_REF_ID}`.isnull() & "
                                    f"`{SourceColumns.EVENT_TYPE}`.str.fullmatch("
                                    f"'{EventTypes.NEW}', case=False, na=False)",
                                    value=OrderStatus.PARF.value,
                                ),
                                Case(
                                    query=f"`{SourceColumns.EXEC_REF_ID}`.isnull() & "
                                    f"`{SourceColumns.EVENT_TYPE}`.str.fullmatch("
                                    f"'{EventTypes.CORRECT}', case=False, na=False)",
                                    value=OrderStatus.REME.value,
                                ),
                                Case(
                                    query=f"`{TempColumns.REME_FLAG}` == 1",
                                    value=OrderStatus.REME.value,
                                ),
                            ],
                        ),
                    ),
                    MapStatic.process(
                        source_frame=self.source_frame,
                        params=ParamsMapStatic(
                            target_value=OrderStatus.NEWO.value,
                            target_attribute=add_prefix(
                                prefix=SYNTH_PARENT_PREFIX,
                                attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                            ),
                        ),
                    ),
                ],
                axis=1,
            )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.EXECUTION_ORDER_TYPE
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.EXECUTION_ORDER_TYPE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
            ),
            auditor=self.auditor,
        ).fillna(OrderType.MARKET)

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.INSTRUCTIONS for Allocations and from
        SourceColumns.ORDER_STATUS_TEXT,  SourceColumns.CUSIP, SourceColumns.RIC,
        SourceColumns.SEDOL, SourceColumns.DESCRIPTION and  SourceColumns.BB_YELLOW
        for Executions.
        It will concatenate the values, separated by a comma (',') and prefixed by
        the non normalized source column name.
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return ConcatAttributes.process(
                source_frame=pd.DataFrame(
                    data=self.source_frame.loc[
                        :, [SourceColumns.INSTRUCTIONS, SourceColumns.ORDER_TYPE]
                    ].values,
                    index=self.source_frame.index,
                    columns=[TempColumns.INSTRUCTIONS, TempColumns.ORDER_TYPE],
                ),
                params=ParamsConcatAttributes(
                    source_attributes=[
                        TempColumns.INSTRUCTIONS,
                        TempColumns.ORDER_TYPE,
                    ],
                    delimiter=", ",
                    prefix="attribute name",
                    target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                ),
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            return ConcatAttributes.process(
                source_frame=pd.DataFrame(
                    data=self.source_frame.loc[
                        :,
                        [
                            SourceColumns.ORDER_STATUS_TEXT,
                            SourceColumns.CUSIP,
                            SourceColumns.RIC,
                            SourceColumns.SEDOL,
                            SourceColumns.DESCRIPTION,
                            SourceColumns.BB_YELLOW,
                        ],
                    ].values,
                    index=self.source_frame.index,
                    columns=[
                        TempColumns.ORDER_STATUS_TEXT,
                        SourceColumns.CUSIP,
                        SourceColumns.RIC,
                        SourceColumns.SEDOL,
                        TempColumns.DESCRIPTION,
                        TempColumns.BBYELLOW,
                    ],
                ),
                params=ParamsConcatAttributes(
                    source_attributes=[
                        TempColumns.ORDER_STATUS_TEXT,
                        SourceColumns.CUSIP,
                        SourceColumns.RIC,
                        SourceColumns.SEDOL,
                        TempColumns.DESCRIPTION,
                        TempColumns.BBYELLOW,
                    ],
                    delimiter=", ",
                    prefix="attribute name",
                    target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                ),
            )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        pass

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        pass

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        """
        Populates from TempColumns.SHORT_SELL temporary column
        Contains data from SourceColumns.TRANSACTION_TYPE for Allocations
        and SourceColumns.ORDER_SIDE for Executions
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.SHORT_SELL,
                target_attribute=OrderColumns.EXECUTION_DETAILS_SHORT_SELLING_INDICATOR,
            ),
            auditor=self.auditor,
        )

    def _execution_details_stop_price(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.ORDER_STOP_PRICE
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ORDER_STOP_PRICE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_STOP_PRICE,
            ),
            auditor=self.auditor,
        )

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.TRADING_CAPACITY for Allocations
        and with "AOTC" for Executions
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.TRADING_CAPACITY,
                    target_attribute=OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY,
                    fill_nan=TradingCapacity.AOTC.value,
                ),
                auditor=self.auditor,
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            return MapStatic.process(
                source_frame=self.source_frame,
                params=ParamsMapStatic(
                    target_value=TradingCapacity.AOTC.value,
                    target_attribute=OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY,
                ),
            )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """
        Populates from TempColumns.VALIDITY_PERIOD
        Converts into list of strings
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.VALIDITY_PERIOD,
                target_attribute=OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD,
                cast_to="string.list",
                list_delimiter=",",
            ),
            auditor=self.auditor,
        )

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _financing_type(self) -> pd.DataFrame:
        pass

    def _hierarchy(self) -> pd.DataFrame:
        """
        Populates with "Standalone"
        """
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_value=HierarchyEnum.STANDALONE.value,
                target_attribute=OrderColumns.HIERARCHY,
            ),
        )

    def _id(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.ORDER_ID or TempColumns.TRANS_REF_NUMBER_ORDER_DATE
        Adds '__synth_parent.id' for Executions
        Adds '.order' and '.orderState' prefixes for Allocations
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            order_id_series = (
                "ALLO|"
                + self.source_frame[SourceColumns.ORDER_ID]
                + "|"
                + self.source_frame[SourceColumns.TRANSACTION_REFERENCE_NUMBER]
            )
            id_cases = [
                Case(
                    query=f"(`{TempColumns.RECORD_TYPE}` == '{RecordType.ALLOCATION}')",
                    attribute=TempColumns.ALLOCATION_ORDER_ID,
                ),
                Case(
                    query=f"`{TempColumns.RECORD_TYPE}` == '{RecordType.MARKET}'",
                    attribute=SourceColumns.ORDER_ID,
                ),
                Case(
                    query=f"`{SourceColumns.ORDER_ID}`.isnull()",
                    attribute=SourceColumns.TRANSACTION_REFERENCE_NUMBER,
                ),
            ]
            return pd.concat(
                [
                    MapConditional.process(
                        source_frame=pd.concat(
                            [
                                self.source_frame,
                                pd.DataFrame(
                                    {TempColumns.ALLOCATION_ORDER_ID: order_id_series}
                                ),
                            ],
                            axis=1,
                        ),
                        params=ParamsMapConditional(
                            target_attribute=add_prefix(
                                prefix=ModelPrefix.ORDER_STATE,
                                attribute=OrderColumns.ID,
                            ),
                            cases=id_cases,
                        ),
                    ),
                    MapConditional.process(
                        source_frame=pd.concat(
                            [
                                self.source_frame,
                                pd.DataFrame(
                                    {TempColumns.ALLOCATION_ORDER_ID: order_id_series}
                                ),
                            ],
                            axis=1,
                        ),
                        params=ParamsMapConditional(
                            target_attribute=add_prefix(
                                prefix=ModelPrefix.ORDER,
                                attribute=OrderColumns.ID,
                            ),
                            cases=id_cases,
                        ),
                    ),
                ],
                axis=1,
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            return pd.concat(
                [
                    MapAttribute.process(
                        source_frame=self.source_frame,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.ORDER_ID,
                            target_attribute=OrderColumns.ID,
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=self.source_frame,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.ORDER_ID,
                            target_attribute=add_prefix(
                                prefix=SYNTH_PARENT_PREFIX, attribute=OrderColumns.ID
                            ),
                        ),
                        auditor=self.auditor,
                    ),
                ],
                axis=1,
            )

    def _is_discretionary(self) -> pd.DataFrame:
        pass

    def _is_iceberg(self):
        pass

    def _is_repo(self):
        pass

    def _is_synthetic(self):
        pass

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        pass

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        pass

    def _jurisdiction_country(self) -> pd.DataFrame:
        pass

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        pass

    def _market_identifiers(self) -> pd.DataFrame:
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """
        Populates from the following temporary columns:
            TempColumns.ASSET_CLASS
            TempColumns.BBGID
            TempColumns.INSTR_IDS_CURRENCY
            TempColumns.EXPIRY_DATE
            TempColumns.ISIN
            TempColumns.NOTIONAL_CURRENCY_2
            TempColumns.OPTION_STRIKE_PRICE
            TempColumns.OPTION_TYPE
            TempColumns.UNDERLYING_ISIN
            TempColumns.UNDERLYING_SYMBOL
            TempColumns.VENUE
        """
        return InstrumentIdentifiers.process(
            source_frame=self.pre_process_df,
            params=ParamsInstrumentIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                asset_class_attribute=TempColumns.ASSET_CLASS,
                bbg_figi_id_attribute=TempColumns.BBGID,
                underlying_symbol_expiry_code_attribute=TempColumns.UNDERLYING_SYMBOL_EXPIRY_CODE,
                currency_attribute=TempColumns.INSTR_IDS_CURRENCY,
                expiry_date_attribute=TempColumns.EXPIRY_DATE,
                isin_attribute=TempColumns.ISIN,
                notional_currency_2_attribute=TempColumns.NOTIONAL_CURRENCY_2,
                option_strike_price_attribute=TempColumns.OPTION_STRIKE_PRICE,
                option_type_attribute=TempColumns.OPTION_TYPE,
                underlying_isin_attribute=TempColumns.UNDERLYING_ISIN,
                underlying_symbol_attribute=TempColumns.UNDERLYING_SYMBOL,
                venue_attribute=TempColumns.VENUE,
                retain_task_inputs=True,
            ),
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """
        Populates from the following temporary columns:
            TempColumns.PARTY_EXECUTING_ENTITY
            TempColumns.TRADER
            TempColumns.EXECUTION_WITHIN_FIRM
            TempColumns.INVESTMENT_DECISION_MAKER
            TempColumns.COUNTERPARTY
            TempColumns.BUYER
            TempColumns.BUYER_DECISION_MAKER
            TempColumns.SELLER
            TempColumns.SELLER_DECISION_MAKER
        """
        return GenericOrderPartyIdentifiers.process(
            source_frame=self.pre_process_df,
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=TempColumns.PARTY_EXECUTING_ENTITY,
                trader_identifier=TempColumns.TRADER,
                execution_within_firm_identifier=TempColumns.EXECUTION_WITHIN_FIRM,
                investment_decision_within_firm_identifier=TempColumns.INVESTMENT_DECISION_MAKER,
                client_identifier=TempColumns.CLIENT,
                counterparty_identifier=TempColumns.COUNTERPARTY,
                buyer_identifier=TempColumns.BUYER,
                buyer_decision_maker_identifier=TempColumns.BUYER_DECISION_MAKER,
                seller_identifier=TempColumns.SELLER,
                seller_decision_maker_identifier=TempColumns.SELLER_DECISION_MAKER,
                create_fallback_fields=True,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """
        Populates with "Order" for Allocations and "OrderState" for Executions,
        Adds '__synth_parent.__meta_model__', needed for Allocations to escape AssignMetaParent
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return pd.concat(
                [
                    MapConditional.process(
                        source_frame=self.source_frame,
                        params=ParamsMapConditional(
                            target_attribute=add_prefix(
                                prefix=ModelPrefix.ORDER,
                                attribute=OrderColumns.META_MODEL,
                            ),
                            cases=[
                                Case(
                                    query=f"`{TempColumns.ORDER_STATUS}` == '{OrderStatus.NEWO.value}'",
                                    value="Order",
                                ),
                            ],
                        ),
                    ),
                    MapConditional.process(
                        source_frame=self.source_frame,
                        params=ParamsMapConditional(
                            target_attribute=add_prefix(
                                prefix=ModelPrefix.ORDER_STATE,
                                attribute=OrderColumns.META_MODEL,
                            ),
                            cases=[
                                Case(
                                    query=f"`{TempColumns.ORDER_STATUS}` != '{OrderStatus.NEWO.value}'",
                                    value="OrderState",
                                ),
                            ],
                        ),
                    ),
                ],
                axis=1,
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            return pd.concat(
                [
                    MapStatic.process(
                        source_frame=self.source_frame,
                        params=ParamsMapStatic(
                            target_value="Order",
                            target_attribute=add_prefix(
                                prefix=SYNTH_PARENT_PREFIX,
                                attribute=OrderColumns.META_MODEL,
                            ),
                        ),
                    ),
                    MapStatic.process(
                        source_frame=self.source_frame,
                        params=ParamsMapStatic(
                            target_value="OrderState",
                            target_attribute=OrderColumns.META_MODEL,
                        ),
                    ),
                ],
                axis=1,
            )

    def _order_class(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        pass

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.ORDER_REFERENCE, only for Executions
        """
        if self.file_type == FileTypes.EXECUTIONS:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.ORDER_REFERENCE,
                    target_attribute=OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE,
                ),
                auditor=self.auditor,
            )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.ORDER_ID or TempColumns.ALLOCATION_ORDER_ID
        based on file type and record type incase it's an allocation file.
        """

        if self.file_type == FileTypes.ALLOCATIONS:
            id_column = add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID)

            return MapAttribute.process(
                source_frame=pd.concat(
                    [self.source_frame, self.target_df.loc[:, id_column]],
                    axis=1,
                ),
                params=ParamsMapAttribute(
                    source_attribute=id_column,
                    target_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
                ),
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.ORDER_ID,
                    target_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
                ),
                auditor=self.auditor,
            )

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.PARENT_ORDER_ID
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.PARENT_ORDER_ID,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_PARENT_ORDER_ID,
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """
        Temporary column used for:
            OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO
            OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO
        Populates from SourceColumns.EXEC_ID or
            SourceColumns.TRANSACTION_REFERENCE_NUMBER and SourceColumns.ORDER_DATE
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapConditional.process(
                source_frame=pd.concat(
                    [self.source_frame, self.pre_process_df], axis=1
                ),
                params=ParamsMapConditional(
                    target_attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                    cases=[
                        Case(
                            query=f"`{SourceColumns.ORDER_ID}`.notnull()",
                            attribute=SourceColumns.TRANSACTION_REFERENCE_NUMBER,
                        ),
                        Case(
                            query=f"`{SourceColumns.ORDER_ID}`.isnull()",
                            attribute=TempColumns.TRANS_REF_NUMBER_ORDER_DATE,
                        ),
                    ],
                ),
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            if self.fill_by_fill_flag:
                return MapAttribute.process(
                    source_frame=self.source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=SourceColumns.EXEC_ID,
                        target_attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                    ),
                    auditor=self.auditor,
                )
            else:
                return MapValue.process(
                    source_frame=self.source_frame,
                    value_map=self.fill_by_fill_map[ExecMapKeys.FIRST_EXEC_ID],
                    params=ParamsMapValue(
                        source_attribute=SourceColumns.ORDER_ID,
                        target_attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                    ),
                )

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """
        Populates from TempColumns.INITIAL_QUANTITY or TempColumns.AGGREGATED_QUANTITY
        Contains data from SourceColumns.QUANTITY or SourceColumns.ORDER_TOTAL_QUANTITY
        """
        initial_quantity_df = MapConditional.process(
            source_frame=self.pre_process_df,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                cases=[
                    Case(
                        query=f"`{TempColumns.AGGREGATION_CODE}`.isnull()",
                        attribute=TempColumns.INITIAL_QUANTITY,
                    ),
                    Case(
                        query=f"`{TempColumns.AGGREGATION_CODE}`.notnull()",
                        attribute=TempColumns.AGGREGATED_QUANTITY,
                    ),
                ],
            ),
            auditor=self.auditor,
        )
        return initial_quantity_df

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price(self) -> pd.DataFrame:
        """
        Populates from TempColumns.LAST_PX temporary column for Executions
            and from TempColumns.PRICE for Allocations without SourceColumns.ORDER_ID
            and also for allocation records. If aggregation is active it uses data from
            TempColumns.AGGREGATED_PRICE as well.
        Contains data from SourceColumns.LAST_PX or SourceColumns.PRICE
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapConditional.process(
                source_frame=pd.concat(
                    [self.source_frame, self.pre_process_df], axis=1
                ),
                params=ParamsMapConditional(
                    target_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                    cases=[
                        Case(
                            query=f"`{SourceColumns.ORDER_ID}`.isnull() | "
                            f"`{TempColumns.RECORD_TYPE}` == '{RecordType.ALLOCATION}'",
                            attribute=TempColumns.PRICE,
                        ),
                        Case(
                            query=f"`{TempColumns.AGGREGATION_CODE}`.notnull()",
                            attribute=TempColumns.AGGREGATED_PRICE,
                        ),
                    ],
                ),
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            if self.fill_by_fill_flag:
                return MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.LAST_PX,
                        target_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                    ),
                    auditor=self.auditor,
                )

            return MapValue.process(
                source_frame=self.source_frame,
                value_map=self.fill_by_fill_map[ExecMapKeys.MEAN_PRICE],
                params=ParamsMapValue(
                    source_attribute=SourceColumns.ORDER_ID,
                    target_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                ),
                auditor=self.auditor,
            )

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.ORDER_REMAINING_QUANTITY for Allocations
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.ORDER_REMAINING_QUANTITY,
                    target_attribute=OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
                ),
                auditor=self.auditor,
            )

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        pass

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """
        Populates from TempColumns.TRADED_QUANTITY temporary column
        Contains data from SourceColumns.LAST_QTY
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapAttribute.process(
                source_frame=self.pre_process_df,
                params=ParamsMapAttribute(
                    source_attribute=TempColumns.TRADED_QUANTITY,
                    target_attribute=add_prefix(
                        prefix=ModelPrefix.ORDER_STATE,
                        attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                    ),
                ),
                auditor=self.auditor,
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            if self.fill_by_fill_flag:
                return MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.TRADED_QUANTITY,
                        target_attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                    ),
                    auditor=self.auditor,
                )
            else:
                return MapValue.process(
                    source_frame=self.source_frame,
                    value_map=self.fill_by_fill_map[ExecMapKeys.TOTAL_QUANTITY],
                    params=ParamsMapValue(
                        source_attribute=SourceColumns.ORDER_ID,
                        target_attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                    ),
                    auditor=self.auditor,
                )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """
        This method uses the same value as OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO, therefore it
         depends on _order_identifiers_transaction_ref_no().
        """
        return MapAttribute.process(
            source_frame=self.target_df,
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                target_attribute=OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO,
            ),
            auditor=self.auditor,
        )

    def _source_index(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceIndex column"""
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                from_index=True, target_attribute=OrderColumns.SOURCE_INDEX
            ),
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceKey column from the SWARM_FILE_URL"""
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_value=os.getenv("SWARM_FILE_URL"),
                target_attribute=OrderColumns.SOURCE_KEY,
            ),
        )

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_order_received(self) -> pd.DataFrame:
        """
        Populates from TempColumns.ORDER_RECEIVED temporary column,
        SourceColumns.TRADING_DATE_TIME for Allocations
        and SourceColumns.ORDER_DATE + SourceColumns.ORDER_TIMESTAMP for Executions
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.ORDER_RECEIVED,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
            ),
            auditor=self.auditor,
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """
        Populates from TempColumns.TRADING_DATE_TIME_EXECUTION_DATETIME temporary column
        Contains date from SourceColumns.EXECUTION_DATE and SourceColumns.TRADING_DATE_TIME
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapConditional.process(
                source_frame=pd.concat(
                    [self.source_frame, self.pre_process_df], axis=1
                ),
                params=ParamsMapConditional(
                    target_attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
                    cases=[
                        Case(
                            query=f"(`{SourceColumns.ORDER_ID}`.isnull() | "
                            f"`{TempColumns.RECORD_TYPE}` == '{RecordType.ALLOCATION}')"
                            f"& (`{TempColumns.LAST_FILL_TIME}`.notnull())",
                            attribute=TempColumns.LAST_FILL_TIME,
                        ),
                        Case(
                            query=f"(`{SourceColumns.ORDER_ID}`.isnull() | "
                            f"`{TempColumns.RECORD_TYPE}` == '{RecordType.ALLOCATION}')"
                            f"& (`{TempColumns.LAST_FILL_TIME}`.isnull())",
                            attribute=TempColumns.TRADING_DATE_TIME,
                        ),
                    ],
                ),
            )

        if self.file_type == FileTypes.EXECUTIONS:
            if self.fill_by_fill_flag:
                return MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.EXECUTION_DATETIME,
                        target_attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
                    ),
                    auditor=self.auditor,
                )

            return MapValue.process(
                source_frame=self.source_frame,
                value_map=self.fill_by_fill_map[ExecMapKeys.EARLIER_EXECUTION_DATE],
                params=ParamsMapValue(
                    source_attribute=SourceColumns.ORDER_ID,
                    target_attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
                ),
            )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """
        Populates from TempColumns.ORDER_RECEIVED temporary column,
        SourceColumns.TRADING_DATE_TIME for Allocations
        and SourceColumns.ORDER_DATE + SourceColumns.ORDER_TIMESTAMP for Executions
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.ORDER_RECEIVED,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
            ),
            auditor=self.auditor,
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """
        This method uses the same value as OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED, therefore
        it depends on _timestamps_order_status_updated().
        """
        return MapAttribute.process(
            source_frame=self.target_df,
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
                target_attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
            ),
            auditor=self.auditor,
        )

    def _timestamps_validity_period(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """
        Populates from TempColumns.SHORT_SELL temporary column
        Contains data from SourceColumns.TRANSACTION_TYPE for Allocations
        and SourceColumns.ORDER_SIDE for Executions
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.SHORT_SELL,
                target_attribute=OrderColumns.TRADERS_ALGOS_WAIVER_INDICATORS_SHORT_SELLING_INDICATOR,
            ),
            auditor=self.auditor,
        )

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_waiver_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.PROGRAMID
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.PROGRAM_ID,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_BASKET_ID,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        pass

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Populates from TempColumns.BUY_SELL_INDICATOR temporary column
        Contains data from SourceColumns.TRANSACTION_TYPE for Allocations
        and SourceColumns.ORDER_SIDE for Executions
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.BUY_SELL_INDICATOR,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.COMMISSIONS, only for Allocations
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ParamsConvertMinorToMajor(
                    source_ccy_attribute=SourceColumns.PRICE_CURRENCY,
                    source_price_attribute=SourceColumns.COMMISSIONS,
                    target_price_attribute=OrderColumns.TRANSACTION_DETAILS_COMMISSION_AMOUNT,
                ),
            )

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.PRICE_CURRENCY, only for Allocations
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapAttribute.process(
                source_frame=self.pre_process_df,
                params=ParamsMapAttribute(
                    source_attribute=TempColumns.PRICE_CURRENCY,
                    target_attribute=OrderColumns.TRANSACTION_DETAILS_COMMISSION_AMOUNT_CURRENCY,
                ),
                auditor=self.auditor,
            )

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.COMMISSION_TYPE, only for Allocations
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapValue.process(
                source_frame=self.source_frame,
                params=ParamsMapValue(
                    source_attribute=SourceColumns.COMMISSION_TYPE,
                    target_attribute=OrderColumns.TRANSACTION_DETAILS_COMMISSION_AMOUNT_TYPE,
                    case_insensitive=True,
                    value_map={
                        "Commission (Basis Points)": CommissionAmountType.BASIS_POINTS.value,
                        "Commission (Per Share)": CommissionAmountType.AMOUNT_PER_UNIT.value,
                        "Commission (Cents Per Share)": CommissionAmountType.AMOUNT_PER_UNIT.value,
                    },
                ),
                auditor=self.auditor,
            )

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.COMPLEX_TRADE_COMPONENT_ID
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.COMPLEX_TRADE_COMPONENT_ID,
                    target_attribute=OrderColumns.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID,
                ),
                auditor=self.auditor,
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.ORDER_REFERENCE,
                    target_attribute=OrderColumns.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID,
                ),
                auditor=self.auditor,
            )

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        pass

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        pass

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        pass

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_price(self) -> pd.DataFrame:
        """
        It uses the same value as OrderColumns.PRICE_FORMING_DATA_PRICE, therefore it depends on
         _price_forming_data_price().
        """
        return MapAttribute.process(
            source_frame=self.target_df,
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_price_average(self) -> pd.DataFrame:
        pass

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.PRICE_CURRENCY for Allocations
        and SourceColumns.CURRENCY for Executions
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapAttribute.process(
                source_frame=self.pre_process_df,
                params=ParamsMapAttribute(
                    source_attribute=TempColumns.PRICE_CURRENCY,
                    target_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                ),
                auditor=self.auditor,
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            return MapAttribute.process(
                source_frame=self.pre_process_df,
                params=ParamsMapAttribute(
                    source_attribute=TempColumns.CURRENCY,
                    target_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                ),
                auditor=self.auditor,
            )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.PRICE_TYPE for Allocations
        and "MONE" for Executions
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ParamsConvertMinorToMajor(
                    source_ccy_attribute=SourceColumns.PRICE_TYPE,
                    target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION,
                ),
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            return MapStatic.process(
                source_frame=self.source_frame,
                params=ParamsMapStatic(
                    target_value=PriceNotation.MONE.value,
                    target_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION,
                ),
            )

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        pass

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        pass

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """
        This method uses the same value as OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
        therefore it depends on _price_forming_data_traded_quantity().
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            source_attribute = add_prefix(
                prefix=ModelPrefix.ORDER_STATE,
                attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
            )
            target_attribute = add_prefix(
                prefix=ModelPrefix.ORDER_STATE,
                attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY,
            )
        else:
            source_attribute = OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
            target_attribute = OrderColumns.TRANSACTION_DETAILS_QUANTITY

        return MapAttribute.process(
            source_frame=self.target_df,
            params=ParamsMapAttribute(
                source_attribute=source_attribute,
                target_attribute=target_attribute,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """
        Populates from TempColumns.CURRENCY temporary column for Executions
        Contains data from SourceColumns.CURRENCY for Executions
        Populates from TempColumns.PRICE_CURRENCY temporary column for Allocations
        Contains data from SourceColumns.PRICE_CURRENCY for Allocations
        """
        return MapConditional.process(
            source_frame=self.pre_process_df,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
                cases=[
                    Case(
                        query=f"`{TempColumns.QUANTITY_TYPE}` == '{QuantityNotation.MONE.value}'",
                        attribute=TempColumns.DESCRIPTION_CURRENCY_1,
                    ),
                    Case(
                        query=f"`{TempColumns.QUANTITY_TYPE}` == '{QuantityNotation.NOML.value}'",
                        attribute=TempColumns.QUANTITY_CURRENCY,
                    ),
                ],
            ),
        )

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.QUANTITY_TYPE
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.QUANTITY_TYPE,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """
        Populates "Allocation" for Allocations and
        "Market Side" for Executions and market records
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapConditional.process(
                source_frame=self.source_frame,
                params=ParamsMapConditional(
                    target_attribute=OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE,
                    cases=[
                        Case(
                            query="index == index",
                            value=OrderRecordType.ALLOCATION.value,
                        ),
                        Case(
                            query=f"`{TempColumns.RECORD_TYPE}` == '{RecordType.MARKET}'",
                            value=OrderRecordType.MARKET_SIDE.value,
                        ),
                    ],
                ),
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            return MapStatic.process(
                source_frame=self.source_frame,
                params=ParamsMapStatic(
                    target_value=OrderRecordType.MARKET_SIDE.value,
                    target_attribute=OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE,
                ),
            )

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        pass

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        pass

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populated from the execution details trading capacity
        """
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """
        This method uses the same value as OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED, therefore
        it depends on _timestamps_order_status_updated().
        """
        return MapAttribute.process(
            source_frame=self.target_df,
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.EXCHANGE_MIC_CODE and fills with "XOFF" for aggregated
        records.
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                cases=[
                    Case(
                        query=f"`{TempColumns.AGGREGATION_CODE}`.isnull()",
                        attribute=SourceColumns.EXCHANGE_MIC_CODE,
                    ),
                    Case(
                        query=f"`{TempColumns.AGGREGATION_CODE}`.notnull()",
                        value=Venue.XOFF,
                    ),
                ],
            ),
        )

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        pass

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_venue(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.VENUE for Allocations
        and fills with "XOFF" for aggregated records. For Executions populates from
        SourceColumns.EXCHANGE_MIC_CODE and if not populated fills with "XOFF"
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapConditional.process(
                source_frame=self.source_frame,
                params=ParamsMapConditional(
                    target_attribute=OrderColumns.TRANSACTION_DETAILS_VENUE,
                    cases=[
                        Case(
                            query=f"`{TempColumns.AGGREGATION_CODE}`.isnull()",
                            attribute=SourceColumns.VENUE,
                        ),
                        Case(
                            query=f"`{TempColumns.AGGREGATION_CODE}`.notnull()",
                            value=Venue.XOFF,
                        ),
                    ],
                ),
            )

        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.EXCHANGE_MIC_CODE,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_VENUE,
            ),
            auditor=self.auditor,
        ).fillna(Venue.XOFF)

    # Temporary Fields
    def _temp_buy_sell(self) -> pd.DataFrame:
        """
        Temporary Column for:
            OrderColumns.BUY_SELL
            TempColumns.BUY_SELL_INDICATOR
        Populates from TempColumns.ORDERSIDE_OR_TRANSACTIONTYPE for Allocations
        and Executions, according to the
        following map:
            "Buy": "1"
            "BuyToCover": "1
            "Cover": "1"
            "Sell": "2"
            "SellShort": "2"
            "Short": "2"
        If self.allocation_aggregation_flag=True this column uses the mapper information.
        """
        temp_buy_sell_df = MapValue.process(
            source_frame=self.pre_process_df,
            params=ParamsMapValue(
                source_attribute=TempColumns.ORDERSIDE_OR_TRANSACTIONTYPE,
                target_attribute=TempColumns.BUY_SELL,
                case_insensitive=True,
                value_map={
                    "Buy": "1",
                    "BuyToCover": "1",
                    "Buy To Cover": "1",
                    "Cover": "1",
                    "Sell": "2",
                    "SellShort": "2",
                    "Sell Short": "2",
                    "Short": "2",
                },
            ),
            auditor=self.auditor,
        )

        if self.allocation_aggregation_flag:
            aggregated_buy_sell_df = MapValue.process(
                source_frame=self.pre_process_df,
                value_map=self.allocation_aggregation_map[AlloMapKeys.BUY_SELL],
                params=ParamsMapValue(
                    source_attribute=TempColumns.AGGREGATION_CODE,
                    target_attribute=TempColumns.BUY_SELL,
                    fill_source_na=pd.NA,
                ),
            )
            temp_buy_sell_df = aggregated_buy_sell_df.fillna(temp_buy_sell_df)

        return temp_buy_sell_df

    def _temp_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Temporary column for:
            OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
            OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
        Populates from TempColumns.BUY_SELL temporary column according to
        the map {"1": "BUYI", "2": "SELL"}
        Contains data from SourceColumns.ORDER_SIDE for Allocations
        and Executions
        """
        return MapValue.process(
            source_frame=self.pre_process_df,
            params=ParamsMapValue(
                source_attribute=TempColumns.BUY_SELL,
                target_attribute=TempColumns.BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "1": BuySellIndicator.BUYI.value,
                    "2": BuySellIndicator.SELL.value,
                },
            ),
            auditor=self.auditor,
        )

    def _temp_currency(self) -> pd.DataFrame:
        """
        Temporary column used for:
            OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
            OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY
        Populates from SourceColumns.CURRENCY
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.CURRENCY,
                target_ccy_attribute=TempColumns.CURRENCY,
            ),
        )

    def _temp_currency_2(self) -> pd.DataFrame:
        """
        Temporary Column for TempColumns.INSTR_IDS_NOTIONAL_CURRENCY_2
        Populates from SourceColumns.CURRENCY_2
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.CURRENCY_2,
                target_ccy_attribute=TempColumns.CURRENCY_2,
            ),
        )

    def _temp_quantity_currency(self) -> pd.DataFrame:
        """
        Temporary Column for TempColumns.QUANTITY_CURRENCY
        Populates from SourceColumns.QUANTITY_CURRENCY
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.QUANTITY_CURRENCY,
                target_ccy_attribute=TempColumns.QUANTITY_CURRENCY,
            ),
        )

    def _temp_description_date(self) -> pd.DataFrame:
        """
        Temp column for:
        `TempColumns.EXPIRY_DATE
        Populated from SourceColumns.DESCRIPTION for execution and
        SourceColumns.INSTRUMENT_FULL_NAME for allocation
        """
        sliced_description = pd.DataFrame(index=self.source_frame.index)
        if self.file_type == FileTypes.ALLOCATIONS:
            sliced_description = MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.INSTRUMENT_FULL_NAME,
                    target_attribute=TempColumns.SLICED_DESCRIPTION,
                    start_index=-10,
                ),
                auditor=self.auditor,
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            sliced_description = MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.DESCRIPTION,
                    target_attribute=TempColumns.SLICED_DESCRIPTION,
                    start_index=-10,
                ),
                auditor=self.auditor,
            )
        fx_forwards = MapConditional.process(
            source_frame=pd.concat([self.pre_process_df, sliced_description], axis=1),
            params=ParamsMapConditional(
                target_attribute=TempColumns.FX_FORWARDS_DESCRIPTION,
                cases=[
                    Case(
                        query=f"`{TempColumns.ASSET_CLASS}` == '{AssetClass.FX_FORWARD}'",
                        attribute=TempColumns.SLICED_DESCRIPTION,
                    )
                ],
            ),
        )
        return ConvertDatetime.process(
            source_frame=fx_forwards,
            params=ParamsConvertDatetime(
                source_attribute=TempColumns.FX_FORWARDS_DESCRIPTION,
                source_attribute_format="%Y-%m-%d",
                target_attribute=TempColumns.DESCRIPTION_DATE,
                convert_to="date",
            ),
        )

    def _temp_description_currencies(self) -> pd.DataFrame:
        """
        Temp columns for:
            TempColumns.INSTR_IDS_CURRENCY
        Populated from SourceColumns.DESCRIPTION
        """

        def _get_currency_from_description(
            desc_str: str, str_idx: int, str_start: int = None, str_end: int = None
        ):
            """
            aux function to get currencies from SourceColumns.DESCRIPTION
            slices description accordingly
            :param desc_str: description
            :param str_idx: where to find currency, 0 before '/', 1 after '/'
            :param str_start: slicing start
            :param str_end: slicing end
            :return: sliced currency
            """
            try:
                return desc_str.split("/")[str_idx][str_start:str_end]
            except (IndexError, AttributeError):
                return pd.NA

        def _check_currency(currency):
            """
            aux function, checks if it's a valid currency
            :param currency:
            :return:
            """
            try:
                pycountry.currencies.lookup(currency)
                return currency
            except LookupError:
                return pd.NA

        currencies = pd.DataFrame(index=self.pre_process_df.index)

        if self.file_type == FileTypes.ALLOCATIONS:
            description_column = SourceColumns.INSTRUMENT_FULL_NAME
        else:
            description_column = SourceColumns.DESCRIPTION

        currencies.loc[:, TempColumns.DESCRIPTION_CURRENCY_1] = self.source_frame.loc[
            :, description_column
        ].apply(lambda x: _get_currency_from_description(x, str_idx=1, str_end=3))

        currencies.loc[:, TempColumns.DESCRIPTION_CURRENCY_2] = self.source_frame.loc[
            :, description_column
        ].apply(lambda x: _get_currency_from_description(x, str_idx=0, str_start=-3))

        currencies_converted = pd.concat(
            [
                ConvertMinorToMajor.process(
                    source_frame=currencies,
                    params=ParamsConvertMinorToMajor(
                        source_ccy_attribute=TempColumns.DESCRIPTION_CURRENCY_1,
                        target_ccy_attribute=TempColumns.DESCRIPTION_CURRENCY_1,
                    ),
                ),
                ConvertMinorToMajor.process(
                    source_frame=currencies,
                    params=ParamsConvertMinorToMajor(
                        source_ccy_attribute=TempColumns.DESCRIPTION_CURRENCY_2,
                        target_ccy_attribute=TempColumns.DESCRIPTION_CURRENCY_2,
                    ),
                ),
            ],
            axis=1,
        ).fillna("")

        unique_desc_ccy = (
            currencies_converted[TempColumns.DESCRIPTION_CURRENCY_1]
            .append(currencies_converted[TempColumns.DESCRIPTION_CURRENCY_2])
            .unique()
            .tolist()
        )

        result_map_ccy = {}
        for ccy in unique_desc_ccy:
            result_map_ccy.update({ccy: _check_currency(ccy)})

        currencies_converted[TempColumns.DESCRIPTION_CURRENCY_1] = currencies_converted[
            TempColumns.DESCRIPTION_CURRENCY_1
        ].map(result_map_ccy)

        currencies_converted[TempColumns.DESCRIPTION_CURRENCY_2] = currencies_converted[
            TempColumns.DESCRIPTION_CURRENCY_2
        ].map(result_map_ccy)

        return currencies_converted

    def _temp_execution_datetime(self) -> pd.DataFrame:
        """
        Temporary column used for:
            OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED
            OrderColumns.TIMESTAMPS_TRADING_DATE_TIME
            OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME
        Populates from SourceColumns.EXECUTION_DATE
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.EXECUTION_DATE,
                target_attribute=TempColumns.EXECUTION_DATETIME,
                source_attribute_format="%Y-%m-%dT%H:%M:%S.%f",
                convert_to="datetime",
            ),
        )

    def _temp_expiry_date(self) -> pd.DataFrame:
        """
        Temporary column for:
            TempColumns.EXPIRY_DATE_SOURCE
        Populates from SourceColumns.EXPIRY_DATE
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.EXPIRY_DATE,
                source_attribute_format="%Y-%m-%d",
                target_attribute=TempColumns.EXPIRY_DATE_SOURCE,
                convert_to="date",
            ),
        )

    def _temp_future_expiration_date(self) -> pd.DataFrame:
        """
        Temporary column for:
            TempColumns.FUTURE_EXPIRATION_DATE
        Populates from SourceColumns.FUTURE_EXPIRATION_DATE
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.FUTURE_EXPIRATION_DATE,
                source_attribute_format="%Y-%m-%d",
                target_attribute=TempColumns.FUTURE_EXPIRATION_DATE,
                convert_to="date",
            ),
        )

    def _temp_initial_quantity(self) -> pd.DataFrame:
        """
        Temporary column for:
            TempColumns.INITIAL_QUANTITY
            TempColumns.TRADED_QUANTITY
        Populates from SourceColumns.QUANTITY or SourceColumns.ORDER_TOTAL_QUANTITY
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapConditional.process(
                source_frame=pd.concat(
                    [self.source_frame, self.pre_process_df], axis=1
                ),
                params=ParamsMapConditional(
                    target_attribute=TempColumns.INITIAL_QUANTITY,
                    cases=[
                        Case(
                            query="index == index",
                            attribute=SourceColumns.QUANTITY,
                        ),
                        Case(
                            query=f"`{TempColumns.RECORD_TYPE}` == '{RecordType.MARKET}'",
                            attribute=SourceColumns.ORDER_TOTAL_QUANTITY,
                        ),
                    ],
                ),
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.QUANTITY,
                    target_attribute=TempColumns.INITIAL_QUANTITY,
                ),
                auditor=self.auditor,
            )

    def _temp_last_px(self) -> pd.DataFrame:
        """
        Temporary column for:
            OrderColumns.PRICE_FORMING_DATA_PRICE
            OrderColumns.TRANSACTION_DETAILS_PRICE
        Populates from SourceColumns.LAST_PX
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.CURRENCY,
                source_price_attribute=SourceColumns.LAST_PX,
                target_price_attribute=TempColumns.LAST_PX,
            ),
        )

    def _temp_newo_in_file(self) -> pd.DataFrame:
        """
        Temporary column for RemoveDuplicateNEWO
        Only needed for Allocations
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapStatic.process(
                source_frame=self.source_frame,
                params=ParamsMapStatic(
                    target_attribute=TempColumns.NEWO_IN_FILE, target_value=False
                ),
            )

    def _temp_aggregated_quantity(self) -> pd.DataFrame:
        """
        Populates a temporary column representing the traded quantity in an aggregated record.
        The final value sums quantity from all BUYI and subtracts from all SELL records.
        """
        return MapValue.process(
            source_frame=self.source_frame,
            value_map=self.allocation_aggregation_map[AlloMapKeys.NET_QUANTITY],
            params=ParamsMapValue(
                source_attribute=TempColumns.AGGREGATION_CODE,
                target_attribute=TempColumns.AGGREGATED_QUANTITY,
                fill_source_na=pd.NA,
            ),
        )

    def _temp_aggregated_price(self) -> pd.DataFrame:
        """
        Populates a temporary column which holds the average price weighted by quantity
        in an aggregated record.
        """
        return MapValue.process(
            source_frame=self.source_frame,
            value_map=self.allocation_aggregation_map[AlloMapKeys.MEAN_PRICE],
            params=ParamsMapValue(
                source_attribute=TempColumns.AGGREGATION_CODE,
                target_attribute=TempColumns.AGGREGATED_PRICE,
                fill_source_na=pd.NA,
            ),
        )

    def _temp_cancel_newo_indicator(self) -> pd.DataFrame:
        """
        Temporary column which will be used to derive if the record is a
        NEWO record corresponding to a CANC record, i.e.,
        SourceColumns.REPORT_STATUS = CANC
        Only needed for Allocations
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            query = f"((`{TempColumns.ORDER_STATUS}`.str.fullmatch('{OrderStatus.NEWO.value}', case=False, na=False)) & ({SourceColumns.REPORT_STATUS}.str.fullmatch('CANC', case=False, na=False)))"
            return MapConditional.process(
                source_frame=self.source_frame,
                params=ParamsMapConditional(
                    target_attribute=TempColumns.NEWO_CANCEL_INDICATOR,
                    cases=[
                        Case(
                            query=query,
                            value=True,
                        ),
                        Case(
                            query=f"~{query}",
                            value=False,
                        ),
                    ],
                ),
            )

    def _temp_notional_currency_2(self) -> pd.DataFrame:
        """
        Temporary Column for TempColumns.INSTR_IDS_NOTIONAL_CURRENCY_2
        Populates from SourceColumns.NOTIONAL_CURRENCY_2
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.NOTIONAL_CURRENCY_2,
                target_ccy_attribute=TempColumns.NOTIONAL_CURRENCY_2,
            ),
        )

    def _temp_option_expiration_date(self) -> pd.DataFrame:
        """
        Temporary column for:
            TempColumns.OPTION_EXPIRATION_DATE
        Populates from SourceColumns.OPTION_EXPIRATION_DATE
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.OPTION_EXPIRATION_DATE,
                source_attribute_format="%Y-%m-%d",
                target_attribute=TempColumns.OPTION_EXPIRATION_DATE,
                convert_to="date",
            ),
        )

    def _temp_order_date_order_timestamp(self) -> pd.DataFrame:
        """
        Temporary column used for:
            TempColumns.ORDER_RECEIVED
        SourceColumns.ORDER_DATE and SourceColumns.ORDER_TIMESTAMP
        """
        return JoinDateAndTimeFormat.process(
            source_frame=self.source_frame,
            params=ParamsJoinDateAndTimeFormat(
                source_date_attribute=SourceColumns.ORDER_DATE,
                source_time_attribute=SourceColumns.ORDER_TIMESTAMP,
                source_format="%Y-%m-%d%H:%M:%S.%f",
                target_attribute=TempColumns.ORDER_DATE_ORDER_TIMESTAMP,
                target_format="%Y-%m-%dT%H:%M:%S.%fZ",
            ),
        )

    def _temp_orderside_or_transactiontype(self) -> pd.DataFrame:
        """
        Temporary column used for:
            TempColumns.ORDERSIDE_OR_TRANSACTIONTYPE
        SourceColumns.ORDER_SIDE or SourceColumns.TRANSACTION_TYPE
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapConditional.process(
                source_frame=self.source_frame,
                params=ParamsMapConditional(
                    target_attribute=TempColumns.ORDERSIDE_OR_TRANSACTIONTYPE,
                    cases=[
                        Case(
                            query=f"`{SourceColumns.ORDER_SIDE}`.notnull()",
                            attribute=SourceColumns.ORDER_SIDE,
                        ),
                        Case(
                            query=f"`{SourceColumns.ORDER_SIDE}`.isnull()",
                            attribute=SourceColumns.TRANSACTION_TYPE,
                        ),
                    ],
                ),
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            return pd.DataFrame(
                data=self.source_frame.loc[:, SourceColumns.ORDER_SIDE].values,
                index=self.source_frame.index,
                columns=[TempColumns.ORDERSIDE_OR_TRANSACTIONTYPE],
            )

    def _temp_order_received_date(self) -> pd.DataFrame:
        """
        Temporary column for TempColumns.ORDER_RECEIVED_DATE
        Populates from SourceColumns.ORDER_DATE
        """
        return ConvertDatetime.process(
            source_frame=self.pre_process_df,
            params=ParamsConvertDatetime(
                source_attribute=TempColumns.ORDER_RECEIVED,
                source_attribute_format="%Y-%m-%dT%H:%M:%S.%fZ",
                target_attribute=TempColumns.ORDER_RECEIVED_DATE,
                convert_to="date",
            ),
        )

    def _temp_order_received_date_stripped(self) -> pd.DataFrame:
        """
        Temporary column for TempColumns.ORDER_DATE_STRIPPED
        Populates from SourceColumns.ORDER_DATE
        """
        return MapValue.process(
            source_frame=self.pre_process_df,
            params=ParamsMapValue(
                source_attribute=TempColumns.ORDER_RECEIVED_DATE,
                target_attribute=TempColumns.ORDER_DATE_STRIPPED,
                regex_replace_only=True,
                regex_replace_map=[RegexReplaceMap(**{"regex": "-", "drop": True})],
            ),
        )

    def _temp_price(self) -> pd.DataFrame:
        """
        Temporary column used for:
            OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
            OrderColumns.TRANSACTION_DETAILS_COMMISSION_AMOUNT_CURRENCY
        Populates from SourceColumns.CURRENCY
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.PRICE_CURRENCY,
                source_price_attribute=SourceColumns.PRICE,
                target_price_attribute=TempColumns.PRICE,
            ),
        )

    def _temp_price_currency(self) -> pd.DataFrame:
        """
        Temporary column used for:
            OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
            OrderColumns.TRANSACTION_DETAILS_COMMISSION_AMOUNT_CURRENCY
        Populates from SourceColumns.CURRENCY
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.PRICE_CURRENCY,
                target_ccy_attribute=TempColumns.PRICE_CURRENCY,
            ),
        )

    def _temp_quantity_type(self) -> pd.DataFrame:
        """
        Temp column used in transactionDetails.quantityNotation and transactionDetails.quantityCurrency

        Populated from SourceColumns.QUANTITY_TYPE
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            description_column = SourceColumns.INSTRUMENT_FULL_NAME
        else:
            description_column = SourceColumns.DESCRIPTION
        temp_df = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.QUANTITY_TYPE,
                cases=[
                    Case(
                        query="index == index",
                        attribute=SourceColumns.QUANTITY_TYPE,
                    ),
                    Case(
                        query=f"`{description_column}`.str.match('{AssetClass.FX_FORWARD}', case=False, na=False) | "
                        f"`{description_column}`.str.match('{AssetClass.FX_SPOT}', case=False, na=False)",
                        value=QuantityNotation.MONE.value,
                    ),
                ],
            ),
        )
        return MapValue.process(
            source_frame=temp_df,
            params=ParamsMapValue(
                source_attribute=TempColumns.QUANTITY_TYPE,
                target_attribute=TempColumns.QUANTITY_TYPE,
                case_insensitive=True,
                preserve_original=True,
                value_map={
                    "NOMI": QuantityNotation.NOML.value,
                },
            ),
            auditor=self.auditor,
        )

    def _temp_short_sell_indicator(self) -> pd.DataFrame:
        """
        Temporary Column for:
            OrderColumns.EXECUTION_DETAILS_SHORT_SELLING_INDICATOR
            OrderColumns.TRADERS_ALGOS_WAIVER_INDICATORS_SHORT_SELLING_INDICATOR
        Populates from SourceColumns.TRANSACTION_TYPE for Allocations
        and SourceColumns.ORDER_SIDE for Executions, according to the
        following map:
            "SellShort": "SESH"
            "Short": "SESH"
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapValue.process(
                source_frame=self.source_frame,
                params=ParamsMapValue(
                    source_attribute=SourceColumns.TRANSACTION_TYPE,
                    target_attribute=TempColumns.SHORT_SELL,
                    case_insensitive=True,
                    value_map={
                        "SellShort": ShortSellingIndicator.SESH.value,
                        "Short": ShortSellingIndicator.SESH.value,
                    },
                ),
                auditor=self.auditor,
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            return MapValue.process(
                source_frame=self.source_frame,
                params=ParamsMapValue(
                    source_attribute=SourceColumns.ORDER_SIDE,
                    target_attribute=TempColumns.SHORT_SELL,
                    case_insensitive=True,
                    value_map={
                        "SellShort": ShortSellingIndicator.SESH.value,
                        "Short": ShortSellingIndicator.SESH.value,
                    },
                ),
                auditor=self.auditor,
            )

    def _temp_timestamps_order_received(self) -> pd.DataFrame:
        """
        Temporary column used for:
            OrderColumns.DATE
            OrderColumns.TIMESTAMPS_ORDER_RECEIVED
            OrderColumns.TIMESTAMPS_ORDER_SUBMITTED
        Populates from SourceColumns.TRADING_DATE_TIME for Allocations
        SourceColumns.ORDER_DATE and SourceColumns.ORDER_TIMESTAMP for
            Executions or Allocations with SourceColumns.ORDER_ID
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapConditional.process(
                source_frame=pd.concat(
                    [self.source_frame, self.pre_process_df], axis=1
                ),
                params=ParamsMapConditional(
                    target_attribute=TempColumns.ORDER_RECEIVED,
                    cases=[
                        Case(
                            query="index == index",
                            attribute=TempColumns.TRADING_DATE_TIME,
                        ),
                        Case(
                            query=f"`{TempColumns.RECORD_TYPE}` == '{RecordType.MARKET}'",
                            attribute=TempColumns.ORDER_DATE_ORDER_TIMESTAMP,
                        ),
                    ],
                ),
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            return MapAttribute.process(
                source_frame=self.pre_process_df,
                params=ParamsMapAttribute(
                    source_attribute=TempColumns.ORDER_DATE_ORDER_TIMESTAMP,
                    target_attribute=TempColumns.ORDER_RECEIVED,
                ),
                auditor=self.auditor,
            )

    def _temp_traded_quantity(self) -> pd.DataFrame:
        """
        Temporary column for:
            OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
            OrderColumns.TRANSACTION_DETAILS_QUANTITY
        Contains data from
            TempColumns.AGGREGATED_QUANTITY
            TempColumns.INITIAL_QUANTITY
            TempColumns.TRADED_QUANTITY
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapConditional.process(
                source_frame=pd.concat(
                    [self.source_frame, self.pre_process_df], axis=1
                ),
                params=ParamsMapConditional(
                    target_attribute=TempColumns.TRADED_QUANTITY,
                    cases=[
                        Case(
                            query=f"`{SourceColumns.ORDER_ID}`.isnull() | "
                            f"`{TempColumns.RECORD_TYPE}` == '{RecordType.ALLOCATION}'",
                            attribute=TempColumns.INITIAL_QUANTITY,
                        ),
                        Case(
                            query=f"`{TempColumns.AGGREGATION_CODE}`.notnull()",
                            attribute=TempColumns.AGGREGATED_QUANTITY,
                        ),
                    ],
                ),
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.LAST_QTY,
                    target_attribute=TempColumns.TRADED_QUANTITY,
                ),
                auditor=self.auditor,
            )

    def _temp_trading_date_time(self) -> pd.DataFrame:
        """
        Temporary column used for:
            TempColumns.ORDER_RECEIVED
        Populates from SourceColumns.TRADING_DATE_TIME
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.TRADING_DATE_TIME,
                source_attribute_format="%Y-%m-%dT%H:%M:%S.%fZ",
                target_attribute=TempColumns.TRADING_DATE_TIME,
                convert_to="datetime",
            ),
        )

    def _temp_last_fill_time(self) -> pd.DataFrame:
        """
        Temporary column used for:
            OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME
            OrderColumns.TIMESTAMPS_TRADING_DATE_TIME
        Populates from SourceColumns.LAST_FILL_TIME
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.LAST_FILL_TIME,
                source_attribute_format="%Y%m%d-%H:%M:%S.%f",
                target_attribute=TempColumns.LAST_FILL_TIME,
                convert_to="datetime",
            ),
        )

    def _temp_trans_ref_number_order_date(self) -> pd.DataFrame:
        """
        Temporary column used for:
            OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE
        Populates from SourceColumns.TRANSACTION_REFERENCE_NUMBER and
            SourceColumns.ORDER_DATE
        """
        return ConcatAttributes.process(
            source_frame=pd.concat([self.source_frame, self.pre_process_df], axis=1),
            params=ParamsConcatAttributes(
                target_attribute=TempColumns.TRANS_REF_NUMBER_ORDER_DATE,
                source_attributes=[
                    SourceColumns.TRANSACTION_REFERENCE_NUMBER,
                    TempColumns.ORDER_DATE_STRIPPED,
                ],
            ),
        )

    def _temp_validity_period(self) -> pd.DataFrame:
        """
        Temporary column for:
        `OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD
        Populates from SourceColumns.ORDER_TIME_IN_FORCE with the map:
            DAY: DAVY
            GTC: DTCV
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.ORDER_TIME_IN_FORCE,
                target_attribute=TempColumns.VALIDITY_PERIOD,
                case_insensitive=True,
                value_map={"DAY": "DAVY", "GTC": "GTCV"},
            ),
            auditor=self.auditor,
        )

    # Party Identifiers Fields
    def _temp_executing_entity(self) -> pd.DataFrame:
        """
        Temporary column required by
            TempColumns.PARTY_EXECUTING_ENTITY
            TempColumns.BUYER
            TempColumns.SELLER
        Populates from SourceColumns.EXECUTION_ENTITY_IDENTIFICATION_CODE for execution file and market record type.
        Populates from SourceColumns.LE_NAME for allocation records
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapConditional.process(
                source_frame=self.source_frame,
                params=ParamsMapConditional(
                    target_attribute=TempColumns.EXECUTING_ENTITY,
                    cases=[
                        Case(
                            query=f"`{TempColumns.RECORD_TYPE}` == '{RecordType.MARKET}'",
                            attribute=SourceColumns.EXECUTION_ENTITY_IDENTIFICATION_CODE,
                        ),
                        Case(
                            query=f"`{TempColumns.RECORD_TYPE}` == '{RecordType.ALLOCATION}'",
                            attribute=SourceColumns.LE_NAME,
                        ),
                    ],
                ),
            )
        if self.file_type == FileTypes.EXECUTIONS:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.EXECUTION_ENTITY_IDENTIFICATION_CODE,
                    target_attribute=TempColumns.EXECUTING_ENTITY,
                ),
                auditor=self.auditor,
            )

    def _temp_party_ids_executing_entity(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populated from TempColumns.EXECUTING_ENTITY
        Adds id/lei prefix to the column
        """
        executing_entity = self.pre_process_df.loc[
            :, [TempColumns.EXECUTING_ENTITY]
        ].rename(
            columns={TempColumns.EXECUTING_ENTITY: TempColumns.PARTY_EXECUTING_ENTITY}
        )

        return add_id_or_lei(executing_entity, TempColumns.PARTY_EXECUTING_ENTITY)

    def _temp_party_ids_trader(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from SourceColumns.TRADER
        Adds id prefix to the column
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.TRADER,
                target_attribute=TempColumns.TRADER,
                prefix="id:",
            ),
            auditor=self.auditor,
        )

    def _temp_party_ids_execution_within_firm(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from SourceColumns.EXECUTION_WITHIN_FIRM for Allocations
        Adds id prefix to the column
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.EXECUTION_WITHIN_FIRM,
                    target_attribute=TempColumns.EXECUTION_WITHIN_FIRM,
                    prefix="id:",
                    fill_nan="clnt:nore",
                ),
                auditor=self.auditor,
            )

    def _temp_party_ids_investment_decision_maker(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from SourceColumns.INVESTMENT_DECISION_MAKER for Allocations
        Adds id prefix to the column
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.PORTFOLIO_MANAGER,
                    target_attribute=TempColumns.INVESTMENT_DECISION_MAKER,
                    prefix="id:",
                ),
                auditor=self.auditor,
            )

    def _temp_party_ids_buyer_decision_maker(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from SourceColumns.BUYER_DECISION_MAKER_CODE for Allocations,
        if it's not equal to PartyIdentifiers.Buyer
        Adds id/lei prefix to the column
        In aggregated records this field should be pd.NA
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            buyer_decision_maker = MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.BUYER_DECISION_MAKER_CODE,
                    target_attribute=TempColumns.BUYER_DECISION_MAKER,
                ),
                auditor=self.auditor,
            )
            buyer_decision_maker = add_id_or_lei(
                buyer_decision_maker, TempColumns.BUYER_DECISION_MAKER
            )
            return MapConditional.process(
                source_frame=pd.concat(
                    [self.pre_process_df, buyer_decision_maker], axis=1
                ),
                params=ParamsMapConditional(
                    target_attribute=TempColumns.BUYER_DECISION_MAKER,
                    cases=[
                        Case(
                            query=f"`{TempColumns.BUYER}` != `{TempColumns.BUYER_DECISION_MAKER}`",
                            attribute=TempColumns.BUYER_DECISION_MAKER,
                        ),
                        Case(
                            query=f"`{TempColumns.AGGREGATION_CODE}`.notnull()",
                            value=pd.NA,
                        ),
                    ],
                ),
            )

    def _temp_party_ids_seller_decision_maker(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from SourceColumns.SELLER_DECISION_MAKER_CODE for Allocations,
        if it's not equal to PartyIdentifiers.Seller
        Adds id/lei prefix to the column
        In aggregated records this field should be pd.NA
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            seller_decision_maker = MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.SELLER_DECISION_MAKER_CODE,
                    target_attribute=TempColumns.SELLER_DECISION_MAKER,
                ),
                auditor=self.auditor,
            )
            seller_decision_maker = add_id_or_lei(
                seller_decision_maker, TempColumns.SELLER_DECISION_MAKER
            )
            return MapConditional.process(
                source_frame=pd.concat(
                    [self.pre_process_df, seller_decision_maker], axis=1
                ),
                params=ParamsMapConditional(
                    target_attribute=TempColumns.SELLER_DECISION_MAKER,
                    cases=[
                        Case(
                            query=f"`{TempColumns.SELLER}` != `{TempColumns.SELLER_DECISION_MAKER}`",
                            attribute=TempColumns.SELLER_DECISION_MAKER,
                        ),
                        Case(
                            query=f"`{TempColumns.AGGREGATION_CODE}`.notnull()",
                            value=pd.NA,
                        ),
                    ],
                ),
            )

    def _temp_party_ids_buyer(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates, in order of hierarchy from:
        if BUY:
            BUYER_IDENTIFICATION_CODE
        if SELL:
            SourceColumns.COUNTERPARTY if allocation file else SourceColumns.ORDER_EXECUTION_DESTINATION
            SourceColumns.SELLER_IDENTIFICATION_CODE
            SourceColumns.SELLER_DECISION_MAKER_CODE
        Adds id/lei prefix to the column
        In aggregation this field should be filled with 'MULTI' in SELL records
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            file_type_referenced_column = SourceColumns.COUNTERPARTY
        else:
            file_type_referenced_column = SourceColumns.ORDER_EXECUTION_DESTINATION

        buy_condition = (
            f"`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.BUYI.value}'"
        )
        sell_condition = (
            f"`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.SELL.value}'"
        )

        first_case_field = self.get_buyer_seller_first_case_column()

        buyer = MapConditional.process(
            source_frame=pd.concat([self.source_frame, self.pre_process_df], axis=1),
            params=ParamsMapConditional(
                target_attribute=TempColumns.BUYER,
                cases=[
                    Case(
                        query=buy_condition,
                        attribute=first_case_field,
                    ),
                    Case(
                        query=sell_condition,
                        attribute=file_type_referenced_column,
                    ),
                    Case(
                        query=f"{sell_condition} & `{file_type_referenced_column}`.isnull()",
                        attribute=SourceColumns.SELLER_IDENTIFICATION_CODE,
                    ),
                    Case(
                        query=f"{sell_condition} & `{file_type_referenced_column}`.isnull() & "
                        f"`{SourceColumns.SELLER_IDENTIFICATION_CODE}`.isnull()",
                        attribute=SourceColumns.SELLER_DECISION_MAKER_CODE,
                    ),
                ],
            ),
        )
        buyer = add_id_or_lei(buyer, TempColumns.BUYER)
        sell_aggregated_mask = (
            self.pre_process_df[TempColumns.AGGREGATION_CODE].notnull()
            & self.pre_process_df[TempColumns.BUY_SELL_INDICATOR]
            == BuySellIndicator.SELL
        )
        buyer.loc[sell_aggregated_mask, TempColumns.BUYER] = MULTI
        return buyer

    def _temp_party_ids_seller(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates, in order of hierarchy from:
        if SELL:
            BUYER_IDENTIFICATION_CODE
        if BUY:
            SourceColumns.COUNTERPARTY if allocation file else SourceColumns.ORDER_EXECUTION_DESTINATION
            SourceColumns.BUYER_IDENTIFICATION_CODE
            SourceColumns.BUYER_DECISION_MAKER_CODE
        Adds id/lei prefix to the column
        In aggregation this field should be filled with 'MULTI' in BUYI records
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            file_type_referenced_column = SourceColumns.COUNTERPARTY
        else:
            file_type_referenced_column = SourceColumns.ORDER_EXECUTION_DESTINATION

        buy_condition = (
            f"`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.BUYI.value}'"
        )
        sell_condition = (
            f"`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.SELL.value}'"
        )

        first_case_field = self.get_buyer_seller_first_case_column()

        seller = MapConditional.process(
            source_frame=pd.concat([self.source_frame, self.pre_process_df], axis=1),
            params=ParamsMapConditional(
                target_attribute=TempColumns.SELLER,
                cases=[
                    Case(
                        query=sell_condition,
                        attribute=first_case_field,
                    ),
                    Case(
                        query=buy_condition,
                        attribute=file_type_referenced_column,
                    ),
                    Case(
                        query=f"{buy_condition} & `{file_type_referenced_column}`.isnull()",
                        attribute=SourceColumns.BUYER_IDENTIFICATION_CODE,
                    ),
                    Case(
                        query=f"{buy_condition} & `{file_type_referenced_column}`.isnull() & "
                        f"`{SourceColumns.BUYER_IDENTIFICATION_CODE}`.isnull()",
                        attribute=SourceColumns.BUYER_DECISION_MAKER_CODE,
                    ),
                ],
            ),
        )
        seller = add_id_or_lei(seller, TempColumns.SELLER)
        buy_aggregated_mask = (
            self.pre_process_df[TempColumns.AGGREGATION_CODE].notnull()
            & self.pre_process_df[TempColumns.BUY_SELL_INDICATOR]
            == BuySellIndicator.BUYI
        )
        seller.loc[buy_aggregated_mask, TempColumns.SELLER] = MULTI
        return seller

    @staticmethod
    def get_buyer_seller_first_case_column():
        """
        method used to facilitate thornbridge override
        """
        return TempColumns.EXECUTING_ENTITY

    def _temp_party_ids_counterparty_and_client(self) -> pd.DataFrame:
        """
        Temporary Columns for PartyIdentifiers
        Counterparty is populated from TempColumn.BUYER or TempColumns.SELLER.
        Client is populated from either LE Name or the Counterparty
        Adds id/lei prefix to the column
        In aggregated records this field should be filled with 'MULTI'
        """
        result_df = MapConditional.process(
            source_frame=self.pre_process_df,
            params=ParamsMapConditional(
                target_attribute=TempColumns.COUNTERPARTY,
                cases=[
                    Case(
                        query=f"`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.BUYI.value}'",
                        attribute=TempColumns.SELLER,
                    ),
                    Case(
                        query=f"`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.SELL.value}'",
                        attribute=TempColumns.BUYER,
                    ),
                    Case(
                        query=f"`{TempColumns.AGGREGATION_CODE}`.notnull()",
                        value=MULTI,
                    ),
                ],
            ),
        )
        client_df = add_id_or_lei(
            dataframe=self.source_frame.loc[:, [SourceColumns.LE_NAME]],
            source_attribute=SourceColumns.LE_NAME,
        )
        result_df[TempColumns.CLIENT] = client_df.loc[:, SourceColumns.LE_NAME].fillna(
            result_df[TempColumns.COUNTERPARTY]
        )
        return result_df

    # Instrument Identifiers Fields
    def _temp_instr_ids_asset_class(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates based on:
            Future if SourceColumns.FUTURE_EXPIRATION_DATE is populated
            Option if SourceColumns.OPTION_TYPE
            FX Forward if SourceColumns.INSTRUMENT_CLASSIFICATION starts with 'JF'
        """
        instr_class_starts_with_jf = f"(`{SourceColumns.INSTRUMENT_CLASSIFICATION}`.fillna('').str.upper().str.startswith('JF'))"
        option_type_populated = f"(`{SourceColumns.OPTION_TYPE}`.notnull())"
        future_exp_date_populated = (
            f"(`{SourceColumns.FUTURE_EXPIRATION_DATE}`.notnull())"
        )
        description_contains_fx_forward = (
            f"`{SourceColumns.DESCRIPTION}`.str.match('{AssetClass.FX_FORWARD}', case=False, na=False) "
            f"| `{SourceColumns.INSTRUMENT_FULL_NAME}`.str.match('{AssetClass.FX_FORWARD}', case=False, na=False)"
        )
        description_contains_fx_spot = (
            f"`{SourceColumns.DESCRIPTION}`.str.match('{AssetClass.FX_SPOT}', case=False, na=False) "
            f"| `{SourceColumns.INSTRUMENT_FULL_NAME}`.str.match('{AssetClass.FX_SPOT}', case=False, na=False)"
        )
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.ASSET_CLASS,
                cases=[
                    Case(
                        query=f"{future_exp_date_populated} & ~{instr_class_starts_with_jf}",
                        value=AssetClass.FUTURE,
                    ),
                    Case(
                        query=f"{description_contains_fx_forward}",
                        value=AssetClass.FX_FORWARD,
                    ),
                    Case(
                        query=f"{description_contains_fx_spot}",
                        value=AssetClass.FX_SPOT,
                    ),
                    Case(
                        query=f"{option_type_populated}",
                        value=AssetClass.OPTION,
                    ),
                ],
            ),
        )

    def _temp_instr_ids_bbgid(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates from SourceColumns.TICKER
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.TICKER,
                target_attribute=TempColumns.BBGID,
            ),
            auditor=self.auditor,
        )

    def _temp_underlying_symbol_expiry_code(self) -> pd.DataFrame:
        """
        It will create a temporary column for the underlying symbol and expiry code
        that are valid futures contracts.
        """
        underlying_symbol_and_expiry_code_regex = f"^[A-Z0-9]+{FUTURES_CONTRACT_REGEX}$"

        is_valid_futures_contract_mask = self.source_frame[
            SourceColumns.TICKER
        ].str.fullmatch(underlying_symbol_and_expiry_code_regex, case=False, na=False)

        tmp_df: pd.DataFrame = pd.DataFrame(
            data={
                TempColumns.UNDERLYING_SYMBOL_EXPIRY_CODE: pd.NA,
            },
            index=self.source_frame.index,
        )

        tmp_df.loc[
            is_valid_futures_contract_mask, TempColumns.UNDERLYING_SYMBOL_EXPIRY_CODE
        ] = self.source_frame.loc[is_valid_futures_contract_mask, SourceColumns.TICKER]

        return tmp_df

    def _temp_instr_ids_currency(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates from SourceColumns.PRICE_CURRENCY for Allocations
        and TempColumns.CURRENCY for Executions,
        or TempColumns.DESCRIPTION_CURRENCY_2
        """
        fx_forward_or_fx_spot = f"(`{TempColumns.ASSET_CLASS}`.isin(['{AssetClass.FX_FORWARD}', '{AssetClass.FX_SPOT}']))"
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapConditional.process(
                source_frame=self.pre_process_df,
                params=ParamsMapConditional(
                    target_attribute=TempColumns.INSTR_IDS_CURRENCY,
                    cases=[
                        Case(
                            query=f"{fx_forward_or_fx_spot}",
                            attribute=TempColumns.DESCRIPTION_CURRENCY_2,
                        ),
                        Case(
                            query=f"~{fx_forward_or_fx_spot}",
                            attribute=TempColumns.PRICE_CURRENCY,
                        ),
                    ],
                ),
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            return MapConditional.process(
                source_frame=self.pre_process_df,
                params=ParamsMapConditional(
                    target_attribute=TempColumns.INSTR_IDS_CURRENCY,
                    cases=[
                        Case(
                            query=f"{fx_forward_or_fx_spot}",
                            attribute=TempColumns.DESCRIPTION_CURRENCY_1,
                        ),
                        Case(
                            query=f"~{fx_forward_or_fx_spot}",
                            attribute=TempColumns.CURRENCY,
                        ),
                    ],
                ),
            )

    def _temp_instr_ids_expiry_date(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates from SourceColumns.FUTURE_EXPIRATION_DATE, TempColumns.EXPIRY_DATE_SOURCE,
        SourceColumns.OPTION_EXPIRATION_DATE or SourceColumns.DESCRIPTION
        """
        fx_forward = f"(`{TempColumns.ASSET_CLASS}` == '{AssetClass.FX_FORWARD}')"
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapConditional.process(
                source_frame=self.pre_process_df,
                params=ParamsMapConditional(
                    target_attribute=TempColumns.EXPIRY_DATE,
                    cases=[
                        Case(
                            query=f"{fx_forward}",
                            attribute=TempColumns.DESCRIPTION_DATE,
                        ),
                        Case(
                            query=f"~{fx_forward} & `{TempColumns.EXPIRY_DATE_SOURCE}`.notnull()",
                            attribute=TempColumns.EXPIRY_DATE_SOURCE,
                        ),
                        Case(
                            query=f"~{fx_forward} & (`{TempColumns.FUTURE_EXPIRATION_DATE}`.notnull()) "
                            f"& (`{TempColumns.EXPIRY_DATE_SOURCE}`.isnull())",
                            attribute=TempColumns.FUTURE_EXPIRATION_DATE,
                        ),
                        Case(
                            query=f"~{fx_forward} & (`{TempColumns.OPTION_EXPIRATION_DATE}`.notnull()) "
                            f"& (`{TempColumns.FUTURE_EXPIRATION_DATE}`.isnull())"
                            f"& (`{TempColumns.EXPIRY_DATE_SOURCE}`.isnull())",
                            attribute=TempColumns.OPTION_EXPIRATION_DATE,
                        ),
                    ],
                ),
            )
        if self.file_type == FileTypes.EXECUTIONS:
            return MapConditional.process(
                source_frame=self.pre_process_df,
                params=ParamsMapConditional(
                    target_attribute=TempColumns.EXPIRY_DATE,
                    cases=[
                        Case(
                            query=f"{fx_forward}",
                            attribute=TempColumns.DESCRIPTION_DATE,
                        ),
                        Case(
                            query=f"~{fx_forward} & (`{TempColumns.FUTURE_EXPIRATION_DATE}`.notnull()) "
                            f"& (`{TempColumns.EXPIRY_DATE_SOURCE}`.isnull())",
                            attribute=TempColumns.FUTURE_EXPIRATION_DATE,
                        ),
                        Case(
                            query=f"~{fx_forward} & (`{TempColumns.OPTION_EXPIRATION_DATE}`.notnull()) "
                            f"& (`{TempColumns.FUTURE_EXPIRATION_DATE}`.isnull())"
                            f"& (`{TempColumns.EXPIRY_DATE_SOURCE}`.isnull())",
                            attribute=TempColumns.OPTION_EXPIRATION_DATE,
                        ),
                    ],
                ),
            )

    def _temp_instr_ids_isin(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates from SourceColumns.INSTRUMENT_IDENTIFICATION_CODE
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.INSTRUMENT_IDENTIFICATION_CODE,
                target_attribute=TempColumns.ISIN,
            ),
            auditor=self.auditor,
        )

    def _temp_instr_ids_notional_currency_2(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates according to the following priority:
            TempColumns.DESCRIPTION_CURRENCY_2
            TempColumns.NOTIONAL_CURRENCY_2
            TempColumns.CURRENCY_2
        """
        fx_forward_or_fx_spot = f"(`{TempColumns.ASSET_CLASS}`.isin(['{AssetClass.FX_FORWARD}', '{AssetClass.FX_SPOT}']))"
        has_notional_currency_2 = f"(`{TempColumns.NOTIONAL_CURRENCY_2}`.notnull())"
        has_currency_2 = f"(`{TempColumns.CURRENCY_2}`.notnull())"
        return MapConditional.process(
            source_frame=self.pre_process_df,
            params=ParamsMapConditional(
                target_attribute=TempColumns.INSTR_IDS_NOTIONAL_CURRENCY_2,
                cases=[
                    Case(
                        query=f"{fx_forward_or_fx_spot}",
                        attribute=TempColumns.DESCRIPTION_CURRENCY_2,
                    ),
                    Case(
                        query=f"~{fx_forward_or_fx_spot} & {has_notional_currency_2}",
                        attribute=TempColumns.NOTIONAL_CURRENCY_2,
                    ),
                    Case(
                        query=f"~{fx_forward_or_fx_spot} & ~{has_notional_currency_2} & {has_currency_2}",
                        attribute=TempColumns.CURRENCY_2,
                    ),
                ],
            ),
        )

    def _temp_instr_ids_options_strike_price(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates from SourceColumns.OPTION_STRIKE
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ParamsConvertMinorToMajor(
                    source_ccy_attribute=SourceColumns.PRICE_CURRENCY,
                    source_price_attribute=SourceColumns.OPTION_STRIKE,
                    target_price_attribute=TempColumns.OPTION_STRIKE_PRICE,
                ),
            )
        elif self.file_type == FileTypes.EXECUTIONS:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ParamsConvertMinorToMajor(
                    source_ccy_attribute=SourceColumns.CURRENCY,
                    source_price_attribute=SourceColumns.OPTION_STRIKE,
                    target_price_attribute=TempColumns.OPTION_STRIKE_PRICE,
                ),
            )

    def _temp_instr_ids_option_type(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates from SourceColumns.OPTION_TYPE
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.OPTION_TYPE,
                target_attribute=TempColumns.OPTION_TYPE,
            ),
            auditor=self.auditor,
        )

    def _temp_instr_ids_underlying_isin(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates from SourceColumns.OPTION_TYPE
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.UNDERLYING_INSTRUMENT_CODE,
                target_attribute=TempColumns.UNDERLYING_ISIN,
            ),
            auditor=self.auditor,
        )

    def _temp_instr_ids_underlying_symbol(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates from Asset class mapping. If asset class is Option
        then map `Option Contract Bloomberg Root Code`
        elif Future then map `Future Bloomberg Root`
        """
        return MapConditional.process(
            source_frame=pd.concat([self.source_frame, self.pre_process_df], axis=1),
            params=ParamsMapConditional(
                target_attribute=TempColumns.UNDERLYING_SYMBOL,
                cases=[
                    Case(
                        query=f"`{TempColumns.ASSET_CLASS}` == '{AssetClass.OPTION}'",
                        attribute=SourceColumns.OPTION_CONTRACT_BBG_ROOT_CODE,
                    ),
                    Case(
                        query=f"`{TempColumns.ASSET_CLASS}` == '{AssetClass.FUTURE}'",
                        attribute=SourceColumns.FUTURE_BLOOMBERG_ROOT,
                    ),
                ],
            ),
        )

    def _temp_instr_ids_venue(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates from SourceColumns.EXCHANGE_MIC_CODE
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.EXCHANGE_MIC_CODE,
                target_attribute=TempColumns.VENUE,
            ),
            auditor=self.auditor,
        )

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""
