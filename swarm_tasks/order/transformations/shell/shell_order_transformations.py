import copy
import os
from typing import Dict
from typing import List
from typing import NoReturn

import numpy as np
import pandas as pd
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_attribute import CastTo
from se_core_tasks.map.map_conditional import Case
from se_core_tasks.map.map_value import Params as MapValueParams
from se_core_tasks.map.map_value import run_map_value
from se_core_tasks.utils.datetime import DatetimeFormat
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import DeliveryType
from se_elastic_schema.static.mifid2 import HierarchyEnum
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import ShortSellingIndicator
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order.transformations.bbg.audt.static import Events
from se_trades_tasks.order.transformations.bbg.audt.static import future_expiry_months
from se_trades_tasks.order.transformations.bbg.audt.static import SourceColumns
from se_trades_tasks.order_and_tr.party.static import PartiesFields
from se_trades_tasks.order_and_tr.static import AssetClass
from se_trades_tasks.order_and_tr.static import PartyPrefix
from se_trades_tasks.utilities.data_util import propagate_values_by_group

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.order.feed.shell.static import AddlInfoColumns
from swarm_tasks.order.feed.shell.static import ES_COL_PREFIX
from swarm_tasks.order.feed.shell.static import ES_COLS_NOT_NEEDED_IN_POST_PROCESS
from swarm_tasks.order.feed.shell.static import ES_COLUMNS_FOR_AGGREGATE_BACKFILL
from swarm_tasks.order.feed.shell.static import (
    ES_COLUMNS_FOR_AGGREGATE_BACKFILL_WITH_PREFIX,
)
from swarm_tasks.order.feed.shell.static import Function
from swarm_tasks.order.feed.shell.static import QUANTITY_COLS
from swarm_tasks.order.feed.shell.static import ShellPrefix
from swarm_tasks.order.feed.shell.static import TempColumns
from swarm_tasks.order.feed.shell.static import TIME_ZONE
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.generic.get_tenant_lei import Params as ParamsGetTenantLEI
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.concat.concat_attributes import Prefix
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.datetime.join_date_and_time import JoinDateAndTimeFormat
from swarm_tasks.transform.datetime.join_date_and_time import (
    Params as ParamsJoinDateAndTimeFormat,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_static import MapStatic
from swarm_tasks.transform.map.map_static import Params as ParamsMapStatic
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.utilities.static import Delimiters


class ShellOrderTransformations(AbstractOrderTransformations):
    """
    Specs: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2592997377/Order+Bloomberg+AUDT+Shell
    """

    def __init__(self, *args, **kwargs):

        self.aggregated_order_info = (
            kwargs["source_frame"]
            .loc[:, ES_COLUMNS_FOR_AGGREGATE_BACKFILL_WITH_PREFIX]
            .fillna(pd.NA)
        )

        kwargs["source_frame"] = kwargs["source_frame"].drop(
            ES_COLUMNS_FOR_AGGREGATE_BACKFILL_WITH_PREFIX, axis=1
        )

        super().__init__(*args, **kwargs)

    def _pre_process(self):
        """
        Temporary columns needed for multiple fields
        """

        # Keeping a reference of SourceColumns.DELIV_TYPE as we need its reference before 0 rows are converted tp pd.NA
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self.source_frame.loc[:, [SourceColumns.DELIV_TYPE]],
            ],
            axis=1,
        )

        # Converting all 0 populated fields with pd.NA, except for SourceColumns.IDENTTYPE
        columns_except_identtype = [
            col for col in self.source_frame.columns if col != SourceColumns.IDENTTYPE
        ]
        self.source_frame.loc[:, columns_except_identtype] = self.source_frame.loc[
            :, columns_except_identtype
        ].replace({0: pd.NA, "0": pd.NA})

        del columns_except_identtype

        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_has_aggregate_info_from_es(),
                self._temp_buy_sell(),
                self._temp_date_time(),
                self._temp_instr_fallback_bond_maturity_date(),
                self._temp_instr_fallback_delivery_type(),
                self._temp_instr_fallback_expiry_date(),
                self._temp_instr_fallback_instrument_full_name(),
                self._temp_instr_ids_asset_class(),
                self._temp_order_id(),
                self._temp_party_ids_counterparty(),
                self._temp_party_ids_execution_within_firm(),
                self._temp_party_ids_investment_decision_maker(),
                self._temp_route(),
                self._temp_settlement_date(),
                self._temp_transaction_reference_number(),
                self._temp_order_from_auditidtkt(),
                self._temp_trading_capacity(),
            ],
            axis=1,
        )

        # these columns need access to others defined above
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._propagate_missing_fields(),  # creates trader_uuid, ord_typ, currency, quantity, account_propagated; needs order_id
                self._temp_aggregated_order_id(),  # needs order id
                self._temp_buy_sell_indicator(),  # needs buy_sell
                self._temp_es_aggregated_order_clients(),  # needs order id
                self._temp_instr_fallback_instrument_id_code(),  # needs order id
                self._temp_order_status(),  # needs order_from_auditidtkt
                self._temp_party_ids_executing_entity(),  # need temp_executing_entity
                self._temp_party_ids_seller(),  # needs counterparty
                self._temp_synthetic_order_column(),  # needs order id
                self._temp_sys_date(),  # needs date_time
                self._temp_sys_date_time(),  # needs date_time
            ],
            axis=1,
        )

        # these columns need access to others defined above
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_currency_code(),  # needs currency
                self._temp_initial_quantity(),  # needs synth order, order id
                self._temp_parent_sys_date(),  # needs sys date time
                self._temp_party_ids_client(),  # needs account propagated
                self._temp_party_ids_trader(),  # needs trader uuid
                self._temp_price(),  # needs currency,
            ],
            axis=1,
        )

        # these columns need access to others defined above
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_aggregate_fields(),  # needs client, aggregated order id, es aggregated order clients
                self._temp_party_ids_buyer(),  # needs client, executing entity
                self._temp_quantities(),  # needs initial quantity propagated
            ],
            axis=1,
        )

    def process(self) -> pd.DataFrame:
        self.pre_process()
        self.buy_sell()
        self.data_source_name()
        self.date()
        self.execution_details_aggregated_order()
        self.execution_details_buy_sell_indicator()
        self.execution_details_order_status()
        self.execution_details_order_type()
        self.execution_details_outgoing_order_addl_info()
        self.execution_details_short_selling_indicator()
        self.execution_details_trading_capacity()
        self.execution_details_validity_period()
        self.execution_details_waiver_indicator()
        self.hierarchy()
        self.id()
        self.meta_model()
        self.order_class()
        self.order_identifiers_aggregated_order_id_code()
        self.order_identifiers_initial_order_designation()
        self.order_identifiers_internal_order_id_code()
        self.order_identifiers_order_id_code()
        self.order_identifiers_order_routing_code()
        self.order_identifiers_sequence_number()
        self.order_identifiers_trading_venue_transaction_id_code()
        self.order_identifiers_transaction_ref_no()
        self.price_forming_data_initial_quantity()
        self.price_forming_data_price()
        self.price_forming_data_traded_quantity()
        self.price_forming_data_remaining_quantity()
        self.report_details_transaction_ref_no()
        self.source_index()
        self.source_key()
        self.timestamps_order_received()
        self.timestamps_order_status_updated()
        self.timestamps_order_submitted()
        self.timestamps_trading_date_time()
        self.traders_algos_waivers_indicators_short_selling_indicator()
        self.traders_algos_waivers_indicators_waiver_indicator()
        self.transaction_details_buy_sell_indicator()
        self.transaction_details_price()
        self.transaction_details_price_currency()
        self.transaction_details_price_notation()
        self.transaction_details_quantity()
        self.transaction_details_quantity_currency()
        self.transaction_details_quantity_notation()
        self.transaction_details_settlement_date()
        self.transaction_details_trading_capacity()
        self.transaction_details_trading_date_time()
        self.transaction_details_ultimate_venue()
        self.transaction_details_upfront_payment()
        self.transaction_details_upfront_payment_currency()
        self.transaction_details_venue()
        self.market_identifiers_parties()
        self.market_identifiers()
        self.post_process()
        return self.target_df

    def _post_process(self):
        inst_fallback_columns = [
            TempColumns.BOND_MATURITY_DATE,
            TempColumns.EXPIRY_DATE_FALLBACK,
            TempColumns.DELIVERY_TYPE,
            TempColumns.ISIN,
            TempColumns.INSTR_FULL_NAME,
        ]

        source_columns_for_instr_fallback = [
            SourceColumns.ASSET_CLASS,
            SourceColumns.BB_ID,
            SourceColumns.CONTRACT_SIZE,
            SourceColumns.FIGI,
            SourceColumns.PARSE_KEY,
            SourceColumns.TICKER,
            SourceColumns.IDENTIFIER,
        ]

        synth_newo_columns = [TempColumns.SYNTH_ORDER]

        self.target_df = pd.concat(
            [
                self.target_df,
                self.source_frame.loc[:, source_columns_for_instr_fallback],
                self.pre_process_df.loc[:, inst_fallback_columns],
                self.pre_process_df.loc[:, synth_newo_columns],
            ],
            axis=1,
        )
        self.target_df.loc[:, TempColumns.IS_CREATED_THROUGH_FALLBACK] = True
        self.target_df.loc[:, TempColumns.NEWO_IN_FILE] = False

        # this logic below fetches data for aggregated orders that have it missing by looking up the orders
        # they aggregate from and copying over the values when available
        list_of_cols_to_update = set(ES_COLUMNS_FOR_AGGREGATE_BACKFILL) - set(
            ES_COLS_NOT_NEEDED_IN_POST_PROCESS
        )
        field_es_lookup_column_combos = [
            (col, ES_COL_PREFIX + col) for col in list_of_cols_to_update
        ]
        field_es_lookup_column_combos.extend(
            [
                (TempColumns.ISIN, ES_COL_PREFIX + TempColumns.ISIN),
                (
                    TempColumns.INSTR_FULL_NAME,
                    ES_COL_PREFIX + TempColumns.INSTR_FULL_NAME,
                ),
                (
                    ModelPrefix.ORDER_DOT + OrderColumns.BUY_SELL,
                    ES_COL_PREFIX + OrderColumns.BUY_SELL,
                ),
                (
                    ModelPrefix.ORDER_STATE_DOT + OrderColumns.BUY_SELL,
                    ES_COL_PREFIX + OrderColumns.BUY_SELL,
                ),
                (
                    ModelPrefix.ORDER_DOT + OrderColumns.PRICE_FORMING_DATA_PRICE,
                    ES_COL_PREFIX + OrderColumns.PRICE_FORMING_DATA_PRICE,
                ),
                (
                    ModelPrefix.ORDER_DOT + OrderColumns.TRANSACTION_DETAILS_PRICE,
                    ES_COL_PREFIX + OrderColumns.TRANSACTION_DETAILS_PRICE,
                ),
            ]
        )

        for column_combo in field_es_lookup_column_combos:
            self._fetch_data_from_es_and_in_file_lookup(
                df_to_update=self.target_df,
                column_to_update=column_combo[0],
                es_lookup_column=column_combo[1],
            )

        # the quantity fields need to be added up
        for column in QUANTITY_COLS:
            self._fetch_and_sum_data_from_es_and_in_file_lookup(column_to_update=column)

    def _buy_sell(self) -> pd.DataFrame:
        """
        Populates from TempColumns.BUY_SELL
        Adds '.order' and '.orderState' prefixes
        """
        return pd.concat(
            [
                MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.BUY_SELL,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.BUY_SELL,
                        ),
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.BUY_SELL,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.BUY_SELL,
                        ),
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        """
        Static value: SAMCo BBG AUDT
        """
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_value="SAMCo BBG AUDT",
                target_attribute=OrderColumns.DATA_SOURCE_NAME,
            ),
        )

    def _date(self) -> pd.DataFrame:
        """
        Maps OrderColumns.DATE deriving its value from TempColumns.SYS_DATE.
        Where-ever TempColumns.SYS_DATE is blank TempColumns.SETTLE_DT is used.
        """
        return ConvertDatetime.process(
            source_frame=self.pre_process_df,
            params=ParamsConvertDatetime(
                source_attribute=TempColumns.PARENT_SYS_DATE_TIME,
                source_attribute_format="%Y-%m-%dT%H:%M:%S.%fZ",
                target_attribute=OrderColumns.DATE,
                convert_to=ConvertTo.DATE,
            ),
        )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        """
        Populates with True or False according to if SourceColumns.EVENT is one of
            'ADDED TO BASKET', "AGGREGATED FROM', 'AGGREGATED TO'
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.EXECUTION_DETAILS_AGGREGATED_ORDER,
                cases=[
                    Case(
                        query=f"`{SourceColumns.EVENT}`.str.fullmatch('{Events.ADDED_TO_BASKET}|{Events.AGGREGATED_FROM}|{Events.AGGREGATED_TO}', case=False, na=False)",
                        value=True,
                    ),
                ],
            ),
        ).fillna(False)

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
        from TempColumns.BUY_SELL_INDICATOR temporary column
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.BUY_SELL_INDICATOR].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_order_status(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_ORDER_STATUS from SourceColumns.EVENT
        """
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, TempColumns.ORDER_STATUS].values,
                    index=self.pre_process_df.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        )
                    ],
                ),
                pd.DataFrame(
                    data=OrderStatus.NEWO.value,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_ORDER_TYPE from SourceColumns.ORD_TYPE
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.ORD_TYPE].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.EXECUTION_DETAILS_ORDER_TYPE],
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO
        It will concatenate the values, separated by a comma (',')
        Empty fields are shown as empty strings.
        """
        filter_cols = [
            SourceColumns.FUNCTION,
            SourceColumns.USER,
            SourceColumns.AUDIT_ID,
            SourceColumns.MTKT_NUM,
            SourceColumns.TKT_NUM,
            SourceColumns.CLIENT_ORDER_ID,
            SourceColumns.BROKER,
            SourceColumns.PRICE,
            SourceColumns.ACCOUNT,
            SourceColumns.ORDER_NAME,
            SourceColumns.INSTR,
            SourceColumns.OLD_INSTR,
            SourceColumns.ROUTE_INSTRUCTION,
            SourceColumns.EXEC_INSTR,
            SourceColumns.OLD_EXEC_INSTR,
            SourceColumns.BASKET_NAME,
            SourceColumns.FULL_QUANTITY,
            SourceColumns.QUANTITY,
            SourceColumns.PRINCIPAL,
            SourceColumns.ASSET_CLASS,
            SourceColumns.SECURITY,
            SourceColumns.TRAD_DESK,
            SourceColumns.REASON_CODE,
            SourceColumns.IDENTTYPE,
            SourceColumns.SYS_UTCDATETIME,
            SourceColumns.EXTERNAL_ORDER_ID,
        ]
        replace_col_names = [
            AddlInfoColumns.FUNCTION,
            AddlInfoColumns.USER,
            AddlInfoColumns.AUDIT_ID,
            AddlInfoColumns.MASTER_NUMBER,
            AddlInfoColumns.TICKET_NUMBER,
            AddlInfoColumns.CLIENT_ORDER_ID,
            AddlInfoColumns.BROKER,
            AddlInfoColumns.PRICE,
            AddlInfoColumns.ACCOUNT,
            AddlInfoColumns.ORDER_NAME,
            AddlInfoColumns.ORDER_INSTR,
            AddlInfoColumns.OLD_INSTR,
            AddlInfoColumns.ROUTE_INSTR,
            AddlInfoColumns.EXECUTIONS_INSTR,
            AddlInfoColumns.OLD_EXECUTION_INSTR,
            AddlInfoColumns.BASKET_NAME,
            AddlInfoColumns.TRADING_DESK,
            AddlInfoColumns.REASON_CODE,
            AddlInfoColumns.IDENT_TYPE,
            AddlInfoColumns.UTC_TIME,
            AddlInfoColumns.VENUE_ORDER_ID,
            # This needs to be the last column; do not change ordering
            AddlInfoColumns.MEASUREMENT_UNIT,
        ]
        temp_df = self.source_frame.loc[:, filter_cols]

        sft_asset_class = (
            temp_df.loc[:, SourceColumns.ASSET_CLASS]
            .astype(str)
            .str.fullmatch("SFT", case=False, na=False)
        )
        generic_security = (
            temp_df.loc[:, SourceColumns.SECURITY]
            .astype(str)
            .str.fullmatch("GENERIC", case=False, na=False)
        )
        sft_and_generic = sft_asset_class & generic_security

        temp_df[TempColumns.TEMP_COL_2] = temp_df[SourceColumns.FULL_QUANTITY]
        temp_df.loc[sft_and_generic, TempColumns.TEMP_COL_2] = temp_df.loc[
            sft_and_generic, SourceColumns.PRINCIPAL
        ]

        temp_df[TempColumns.TEMP_COL_1] = (
            temp_df[TempColumns.TEMP_COL_2] / temp_df[SourceColumns.QUANTITY]
        )
        temp_df = temp_df.drop(
            columns=[
                SourceColumns.FULL_QUANTITY,
                SourceColumns.QUANTITY,
                SourceColumns.PRINCIPAL,
                SourceColumns.ASSET_CLASS,
                SourceColumns.SECURITY,
                TempColumns.TEMP_COL_2,
            ]
        )
        temp_df.columns = replace_col_names
        temp_df = temp_df.fillna("")
        return ConcatAttributes.process(
            source_frame=temp_df,
            params=ParamsConcatAttributes(
                source_attributes=[
                    AddlInfoColumns.FUNCTION,
                    AddlInfoColumns.USER,
                    AddlInfoColumns.AUDIT_ID,
                    AddlInfoColumns.MASTER_NUMBER,
                    AddlInfoColumns.TICKET_NUMBER,
                    AddlInfoColumns.CLIENT_ORDER_ID,
                    AddlInfoColumns.BROKER,
                    AddlInfoColumns.PRICE,
                    AddlInfoColumns.ACCOUNT,
                    AddlInfoColumns.ORDER_NAME,
                    AddlInfoColumns.ORDER_INSTR,
                    AddlInfoColumns.OLD_INSTR,
                    AddlInfoColumns.ROUTE_INSTR,
                    AddlInfoColumns.EXECUTIONS_INSTR,
                    AddlInfoColumns.OLD_EXECUTION_INSTR,
                    AddlInfoColumns.BASKET_NAME,
                    AddlInfoColumns.MEASUREMENT_UNIT,
                    AddlInfoColumns.TRADING_DESK,
                    AddlInfoColumns.REASON_CODE,
                    AddlInfoColumns.IDENT_TYPE,
                    AddlInfoColumns.UTC_TIME,
                    AddlInfoColumns.VENUE_ORDER_ID,
                ],
                prefix=Prefix.attribute_name.value,
                delimiter=Delimiters.COMMA_SPACE + Delimiters.NEW_LINE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
            ),
        )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.SIDE column
        """
        short_query = (
            f"`{SourceColumns.SIDE}`.str.fullmatch('SHORT', case=False, na=False)"
        )
        sell_query = f"`{TempColumns.BUY_SELL_INDICATOR}`.str.fullmatch('{BuySellIndicator.SELL.value}', case=False, na=False)"

        return MapConditional.process(
            source_frame=pd.concat(
                [
                    self.source_frame.loc[:, SourceColumns.SIDE],
                    self.pre_process_df.loc[:, TempColumns.BUY_SELL_INDICATOR],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=OrderColumns.EXECUTION_DETAILS_SHORT_SELLING_INDICATOR,
                cases=[
                    Case(
                        query=sell_query,
                        value=ShortSellingIndicator.SELL.value,
                    ),
                    Case(
                        query=short_query,
                        value=ShortSellingIndicator.SESH.value,
                    ),
                ],
            ),
        )

    def _execution_details_stop_price(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populates from TempColumns.TRADING_CAPACITY
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.TRADING_CAPACITY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY],
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD from SourceColumns.TIF
        """
        temp_df = MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.TIF,
                target_attribute=TempColumns.EXECUTION_DETAILS_VALIDITY_PERIOD,
                case_insensitive=True,
                value_map={
                    "GTC": "GTCV",
                    "DAY": "DAVY",
                },
            ),
            auditor=self.auditor,
        )

        return MapAttribute.process(
            source_frame=temp_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.EXECUTION_DETAILS_VALIDITY_PERIOD,
                target_attribute=OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD,
                cast_to=CastTo.STRING_LIST.value,
                list_delimiter=Delimiters.SEMI_COLON,
            ),
            auditor=self.auditor,
        )

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.WAIVER_INDICATORS
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.WAIVER_INDICATORS,
                target_attribute=OrderColumns.EXECUTION_DETAILS_WAIVER_INDICATOR,
                cast_to=CastTo.STRING_LIST.value,
                list_delimiter=Delimiters.SEMI_COLON,
            ),
            auditor=self.auditor,
        )

    def _financing_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _hierarchy(self) -> pd.DataFrame:
        """
        Populates with "Standalone"
        """
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_value=HierarchyEnum.STANDALONE.value,
                target_attribute=OrderColumns.HIERARCHY,
            ),
        )

    def _id(self) -> pd.DataFrame:
        """
        Populates OrderColumns.ID from TempColumns.ORDER_ID
        """
        return pd.concat(
            [
                MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.ORDER_ID,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.ID,
                        ),
                    ),
                ),
                MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.ORDER_ID,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.ID,
                        ),
                    ),
                ),
            ],
            axis=1,
        )

    def _is_discretionary(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_iceberg(self):
        """Not Implemented"""

    def _is_repo(self):
        """Not Implemented"""

    def _is_synthetic(self):
        """Not Implemented"""

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        """Not Implemented"""

    def _market_identifiers(self) -> pd.DataFrame:
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """Instruments for this flow are implemented with Fallback only"""

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """
        Populates from the following temporary columns:
            TempColumns.CLIENT
            TempColumns.EXECUTING_ENTITY
            TempColumns.TRADER
            TempColumns.EXECUTION_WITHIN_FIRM
            TempColumns.INVESTMENT_DECISION_MAKER
            TempColumns.COUNTERPARTY
            TempColumns.BUYER
            TempColumns.SELLER
        """

        # this logic below fetches data for aggregated orders that have it missing by looking up the orders
        # they aggregate from and copying over the values when available
        party_es_lookup_column_combos = [
            (TempColumns.TRADER, ES_COL_PREFIX + PartiesFields.PARTIES_TRADER_FILE_ID),
            (
                TempColumns.EXECUTION_WITHIN_FIRM,
                ES_COL_PREFIX + PartiesFields.PARTIES_EXEC_WITHIN_FIRM_FILE_ID,
            ),
            (
                TempColumns.INVESTMENT_DECISION_MAKER,
                ES_COL_PREFIX + PartiesFields.PARTIES_INVEST_DEC_WITHIN_FIRM_FILE_ID,
            ),
            (
                TempColumns.COUNTERPARTY,
                ES_COL_PREFIX + PartiesFields.PARTIES_CP_FILE_ID,
            ),
            (TempColumns.BUYER, ES_COL_PREFIX + PartiesFields.PARTIES_BUYER_FILE_ID),
            (TempColumns.SELLER, ES_COL_PREFIX + PartiesFields.PARTIES_SELLER_FILE_ID),
        ]

        for column_combo in party_es_lookup_column_combos:
            self._fetch_data_from_es_and_in_file_lookup(
                df_to_update=self.pre_process_df,
                column_to_update=column_combo[0],
                es_lookup_column=column_combo[1],
            )

        return GenericOrderPartyIdentifiers.process(
            source_frame=pd.concat(
                [
                    self.pre_process_df,
                    self.target_df.loc[
                        :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
                    ],
                ],
                axis=1,
            ),
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=TempColumns.EXECUTING_ENTITY,
                trader_identifier=TempColumns.TRADER,
                execution_within_firm_identifier=TempColumns.EXECUTION_WITHIN_FIRM,
                investment_decision_within_firm_identifier=TempColumns.INVESTMENT_DECISION_MAKER,
                client_identifier=TempColumns.CLIENT,
                counterparty_identifier=TempColumns.COUNTERPARTY,
                buyer_identifier=TempColumns.BUYER,
                seller_identifier=TempColumns.SELLER,
                use_buy_mask_for_buyer_seller=True,
                buy_sell_side_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                use_buy_mask_for_buyer_seller_decision_maker=True,
                create_fallback_fields=True,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """
        Populates with Orders and OrderState values for OrderColumns.META_MODEL,
        """
        return pd.concat(
            [
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=ParamsMapStatic(
                        target_value="Order",
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.META_MODEL,
                        ),
                    ),
                ),
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=ParamsMapStatic(
                        target_value="OrderState",
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.META_MODEL,
                        ),
                    ),
                ),
            ],
            axis=1,
        )

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""

    def _order_class(self) -> pd.DataFrame:
        """
        Populates OrderColumns.ORDER_CLASS from SourceColumns.EVENT
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.EVENT].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_CLASS],
        )

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        """
        Populates OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID from:
            SourceColumns.AGGR__TO if SourceColumns.EVENT == 'AGGREGATED TO'
            SourceColumns.AGGR__FROM if SourceColumns.EVENT == 'AGGREGATED FROM'
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.AGGREGATED_ORDER_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID],
        )

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.BASKET_NO
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.BASKET_NO].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_INITIAL_ORDER_DESIGNATION],
        )

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """
        Populates from TempColumns.INT_ORDER_ID_CODE
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.INT_ORDER_ID_CODE].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE],
        )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """
        Maps OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE from OrderColumns.ID
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.ORDER_ID].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
        )

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.TRN if present else SourceColumns.AUDIT_ID.
        """
        routing_values = pd.Series(
            data=self.source_frame.loc[:, SourceColumns.TRN].values,
            index=self.source_frame.index,
        ).fillna(self.source_frame.loc[:, SourceColumns.AUDIT_ID])

        return pd.DataFrame(
            data=routing_values.values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ROUTING_CODE],
        )

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        """
        Populates from TempColumns.SEQUENCE_NUMBER
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.SEQUENCE_NUMBER].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_SEQUENCE_NUMBER],
        )

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.TRN
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.TRN].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_TRADING_VENUE_TRANSACTION_ID_CODE],
        )

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """
        Maps OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO
        from TempColumns.TRANSACTION_REFERENCE_NUMBER temporary column
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[
                :, TempColumns.TRANSACTION_REFERENCE_NUMBER
            ].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO],
        )

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """
        Populates from TempColumns.INITIAL_QUANTITY_PROPAGATED unless
            order id is populated from SourceColumns.AUDIT_ID_TKT
            in which case, take SourceColumns.QUANTITY

        Divides Order/OrderState for ES/In-file lookup logic downstream
        """
        initial_quantity = self.pre_process_df.loc[
            :, TempColumns.INITIAL_QUANTITY_PROPAGATED
        ]
        order_id_from_auditidtkt = self.pre_process_df.loc[
            :, TempColumns.ORDER_FROM_AUDIT_ID_TKT
        ]
        initial_quantity.loc[order_id_from_auditidtkt] = self.source_frame.loc[
            order_id_from_auditidtkt, SourceColumns.QUANTITY
        ]

        return pd.concat(
            [
                pd.DataFrame(
                    data=initial_quantity.values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            ModelPrefix.ORDER,
                            OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                        )
                    ],
                ),
                pd.DataFrame(
                    data=initial_quantity.values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            ModelPrefix.ORDER_STATE,
                            OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.QUANTITY
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.QUANTITY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_MODIFIED_QUANTITY],
        )

    def _price_forming_data_price(self) -> pd.DataFrame:
        """
        Populates OrderColumns.PRICE_FORMING_DATA_PRICE from temporary column TempColumns.PRICE

        Divides Order/OrderState for ES/In-file lookup logic downstream
        """
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, TempColumns.PRICE].values,
                    index=self.pre_process_df.index,
                    columns=[
                        add_prefix(
                            ModelPrefix.ORDER, OrderColumns.PRICE_FORMING_DATA_PRICE
                        )
                    ],
                ),
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, TempColumns.PRICE].values,
                    index=self.pre_process_df.index,
                    columns=[
                        add_prefix(
                            ModelPrefix.ORDER_STATE,
                            OrderColumns.PRICE_FORMING_DATA_PRICE,
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """
        Populates _order.OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY
            For NEWO populate from TempColumns.REMAINING_QUANTITY
            For OrderStates subtracts the tradedQuantity cumulatively from initialQuantity
            For 'NEW CASH/COLLATERAL TICKET' with order id from AUDITIDTKT and Record Type in [MRC,ARC,RT] set to null

        Divides Order/OrderState for ES/In-file lookup logic downstream
        """
        remaining_quantity = pd.Series(
            data=pd.NA,
            index=self.source_frame.index,
            name=OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
        )

        newo_rows = self.pre_process_df.loc[:, TempColumns.SYNTH_ORDER]

        remaining_quantity.loc[newo_rows] = self.pre_process_df.loc[
            newo_rows, TempColumns.REMAINING_QUANTITY
        ].values

        temp_df = pd.concat(
            objs=[
                self.pre_process_df.loc[:, TempColumns.ORDER_ID],
                self.target_df.loc[
                    :,
                    OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                ],
            ],
            axis=1,
        ).fillna(np.nan)

        traded_cumsum = temp_df.groupby(TempColumns.ORDER_ID)[
            OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
        ].cumsum()

        remaining_quantity.loc[~newo_rows] = (
            self.target_df.loc[
                ~newo_rows,
                add_prefix(
                    ModelPrefix.ORDER, OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY
                ),
            ]
            - traded_cumsum.loc[~newo_rows]
        ).fillna(pd.NA)

        new_cash_collateral_ticket = self.source_frame.loc[:, SourceColumns.EVENT].isin(
            [Events.NEW_CASH_TICKET, Events.NEW_COLLATERAL_TICKET]
        )
        record_type_query = self.source_frame.loc[:, SourceColumns.RECORD_TYPE].isin(
            ["MRC", "ARC", "RT"]
        )

        remaining_quantity.loc[
            new_cash_collateral_ticket
            & record_type_query
            & self.pre_process_df.loc[:, TempColumns.ORDER_FROM_AUDIT_ID_TKT]
        ] = pd.NA

        return pd.concat(
            [
                pd.DataFrame(
                    data=remaining_quantity.values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            ModelPrefix.ORDER,
                            OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
                        )
                    ],
                ),
                pd.DataFrame(
                    data=remaining_quantity.values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            ModelPrefix.ORDER_STATE,
                            OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """
        Populates OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY from TempColumns.QUANTITY only for PARF/FILL
        """
        return MapConditional.process(
            source_frame=self.pre_process_df.loc[
                :, [TempColumns.ORDER_STATUS, TempColumns.QUANTITY]
            ],
            params=ParamsMapConditional(
                target_attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                cases=[
                    Case(
                        query=f"`__order_status__`.isin(['{OrderStatus.PARF}','{OrderStatus.FILL}'])",
                        attribute=TempColumns.QUANTITY,
                    )
                ],
            ),
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """
        Maps OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO
        from TempColumns.TRANSACTION_REFERENCE_NUMBER temporary column
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[
                :, TempColumns.TRANSACTION_REFERENCE_NUMBER
            ].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO],
        )

    def _source_index(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceIndex column"""
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                from_index=True, target_attribute=OrderColumns.SOURCE_INDEX
            ),
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceKey column from the SWARM_FILE_URL"""
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_value=os.getenv("SWARM_FILE_URL"),
                target_attribute=OrderColumns.SOURCE_KEY,
            ),
        )

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _timestamps_order_received(self) -> pd.DataFrame:
        """
        Maps OrderColumns.TIMESTAMPS_ORDER_RECEIVED
        deriving its value from TempColumns.PARENT_SYS_DATE_TIME
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.PARENT_SYS_DATE_TIME].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_RECEIVED],
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """
        Maps OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED
        deriving its value from TempColumns.SYS_DATE_TIME
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.SYS_DATE_TIME].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED],
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """
        Maps OrderColumns.TIMESTAMPS_ORDER_SUBMITTED
        deriving its value from TempColumns.ROUTE_SYS_DATE_TIME
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.ROUTE_SYS_DATE_TIME].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_SUBMITTED],
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """
        Maps OrderColumns.TIMESTAMPS_ORDER_SUBMITTED
        deriving its value from TempColumns.SYS_DATE_TIME only populated for PARF and FILL records
        """
        return MapConditional.process(
            source_frame=self.pre_process_df.loc[
                :, [TempColumns.ORDER_STATUS, TempColumns.SYS_DATE_TIME]
            ],
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
                cases=[
                    Case(
                        query=f"(`{TempColumns.ORDER_STATUS}` == '{OrderStatus.FILL.value}') | "
                        f"(`{TempColumns.ORDER_STATUS}` == '{OrderStatus.PARF.value}')",
                        attribute=TempColumns.SYS_DATE_TIME,
                    )
                ],
            ),
        )

    def _timestamps_validity_period(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.SIDE column
        Assumes self.execution_details_validity_period() ran prior
        """
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.EXECUTION_DETAILS_SHORT_SELLING_INDICATOR
            ].values,
            index=self.target_df.index,
            columns=[
                OrderColumns.TRADERS_ALGOS_WAIVER_INDICATORS_SHORT_SELLING_INDICATOR
            ],
        )

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _traders_algos_waivers_indicators_waiver_indicator(
        self,
    ) -> pd.DataFrame:
        """
        Populates from SourceColumns.WAIVER_INDICATORS
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.WAIVER_INDICATORS,
                target_attribute=OrderColumns.TRADERS_ALGOS_WAIVER_INDICATORS_WAIVER_INDICATOR,
                cast_to=CastTo.STRING_LIST.value,
                list_delimiter=Delimiters.SEMI_COLON,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
        from TempColumns.BUY_SELL_INDICATOR temporary column
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.BUY_SELL_INDICATOR].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_position_id(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_price(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_PRICE from temporary column TempColumns.PRICE
        Populated only for PARF and FILL records.

        Divides Order/OrderState for ES/In-file lookup logic downstream
        """
        price = MapConditional.process(
            source_frame=self.pre_process_df.loc[
                :, [TempColumns.ORDER_STATUS, TempColumns.PRICE]
            ],
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE,
                cases=[
                    Case(
                        query=f"(`{TempColumns.ORDER_STATUS}` == '{OrderStatus.FILL.value}') | "
                        f"(`{TempColumns.ORDER_STATUS}` == '{OrderStatus.PARF.value}')",
                        attribute=TempColumns.PRICE,
                    )
                ],
            ),
        )

        return pd.concat(
            [
                pd.DataFrame(
                    data=price.values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            ModelPrefix.ORDER, OrderColumns.TRANSACTION_DETAILS_PRICE
                        )
                    ],
                ),
                pd.DataFrame(
                    data=price.values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            ModelPrefix.ORDER_STATE,
                            OrderColumns.TRANSACTION_DETAILS_PRICE,
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _transaction_details_price_average(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY from TempColumns.CURRENCY_CODE
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.CURRENCY_CODE].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY],
        )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION from SourceColumns.ASSET_CLASS
        and SourceColumns.SECURITY
        """
        temp_df = MapValue.process(
            source_frame=self.pre_process_df,
            params=ParamsMapValue(
                source_attribute=TempColumns.ASSET_CLASS_PROPAGATED,
                target_attribute=TempColumns.TEMP_COL_1,
                case_insensitive=True,
                value_map={
                    "CDS": PriceNotation.MONE.value,
                    "CDSWINDEX": PriceNotation.MONE.value,
                    "CDSWSINGLE": PriceNotation.MONE.value,
                    "EQUITY": PriceNotation.MONE.value,
                    "FUTURECOMMODITY": PriceNotation.MONE.value,
                    "FUTUREINDEX": PriceNotation.MONE.value,
                    "FXFORWARD": PriceNotation.MONE.value,
                    "FXFORWARDSWAP": PriceNotation.MONE.value,
                    "FXSPOT": PriceNotation.MONE.value,
                    "FXSPOTSWAP": PriceNotation.MONE.value,
                    "IRSSWAPTION": PriceNotation.MONE.value,
                    "LOWARRANTS": PriceNotation.MONE.value,
                    "TRS": PriceNotation.MONE.value,
                    "FICORP": PriceNotation.PERC.value,
                    "FIGOVT": PriceNotation.PERC.value,
                    "FIMMKT": PriceNotation.PERC.value,
                    "FIMTGE": PriceNotation.PERC.value,
                    "FIPFD": PriceNotation.MONE.value,
                },
            ),
            auditor=self.auditor,
        )

        asset_class_sft = f"`{TempColumns.ASSET_CLASS_PROPAGATED}`.str.fullmatch('SFT', case=False, na=False)"
        security_generic = (
            f"`{SourceColumns.SECURITY}`.str.fullmatch('GENERIC', case=False, na=False)"
        )

        return MapConditional.process(
            source_frame=pd.concat(
                [
                    temp_df,
                    self.source_frame.loc[:, SourceColumns.SECURITY],
                    self.pre_process_df.loc[:, TempColumns.ASSET_CLASS_PROPAGATED],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION,
                cases=[
                    Case(
                        query="index == index",
                        attribute=TempColumns.TEMP_COL_1,
                    ),
                    Case(
                        query=f"({asset_class_sft}) & ~({security_generic})",
                        value=PriceNotation.PERC.value,
                    ),
                ],
            ),
        )

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_QUANTITY from TempColumns.QUANTITY only for PARF/FILL
        """
        return MapConditional.process(
            source_frame=self.pre_process_df.loc[
                :, [TempColumns.ORDER_STATUS, TempColumns.QUANTITY]
            ],
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY,
                cases=[
                    Case(
                        query=f"`__order_status__`.isin(['{OrderStatus.PARF}','{OrderStatus.FILL}'])",
                        attribute=TempColumns.QUANTITY,
                    )
                ],
            ),
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """
        Populated from SourceColumns.TRADED_CUR when SourceColumns.ASSET_CLASS
        is in ["FXFORWARD", "FXFORWARDSWAP", "FXSPOT", "FXSPOTSWAP"]
        """
        query_list = ["FXFORWARD", "FXFORWARDSWAP", "FXSPOT", "FXSPOTSWAP"]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
                cases=[
                    Case(
                        query=f"`{SourceColumns.ASSET_CLASS}`.isin({str(query_list)})",
                        attribute=SourceColumns.TRADED_CUR,
                    )
                ],
            ),
        )

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION from SourceColumns.ASSET_CLASS
        and SourceColumns.SECURITY
        """
        temp_df = MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.ASSET_CLASS,
                target_attribute=TempColumns.TEMP_COL_1,
                case_insensitive=True,
                value_map={
                    "CDS": QuantityNotation.NOML.value,
                    "CDSWINDEX": QuantityNotation.NOML.value,
                    "CDSWSINGLE": QuantityNotation.NOML.value,
                    "EQUITY": QuantityNotation.UNIT.value,
                    "FUTURECOMMODITY": QuantityNotation.UNIT.value,
                    "FUTUREINDEX": QuantityNotation.UNIT.value,
                    "FXFORWARD": QuantityNotation.MONE.value,
                    "FXFORWARDSWAP": QuantityNotation.MONE.value,
                    "FXSPOT": QuantityNotation.MONE.value,
                    "FXSPOTSWAP": QuantityNotation.MONE.value,
                    "IRSSWAPTION": QuantityNotation.NOML.value,
                    "LOWARRANTS": QuantityNotation.UNIT.value,
                    "TRS": QuantityNotation.MONE.value,
                    "FICORP": QuantityNotation.NOML.value,
                    "FIGOVT": QuantityNotation.NOML.value,
                    "FIMMKT": QuantityNotation.NOML.value,
                    "FIMTGE": QuantityNotation.NOML.value,
                    "FIPFD": QuantityNotation.UNIT.value,
                },
            ),
            auditor=self.auditor,
        )

        asset_class_sft = (
            f"`{SourceColumns.ASSET_CLASS}`.str.fullmatch('SFT', case=False, na=False)"
        )
        security_generic = (
            f"`{SourceColumns.SECURITY}`.str.fullmatch('GENERIC', case=False, na=False)"
        )

        order_id_from_auditidtkt = f"`{TempColumns.ORDER_FROM_AUDIT_ID_TKT}`"

        return MapConditional.process(
            source_frame=pd.concat(
                [
                    temp_df,
                    self.source_frame.loc[
                        :, [SourceColumns.ASSET_CLASS, SourceColumns.SECURITY]
                    ],
                    self.pre_process_df.loc[:, TempColumns.ORDER_FROM_AUDIT_ID_TKT],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
                cases=[
                    Case(
                        query="index == index",
                        attribute=TempColumns.TEMP_COL_1,
                    ),
                    Case(
                        query=f"({asset_class_sft}) & ({security_generic})",
                        value=QuantityNotation.NOML.value,
                    ),
                    Case(
                        query=order_id_from_auditidtkt,
                        value=QuantityNotation.NOML.value,
                    ),
                ],
            ),
        )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        """
        Maps OrderColumns.TRANSACTION_DETAILS_SETTLEMENT_DATE
        deriving its value from TempColumns.SETTLE_DT
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.SETTLE_DT].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_SETTLEMENT_DATE],
        )

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populates from TempColumns.TRADING_CAPACITY
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.TRADING_CAPACITY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """
        Maps OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME
        deriving its value from TempColumns.SYS_DATE_TIME only populated for PARF and FILL records
        """
        return MapConditional.process(
            source_frame=self.pre_process_df.loc[
                :, [TempColumns.ORDER_STATUS, TempColumns.SYS_DATE_TIME]
            ],
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
                cases=[
                    Case(
                        query=f"(`{TempColumns.ORDER_STATUS}` == '{OrderStatus.FILL.value}') | "
                        f"(`{TempColumns.ORDER_STATUS}` == '{OrderStatus.PARF.value}')",
                        attribute=TempColumns.SYS_DATE_TIME,
                    )
                ],
            ),
        )

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE from SourceColumns.LAST_MARKET
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LAST_MARKET].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE],
        )

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """
        Maps SourceColumns.UP_FEE to OrderColumns.TRANSACTION_DETAILS_UPFRONT_PAYMENT
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.UP_FEE_CURR,
                source_price_attribute=SourceColumns.UP_FEE,
                target_price_attribute=OrderColumns.TRANSACTION_DETAILS_UPFRONT_PAYMENT,
            ),
        )

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """
        Maps SourceColumns.UP_FEE_CURR to OrderColumns.TRANSACTION_DETAILS_UPFRONT_PAYMENT_CURRENCY
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.UP_FEE_CURR,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_UPFRONT_PAYMENT_CURRENCY,
            ),
        )

    def _transaction_details_venue(self) -> pd.DataFrame:
        """
        Populates OrderColumns.TRANSACTION_DETAILS_VENUE from SourceColumns.LAST_MARKET
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LAST_MARKET].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_VENUE],
        )

    # aggregation methods
    def _temp_aggregate_fields(self):
        """
        Generates the two temp columns to be used for:
            orderIdentifiers.internalOrderIdCode
            orderIdentifiers.sequenceNumber
        Following these specs:
            requirement is to store all the orderIds from the single order that were aggregated
                to the single aggregated from new order

            if C_EVENT(30) is in ['ADDED TO BASKET','AGGREGATED TO','AGGREGATED FROM']:
              attempt 1:
              search the file for all records with the same value in I_TSORDNUM(10)
                and with C_EVENT(30) == "ORDER ROLLED OVER TO NEXT DAY"
              if any hits (note there will be 0 or 2 or more) then concatenate all values in I_AGGRFROM(10)
                using a comma delimiter.
              this schema field has a limit of 50 characters so if the string is greater than 50
                then remove the last 5 characters and replace with "[...]"

              attempt 2:
              search the rest of file for all records with the value from I_TSORDNUM(10)
                against the values in I_AGGRTO(10)
              if any hits (note there will be 0 or 2 or more) then concatenate all values in I_TSORDNUM(10)
                from the hits using a comma delimiter.
              this schema field has a limit of 50 characters so if the string is greater than 50
                then remove the last 5 characters and replace with "[...]"
            -------------------------------------------------------------------------
            requirement here is to count all clients that were involved in order aggregation
                (1 client who has 3 orders would count as 1)

            if C_EVENT(30) is in ['ADDED TO BASKET','AGGREGATED TO','AGGREGATED FROM']:
                search the value in orderIdentifiers.orderIdCode against the value in
                    orderIdentifiers.orderIdCode from all other records
                there should always be 1 or more hits.
                collect the value within clientIdentifiers.client for all the hits
                then count unique values
            note this logic also needs to be apply to records in elastic. run this query against the last month of records in elastic
        """
        temp_df = self._setup_attempts_temp_df()
        added_to_basket_or_aggregated_mask = self.source_frame.loc[
            :, SourceColumns.EVENT
        ].isin([Events.ADDED_TO_BASKET, Events.AGGREGATED_FROM, Events.AGGREGATED_TO])

        ts_ord_num_values_where_added_to_basket_or_aggregated = (
            self._get_ts_ord_nums_to_lookup(mask=added_to_basket_or_aggregated_mask)
        )

        order_rolled_mask = (
            self.source_frame.loc[:, SourceColumns.EVENT]
            == Events.ORDER_ROLLED_OVER_TO_NEXT_DAY
        )

        if ts_ord_num_values_where_added_to_basket_or_aggregated:
            # internal order id code
            self._perform_attempts(
                target_dataframe=temp_df,
                target_merged_column=TempColumns.INT_ORDER_ID_CODE,
                first_attempt_target_col=TempColumns.FIRST_ATTEMPT_ID,
                second_attempt_target_col=TempColumns.SECOND_ATTEMPT_ID,
                values_to_fill_mask=added_to_basket_or_aggregated_mask,
                values_to_match=ts_ord_num_values_where_added_to_basket_or_aggregated,
                aggregate_mask=order_rolled_mask,
                first_aggregate_column=SourceColumns.AGGR_FROM,
                second_aggregate_column=SourceColumns.TS_ORD_NUM,
            )
            self.slice_over_50(added_to_basket_or_aggregated_mask, temp_df)

            # sequence number
            sequence_number_df = pd.concat(
                [
                    added_to_basket_or_aggregated_mask,
                    self.pre_process_df.loc[
                        :,
                        [
                            TempColumns.ORDER_ID,
                            TempColumns.AGGREGATED_ORDER_ID,
                            TempColumns.CLIENT,
                            TempColumns.ES_CLIENTS,
                        ],
                    ],
                ],
                axis=1,
            )

            # join client id columns in a single column
            sequence_number_df.loc[:, "__all_clients__"] = sequence_number_df.loc[
                :, [TempColumns.CLIENT, TempColumns.ES_CLIENTS]
            ].apply(
                lambda x: self._joint_clients(
                    client=x[TempColumns.CLIENT], es_clients=x[TempColumns.ES_CLIENTS]
                ),
                axis=1,
            )

            # order ids where added to basket mask is true
            order_ids_added_to_basket_or_aggregated = (
                sequence_number_df.loc[
                    added_to_basket_or_aggregated_mask, TempColumns.ORDER_ID
                ]
                .drop_duplicates()
                .to_list()
            )

            #  orders with matching ids and client ids
            matching_order_ids_not_null_clients = (
                sequence_number_df.loc[:, TempColumns.ORDER_ID].isin(
                    order_ids_added_to_basket_or_aggregated
                )
                & sequence_number_df.loc[:, TempColumns.ALL_CLIENTS].notnull()
            )

            # number of unique client ids per aggregate order id
            aggregate_order_id_groupby_all_clients = (
                sequence_number_df.loc[matching_order_ids_not_null_clients, :]
                .groupby(by=TempColumns.ORDER_ID)[TempColumns.ALL_CLIENTS]
                .sum()
                .apply(set)
                .apply(len)
                .apply(str)
                .to_dict()
            )

            temp_df.loc[
                added_to_basket_or_aggregated_mask, TempColumns.SEQUENCE_NUMBER
            ] = sequence_number_df.loc[
                added_to_basket_or_aggregated_mask, TempColumns.ORDER_ID
            ].map(
                aggregate_order_id_groupby_all_clients
            )

        return temp_df.loc[
            :, [TempColumns.INT_ORDER_ID_CODE, TempColumns.SEQUENCE_NUMBER]
        ].fillna(pd.NA)

    @staticmethod
    def _joint_clients(client: str, es_clients: set) -> list:
        """
        aux method to join client ids in a single set to determine sequence number
        :param client: client id as string
        :param es_clients: client ids from elastic in a set
        :return: set with both client ids joined in a set
        """
        return_set = set()
        if isinstance(client, str):
            return_set.add(client)
        if isinstance(es_clients, set):
            return_set.update(es_clients)

        if return_set:
            return list(return_set)
        else:
            return pd.NA

    def _setup_attempts_temp_df(self) -> pd.DataFrame:
        """
        Generates temp_df to be used for:
            TempColumns.INT_ORDER_ID_CODE
            TempColumns.SEQUENCE_NUMBER
        :return: dataframe with the necessary columns
        """
        temp_df = pd.DataFrame(index=self.pre_process_df.index)
        temp_df.loc[
            :,
            [
                TempColumns.FIRST_ATTEMPT_ID,
                TempColumns.SECOND_ATTEMPT_ID,
                TempColumns.INT_ORDER_ID_CODE,
                TempColumns.SEQUENCE_NUMBER,
            ],
        ] = pd.NA
        return temp_df

    def _get_ts_ord_nums_to_lookup(self, mask: pd.Series) -> set:
        """
        Looks up SourceColumns.TS_ORD_NUM where SourceColumns.EVENT == 'ADDED TO BASKET'
        :param mask: mask to filter lookup values from
        :return: set of values; pd.NA excluded
        """
        added_to_basket_df = self.source_frame.loc[mask, :]
        ts_ord_num_values_where_added_to_basket = set(
            added_to_basket_df.loc[:, SourceColumns.TS_ORD_NUM].values
        )
        return {x for x in ts_ord_num_values_where_added_to_basket if pd.notna(x)}

    def _perform_attempts(
        self,
        target_dataframe: pd.DataFrame,
        target_merged_column: str,
        first_attempt_target_col: str,
        second_attempt_target_col: str,
        values_to_fill_mask: pd.Series,
        values_to_match: set,
        aggregate_mask: pd.Series,
        first_aggregate_column: str,
        second_aggregate_column: str,
    ) -> NoReturn:
        """
        This method calls the aggregating matching method for both attempts

        This method was extracted in such a way because the same logic was used for two different fields.
        One of the fields has since changed so it's only used for one of them.

        :param target_dataframe: dataframe to be filled
        :param target_merged_column: column to be filled
        :param first_attempt_target_col: column to be filled for the first attempt
        :param second_attempt_target_col: column to be filled for the second attempt
        :param values_to_fill_mask: rows to be filled with aggregation
        :param values_to_match: values to be looked into to match
        :param aggregate_mask: mask to find values to aggregate for the first attempt
        :param first_aggregate_column: column to aggregate values from for the first attempt
        :param second_aggregate_column: column to aggregate values from for the second attempt
        """
        self._aggregate_values_from_matching_columns(
            values_to_fill_mask=values_to_fill_mask,
            target_dataframe=target_dataframe,
            values_to_match=values_to_match,
            values_to_aggregate_from_mask=aggregate_mask,
            col_to_aggregate_from=first_aggregate_column,
            col_to_match_with=SourceColumns.TS_ORD_NUM,
            target_col=first_attempt_target_col,
            merge_on_kwargs={"on": SourceColumns.TS_ORD_NUM},
        )

        if (
            target_dataframe.loc[values_to_fill_mask, first_attempt_target_col]
            .isna()
            .any()
        ):
            self._aggregate_values_from_matching_columns(
                values_to_fill_mask=values_to_fill_mask,
                target_dataframe=target_dataframe,
                values_to_match=values_to_match,
                values_to_aggregate_from_mask=~aggregate_mask,
                col_to_aggregate_from=second_aggregate_column,
                col_to_match_with=SourceColumns.AGGR_TO,
                target_col=second_attempt_target_col,
                merge_on_kwargs={
                    "left_on": SourceColumns.TS_ORD_NUM,
                    "right_on": SourceColumns.AGGR_TO,
                },
            )

        target_dataframe.loc[
            values_to_fill_mask, target_merged_column
        ] = target_dataframe.loc[values_to_fill_mask, first_attempt_target_col].fillna(
            target_dataframe.loc[values_to_fill_mask, second_attempt_target_col]
        )

    def _aggregate_values_from_matching_columns(
        self,
        target_dataframe: pd.DataFrame,
        target_col: str,
        values_to_fill_mask: pd.Series,
        values_to_match: set,
        values_to_aggregate_from_mask: pd.Series,
        col_to_aggregate_from: str,
        col_to_match_with: str,
        merge_on_kwargs: Dict[str, str],
    ) -> NoReturn:
        """
        Finds rows with matching values
        Groupbys on the column with matching values
        Aggregates a second column based on that groupby.
        Uses this new matching dataframe to merge with the source_frame
        To crate a new column for pre_process

        :param target_dataframe: dataframe to be filled
        :param target_col: column to be filled
        :param values_to_fill_mask: rows to be filled with aggregation
        :param values_to_match: values to be looked into to match
        :param values_to_aggregate_from_mask: mask to find matching values to aggregate
        :param col_to_aggregate_from: column to aggregate values from
        :param col_to_match_with: column to match values with
        :param merge_on_kwargs: kwargs for the merge
        """
        matching_values_df = self.find_values_to_aggregate(
            col_to_aggregate_from=col_to_aggregate_from,
            col_to_match_with=col_to_match_with,
            values_to_aggregate_from_mask=values_to_aggregate_from_mask,
            values_to_match=values_to_match,
        )

        if matching_values_df.loc[:, col_to_aggregate_from].notna().any():
            matching_values_df = self.aggregate_values_by_group(
                col_to_aggregate_from=col_to_aggregate_from,
                col_to_match_with=col_to_match_with,
                matching_values_df=matching_values_df,
                target_col=target_col,
            )

            # merge values into dataframe
            self.merge_into_target_dataframe(
                matching_values_df=matching_values_df,
                merge_on_kwargs=merge_on_kwargs,
                target_col=target_col,
                target_dataframe=target_dataframe,
                values_to_fill_mask=values_to_fill_mask,
            )

    def find_values_to_aggregate(
        self,
        col_to_aggregate_from: str,
        col_to_match_with: str,
        values_to_aggregate_from_mask: pd.Series,
        values_to_match: List[str],
    ) -> pd.DataFrame:
        """
        Find matching values to aggregate
        :param col_to_aggregate_from: column to aggregate values from
        :param col_to_match_with: column to match values with
        :param values_to_aggregate_from_mask: mask to find matching values to aggregate
        :param values_to_match: values to be looked into to match
        :return: dataframe containing values to aggregate
        """
        matching_values_mask = self.source_frame.loc[:, col_to_match_with].isin(
            values_to_match
        )
        matching_values_df = self.source_frame.loc[
            values_to_aggregate_from_mask & matching_values_mask,
            [col_to_match_with, col_to_aggregate_from],
        ]
        return matching_values_df

    @staticmethod
    def aggregate_values_by_group(
        col_to_aggregate_from: str,
        col_to_match_with: str,
        matching_values_df: pd.DataFrame,
        target_col: str,
    ):
        """
        Group dataframe by `col_to_match_with` and aggregate values from `col_to_aggregate_from`
        :param col_to_aggregate_from: column to aggregate values from
        :param col_to_match_with: column to match values with
        :param matching_values_df: dataframe with values to aggregate
        :param target_col: column where to store the aggregation
        :return: dataframe with aggregated values
        """
        matching_values_df.loc[:, target_col] = matching_values_df.groupby(
            col_to_match_with
        )[col_to_aggregate_from].transform(lambda x: ",".join(sorted(list(set(x)))))
        matching_values_df = matching_values_df.drop(
            col_to_aggregate_from, axis=1
        ).drop_duplicates(col_to_match_with)
        return matching_values_df

    def merge_into_target_dataframe(
        self,
        matching_values_df: pd.DataFrame,
        merge_on_kwargs: Dict[str, str],
        target_col: str,
        target_dataframe: pd.DataFrame,
        values_to_fill_mask: pd.Series,
    ) -> NoReturn:
        """

        :param matching_values_df: dataframe with aggregated values
        :param merge_on_kwargs: kwargs for merge
        :param target_col: target column for merge operation
        :param target_dataframe: dataframe to store merge result
        :param values_to_fill_mask: values from dataframe to fill with merge result
        """
        keep_index = self.source_frame.loc[values_to_fill_mask, :].index
        target_dataframe.loc[:, target_col] = (
            self.source_frame.loc[values_to_fill_mask, :]
            .merge(matching_values_df, how="left", **merge_on_kwargs)
            .set_index(keep_index)
            .loc[:, target_col]
        )

    @staticmethod
    def slice_over_50(
        added_to_basket_mask: pd.Series,
        dataframe: pd.DataFrame,
    ) -> NoReturn:
        """
        Slices any column over 50n characters; adds '[...]' at the end if so
        :param added_to_basket_mask: mask where EVENT is 'ADDED TO BASKET'
        :param dataframe: temp dataframe
        """
        over_50_len = (
            dataframe.loc[added_to_basket_mask, TempColumns.INT_ORDER_ID_CODE].str.len()
            > 50
        )
        not_null_mask = dataframe.loc[:, TempColumns.INT_ORDER_ID_CODE].notnull()
        dataframe.loc[
            added_to_basket_mask & over_50_len & not_null_mask,
            TempColumns.INT_ORDER_ID_CODE,
        ] = (
            dataframe.loc[
                added_to_basket_mask & over_50_len & not_null_mask,
                TempColumns.INT_ORDER_ID_CODE,
            ].str.slice(stop=45)
            + "[...]"
        )

    def _fetch_data_from_es_and_in_file_lookup(
        self, df_to_update: pd.DataFrame, column_to_update: str, es_lookup_column: str
    ) -> None:
        """
        This is a generic method to map values looked up in elastic and in-file that are missing
        in some aggregated orders

        :param df_to_update: dataframe that needs to be updated
        :param column_to_update: column in the dataframe, that needs to be updated
        :param es_lookup_column: column in self.aggregated_order_info that contains fetched data
        """

        aggr_missing_info_from_es = self.pre_process_df.loc[
            :, TempColumns.HAS_AGGREGATE_INFO_FROM_ES
        ]
        orders_with_aggr_from_in_file = self.source_frame.loc[
            :, SourceColumns.AGGR_FROM
        ].notnull()
        ids_missing_info = (
            self.source_frame.loc[
                aggr_missing_info_from_es | orders_with_aggr_from_in_file,
                SourceColumns.TS_ORD_NUM,
            ]
            .drop_duplicates()
            .to_list()
        )

        if not ids_missing_info:
            return

        orders_to_update = self.pre_process_df.loc[
            :, TempColumns.SYNTH_ORDER
        ] & self.source_frame.loc[:, SourceColumns.TS_ORD_NUM].isin(ids_missing_info)

        lookup_field_values = (
            pd.concat(
                [
                    df_to_update.loc[orders_to_update, column_to_update],
                    self.source_frame.loc[:, SourceColumns.TS_ORD_NUM],
                ],
                axis=1,
            )
            .dropna(axis=0, subset=[column_to_update])
            .set_index(SourceColumns.TS_ORD_NUM)
            .to_dict()[column_to_update]
        )

        # es lookup fields
        for order_id_value in ids_missing_info:

            if not aggr_missing_info_from_es.any() or lookup_field_values.get(
                order_id_value
            ):
                continue

            order_to_fetch_from = (
                self.source_frame.loc[:, SourceColumns.TS_ORD_NUM] == order_id_value
            ) & aggr_missing_info_from_es
            lookup_field_values_for_order_id = (
                self.aggregated_order_info.loc[
                    order_to_fetch_from,
                    [es_lookup_column],
                ]
                .stack()
                .to_list()
            )
            if lookup_field_values_for_order_id:
                lookup_field_values[order_id_value] = lookup_field_values_for_order_id[
                    0
                ]

        # in file lookup fields
        for order_id_value in ids_missing_info:

            if not orders_with_aggr_from_in_file.any() or lookup_field_values.get(
                order_id_value
            ):
                continue

            ids_to_fetch_info_from = (
                self.source_frame.loc[
                    orders_with_aggr_from_in_file
                    & (
                        self.source_frame.loc[:, SourceColumns.TS_ORD_NUM]
                        == order_id_value
                    ),
                    SourceColumns.AGGR_FROM,
                ]
                .drop_duplicates()
                .to_list()
            )

            orders_to_fetch_from = self.source_frame.loc[
                :, SourceColumns.TS_ORD_NUM
            ].isin(ids_to_fetch_info_from)

            lookup_field_values_for_order_id = (
                df_to_update.loc[orders_to_fetch_from, [column_to_update]]
                .stack()
                .to_list()
            )

            if lookup_field_values_for_order_id:
                lookup_field_values[order_id_value] = lookup_field_values_for_order_id[
                    0
                ]

        # apply fetched values to null rows
        null_lookup_field = df_to_update.loc[:, column_to_update].isnull()
        df_to_update.loc[orders_to_update & null_lookup_field, column_to_update] = (
            self.source_frame.loc[
                orders_to_update & null_lookup_field, SourceColumns.TS_ORD_NUM
            ]
            .map(lookup_field_values)
            .fillna(pd.NA)
        )

    def _fetch_and_sum_data_from_es_and_in_file_lookup(self, column_to_update) -> None:
        """
        This is a generic method to map values looked up in elastic and in-file that are missing
        in some aggregated orders that need to be added up before assigning

        :param column_to_update: column on which to perform the aggregate and sum logic
        """

        values_from_es_lookup = (
            self.aggregated_order_info.groupby(by=ES_COL_PREFIX + OrderColumns.ID)[
                ES_COL_PREFIX + column_to_update
            ]
            .sum()
            .to_dict()
        )

        order_column_to_update = ModelPrefix.ORDER_DOT + column_to_update

        newo_rows = self.pre_process_df.loc[:, TempColumns.SYNTH_ORDER]
        orders_with_aggr_from_in_file_info_list = (
            self.source_frame.loc[:, [SourceColumns.AGGR_FROM]]
            .drop_duplicates(subset=SourceColumns.AGGR_FROM)
            .stack()
            .to_list()
        )
        orders_with_aggr_from_in_file_info = self.source_frame.loc[
            :, SourceColumns.TS_ORD_NUM
        ].isin(orders_with_aggr_from_in_file_info_list)
        values_from_in_file_lookup = (
            self.target_df.loc[
                newo_rows & orders_with_aggr_from_in_file_info,
                [
                    ModelPrefix.ORDER_DOT + OrderColumns.ID,
                    order_column_to_update,
                ],
            ]
            .drop_duplicates(
                subset=ModelPrefix.ORDER_DOT + OrderColumns.ID, keep="first"
            )
            .set_index(ModelPrefix.ORDER_DOT + OrderColumns.ID)
            .to_dict()[order_column_to_update]
        )

        values_from_in_file_lookup.update(values_from_es_lookup)

        aggr_from_not_null = self.source_frame.loc[:, SourceColumns.AGGR_FROM].notnull()
        ids_with_aggr_not_null = (
            self.source_frame.loc[aggr_from_not_null, [SourceColumns.TS_ORD_NUM]]
            .drop_duplicates(subset=SourceColumns.TS_ORD_NUM)
            .stack()
            .to_list()
        )
        orders_with_aggr_not_null = self.source_frame.loc[
            :, SourceColumns.TS_ORD_NUM
        ].isin(ids_with_aggr_not_null)
        target_field_is_null_or_zero = self.target_df.loc[
            :, order_column_to_update
        ].isnull() | (self.target_df.loc[:, order_column_to_update] == 0.0)
        orders_to_update = (
            orders_with_aggr_not_null & newo_rows & target_field_is_null_or_zero
        )

        ts_ord_num_to_aggr_from_dict = (
            self.source_frame.loc[
                aggr_from_not_null, [SourceColumns.TS_ORD_NUM, SourceColumns.AGGR_FROM]
            ]
            .groupby(by=SourceColumns.TS_ORD_NUM)[SourceColumns.AGGR_FROM]
            .apply(set)
            .apply(list)
            .to_dict()
        )

        sum_values_dict = {
            key: sum(
                [values_from_in_file_lookup.get(value, 0.0) for value in values_list]
            )
            for key, values_list in ts_ord_num_to_aggr_from_dict.items()
        }

        self.target_df.loc[
            orders_to_update, order_column_to_update
        ] = self.source_frame.loc[orders_to_update, SourceColumns.TS_ORD_NUM].map(
            sum_values_dict
        )

    # temporary fields
    def _temp_aggregated_order_id(self) -> pd.DataFrame:
        """
        Populates for OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID
        """
        return MapConditional.process(
            source_frame=self.source_frame.loc[
                :, [SourceColumns.EVENT, SourceColumns.AGGR_TO, SourceColumns.AGGR_FROM]
            ],
            params=ParamsMapConditional(
                target_attribute=TempColumns.AGGREGATED_ORDER_ID,
                cases=[
                    Case(
                        query=f"`{SourceColumns.EVENT}`.str.fullmatch('{Events.AGGREGATED_TO}', case=False, na=False)",
                        attribute=SourceColumns.AGGR_TO,
                    ),
                    Case(
                        query=f"`{SourceColumns.EVENT}`.str.fullmatch('{Events.AGGREGATED_FROM}', case=False, na=False)",
                        attribute=SourceColumns.AGGR_FROM,
                    ),
                ],
            ),
        )

    def _temp_es_aggregated_order_clients(self) -> pd.DataFrame:
        """
        Aux column for Aggregated Sequence Number logic
        :return: Dataframe with aggregates order client values from ES
        """
        es_clients = pd.Series(data=pd.NA, index=self.source_frame.index)
        order_id_from_ts_ord_num = (
            self.source_frame.loc[:, SourceColumns.TS_ORD_NUM]
            == self.pre_process_df.loc[:, TempColumns.ORDER_ID]
        )
        order_id_from_audit_id_tkt = (
            self.source_frame.loc[:, SourceColumns.AUDIT_ID_TKT]
            == self.pre_process_df.loc[:, TempColumns.ORDER_ID]
        )
        es_clients.loc[order_id_from_ts_ord_num] = self.source_frame.loc[
            order_id_from_ts_ord_num, TempColumns.ES_TS_ORD_NUM_CLIENTS
        ]
        es_clients.loc[order_id_from_audit_id_tkt] = self.source_frame.loc[
            order_id_from_audit_id_tkt, TempColumns.ES_AUDIT_ID_TKT_CLIENTS
        ]

        return pd.DataFrame(
            data=es_clients.values,
            index=self.source_frame.index,
            columns=[TempColumns.ES_CLIENTS],
        )

    def _temp_has_aggregate_info_from_es(self) -> pd.Series:
        """
        Populates a column to act as a mask for aggregate columns with missing info
        for which we want to fetch information from ES
        """
        return pd.Series(
            data=self.aggregated_order_info.loc[
                :, ES_COL_PREFIX + OrderColumns.ID
            ].notnull(),
            index=self.source_frame.index,
            name=TempColumns.HAS_AGGREGATE_INFO_FROM_ES,
        )

    def _temp_buy_sell(self) -> pd.DataFrame:
        """
        Temporary Column for:
            OrderColumns.BUY_SELL
            TempColumns.BUY_SELL_INDICATOR
        Populates from SourceColumns.SIDE
        following map:
            "BUY": "1"
            "COVER": "1"
            "SELL": "2"
            "SHORT": "2"

        if not populated then default value BUY is populated
        """
        # get order sides present in elastic, generated in upstream task
        null_side = self.source_frame.loc[:, SourceColumns.SIDE].isnull()
        self.source_frame.loc[null_side, SourceColumns.SIDE] = self.source_frame.loc[
            null_side, TempColumns.ES_ORDER_SIDES
        ]

        # Filling the blanks for whole group (group by TS_ORD_NUM)
        # with the SIDE value contained in record "SELL ORDER PRE ALLOCATED"
        temp_df = self.source_frame.loc[
            :,
            [
                SourceColumns.TS_ORD_NUM,
                SourceColumns.EVENT,
                SourceColumns.SIDE,
                SourceColumns.DATE,
                SourceColumns.TIME,
                SourceColumns.AGGR_FROM,
            ],
        ]

        aggregated_from_mask = (
            temp_df.loc[:, SourceColumns.EVENT] == Events.AGGREGATED_FROM
        )

        temp_df_to_dict = (
            temp_df.loc[~null_side, [SourceColumns.TS_ORD_NUM, SourceColumns.SIDE]]
            .set_index(SourceColumns.TS_ORD_NUM)
            .to_dict()[SourceColumns.SIDE]
        )

        temp_df.loc[aggregated_from_mask & null_side, SourceColumns.SIDE] = (
            temp_df.loc[aggregated_from_mask & null_side, SourceColumns.AGGR_FROM]
            .map(temp_df_to_dict)
            .fillna(pd.NA)
        )

        propagate = propagate_values_by_group(
            df=temp_df,
            group_columns=[SourceColumns.TS_ORD_NUM],
            propagate_column=SourceColumns.SIDE,
        )

        if not propagate.empty:
            temp_df[TempColumns.TEMP_COL_1] = propagate
        else:
            temp_df[TempColumns.TEMP_COL_1] = pd.NA

        temp_df[SourceColumns.SIDE] = (
            temp_df[SourceColumns.SIDE].fillna(temp_df[TempColumns.TEMP_COL_1])
        ).fillna("BUY")

        return MapValue.process(
            source_frame=temp_df,
            params=ParamsMapValue(
                source_attribute=SourceColumns.SIDE,
                target_attribute=TempColumns.BUY_SELL,
                case_insensitive=True,
                value_map={
                    "BUY": "1",
                    "COVER": "1",
                    "SELL": "2",
                    "SHORT": "2",
                },
            ),
            auditor=self.auditor,
        )

    def _temp_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Temporary column for:
            OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
            OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
        Populates from TempColumns.BUY_SELL temporary column according to
        the map {"1": "BUYI", "2": "SELL"}
        Contains data from SourceColumns.SIDE
        """
        return MapValue.process(
            source_frame=self.pre_process_df,
            params=ParamsMapValue(
                source_attribute=TempColumns.BUY_SELL,
                target_attribute=TempColumns.BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "1": BuySellIndicator.BUYI.value,
                    "2": BuySellIndicator.SELL.value,
                },
            ),
            auditor=self.auditor,
        )

    def _temp_currency_code(self) -> pd.DataFrame:
        """
        Temporary column used for:
            OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
            and Instrument Identifiers 'input-currency'
        Populates from SourceColumns.CURRENCY
        """
        return ConvertMinorToMajor.process(
            source_frame=self.pre_process_df,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=TempColumns.CURRENCY,
                target_ccy_attribute=TempColumns.CURRENCY_CODE,
            ),
        )

    def _temp_price(self) -> pd.DataFrame:
        """
        Temporary column for:
            OrderColumns.PRICE_FORMING_DATA_PRICE
            OrderColumns.TRANSACTION_DETAILS_PRICE
        Populates from SourceColumns.FILL_PRICE
        """
        price_currency_df = pd.concat(
            [
                self.source_frame.loc[
                    :, [SourceColumns.PRICE, SourceColumns.EXEC_LFILL_PRC]
                ],
                self.pre_process_df.loc[:, TempColumns.CURRENCY],
            ],
            axis=1,
        )
        temp_df = pd.concat(
            [
                self.source_frame.loc[:, [SourceColumns.FUNCTION, SourceColumns.EVENT]],
                ConvertMinorToMajor.process(
                    source_frame=price_currency_df,
                    params=ParamsConvertMinorToMajor(
                        source_ccy_attribute=TempColumns.CURRENCY,
                        source_price_attribute=SourceColumns.PRICE,
                        target_price_attribute=TempColumns.TEMP_COL_1,
                    ),
                ),
                ConvertMinorToMajor.process(
                    source_frame=price_currency_df,
                    params=ParamsConvertMinorToMajor(
                        source_ccy_attribute=TempColumns.CURRENCY,
                        source_price_attribute=SourceColumns.EXEC_LFILL_PRC,
                        target_price_attribute=TempColumns.TEMP_COL_2,
                    ),
                ),
            ],
            axis=1,
        )

        return MapConditional.process(
            source_frame=temp_df,
            params=ParamsMapConditional(
                target_attribute=TempColumns.PRICE,
                cases=[
                    Case(
                        query=f"`{SourceColumns.FUNCTION}`.str.fullmatch('{Function.TSOX}', case=False, na=False)",
                        attribute=TempColumns.TEMP_COL_1,
                    ),
                    Case(
                        query=f"(`{SourceColumns.FUNCTION}`.str.fullmatch('{Function.EMSX}', case=False, na=False)) &"
                        f"(`{SourceColumns.EVENT}`).str.contains('E-SENT|E-WORK|M-WORK', case=False, na=False)",
                        attribute=TempColumns.TEMP_COL_1,
                    ),
                    Case(
                        query=f"(`{SourceColumns.FUNCTION}`.str.fullmatch('{Function.EMSX}', case=False, na=False)) &"
                        f"(`{SourceColumns.EVENT}`).str.contains('E-PFIL|E-FILL|M-FILL', case=False, na=False)",
                        attribute=TempColumns.TEMP_COL_2,
                    ),
                ],
            ),
        )[TempColumns.PRICE].fillna(temp_df[TempColumns.TEMP_COL_1])

    def _temp_order_status(self) -> pd.DataFrame:
        """
        Create temporary column TempColumns.ORDER_STATUS which is used in
        OrderColumns.EXECUTION_DETAILS_ORDER_STATUS
        """
        new_cash_collateral_ticket_query = f"`{SourceColumns.EVENT}`.isin(['{Events.NEW_CASH_TICKET}','{Events.NEW_COLLATERAL_TICKET}'])"
        record_type_query = f"`{SourceColumns.RECORD_TYPE}`.isin(['PMR','PRA','PRC'])"

        temp_df = MapConditional.process(
            source_frame=pd.concat(
                objs=[
                    self.source_frame,
                    self.pre_process_df.loc[:, TempColumns.ORDER_FROM_AUDIT_ID_TKT],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=TempColumns.ORDER_STATUS,
                cases=[
                    Case(
                        query="index == index",
                        value=OrderStatus.CHMO.value,
                    ),
                    Case(
                        query=f"`{SourceColumns.EVENT}`.str.upper().str.startswith('E-PFIL', na=False)",
                        value=OrderStatus.PARF.value,
                    ),
                    Case(
                        query=f"`{SourceColumns.EVENT}`.str.upper().str.startswith("
                        f"'ELECTRONIC PARTIAL FILL', na=False)",
                        value=OrderStatus.PARF.value,
                    ),
                    Case(
                        query=f"`{SourceColumns.EVENT}`.str.upper().str.startswith('E-FILL', na=False)",
                        value=OrderStatus.FILL.value,
                    ),
                    Case(
                        query=f"`{SourceColumns.EVENT}`.str.upper().str.startswith('ELECTRONIC FILL', na=False)",
                        value=OrderStatus.FILL.value,
                    ),
                    Case(
                        query=f"`{SourceColumns.EVENT}`.str.upper().str.startswith('EXECUTION OCCURRED', na=False)",
                        value=OrderStatus.FILL.value,
                    ),
                    Case(
                        query=f"`{SourceColumns.EVENT}`.str.upper().str.startswith('M-FILL: MANU FILL OCCURRED', na=False)",
                        value=OrderStatus.FILL.value,
                    ),
                    Case(
                        query=f"`{SourceColumns.EVENT}`.str.upper().str.startswith('CANCEL', na=False)",
                        value=OrderStatus.CAME.value,
                    ),
                    Case(
                        query=f"`{SourceColumns.TS_ORD_NUM}`.isnull()",
                        value=OrderStatus.CHMO.value,
                    ),
                    Case(
                        query=f"(`{SourceColumns.TS_ORD_NUM}`.isnull()) & (`{SourceColumns.EVENT}`.str.upper().str.startswith('CANCEL', na=False))",
                        value=OrderStatus.CAME.value,
                    ),
                    Case(
                        query=f"({new_cash_collateral_ticket_query}) & ~((`{TempColumns.ORDER_FROM_AUDIT_ID_TKT}`) & ({record_type_query}))",
                        value=OrderStatus.FILL.value,
                    ),
                ],
            ),
        )
        return pd.DataFrame(
            data=self.source_frame.loc[:, TempColumns.ORDER_STATUS]
            .fillna(temp_df[TempColumns.ORDER_STATUS])
            .values,
            index=self.source_frame.index,
            columns=[TempColumns.ORDER_STATUS],
        )

    def _temp_date_time(self) -> pd.DataFrame:
        """
        Temporary column used in:
            TempColumns.SYS_DATE_TIME
            TempColumns.SYS_DATE

        and derived from SourceColumns.DATE and SourceColumn.TIME
        """
        return JoinDateAndTimeFormat.process(
            source_frame=self.source_frame,
            params=ParamsJoinDateAndTimeFormat(
                source_date_attribute=SourceColumns.DATE,
                source_time_attribute=SourceColumns.TIME,
                target_attribute=TempColumns.DATE_TIME,
                source_formats=["%Y%m%d", "%H:%M:%S"],
                target_format=DatetimeFormat.DATETIME,
                timezone_info=TIME_ZONE,
            ),
        )

    def _temp_route(self) -> pd.DataFrame:
        """
        Temporary Column to detect if the order is a route.
        Derived from SourceColumns.FUNCTION and SourceColumns.EVENT
        """
        function_tsox = (
            self.source_frame.loc[:, SourceColumns.FUNCTION]
            .astype(str)
            .str.fullmatch(Function.TSOX, case=False, na=False)
        )
        event_working_order_or_aggregated_to = (
            self.source_frame.loc[:, SourceColumns.EVENT]
            .astype(str)
            .str.contains(
                f"WORKING ORDER|VCON: TRADE AFFIRMED|{Events.AGGREGATED_TO}",
                case=False,
                na=False,
            )
        )
        tsox_route_query = function_tsox & event_working_order_or_aggregated_to

        function_emsx = (
            self.source_frame.loc[:, SourceColumns.FUNCTION]
            .astype(str)
            .str.fullmatch(Function.EMSX, case=False, na=False)
        )
        event_electronic_or_aggregated_to = (
            self.source_frame.loc[:, SourceColumns.EVENT]
            .astype(str)
            .str.contains(
                f"^E-SENT|^E-WORK|^M-WORK|{Events.AGGREGATED_TO}",
                case=False,
                na=False,
            )
        )
        emsx_route_query = function_emsx & event_electronic_or_aggregated_to

        record_type_mrc = (
            self.source_frame.loc[:, SourceColumns.RECORD_TYPE]
            .astype(str)
            .str.fullmatch("MRC", case=False, na=False)
        )
        event_new_master_ticket = (
            self.source_frame.loc[:, SourceColumns.EVENT]
            .astype(str)
            .str.fullmatch(Events.NEW_MASTER_TICKET, case=False, na=False)
        )
        mrc_route_query = record_type_mrc & event_new_master_ticket

        is_route_query = tsox_route_query | emsx_route_query | mrc_route_query

        return pd.DataFrame(
            data=is_route_query.values,
            index=self.source_frame.index,
            columns=[TempColumns.ROUTE],
        )

    def _temp_sys_date_time(self) -> pd.DataFrame:
        """
        Temporary column used in:
            OrderColumns.TIMESTAMPS_ORDER_RECEIVED
            OrderColumns.TIMESTAMPS_ORDER_SUBMITTED
            OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED
            OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME
            OrderColumns.TIMESTAMPS_TRADING_DATE_TIME

        Derived from SourceColumns.SYS_UTCDATETIME
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.SYS_UTCDATETIME,
                source_attribute_format="%Y-%m-%dT%H:%M:%S.%f%z",
                target_attribute=TempColumns.SYS_DATE_TIME,
                convert_to=ConvertTo.DATETIME,
            ),
        )[TempColumns.SYS_DATE_TIME].fillna(self.pre_process_df[TempColumns.DATE_TIME])

    def _temp_sys_date(self) -> pd.DataFrame:
        """
        Temporary column used in OrderColumns.DATE
        and derived from SourceColumns.SYS_UTCDATETIME
        """
        temp_df = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.DATE,
                source_attribute_format="%Y%m%d",
                target_attribute=TempColumns.TEMP_COL_1,
                convert_to=ConvertTo.DATE,
            ),
        )

        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.SYS_UTCDATETIME,
                source_attribute_format="%Y-%m-%dT%H:%M:%S.%f%z",
                target_attribute=TempColumns.SYS_DATE,
                convert_to=ConvertTo.DATE,
            ),
        )[TempColumns.SYS_DATE].fillna(temp_df[TempColumns.TEMP_COL_1])

    def _temp_parent_sys_date(self) -> pd.DataFrame:
        """
        Propagates the synthetic orders (NEWO order's) TempColumns.SYS_DATE_TIME to the child records (non NEWO records)
            to populate timestamps.orderReceived
        Propagates the Route events TempColumns.SYS_DATE_TIME to the next events to populate timestamps.orderSubmitted
        """

        temp_df = self.pre_process_df.loc[
            :,
            [
                TempColumns.ORDER_ID,
                TempColumns.SYNTH_ORDER,
                TempColumns.SYS_DATE_TIME,
                TempColumns.ROUTE,
            ],
        ]
        temp_df[TempColumns.PARENT_SYS_DATE_TIME] = pd.NA
        temp_df[TempColumns.PARENT_SYS_DATE_TIME] = temp_df.loc[
            temp_df[TempColumns.SYNTH_ORDER], TempColumns.SYS_DATE_TIME
        ]

        order_received = propagate_values_by_group(
            df=temp_df,
            group_columns=[TempColumns.ORDER_ID],
            propagate_column=TempColumns.PARENT_SYS_DATE_TIME,
        )

        route_mask = self.pre_process_df.loc[:, TempColumns.ROUTE]
        temp_df.loc[route_mask, TempColumns.ROUTE_SYS_DATE_TIME] = temp_df.loc[
            route_mask, TempColumns.SYS_DATE_TIME
        ]

        order_submitted = (
            temp_df.groupby([TempColumns.ORDER_ID], as_index=False)[
                [TempColumns.ROUTE_SYS_DATE_TIME]
            ]
            .apply(lambda x: x.fillna(x.ffill()))
            .fillna(pd.NA)
        )

        return pd.concat(objs=[order_received, order_submitted], axis=1)

    def _temp_quantities(self) -> pd.DataFrame:
        """
        Creates two columns:
            Quantity as: SourceColumns.FILL if present and SourceColumns.FUNCTION == 'TSOX',
                else SourceColumns.EXEC_LFILL_AMT
                If order id is populated from SourceColumns.AUDIT_ID_TKT and SourceColumns.EVENT is in
                    [Events.NEW_CASH_TICKET, Events.NEW_COLLATERAL_TICKET] populate from SourceColumns.QUANTITY
            Remaining quantity as: SourceColumns.QUANTITY - Quantity
        Temporary columns for:
            OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
            OrderColumns.TRANSACTION_DETAILS_QUANTITY
            OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY
        """
        fill_or_exec_lfill_amt = pd.Series(data=pd.NA, index=self.source_frame.index)
        quantity_or_initial_quantity = pd.Series(
            data=pd.NA, index=self.source_frame.index
        )
        tsox_and_fill_mask = (
            self.source_frame.loc[:, SourceColumns.FUNCTION] == Function.TSOX
        ) & (self.source_frame.loc[:, SourceColumns.FILL].notnull())
        fill_or_exec_lfill_amt.loc[tsox_and_fill_mask] = self.source_frame.loc[
            tsox_and_fill_mask, SourceColumns.FILL
        ]
        fill_or_exec_lfill_amt = fill_or_exec_lfill_amt.fillna(
            self.source_frame.loc[:, SourceColumns.EXEC_LFILL_AMT]
        ).fillna(0.0)

        order_id_from_auditidtkt = self.pre_process_df.loc[
            :, TempColumns.ORDER_FROM_AUDIT_ID_TKT
        ]
        event_new_cash_or_new_collateral = self.source_frame.loc[
            :, SourceColumns.EVENT
        ].isin([Events.NEW_CASH_TICKET, Events.NEW_COLLATERAL_TICKET])

        traded_quantity = copy.deepcopy(fill_or_exec_lfill_amt)
        traded_quantity.loc[
            order_id_from_auditidtkt & event_new_cash_or_new_collateral
        ] = self.source_frame.loc[
            order_id_from_auditidtkt & event_new_cash_or_new_collateral,
            SourceColumns.QUANTITY,
        ]

        record_type_no_quantity = self.source_frame.loc[
            :, SourceColumns.RECORD_TYPE
        ].isin(["PRM", "PRA", "PRC"])

        traded_quantity.loc[
            order_id_from_auditidtkt
            & event_new_cash_or_new_collateral
            & record_type_no_quantity,
        ] = 0.0

        quantity_or_initial_quantity.loc[tsox_and_fill_mask] = self.source_frame.loc[
            tsox_and_fill_mask, SourceColumns.QUANTITY
        ]

        quantity_or_initial_quantity = quantity_or_initial_quantity.fillna(
            self.pre_process_df.loc[:, TempColumns.INITIAL_QUANTITY_PROPAGATED]
        ).fillna(0.0)

        return pd.concat(
            [
                pd.DataFrame(
                    data=traded_quantity.values,
                    index=self.source_frame.index,
                    columns=[TempColumns.QUANTITY],
                ),
                pd.DataFrame(
                    data=(quantity_or_initial_quantity - fill_or_exec_lfill_amt).values,
                    index=self.source_frame.index,
                    columns=[TempColumns.REMAINING_QUANTITY],
                ),
            ],
            axis=1,
        )

    def _temp_settlement_date(self) -> pd.DataFrame:
        """
        Temporary column used in OrderColumns.TRANSACTION_DETAILS_SETTLEMENT_DATE
        and derived from SourceColumns.SETTLE_DT
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.SETTLE_DT,
                source_attribute_format="%Y%m%d",
                target_attribute=TempColumns.SETTLE_DT,
                convert_to=ConvertTo.DATE,
            ),
        )

    def _temp_order_id(self) -> pd.DataFrame:
        """
        Temporary column used in:
            OrderColumns.ID
            OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE
        and derived from SourceColumns.TS_ORD_NUM, SourceColumns.AUDIT_ID_TKT
            or SourceColumns.AUDIT_ID + "|" + __source_index__
        """
        temp_df = self.source_frame.loc[
            :,
            [
                SourceColumns.AUDIT_ID,
                SourceColumns.TS_ORD_NUM,
                SourceColumns.AUDIT_ID_TKT,
            ],
        ]
        temp_df[TempColumns.TEMP_COL_1] = self.source_frame.index
        temp_df = pd.concat(
            [
                temp_df,
                ConcatAttributes.process(
                    source_frame=temp_df,
                    params=ParamsConcatAttributes(
                        source_attributes=[
                            SourceColumns.AUDIT_ID,
                            TempColumns.TEMP_COL_1,
                        ],
                        delimiter=Delimiters.PIPE,
                        target_attribute=TempColumns.TEMP_COL_2,
                    ),
                ),
            ],
            axis=1,
        )

        return MapConditional.process(
            source_frame=temp_df,
            params=ParamsMapConditional(
                target_attribute=TempColumns.ORDER_ID,
                cases=[
                    Case(
                        query=f"`{SourceColumns.TS_ORD_NUM}`.notnull()",
                        attribute=SourceColumns.TS_ORD_NUM,
                    ),
                    Case(
                        query=f"(`{SourceColumns.TS_ORD_NUM}`.isnull()) & (`{SourceColumns.AUDIT_ID_TKT}`.notnull())",
                        attribute=SourceColumns.AUDIT_ID_TKT,
                    ),
                    Case(
                        query=f"(`{SourceColumns.TS_ORD_NUM}`.isnull()) & (`{SourceColumns.AUDIT_ID_TKT}`.isnull())",
                        attribute=TempColumns.TEMP_COL_2,
                    ),
                ],
            ),
        )

    def _temp_transaction_reference_number(self) -> pd.DataFrame:
        """
        Temporary column used in:
            OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO
            OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO
        Populates from first 50 characters of SourceColumns.TRN or SourceColumns.AUDIT_ID + sourceIndex
        """
        temp_df = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_COL_1,
                cases=[
                    Case(
                        query=f"`{SourceColumns.TRN}`.notnull()",
                        attribute=SourceColumns.TRN,
                    ),
                    Case(
                        query=f"`{SourceColumns.TRN}`.isnull()",
                        attribute=SourceColumns.AUDIT_ID,
                    ),
                ],
            ),
        )
        temp_df = temp_df.assign(**{TempColumns.TEMP_COL_2: self.source_frame.index})
        temp_df = ConcatAttributes.process(
            source_frame=temp_df,
            params=ParamsConcatAttributes(
                source_attributes=[
                    TempColumns.TEMP_COL_1,
                    TempColumns.TEMP_COL_2,
                ],
                delimiter=Delimiters.PIPE,
                target_attribute=TempColumns.TEMP_COL_3,
            ),
        )
        return pd.DataFrame(
            data=temp_df.loc[:, TempColumns.TEMP_COL_3].str[:50].values,
            index=temp_df.index,
            columns=[TempColumns.TRANSACTION_REFERENCE_NUMBER],
        )

    def _temp_trading_capacity(self) -> pd.DataFrame:
        """
        If BKRTRADCAP populated, get value within parentheses.
        Map to Schema Enum
        """

        bkr_trade_cap_regex_extract = (
            self.source_frame.loc[:, SourceColumns.BKR_TRADE_CAP]
            .astype(str)
            .str.extract(pat=r"\((\w+)\)")[0]
            .str.lower()
            .fillna(pd.NA)
        )
        bkr_trade_cap_regex_extract.name = SourceColumns.BKR_TRADE_CAP

        return run_map_value(
            source_frame=bkr_trade_cap_regex_extract.to_frame(),
            params=MapValueParams(
                source_attribute=SourceColumns.BKR_TRADE_CAP,
                target_attribute=TempColumns.TRADING_CAPACITY,
                value_map={
                    "aotc": TradingCapacity.AOTC.value,
                    "deal": TradingCapacity.DEAL.value,
                    "mtch": TradingCapacity.MTCH.value,
                },
                default_value=pd.NA,
            ),
        )

    # Party Identifiers Fields
    def _temp_party_ids_executing_entity(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Adds lei prefix to the column
        """
        return GetTenantLEI.process(
            source_frame=self.source_frame,
            params=ParamsGetTenantLEI(
                target_lei_column=TempColumns.EXECUTING_ENTITY,
                target_column_prefix=PartyPrefix.LEI,
            ),
        )

    def _temp_party_ids_trader(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        If order id is populated from SourceColumns.AUDIT_ID_TKT:
            Populate from SourceColumns.USER_UUID
        Else:
            Populate from TempColumns.TRADER_UUID
        Adds id prefix to the column
        """
        trader_or_user_uuid = pd.Series(data=pd.NA, index=self.source_frame.index)

        order_id_from_auditidtkt = self.pre_process_df.loc[
            :, TempColumns.ORDER_FROM_AUDIT_ID_TKT
        ]

        trader_or_user_uuid[order_id_from_auditidtkt] = self.source_frame.loc[
            order_id_from_auditidtkt, SourceColumns.USER_UUID
        ]

        trader_or_user_uuid[~order_id_from_auditidtkt] = self.pre_process_df.loc[
            ~order_id_from_auditidtkt, TempColumns.TRADER_UUID
        ]

        return pd.DataFrame(
            data={
                TempColumns.TRADER: PartyPrefix.ID + trader_or_user_uuid.values,
            },
            index=self.source_frame.index,
        )

    def _temp_party_ids_client(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from TempColumns.ACCOUNT_PROPAGATED
            except when related to aggregated newo orders where it gets from SourceColumns.ACCOUNT
        Adds id prefix to the column
        """
        aggregated_orders_rows = self.get_aggregated_order_rows()

        account_propagated = self.pre_process_df.loc[:, TempColumns.ACCOUNT_PROPAGATED]
        account_propagated.loc[aggregated_orders_rows] = self.source_frame.loc[
            aggregated_orders_rows, SourceColumns.ACCOUNT
        ]
        not_null_accounts = account_propagated.notnull()
        account_propagated.loc[not_null_accounts] = (
            account_propagated.loc[not_null_accounts].astype(str).str[1:3]
        )

        return pd.DataFrame(
            data={
                TempColumns.CLIENT: PartyPrefix.ID + account_propagated.values,
            },
            index=self.source_frame.index,
        )

    def _temp_party_ids_execution_within_firm(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from SourceColumns.STRATNAME
        Adds id and pm prefix to the column
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.EXECUTION_WITHIN_FIRM,
                cases=[
                    Case(
                        query=f"`{SourceColumns.STRAT_NAME}`.str.contains('AQUA|ARC|BLITZ|CATCH|CLOSE"
                        f"|DPOUNCE|DYNAPART|DYNVOL|FAN|Iceberg|ICENINJA|IS|ISDARK|OPPORTUNI|OPTIMUS|PART"
                        f"|POV|POV1|POV-EU|SEEK|SHADOW|SMART|SmartMOC|SNIPER|SOR|TCLOSE|TGT CLOSE|TWAP"
                        f"|TWAP1|ULTRA|VOLPART|VWAP|VWAP1|WORK', case=False, na=False)",
                        attribute=SourceColumns.STRAT_NAME,
                        attribute_prefix=PartyPrefix.ID + ShellPrefix.ALGO,
                    ),
                ],
            ),
        )

    def _temp_party_ids_investment_decision_maker(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from SourceColumns.PM_UUID
        Adds id prefix to the column
        """
        return pd.DataFrame(
            data={
                TempColumns.INVESTMENT_DECISION_MAKER: PartyPrefix.ID
                + self.source_frame.loc[:, SourceColumns.PM_UUID].values,
            },
            index=self.source_frame.index,
        )

    def _temp_party_ids_buyer(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from TempColumns.CLIENT

        For aggregated orders, populates with CLIENT for order allocated and EXECUTING_ENTITY for the other events

        Adds id prefix to the column
        """
        buyer = pd.Series(data=pd.NA, index=self.source_frame.index)
        buyer.loc[:] = self.pre_process_df.loc[:, TempColumns.CLIENT]

        allocated_orders = (
            self.source_frame.loc[:, SourceColumns.EVENT]
            .astype(str)
            .str.fullmatch(
                f"{Events.ORDER_ALLOCATED}|{Events.GTD_ORDER_ROLLED_OFF_BLOTTER}",
                case=False,
                na=False,
            )
        )

        aggregated_orders_rows = self.get_aggregated_order_rows()

        buyer.loc[aggregated_orders_rows & ~allocated_orders] = self.pre_process_df.loc[
            aggregated_orders_rows & ~allocated_orders,
            TempColumns.EXECUTING_ENTITY,
        ]

        return pd.DataFrame(
            data={
                TempColumns.BUYER: buyer.values,
            },
            index=self.source_frame.index,
        )

    def _temp_party_ids_seller(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Adds id prefix to the column
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.COUNTERPARTY,
                target_attribute=TempColumns.SELLER,
            ),
            auditor=self.auditor,
        )

    def _temp_party_ids_counterparty(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from SourceColumns.BROKER
        Adds id prefix to the column.
        """
        competing_quote_entered_query = (
            f"`{SourceColumns.EVENT}`.str.fullmatch('{Events.COMPETING_QUOTE_ENTERED}',"
            f"case=False, na=False)"
        )

        id_broker = pd.DataFrame(
            data=PartyPrefix.ID + self.source_frame.loc[:, SourceColumns.BROKER].values,
            index=self.source_frame.index,
            columns=[TempColumns.COUNTERPARTY],
        )

        return MapConditional.process(
            source_frame=pd.concat(
                [self.source_frame.loc[:, SourceColumns.EVENT], id_broker], axis=1
            ),
            params=ParamsMapConditional(
                target_attribute=TempColumns.COUNTERPARTY,
                cases=[
                    Case(
                        query=f"~({competing_quote_entered_query})",
                        attribute=TempColumns.COUNTERPARTY,
                    ),
                    Case(query=competing_quote_entered_query, as_empty=True),
                ],
            ),
        )

    def _temp_order_from_auditidtkt(self) -> pd.Series:
        """
        Mask to determine if an order id is populated from SourceColumns.AUDIT_ID_TKT
        """
        order_id_from_auditidtkt = (
            self.source_frame.loc[:, SourceColumns.TS_ORD_NUM].isnull()
            & self.source_frame.loc[:, SourceColumns.AUDIT_ID_TKT].notnull()
        )

        return pd.Series(
            data=order_id_from_auditidtkt.values,
            index=self.source_frame.index,
            name=TempColumns.ORDER_FROM_AUDIT_ID_TKT,
        )

    # Instrument Identifiers Fields
    def _temp_instr_ids_asset_class(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates based on values of SourceColumns.SECURITY_TYPE
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.ASSET_CLASS,
                cases=[
                    Case(
                        query=f"`{SourceColumns.ASSET_CLASS}`.str.fullmatch('Equity', case=False, na=False)",
                        value=AssetClass.EQUITY,
                    ),
                    Case(
                        query=f"`{SourceColumns.FUNCTION}`.str.fullmatch('Equity', case=False, na=False) &"
                        f"`{SourceColumns.ASSET_CLASS}`.isnull()",
                        value=AssetClass.EQUITY,
                    ),
                    Case(
                        query=f"`{SourceColumns.ASSET_CLASS}`.str.fullmatch('Equity', case=False, na=False)",
                        value=AssetClass.EQUITY,
                    ),
                    Case(
                        query=f"`{SourceColumns.ASSET_CLASS}`.str.fullmatch("
                        f"'FICORP|FIGOVT|FIMMKT|FIMTGE|FIPFD|SFT|LOSWARRANTS', case=False, na=False)",
                        value=AssetClass.BOND,
                    ),
                    Case(
                        query=f"`{SourceColumns.ASSET_CLASS}`.str.fullmatch('CDSWINDEX', case=False, na=False)",
                        value=AssetClass.CDS_INDEX,
                    ),
                    Case(
                        query=f"`{SourceColumns.ASSET_CLASS}`.str.fullmatch('CDSWSINGLE|CDS', case=False, na=False)",
                        value=AssetClass.CDS_SINGLE_STOCK,
                    ),
                    Case(
                        query=f"`{SourceColumns.ASSET_CLASS}`.str.fullmatch("
                        f"'FUTUREINDEX|FUTURECOMMODITY', case=False, na=False)",
                        value=AssetClass.FUTURE,
                    ),
                    Case(
                        query=f"`{SourceColumns.ASSET_CLASS}`.str.fullmatch('FXSPOT', case=False, na=False)",
                        value=AssetClass.FX_SPOT,
                    ),
                    Case(
                        query=f"`{SourceColumns.ASSET_CLASS}`.str.fullmatch('FXFORWARD', case=False, na=False)",
                        value=AssetClass.FX_FORWARD,
                    ),
                    Case(
                        query=f"`{SourceColumns.ASSET_CLASS}`.str.fullmatch("
                        f"'FXFORWARDSWAP|FXSPOTSWAP', case=False, na=False)",
                        value=AssetClass.FX_SWAP,
                    ),
                    Case(
                        query=f"`{SourceColumns.ASSET_CLASS}`.str.fullmatch('TRS', case=False, na=False)",
                        value=AssetClass.TRS_SINGLE_STOCK,
                    ),
                ],
            ),
        )

    # Instrument Fallback
    def _temp_instr_fallback_instrument_full_name(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.SEC_DESC and/or SourceColumns.SECURITY
        """
        temp_df = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                target_attribute=TempColumns.TEMP_COL_1,
                source_attribute=SourceColumns.SECURITY,
                prefix="(",
                suffix=")",
            ),
        )
        temp_df = ConcatAttributes.process(
            source_frame=pd.concat(
                [temp_df, self.source_frame.loc[:, SourceColumns.SEC_DESC]], axis=1
            ),
            params=ParamsConcatAttributes(
                source_attributes=[
                    SourceColumns.SEC_DESC,
                    TempColumns.TEMP_COL_1,
                ],
                target_attribute=TempColumns.TEMP_COL_2,
            ),
        )

        return MapConditional.process(
            source_frame=pd.concat(
                [
                    temp_df,
                    self.source_frame.loc[
                        :, [SourceColumns.SECURITY, SourceColumns.SEC_DESC]
                    ],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=TempColumns.INSTR_FULL_NAME,
                cases=[
                    Case(
                        query=f"`{SourceColumns.SEC_DESC}`.isnull()",
                        attribute=SourceColumns.SECURITY,
                    ),
                    Case(
                        query=f"`{SourceColumns.SEC_DESC}`.notnull()",
                        attribute=TempColumns.TEMP_COL_2,
                    ),
                ],
            ),
        )

    def _temp_instr_fallback_bond_maturity_date(self) -> pd.DataFrame:
        """
        Temp column used in Instrument Fallback
        Populates from SourceColumns.SECURITY,
        if Event == `NEW COLLATERAL TICKET` use SourceColumns.TERM_DATE
        """

        security = ConvertDatetime.process(
            source_frame=self.source_frame.loc[:, SourceColumns.SECURITY]
            .str[-8:]
            .to_frame(),
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.SECURITY,
                source_attribute_format="%m/%d/%y",
                target_attribute=TempColumns.TEMP_COL_1,
                convert_to=ConvertTo.DATE,
            ),
        )
        term_date = ConvertDatetime.process(
            source_frame=self.source_frame.loc[:, [SourceColumns.TERM_DATE]],
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.TERM_DATE,
                source_attribute_format="%Y%m%d",
                target_attribute=TempColumns.TEMP_COL_2,
                convert_to=ConvertTo.DATE,
            ),
        )

        asset_class_query = f"(`{SourceColumns.ASSET_CLASS}`.isin(['SFT','FIMMKT','FICORP','FIGOVT','FIPFD']))"
        event_query = f"(`{SourceColumns.EVENT}`.str.fullmatch('{Events.NEW_COLLATERAL_TICKET}', case=False, na=False))"

        return MapConditional.process(
            source_frame=pd.concat(
                [
                    security,
                    term_date,
                    self.source_frame.loc[
                        :, [SourceColumns.EVENT, SourceColumns.ASSET_CLASS]
                    ],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=TempColumns.BOND_MATURITY_DATE,
                cases=[
                    Case(
                        query=f"{asset_class_query}", attribute=TempColumns.TEMP_COL_1
                    ),
                    Case(
                        query=f"{asset_class_query} & {event_query}",
                        attribute=TempColumns.TEMP_COL_2,
                    ),
                ],
            ),
        )

    def _temp_instr_fallback_expiry_date(self) -> pd.DataFrame:
        """
        Temp column used in Instrument Fallback
        Populates from SourceColumns.SECURITY
        """

        # this only works until 2029??
        temp_df = (
            self.source_frame.loc[:, SourceColumns.SECURITY]
            .astype(str)
            .str.split()
            .str.get(0)
            .str[-2:]
            .replace(to_replace=future_expiry_months, regex=True)
            .to_frame()
        )

        future_security = ConvertDatetime.process(
            source_frame=temp_df,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.SECURITY,
                source_attribute_format="%m/%y",
                target_attribute=TempColumns.TEMP_COL_1,
                convert_to=ConvertTo.DATE,
            ),
        )

        extracted_dates = ConvertDatetime.process(
            source_frame=self.source_frame.loc[:, SourceColumns.SECURITY]
            .astype(str)
            .str.extract(pat=r"(\d{2}\/\d{2}\/\d{2})", expand=False)
            .fillna(pd.NA)
            .to_frame(),
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.SECURITY,
                source_attribute_format="%m/%d/%y",
                target_attribute=TempColumns.TEMP_COL_2,
                convert_to=ConvertTo.DATE,
            ),
        )

        return MapConditional.process(
            source_frame=pd.concat(
                [
                    extracted_dates,
                    future_security,
                    self.source_frame.loc[:, SourceColumns.ASSET_CLASS],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=TempColumns.EXPIRY_DATE_FALLBACK,
                cases=[
                    Case(
                        query=f"`{SourceColumns.ASSET_CLASS}`.isin(['FUTURECOMMODITY','FUTUREINDEX'])",
                        attribute=TempColumns.TEMP_COL_1,
                    ),
                    Case(
                        query=f"`{SourceColumns.ASSET_CLASS}`.isin(['FXFORWARD','FXFORWARDSWAP','FXSPOTSWAP','CDS','CDSWINDEX','CDSWSINGLE','IRSSWAPTION','LOWARRANTS','TRS'])",
                        attribute=TempColumns.TEMP_COL_2,
                    ),
                ],
            ),
        )

    def _temp_instr_fallback_delivery_type(self) -> pd.DataFrame:
        """
        Temp column used in Instrument Fallback
        Populates static DeliveryType values conditionally from SourceColumns.EVENT
        """
        return MapConditional.process(
            source_frame=pd.concat(
                [
                    self.pre_process_df.loc[:, [SourceColumns.DELIV_TYPE]],
                    self.source_frame.loc[:, [SourceColumns.EVENT]],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=TempColumns.DELIVERY_TYPE,
                cases=[
                    Case(
                        query=f"`{SourceColumns.DELIV_TYPE}` != '0'",
                        value=pd.NA,
                    ),
                    Case(
                        query=f"(`{SourceColumns.EVENT}`.str.contains('COLL', case=False, na=False))",
                        value=DeliveryType.PHYS.value,
                    ),
                    Case(
                        query=f"~(`{SourceColumns.EVENT}`.str.contains('COLL', case=False, na=False))",
                        value=DeliveryType.CASH.value,
                    ),
                ],
            ),
        )

    def _temp_instr_fallback_instrument_id_code(self) -> pd.DataFrame:
        """
        Temp column used in Instrument Fallback
        Populates static DeliveryType values conditionally from SourceColumns.EVENT

        When order id is derived from SourceColumns.AUDIT_ID_TKT,
            only populate when SourceColumns.EVENT contains 'COLLATERAL'
        """
        propagate = propagate_values_by_group(
            df=pd.concat(
                objs=[
                    self.source_frame.loc[:, SourceColumns.ISIN],
                    self.pre_process_df.loc[:, TempColumns.ORDER_ID],
                ],
                axis=1,
            ),
            group_columns=[TempColumns.ORDER_ID],
            propagate_column=SourceColumns.ISIN,
        )

        order_id_from_auditidtkt = self.pre_process_df.loc[
            :, TempColumns.ORDER_FROM_AUDIT_ID_TKT
        ]

        collateral_events = (
            self.source_frame.loc[:, SourceColumns.EVENT]
            .astype(str)
            .str.contains("COLLATERAL", case=False)
        )

        propagate.loc[
            order_id_from_auditidtkt & ~collateral_events, SourceColumns.ISIN
        ] = pd.NA

        if not propagate.empty:
            return propagate.rename(columns={SourceColumns.ISIN: TempColumns.ISIN})
        else:
            return pd.DataFrame(
                data=pd.NA, index=self.source_frame.index, columns=[TempColumns.ISIN]
            )

    # propagate columns methods
    def _propagate_missing_fields(self) -> pd.DataFrame:
        """
        Propagates missing fields for some requested columns.
        Aggregated here and used the same method because the logic tis the same
        :return: dataframe with all columns
        """
        return pd.concat(
            [
                self._propagate_by_order_id(
                    source_column=SourceColumns.TRADER_UUID,
                    target_column=TempColumns.TRADER_UUID,
                ),
                self._propagate_by_order_id(
                    source_column=SourceColumns.ORD_TYPE,
                    target_column=TempColumns.ORD_TYPE,
                ),
                self._propagate_by_order_id(
                    source_column=SourceColumns.CURRENCY,
                    target_column=TempColumns.CURRENCY,
                ),
                self._propagate_by_order_id(
                    source_column=SourceColumns.QUANTITY,
                    target_column=TempColumns.QUANTITY_PROPAGATED,
                ),
                self._propagate_by_order_id(
                    source_column=SourceColumns.ASSET_CLASS,
                    target_column=TempColumns.ASSET_CLASS_PROPAGATED,
                ),
                self._propagate_by_order_id(
                    source_column=SourceColumns.ACCOUNT,
                    target_column=TempColumns.ACCOUNT_PROPAGATED,
                ),
            ],
            axis=1,
        )

    def _propagate_by_order_id(
        self,
        source_column: str,
        target_column: str,
        alternative_df: pd.DataFrame = None,
    ) -> pd.DataFrame:
        """
        Creates a temporary column to be used for:
            Parties Trader
        :param source_column: columns to propagate
        :param target_column: return column name
        :param alternative_df: source dataframe to use instead of self.source_frame
        :return: single column dataframe
        """
        source_df = self.source_frame if alternative_df is None else alternative_df

        # Filling the blanks for whole group (group by TempColumns.ORDER_ID)
        temp_df = pd.concat(
            [
                source_df.loc[:, source_column],
                self.pre_process_df.loc[:, TempColumns.ORDER_ID],
            ],
            axis=1,
        )
        propagate = propagate_values_by_group(
            df=temp_df,
            group_columns=[TempColumns.ORDER_ID],
            propagate_column=source_column,
        )

        if not propagate.empty:
            temp_df[target_column] = propagate
        else:
            temp_df[target_column] = pd.NA

        return temp_df.loc[:, [target_column]]

    # synthetic orders related methods
    def _temp_synthetic_order_column(self) -> pd.DataFrame:
        """
        Created a column to be added to self.pre_process_df that indicates whether the row
            will be used to genereate a synthetic/NEWO order or not
        :return: pd.Dataframe with TempColumns.SYNTH_ORDER column
        """
        temp_df = pd.concat(
            [
                self.pre_process_df.loc[:, TempColumns.ORDER_ID],
                self.source_frame.loc[
                    :,
                    [
                        SourceColumns.SYS_UTCDATETIME,
                        SourceColumns.QUANTITY,
                        SourceColumns.EVENT,
                    ],
                ],
            ],
            axis=1,
        )

        synth_mask = ~(
            temp_df.sort_values(SourceColumns.SYS_UTCDATETIME).duplicated(
                TempColumns.ORDER_ID
            )
        )

        return pd.DataFrame(
            data=synth_mask.values,
            index=synth_mask.index,
            columns=[TempColumns.SYNTH_ORDER],
        )

    def _temp_initial_quantity(self) -> NoReturn:
        """
        Changes the values of previously flagged orders to generate NEWOs
            from them by generating the TempColumns.INITIAL_QUANTITY for:
                _order.priceFormingData.initialQuantity
            And TempColumns.INITIAL_QUANTITY_PROPAGATED for:
                priceFormingData.remainingQuantity
        This _order.priceFormingData.initialQuantity is generated according to the following:
            If C_EVENT(30)contains more than one:
            BUY ORDER PRE ALLOCATED or
            SELL ORDER PRE ALLOCATED
            then sum all F_QUANTITY(20) for each of those events as total qty for synthetic order

            if C_EVENT(30) does not contain
            BUY ORDER PRE ALLOCATED
            SELL ORDER PRE ALLOCATED
            ORDER ROLLED OVER TO NEXT DAY
            then
            Sum up all F_QUANTITY(20) for C_EVENT(30)
            containing
            ELECTRONIC WORKING ORDER or E-SENT: ROUTED TO ELEC BRKR
            for each of those events as total qty for synthetic order

            if all above fails, use F_QUANTITY(20) of the respective synthetic order
        """
        synth_order_mask = self.pre_process_df.loc[:, TempColumns.SYNTH_ORDER]

        # edit quantities
        temp_df = pd.concat(
            [
                self.pre_process_df.loc[:, TempColumns.ORDER_ID],
                self.source_frame.loc[:, [SourceColumns.QUANTITY, SourceColumns.EVENT]],
            ],
            axis=1,
        )

        order_id_groups = temp_df.loc[:, TempColumns.ORDER_ID].unique()
        quantities_df = pd.DataFrame(
            data=pd.NA,
            index=order_id_groups,
            columns=[
                TempColumns.TEMP_COL_1,
                TempColumns.TEMP_COL_2,
                SourceColumns.QUANTITY,
            ],
        )

        # BUY/SELL ORDER PRE ALLOCATED > 1 quantity
        buy_sell_order_sell_pre_allocated_mask = temp_df.loc[
            :, SourceColumns.EVENT
        ].isin([Events.BUY_ORDER_PRE_ALLOCATED, Events.SELL_ORDER_PRE_ALLOCATED])
        over_one_buy_sell_order_sell_pre_allocated_mask = (
            temp_df.loc[
                buy_sell_order_sell_pre_allocated_mask,
                [TempColumns.ORDER_ID, SourceColumns.QUANTITY],
            ]
            .groupby(TempColumns.ORDER_ID)
            .count()
            > 1
        ).loc[:, SourceColumns.QUANTITY]
        if over_one_buy_sell_order_sell_pre_allocated_mask.any():
            buy_sell_order_pre_allocated_quantities = (
                temp_df.loc[
                    buy_sell_order_sell_pre_allocated_mask,
                    [
                        TempColumns.ORDER_ID,
                        SourceColumns.QUANTITY,
                    ],
                ]
                .groupby(TempColumns.ORDER_ID)
                .sum()
            )
            buy_sell_order_pre_allocated_quantities = (
                buy_sell_order_pre_allocated_quantities.loc[
                    over_one_buy_sell_order_sell_pre_allocated_mask, :
                ]
            )
            quantities_df.loc[
                buy_sell_order_pre_allocated_quantities.index, TempColumns.TEMP_COL_1
            ] = buy_sell_order_pre_allocated_quantities.loc[
                buy_sell_order_pre_allocated_quantities.index, SourceColumns.QUANTITY
            ]

        # ELECTRONIC WORKING ORDER quantity
        electronic_working_order_mask = temp_df.loc[:, SourceColumns.EVENT].isin(
            [Events.ELECTRONIC_WORKING_ORDER, Events.E_SENT_ROUTED_TO_ELEC_BRKR]
        )
        electronic_quantities = (
            temp_df.loc[
                electronic_working_order_mask,
                [TempColumns.ORDER_ID, SourceColumns.QUANTITY],
            ]
            .groupby(TempColumns.ORDER_ID)
            .sum()
        )
        quantities_df.loc[
            electronic_quantities.index, TempColumns.TEMP_COL_2
        ] = electronic_quantities.loc[
            electronic_quantities.index, SourceColumns.QUANTITY
        ]

        # default source quantity
        quantities_df.loc[:, SourceColumns.QUANTITY] = (
            temp_df.loc[
                synth_order_mask,
                [TempColumns.ORDER_ID, SourceColumns.QUANTITY],
            ]
            .groupby(TempColumns.ORDER_ID)
            .first()
        )

        # back-fill to get values in order of priority
        quantities_series = quantities_df.bfill(axis="columns").iloc[:, 0]
        quantities_dict = dict(zip(quantities_series.index, quantities_series.values))

        return_df = self.pre_process_df.loc[:, [TempColumns.ORDER_ID]]

        return_df.loc[
            synth_order_mask,
            TempColumns.INITIAL_QUANTITY,
        ] = self.pre_process_df.loc[synth_order_mask, TempColumns.ORDER_ID].map(
            quantities_dict
        )

        return_df.loc[
            :, TempColumns.INITIAL_QUANTITY_PROPAGATED
        ] = self._propagate_by_order_id(
            source_column=TempColumns.INITIAL_QUANTITY,
            target_column=TempColumns.INITIAL_QUANTITY_PROPAGATED,
            alternative_df=return_df,
        )

        return return_df.drop(columns=TempColumns.ORDER_ID)

    # helper methods for repeated code
    def get_aggregated_order_rows(self) -> pd.Series:
        """
        Logic to create a mask to identify rows that belong to aggregated orders
        :return: aggregated order rows mask
        """
        aggregated_orders = (
            self.source_frame.loc[:, SourceColumns.EVENT]
            .astype(str)
            .str.fullmatch(
                f"{Events.ADDED_TO_BASKET}|{Events.AGGREGATED_FROM}|{Events.AGGREGATED_TO}",
                case=False,
                na=False,
            )
        )
        newo_orders = self.pre_process_df.loc[:, TempColumns.SYNTH_ORDER]
        aggregated_newo_orders = aggregated_orders & newo_orders
        aggregated_order_ids = (
            self.pre_process_df.loc[aggregated_newo_orders, TempColumns.ORDER_ID]
            .drop_duplicates()
            .to_list()
        )
        aggregated_orders_rows = self.pre_process_df.loc[:, TempColumns.ORDER_ID].isin(
            aggregated_order_ids
        )
        return aggregated_orders_rows
