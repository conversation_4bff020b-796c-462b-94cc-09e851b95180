import os
import re
from pathlib import Path

import pandas as pd
from se_core_tasks.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from se_core_tasks.currency.convert_minor_to_major import run_convert_minor_to_major
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_elastic_schema.static.reference import OrderRecordType
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import AssetClass
from se_trades_tasks.order_and_tr.static import PartyPrefix
from se_trades_tasks.utilities.data_util import Delimiters

from swarm_tasks.order.feed.aladdin.static import ALADDIN_FILES_PATTERN
from swarm_tasks.order.feed.aladdin.static import CustomOrdType
from swarm_tasks.order.feed.aladdin.static import DevColumns
from swarm_tasks.order.feed.aladdin.static import FileTypeAssetClass
from swarm_tasks.order.feed.aladdin.static import FileTypes
from swarm_tasks.order.feed.aladdin.static import FillSourceColumns
from swarm_tasks.order.feed.aladdin.static import OrderDetailSourceColumns
from swarm_tasks.order.feed.aladdin.static import OrderSourceColumns
from swarm_tasks.order.feed.aladdin.static import PlacementSourceColumns
from swarm_tasks.order.feed.aladdin.static import SecGroupValues
from swarm_tasks.order.feed.aladdin.static import SecTypeValues
from swarm_tasks.order.feed.aladdin.static import TransactionSourceColumns
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.generic.get_tenant_lei import Params as ParamsGetTenantLEI
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_static import MapStatic
from swarm_tasks.transform.map.map_static import Params as ParamsMapStatic
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)
from swarm_tasks.utilities.market.instruments import instrument_utils


class AladdinOrderTransformations(AbstractOrderTransformations):
    """
    Primary transformation for Aladdin Orders
    """

    def _pre_process(self):
        self.file_type_asset_class = self._get_file_type_asset_class()
        self.pre_process_df.loc[
            :, DevColumns.FILE_TYPE_ASSET_CLASS
        ] = self.file_type_asset_class
        self.pre_process_df.loc[:, DevColumns.BUY_SELL] = self._get_buy_sell()
        self.pre_process_df.loc[:, DevColumns.ID] = self._get_id()
        self.pre_process_df.loc[:, DevColumns.ASSET_CLASS] = self._get_asset_class()
        # Populates currency and notional_currency_2
        self._get_currency()
        self.pre_process_df.loc[
            :, DevColumns.TRADING_CAPACITY
        ] = self._get_trading_capacity()

        # Get option type (put or call) from instrument utils from order.ric column
        self.pre_process_df = pd.concat(
            [self.pre_process_df, self._get_option_type()],
            axis=1,
        )

        self.pre_process_df.loc[
            :, DevColumns.INSTRUMENT_CLASSIFICATION
        ] = self._get_instrument_classification()

        self.pre_process_df = pd.concat(
            [self.pre_process_df, self._get_underlying_index_term()],
            axis=1,
        )

        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                ConvertDatetime.process(
                    self.source_frame,
                    params=ParamsConvertDatetime(
                        source_attribute=add_prefix(
                            FileTypes.TRANSACTION, TransactionSourceColumns.MATURITY
                        ),
                        target_attribute=DevColumns.EXPIRY_DATE,
                        convert_to=ConvertTo.DATE,
                    ),
                ),
            ],
            axis=1,
        )

        self.pre_process_df = pd.concat(
            [self.pre_process_df, self._get_option_strike_price()],
            axis=1,
        )

        self.pre_process_df = pd.concat(
            [self.pre_process_df, self._get_underlying_symbol()],
            axis=1,
        )

    def process(self) -> pd.DataFrame:
        self.pre_process()
        self.buy_sell()
        self.data_source_name()
        self.date()
        self.execution_details_buy_sell_indicator()
        self.execution_details_limit_price()
        self.execution_details_order_status()
        self.execution_details_order_type()
        self.execution_details_stop_price()
        self.execution_details_outgoing_order_addl_info()
        self.execution_details_trading_capacity()
        self.execution_details_validity_period()
        self.id()
        self.transaction_details_ultimate_venue()
        self.transaction_details_venue()
        self.market_identifiers_instrument()  # needs transaction_details_ultimate_venue()
        self.market_identifiers_parties()
        self.market_identifiers()
        self.meta_model()
        self.order_identifiers_initial_order_designation()
        self.order_identifiers_internal_order_id_code()  # needs order_identifiers_initial_order_designation
        self.order_identifiers_order_id_code()
        self.order_identifiers_transaction_ref_no()
        self.order_identifiers_trading_venue_transaction_id_code()  # needs order_identifiers_transaction_ref_no
        self.price_forming_data_initial_quantity()
        self.price_forming_data_price()
        self.price_forming_data_traded_quantity()
        self.report_details_transaction_ref_no()
        self.source_key()
        self.source_index()
        self.timestamps_order_received()
        self.timestamps_order_submitted()
        self.timestamps_internal_order_received()  # needs timestamps_order_received()
        self.timestamps_internal_order_submitted()  # needs timestamps_order_submitted()
        self.timestamps_order_status_updated()
        self.timestamps_trading_date_time()
        self.transaction_details_basket_id()
        self.transaction_details_buy_sell_indicator()
        self.transaction_details_complex_trade_component_id()
        self.transaction_details_price()
        self.transaction_details_price_currency()
        self.transaction_details_price_notation()
        self.transaction_details_quantity()
        self.transaction_details_quantity_currency()
        self.transaction_details_quantity_notation()
        self.transaction_details_record_type()
        self.transaction_details_trading_capacity()
        self.transaction_details_trading_date_time()
        self.post_process()
        return self.target_df

    def _post_process(self):
        """All the temp columns which are needed for downstream tasks are populated
        in self.target_df inside the _post_process() method.
        """

        # Initialise cols to NA so that they are always populated.
        for col in {
            DevColumns.RIC,
            DevColumns.INSTRUMENT_UNIQUE_IDENTIFIER,
        }:
            self.target_df.loc[:, col] = pd.NA

        self.target_df.loc[:, DevColumns.ASSET_CLASS] = self.pre_process_df.loc[
            :, DevColumns.ASSET_CLASS
        ]

        self.target_df = pd.concat(
            [self.target_df, self._get_pricing_references()], axis=1
        )

        if self.file_type_asset_class in (
            FileTypeAssetClass.EQ,
            FileTypeAssetClass.DERIV,
        ):
            self.target_df.loc[:, DevColumns.RIC] = self.source_frame.loc[
                :, add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC)
            ]

            ric_prefix = "aladdin-ric-"
            self.target_df.loc[
                :, DevColumns.INSTRUMENT_UNIQUE_IDENTIFIER
            ] = ric_prefix + self.source_frame.loc[
                :, add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC)
            ].astype(
                "string"
            )

        if self.file_type_asset_class == FileTypeAssetClass.DERIV:
            # this intentionally overrides ric identifier when there is a redcode pricing reference

            cds_sec_type_mask = (
                self.source_frame.loc[
                    :,
                    add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE
                    ),
                ]
                .astype("str")
                .str.fullmatch(SecGroupValues.CDSWAP, case=False, na=False)
            )

            not_null_redcode = self.target_df.loc[
                :, DevColumns.PRICING_REFERENCE_REDCODE
            ].notnull()

            redcode_prefix = "aladdin-redcode-"
            self.target_df.loc[
                cds_sec_type_mask & not_null_redcode,
                DevColumns.INSTRUMENT_UNIQUE_IDENTIFIER,
            ] = redcode_prefix + self.target_df.loc[
                cds_sec_type_mask & not_null_redcode,
                DevColumns.PRICING_REFERENCE_REDCODE,
            ].astype(
                "string"
            )

        if self.file_type_asset_class == FileTypeAssetClass.FI:

            loan_sec_group_mask = (
                self.source_frame.loc[
                    :,
                    add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP
                    ),
                ]
                .astype("str")
                .str.fullmatch(SecGroupValues.LOAN, case=False, na=False)
            )

            lxid_prefix = "aladdin-lxid-"
            self.target_df.loc[
                loan_sec_group_mask, DevColumns.INSTRUMENT_UNIQUE_IDENTIFIER
            ] = lxid_prefix + self.target_df.loc[
                loan_sec_group_mask, DevColumns.PRICING_REFERENCE_LXID
            ].astype(
                "string"
            )

        # Fallback for Instrument Unique Identifier: populate first value from marketIdentifiers.
        iue_from_market_ids_mask = (
            self.target_df[OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT].notnull()
            & self.target_df[DevColumns.INSTRUMENT_UNIQUE_IDENTIFIER].isnull()
        )
        self.target_df.loc[
            iue_from_market_ids_mask, DevColumns.INSTRUMENT_UNIQUE_IDENTIFIER
        ] = (
            self.target_df.loc[
                iue_from_market_ids_mask, OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT
            ]
            .str.get(0)
            .str.get("labelId")
        )

        self.target_df.loc[:, DevColumns.NEWO_IN_FILE] = self._get_newo_in_file()

        self.target_df.loc[:, DevColumns.INSTRUMENT_FULL_NAME] = self.source_frame.loc[
            :, add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_DESC_1)
        ]
        if self.file_type_asset_class == FileTypeAssetClass.FX:
            self.target_df.loc[:, DevColumns.INSTRUMENT_FULL_NAME] = (
                self.target_df.loc[:, DevColumns.INSTRUMENT_FULL_NAME]
                + " "
                + self.source_frame.loc[
                    :,
                    add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE
                    ),
                ]
            )

        self.target_df.loc[
            :, DevColumns.INSTRUMENT_CLASSIFICATION
        ] = self.pre_process_df.loc[:, DevColumns.INSTRUMENT_CLASSIFICATION]

        self.target_df.loc[:, DevColumns.CFI_CATEGORY] = self._get_cfi_category()

        self.target_df.loc[:, DevColumns.CFI_GROUP] = self._get_cfi_group()

        self.target_df.loc[
            :, DevColumns.FILE_TYPE_ASSET_CLASS
        ] = self.pre_process_df.loc[:, DevColumns.FILE_TYPE_ASSET_CLASS]
        self.target_df.loc[:, DevColumns.INSTRUMENT_CREATED_THROUGH_FB] = True
        self.target_df.loc[
            :, DevColumns.BEST_EX_ASSET_CLASS_MAIN
        ] = MapConditional.process(
            source_frame=self.target_df,
            params=ParamsMapConditional(
                target_attribute=DevColumns.BEST_EX_ASSET_CLASS_MAIN,
                cases=[
                    Case(
                        query=f"`{DevColumns.FILE_TYPE_ASSET_CLASS}`.str.fullmatch('DERIV', case=False, na=False)",
                        value="Derivatives",
                    ),
                    Case(
                        query=f"`{DevColumns.FILE_TYPE_ASSET_CLASS}`.str.fullmatch('FX', case=False, na=False)",
                        value="Currency Derivatives",
                    ),
                    Case(
                        query=f"`{DevColumns.FILE_TYPE_ASSET_CLASS}`.str.fullmatch('FI', case=False, na=False)",
                        value="Debt Instruments",
                    ),
                    Case(
                        query=f"`{DevColumns.FILE_TYPE_ASSET_CLASS}`.str.fullmatch('EQ', case=False, na=False)",
                        value="Equity",
                    ),
                ],
            ),
        )

    def _buy_sell(self) -> pd.DataFrame:
        """Returns a data frame containing _order.buySell and _orderState.buySell"""
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, DevColumns.BUY_SELL].values,
                    index=self.pre_process_df.index,
                    columns=[add_prefix(ModelPrefix.ORDER, OrderColumns.BUY_SELL)],
                ),
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, DevColumns.BUY_SELL].values,
                    index=self.pre_process_df.index,
                    columns=[
                        add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.BUY_SELL)
                    ],
                ),
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        """Returns a data frame containing dataSourceName.
        This is populated with the static value 'Aladdin'"""
        return pd.DataFrame(
            data="Aladdin",
            index=self.target_df.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """Returns a data frame containing date.
        This is populated from 'createdTimestampUtc' col in the Order File"""
        return ConvertDatetime.process(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=add_prefix(
                    FileTypes.ORDER, OrderSourceColumns.CREATED_TIMESTAMP_UTC
                ),
                target_attribute=OrderColumns.DATE,
                convert_to=ConvertTo.DATE,
            ),
        )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.buySellIndicator."""
        return MapValue.process(
            source_frame=self.pre_process_df,
            params=ParamsMapValue(
                source_attribute=DevColumns.BUY_SELL,
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "1": BuySellIndicator.BUYI.value,
                    "2": BuySellIndicator.SELL.value,
                },
            ),
            auditor=self.auditor,
        )

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.limitPrice.
        This is populated only for Market Orders, from 'limitValue' col in the placement file"""
        cases = [
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.MARKET}'",
                "attribute": add_prefix(
                    FileTypes.PLACEMENT, PlacementSourceColumns.LIMIT_VALUE
                ),
            }
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE, cases=cases
            ),
        )

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_order_status(self) -> pd.DataFrame:
        """Returns a data frame containing _order.executionDetails.orderStatus and
        _orderState.executionDetails.orderStatus."""
        order_execution_details_order_status = pd.DataFrame(
            data=OrderStatus.NEWO.value,
            index=self.target_df.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                )
            ],
        )

        cases = [
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.MARKET}'",
                "value": OrderStatus.PARF.value,
            },
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.CLIENT}'",
                "value": OrderStatus.FILL.value,
            },
        ]
        order_state_execution_details_order_status = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                ),
                cases=cases,
            ),
        )
        return pd.concat(
            [
                order_execution_details_order_status,
                order_state_execution_details_order_status,
            ],
            axis=1,
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.orderType"""
        value_map = {
            "M": "Market",
            "L": "Limit",
            "S": "Stop",
            "X": "Stop Limit",
            "5": "Market on Close",
            "O": "Market on Open",
            "B": "Limit on Close",
            "V": "Market on Close",
            "C": "Market on Cash Close",
            "I": "Funari",
            "W": "WMR Close",
            "P": "EFP",
        }
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=add_prefix(
                    FileTypes.ORDER, OrderSourceColumns.ORDER_TYPE
                ),
                target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
                case_insensitive=True,
                value_map=value_map,
            ),
            auditor=self.auditor,
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.outgoingOrderAddlInfo."""

        asset_id = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=add_prefix(
                    FileTypes.TRANSACTION, TransactionSourceColumns.ASSET_ID
                ),
                target_attribute=DevColumns.ASSET_ID,
                prefix=f"{TransactionSourceColumns.ASSET_ID}: ",
            ),
            auditor=self.auditor,
        )

        return ConcatAttributes.process(
            source_frame=pd.concat(
                [
                    self.source_frame.loc[
                        :, add_prefix(FileTypes.ORDER, OrderSourceColumns.BASKET_ID)
                    ],
                    asset_id,
                ],
                axis=1,
            ),
            params=ParamsConcatAttributes(
                source_attributes=[
                    add_prefix(FileTypes.ORDER, OrderSourceColumns.BASKET_ID),
                    DevColumns.ASSET_ID,
                ],
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                delimiter=Delimiters.NEW_LINE,
            ),
        )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_stop_price(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.stopPrice.
        This is populated only for Market Orders, from 'stopValue' col in the placement file"""
        cases = [
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.MARKET}'",
                "attribute": add_prefix(
                    FileTypes.PLACEMENT, PlacementSourceColumns.STOP_VALUE
                ),
            }
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.EXECUTION_DETAILS_STOP_PRICE, cases=cases
            ),
        )

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, DevColumns.TRADING_CAPACITY].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY],
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.validityPeriod."""
        value_map = {
            "2": "GTCV",
            "1": "DAVY",
        }
        timestamps_validity_period = MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=add_prefix(
                    FileTypes.ORDER, OrderSourceColumns.TIME_IN_FORCE
                ),
                target_attribute=DevColumns.TIMESTAMPS_VALIDITY_PERIOD,
                case_insensitive=True,
                value_map=value_map,
            ),
            auditor=self.auditor,
        )
        return MapAttribute.process(
            source_frame=timestamps_validity_period,
            params=ParamsMapAttribute(
                source_attribute=DevColumns.TIMESTAMPS_VALIDITY_PERIOD,
                target_attribute=OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD,
                cast_to="string.list",
                list_delimiter=",",
            ),
            auditor=self.auditor,
        )

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _financing_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _hierarchy(self) -> pd.DataFrame:
        """Not Implemented"""

    def _id(self) -> pd.DataFrame:
        """Returns a data frame containing _order.id and _orderState.id"""
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, DevColumns.ID].values,
                    index=self.pre_process_df.index,
                    columns=[add_prefix(ModelPrefix.ORDER, OrderColumns.ID)],
                ),
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, DevColumns.ID].values,
                    index=self.pre_process_df.index,
                    columns=[add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID)],
                ),
            ],
            axis=1,
        )

    def _is_discretionary(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_iceberg(self):
        """Not Implemented"""

    def _is_repo(self):
        """Not Implemented"""

    def _is_synthetic(self):
        """Not Implemented"""

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        """Not Implemented"""

    def _market_identifiers(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_instrument() and _market_identifiers_parties() have been
        called earlier"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.instrument by calling InstrumentIdentifiers."""
        source_frame_cols = [
            add_prefix(FileTypes.ORDER, OrderSourceColumns.PRICE_CCY),
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.MATURITY),
        ]
        source_frame = pd.concat(
            [
                self.pre_process_df,
                self.source_frame.loc[:, source_frame_cols],
                self.target_df.loc[:, OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE],
            ],
            axis=1,
        )

        source_frame.loc[:, DevColumns.ISIN] = self.source_frame.loc[
            :, add_prefix(FileTypes.ORDER, OrderSourceColumns.ISIN)
        ].fillna(
            self.source_frame.loc[
                :, add_prefix(FileTypes.TRANSACTION, OrderSourceColumns.ISIN)
            ]
        )

        loans = self.source_frame.loc[
            :, add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP)
        ].str.fullmatch(SecGroupValues.LOAN, case=False, na=False)

        source_frame.loc[loans, DevColumns.ISIN] = pd.NA

        return InstrumentIdentifiers.process(
            source_frame=source_frame,
            params=ParamsInstrumentIdentifiers(
                currency_attribute=DevColumns.CURRENCY,
                notional_currency_2_attribute=DevColumns.NOTIONAL_CURRENCY_2,
                expiry_date_attribute=DevColumns.EXPIRY_DATE,
                isin_attribute=DevColumns.ISIN,
                option_strike_price_attribute=DevColumns.STRIKE_PRICE,
                underlying_symbol_attribute=DevColumns.SYMBOL,
                underlying_index_term_attribute=DevColumns.UNDERLYING_INDEX_TERM,
                asset_class_attribute=DevColumns.ASSET_CLASS,
                option_type_attribute=DevColumns.OPTION_TYPE,
                venue_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                instrument_classification_attribute=DevColumns.INSTRUMENT_CLASSIFICATION,
                retain_task_inputs=True,
            ),
            auditor=self.auditor,
        )

    def _get_trader(self):
        return pd.Series(
            data=(
                PartyPrefix.ID
                + self.source_frame.loc[
                    :, add_prefix(FileTypes.ORDER, OrderSourceColumns.TRADER)
                ]
            ).values,
            name=DevColumns.TRADER,
            index=self.source_frame.index,
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.parties by calling PartyIdentifiers"""
        executing_entity = self._get_executing_entity()
        client = self._get_client()
        trader = self._get_trader()

        counterparty = self._get_counterparty()

        investment_decision_within_firm = pd.Series(
            data=(
                PartyPrefix.ID
                + self.source_frame.loc[
                    :, add_prefix(FileTypes.ORDER, OrderSourceColumns.PM_INITIALS)
                ].astype("string")
            ).values,
            name=DevColumns.INVESTMENT_DECISION_WITHIN_FIRM,
            index=self.source_frame.index,
        )

        parties_source_frame = pd.concat(
            [
                executing_entity,
                trader,
                client,
                counterparty,
                investment_decision_within_firm,
                self.target_df.loc[
                    :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
                ],
            ],
            axis=1,
        )

        return GenericOrderPartyIdentifiers.process(
            source_frame=parties_source_frame,
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                counterparty_identifier=DevColumns.COUNTERPARTY,
                client_identifier=DevColumns.CLIENT,
                executing_entity_identifier=DevColumns.EXECUTING_ENTITY_WITH_LEI,
                buyer_identifier=DevColumns.EXECUTING_ENTITY_WITH_LEI,
                seller_identifier=DevColumns.COUNTERPARTY,
                trader_identifier=DevColumns.TRADER,
                investment_decision_within_firm_identifier=DevColumns.INVESTMENT_DECISION_WITHIN_FIRM,
                execution_within_firm_identifier=DevColumns.TRADER,
                buy_sell_side_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                use_buy_mask_for_buyer_seller=True,
                create_fallback_fields=True,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """Returns a data frame containing _order.__meta_model__ and _orderState.__meta_model__.
        The columns are populated with the static values Order and OrderState respectively"""
        return pd.DataFrame(
            data=[["Order", "OrderState"]],
            index=self.source_frame.index,
            columns=[
                add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.META_MODEL),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.META_MODEL,
                ),
            ],
        )

    def _order_class(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.initialOrderDesignation"""
        cases = [
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.MARKET}'",
                "attribute": f"{add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_ID)}",
            },
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.CLIENT}'",
                "attribute": f"{add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID)}",
            },
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_INITIAL_ORDER_DESIGNATION,
                cases=cases,
            ),
        )

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.ORDER_IDENTIFIERS_INITIAL_ORDER_DESIGNATION
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE],
        )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.orderIdCode"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, DevColumns.ID].values,
            index=self.target_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
        )

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_TRADING_VENUE_TRANSACTION_ID_CODE],
        )

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.transactionRefNo"""
        cases = [
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.MARKET}'",
                "attribute": f"{add_prefix(FileTypes.FILL, FillSourceColumns.FILL_ID)}",
            },
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.CLIENT}'",
                "attribute": f"{add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.TRADE_NUM)}",
            },
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                cases=cases,
            ),
        )

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing priceFormingData.initialQuantity.
        This is populated from orderQuantity col in the order file"""
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_QUANTITY)
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY],
        ).abs()

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price(self) -> pd.DataFrame:
        """Returns a data frame containing priceFormingData.price.
        mapped to avgPrice col from the Order File"""
        cases = [
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.MARKET}'",
                "attribute": f"{add_prefix(FileTypes.FILL, FillSourceColumns.EXECUTED_PRICE)}",
            },
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.CLIENT}' |`{add_prefix(FileTypes.FILL, FillSourceColumns.EXECUTED_PRICE)}`.isnull()",
                "attribute": f"{add_prefix(FileTypes.ORDER, OrderSourceColumns.AVG_PRICE)}",
            },
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                cases=cases,
            ),
        )

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing priceFormingData.tradedQuantity"""
        cases = [
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.MARKET}' & `{add_prefix(FileTypes.FILL, FillSourceColumns.EXECUTED_QUANTITY)}`.notnull()",
                "attribute": f"{add_prefix(FileTypes.FILL, FillSourceColumns.EXECUTED_QUANTITY)}",
            },
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.CLIENT}'",
                "attribute": f"{add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.TRADE_QUANTITY)}",
            },
            # OTC trades don't have transaction placement id and
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.MARKET}' & `{add_prefix(FileTypes.FILL, FillSourceColumns.EXECUTED_QUANTITY)}`.isnull()",
                "attribute": f"{add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_QUANTITY)}",
            },
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=add_prefix(
                    ModelPrefix.ORDER_STATE,
                    OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                ),
                cases=cases,
            ),
        ).abs()

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a data frame containing reportDetails.transactionRefNo"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO],
        )

    def _source_index(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceIndex column"""
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                from_index=True, target_attribute=OrderColumns.SOURCE_INDEX
            ),
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceKey column from the SWARM_FILE_URL"""
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_value=os.getenv("SWARM_FILE_URL"),
                target_attribute=OrderColumns.SOURCE_KEY,
            ),
        )

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.internalOrderReceived"""
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.TIMESTAMPS_ORDER_RECEIVED].values,
            index=self.target_df.index,
            columns=[OrderColumns.TIMESTAMPS_INTERNAL_ORDER_RECEIVED],
        )

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.internalOrderSubmitted."""
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.TIMESTAMPS_ORDER_SUBMITTED].values,
            index=self.target_df.index,
            columns=[OrderColumns.TIMESTAMPS_INTERNAL_ORDER_SUBMITTED],
        )

    def _timestamps_order_received(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderReceived"""
        return ConvertDatetime.process(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=add_prefix(
                    FileTypes.ORDER, OrderSourceColumns.CREATED_TIMESTAMP_UTC
                ),
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                convert_to=ConvertTo.DATETIME,
            ),
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderStatusUpdated"""
        cases = [
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.MARKET}'",
                "attribute": f"{add_prefix(FileTypes.FILL, FillSourceColumns.EXECUTED_TIMESTAMP_UTC)}",
            },
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.CLIENT}'",
                "attribute": f"{add_prefix(FileTypes.ORDER, OrderSourceColumns.MODIFIED_TIMESTAMP_UTC)}",
            },
        ]
        order_status_updated = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=DevColumns.ORDER_STATUS_UPDATED,
                cases=cases,
            ),
        )
        return ConvertDatetime.process(
            order_status_updated,
            params=ParamsConvertDatetime(
                source_attribute=DevColumns.ORDER_STATUS_UPDATED,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
                convert_to=ConvertTo.DATETIME,
            ),
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderSubmitted."""
        return ConvertDatetime.process(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=add_prefix(
                    FileTypes.ORDER, OrderSourceColumns.ACTIVATED_TIMESTAMP_UTC
                ),
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                convert_to=ConvertTo.DATETIME,
            ),
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.tradingDateTime"""
        return MapConditional.process(
            source_frame=self.target_df,
            params=ParamsMapConditional(
                target_attribute=add_prefix(
                    ModelPrefix.ORDER_STATE, OrderColumns.TIMESTAMPS_TRADING_DATE_TIME
                ),
                cases=[
                    Case(
                        query=f"`{add_prefix(ModelPrefix.ORDER_STATE,OrderColumns.EXECUTION_DETAILS_ORDER_STATUS)}`.str.fullmatch('{OrderStatus.FILL.value}|{OrderStatus.PARF.value}', case=False, na=False)",
                        value=self.target_df.loc[
                            :, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED
                        ].values,
                    )
                ],
            ),
        )

    def _timestamps_validity_period(self) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.basketId"""
        return pd.DataFrame(
            data=self.source_frame.loc[
                :,
                add_prefix(
                    FileTypes.TRANSACTION, TransactionSourceColumns.PORTFOLIO_TICKER
                ),
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_BASKET_ID],
        )

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.buySellIndicator"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.complexTradeComponentId"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.ORDER_IDENTIFIERS_INITIAL_ORDER_DESIGNATION
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID],
        )

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price(self) -> pd.DataFrame:
        """Not Implemented"""
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.PRICE_FORMING_DATA_PRICE].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE],
        )

    def _transaction_details_price_average(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.priceCurrency"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, DevColumns.CURRENCY].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY],
        )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """
        Returns a data frame containing transactionDetails.priceNotation.
        This is populated depending on file type asset class:
            DERIV: MONE
            FX: MONE
            FI: PERC
            EQ: MONE
        """
        price_notation_map = {
            "DERIV": PriceNotation.MONE.value,
            "FX": PriceNotation.MONE.value,
            "FI": PriceNotation.PERC.value,
            "EQ": PriceNotation.MONE.value,
        }
        price_notation = price_notation_map.get(self.file_type_asset_class, pd.NA)
        return pd.DataFrame(
            data=price_notation,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION],
        )

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.quantity"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :,
                add_prefix(
                    ModelPrefix.ORDER_STATE,
                    OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                ),
            ].values,
            index=self.target_df.index,
            columns=[
                add_prefix(
                    ModelPrefix.ORDER_STATE, OrderColumns.TRANSACTION_DETAILS_QUANTITY
                )
            ],
        ).abs()

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """Returns a dataframe containing transactionDetails.quantityCurrency"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, DevColumns.NOTIONAL_CURRENCY_2].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY],
        )

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.quantityNotation"""
        sec_type = add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE)

        return MapConditional.process(
            source_frame=pd.concat(
                [
                    self.source_frame.loc[:, sec_type],
                    self.pre_process_df.loc[:, DevColumns.FILE_TYPE_ASSET_CLASS],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
                cases=[
                    Case(
                        query=f"(`{DevColumns.FILE_TYPE_ASSET_CLASS}`.str.fullmatch('DERIV', case=False, na=False)) &"
                        f" ~(`{sec_type}`.str.fullmatch('CSWAP', case=False, na=False))",
                        value=QuantityNotation.UNIT.value,
                    ),
                    Case(
                        query=f"(`{DevColumns.FILE_TYPE_ASSET_CLASS}`.str.fullmatch('DERIV', case=False, na=False)) &"
                        f" (`{sec_type}`.str.fullmatch('CSWAP', case=False, na=False))",
                        value=QuantityNotation.MONE.value,
                    ),
                    Case(
                        query=f"`{DevColumns.FILE_TYPE_ASSET_CLASS}`.str.fullmatch('FX', case=False, na=False)",
                        value=QuantityNotation.MONE.value,
                    ),
                    Case(
                        query=f"`{DevColumns.FILE_TYPE_ASSET_CLASS}`.str.fullmatch('FI', case=False, na=False)",
                        value=QuantityNotation.NOML.value,
                    ),
                    Case(
                        query=f"`{DevColumns.FILE_TYPE_ASSET_CLASS}`.str.fullmatch('EQ', case=False, na=False)",
                        value=QuantityNotation.UNIT.value,
                    ),
                ],
            ),
        )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.recordType"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=DevColumns.ORDER_TYPE,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE,
                case_insensitive=True,
                value_map={
                    CustomOrdType.CLIENT: OrderRecordType.ALLOCATION.value,
                    CustomOrdType.MARKET: OrderRecordType.MARKET_SIDE.value,
                },
            ),
            auditor=self.auditor,
        )

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, DevColumns.TRADING_CAPACITY].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.tradingDateTime"""
        return MapConditional.process(
            source_frame=self.target_df,
            params=ParamsMapConditional(
                target_attribute=add_prefix(
                    ModelPrefix.ORDER_STATE,
                    OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
                ),
                cases=[
                    Case(
                        query=f"`{add_prefix(ModelPrefix.ORDER_STATE,OrderColumns.EXECUTION_DETAILS_ORDER_STATUS)}`.str.fullmatch('{OrderStatus.FILL.value}|{OrderStatus.PARF.value}', case=False, na=False)",
                        value=self.target_df.loc[
                            :, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED
                        ].values,
                    )
                ],
            ),
        )

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.ultimateVenue"""
        fill_order_mask = self.source_frame[
            add_prefix(FileTypes.FILL, FillSourceColumns.EXCHANGE)
        ].notnull()
        id_venue_df = self.source_frame[fill_order_mask][
            [
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.ORDER_ID),
                add_prefix(FileTypes.FILL, FillSourceColumns.EXCHANGE),
            ]
        ]
        id_venue_dict = id_venue_df.set_index(
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.ORDER_ID)
        ).to_dict()[add_prefix(FileTypes.FILL, FillSourceColumns.EXCHANGE)]
        return pd.DataFrame(
            data=(
                self.source_frame.loc[
                    :,
                    add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.ORDER_ID
                    ),
                ].map(id_venue_dict)
            ).values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE],
        ).fillna("XOFF")

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_venue(self) -> pd.DataFrame:
        cases = [
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.MARKET}'",
                "attribute": OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
            },
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.CLIENT}'",
                "value": "XOFF",
            },
        ]
        return MapConditional.process(
            source_frame=pd.concat(
                [
                    self.source_frame.loc[:, DevColumns.ORDER_TYPE],
                    self.target_df.loc[
                        :, OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE
                    ],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_VENUE,
                cases=cases,
            ),
        )

    def _get_asset_class(self) -> pd.Series:
        """Returns a series containing info about asset class
        For FX files, the asset class is SEC_GROUP + SEC_TYPE. For all other asset classes
        it is simply SEC_GROUP from the transaction file"""
        value_map = {
            # SecGroupValues.ABS: AssetClass.BOND,  # all commented are waiting for specs
            SecGroupValues.BND: AssetClass.BOND,
            SecGroupValues.CASH: AssetClass.BOND,
            # SecGroupValues.CMBS: AssetClass.BOND,
            # SecGroupValues.CMO: AssetClass.BOND,
            # SecGroupValues.MBS: AssetClass.BOND,
            SecGroupValues.CDSWAP: AssetClass.CDS_INDEX,
            SecGroupValues.EQUITY: AssetClass.EQUITY,
            # SecGroupValues.SWAP: AssetClass.EQUITY_SWAP,
            SecGroupValues.FUTURE: AssetClass.FUTURE,
            f"{SecGroupValues.FX} {SecTypeValues.FWRD}": AssetClass.FX_FORWARD,
            # f"{SecGroupValues.FX} {SecTypeValues.OPT}": AssetClass.FX_OPTION,
            f"{SecGroupValues.FX} {SecTypeValues.SPOT}": AssetClass.FX_SPOT,
            # f"{SecGroupValues.FX} {SecTypeValues.CSWAP}": AssetClass.FX_SWAP,
            SecGroupValues.OPTION: AssetClass.OPTION,
        }
        asset_class_df = pd.DataFrame(
            data=self.source_frame.loc[
                :, add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP)
            ].values,
            index=self.source_frame.index,
            columns=[DevColumns.TEMP_ASSET_CLASS],
        )
        if self.file_type_asset_class == FileTypeAssetClass.FX:
            asset_class_df[DevColumns.TEMP_ASSET_CLASS] = (
                asset_class_df[DevColumns.TEMP_ASSET_CLASS]
                + " "
                + self.source_frame.loc[
                    :,
                    add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE
                    ),
                ]
            )

        return MapValue.process(
            source_frame=asset_class_df,
            params=ParamsMapValue(
                source_attribute=DevColumns.TEMP_ASSET_CLASS,
                target_attribute=DevColumns.ASSET_CLASS,
                case_insensitive=True,
                value_map=value_map,
            ),
            auditor=self.auditor,
        )[DevColumns.ASSET_CLASS]

    def _get_buy_sell(self) -> pd.Series:
        """Returns a Series containing buy/sell information"""
        value_map = {
            "BUY": "1",
            "SELL": "2",
            "BUYPROT INIT": "2",
            "RCVFIX INIT": "1",
            "PAYBASE UNWIND": "2",
            "PAYFIX INIT": "2",
            "PAYFIX UNWIND": "2",
            "RCVFIX UNWIND": "1",
            "SELLPROT UNWIND": "1",
            "SELLPROT INIT": "1",
            "BUYPROT UNWIND": "2",
            "RCVBASE INIT": "1",
            "BUYCLOSE": "1",
            "SELLOPEN": "2",
        }
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=add_prefix(
                    FileTypes.ORDER, OrderSourceColumns.TRAN_TYPE
                ),
                target_attribute=DevColumns.BUY_SELL,
                case_insensitive=True,
                value_map=value_map,
            ),
            auditor=self.auditor,
        )[DevColumns.BUY_SELL]

    def _get_currency(self) -> None:
        """Populates pre_process_df with two cols - __currency__ and __notional_currency_2__
        For FX files, it's obtained upon spliting the SEC_DESC_1 col in transaction file by /
        for all other files, currency is mapped to priceCcy from Order file and notional_currency_2
        is null.
        NOTE: Aladdin send us CNH instead of CNY, this is converted to CNY in ConvertMinorToMajor.
        """
        if self.file_type_asset_class == FileTypeAssetClass.FX:
            self.pre_process_df[
                [DevColumns.CURRENCY, DevColumns.NOTIONAL_CURRENCY_2]
            ] = (
                self.source_frame.loc[
                    :,
                    add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.SEC_DESC_1
                    ),
                ]
                .astype("string")
                .str.split("/", expand=True)
            )
            self.pre_process_df[DevColumns.CURRENCY] = run_convert_minor_to_major(
                source_frame=self.pre_process_df,
                params=ParamsConvertMinorToMajor(
                    source_ccy_attribute=DevColumns.CURRENCY,
                    target_ccy_attribute=DevColumns.CURRENCY,
                ),
            ).loc[:, DevColumns.CURRENCY]
            self.pre_process_df[
                DevColumns.NOTIONAL_CURRENCY_2
            ] = run_convert_minor_to_major(
                source_frame=self.pre_process_df,
                params=ParamsConvertMinorToMajor(
                    source_ccy_attribute=DevColumns.NOTIONAL_CURRENCY_2,
                    target_ccy_attribute=DevColumns.NOTIONAL_CURRENCY_2,
                ),
            ).loc[
                :, DevColumns.NOTIONAL_CURRENCY_2
            ]
        else:
            self.pre_process_df.loc[
                :, DevColumns.CURRENCY
            ] = run_convert_minor_to_major(
                source_frame=self.source_frame,
                params=ParamsConvertMinorToMajor(
                    source_ccy_attribute=add_prefix(
                        FileTypes.ORDER, OrderSourceColumns.PRICE_CCY
                    ),
                    target_ccy_attribute=DevColumns.CURRENCY,
                ),
            ).loc[
                :, DevColumns.CURRENCY
            ]
            self.pre_process_df.loc[:, DevColumns.NOTIONAL_CURRENCY_2] = pd.NA

    def _get_client(self) -> pd.DataFrame:
        """Returns a dataframe with col '__client__"""
        portfolio_id_with_prefix = PartyPrefix.ID + self.source_frame.loc[
            :,
            add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.PORTFOLIO_ID),
        ].astype("string")

        return pd.DataFrame(
            data=portfolio_id_with_prefix.values,
            index=self.source_frame.index,
            columns=[DevColumns.CLIENT],
        )

    def _get_counterparty(self) -> pd.Series:
        return pd.Series(
            data=(
                PartyPrefix.ID
                + self.source_frame.loc[
                    :,
                    add_prefix(
                        FileTypes.TRANSACTION,
                        TransactionSourceColumns.EXEC_CPCY_ID,
                    ),
                ].astype("string")
            ).values,
            name=DevColumns.COUNTERPARTY,
            index=self.source_frame.index,
        )

    def _get_executing_entity(self):
        """Gets the executing entity value to be used in PartyIdentifiers from the
        AccountFirm record"""
        return GetTenantLEI.process(
            source_frame=self.source_frame,
            params=ParamsGetTenantLEI(
                target_column_prefix=PartyPrefix.LEI,
                target_lei_column=DevColumns.EXECUTING_ENTITY_WITH_LEI,
            ),
        )

    def _get_id(self) -> pd.Series:
        """Returns a Series containing 'id' information, as string dtype"""
        cases = [
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.MARKET}'",
                "attribute": f"{add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.PLACEMENT_ID)}",
            },
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.CLIENT}'",
                "attribute": f"{add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_DETAIL_ID)}",
            },
        ]
        ids = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=DevColumns.ID,
                cases=cases,
            ),
        ).astype("string")
        market_mask = (
            self.source_frame.loc[:, DevColumns.ORDER_TYPE] == CustomOrdType.MARKET
        )

        # OTC orders may be missing transaction.placement_id . In such cases, we'll use the order.orderId as the
        # id and suffix it with OTC
        missing_market_side_placement_id = (
            market_mask
            & self.source_frame.loc[
                :,
                add_prefix(
                    FileTypes.TRANSACTION, TransactionSourceColumns.PLACEMENT_ID
                ),
            ].isnull()
        )
        if any(missing_market_side_placement_id):
            ids.loc[missing_market_side_placement_id, DevColumns.ID] = (
                self.source_frame.loc[
                    missing_market_side_placement_id,
                    add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_ID),
                ].astype("string")
                + "|OTC"
            )

        ids.loc[market_mask, DevColumns.ID] = "M|" + ids.loc[market_mask, DevColumns.ID]
        ids.loc[~market_mask, DevColumns.ID] = (
            "C|" + ids.loc[~market_mask, DevColumns.ID]
        )

        # This is a temporary fix before we hear back from clients. There are some
        # uncertain buy/sell values which are being mapped to 1 (BUY)  and a prefix
        # is being added to order._id to to highlight these trades.
        uncertain_buy_sell_mask = self.pre_process_df.loc[
            :, DevColumns.BUY_SELL
        ].isnull()
        self.pre_process_df.loc[uncertain_buy_sell_mask, DevColumns.BUY_SELL] = "1"
        ids.loc[uncertain_buy_sell_mask, DevColumns.ID] = (
            "TEMP|" + ids.loc[uncertain_buy_sell_mask, DevColumns.ID]
        )
        return ids[DevColumns.ID]

    def _get_newo_in_file(self) -> pd.Series:
        """
        Since the for market side, we only keep PARF rows and create a NEWO for each
        __newo__ in file is set to True and for client allocations, FILL is essentially
        being created from NEWO (there's just one row representing both NEWO & FILL)
        it can be set to True for client allocations.
        :return:
        """
        cases = [
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.MARKET}'",
                "value": False,
            },
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.CLIENT}'",
                "value": True,
            },
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=DevColumns.NEWO_IN_FILE,
                cases=cases,
            ),
        )[DevColumns.NEWO_IN_FILE]

    def _get_instrument_classification(self) -> pd.Series:
        """
        Map secType or secGroup against the following:
            transaction.[secGroup] = "LOAN": "LLXXXX"
            transaction.[secGroup] = "EQUITY": "ESXXXX"
            transaction.[secGroup] = "FUTURE": "FFXXXX"
            transaction.[secType] = "SPOT": "IFXXXX"
            transaction.[secGroup] = "SWAP": "SEXXXX"
            transaction.[secType] = "FWRD": "JXXXXX"
            transaction.[secType] = "OPT": "HFXXXX"
            transaction.[SecGroup] = "CDS": "SCXXXX"
            transaction.[secType] = "CSWAP": "SFXXXX"
            transaction.[secGroup] = "OPTION":"OXXXXX"
            transaction.[secGroup] = ["CMO", "CMBS"] : "DXXXXX"
            transaction.[secGroup] = "ABS" : "DAXXXX"
            transaction.[secGroup] = "BND" : "DBXXXX"
            transaction.[secGroup] = "MBS" : "DGXXXX"
            transaction.[secGroup] = "CASH" : "DYXXXX"
        """
        # commented bits waiting for specs

        # sec_type = add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE)
        sec_group = add_prefix(
            FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP
        )

        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=DevColumns.INSTRUMENT_CLASSIFICATION,
                cases=[
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.LOAN}', case=False, na=False)",
                        value="LLXXXX",
                    ),
                    # Case(
                    #     query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.EQUITY}', case=False, na=False)",
                    #     value="ESXXXX",
                    # ),
                    # Case(
                    #     query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.FUTURE}', case=False, na=False)",
                    #     value="FFXXXX",
                    # ),
                    # Case(
                    #     query=f"`{sec_type}`.str.fullmatch('{SecTypeValues.SPOT}', case=False, na=False)",
                    #     value="IFXXXX",
                    # ),
                    # Case(
                    #     query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.SWAP}', case=False, na=False)",
                    #     value="SEXXXX",
                    # ),
                    # Case(
                    #     query=f"`{sec_type}`.str.fullmatch('{SecTypeValues.FWRD}', case=False, na=False)",
                    #     value="JXXXXX",
                    # ),
                    # Case(
                    #     query=f"`{sec_type}`.str.fullmatch('{SecTypeValues.OPT}', case=False, na=False)",
                    #     value="HFXXXX",
                    # ),
                    # Case(
                    #     query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.CDS}', case=False, na=False)",
                    #     value="SCXXXX",
                    # ),
                    # Case(
                    #     query=f"`{sec_type}`.str.fullmatch('{SecTypeValues.CSWAP}', case=False, na=False)",
                    #     value="SFXXXX",
                    # ),
                    # Case(
                    #     query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.OPTION}', case=False, na=False)",
                    #     value="OXXXXX",
                    # ),
                    # Case(
                    #     query=f"`{sec_group}`.str.fullmatch("
                    #     f"'{SecGroupValues.CMO}|{SecGroupValues.CMBS}', case=False, na=False)",
                    #     value="DXXXXX",
                    # ),
                    # Case(
                    #     query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.ABS}', case=False, na=False)",
                    #     value="DAXXXX",
                    # ),
                    # Case(
                    #     query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.BND}', case=False, na=False)",
                    #     value="DBXXXX",
                    # ),
                    # Case(
                    #     query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.MBS}', case=False, na=False)",
                    #     value="DGXXXX",
                    # ),
                    # Case(
                    #     query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.CASH}', case=False, na=False)",
                    #     value="DYXXXX",
                    # ),
                ],
            ),
        )[DevColumns.INSTRUMENT_CLASSIFICATION]

    def _get_trading_capacity(self) -> pd.Series:
        cases = [
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.MARKET}'",
                "attribute": f"{add_prefix(FileTypes.FILL, FillSourceColumns.DEALING_CAPACITY)}",
            },
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.CLIENT}'",
                "attribute": f"{add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.DEALING_CAPACITY)}",
            },
        ]
        trading_capacity = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=DevColumns.DEALING_CAPACITY,
                cases=cases,
            ),
        )
        value_map = {
            "A": TradingCapacity.AOTC.value,
            "O": TradingCapacity.AOTC.value,
            "P": TradingCapacity.DEAL.value,
            "R": TradingCapacity.AOTC.value,
            "M": TradingCapacity.AOTC.value,
            "I": TradingCapacity.DEAL.value,
            "S": TradingCapacity.DEAL.value,
            "L": TradingCapacity.DEAL.value,
            "C": TradingCapacity.DEAL.value,
        }
        return MapValue.process(
            source_frame=trading_capacity,
            params=ParamsMapValue(
                source_attribute=DevColumns.DEALING_CAPACITY,
                target_attribute=DevColumns.TRADING_CAPACITY,
                case_insensitive=True,
                value_map=value_map,
                default_value=TradingCapacity.AOTC.value,
            ),
            auditor=self.auditor,
        )[DevColumns.TRADING_CAPACITY]

    @staticmethod
    def _get_file_type_asset_class() -> str:
        input_file_url = os.getenv("SWARM_FILE_URL")
        return re.match(ALADDIN_FILES_PATTERN, Path(input_file_url).stem).group(
            "asset_class"
        )

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""

    def _get_pricing_references(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=[[pd.NA, pd.NA]] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[
                DevColumns.PRICING_REFERENCE_LXID,
                DevColumns.PRICING_REFERENCE_REDCODE,
            ],
        )

    def _get_option_type(self):
        """
        Gets the option type from the RIC column
        """
        instrument_related_cols = [
            add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC),
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP),
        ]
        instrument_frame = pd.concat(
            [self.pre_process_df, self.source_frame.loc[:, instrument_related_cols]],
            axis=1,
        )
        return instrument_utils.get_option_type_from_desc(
            data=instrument_frame,
            desc_column=add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC),
            asset_class_column=DevColumns.ASSET_CLASS,
            target_option=DevColumns.OPTION_TYPE,
        )

    def _get_underlying_symbol(self):
        """
        Gets the underlying symbol from the RIC column
        """
        instrument_related_cols = [
            add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC),
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP),
        ]
        instrument_frame = pd.concat(
            [self.pre_process_df, self.source_frame.loc[:, instrument_related_cols]],
            axis=1,
        )
        return instrument_utils.get_symbol_from_desc(
            data=instrument_frame,
            desc_column=add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC),
            target_symbol=DevColumns.SYMBOL,
        )

    def _get_underlying_index_term(self):
        """
        Currently set to null, but populated in the Barings override
        """
        return pd.DataFrame(
            data=pd.NA,
            index=self.source_frame.index,
            columns=[DevColumns.UNDERLYING_INDEX_TERM],
        )

    def _get_option_strike_price(self):
        """
        Gets the strike price from the RIC column
        """
        instrument_related_cols = [
            add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC),
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP),
        ]
        instrument_frame = pd.concat(
            [self.pre_process_df, self.source_frame.loc[:, instrument_related_cols]],
            axis=1,
        )

        return instrument_utils.get_strike_price_from_desc(
            data=instrument_frame,
            desc_column=add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC),
            asset_class_column=add_prefix(
                FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP
            ),
            target_strike_price=DevColumns.STRIKE_PRICE,
        )

    def _get_cfi_category(self) -> pd.Series:
        """
        Map asset class and secGroup to cfiCategory.
        """
        return pd.Series()

    def _get_cfi_group(self) -> pd.Series:
        """
        Map asset class and secGroup to cfiGroup.
        """
        return pd.Series()
