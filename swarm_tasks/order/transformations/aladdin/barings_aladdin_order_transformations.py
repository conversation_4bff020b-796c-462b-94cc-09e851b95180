from typing import List

import pandas as pd
from prefect.engine import signals
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_conditional import Case
from se_core_tasks.map.map_conditional import run_map_conditional
from se_elastic_schema.static.mifid2 import OptionType
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import AssetClass
from se_trades_tasks.order_and_tr.static import PartyPrefix
from se_trades_tasks.utilities.data_util import Delimiters

from swarm_tasks.io.read.aws.df_from_s3_csv import DfFromS3Csv
from swarm_tasks.io.read.aws.df_from_s3_csv import Params as ParamsDfFromS3Csv
from swarm_tasks.order.feed.aladdin.static import CustomOrdType
from swarm_tasks.order.feed.aladdin.static import DevColumns
from swarm_tasks.order.feed.aladdin.static import FileTypeAssetClass
from swarm_tasks.order.feed.aladdin.static import FileTypes
from swarm_tasks.order.feed.aladdin.static import OrderDetailSourceColumns
from swarm_tasks.order.feed.aladdin.static import OrderSourceColumns
from swarm_tasks.order.feed.aladdin.static import PlacementSourceColumns
from swarm_tasks.order.feed.aladdin.static import SecGroupValues
from swarm_tasks.order.feed.aladdin.static import SecTypeValues
from swarm_tasks.order.feed.aladdin.static import TransactionSourceColumns
from swarm_tasks.order.transformations.aladdin.aladdin_order_transformations import (
    AladdinOrderTransformations,
)
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.utilities.market.instruments import instrument_utils
from swarm_tasks.utilities.market.instruments.instrument_utils import (
    SecurityDescDelimiter,
)

S3_LOOKUP_INSTRUMENT_FILE_PATH = "mapping_tables/aladdin-instrument-ids/Barings_SM.csv"
S3_LOOKUP_PRIVATE_FILE_PATH = "mapping_tables/aladdin-private/Barings_Private_SM.csv"


class BaringsFileColumns:
    IDENTIFIER_TYPE = "identifier_type"
    IDENTIFIER_VALUE = "identifier_value"
    SECURITY_ID = "securityid"


class IdentifierTypes:
    LOANX_ID = "LOANX_ID"
    RED_PAIR_CODE = "RED_PAIR_CODE"


class BaringsAladdinOrderTransformations(AladdinOrderTransformations):
    def __init__(self, source_frame, **kwargs):

        source_frame = self.filter_out_private_asset_ids(source_frame=source_frame)

        source_frame = self.filter_out_direct_broker(source_frame=source_frame)

        if source_frame.empty:
            raise signals.SKIP(
                message="No orders to process after skip logic. Skipping processing."
            )

        super().__init__(source_frame=source_frame, **kwargs)

    @staticmethod
    def filter_out_private_asset_ids(source_frame: pd.DataFrame) -> pd.DataFrame:
        """
        Barings send us a file in the path
        mapping_tables/aladdin-private/Barings_Private_SM.csv
        This file will always have the same name and will be used
        as a lookup file to get the identifier value for asset ids we want to skip.

        It checks the values on the file against the ones in the source_frame and removes any
        row where the asset id is present in the file.
        """
        lookup_df = DfFromS3Csv.process(
            params=ParamsDfFromS3Csv(s3_key=S3_LOOKUP_PRIVATE_FILE_PATH)
        )

        if BaringsFileColumns.SECURITY_ID not in lookup_df.columns:
            raise signals.FAIL(
                message=f"The lookup file located at '{S3_LOOKUP_PRIVATE_FILE_PATH}'"
                f" is missing the '{BaringsFileColumns.SECURITY_ID}' column."
            )

        private_asset_ids = [
            x
            for x in lookup_df.loc[:, BaringsFileColumns.SECURITY_ID].to_list()
            if isinstance(x, str)
        ]

        private_asset_rows = source_frame.loc[
            :, add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.ASSET_ID)
        ].isin(private_asset_ids)

        source_frame = source_frame.loc[~private_asset_rows, :]

        return source_frame

    @staticmethod
    def filter_out_direct_broker(source_frame: pd.DataFrame) -> pd.DataFrame:
        """
        Barings require any trade that is executed with Broker/Counterparty = DIRECT

        if transaction.[tradePurpose] match with 'NEW_ISSUE' or 'IPO', then skip record
         ingestion
        """

        new_issue_ipo_purpose = (
            source_frame.loc[
                :,
                add_prefix(
                    FileTypes.TRANSACTION, TransactionSourceColumns.TRADE_PURPOSE
                ),
            ]
            .astype("string")
            .str.fullmatch("NEW_ISSUE|IPO", na=False, case=False)
        )

        source_frame = source_frame.loc[~new_issue_ipo_purpose, :]

        return source_frame

    def _get_pricing_references(self) -> pd.DataFrame:
        """
        Barings send us a daily file in the path
        mapping_tables/aladdin-instrument-ids/Barings_SM.csv.
        This file will always have the same name and will be used
        as a lookup file to get the identifier value for loan and cds sec groups.
        :return: Dataframe containing the looked up identifier values
        """
        result: pd.DataFrame = pd.DataFrame(index=self.source_frame.index)
        lookup_df = DfFromS3Csv.process(
            params=ParamsDfFromS3Csv(s3_key=S3_LOOKUP_INSTRUMENT_FILE_PATH)
        )

        result.loc[
            :, DevColumns.PRICING_REFERENCE_LXID
        ] = self.get_pricing_reference_lxid(lookup_df)
        result.loc[
            :, DevColumns.PRICING_REFERENCE_REDCODE
        ] = self.get_pricing_reference_redcode(lookup_df)

        return result.fillna(pd.NA)

    def get_pricing_reference_lxid(self, lookup_df) -> pd.Series:
        """
        Looks up Loan identifiers in the input dataframe.
        Matches Security ID values against the Asset Id in the source frame.
        Retrieves the Identifier Value for Loan sec group
        """
        # Filter to get only LOANX_ID identifier types
        lookup_df = lookup_df.query(
            f"`{BaringsFileColumns.IDENTIFIER_TYPE}`.str.fullmatch('{IdentifierTypes.LOANX_ID}', case=False, na=False)",
            engine="python",
        ).reset_index(drop=True)

        required_source_cols = [
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP),
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.ASSET_ID),
        ]

        loan_sec_group = (
            self.source_frame[
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP)
            ]
            == SecGroupValues.LOAN
        )

        source_loans_df = self.source_frame.loc[
            loan_sec_group,
            required_source_cols,
        ]

        merged_df = pd.merge(
            left=source_loans_df,
            right=lookup_df,
            left_on=add_prefix(
                FileTypes.TRANSACTION, TransactionSourceColumns.ASSET_ID
            ),
            right_on=BaringsFileColumns.SECURITY_ID,
            how="left",
        )

        if merged_df.empty:
            return pd.Series(
                data=[pd.NA] * source_loans_df.shape[0],
                index=source_loans_df.index,
                name=DevColumns.PRICING_REFERENCE_LXID,
            )

        return pd.Series(
            data=merged_df.loc[:, BaringsFileColumns.IDENTIFIER_VALUE]
            .fillna(pd.NA)
            .values,
            index=source_loans_df.index,
            name=DevColumns.PRICING_REFERENCE_LXID,
        )

    def get_pricing_reference_redcode(self, lookup_df) -> pd.Series:
        """
        Looks up CDS identifiers in the input dataframe.
        Matches Security ID values against the Asset Id in the source frame.
        Retrieves the Identifier Value for CDSWAP sec type and keeps only first 6 chars
        """

        # Filter to get only RED_PAIR_CODE identifier types
        lookup_df = lookup_df.query(
            f"`{BaringsFileColumns.IDENTIFIER_TYPE}`.str.fullmatch('{IdentifierTypes.RED_PAIR_CODE}', case=False, na=False)",
            engine="python",
        ).reset_index(drop=True)

        required_source_cols = [
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE),
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.ASSET_ID),
        ]

        cds_sec_type = (
            self.source_frame[
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE)
            ]
            == SecTypeValues.CDSWAP
        )

        source_cds_df = self.source_frame.loc[
            cds_sec_type,
            required_source_cols,
        ]

        merged_df = pd.merge(
            left=source_cds_df,
            right=lookup_df,
            left_on=add_prefix(
                FileTypes.TRANSACTION, TransactionSourceColumns.ASSET_ID
            ),
            right_on=BaringsFileColumns.SECURITY_ID,
            how="left",
        )

        if merged_df.empty:
            return pd.Series(
                data=[pd.NA] * source_cds_df.shape[0],
                index=source_cds_df.index,
                name=DevColumns.PRICING_REFERENCE_REDCODE,
            )

        return pd.Series(
            data=merged_df.loc[:, BaringsFileColumns.IDENTIFIER_VALUE].values,
            index=source_cds_df.index,
            name=DevColumns.PRICING_REFERENCE_REDCODE,
        )

    def _get_counterparty(self) -> pd.Series:
        exec_cpcy_id = add_prefix(
            FileTypes.TRANSACTION,
            TransactionSourceColumns.EXEC_CPCY_ID,
        )

        null_exec_cpcy_id = self.source_frame.loc[:, exec_cpcy_id].isnull()

        zero_exec_cpty_id = (
            self.source_frame.loc[
                :,
                exec_cpcy_id,
            ]
            .astype("string")
            .str.fullmatch("0", na=False)
        )

        null_or_zero_exec_cpcy_id = null_exec_cpcy_id | zero_exec_cpty_id

        counterparty_values = pd.Series(
            data=self.source_frame.loc[:, exec_cpcy_id].values,
            index=self.source_frame.index,
            name=DevColumns.COUNTERPARTY,
        )

        counterparty_values.loc[null_or_zero_exec_cpcy_id] = self.source_frame.loc[
            null_or_zero_exec_cpcy_id,
            add_prefix(
                FileTypes.TRANSACTION,
                TransactionSourceColumns.CPTY_ID,
            ),
        ]

        return pd.Series(
            data=(PartyPrefix.ID + counterparty_values.astype("string")).values,
            name=DevColumns.COUNTERPARTY,
            index=self.source_frame.index,
        )

    def _get_trader(self):
        query = f"`{add_prefix(FileTypes.ORDER, OrderSourceColumns.TRADER)}`.str.fullmatch('id:Eqaor', case=False, na=False)"
        prefix_frame = pd.concat(
            [
                PartyPrefix.ID
                + self.source_frame.loc[
                    :, add_prefix(FileTypes.ORDER, OrderSourceColumns.TRADER)
                ],
                PartyPrefix.ID
                + self.source_frame.loc[
                    :,
                    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.TRADER),
                ],
            ],
            axis=1,
        )
        return MapConditional.process(
            source_frame=prefix_frame,
            params=ParamsMapConditional(
                target_attribute=DevColumns.TRADER,
                cases=[
                    Case(
                        query=query,
                        attribute=add_prefix(
                            FileTypes.TRANSACTION, OrderSourceColumns.TRADER
                        ),
                    ),
                    Case(
                        query=f"~{query}",
                        attribute=add_prefix(
                            FileTypes.ORDER, OrderSourceColumns.TRADER
                        ),
                    ),
                ],
            ),
        )

    def _get_client(self) -> pd.DataFrame:
        """
        Client side takes orderdetail.portfolioId, same as super version.
        Markets side uses aggregate portfolios for the same transaction.portfolioId,
            generated in merger task
        """

        # Market Side Portfolios
        def _turn_aggregate_to_list_of_ids(aggregate_string: str) -> List[str]:
            if pd.isna(aggregate_string):
                return pd.NA

            removed_brackets = aggregate_string[1:-1]
            list_of_portfolio_ids = removed_brackets.replace(" ", "").split(",")
            return [PartyPrefix.ID + x for x in list_of_portfolio_ids]

        portfolio_id_with_prefix_market_side = self.source_frame.loc[
            :,
            DevColumns.AGGREGATE_PORTFOLIO_ID,
        ].apply(_turn_aggregate_to_list_of_ids)

        # Client Side Portofolio
        portfolio_id_with_prefix_client_side = PartyPrefix.ID + self.source_frame.loc[
            :,
            add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.PORTFOLIO_ID),
        ].astype("string")

        portfolio_id_with_prefix = pd.DataFrame(
            data=zip(
                portfolio_id_with_prefix_market_side.values,
                portfolio_id_with_prefix_client_side.values,
            ),
            index=self.source_frame.index,
            columns=[DevColumns.CLIENT_MARKET_SIDE, DevColumns.CLIENT_CLIENT_SIDE],
        )

        return run_map_conditional(
            source_frame=pd.concat(
                [portfolio_id_with_prefix, self.source_frame[DevColumns.ORDER_TYPE]],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=DevColumns.CLIENT,
                cases=[
                    Case(
                        query=f"`{DevColumns.ORDER_TYPE}`.astype('str').str.fullmatch('{CustomOrdType.MARKET}', case=False, na=False)",
                        attribute=DevColumns.CLIENT_MARKET_SIDE,
                    ),
                    Case(
                        query=f"~(`{DevColumns.ORDER_TYPE}`.astype('str').str.fullmatch('{CustomOrdType.MARKET}', case=False, na=False))",
                        attribute=DevColumns.CLIENT_CLIENT_SIDE,
                    ),
                ],
            ),
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.outgoingOrderAddlInfo."""
        concat_attributes_source_frame = pd.concat(
            [
                f"{TransactionSourceColumns.ASSET_ID}: "
                + self.source_frame.loc[
                    :,
                    add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.ASSET_ID
                    ),
                ],
                self.source_frame.loc[
                    :, add_prefix(FileTypes.ORDER, OrderSourceColumns.BASKET_ID)
                ],
                "Transaction Type: "
                + self.source_frame.loc[
                    :, add_prefix(FileTypes.ORDER, OrderSourceColumns.TRAN_TYPE)
                ],
                f"{OrderSourceColumns.ORDER_GEN_COMMENTS}: "
                + self.source_frame.loc[
                    :,
                    add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_GEN_COMMENTS),
                ],
                f"{TransactionSourceColumns.TRADE_PURPOSE}: "
                + self.source_frame.loc[
                    :,
                    add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.TRADE_PURPOSE
                    ),
                ],
            ],
            axis=1,
        )

        return ConcatAttributes.process(
            source_frame=concat_attributes_source_frame,
            params=ParamsConcatAttributes(
                source_attributes=[
                    add_prefix(FileTypes.ORDER, OrderSourceColumns.BASKET_ID),
                    add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.ASSET_ID
                    ),
                    add_prefix(FileTypes.ORDER, OrderSourceColumns.TRAN_TYPE),
                    add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_GEN_COMMENTS),
                    add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.TRADE_PURPOSE
                    ),
                ],
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                delimiter=Delimiters.NEW_LINE,
            ),
        )

    def _get_option_type(self):
        """
        Gets the option type
        """
        instrument_related_cols = [
            add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC),
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP),
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER),
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE),
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_DESC_1),
        ]
        instrument_frame = pd.concat(
            [self.pre_process_df, self.source_frame.loc[:, instrument_related_cols]],
            axis=1,
        )
        result_frame = instrument_utils.get_option_type_from_desc(
            data=instrument_frame,
            desc_column=add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC),
            asset_class_column=DevColumns.ASSET_CLASS,
            target_option=DevColumns.OPTION_TYPE,
        )

        # Now, we need to start overwriting values in the target column based on conditions
        curotc_mask = (
            result_frame.loc[:, DevColumns.OPTION_TYPE].isnull()
            & (
                self.pre_process_df.loc[:, DevColumns.ASSET_CLASS].str.fullmatch(
                    AssetClass.OPTION, na=False
                )
            )
            & (
                self.source_frame.loc[
                    :, add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC)
                ].isnull()
            )
            & (
                self.source_frame.loc[
                    :,
                    add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE
                    ),
                ].str.fullmatch("CUROTC", case=False, na=False)
            )
        )

        equity_mask = (
            result_frame.loc[:, DevColumns.OPTION_TYPE].isnull()
            & (
                self.pre_process_df.loc[:, DevColumns.ASSET_CLASS].str.fullmatch(
                    AssetClass.OPTION, na=False
                )
            )
            & (
                self.source_frame.loc[
                    :, add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC)
                ].isnull()
            )
            & (
                self.source_frame.loc[
                    :,
                    add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE
                    ),
                ].str.fullmatch("EQUITY", case=False, na=False)
            )
        )

        result_frame.loc[curotc_mask, DevColumns.OPTION_TYPE] = (
            instrument_frame.loc[
                curotc_mask,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER),
            ]
            .str.split(SecurityDescDelimiter.OPTIONS_DELIMITER)
            .str.get(0)
            .fillna(pd.NA)
            .str.upper()
            .map({"CALL": OptionType.CALL.value, "PUT": OptionType.PUTO.value})
        )
        result_frame.loc[equity_mask, DevColumns.OPTION_TYPE] = (
            instrument_frame.loc[
                equity_mask,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER),
            ]
            .str.split("@")
            .str.get(0)
            .str.strip()
            .str[-1]
            .fillna(pd.NA)
            .str.upper()
            .map({"C": OptionType.CALL.value, "P": OptionType.PUTO.value})
        )

        return result_frame.fillna(pd.NA)

    def _get_instrument_classification(self) -> pd.Series:
        """
        Map secType or secGroup against the following:
            transaction.[secGroup] = "LOAN": "LLXXXX"
            transaction.[secGroup] = "EQUITY": "ESXXXX"
            transaction.[secGroup] = "FUTURE": "FFXXXX"
            transaction.[secType] = "SPOT": "IFXXXX"
            transaction.[secGroup] = "SWAP": "SEXXXX"
            transaction.[secType] = "FWRD": "JXXXXX"
            transaction.[secType] = "OPT": "HFXXXX"
            transaction.[SecGroup] = "CDS": "SCXXXX"
            transaction.[secType] = "CSWAP": "SFXXXX"
            transaction.[secGroup] = "OPTION":"OXXXXX"
            transaction.[secGroup] = ["CMO", "CMBS"] : "DXXXXX"
            transaction.[secGroup] = "ABS" : "DAXXXX"
            transaction.[secGroup] = "BND" : "DBXXXX"
            transaction.[secGroup] = "MBS" : "DGXXXX"
            transaction.[secGroup] = "CASH" : "DYXXXX"
        """
        # commented bits waiting for specs

        # sec_type = add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE)
        sec_group = add_prefix(
            FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP
        )
        sec_desc_1 = add_prefix(
            FileTypes.TRANSACTION, TransactionSourceColumns.SEC_DESC_1
        )

        return MapConditional.process(
            source_frame=pd.concat(
                [self.source_frame, self.pre_process_df.loc[:, DevColumns.OPTION_TYPE]],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=DevColumns.INSTRUMENT_CLASSIFICATION,
                cases=[
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.LOAN}', case=False, na=False)",
                        value="LLXXXX",
                    ),
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.EQUITY}', case=False, na=False)",
                        value="EXXXXX",
                    ),
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.FUTURE}', case=False, na=False)",
                        value="FFXXXX",
                    ),
                    # Case(
                    #     query=f"`{sec_type}`.str.fullmatch('{SecTypeValues.SPOT}', case=False, na=False)",
                    #     value="IFXXXX",
                    # ),
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.SWAP}', case=False, na=False)",
                        value="SEXXXX",
                    ),
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.SWAP}', case=False, na=False) & (`{sec_desc_1}`.str.contains('OIS', case=False, na=False))",
                        value="SRHXXX",
                    ),
                    # Case(
                    #     query=f"`{sec_type}`.str.fullmatch('{SecTypeValues.FWRD}', case=False, na=False)",
                    #     value="JXXXXX",
                    # ),
                    # Case(
                    #     query=f"`{sec_type}`.str.fullmatch('{SecTypeValues.OPT}', case=False, na=False)",
                    #     value="HFXXXX",
                    # ),
                    # Case(
                    #     query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.CDS}', case=False, na=False)",
                    #     value="SCXXXX",
                    # ),
                    # Case(
                    #     query=f"`{sec_type}`.str.fullmatch('{SecTypeValues.CSWAP}', case=False, na=False)",
                    #     value="SFXXXX",
                    # ),
                    # Case(
                    #     query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.OPTION}', case=False, na=False)",
                    #     value="OXXXXX",
                    # ),
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.OPTION}', case=False, na=False) & ({DevColumns.OPTION_TYPE}.str.fullmatch('{OptionType.CALL.value}', case=False, na=False))",
                        value="OCXXXX",
                    ),
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.OPTION}', case=False, na=False) & ({DevColumns.OPTION_TYPE}.str.fullmatch('{OptionType.PUTO.value}', case=False, na=False))",
                        value="OPXXXX",
                    ),
                    # Case(
                    #     query=f"`{sec_group}`.str.fullmatch("
                    #     f"'{SecGroupValues.CMO}|{SecGroupValues.CMBS}', case=False, na=False)",
                    #     value="DXXXXX",
                    # ),
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.ABS}', case=False, na=False)",
                        value="DAXXXX",
                    ),
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.BND}', case=False, na=False)",
                        value="DBXXXX",
                    ),
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.MBS}', case=False, na=False)",
                        value="DGXXXX",
                    ),
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.CASH}', case=False, na=False)",
                        value="DYXXXX",
                    ),
                ],
            ),
        )[DevColumns.INSTRUMENT_CLASSIFICATION]

    def _get_underlying_symbol(self):
        """
        Gets the underlying symbol from the RIC column
        """
        instrument_related_cols = [
            add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC),
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP),
            add_prefix(
                FileTypes.TRANSACTION, TransactionSourceColumns.UNDERLYING_SNP_CUSIP
            ),
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE),
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER),
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_DESC_1),
        ]
        instrument_frame = pd.concat(
            [self.pre_process_df, self.source_frame.loc[:, instrument_related_cols]],
            axis=1,
        )
        result_frame = instrument_utils.get_symbol_from_desc(
            data=instrument_frame,
            desc_column=add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC),
            target_symbol=DevColumns.SYMBOL,
        )

        # Options logic when there isn't a RIC
        curotc_mask = (
            result_frame.loc[:, DevColumns.SYMBOL].isnull()
            & (
                self.pre_process_df.loc[:, DevColumns.ASSET_CLASS].str.fullmatch(
                    AssetClass.OPTION, na=False
                )
            )
            & (
                self.source_frame.loc[
                    :, add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC)
                ].isnull()
            )
            & (
                self.source_frame.loc[
                    :,
                    add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE
                    ),
                ].str.fullmatch("CUROTC", case=False, na=False)
            )
        )

        equity_mask = (
            result_frame.loc[:, DevColumns.SYMBOL].isnull()
            & (
                self.pre_process_df.loc[:, DevColumns.ASSET_CLASS].str.fullmatch(
                    AssetClass.OPTION, na=False
                )
            )
            & (
                self.source_frame.loc[
                    :, add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC)
                ].isnull()
            )
            & (
                self.source_frame.loc[
                    :,
                    add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE
                    ),
                ].str.fullmatch("EQUITY", case=False, na=False)
            )
        )

        result_frame.loc[curotc_mask, DevColumns.SYMBOL] = instrument_frame.loc[
            curotc_mask,
            add_prefix(
                FileTypes.TRANSACTION, TransactionSourceColumns.UNDERLYING_SNP_CUSIP
            ),
        ]

        result_frame.loc[equity_mask, DevColumns.SYMBOL] = (
            instrument_frame.loc[
                equity_mask,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER),
            ]
            .str.split(" ")
            .str.get(0)
            .str.strip()
            .str[:4]
            .fillna(pd.NA)
        )

        if self.file_type_asset_class == FileTypeAssetClass.DERIV:
            # FX Swaps logic
            fx_swap_mask = instrument_frame.loc[
                :, add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE)
            ].str.fullmatch("CSWAP", case=False, na=False)

            result_frame.loc[fx_swap_mask, DevColumns.SYMBOL] = (
                instrument_frame.loc[
                    fx_swap_mask,
                    add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.SEC_DESC_1
                    ),
                ]
                .str.split("CSWAP:")
                .str.get(1)
                .str.strip()
                .str.split(" ")
                .str.get(0)
                .fillna(pd.NA)
            )

        return result_frame.fillna(pd.NA)

    def _get_option_strike_price(self):
        """
        Gets the strike price from the RIC column
        """
        instrument_related_cols = [
            add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC),
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP),
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE),
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.TRADE_COUPON),
        ]
        instrument_frame = pd.concat(
            [self.pre_process_df, self.source_frame.loc[:, instrument_related_cols]],
            axis=1,
        )

        result_frame = instrument_utils.get_strike_price_from_desc(
            data=instrument_frame,
            desc_column=add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC),
            asset_class_column=add_prefix(
                FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP
            ),
            target_strike_price=DevColumns.STRIKE_PRICE,
        )

        equity_curotc_mask = (
            result_frame.loc[:, DevColumns.STRIKE_PRICE].isnull()
            & (
                self.pre_process_df.loc[:, DevColumns.ASSET_CLASS].str.fullmatch(
                    AssetClass.OPTION, na=False
                )
            )
            & (
                self.source_frame.loc[
                    :, add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC)
                ].isnull()
            )
            & (
                self.source_frame.loc[
                    :,
                    add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE
                    ),
                ].str.fullmatch("EQUITY|CUROTC", case=False, na=False)
            )
        )
        result_frame.loc[
            equity_curotc_mask, DevColumns.STRIKE_PRICE
        ] = instrument_frame.loc[
            equity_curotc_mask,
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.TRADE_COUPON),
        ]
        return result_frame.fillna(pd.NA)

    def _get_underlying_index_term(self):
        """
        Sets the underlying index term for IRS swaps
        """
        instrument_related_cols = [
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP),
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_DESC_1),
        ]
        instrument_frame = pd.concat(
            [self.pre_process_df, self.source_frame.loc[:, instrument_related_cols]],
            axis=1,
        )
        # Interest rate swaps logic
        irs_mask = instrument_frame.loc[
            :, add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP)
        ].str.fullmatch("SWAP", case=False, na=False) & (
            instrument_frame.loc[
                :,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_DESC_1),
            ].str.contains("OIS", case=False, na=False)
        )
        result_frame = pd.DataFrame(
            data=pd.NA,
            index=self.source_frame.index,
            columns=[DevColumns.UNDERLYING_INDEX_TERM],
        )
        result_frame.loc[irs_mask, DevColumns.UNDERLYING_INDEX_TERM] = "1 DAY"
        return result_frame

    def _get_cfi_category(self) -> pd.Series:
        """
        Map asset class and secGroup to cfiCategory.
        Do not map for FX
        """

        if self.file_type_asset_class == FileTypeAssetClass.FX:
            return pd.Series(
                index=self.source_frame.index, data=[pd.NA] * self.source_frame.shape[0]
            )

        sec_group = add_prefix(
            FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP
        )
        sec_desc_1 = add_prefix(
            FileTypes.TRANSACTION, TransactionSourceColumns.SEC_DESC_1
        )

        return MapConditional.process(
            source_frame=pd.concat(
                [self.source_frame, self.pre_process_df.loc[:, DevColumns.OPTION_TYPE]],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=DevColumns.CFI_CATEGORY,  # Only cfiCategory
                cases=[
                    # Case for "option" asset class with input-optionType checks
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.OPTION}', case=False, na=False) & ({DevColumns.OPTION_TYPE}.str.fullmatch('{OptionType.CALL.value}', case=False, na=False))",
                        value="Options",  # cfiCategory for Call options
                    ),
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.OPTION}', case=False, na=False) & ({DevColumns.OPTION_TYPE}.str.fullmatch('{OptionType.PUTO.value}', case=False, na=False))",
                        value="Options",  # cfiCategory for Put options
                    ),
                    # Case for "FX Swap"
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.FX}', case=False, na=False)",
                        value="Swaps",  # cfiCategory for FX Swap
                    ),
                    # Case for "IRS Swap"
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.SWAP}', case=False, na=False) & (`{sec_desc_1}`.str.contains('OIS', case=False, na=False))",
                        value="Swaps",  # cfiCategory for IRS Swap
                    ),
                    # Case for "Equity"
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.EQUITY}', case=False, na=False)",
                        value="Equity",  # cfiCategory for Equity
                    ),
                    # Case for ABS
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.ABS}', case=False, na=False)",
                        value="Debt",  # cfiCategory for ABS
                    ),
                    # Case for MBS
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.MBS}', case=False, na=False)",
                        value="Debt",  # cfiCategory for MBS
                    ),
                    # Case for LOAN
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.LOAN}', case=False, na=False)",
                        value="Financing",  # cfiCategory for LOAN
                    ),
                    # Case for "FUTURE"
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.FUTURE}', case=False, na=False)",
                        value="Futures",  # cfiCategory for FUTURE
                    ),
                ],
            ),
        )[DevColumns.CFI_CATEGORY]

    def _get_cfi_group(self) -> pd.Series:
        """
        Map asset class and secGroup to cfiGroup.
        Do not map for FX
        """

        if self.file_type_asset_class == FileTypeAssetClass.FX:
            return pd.Series(
                index=self.source_frame.index, data=[pd.NA] * self.source_frame.shape[0]
            )

        sec_group = add_prefix(
            FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP
        )

        return MapConditional.process(
            source_frame=pd.concat(
                [self.source_frame, self.pre_process_df.loc[:, DevColumns.OPTION_TYPE]],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=DevColumns.CFI_GROUP,  # Only cfiGroup
                cases=[
                    # Case for "option" asset class with input-optionType checks
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.OPTION}', case=False, na=False) & ({DevColumns.OPTION_TYPE}.str.fullmatch('{OptionType.CALL.value}', case=False, na=False))",
                        value="Call options",  # cfiGroup for Call options
                    ),
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.OPTION}', case=False, na=False) & ({DevColumns.OPTION_TYPE}.str.fullmatch('{OptionType.PUTO.value}', case=False, na=False))",
                        value="Put options",  # cfiGroup for Put options
                    ),
                    # Case for "FX Swap"
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.FX}', case=False, na=False)",
                        value="Financial Futures",  # cfiGroup for FX Swap
                    ),
                    # No cfiGroup for IRS Swap
                    # Case for ABS
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.ABS}', case=False, na=False)",
                        value="Asset-backed securities",  # cfiGroup for ABS
                    ),
                    # Case for MBS
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.MBS}', case=False, na=False)",
                        value="Mortgage-backed securities",  # cfiGroup for MBS
                    ),
                    # Case for LOAN
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.LOAN}', case=False, na=False)",
                        value="Loan Lease",  # cfiGroup for LOAN
                    ),
                    Case(
                        query=f"`{sec_group}`.str.fullmatch('{SecGroupValues.FUTURE}', case=False, na=False)",
                        value="Financial Futures",  # cfiGroup for FUTURE
                    ),
                ],
            ),
        )[DevColumns.CFI_GROUP]

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderSubmitted."""
        cases = [
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.MARKET}'",
                "attribute": f"{add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.SEND_TIME_UTC)}",
            },
            {
                "query": f"`{DevColumns.ORDER_TYPE}` == '{CustomOrdType.CLIENT}'",
                "attribute": f"{add_prefix(FileTypes.ORDER, OrderSourceColumns.ACTIVATED_TIMESTAMP_UTC)}",
            },
        ]
        order_submitted = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=DevColumns.TIMESTAMPS_ORDER_SUBMITTED,
                cases=cases,
            ),
        )
        return ConvertDatetime.process(
            order_submitted,
            params=ParamsConvertDatetime(
                source_attribute=DevColumns.TIMESTAMPS_ORDER_SUBMITTED,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                convert_to=ConvertTo.DATETIME,
            ),
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """Returns a dataframe containing transactionDetails.quantityCurrency"""

        if self.file_type_asset_class == FileTypeAssetClass.FI:
            return pd.DataFrame(
                data=self.source_frame.loc[
                    :, add_prefix(FileTypes.ORDER, OrderSourceColumns.PRICE_CCY)
                ].values,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY],
            )
        else:
            return pd.DataFrame(
                data=self.pre_process_df.loc[:, DevColumns.NOTIONAL_CURRENCY_2].values,
                index=self.pre_process_df.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY],
            )
