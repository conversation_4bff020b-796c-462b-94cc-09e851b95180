from typing import Dict
from typing import List
from typing import Union

import pandas as pd
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_elastic_schema.static.mifid2 import OrderStatus
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.fix.fix_parser_result_to_frame import StaticFields
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.order.transformations.cme.stp.static import DATA_SOURCE
from swarm_tasks.order.transformations.cme.stp.static import MARKET_SIDE
from swarm_tasks.order.transformations.cme.stp.static import SourceColumns
from swarm_tasks.order.transformations.cme.stp.static import TempColumns
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.utilities.static import Delimiters


class CmeStpFixOrderTransformations(AbstractOrderTransformations):
    def _pre_process(self):
        self.pre_process_df.loc[
            :, [TempColumns.ORDER_ID, TempColumns.ORDER_ID_WITH_INVERTED_SIDE]
        ] = self._get_order_ids()
        self.pre_process_df.loc[:, TempColumns.BUY_SELL] = self._get_buy_sell()

        self.pre_process_df[
            [TempColumns.TRADER, TempColumns.CLIENT, TempColumns.COUNTERPARTY]
        ] = self.source_frame.loc[
            :, [SourceColumns.PARTY_ROLE, SourceColumns.PARTY_ID]
        ].apply(
            lambda x: self._get_party_id(
                party_role=x[SourceColumns.PARTY_ROLE],
                party_id=x[SourceColumns.PARTY_ID],
            ),
            axis=1,
        )

    def process(self):
        self.pre_process()
        self.buy_sell()
        self.data_source_name()
        self.date()
        self.execution_details_buy_sell_indicator()
        self.execution_details_order_status()
        self.execution_details_order_type()
        self.execution_details_outgoing_order_addl_info()
        self.execution_details_passive_aggressive_indicator()
        self.execution_details_trading_capacity()
        self.id()
        self.market_identifiers_parties()
        self.market_identifiers()
        self.meta_model()
        self.order_class()
        self.order_identifiers_aggregated_order_id_code()
        self.order_identifiers_initial_order_designation()
        self.order_identifiers_internal_order_id_code()
        self.order_identifiers_order_id_code()
        self.order_identifiers_trading_venue_transaction_id_code()
        self.order_identifiers_transaction_ref_no()
        self.price_forming_data_initial_quantity()
        self.price_forming_data_traded_quantity()
        self.report_details_transaction_ref_no()
        self.source_key()
        self.timestamps_order_received()
        self.timestamps_order_submitted()
        self.timestamps_trading_date_time()
        self.transaction_details_buy_sell_indicator()
        self.transaction_details_record_type()
        self.transaction_details_trading_capacity()
        self.transaction_details_trading_date_time()
        self.transaction_details_quantity()
        self.post_process()
        return self.target_df

    def _post_process(self):
        self.target_df.loc[:, TempColumns.LAST_PX] = self.source_frame.loc[
            :, SourceColumns.LAST_PX
        ]
        self.target_df.loc[:, TempColumns.SYMBOL] = self.source_frame.loc[
            :, SourceColumns.SYMBOL
        ]
        self.target_df.loc[:, TempColumns.PRICE] = pd.NA
        self.target_df.loc[:, TempColumns.STOP_PX] = pd.NA

    def _buy_sell(self) -> pd.DataFrame:
        """Returns a data frame containing _order.buySell and _orderState.buySell.
        This is populated from SourceColumns.SIDE"""
        order_buy_sell = pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.BUY_SELL].values,
            index=self.pre_process_df.index,
            columns=[
                add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.BUY_SELL)
            ],
        )
        order_state_buy_sell = pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.BUY_SELL].values,
            index=self.pre_process_df.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE, attribute=OrderColumns.BUY_SELL
                )
            ],
        )
        return pd.concat([order_buy_sell, order_state_buy_sell], axis=1)

    def _data_source_name(self) -> pd.DataFrame:
        """Returns a data frame containing dataSourceName.
        This is populated with the static value 'CME STP'"""
        return pd.DataFrame(
            data=DATA_SOURCE,
            index=self.target_df.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """Returns a data frame containing date as the target column"""
        return ConvertDatetime.process(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.SENDING_TIME,
                target_attribute=OrderColumns.DATE,
                convert_to=ConvertTo.DATE,
            ),
        )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        pass

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Populates and returns executionDetails.buySellIndicator column"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.BUY_SELL].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_order_status(self) -> pd.DataFrame:
        """Returns a data frame containing _order.executionDetails.orderStatus and
        _orderState.executionDetails.orderStatus"""
        order_execution_details_order_status = pd.DataFrame(
            data=OrderStatus.NEWO.value,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                )
            ],
        )
        order_status_map = {"1": "CANC", "2": "FILL"}
        order_state_execution_details_order_status = pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.TRADE_REPORT_TRANS_TYPE]
            .astype("string")
            .map(order_status_map)
            .values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                )
            ],
        )
        return pd.concat(
            [
                order_execution_details_order_status,
                order_state_execution_details_order_status,
            ],
            axis=1,
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.orderType.
        This is populated with the static value mapping."""
        return pd.DataFrame(
            data="Market",
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_ORDER_TYPE],
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.outgoingOrderAddlInfo."""
        venue_type_map = {
            "C": "Clearing house",
            "E": "Electronic",
            "O": "Off facility",
            "P": "Pit",
            "R": "Registered Market (SEF)",
            "X": "Ex-Pit",
        }
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.VENUE_TYPE]
            .astype("string")
            .map(venue_type_map)
            .values,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO],
        )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.passiveAggressiveIndicator"""
        value_map = {"Y": "AGRE", "N": "PASV"}
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.AGGRESSOR_INDICATOR]
            .astype("string")
            .str.upper()
            .map(value_map)
            .values,
            index=self.target_df.index,
            columns=[OrderColumns.EXECUTION_DETAILS_PASSIVE_AGGRESSIVE_INDICATOR],
        )

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_stop_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.tradingCapacity"""
        value_map = {"1": "DEAL", "2": "DEAL", "3": "AOTC", "4": "AOTC"}
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.CUST_ORDER_CAPACITY]
            .astype("string")
            .map(value_map)
            .values,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY],
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _financing_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _hierarchy(self) -> pd.DataFrame:
        """Not Implemented"""

    def _id(self) -> pd.DataFrame:
        """Returns a data frame containing _order.id and _orderState.id.
        This is populated from SourceColumns.ORDER_ID and SECURITY_ID"""
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, TempColumns.ORDER_ID].values,
                    index=self.pre_process_df.index,
                    columns=[
                        add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID)
                    ],
                ),
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, TempColumns.ORDER_ID].values,
                    index=self.pre_process_df.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER_STATE, attribute=OrderColumns.ID
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _is_discretionary(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_iceberg(self):
        """Not Implemented"""

    def _is_repo(self):
        """Not Implemented"""

    def _is_synthetic(self):
        """Not Implemented"""

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        """Not Implemented"""

    def _market_identifiers(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_parties() has been called earlier"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """Not Implemented"""

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.parties by calling PartyIdentifiers"""
        order_party_id_mappings = self._get_order_to_party_mappings()
        sellers = pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.ORDER_ID]
            .apply(
                lambda x: order_party_id_mappings.get(x, {}).get(
                    TempColumns.CLIENT, pd.NA
                )
            )
            .values,
            index=self.pre_process_df.index,
            columns=[TempColumns.SELLER],
        )

        parties_source_frame = pd.concat(
            [
                self.pre_process_df.loc[
                    :,
                    [
                        TempColumns.ORDER_ID_WITH_INVERTED_SIDE,
                        TempColumns.TRADER,
                        TempColumns.CLIENT,
                        TempColumns.COUNTERPARTY,
                    ],
                ],
                sellers,
                self.target_df.loc[
                    :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
                ],
            ],
            axis=1,
        )

        return GenericOrderPartyIdentifiers.process(
            source_frame=parties_source_frame,
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                trader_identifier=TempColumns.TRADER,
                client_identifier=TempColumns.CLIENT,
                counterparty_identifier=TempColumns.COUNTERPARTY,
                buyer_identifier=TempColumns.CLIENT,
                seller_identifier=TempColumns.SELLER,
                buy_sell_side_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                use_buy_mask_for_buyer_seller=True,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """Returns a data frame containing _order.__meta_model__ and _orderState.__meta_model__.
        The columns are populated with the static values Order and OrderState respectively"""
        return pd.DataFrame(
            data=[["Order", "OrderState"]],
            index=self.source_frame.index,
            columns=[
                add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.META_MODEL),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.META_MODEL,
                ),
            ],
        )

    def _order_class(self) -> pd.DataFrame:
        trd_type_map = {
            "0": "Regular Trade",
            "1": "Block Trade",
            "2": "EFP(Exchange for physical)",
            "3": "Transfer",
            "11": "Exchange for Risk(EFR)",
            "22": "Over the Counter Privately Negotiated Trades (OPNT)",
            "23": "Substitution of Futures for Forwards",
            "45": "Option exercise",
            "54": "OTC / Large Notional Off Facility Swap",
            "55": "Exchange Basis Facility (EBF)",
            "57": "Netted trade",
            "58": "Block swap trade",
            "59": "Credit event trade",
            "60": "Succession event trade",
        }
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.TRD_TYPE]
            .astype("string")
            .map(trd_type_map)
            .values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_CLASS],
        )

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing column orderIdentifiers.aggregatedOrderId"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.TRADE_REQUEST_ID].values,
            index=self.target_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID],
        )

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.initialOrderDesignation column"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.SECONDARY_EXEC_ID].values,
            index=self.target_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_INITIAL_ORDER_DESIGNATION],
        )

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """Returns a dataframe with the col orderIdentifiers.internalOrderIdCode"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.ORDER_ID].values,
            index=self.target_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE],
        )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.orderIdCode"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.ORDER_ID].values,
            index=self.target_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
        )

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        """Returns a dataframe with the col orderIdentifiers.tradingVenueTransactionIdCode"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.ORDER_ID].values,
            index=self.target_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_TRADING_VENUE_TRANSACTION_ID_CODE],
        )

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a df with the col orderIdentifiers.transactionRefNo"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.EXEC_ID].values,
            index=self.target_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO],
        )

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """Returns a df with the col priceFormingData.initialQuantity"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LAST_QTY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY],
        )

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """Returns a df with the col priceFormingData.tradedQuantity"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LAST_QTY].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    ModelPrefix.ORDER_STATE,
                    OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                )
            ],
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a df with the col reportDetails.transactionRefNo"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.EXEC_ID].values,
            index=self.target_df.index,
            columns=[OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO],
        )

    def _source_index(self) -> pd.DataFrame:
        """Not Implemented"""

    def _source_key(self) -> pd.DataFrame:
        """Returns a df with the col sourceKey"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, StaticFields.FILE_URL].values,
            index=self.target_df.index,
            columns=[OrderColumns.SOURCE_KEY],
        )

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_order_received(self) -> pd.DataFrame:
        """Returns a df with col timestamps.orderReceived"""
        return ConvertDatetime.process(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.SENDING_TIME,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                convert_to=ConvertTo.DATETIME,
            ),
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """Returns a df with col timestamps.orderSubmitted"""
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.TIMESTAMPS_ORDER_RECEIVED].values,
            index=self.target_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED],
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """Returns a df with col timestamps.orderSubmitted"""
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.TIMESTAMPS_ORDER_RECEIVED].values,
            index=self.target_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_SUBMITTED],
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """Returns a df with col timestamps.tradingDateTime"""
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.TIMESTAMPS_ORDER_RECEIVED].values,
            index=self.target_df.index,
            columns=[OrderColumns.TIMESTAMPS_TRADING_DATE_TIME],
        )

    def _timestamps_validity_period(self) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a df with col transactionDetails.buySellIndicator"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.BUY_SELL].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price_average(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.quantity populated from priceFormingData.tradedQuantity"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :,
                add_prefix(
                    ModelPrefix.ORDER_STATE,
                    OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                ),
            ].values,
            index=self.target_df.index,
            columns=[
                add_prefix(
                    ModelPrefix.ORDER_STATE,
                    OrderColumns.TRANSACTION_DETAILS_QUANTITY,
                )
            ],
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """Returns a df with col transactionDetails.recordType with a static value
        'Market Side'"""
        return pd.DataFrame(
            data=MARKET_SIDE,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE,
                )
            ],
        )

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.tradingCapacity"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.transactionDetailsTradingDateTime"""
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.TIMESTAMPS_ORDER_RECEIVED].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME],
        )

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_venue(self) -> pd.DataFrame:
        """Not Implemented"""

    @staticmethod
    def _get_party_id(
        party_role: Union[List, str], party_id: Union[List, str]
    ) -> pd.Series:
        """
        This method retuns one PartyID from the list of PartyID with the same index where the PartyRole is 36
        :param party_role: list of all party roles
        :param party_id: list of all party roles
        :return:
        """
        selected_trader = pd.NA
        selected_client = pd.NA
        selected_counterparty = pd.NA
        if not isinstance(party_role, list):
            return PartyPrefix.ID + pd.Series(
                [selected_trader, selected_client, selected_counterparty]
            )

        for index, value in enumerate(party_role):
            if str(value) == "7":
                selected_client = party_id[index]
            elif str(value) == "30":
                selected_counterparty = party_id[index]
            elif str(value) == "62":
                selected_trader = party_id[index]

        return PartyPrefix.ID + pd.Series(
            [selected_trader, selected_client, selected_counterparty]
        )

    def _get_order_to_party_mappings(self) -> Dict:
        """
        This methods returns a dict with the party related data from the associated order.
        For example, if there's a BUY order with order_id ABC|SYM, there'll be a key "ABC|SYM|2" (note the
        '2' here, the key represents SELL even though it's a BUY since these fields will be used to populate
        counterparty and buyers of the respective SELL order
        Refer https://steeleye.atlassian.net/wiki/spaces/IN/pages/2379808783/Order+CME+STP+FIX#1.-Linking-buyer-seller-FIX-messages for
        complete details
        :return:
        """
        order_id_source_frame_df = self.pre_process_df.loc[
            :,
            [
                TempColumns.ORDER_ID_WITH_INVERTED_SIDE,
                TempColumns.TRADER,
                TempColumns.CLIENT,
            ],
        ]
        mappings = order_id_source_frame_df.set_index(
            [TempColumns.ORDER_ID_WITH_INVERTED_SIDE]
        ).T.to_dict()
        return mappings

    def _get_order_ids(self) -> List:
        """
        This method returns a df of two cols OrderID and the OrderID with inverted sides as a list
        TempColumns.OrderID = "ABC|SYM|1 (this is the order_id for the BUY)
        this method would also generate an inverted sides order_id "ABC|SYM|2"
        This is used later to get the Party related fields from the associate order. So, in case of BUY,
        the party details from the SELL order is required. That's where this field will be directly used
        to fetch fields from the associated order
        """
        inverted_side_map = {"1": "2", "2": "1"}
        temp_order_id_df = ConcatAttributes.process(
            source_frame=self.source_frame,
            params=ParamsConcatAttributes(
                target_attribute=TempColumns.ORDER_ID_SYMBOL,
                source_attributes=[
                    SourceColumns.CL_ORD_ID,
                    SourceColumns.SYMBOL,
                ],
                delimiter=Delimiters.PIPE,
            ),
        )[TempColumns.ORDER_ID_SYMBOL].str.replace(" ", "")

        inverted_side_series = self.source_frame.loc[:, SourceColumns.SIDE].map(
            inverted_side_map
        )
        order_ids = (
            temp_order_id_df + "|" + self.source_frame.loc[:, SourceColumns.SIDE]
        )
        inverted_ids = temp_order_id_df + "|" + inverted_side_series

        return pd.concat([order_ids, inverted_ids], axis=1).values.tolist()

    def _get_buy_sell(self) -> pd.Series:
        """Populates a temp col __buy_sell__ to be used to populate other buy_sell fields"""
        value_map = {
            "1": "BUYI",
            "2": "SELL",
        }
        return (
            self.source_frame.loc[:, SourceColumns.SIDE].astype("string").map(value_map)
        )

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""
