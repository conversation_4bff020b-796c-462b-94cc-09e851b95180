import pandas as pd
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.frame.map_conditional_attribute_from_list_items import (
    MapConditionalAttributeFromListItems,
)
from swarm_tasks.generic.frame.map_conditional_attribute_from_list_items import (
    Params as MapFromListItemsParams,
)
from swarm_tasks.order.transformations.cme.stp.cme_stp_fix_order_transformations import (
    CmeStpFixOrderTransformations,
)
from swarm_tasks.order.transformations.cme.stp.static import (
    SourceColumns as CmeSourceColumns,
)
from swarm_tasks.order.transformations.cme.stp.static import (
    TempColumns as CmeTempColumns,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)


class TempColumns(CmeTempColumns):
    INITIAL_OR_TRADED_QUANTITY = "__initial_or_traded_quantity__"


class SourceColumns(CmeSourceColumns):
    UNIT_OF_MEASURE_QTY = "UnitOfMeasureQty"


class SocarCmeStpFixOrderTransformations(CmeStpFixOrderTransformations):
    def _pre_process(self):
        super(SocarCmeStpFixOrderTransformations, self)._pre_process()
        self.pre_process_df[TempColumns.INITIAL_OR_TRADED_QUANTITY] = (
            self.source_frame.loc[:, SourceColumns.LAST_QTY]
            * self.source_frame.loc[:, SourceColumns.UNIT_OF_MEASURE_QTY]
        )

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """Returns a df with the col priceFormingData.initialQuantity"""
        return pd.DataFrame(
            data=self.pre_process_df[TempColumns.INITIAL_OR_TRADED_QUANTITY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY],
        )

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """Returns a df with the col priceFormingData.tradedQuantity"""
        return pd.DataFrame(
            data=self.pre_process_df[TempColumns.INITIAL_OR_TRADED_QUANTITY].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    ModelPrefix.ORDER_STATE,
                    OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                )
            ],
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.parties by calling PartyIdentifiers"""
        map_client_from_list_item_params_clearing_org = MapFromListItemsParams(
            source_attribute_for_pattern_search=SourceColumns.PARTY_ROLE,
            source_attribute_for_mapping=SourceColumns.PARTY_ID,
            regex_pattern="^21$",
            target_attribute=TempColumns.CLEARING_ORG,
        )
        clearing_org = MapConditionalAttributeFromListItems.process(
            source_frame=self.source_frame,
            params=map_client_from_list_item_params_clearing_org,
        )
        multiple_client_map = clearing_org[TempColumns.CLEARING_ORG].str.match(
            r"\[.*\]", na=False
        )
        clearing_org.loc[
            multiple_client_map, TempColumns.CLEARING_ORG
        ] = clearing_org.loc[multiple_client_map, TempColumns.CLEARING_ORG].str.get(0)
        clearing_org = PartyPrefix.ID + clearing_org

        map_client_from_list_item_params_account = MapFromListItemsParams(
            source_attribute_for_pattern_search=SourceColumns.PARTY_ROLE,
            source_attribute_for_mapping=SourceColumns.PARTY_ID,
            regex_pattern="^24$",
            target_attribute=TempColumns.ACCOUNT,
        )
        accounts = MapConditionalAttributeFromListItems.process(
            source_frame=self.source_frame,
            params=map_client_from_list_item_params_account,
        )
        multiple_counterparties_map = accounts[TempColumns.ACCOUNT].str.match(
            r"\[.*\]", na=False
        )
        accounts.loc[multiple_counterparties_map, TempColumns.ACCOUNT] = accounts.loc[
            multiple_counterparties_map, TempColumns.ACCOUNT
        ].str.get(0)
        accounts = PartyPrefix.ID + accounts

        parties_source_frame = pd.concat(
            [
                self.pre_process_df.loc[
                    :,
                    [
                        TempColumns.TRADER,
                    ],
                ],
                clearing_org,
                accounts,
                self.target_df.loc[
                    :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
                ],
            ],
            axis=1,
        )

        return GenericOrderPartyIdentifiers.process(
            source_frame=parties_source_frame,
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                trader_identifier=TempColumns.TRADER,
                client_identifier=TempColumns.ACCOUNT,
                counterparty_identifier=TempColumns.CLEARING_ORG,
                buyer_identifier=TempColumns.ACCOUNT,
                seller_identifier=TempColumns.CLEARING_ORG,
                buy_sell_side_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                use_buy_mask_for_buyer_seller=True,
            ),
        )
