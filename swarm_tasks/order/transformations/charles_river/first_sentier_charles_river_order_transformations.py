from swarm_tasks.order.transformations.charles_river.charles_river_order_transformations import (
    CharlesRiverOrderTransformations,
)


SYDNEY_TIMEZONE = "Australia/Sydney"


class FirstSentierCharlesRiverOrderTransformations(CharlesRiverOrderTransformations):
    """
    This transformation is specific for First Sentier
    It overrides all timestamps to use Sydney time (AEST or AEDT)
    """

    @staticmethod
    def _set_timezone_info() -> str:
        """
        Sets the timezone info for the ConvertDateTime tasks
        This is a tenant specific parameter, hence these Tenant specific transformations
        """
        return SYDNEY_TIMEZONE
