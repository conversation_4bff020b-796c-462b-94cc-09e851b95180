import logging
import os
import re
from typing import List
from typing import NoReturn

import numpy as np
import pandas as pd
from prefect.engine import signals
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_attribute import CastTo
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.mifid2 import BestExAssetClassMain
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import HierarchyEnum
from se_elastic_schema.static.mifid2 import OptionType
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import ShortSellingIndicator
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_elastic_schema.static.mifid2 import Validity<PERSON>eriod
from se_elastic_schema.static.reference import OrderRecordType
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order.static import OrderType
from se_trades_tasks.order_and_tr.static import AssetClass
from se_trades_tasks.order_and_tr.static import PartyPrefix
from swarm.conf import Settings

from swarm_tasks.cloud.aws.s3.utils import read_csv_from_s3_download
from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.order.feed.charles_river.static import BROKER_CODE_TO_MIC_S3_FILE_KEY
from swarm_tasks.order.feed.charles_river.static import BrokerCodeLookupColumns
from swarm_tasks.order.feed.charles_river.static import FileTypes
from swarm_tasks.order.feed.charles_river.static import ID_LEI_REGEX
from swarm_tasks.order.feed.charles_river.static import SourceColumns
from swarm_tasks.order.feed.charles_river.static import TempColumns
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.generic.get_tenant_lei import Params as ParamsGetTenantLEI
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_static import MapStatic
from swarm_tasks.transform.map.map_static import Params as ParamsMapStatic
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)
from swarm_tasks.utilities.market.instruments.instrument_utils import (
    get_expiry_date_from_desc,
)
from swarm_tasks.utilities.static import Delimiters
from swarm_tasks.utilities.task_utils import match_enum_value

logger = logging.getLogger(__name__)


class CharlesRiverOrderTransformations(AbstractOrderTransformations):
    """Transformations class for Charles River. 4 types of files
    are handled:  Allocations, Order, Place and Fills.
    Fields are created for Order and OrderState models.

    Specs: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2450587649/Order+Charles+River
    """

    def __init__(self, source_frame: pd.DataFrame, auditor, **kwargs):
        super().__init__(source_frame=source_frame, auditor=auditor)
        # File type already populated upstream and is same value all across
        self.file_type = match_enum_value(
            source_frame[SourceColumns.FILE_TYPE].iloc[0], FileTypes
        )
        # Charles River Transformations allow for the inclusion of a timezone to convert the
        #   datetime used. Please make sure all ConvertDateTimeTasks use this variable as
        #   timezone_info param
        self.timezone_info = self._set_timezone_info()
        if not self.file_type:
            raise signals.FAIL(
                message="This flow cannot handle this file type. It can only handle"
                " Allocations files (which have 'allocations' in the file name) and "
                "Non Allocations files (which have 'order', 'place', 'fills' in the file name)"
            )

    def _pre_process(self):
        """
        Temporary columns needed for multiple fields
        """
        # ON-3404 Skip row if [fillqty] or [fillprice] is NULL for FILLS
        invalid_fills_mask = (
            self.source_frame[SourceColumns.ORDER_STATUS_TRANSFORMED]
            == OrderStatus.PARF.value
        ) & (
            (self.source_frame[SourceColumns.FILL_QTY].isnull())
            | (self.source_frame[SourceColumns.FILL_PRICE].isnull())
        )

        self.source_frame = self.source_frame.loc[~invalid_fills_mask]
        self.pre_process_df = pd.DataFrame(index=self.source_frame.index)
        self.target_df = pd.DataFrame(index=self.source_frame.index)
        self.post_process_df = pd.DataFrame(index=self.source_frame.index)

        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_buy_sell(),
                self._temp_currency_code(),
                self._temp_from_currency_code(),
                self._temp_fill_create_date(),
                self._temp_fill_date(),
                self._temp_fill_price(),
                self._temp_instr_fallback_quantity_notation(),
                self._temp_instr_ids_asset_class(),
                self._temp_instr_ids_underlying_isin(),
                self._temp_newo_in_file(),
                self._temp_order_average_price(),
                self._temp_order_create_date(),
                self._temp_order_create_date_formatted(),
                self._temp_order_release_date_formatted(),
                self._temp_order_last_update_date(),
                self._temp_order_release_date(),
                self._temp_party_ids_client(),
                self._temp_party_ids_counterparty(),
                self._temp_party_ids_execution_within_firm(),
                self._temp_party_ids_investment_decision_maker(),
                self._temp_party_ids_trader(),
                self._temp_place_date(),
                self._temp_place_create_date(),
                self._temp_reference_order_id(),
                self._temp_security_name_currency_code(),
                self._temp_security_name_date_forward(),
                self._temp_ticker_date_eco(),
                self._temp_ticker_date_ico_ipo(),
                self._temp_to_currency_code(),
                self._temp_trade_date(),
                self._temp_trade_date_formatted(),
                self._temp_traded_quantity(),
            ],
            axis=1,
        )

        # these columns need access to others defined above
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_ultimate_venue(),  # needs asset class
                self._temp_buy_sell_indicator(),  # needs buy_sell
                self._temp_instr_ids_option_type(),  # needs asset class
                self._temp_instr_ids_options_strike_price(),  # needs asset class
                self._temp_instr_ids_underlying_symbol(),  # needs asset_class
                self._temp_order_and_trading_date_time(),  # needs different temp date-time columns
                self._temp_party_ids_executing_entity(),  # need temp_executing_entity
                self._temp_ticker_date_future(),  # needs trade_date
                self._temp_transaction_reference_number(),  # needs trade date formatted
            ],
            axis=1,
        )

        # these columns need access to others defined above
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_price_currency_code(),  # needs currency_code, from_currency_code, buySellIndicator
                self._temp_instr_fallback_option_type_mapped(),  # needs option_type
                self._temp_instr_ids_expiry_date(),  # needs asset class, and asset ticker dates
                self._temp_notional_currency_2(),  # needs buy_sell_indicator
                self._temp_party_ids_buyer(),  # needs executing_entity, buy_sell_indicator and counterparty
                self._temp_party_ids_seller(),  # needs executing_entity, buy_sell_indicator and counterparty
            ],
            axis=1,
        )

    def _post_process(self):
        inst_fallback_columns = [
            TempColumns.OPTION_TYPE_MAPPED,
            TempColumns.INSTR_QUANTITY_NOTATION,
        ]
        self.target_df = pd.concat(
            [
                self.target_df,
                self.source_frame.loc[:, SourceColumns.SECURITY_NAME],
                self.pre_process_df.loc[:, inst_fallback_columns],
                self.pre_process_df.loc[:, TempColumns.NEWO_IN_FILE],
            ],
            axis=1,
        )
        self.target_df = self.target_df.assign(
            **{TempColumns.IS_CREATED_THROUGH_FALLBACK: True}
        )

        # instrument fallback columns
        self.target_df.loc[
            :, TempColumns.INST_FB_BEST_EX_ASSET_CLASS_MAIN
        ] = self._temp_best_ex_asset_class_main()
        self.target_df.loc[
            :, TempColumns.INST_FB_BEST_EX_ASSET_CLASS_SUB
        ] = self._temp_best_ex_asset_class_sub()
        self.target_df.loc[:, TempColumns.INST_ID_CODE] = self.source_frame.loc[
            :, SourceColumns.ISIN
        ]
        temp_df = pd.concat(
            [
                self.source_frame.loc[
                    :, [SourceColumns.TICKER, SourceColumns.SECURITY_TYPE]
                ],
                self.target_df.loc[
                    :, [OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE]
                ],
            ],
            axis=1,
        )
        temp_df[TempColumns.TEMP_COL_1] = "crims"
        self.target_df.loc[:, TempColumns.INST_ALT_ID_CODE] = ConcatAttributes.process(
            source_frame=temp_df,
            params=ParamsConcatAttributes(
                source_attributes=[
                    TempColumns.TEMP_COL_1,
                    SourceColumns.TICKER,
                    SourceColumns.SECURITY_TYPE,
                    OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                ],
                delimiter=Delimiters.UNDERSCORE,
                target_attribute=TempColumns.INST_ALT_ID_CODE,
            ),
        )

        # parties fallback columns
        self.target_df.loc[
            :, TempColumns.PARTIES_FB_EXEC_ENTIY
        ] = self.pre_process_df.loc[:, TempColumns.EXECUTING_ENTITY].str.replace(
            ID_LEI_REGEX, "", regex=True
        )
        self.target_df.loc[:, TempColumns.PARTIES_FB_TRADER] = self.source_frame.loc[
            :, SourceColumns.ORDER_TRADER_ID
        ]
        self.target_df.loc[
            :, TempColumns.PARTIES_FB_EXEC_WITHIN_FIRM
        ] = self.pre_process_df.loc[:, TempColumns.EXECUTION_WITHIN_FIRM].str.replace(
            ID_LEI_REGEX, "", regex=True
        )
        self.target_df.loc[
            :, TempColumns.PARTIES_FB_INV_DEC_MAKER
        ] = self.pre_process_df.loc[
            :, TempColumns.INVESTMENT_DECISION_MAKER
        ].str.replace(
            ID_LEI_REGEX, "", regex=True
        )

        if self.file_type == FileTypes.ALLOCATIONS:
            self.target_df.loc[
                :, TempColumns.PARTIES_FB_CLIENT
            ] = self.pre_process_df.loc[:, TempColumns.CLIENT].str.replace(
                ID_LEI_REGEX, "", regex=True
            )
        else:
            self.target_df.loc[
                :, TempColumns.PARTIES_FB_CLIENT
            ] = self.pre_process_df.loc[:, TempColumns.CLIENT].apply(
                lambda lst: CharlesRiverOrderTransformations._remove_id_lei_from_list(
                    lst=lst
                )
            )

        self.target_df.loc[
            :, TempColumns.PARTIES_FB_COUNTERPARTY
        ] = self.pre_process_df.loc[:, TempColumns.COUNTERPARTY].str[3:]
        self.target_df.loc[:, TempColumns.PARTIES_FB_BUYER] = self.pre_process_df.loc[
            :, TempColumns.BUYER
        ].str.replace(ID_LEI_REGEX, "", regex=True)
        self.target_df.loc[:, TempColumns.PARTIES_FB_SELLER] = self.pre_process_df.loc[
            :, TempColumns.SELLER
        ].str.replace(ID_LEI_REGEX, "", regex=True)

        # Columns needed to populate bestEx native downstream
        fill_amount_column = (
            SourceColumns.ORDER_NET_AMOUNT
            if self.file_type == FileTypes.ALLOCATIONS
            else SourceColumns.FILL_AMOUNT
        )
        self.target_df.loc[:, TempColumns.FILL_AMOUNT] = self.source_frame.loc[
            :, fill_amount_column
        ]
        self.target_df.loc[:, TempColumns.ORDER_NET_AMOUNT] = self.source_frame.loc[
            :, SourceColumns.ORDER_NET_AMOUNT
        ]

    @staticmethod
    def _remove_id_lei_from_list(lst: List[str]) -> List[str]:
        """
        1. Removes the id/lei using regex from each client.
        2. Removes duplicates preserving the original list order.

        :param lst: the to be processed
        :return: the cleaned list without duplicates or an empty list if input is invalid
        """
        if isinstance(lst, list):
            list_without_id_lei: List[str] = [
                re.sub(pattern=ID_LEI_REGEX, repl="", string=client) for client in lst
            ]

            return list(dict([(value, 1) for value in list_without_id_lei]).keys())
        return []

    def _buy_sell(self) -> pd.DataFrame:
        """
        Populates from TempColumns.BUY_SELL
        Adds '.order' and '.orderState' prefixes
        """
        return pd.concat(
            [
                MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.BUY_SELL,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.BUY_SELL,
                        ),
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.BUY_SELL,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.BUY_SELL,
                        ),
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        """
        Static value: Charles River
        """
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_value="Charles River",
                target_attribute=OrderColumns.DATA_SOURCE_NAME,
            ),
        )

    def _date(self) -> pd.DataFrame:
        """
        Populates from TempColumns.TRADE_DATE if allocation file
        else populates from TempColumns.ORDER_CREATE_DATE_FORMATTED
        or TempColumns.ORDER_RELEASE_DATE_FORMATTED
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapAttribute.process(
                source_frame=self.pre_process_df,
                params=ParamsMapAttribute(
                    source_attribute=TempColumns.TRADE_DATE_FORMATTED,
                    target_attribute=OrderColumns.DATE,
                ),
                auditor=self.auditor,
            )
        else:
            return MapConditional.process(
                source_frame=self.pre_process_df,
                params=ParamsMapConditional(
                    target_attribute=OrderColumns.DATE,
                    cases=[
                        Case(
                            query=f"`{TempColumns.ORDER_CREATE_DATE_FORMATTED}`.notnull()",
                            attribute=TempColumns.ORDER_CREATE_DATE_FORMATTED,
                        ),
                        Case(
                            query=f"`{TempColumns.ORDER_CREATE_DATE_FORMATTED}`.isnull()",
                            attribute=TempColumns.ORDER_RELEASE_DATE_FORMATTED,
                        ),
                    ],
                ),
            )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        """
        Populates from OrderColumns.EXECUTION_DETAILS_AGGREGATED_ORDER
        from SourceColumns.ORDER_STATUS
        """
        if self.file_type != FileTypes.ALLOCATIONS:
            return MapConditional.process(
                source_frame=self.source_frame,
                params=ParamsMapConditional(
                    target_attribute=OrderColumns.EXECUTION_DETAILS_AGGREGATED_ORDER,
                    cases=[
                        Case(
                            query=f"`{SourceColumns.ORDER_STATUS}`.str.fullmatch('MERGED', case=False, na=False)",
                            value=True,
                        ),
                        Case(
                            query=f"~(`{SourceColumns.ORDER_STATUS}`.str.fullmatch('MERGED', case=False, na=False))",
                            value=False,
                        ),
                    ],
                ),
            )

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Populates from TempColumns.BUY_SELL_INDICATOR temporary column
        Contains data from SourceColumns.SIDE
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.BUY_SELL_INDICATOR,
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
            ),
            auditor=self.auditor,
        )

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        pass

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.MANAGER_LIMIT
        """
        if self.file_type != FileTypes.ALLOCATIONS:
            return MapConditional.process(
                source_frame=self.source_frame,
                params=ParamsMapConditional(
                    target_attribute=OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE,
                    cases=[
                        Case(
                            query=f"`{SourceColumns.ORDER_INSTRUCTION}`.str.upper().str.startswith('L', na=False)",
                            attribute=SourceColumns.MANAGER_LIMIT,
                        ),
                        Case(
                            query=f"`{SourceColumns.ORDER_INSTRUCTION}`.str.upper().str.startswith('P', na=False)",
                            attribute=SourceColumns.MANAGER_LIMIT,
                        ),
                    ],
                ),
            )

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        pass

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        pass

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        pass

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        pass

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        pass

    def _execution_details_order_status(self) -> pd.DataFrame:
        """
        Populates with "NEWO" or "FILL" for Allocations and Non Allocation flow
        Adds '.order' and '.orderState' prefixes
        """
        return pd.concat(
            [
                MapConditional.process(
                    source_frame=self.source_frame,
                    params=ParamsMapConditional(
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        ),
                        cases=[
                            Case(
                                query=f"`{SourceColumns.ORDER_STATUS_TRANSFORMED}` != '{OrderStatus.NEWO.value}'",
                                attribute=SourceColumns.ORDER_STATUS_TRANSFORMED,
                            ),
                        ],
                    ),
                ),
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=ParamsMapStatic(
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        ),
                        target_value=OrderStatus.NEWO.value,
                    ),
                ),
            ],
            axis=1,
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.ORDER_INSTRUCTION in cases of
        Orders flow else maps to static value Allocation
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapStatic.process(
                source_frame=self.source_frame,
                params=ParamsMapStatic(
                    target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
                    target_value="Market",
                ),
            )
        else:
            return MapValue.process(
                source_frame=self.source_frame,
                params=ParamsMapValue(
                    source_attribute=SourceColumns.ORDER_INSTRUCTION,
                    target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
                    case_insensitive=True,
                    value_map={
                        "*HOLD": "Hold",
                        "FRX": "Forex",
                        "PEGFIXED": "Fixed Peg",
                        "FUN": "FUNARI",
                        "FXFIX": "FX Fixing Time",
                        "PEGLAST": "Last Peg",
                        "LIM": "Limit",
                        "LOB": "Limit or Better",
                        "PEGMID": "Mid-price Peg",
                        "LOC": "Limit on Close",
                        "LWW": "Limit With or Without",
                        "PEGOPEN": "Opening Peg",
                        "MC": "Market on Close",
                        "MKT": "Market",
                        "PEGPRIME": "Primary Peg",
                        "MO": "Market on Open",
                        "OB": "On Basis",
                        "PEGVWAP": "Peg to VWAP",
                        "OC": "On Close",
                        "PEG": "Pegged",
                        "PI": "Previously Indicated",
                        "PQ": "Previously Quoted",
                        "STL": "Stop Limit",
                        "STP": "Stop Loss",
                        "VWAP": "Volume Weighted Average Price",
                        "WOW": "With or Without",
                    },
                ),
                auditor=self.auditor,
            ).fillna(OrderType.MARKET)

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO
        It will concatenate the values, separated by a comma (',')
        """
        source_file_df = MapAttribute.process(
            source_frame=self.source_frame.loc[:, [SourceColumns.SOURCE_FILE]],
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.SOURCE_FILE,
                target_attribute=TempColumns.TEMP_COL_1,
                prefix="Source file : ",
            ),
            auditor=self.auditor,
        )
        if self.file_type == FileTypes.ALLOCATIONS:
            filter_cols = [
                SourceColumns.ORIGINAL_ORDER_ID,
                SourceColumns.ALLO_BROKER_REASON,
                SourceColumns.ALLO_CLEARING_BROKER,
                SourceColumns.ALLO_DIRECTED_BROKER,
                SourceColumns.ALLO_DIRECTED_BROKER_TYPE,
            ]
            temp_df = self.source_frame.loc[:, filter_cols]
            temp_df = pd.concat(
                [
                    MapAttribute.process(
                        source_frame=temp_df,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.ORIGINAL_ORDER_ID,
                            target_attribute=TempColumns.ORIGINAL_ORDER_ID,
                            prefix="Original Order Id: ",
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=temp_df,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.ALLO_BROKER_REASON,
                            target_attribute=TempColumns.ALLO_BROKER_REASON,
                            prefix="Broker Reason: ",
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=temp_df,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.ALLO_CLEARING_BROKER,
                            target_attribute=TempColumns.ALLO_CLEARING_BROKER,
                            prefix="Clearing Broker: ",
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=temp_df,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.ALLO_DIRECTED_BROKER,
                            target_attribute=TempColumns.ALLO_DIRECTED_BROKER,
                            prefix="Directed Broker: ",
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=temp_df,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.ALLO_DIRECTED_BROKER_TYPE,
                            target_attribute=TempColumns.ALLO_DIRECTED_BROKER_TYPE,
                            prefix="Directed Broker Type: ",
                        ),
                        auditor=self.auditor,
                    ),
                    source_file_df,
                ],
                axis=1,
            )
            return ConcatAttributes.process(
                source_frame=temp_df,
                params=ParamsConcatAttributes(
                    source_attributes=[
                        TempColumns.ORIGINAL_ORDER_ID,
                        TempColumns.ALLO_BROKER_REASON,
                        TempColumns.ALLO_CLEARING_BROKER,
                        TempColumns.ALLO_DIRECTED_BROKER,
                        TempColumns.ALLO_DIRECTED_BROKER_TYPE,
                        TempColumns.TEMP_COL_1,
                    ],
                    delimiter=Delimiters.COMMA_SPACE,
                    target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                ),
            )
        else:
            filter_cols = [
                SourceColumns.IPO,
                SourceColumns.PLACE_REASON,
                SourceColumns.ORDER_REASON,
                SourceColumns.TIME_ZONE_REGION_NAME,
                SourceColumns.CRD_INTERNAL_SECURITY_ID,
                SourceColumns.ALGO_STRATEGY,
                SourceColumns.ORDER_STATUS,
            ]
            temp_df = self.source_frame.loc[:, filter_cols]
            temp_df[SourceColumns.IPO] = np.where(
                temp_df[SourceColumns.IPO].fillna("N") == "Y", "True", pd.NA
            )
            temp_df = pd.concat(
                [
                    MapAttribute.process(
                        source_frame=temp_df,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.IPO,
                            target_attribute=TempColumns.IPO,
                            prefix="IPO: ",
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=temp_df,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.PLACE_REASON,
                            target_attribute=TempColumns.PLACE_REASON,
                            prefix="Place Reason: ",
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=temp_df,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.ORDER_REASON,
                            target_attribute=TempColumns.ORDER_REASON,
                            prefix="Order Reason: ",
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=temp_df,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.TIME_ZONE_REGION_NAME,
                            target_attribute=TempColumns.TIME_ZONE_REASON_NAME,
                            prefix="Region: ",
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=temp_df,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.CRD_INTERNAL_SECURITY_ID,
                            target_attribute=TempColumns.CRD_INTERNAL_SECURITY_ID,
                            prefix="CRD Security Id: ",
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=temp_df,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.ALGO_STRATEGY,
                            target_attribute=TempColumns.ALGO_STRATEGY,
                            prefix="Algo: ",
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=temp_df,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.ORDER_STATUS,
                            target_attribute=TempColumns.PREFIXED_ORDER_STATUS,
                            prefix="Order Status: ",
                        ),
                        auditor=self.auditor,
                    ),
                    source_file_df,
                ],
                axis=1,
            )
            return ConcatAttributes.process(
                source_frame=temp_df,
                params=ParamsConcatAttributes(
                    source_attributes=[
                        TempColumns.IPO,
                        TempColumns.PLACE_REASON,
                        TempColumns.ORDER_REASON,
                        TempColumns.TIME_ZONE_REASON_NAME,
                        TempColumns.CRD_INTERNAL_SECURITY_ID,
                        TempColumns.ALGO_STRATEGY,
                        TempColumns.PREFIXED_ORDER_STATUS,
                        TempColumns.TEMP_COL_1,
                    ],
                    delimiter=Delimiters.COMMA_SPACE,
                    target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                ),
            )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_PASSIVE_AGGRESSIVE_INDICATOR
        from SourceColumns.NET_TRADE_IND
        """
        if self.file_type != FileTypes.ALLOCATIONS:
            return MapValue.process(
                source_frame=self.source_frame,
                params=ParamsMapValue(
                    source_attribute=SourceColumns.NET_TRADE_IND,
                    target_attribute=OrderColumns.EXECUTION_DETAILS_PASSIVE_AGGRESSIVE_INDICATOR,
                    case_insensitive=True,
                    value_map={
                        "A": "AGRE",
                        "P": "PASV",
                    },
                ),
                auditor=self.auditor,
            )
        else:
            return MapValue.process(
                source_frame=self.source_frame,
                params=ParamsMapValue(
                    source_attribute=SourceColumns.QUANT_IND,
                    target_attribute=OrderColumns.EXECUTION_DETAILS_PASSIVE_AGGRESSIVE_INDICATOR,
                    case_insensitive=True,
                    value_map={
                        "Y": "AGRE",
                        "N": "PASV",
                    },
                    default_value=pd.NA,
                ),
                auditor=self.auditor,
            )

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        """
        Populates OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY
        from SourceColumns.SPECIAL_INST
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.SPECIAL_INST,
                target_attribute=OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY,
            ),
            auditor=self.auditor,
        )

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        pass

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_stop_price(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.ORDER_STOP_PRICE
        """
        if self.file_type != FileTypes.ALLOCATIONS:
            return MapConditional.process(
                source_frame=self.source_frame,
                params=ParamsMapConditional(
                    target_attribute=OrderColumns.EXECUTION_DETAILS_STOP_PRICE,
                    cases=[
                        Case(
                            query=f"`{SourceColumns.ORDER_INSTRUCTION}`.str.upper().str.startswith('S', na=False)",
                            attribute=SourceColumns.STOP_PRICE,
                        )
                    ],
                ),
            )

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populates EXECUTION_DETAILS_TRADING_CAPACITY with static value 'AOTC'
        """
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_value=TradingCapacity.AOTC.value,
                target_attribute=OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY,
            ),
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.TIME_IN_FORCE for orders file
        """
        if self.file_type != FileTypes.ALLOCATIONS:
            temp_df = MapValue.process(
                source_frame=self.source_frame,
                params=ParamsMapValue(
                    source_attribute=SourceColumns.TIME_IN_FORCE,
                    target_attribute=TempColumns.EXECUTION_DETAILS_VALIDITY_PERIOD,
                    case_insensitive=True,
                    value_map={
                        "D": ValidityPeriod.DAVY.value,
                        "GTC": ValidityPeriod.GTCV.value,
                        "OPG": ValidityPeriod.OPGV.value,
                        "OC": ValidityPeriod.IOCV.value,
                        "FOK": ValidityPeriod.FOKV.value,
                        "GTX": ValidityPeriod.GTXV.value,
                        "GTD": ValidityPeriod.GTDV.value,
                        "CLO": ValidityPeriod.CLOV.value,
                    },
                ),
                auditor=self.auditor,
            )

            return MapAttribute.process(
                source_frame=temp_df,
                params=ParamsMapAttribute(
                    source_attribute=TempColumns.EXECUTION_DETAILS_VALIDITY_PERIOD,
                    target_attribute=OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD,
                    cast_to=CastTo.STRING_LIST.value,
                    list_delimiter=Delimiters.SEMI_COLON,
                ),
                auditor=self.auditor,
            )

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _financing_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _hierarchy(self) -> pd.DataFrame:
        """
        Populates with "Standalone"
        """
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_value=HierarchyEnum.STANDALONE.value,
                target_attribute=OrderColumns.HIERARCHY,
            ),
        )

    def _id(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.ORDER_ID or SourceColumns.ACCOUNT_ID with pattern
        ('A|' + allocation.[ACCOUNT_ID] + '|' + [ORDER_ID])
        based on file type and record type incase it's an allocation file.
        Adds '.order' and '.orderState' prefixes
        """
        if self.file_type != FileTypes.ALLOCATIONS:
            temp_df = self.source_frame.loc[
                :, [SourceColumns.ORDER_STATUS, SourceColumns.ORDER_ID]
            ]
            temp_df = temp_df.assign(
                **{
                    TempColumns.TEMP_PREFIX: "M|",
                }
            )
            temp_df = pd.concat(
                [
                    temp_df,
                    ConcatAttributes.process(
                        source_frame=temp_df,
                        params=ParamsConcatAttributes(
                            target_attribute=TempColumns.PREFIXED_ORDER_ID_CODE,
                            source_attributes=[
                                TempColumns.TEMP_PREFIX,
                                SourceColumns.ORDER_ID,
                            ],
                        ),
                    ),
                ],
                axis=1,
            )
            temp_df = MapConditional.process(
                source_frame=temp_df,
                params=ParamsMapConditional(
                    target_attribute=TempColumns.ORDER_ID_CODE,
                    cases=[
                        Case(
                            query=f"`{SourceColumns.ORDER_STATUS}`.str.fullmatch('MERGED', case=False, na=False)",
                            attribute=TempColumns.PREFIXED_ORDER_ID_CODE,
                        ),
                        Case(
                            query=f"~(`{SourceColumns.ORDER_STATUS}`.str.fullmatch('MERGED', case=False, na=False))",
                            attribute=SourceColumns.ORDER_ID,
                        ),
                    ],
                ),
            )
            return pd.concat(
                [
                    MapAttribute.process(
                        source_frame=temp_df,
                        params=ParamsMapAttribute(
                            source_attribute=TempColumns.ORDER_ID_CODE,
                            target_attribute=add_prefix(
                                prefix=ModelPrefix.ORDER,
                                attribute=OrderColumns.ID,
                            ),
                        ),
                    ),
                    MapAttribute.process(
                        source_frame=temp_df,
                        params=ParamsMapAttribute(
                            source_attribute=TempColumns.ORDER_ID_CODE,
                            target_attribute=add_prefix(
                                prefix=ModelPrefix.ORDER_STATE,
                                attribute=OrderColumns.ID,
                            ),
                        ),
                    ),
                ],
                axis=1,
            )
        else:
            temp_df = self.source_frame.loc[
                :, [SourceColumns.ORDER_ID, SourceColumns.ACCOUNT_ID]
            ]
            temp_df = temp_df.assign(
                **{
                    TempColumns.TEMP_PREFIX: "A|",
                    TempColumns.DELIMITER: Delimiters.PIPE,
                }
            )
            temp_df = ConcatAttributes.process(
                source_frame=temp_df,
                params=ParamsConcatAttributes(
                    target_attribute=TempColumns.TEMP_ID,
                    source_attributes=[
                        TempColumns.TEMP_PREFIX,
                        SourceColumns.ACCOUNT_ID,
                        TempColumns.DELIMITER,
                        SourceColumns.ORDER_ID,
                    ],
                ),
            )
            return pd.concat(
                [
                    MapAttribute.process(
                        source_frame=temp_df,
                        params=ParamsMapAttribute(
                            source_attribute=TempColumns.TEMP_ID,
                            target_attribute=add_prefix(
                                prefix=ModelPrefix.ORDER,
                                attribute=OrderColumns.ID,
                            ),
                        ),
                    ),
                    MapAttribute.process(
                        source_frame=temp_df,
                        params=ParamsMapAttribute(
                            source_attribute=TempColumns.TEMP_ID,
                            target_attribute=add_prefix(
                                prefix=ModelPrefix.ORDER_STATE,
                                attribute=OrderColumns.ID,
                            ),
                        ),
                    ),
                ],
                axis=1,
            )

    def _is_discretionary(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_iceberg(self):
        """Not Implemented"""

    def _is_repo(self):
        """Not Implemented"""

    def _is_synthetic(self):
        """Not Implemented"""

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        """Not Implemented"""

    def _market_identifiers(self) -> pd.DataFrame:
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """
        Populates from the following temporary columns:
            TempColumns.ASSET_CLASS
            TempColumns.INSTR_IDS_CURRENCY
            TempColumns.EXPIRY_DATE
            TempColumns.ISIN
            TempColumns.NOTIONAL_CURRENCY_2
            TempColumns.OPTION_STRIKE_PRICE
            TempColumns.OPTION_TYPE
            TempColumns.UNDERLYING_ISIN
            TempColumns.UNDERLYING_SYMBOL
            OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE
        """
        return InstrumentIdentifiers.process(
            source_frame=pd.concat(
                [
                    self.pre_process_df,
                    self.target_df.loc[
                        :, [OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE]
                    ],
                    self.source_frame.loc[:, [SourceColumns.ISIN]],
                ],
                axis=1,
            ),
            params=ParamsInstrumentIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                asset_class_attribute=TempColumns.ASSET_CLASS,
                currency_attribute=TempColumns.PRICE_CURRENCY,
                expiry_date_attribute=TempColumns.EXPIRY_DATE,
                isin_attribute=SourceColumns.ISIN,
                notional_currency_2_attribute=TempColumns.NOTIONAL_CURRENCY_2,
                option_strike_price_attribute=TempColumns.OPTION_STRIKE_PRICE,
                option_type_attribute=TempColumns.OPTION_TYPE,
                underlying_isin_attribute=TempColumns.UNDERLYING_ISIN,
                underlying_symbol_attribute=TempColumns.UNDERLYING_SYMBOL,
                venue_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                retain_task_inputs=True,
            ),
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """
        Populates from the following temporary columns:
            TempColumns.CLIENT
            TempColumns.EXECUTING_ENTITY
            TempColumns.TRADER
            TempColumns.EXECUTION_WITHIN_FIRM
            TempColumns.INVESTMENT_DECISION_MAKER
            TempColumns.COUNTERPARTY
            TempColumns.BUYER
            TempColumns.SELLER
        """
        return GenericOrderPartyIdentifiers.process(
            source_frame=pd.concat(
                [
                    self.pre_process_df,
                    self.target_df.loc[
                        :, [OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR]
                    ],
                ],
                axis=1,
            ),
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=TempColumns.EXECUTING_ENTITY,
                trader_identifier=TempColumns.TRADER,
                execution_within_firm_identifier=TempColumns.EXECUTION_WITHIN_FIRM,
                investment_decision_within_firm_identifier=TempColumns.INVESTMENT_DECISION_MAKER,
                client_identifier=TempColumns.CLIENT,
                counterparty_identifier=TempColumns.COUNTERPARTY,
                buyer_identifier=TempColumns.BUYER,
                seller_identifier=TempColumns.SELLER,
                buy_sell_side_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                use_buy_mask_for_buyer_seller=True,
                use_buy_mask_for_buyer_seller_decision_maker=True,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """
        Populates with Orders and OrderState values for OrderColumns.META_MODEL,
        """
        return pd.concat(
            [
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=ParamsMapStatic(
                        target_value="Order",
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.META_MODEL,
                        ),
                    ),
                ),
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=ParamsMapStatic(
                        target_value="OrderState",
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.META_MODEL,
                        ),
                    ),
                ),
            ],
            axis=1,
        )

    def _order_class(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        """
        Populates from TempColumns.REFERENCE_ORDER_ID.
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.REFERENCE_ORDER_ID,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID,
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        pass

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.PLACE_FIX_CLIENT_ORDER_ID,
        only for non Allocation flow
        """
        if self.file_type != FileTypes.ALLOCATIONS:
            id_column = SourceColumns.PLACE_FIX_CLIENT_ORDER_ID
        else:
            id_column = SourceColumns.ORDER_ID

        return (
            MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=id_column,
                    target_attribute=OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE,
                    cast_to=CastTo.INTEGER,
                ),
                auditor=self.auditor,
            )
            .astype("Int64")
            .fillna(pd.NA)
        )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """
        Populates from OrderColumns.ID
        """
        id_column = add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID)

        return MapAttribute.process(
            source_frame=pd.concat(
                [self.source_frame, self.target_df.loc[:, id_column]],
                axis=1,
            ),
            params=ParamsMapAttribute(
                source_attribute=id_column,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
            ),
        )

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        """
        Populates from TempColumns.REFERENCE_ORDER_ID.
        In order flow this column is merged from allocations file.
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.REFERENCE_ORDER_ID,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_PARENT_ORDER_ID,
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.PLACE_ID
        """
        if self.file_type != FileTypes.ALLOCATIONS:
            return (
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=SourceColumns.PLACE_ID,
                        target_attribute=OrderColumns.ORDER_IDENTIFIERS_TRADING_VENUE_TRANSACTION_ID_CODE,
                        cast_to=CastTo.INTEGER,
                    ),
                    auditor=self.auditor,
                )
                .astype("Int64")
                .fillna(pd.NA)
            )

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """
        Populates from TempColumns.TRANSACTION_REFERENCE_NUMBER temporary column
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.TRANSACTION_REFERENCE_NUMBER,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
            ),
            auditor=self.auditor,
        )

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.ORDER_TARGET_QUANTITY
        For Orders agg data is taken for each orderID, for allocation mapped as is.
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.ORDER_TARGET_QUANTITY,
                    target_attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                ),
                auditor=self.auditor,
            )
        else:
            return MapConditional.process(
                source_frame=self.source_frame,
                params=ParamsMapConditional(
                    target_attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                    cases=[
                        Case(
                            query=f"`{SourceColumns.ORDER_TARGET_QUANTITY}`.notnull() & "
                            f"`{SourceColumns.ORDER_TARGET_QUANTITY}` != 0",
                            attribute=SourceColumns.ORDER_TARGET_QUANTITY,
                        ),
                        Case(
                            query=f"`{SourceColumns.ORDER_TARGET_QUANTITY}`.isnull() | "
                            f"`{SourceColumns.ORDER_TARGET_QUANTITY}` == 0",
                            attribute=SourceColumns.ORIGINAL_ORDER_TARGET_QTY,
                        ),
                    ],
                ),
            )

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price(self) -> pd.DataFrame:
        """
        Populates from temporary column TempColumns.ORDER_AVERAGE_PRICE for Allocations
        and TempColumns.FILL_PRICE for Orders
        Contains data from SourceColumns.ORDER_AVERAGE_PRICE or SourceColumns.FILL_PRICE
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapAttribute.process(
                source_frame=self.pre_process_df,
                params=ParamsMapAttribute(
                    source_attribute=TempColumns.ORDER_AVERAGE_PRICE,
                    target_attribute=add_prefix(
                        prefix=ModelPrefix.ORDER_STATE,
                        attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                    ),
                ),
                auditor=self.auditor,
            )
        else:
            return MapAttribute.process(
                source_frame=self.pre_process_df,
                params=ParamsMapAttribute(
                    source_attribute=TempColumns.FILL_PRICE,
                    target_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                ),
                auditor=self.auditor,
            )

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        pass

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """
        Populates from TempColumns.TRADED_QUANTITY temporary column
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.TRADED_QUANTITY,
                target_attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
            ),
            auditor=self.auditor,
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """
        Populates from TempColumns.TRANSACTION_REFERENCE_NUMBER temporary column
        contains data from SourceColumns.EXEC_ID or
            SourceColumns.TRANSACTION_REFERENCE_NUMBER and SourceColumns.ORDER_DATE
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.TRANSACTION_REFERENCE_NUMBER,
                target_attribute=OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO,
            ),
            auditor=self.auditor,
        )

    def _source_index(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceIndex column"""
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                from_index=True, target_attribute=OrderColumns.SOURCE_INDEX
            ),
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceKey column from the SWARM_FILE_URL"""
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_value=os.getenv("SWARM_FILE_URL"),
                target_attribute=OrderColumns.SOURCE_KEY,
            ),
        )

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        """
        Not implemented
        """

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        """
        Not implemented
        """

    def _timestamps_order_received(self) -> pd.DataFrame:
        """
        Populates from TempColumns.TRADE_DATE if allocation file
        else populates from TempColumns.ORDER_CREATE_DATE or TempColumns.ORDER_RELEASE_DATE
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapAttribute.process(
                source_frame=self.pre_process_df,
                params=ParamsMapAttribute(
                    source_attribute=TempColumns.TRADE_DATE,
                    target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                ),
                auditor=self.auditor,
            )
        else:
            return MapConditional.process(
                source_frame=self.pre_process_df,
                params=ParamsMapConditional(
                    target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                    cases=[
                        Case(
                            query=f"`{TempColumns.ORDER_CREATE_DATE}`.notnull()",
                            attribute=TempColumns.ORDER_CREATE_DATE,
                        ),
                        Case(
                            query=f"`{TempColumns.ORDER_CREATE_DATE}`.isnull()",
                            attribute=TempColumns.ORDER_RELEASE_DATE,
                        ),
                    ],
                ),
            )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """
        Populates from TempColumns.ORDER_TRADING_DATE_TIME
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.ORDER_TRADING_DATE_TIME,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
            ),
            auditor=self.auditor,
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """
        Populates from TempColumns.TRADE_DATE if allocation file
        else populates from TempColumns.ORDER_CREATE_DATE or TempColumns.ORDER_RELEASE_DATE
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapAttribute.process(
                source_frame=self.pre_process_df,
                params=ParamsMapAttribute(
                    source_attribute=TempColumns.TRADE_DATE,
                    target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                ),
                auditor=self.auditor,
            )
        else:
            return MapConditional.process(
                source_frame=self.pre_process_df,
                params=ParamsMapConditional(
                    target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                    cases=[
                        Case(
                            query=f"`{TempColumns.PLACE_DATE}`.notnull()",
                            attribute=TempColumns.PLACE_DATE,
                        ),
                        Case(
                            query=f"`{TempColumns.PLACE_DATE}`.isnull()",
                            attribute=TempColumns.PLACE_CREATE_DATE,
                        ),
                    ],
                ),
            )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """
        Populates from TempColumns.ORDER_TRADING_DATE_TIME
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.ORDER_TRADING_DATE_TIME,
                target_attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
            ),
            auditor=self.auditor,
        )

    def _timestamps_validity_period(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.SIDE column
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRADERS_ALGOS_WAIVER_INDICATORS_SHORT_SELLING_INDICATOR,
                cases=[
                    Case(
                        query=f"`{SourceColumns.SIDE}`.str.fullmatch('SELLS', case=False, na=False)",
                        value=ShortSellingIndicator.SESH.value,
                    )
                ],
            ),
        )

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_waiver_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        pass

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Populates from TempColumns.BUY_SELL_INDICATOR temporary column
        Contains data from SourceColumns.SIDE
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.BUY_SELL_INDICATOR,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        pass

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.PLACEMENT_ID
        """
        if self.file_type != FileTypes.ALLOCATIONS:
            return (
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=SourceColumns.PLACEMENT_ID,
                        target_attribute=OrderColumns.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID,
                        cast_to=CastTo.INTEGER,
                    ),
                    auditor=self.auditor,
                )
                .astype("Int64")
                .fillna(pd.NA)
            )

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        pass

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        pass

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        pass

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_price(self) -> pd.DataFrame:
        """
        Populates from TempColumns.FILL_PRICE temporary column for Orders
            and from TempColumns.ORDER_AVERAGE_PRICE for Allocations
        Contains data from SourceColumns.FILL_PRICE or SourceColumns.ORDER_AVERAGE_PRICE
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapAttribute.process(
                source_frame=self.pre_process_df,
                params=ParamsMapAttribute(
                    source_attribute=TempColumns.ORDER_AVERAGE_PRICE,
                    target_attribute=add_prefix(
                        prefix=ModelPrefix.ORDER_STATE,
                        attribute=OrderColumns.TRANSACTION_DETAILS_PRICE,
                    ),
                ),
                auditor=self.auditor,
            )
        else:
            return MapAttribute.process(
                source_frame=self.pre_process_df,
                params=ParamsMapAttribute(
                    source_attribute=TempColumns.FILL_PRICE,
                    target_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE,
                ),
                auditor=self.auditor,
            )

    def _transaction_details_price_average(self) -> pd.DataFrame:
        pass

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """
        Populates from TempColumns.PRICE_CURRENCY
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.PRICE_CURRENCY,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
            ),
            auditor=self.auditor,
        ).replace("CNH", "CNY")

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """
        Maps values "MONE" or "PERC" based on SourceColumns.SECURITY_TYPE
        and populates OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION,
                cases=[
                    Case(
                        query=f"`{SourceColumns.SECURITY_TYPE}`.isin(['ZERB', 'FRN', 'FIXB', 'CONB'])",
                        value=PriceNotation.PERC.value,
                    ),
                    Case(
                        query=f"~(`{SourceColumns.SECURITY_TYPE}`.isin(['ZERB', 'FRN', 'FIXB', 'CONB']))",
                        value=PriceNotation.MONE.value,
                    ),
                ],
            ),
        )

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        pass

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        pass

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """
        Populates from TempColumns.TRADED_QUANTITY temporary column
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.TRADED_QUANTITY,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.TO_CURRENCY_CODE, SourceColumns.FROM_CURRENCY_CODE or
        TempColumns.SECURITY_NAME_CURRENCY_CODE_2ND_FIELD
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.NOTIONAL_CURRENCY_2]
            .str.replace("CNH", "CNY")
            .values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY],
        )

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """
        Populates based on condition on SourceColumns.TO_CURRENCY_CODE
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
                cases=[
                    Case(
                        query=f"`{SourceColumns.TO_CURRENCY_CODE}`.isnull()",
                        value=QuantityNotation.UNIT.value,
                    ),
                    Case(
                        query=f"`{SourceColumns.TO_CURRENCY_CODE}`.notnull()",
                        value=QuantityNotation.MONE.value,
                    ),
                ],
            ),
        )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """
        Populates "Allocation" for Allocations else "Market"
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapStatic.process(
                source_frame=self.source_frame,
                params=ParamsMapStatic(
                    target_value=OrderRecordType.ALLOCATION,
                    target_attribute=OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE,
                ),
            )
        else:
            return MapStatic.process(
                source_frame=self.source_frame,
                params=ParamsMapStatic(
                    target_value=OrderRecordType.MARKET_SIDE,
                    target_attribute=OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE,
                ),
            )

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        pass

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        pass

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populates TRANSACTION_DETAILS_TRADING_CAPACITY with static value 'AOTC'
        """
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_value=TradingCapacity.AOTC.value,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY,
            ),
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """
        Populates from TempColumns.ORDER_TRADING_DATE_TIME
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.ORDER_TRADING_DATE_TIME,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """
        Populates from TempColumns.ULTIMATE_VENUE
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.ULTIMATE_VENUE,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        pass

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_venue(self) -> pd.DataFrame:
        """
        Always map `OrderColumns.TRANSACTION_DETAILS_VENUE` to the same value as
        `OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE`
        """
        return MapAttribute.process(
            source_frame=self.target_df,
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_VENUE,
            ),
            auditor=self.auditor,
        )

    # Temporary Fields
    def _temp_buy_sell(self) -> pd.DataFrame:
        """
        Temporary Column for:
            OrderColumns.BUY_SELL
            TempColumns.BUY_SELL_INDICATOR
        Populates from SourceColumns.SIDE
        following map:
            "BUYL": "1"
            "BUYS": "1
            "SELLL": "2"
            "SELLS": "2"
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.SIDE,
                target_attribute=TempColumns.BUY_SELL,
                case_insensitive=True,
                value_map={
                    "BUYL": "1",
                    "BUYS": "1",
                    "SELLL": "2",
                    "SELLS": "2",
                },
            ),
            auditor=self.auditor,
        )

    def _temp_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Temporary column for:
            OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
            OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
        Populates from TempColumns.BUY_SELL temporary column according to
        the map {"1": "BUYI", "2": "SELL"}
        Contains data from SourceColumns.TRANSACTION_TYPE for Allocations
        and SourceColumns.ORDER_SIDE for Executions
        """
        return MapValue.process(
            source_frame=self.pre_process_df,
            params=ParamsMapValue(
                source_attribute=TempColumns.BUY_SELL,
                target_attribute=TempColumns.BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "1": BuySellIndicator.BUYI.value,
                    "2": BuySellIndicator.SELL.value,
                },
            ),
            auditor=self.auditor,
        )

    def _temp_from_currency_code(self) -> pd.DataFrame:
        """
        Temporary column used for:
            OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY
            TempColumns.NOTIONAL_CURRENCY_2
        Populates from SourceColumns.FROM_CURRENCY_CODE
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.FROM_CURRENCY_CODE,
                target_ccy_attribute=TempColumns.FROM_CURRENCY_CODE,
            ),
        )

    def _temp_to_currency_code(self) -> pd.DataFrame:
        """
        Temporary column used for:
            OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY
            OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION
            TempColumns.NOTIONAL_CURRENCY_2
        Populates from SourceColumns.TO_CURRENCY_CODE
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.TO_CURRENCY_CODE,
                target_ccy_attribute=TempColumns.TO_CURRENCY_CODE,
            ),
        )

    def _temp_currency_code(self) -> pd.DataFrame:
        """
        Temporary column used for:
            OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
        Populates from SourceColumns.CURRENCY_CODE
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.CURRENCY_CODE,
                target_ccy_attribute=TempColumns.CURRENCY_CODE,
            ),
        )

    def _temp_security_name_currency_code(self) -> pd.DataFrame:
        """
        Temporary column used for:
            OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
            Instrument Identifiers 'input-currency' and
            OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY
        Populates from SourceColumns.SECURITY_NAME
        """
        sliced_df_2nd_field = (
            self.source_frame[SourceColumns.SECURITY_NAME]
            .str.split(" ")
            .str[1]
            .to_frame()
        )
        sliced_df_4th_field = (
            self.source_frame[SourceColumns.SECURITY_NAME]
            .str.split(" ")
            .str[3]
            .to_frame()
        )
        return pd.concat(
            [
                MapAttribute.process(
                    source_frame=sliced_df_2nd_field,
                    params=ParamsMapAttribute(
                        source_attribute=SourceColumns.SECURITY_NAME,
                        target_attribute=TempColumns.SECURITY_NAME_CURRENCY_CODE_2ND_FIELD,
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=sliced_df_4th_field,
                    params=ParamsMapAttribute(
                        source_attribute=SourceColumns.SECURITY_NAME,
                        target_attribute=TempColumns.SECURITY_NAME_CURRENCY_CODE_4TH_FIELD,
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _temp_price_currency_code(self) -> pd.DataFrame:
        """
        Temporary column used for:
            OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
            and Instrument Identifiers 'input-currency'
        Populates from TempColumns.CURRENCY_CODE or TempColumns.SECURITY_NAME_CURRENCY_CODE_4TH_FIELD
        """
        return MapConditional.process(
            source_frame=pd.concat(
                [
                    self.source_frame.loc[:, [SourceColumns.SECURITY_TYPE]],
                    self.pre_process_df.loc[
                        :,
                        [
                            TempColumns.CURRENCY_CODE,
                            TempColumns.TO_CURRENCY_CODE,
                            TempColumns.FROM_CURRENCY_CODE,
                            TempColumns.BUY_SELL_INDICATOR,
                            TempColumns.SECURITY_NAME_CURRENCY_CODE_4TH_FIELD,
                        ],
                    ],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=TempColumns.PRICE_CURRENCY,
                cases=[
                    Case(
                        query="index == index",
                        attribute=TempColumns.CURRENCY_CODE,
                    ),
                    Case(
                        query=f"`{SourceColumns.SECURITY_TYPE}`.str.fullmatch('CFWD', case=False, na=False)",
                        attribute=TempColumns.SECURITY_NAME_CURRENCY_CODE_4TH_FIELD,
                    ),
                    Case(
                        query=f"(`{SourceColumns.SECURITY_TYPE}`.str.fullmatch('CURR', case=False, na=False)) & "
                        f"(`{TempColumns.TO_CURRENCY_CODE}`.notnull()) & "
                        f"(`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.SELL.value}')",
                        attribute=TempColumns.TO_CURRENCY_CODE,
                    ),
                    Case(
                        query=f"(`{SourceColumns.SECURITY_TYPE}`.str.fullmatch('CURR', case=False, na=False)) & "
                        f"(`{TempColumns.FROM_CURRENCY_CODE}`.notnull()) & "
                        f"(`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.BUYI.value}')",
                        attribute=TempColumns.FROM_CURRENCY_CODE,
                    ),
                ],
            ),
        )

    def _temp_ticker_date_eco(self) -> pd.DataFrame:
        """
        Temp column for:
            TempColumns.EXPIRY_DATE
        Populated from SourceColumns.TICKER and used in options
        """
        eco_mask = self.source_frame[SourceColumns.SECURITY_TYPE].str.fullmatch(
            "ECO", case=False, na=False
        )
        sliced_df = (
            self.source_frame.loc[eco_mask, SourceColumns.TICKER]
            .str.split(" ")
            .str[1]
            .to_frame()
        )
        return ConvertDatetime.process(
            source_frame=sliced_df,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.TICKER,
                source_attribute_format="%m/%d/%y",
                target_attribute=TempColumns.TICKER_DATE_ECO,
                convert_to=ConvertTo.DATE,
                timezone_info=self.timezone_info,
            ),
        )

    def _temp_ticker_date_ico_ipo(self) -> pd.DataFrame:
        """
        Temp column for:
            TempColumns.EXPIRY_DATE
        Populated from SourceColumns.TICKER and used in options
        """
        ico_ipo_mask = self.source_frame[SourceColumns.SECURITY_TYPE].str.fullmatch(
            "ICO|IPO", case=False, na=False
        )
        sliced_df = (
            self.source_frame.loc[ico_ipo_mask, SourceColumns.SECURITY_NAME]
            .str[-11]
            .to_frame()
        )
        return ConvertDatetime.process(
            source_frame=sliced_df,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.SECURITY_NAME,
                source_attribute_format="%m/%d/%y",
                target_attribute=TempColumns.TICKER_DATE_ICO_IPO,
                convert_to=ConvertTo.DATE,
                timezone_info=self.timezone_info,
            ),
        )

    def _temp_ticker_date_future(self) -> pd.DataFrame:
        """
        Temp column for:
            TempColumns.EXPIRY_DATE
        Populated from SourceColumns.TICKER and used in futures
        """
        temp_df = pd.concat(
            [
                self.source_frame.loc[
                    :, [SourceColumns.TICKER, SourceColumns.ORDER_CREATE_DATE]
                ],
                self.pre_process_df.loc[:, [TempColumns.TRADE_DATE]],
            ],
            axis=1,
        )
        temp_df = temp_df.assign(
            **{
                TempColumns.TRADE_DATE: pd.to_datetime(
                    temp_df[SourceColumns.ORDER_CREATE_DATE].fillna(
                        temp_df[TempColumns.TRADE_DATE]
                    )
                )
            }
        )

        return get_expiry_date_from_desc(
            data=temp_df,
            trade_date_column=TempColumns.TRADE_DATE,
            desc_column=SourceColumns.TICKER,
            target_expiry_date=TempColumns.TICKER_DATE_FUTURE,
        )

    def _temp_security_name_date_forward(self) -> pd.DataFrame:
        """
        Temp column for:
            TempColumns.EXPIRY_DATE
        Populated from SourceColumns.SECURITY_NAME and used in fx forward
        """
        sliced_df = (
            self.source_frame[SourceColumns.SECURITY_NAME]
            .str.split(" ")
            .str[5]
            .to_frame()
        )
        return ConvertDatetime.process(
            source_frame=sliced_df,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.SECURITY_NAME,
                source_attribute_format="%d/%m/%y",
                target_attribute=TempColumns.SECURITY_NAME_DATE_FORWARD,
                convert_to=ConvertTo.DATE,
                timezone_info=self.timezone_info,
            ),
        )

    def _temp_fill_price(self) -> pd.DataFrame:
        """
        Temporary column for:
            OrderColumns.PRICE_FORMING_DATA_PRICE
            OrderColumns.TRANSACTION_DETAILS_PRICE
        Populates from SourceColumns.FILL_PRICE
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.FROM_CURRENCY_CODE,
                source_price_attribute=SourceColumns.FILL_PRICE,
                target_price_attribute=TempColumns.FILL_PRICE,
            ),
        )

    def _temp_order_average_price(self) -> pd.DataFrame:
        """
        Temporary column for:
            OrderColumns.PRICE_FORMING_DATA_PRICE
            OrderColumns.TRANSACTION_DETAILS_PRICE
        Populates from SourceColumns.ORDER_AVERAGE_PRICE
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.FROM_CURRENCY_CODE,
                source_price_attribute=SourceColumns.ORDER_AVERAGE_PRICE,
                target_price_attribute=TempColumns.ORDER_AVERAGE_PRICE,
            ),
        )

    def _temp_newo_in_file(self) -> pd.DataFrame:
        """
        Temporary column for RemoveDuplicateNEWO
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.NEWO_IN_FILE,
                cases=[
                    Case(
                        query=f"`{SourceColumns.ORDER_STATUS_TRANSFORMED}` == '{OrderStatus.NEWO.value}'",
                        value=True,
                    ),
                    Case(
                        query=f"`{SourceColumns.ORDER_STATUS_TRANSFORMED}` != '{OrderStatus.NEWO.value}'",
                        value=False,
                    ),
                ],
            ),
        )

    def _temp_notional_currency_2(self) -> pd.DataFrame:
        """
        Temporary Column for TempColumns.INSTR_IDS_NOTIONAL_CURRENCY_2
        Populates from SourceColumns.TO_CURRENCY_CODE
        """
        return MapConditional.process(
            source_frame=pd.concat(
                [
                    self.pre_process_df.loc[
                        :,
                        [
                            TempColumns.FROM_CURRENCY_CODE,
                            TempColumns.TO_CURRENCY_CODE,
                            TempColumns.BUY_SELL_INDICATOR,
                            TempColumns.SECURITY_NAME_CURRENCY_CODE_2ND_FIELD,
                        ],
                    ],
                    self.source_frame.loc[:, [SourceColumns.SECURITY_TYPE]],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=TempColumns.NOTIONAL_CURRENCY_2,
                cases=[
                    Case(
                        query=f"(`{SourceColumns.SECURITY_TYPE}`.str.fullmatch('CURR', case=False, na=False)) & "
                        f"(`{TempColumns.TO_CURRENCY_CODE}`.notnull()) & "
                        f"(`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.BUYI.value}')",
                        attribute=TempColumns.TO_CURRENCY_CODE,
                    ),
                    Case(
                        query=f"(`{SourceColumns.SECURITY_TYPE}`.str.fullmatch('CURR', case=False, na=False)) & "
                        f"(`{TempColumns.FROM_CURRENCY_CODE}`.notnull()) & "
                        f"(`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.SELL.value}')",
                        attribute=TempColumns.FROM_CURRENCY_CODE,
                    ),
                    Case(
                        query=f"`{SourceColumns.SECURITY_TYPE}`.str.fullmatch('CFWD', case=False, na=False)",
                        attribute=TempColumns.SECURITY_NAME_CURRENCY_CODE_2ND_FIELD,
                    ),
                ],
            ),
        )

    def _temp_fill_create_date(self) -> pd.DataFrame:
        """
        Temporary column used for TempColumns.FILL_CREATE_DATE
        and derived from SourceColumns.FILL_CREATE_DATE
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.FILL_CREATE_DATE,
                source_attribute_format="%Y-%m-%d %H:%M:%S",
                target_attribute=TempColumns.FILL_CREATE_DATE,
                convert_to=ConvertTo.DATETIME,
                timezone_info=self.timezone_info,
            ),
        )

    def _temp_place_date(self) -> pd.DataFrame:
        """
        Temporary column used for TempColumns.PLACE_DATE
        and derived from SourceColumns.PLACE_DATE
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.PLACE_DATE,
                source_attribute_format="%Y-%m-%d %H:%M:%S",
                target_attribute=TempColumns.PLACE_DATE,
                convert_to=ConvertTo.DATETIME,
                timezone_info_in_source_frame=SourceColumns.TIME_ZONE_NAME,
            ),
        )

    def _temp_fill_date(self) -> pd.DataFrame:
        """
        Temporary column used for TempColumns.FILL_DATE
        and derived from SourceColumns.FILL_DATE
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.FILL_DATE,
                source_attribute_format="%Y-%m-%d %H:%M:%S",
                target_attribute=TempColumns.FILL_DATE,
                convert_to=ConvertTo.DATETIME,
                timezone_info_in_source_frame=SourceColumns.TIME_ZONE_NAME,
            ),
        )

    def _temp_place_create_date(self) -> pd.DataFrame:
        """
        Temporary column used for TempColumns.PLACE_CREATE_DATE
        and derived from SourceColumns.PLACE_CREATE_DATE
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.PLACE_CREATE_DATE,
                source_attribute_format="%Y/%m/%d %H:%M:%S.%f",
                target_attribute=TempColumns.PLACE_CREATE_DATE,
                convert_to=ConvertTo.DATETIME,
                timezone_info=self.timezone_info,
            ),
        )

    def _temp_trade_date(self) -> pd.DataFrame:
        """
        Temporary column used for TempColumns.TRADE_DATE
        and derived from SourceColumns.TRADE_DATE
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.TRADE_DATE,
                source_attribute_format="%Y-%m-%d %H:%M:%S",
                target_attribute=TempColumns.TRADE_DATE,
                convert_to=ConvertTo.DATETIME,
                timezone_info_in_source_frame=SourceColumns.TIME_ZONE_NAME,
            ),
        )

    def _temp_trade_date_formatted(self) -> pd.DataFrame:
        """
        Temporary column used for TempColumns.TRADE_DATE
        and derived from SourceColumns.TRADE_DATE
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.TRADE_DATE,
                source_attribute_format="%Y-%m-%d %H:%M:%S",
                target_attribute=TempColumns.TRADE_DATE_FORMATTED,
                convert_to=ConvertTo.DATE,
                timezone_info_in_source_frame=SourceColumns.TIME_ZONE_NAME,
            ),
        )

    def _temp_order_create_date(self) -> pd.DataFrame:
        """
        Temporary column used for TempColumns.ORDER_CREATE_DATE
        and derived from SourceColumns.ORDER_CREATE_DATE
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.ORDER_CREATE_DATE,
                source_attribute_format="%Y-%m-%d %H:%M:%S",
                target_attribute=TempColumns.ORDER_CREATE_DATE,
                convert_to=ConvertTo.DATETIME,
                timezone_info=self.timezone_info,
            ),
        )

    def _temp_order_create_date_formatted(self) -> pd.DataFrame:
        """
        Temporary column used for TempColumns.ORDER_CREATE_DATE
        and derived from SourceColumns.ORDER_CREATE_DATE
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.ORDER_CREATE_DATE,
                source_attribute_format="%Y-%m-%d %H:%M:%S",
                target_attribute=TempColumns.ORDER_CREATE_DATE_FORMATTED,
                convert_to=ConvertTo.DATE,
                timezone_info=self.timezone_info,
            ),
        )

    def _temp_order_release_date_formatted(self) -> pd.DataFrame:
        """
        Temporary column used for TempColumns.ORDER_RELEASE_DATE
        and derived from SourceColumns.ORDER_RELEASE_DATE
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.ORDER_RELEASE_DATE,
                source_attribute_format="%Y-%m-%d %H:%M:%S",
                target_attribute=TempColumns.ORDER_RELEASE_DATE_FORMATTED,
                convert_to=ConvertTo.DATE,
                timezone_info=self.timezone_info,
            ),
        )

    def _temp_order_last_update_date(self) -> pd.DataFrame:
        """
        Temporary column used for TempColumns.ORDER_LAST_UPDATE_DATE
        and derived from SourceColumns.ORDER_LAST_UPDATE_DATE
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.ORDER_LAST_UPDATE_DATE,
                source_attribute_format="%Y-%m-%d %H:%M:%S",
                target_attribute=TempColumns.ORDER_LAST_UPDATE_DATE,
                convert_to=ConvertTo.DATETIME,
                timezone_info=self.timezone_info,
            ),
        )

    def _temp_order_release_date(self) -> pd.DataFrame:
        """
        Temporary column used for TempColumns.ORDER_RELEASE_DATE
        and derived from SourceColumns.ORDER_RELEASE_DATE
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.ORDER_RELEASE_DATE,
                source_attribute_format="%Y-%m-%d %H:%M:%S",
                target_attribute=TempColumns.ORDER_RELEASE_DATE,
                convert_to=ConvertTo.DATETIME,
                timezone_info=self.timezone_info,
            ),
        )

    def _temp_order_and_trading_date_time(self) -> pd.DataFrame:
        """
        Temporary column used for:
            OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED
            OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME
            OrderColumns.TIMESTAMPS_TRADING_DATE_TIME
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapAttribute.process(
                source_frame=self.pre_process_df,
                params=ParamsMapAttribute(
                    source_attribute=TempColumns.TRADE_DATE,
                    target_attribute=TempColumns.ORDER_TRADING_DATE_TIME,
                ),
                auditor=self.auditor,
            )
        else:
            return MapConditional.process(
                source_frame=pd.concat(
                    [
                        self.pre_process_df.loc[
                            :,
                            [
                                TempColumns.FILL_DATE,
                                TempColumns.ORDER_LAST_UPDATE_DATE,
                            ],
                        ],
                        self.source_frame.loc[
                            :, [SourceColumns.ORDER_STATUS_TRANSFORMED]
                        ],
                    ],
                    axis=1,
                ),
                params=ParamsMapConditional(
                    target_attribute=TempColumns.ORDER_TRADING_DATE_TIME,
                    cases=[
                        Case(
                            query=f"`{SourceColumns.ORDER_STATUS_TRANSFORMED}`.str.fullmatch('{OrderStatus.PARF.value}', "
                            f"case=False, na=False)",
                            attribute=TempColumns.FILL_DATE,
                        ),
                        Case(
                            query=f"`{SourceColumns.ORDER_STATUS_TRANSFORMED}`.str.fullmatch('{OrderStatus.CAME.value}', "
                            f"case=False, na=False)",
                            attribute=TempColumns.ORDER_LAST_UPDATE_DATE,
                        ),
                    ],
                ),
            )

    def _temp_traded_quantity(self) -> pd.DataFrame:
        """
        Temporary column for:
            OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
            OrderColumns.TRANSACTION_DETAILS_QUANTITY
        Contains data from SourceColumns.ORDER_EXEC_QTY in case of allocations
        else from SourceColumns.FILL_QTY.
        This is not populated in case Order Status is NEWO.
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            column = SourceColumns.ORDER_EXEC_QTY
        else:
            column = SourceColumns.FILL_QTY
        return pd.DataFrame(
            data=np.where(
                self.source_frame[SourceColumns.ORDER_STATUS_TRANSFORMED]
                != OrderStatus.NEWO.value,
                self.source_frame[column],
                pd.NA,
            ),
            index=self.source_frame.index,
            columns=[TempColumns.TRADED_QUANTITY],
        )

    def _temp_transaction_reference_number(self) -> pd.DataFrame:
        """
        Temporary column used for:
            OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO
            OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO
        Populates from SourceColumns.FIX_EXECUTION_ID or
            SourceColumns.TRADE_ALLOC_ID and TempColumns.TRADE_DATE_FORMATTED
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            temp_df = pd.concat(
                [
                    self.source_frame.loc[:, [SourceColumns.TRADE_ALLOC_ID]],
                    self.pre_process_df.loc[:, [TempColumns.TRADE_DATE_FORMATTED]],
                ],
                axis=1,
            )
            temp_df = temp_df.assign(**{TempColumns.DELIMITER: Delimiters.PIPE})
            return ConcatAttributes.process(
                source_frame=temp_df,
                params=ParamsConcatAttributes(
                    target_attribute=TempColumns.TRANSACTION_REFERENCE_NUMBER,
                    source_attributes=[
                        SourceColumns.TRADE_ALLOC_ID,
                        TempColumns.DELIMITER,
                        TempColumns.TRADE_DATE_FORMATTED,
                    ],
                ),
            )
        else:
            temp_df = self.source_frame.loc[
                :, [SourceColumns.ORDER_ID, SourceColumns.FIX_EXECUTION_ID]
            ]
            temp_df = temp_df.assign(
                **{
                    TempColumns.TEMP_PREFIX: "F",
                    TempColumns.DELIMITER: Delimiters.PIPE,
                    TempColumns.SOURCE_INDEX: temp_df.index,
                }
            )
            temp_df = pd.concat(
                [
                    temp_df,
                    ConcatAttributes.process(
                        source_frame=temp_df,
                        params=ParamsConcatAttributes(
                            target_attribute=TempColumns.TRANSACTION_REFERENCE_NUMBER_ORDER,
                            source_attributes=[
                                TempColumns.TEMP_PREFIX,
                                TempColumns.DELIMITER,
                                SourceColumns.ORDER_ID,
                                TempColumns.DELIMITER,
                                TempColumns.SOURCE_INDEX,
                            ],
                        ),
                    ),
                ],
                axis=1,
            )

            temp_df = pd.concat(
                [
                    temp_df,
                    ConcatAttributes.process(
                        source_frame=temp_df,
                        params=ParamsConcatAttributes(
                            target_attribute=TempColumns.TRANSACTION_REFERENCE_NUMBER_FIX,
                            source_attributes=[
                                SourceColumns.FIX_EXECUTION_ID,
                                TempColumns.DELIMITER,
                                TempColumns.SOURCE_INDEX,
                            ],
                        ),
                    ),
                ],
                axis=1,
            )

            return MapConditional.process(
                source_frame=temp_df,
                params=ParamsMapConditional(
                    target_attribute=TempColumns.TRANSACTION_REFERENCE_NUMBER,
                    cases=[
                        Case(
                            query=f"`{SourceColumns.FIX_EXECUTION_ID}`.notnull()",
                            attribute=TempColumns.TRANSACTION_REFERENCE_NUMBER_FIX,
                        ),
                        Case(
                            query=f"`{SourceColumns.FIX_EXECUTION_ID}`.isnull()",
                            attribute=TempColumns.TRANSACTION_REFERENCE_NUMBER_ORDER,
                        ),
                    ],
                ),
            )

    def _temp_reference_order_id(self) -> pd.DataFrame:
        """
        Temp column to be used in:
            OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID
            OrderColumns.ORDER_IDENTIFIERS_PARENT_ORDER_ID

        Populated from SourceColumn.ORIGINAL_ORDER_ID
        In order flow this column is merged from allocations file
        """
        return (
            MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.ORIGINAL_ORDER_ID,
                    target_attribute=TempColumns.REFERENCE_ORDER_ID,
                    cast_to=CastTo.INTEGER,
                ),
                auditor=self.auditor,
            )
            .astype("Int64")
            .fillna(pd.NA)
        )

    def _temp_ultimate_venue(self) -> pd.DataFrame:
        """
        Temp column used in
            OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE
            PartyIdentifiers

        Conditional mapping which maps venue
        from SourceColumns.SECURITY_NAME if SourceColumns.MIC is not populated
        """
        sliced_df_venue_details = (
            self.source_frame[SourceColumns.SECURITY_NAME]
            .str.split(" ")
            .str[-2]
            .to_frame()
        )
        sliced_df_venue_details[SourceColumns.SECURITY_NAME] = np.where(
            (
                (sliced_df_venue_details[SourceColumns.SECURITY_NAME].str.len() == 4)
                & ~(
                    sliced_df_venue_details[SourceColumns.SECURITY_NAME].str.isnumeric()
                ).fillna(False)
                & (sliced_df_venue_details[SourceColumns.SECURITY_NAME].str.isupper())
                & (
                    self.pre_process_df[TempColumns.ASSET_CLASS].isin(
                        [AssetClass.FUTURE, AssetClass.OPTION]
                    )
                )
            ),
            sliced_df_venue_details[SourceColumns.SECURITY_NAME],
            pd.NA,
        )

        # Initial conditions
        result_df: pd.DataFrame = MapConditional.process(
            source_frame=pd.concat(
                [
                    self.source_frame.loc[
                        :, [SourceColumns.MIC, SourceColumns.LAST_MARKET]
                    ],
                    sliced_df_venue_details,
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=TempColumns.ULTIMATE_VENUE,
                cases=[
                    Case(
                        query=f"`{SourceColumns.MIC}`.notnull()",
                        attribute=SourceColumns.MIC,
                    ),
                    Case(
                        query=f"`{SourceColumns.MIC}`.isnull()",
                        attribute=SourceColumns.SECURITY_NAME,
                    ),
                    Case(
                        query=(
                            f"`{SourceColumns.MIC}`.isnull() & `{SourceColumns.SECURITY_NAME}`.isnull()"
                        ),
                        attribute=SourceColumns.LAST_MARKET,
                    ),
                ],
            ),
        )

        # if sliced_df_venue_details[SourceColumns.SECURITY_NAME].isnull().any()
        # Fetch the lookup table from S3
        mic_code_map = self._get_lookup_map_from_s3()
        mic_from_lookup_frame = (
            self.source_frame.loc[:, SourceColumns.BROKER_CODE]
            .map(mic_code_map)
            .to_frame()
            .rename(
                columns={
                    SourceColumns.BROKER_CODE: TempColumns.MIC_CODE_FROM_LOOKUP_TABLE
                }
            )
        )
        result_df = pd.concat([result_df, mic_from_lookup_frame], axis=1)

        # If the conditions above where not enough to populate all rows
        # or values do not meet the requirements set to "XOFF"
        return MapConditional.process(
            source_frame=result_df,
            params=ParamsMapConditional(
                target_attribute=TempColumns.ULTIMATE_VENUE,
                cases=[
                    Case(
                        query=f"((`{TempColumns.ULTIMATE_VENUE}`.notnull()) &"
                        f" (`{TempColumns.ULTIMATE_VENUE}`.str.len() == 4))",
                        attribute=TempColumns.ULTIMATE_VENUE,
                    ),
                    Case(
                        query=f"(((`{TempColumns.ULTIMATE_VENUE}`.isnull()) |"
                        f" (`{TempColumns.ULTIMATE_VENUE}`.str.len() != 4)) &"
                        f" (`{TempColumns.MIC_CODE_FROM_LOOKUP_TABLE}`.notnull()))",
                        attribute=TempColumns.MIC_CODE_FROM_LOOKUP_TABLE,
                    ),
                    Case(
                        query=f"(((`{TempColumns.ULTIMATE_VENUE}`.isnull()) |"
                        f" (`{TempColumns.ULTIMATE_VENUE}`.str.len() != 4)) &"
                        f" (`{TempColumns.MIC_CODE_FROM_LOOKUP_TABLE}`.isnull()))",
                        value="XOFF",
                    ),
                ],
            ),
        )

    def _get_lookup_map_from_s3(self) -> pd.Series:
        """
        Reads a lookup file from S3 and returns a Series with the Broker code as the
        index and the Mic code as the data. If the file isn't found in S3, it
        returns an empty Series with the appropriate series and index names
        :return: Series with broker code as index and mic code as data
        """
        s3_bucket = Settings.realm
        lookup_table = read_csv_from_s3_download(
            bucket=s3_bucket,
            key=BROKER_CODE_TO_MIC_S3_FILE_KEY,
            logger=logger,
        )
        # read_csv_from_s3_download returns an empty df if the file is not found in S3.
        # Return an empty series in this case
        if lookup_table.empty:
            empty_series = pd.Series(
                index=[], name=BrokerCodeLookupColumns.MIC_CODE, dtype="str"
            )
            empty_series.index.name = BrokerCodeLookupColumns.BROKER_CODE
            return empty_series
        # Return a Series with broker_code as index and mic_code as data. Remove
        # stray \xa0 characters, if any.
        return lookup_table.set_index(BrokerCodeLookupColumns.BROKER_CODE)[
            BrokerCodeLookupColumns.MIC_CODE
        ].str.replace("\xa0", "")

    # Party Identifiers Fields
    def _temp_party_ids_executing_entity(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populated from TempColumns.EXECUTING_ENTITY
        Adds id/lei prefix to the column
        """
        return GetTenantLEI.process(
            source_frame=self.source_frame,
            params=ParamsGetTenantLEI(
                target_column_prefix=PartyPrefix.LEI,
                target_lei_column=TempColumns.EXECUTING_ENTITY,
            ),
        )

    def _temp_party_ids_trader(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from SourceColumns.ORDER_TRADER_ID
        Adds id prefix to the column
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ORDER_TRADER_ID,
                target_attribute=TempColumns.TRADER,
                prefix=PartyPrefix.ID,
            ),
            auditor=self.auditor,
        )

    def _temp_party_ids_client(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from SourceColumns.ACCOUNT_IDS which is generated
        on the controller flow, specifically on the transformer
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.ORDER_EXEC_BROKER,
                    target_attribute=TempColumns.CLIENT,
                    prefix=PartyPrefix.ID,
                ),
                auditor=self.auditor,
            )

        # Multiple clients support
        clients_tmp_df: pd.DataFrame = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ACCOUNT_IDS,
                target_attribute=TempColumns.CLIENT,
                list_delimiter=Delimiters.SEMI_COLON,
                cast_to=CastTo.STRING_LIST,
            ),
            auditor=self.auditor,
        )

        clients_not_null_mask = clients_tmp_df[TempColumns.CLIENT].notnull()

        # Add prefix to each account id value in the list
        clients_tmp_df.loc[
            clients_not_null_mask, TempColumns.CLIENT
        ] = clients_tmp_df.loc[clients_not_null_mask, TempColumns.CLIENT].apply(
            # TODO: Remove the first element when front end displays correctly the
            # | clients list.
            # | NOTE: this is currently being done as a workaround to
            # | display all the client values in the dashboard
            lambda lst: [", ".join(lst)]
            + [PartyPrefix.ID + str(item) for item in lst]
        )

        return clients_tmp_df

    def _temp_party_ids_execution_within_firm(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from SourceColumns.EXECUTION_WITHIN_FIRM for Allocations or
        SourceColumns.EXEC_DECISION_MAKER for Orders
        Adds id prefix to the column
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            column = SourceColumns.ALLO_EXEC_BROKER
        else:
            column = SourceColumns.EXEC_DECISION_MAKER
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=column,
                target_attribute=TempColumns.EXECUTION_WITHIN_FIRM,
                prefix=PartyPrefix.ID,
            ),
            auditor=self.auditor,
        )

    def _temp_party_ids_investment_decision_maker(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from SourceColumns.INVESTMENT_DECISION_MAKER for Allocations
        and SourceColumns.ORDER_MANAGER_ID for orders
        Adds id prefix to the column
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            column = SourceColumns.INVESTMENT_DECISION_MAKER
        else:
            column = SourceColumns.ORDER_MANAGER_ID
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=column,
                target_attribute=TempColumns.INVESTMENT_DECISION_MAKER,
                prefix=PartyPrefix.ID,
            ),
            auditor=self.auditor,
        )

    def _temp_party_ids_buyer_decision_maker(self) -> pd.DataFrame:
        pass

    def _temp_party_ids_seller_decision_maker(self) -> pd.DataFrame:
        pass

    def _temp_party_ids_buyer(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Adds id prefix to the column
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            buyer_data = (
                PartyPrefix.ID + self.source_frame.loc[:, SourceColumns.ACCOUNT_ID]
            ).values
        else:
            buyer_data = self.pre_process_df.loc[:, TempColumns.EXECUTING_ENTITY].values
        return pd.DataFrame(
            data=buyer_data,
            index=self.pre_process_df.index,
            columns=[TempColumns.BUYER],
        )

    def _temp_party_ids_seller(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Adds id prefix to the column
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            seller_data = (
                PartyPrefix.ID + self.source_frame.loc[:, SourceColumns.BROKER_CODE]
            ).values
        else:
            seller_data = self.pre_process_df.loc[:, TempColumns.COUNTERPARTY].values
        return pd.DataFrame(
            data=seller_data,
            index=self.pre_process_df.index,
            columns=[TempColumns.SELLER],
        )

    def _temp_party_ids_counterparty(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from SourceColumns.BROKER_CODE for allocations flow
        and the following in priority for Orders flow:
          - SourceColumns.ALLO_EXEC_BROKER
          - SourceColumns.PLACE_BROKER_ID
          - SourceColumns.ORDER_EXEC_BROKER
        Adds id prefix to the column.
        """
        if self.file_type == FileTypes.ALLOCATIONS:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.BROKER_CODE,
                    target_attribute=TempColumns.COUNTERPARTY,
                    prefix=PartyPrefix.ID,
                ),
                auditor=self.auditor,
            )
        else:
            return MapConditional.process(
                source_frame=self.source_frame,
                params=ParamsMapConditional(
                    target_attribute=TempColumns.COUNTERPARTY,
                    cases=[
                        Case(
                            query=f"`{SourceColumns.ORDER_EXEC_BROKER}`.notnull()",
                            attribute=SourceColumns.ORDER_EXEC_BROKER,
                            attribute_prefix=PartyPrefix.ID,
                        ),
                        Case(
                            query=f"`{SourceColumns.PLACE_BROKER_ID}`.notnull()",
                            attribute=SourceColumns.PLACE_BROKER_ID,
                            attribute_prefix=PartyPrefix.ID,
                        ),
                        Case(
                            query=f"`{SourceColumns.ALLO_EXEC_BROKER}`.notnull()",
                            attribute=SourceColumns.ALLO_EXEC_BROKER,
                            attribute_prefix=PartyPrefix.ID,
                        ),
                    ],
                ),
            )

    # Instrument Identifiers Fields
    def _temp_instr_ids_asset_class(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates based on values of SourceColumns.SECURITY_TYPE
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.ASSET_CLASS,
                cases=[
                    Case(
                        query=f"`{SourceColumns.SECURITY_TYPE}`.str.fullmatch('IFUT|BNDFUT|IDXFUT', case=False, na=False)",
                        value=AssetClass.FUTURE,
                    ),
                    Case(
                        query=f"`{SourceColumns.SECURITY_TYPE}`.str.fullmatch('CFWD', case=False, na=False)",
                        value=AssetClass.FX_FORWARD,
                    ),
                    Case(
                        query=f"`{SourceColumns.SECURITY_TYPE}`.str.fullmatch('CURR', case=False, na=False)",
                        value=AssetClass.FX_SPOT,
                    ),
                    Case(
                        query=f"`{SourceColumns.SECURITY_TYPE}`.str.fullmatch('ECO|ICO|IPO', case=False, na=False)",
                        value=AssetClass.OPTION,
                    ),
                ],
            ),
        )

    def _temp_instr_ids_expiry_date(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates from SourceColumns.SECURITY_NAME or SourceColumns.TICKER_DATE
        """
        fx_forward = f"(`{TempColumns.ASSET_CLASS}` == '{AssetClass.FX_FORWARD}')"
        fx_future = f"(`{TempColumns.ASSET_CLASS}` == '{AssetClass.FUTURE}')"
        fx_option_eco = f"(`{SourceColumns.SECURITY_TYPE}` == 'ECO')"
        fx_option_ico_ipo = f"(`{SourceColumns.SECURITY_TYPE}`.str.fullmatch('ICO|IPO',case=False,na=False))"

        return MapConditional.process(
            source_frame=pd.concat([self.pre_process_df, self.source_frame], axis=1),
            params=ParamsMapConditional(
                target_attribute=TempColumns.EXPIRY_DATE,
                cases=[
                    Case(
                        query=fx_option_eco,
                        attribute=TempColumns.TICKER_DATE_ECO,
                    ),
                    Case(
                        query=fx_option_ico_ipo,
                        attribute=TempColumns.TICKER_DATE_ICO_IPO,
                    ),
                    Case(
                        query=fx_future,
                        attribute=TempColumns.TICKER_DATE_FUTURE,
                    ),
                    Case(
                        query=fx_forward,
                        attribute=TempColumns.SECURITY_NAME_DATE_FORWARD,
                    ),
                ],
            ),
        )

    def _temp_instr_ids_options_strike_price(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates from SourceColumns.TICKER
        """
        strike_price_df = (
            self.source_frame[SourceColumns.TICKER]
            .str.split(" ")
            .str[2]
            .fillna(pd.NA)
            .str[1:]
            .to_frame()
        )
        temp_df = ConvertMinorToMajor.process(
            source_frame=pd.concat(
                [
                    self.pre_process_df.loc[:, [TempColumns.FROM_CURRENCY_CODE]],
                    strike_price_df,
                ],
                axis=1,
            ),
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=TempColumns.FROM_CURRENCY_CODE,
                source_price_attribute=SourceColumns.TICKER,
                target_price_attribute=TempColumns.OPTION_STRIKE_PRICE,
            ),
        )
        not_null_mask = temp_df[TempColumns.OPTION_STRIKE_PRICE].notnull()
        temp_df.loc[not_null_mask, TempColumns.OPTION_STRIKE_PRICE] = temp_df.loc[
            not_null_mask, TempColumns.OPTION_STRIKE_PRICE
        ].astype(float)

        return MapConditional.process(
            source_frame=pd.concat(
                [self.pre_process_df.loc[:, [TempColumns.ASSET_CLASS]], temp_df], axis=1
            ),
            params=ParamsMapConditional(
                target_attribute=TempColumns.OPTION_STRIKE_PRICE,
                cases=[
                    Case(
                        query=f"(`{TempColumns.ASSET_CLASS}` == '{AssetClass.OPTION}')",
                        attribute=TempColumns.OPTION_STRIKE_PRICE,
                    ),
                ],
            ),
        )

    def _temp_instr_ids_option_type(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates from SourceColumns.TICKER
        """
        option_type_df = (
            self.source_frame[SourceColumns.TICKER]
            .str.split(" ")
            .str[2]
            .fillna(pd.NA)
            .str[:1]
            .to_frame()
        )
        option_type_df = MapValue.process(
            source_frame=option_type_df,
            params=ParamsMapValue(
                source_attribute=SourceColumns.TICKER,
                target_attribute=TempColumns.TEMP_COL_1,
                case_insensitive=True,
                preserve_original=True,
                value_map={
                    "C": OptionType.CALL.value,
                    "P": OptionType.PUTO.value,
                },
            ),
            auditor=self.auditor,
        )
        return MapConditional.process(
            source_frame=pd.concat(
                [self.pre_process_df.loc[:, [TempColumns.ASSET_CLASS]], option_type_df],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=TempColumns.OPTION_TYPE,
                cases=[
                    Case(
                        query=f"(`{TempColumns.ASSET_CLASS}` == '{AssetClass.OPTION}')",
                        attribute=TempColumns.TEMP_COL_1,
                    ),
                ],
            ),
        )

    def _temp_instr_ids_underlying_isin(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates from SourceColumns.ISIN
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ISIN,
                target_attribute=TempColumns.UNDERLYING_ISIN,
            ),
            auditor=self.auditor,
        )

    def _temp_instr_ids_underlying_symbol(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates from Asset class mapping. If asset class is Option
        then map `Option Contract Bloomberg Root Code`
        elif Future then map `Future Bloomberg Root`
        """
        fx_future = f"(`{TempColumns.ASSET_CLASS}` == '{AssetClass.FUTURE}')"
        fx_option = f"(`{TempColumns.ASSET_CLASS}` == '{AssetClass.OPTION}')"

        temp_df = self.source_frame.loc[:, [SourceColumns.TICKER]]
        temp_df = temp_df.assign(
            **{
                TempColumns.TEMP_UNDERLYING_SYMBOL_OPTION: temp_df[SourceColumns.TICKER]
                .str.split(" ")
                .str[0],
                TempColumns.TEMP_UNDERLYING_SYMBOL_FUTURE: temp_df[
                    SourceColumns.TICKER
                ].str[:-2],
            }
        )
        temp_df.loc[:, TempColumns.UNDERLYING_SYMBOL] = MapConditional.process(
            source_frame=pd.concat(
                [temp_df, self.pre_process_df.loc[:, [TempColumns.ASSET_CLASS]]], axis=1
            ),
            params=ParamsMapConditional(
                target_attribute=TempColumns.UNDERLYING_SYMBOL,
                cases=[
                    Case(
                        query=fx_option,
                        attribute=TempColumns.TEMP_UNDERLYING_SYMBOL_OPTION,
                    ),
                    Case(
                        query=fx_future,
                        attribute=TempColumns.TEMP_UNDERLYING_SYMBOL_FUTURE,
                    ),
                ],
            ),
        )[TempColumns.UNDERLYING_SYMBOL]

        return MapValue.process(
            source_frame=temp_df,
            params=ParamsMapValue(
                source_attribute=TempColumns.UNDERLYING_SYMBOL,
                target_attribute=TempColumns.UNDERLYING_SYMBOL,
                case_insensitive=True,
                preserve_original=True,
                value_map={
                    "TU": "ZT",
                    "BY": "Z3",
                    "FV": "ZF",
                    "TY": "ZN",
                    "UXY": "TN",
                    "US": "ZB",
                    "WN": "UL",
                },
            ),
            auditor=self.auditor,
        )

    # Instrument Fallback
    def _temp_instr_fallback_option_type_mapped(self) -> pd.DataFrame:
        """
        Temporary column used in Instrument Fallback.
        Uses value obtained from TempColumns.OPTION_TYPE and maps the following:
            C: CALL
            P: PUT
        """
        return MapValue.process(
            source_frame=self.pre_process_df,
            params=ParamsMapValue(
                source_attribute=TempColumns.OPTION_TYPE,
                target_attribute=TempColumns.OPTION_TYPE_MAPPED,
                case_insensitive=True,
                value_map={
                    "C": "CALL",
                    "P": "PUT",
                },
            ),
            auditor=self.auditor,
        )

    def _temp_instr_fallback_quantity_notation(self) -> pd.DataFrame:
        """
        Temp field used in Instrument Fallback.
        Maps to ext.quantityNotation.
        """
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=TempColumns.INSTR_QUANTITY_NOTATION,
                target_value=QuantityNotation.UNIT.value,
            ),
        )

    def _temp_best_ex_asset_class_main(self) -> pd.DataFrame:
        """
        Temp field used in Instrument Fallback.
        Maps to ext.bestExAssetClassMain.
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.SECURITY_TYPE,
                target_attribute=TempColumns.INST_FB_BEST_EX_ASSET_CLASS_MAIN,
                case_insensitive=True,
                default_value="Other Instruments",
                value_map={
                    "COM": "Equity",
                    "PFD": "Equity",
                    "ADR": "Equity",
                    "FIXB": BestExAssetClassMain.DEBT_INSTRUMENTS.value,
                    "FRN": BestExAssetClassMain.DEBT_INSTRUMENTS.value,
                    "IPO": BestExAssetClassMain.EQUITY_DERIVATIVES.value,
                    "ICO": BestExAssetClassMain.EQUITY_DERIVATIVES.value,
                    "IDXFUT": BestExAssetClassMain.EQUITY_DERIVATIVES.value,
                    "CFWD": BestExAssetClassMain.CURRENCY_DERIVATIVES.value,
                    "CURR": "Other Instruments",
                    "ECO": BestExAssetClassMain.EQUITY_DERIVATIVES.value,
                    "IFUT": BestExAssetClassMain.EQUITY_DERIVATIVES.value,
                    "BNDFUT": BestExAssetClassMain.CREDIT_DERIVATIVES.value,
                    "SWPTNCDI": BestExAssetClassMain.CREDIT_DERIVATIVES.value,
                    "SWAPIRS": BestExAssetClassMain.INTEREST_RATE_DERIVATIVES.value,
                    "SWAPCDSI": BestExAssetClassMain.CREDIT_DERIVATIVES.value,
                    "SWAPCDS": BestExAssetClassMain.CREDIT_DERIVATIVES.value,
                    "REIT": BestExAssetClassMain.EQUITY_DERIVATIVES.value,
                    "ZERB": BestExAssetClassMain.DEBT_INSTRUMENTS.value,
                    "DR": BestExAssetClassMain.EQUITY_DERIVATIVES.value,
                },
            ),
            auditor=self.auditor,
        )[TempColumns.INST_FB_BEST_EX_ASSET_CLASS_MAIN]

    def _temp_best_ex_asset_class_sub(self) -> pd.DataFrame:
        """
        Temp field used in Instrument Fallback.
        Maps to ext.bestExAssetClassSub.
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.SECURITY_TYPE,
                target_attribute=TempColumns.INST_FB_BEST_EX_ASSET_CLASS_SUB,
                case_insensitive=True,
                default_value="",
                value_map={
                    "FIXB": "Bonds",
                    "FRN": "Bonds",
                    "IPO": "Futures and options admitted to trading on a trading venue",
                    "ICO": "Futures and options admitted to trading on a trading venue",
                    "IDXFUT": "Swaps and other equity derivatives",
                    "CFWD": "Swaps, forwards, and other currency derivatives",
                    "CURR": "Other Instruments",
                    "ECO": "Futures and options admitted to trading on a trading venue",
                    "IFUT": "Futures and options admitted to trading on a trading venue",
                    "BNDFUT": "Futures and options admitted to trading on a trading venue",
                    "SWPTNCDI": "Other credit derivatives",
                    "SWAPIRS": "Swaps, forwards, and other interest rates derivatives",
                    "SWAPCDSI": "Other credit derivatives",
                    "SWAPCDS": "Other credit derivatives",
                    "ZERB": "Bonds",
                },
            ),
            auditor=self.auditor,
        )[TempColumns.INST_FB_BEST_EX_ASSET_CLASS_SUB]

    @staticmethod
    def _set_timezone_info() -> NoReturn:
        """
        Sets the timezone info for the ConvertDateTime tasks
        Default is None
        """
        return None

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""
