import pandas as pd
from se_trades_tasks.order.static import OrderColumns

from swarm_tasks.order.feed.charles_river.static import SourceColumns
from swarm_tasks.order.transformations.charles_river.charles_river_order_transformations import (
    CharlesRiverOrderTransformations,
)
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue

CEST_TIMEZONE = "Europe/Amsterdam"


class RobecoCharlesRiverOrderTransformations(CharlesRiverOrderTransformations):
    """
    This transformation is specific for Robeco
    It overrides all timestamps to use Amsterdam time (CEST or CET)
    """

    @staticmethod
    def _set_timezone_info() -> str:
        """
        Sets the timezone info for the ConvertDateTime tasks
        This is a tenant specific parameter, hence these Tenant specific transformations
        """
        return CEST_TIMEZONE

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        """
        See https://steeleye.atlassian.net/browse/ON-4723 for more details.
        Populates OrderColumns.EXECUTION_DETAILS_PASSIVE_AGGRESSIVE_INDICATOR
        from SourceColumns.QUANT_ID.
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.QUANT_IND,
                target_attribute=OrderColumns.EXECUTION_DETAILS_PASSIVE_AGGRESSIVE_INDICATOR,
                case_insensitive=True,
                value_map={
                    "Y": "AGRE",
                    "N": "PASV",
                },
                default_value=pd.NA,
            ),
            auditor=self.auditor,
        )
