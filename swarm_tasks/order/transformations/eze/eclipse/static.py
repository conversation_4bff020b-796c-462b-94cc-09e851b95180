DATA_SOURCE = "EZE ECLIPSE ORDERS"
PARTIES_CLNT_NORE = "clnt:nore"


class SourceCols:
    ALLOCATIONS = "Allocations"
    ASSET_CLASS = "AssetClass"
    ASSET_TYPE = "AssetType"
    BLOOMBERG_ID = "BloombergID"
    CREATE_DATETIME = "CreateDateTime_x"
    DESTINATION_DISPLAY_NAME = "DestinationDisplayName"
    EXECUTIONS = "Executions"
    EXECUTION_PRICE = "ExecutionPrice"
    EXPIRATION_DATE = "ExpirationDate"
    ISIN = "ISIN"
    ISSUER = "Issuer"
    LAST_MODIFIED_DATE_TIME = "LastModifiedDateTime_x"
    LIMIT = "Limit_x"
    LOCAL_CURRENCY = "LocalCurrency"
    Note = "Note"
    OCC_CODE = "OccCode"
    ORDER_ID = "ID_x"
    ORDER_LIMIT_PRICE = "LimitPrice_x"
    ORIGINAL_ORDER_ID = "OriginalOrderID"
    PRICE_MULTIPLIER = "PriceMultiplier"
    QUANTITY = "Quantity_x"
    ROUTES = "Routes"
    ROUTE_ID = "ID_y"
    ROUTE_LIMIT_PRICE = "LimitPrice_y"
    SECURITY_NAME = "SecurityName"
    SIDE = "Side"
    STRIKE_PRICE = "StrikePrice"
    SYMBOL = "Symbol"
    TRADER = "Trader"
    UNDERLYING_ISIN = "UnderlyingISIN"
    UNDERLYING_SYMBOL = "UnderlyingSymbol"


class SourceExecutions:
    CREATE_DATETIME = "CreateDateTime_y"
    EXECUTION_ID = "ExecutionID"
    EXEC_DATE_TIME = "ExecDateTime"
    QUANTITY = "Quantity"
    VENUE = "Venue"


class SourceAllocations:
    PORTFOLIO_NAME = "PortfolioName"
    MANAGER = "Manager"


class DerivedCols:
    AGGREGATED_DESTINATION_DISPLAY_NAME = "__AGGREGATED_DESTINATION_DISPLAY_NAME__"
    AGGREGATED_DESTINATION_DISPLAY_NAME_WITH_PREFIX = (
        "__AGGREGATED_DESTINATION_DISPLAY_NAME_WITH_PREFIX__"
    )
    AGGREGATED_INVESTMENT_DECISION_WITHIN_FIRM = (
        "__AGGREGATED_INVESTMENT_DECISION_WITHIN_FIRM__"
    )
    AGGREGATED_INVESTMENT_DECISION_WITHIN_FIRM_WITH_PREFIX = (
        "__AGGREGATED_INVESTMENT_DECISION_WITHIN_FIRM_WITH_PREFIX__"
    )
    AGGREGATED_PORTFOLIO_NAME = "__AGGREGATED_PORTFOLIO_NAME__"
    AGGREGATED_PORTFOLIO_NAME_WITH_PREFIX = "__AGGREGATED_PORTFOLIO_NAME_WITH_PREFIX__"
    AGGREGATED_QTY_EXECUTIONS = "__AGGREGATED_QTY_EXECUTIONS__"
    AGGREGATED_TRADER = "__AGGREGATED_TRADER__"
    AGGREGATED_TRADER_WITH_PREFIX = "__AGGREGATED_TRADER_WITH_PREFIX__"
    ASSET_CLASS = "__ASSET_CLASS__"
    CREATE_DATETIME = "__CREATE_DATETIME__"
    EXECUTION_WITHIN_FIRM = "__EXECUTION_WITHIN_FIRM__"
    EXECUTION_WITHIN_FIRM_WITH_PREFIX = "__EXECUTION_WITHIN_FIRM_WITH_PREFIX__"
    EXEC_DATE_TIME = "__EXEC_DATE_TIME__"
    EXEC_VENUE = "__EXEC_VENUE__"
    EXPIRY_DATE = "__EXPIRY_DATE__"
    EXPIRY_DATE_EXP_DATE = "__EXPIRY_DATE_EXP_DATE__"
    EXPIRY_DATE_OCC_CODE = "__EXPIRY_DATE_OCC_CODE__"
    EXPIRY_DATE_SYMBOL = "__EXPIRY_DATE_SYMBOL__"
    INST_FB_EXCH_ROOT_SYMBOL = "__INST_FB_EXCH_ROOT_SYMBOL__"
    INST_FB_EXT_BEST_EX_ASSET_CLS_MAIN = "__INST_FB_EXT_BEST_EX_ASSET_CLS_MAIN__"
    INST_FB_EXT_BEST_EX_ASSET_CLS_SUB = "__INST_FB_EXT_BEST_EX_ASSET_CLS_SUB__"
    INST_FB_INST_FULL_NAME = "__INST_FB_INST_FULL_NAME__"
    INST_FB_INST_ID_CODE = "__INST_FB_INST_ID_CODE__"
    INST_FB_INST_UNIQUE_ID = "__INST_FB_INST_UNIQUE_ID__"
    INST_FB_IS_CREATED_THRU_FB = "__INST_FB_IS_CREATED_THRU_FB__"
    LIMIT_PRICE = "__LIMIT_PRICE__"
    NEW_O_COL_IN_FILE = "__NEW_O_COL_IN_FILE__"
    OCC_CODE = "__OCC_CODE__"
    ONLY_NEWO = "__ONLY_NEWO__"
    OPTION_TYPE = "__OPTION_TYPE__"
    ORDER_SUBMITTED_DT = "__ORDER_SUBMITTED_DT__"
    SOURCE_INDEX = "__source_index__"
    STRIKE_PRICE = "__STRIKE_PRICE__"
    STRIKE_PRICE_OCC_CODE = "__STRIKE_PRICE_OCC_CODE__"
    SWARM_RAW_INDEX = "__swarm_raw_index__"
    TENANT_LEI = "__TENANT_LEI__"
    UNDERLYING_SYMBOL = "__UNDERLYING_SYMBOL__"
