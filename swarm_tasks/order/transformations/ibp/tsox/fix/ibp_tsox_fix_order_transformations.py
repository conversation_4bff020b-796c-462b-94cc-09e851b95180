from typing import NoReturn

import pandas as pd
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.fix.fix_parser_result_to_frame import StaticFields
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.frame.map_conditional_attribute_from_list_items import (
    MapConditionalAttributeFromListItems,
)
from swarm_tasks.generic.frame.map_conditional_attribute_from_list_items import (
    Params as MapFromListItemsParams,
)
from swarm_tasks.order.transformations.ibp.tsox.fix import static
from swarm_tasks.order.transformations.ibp.tsox.fix.static import BUY_SELL_MAP
from swarm_tasks.order.transformations.ibp.tsox.fix.static import SourceColumns
from swarm_tasks.order.transformations.ibp.tsox.fix.static import TempColumns
from swarm_tasks.order.transformations.ibp.tsox.fix.static import TRADING_CAPACITY_MAP
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ConvertDatetimeParams,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as MapConditionalParams
from swarm_tasks.transform.map.map_static import MapStatic
from swarm_tasks.transform.map.map_static import Params as MapStaticParams
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as MapValueParams
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as MergeMarketIdentifiersParams,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as PartyIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as InstrumentIdentifiersParams,
)
from swarm_tasks.utilities.static import Delimiters


class IbpTsoxFixOrderTransformations(AbstractOrderTransformations):
    __data_source_name__ = "TSOX BBG FIX"
    __destination_trade__ = "Destination of trade (dealer)"
    __submitter_trade__ = "Submitter of trade (Buy-side)"
    __client__ = "__client__"
    __counterparty__ = "__counterparty__"
    __execution_within_firm_id__ = "__execution_within_firm_id__"
    __execution_within_firm_id_pre_routing_flip__ = (
        "__execution_within_firm_id_pre_flip__"
    )
    __executing_entity__ = "__executing_entity__"
    __instrument_created_through_fb__ = "__instrument_created_through_fb__"
    __trd_reg_timestamp__ = "__processed_trd_reg_timestamp__"
    __security_id__ = "__security_id__"
    __trader__ = "__trader__"
    __trader_pre_routing_flip__ = "__trader_pre_flip__"
    __transact_time__ = "__transact_time__"

    def process(self) -> pd.DataFrame:
        self.pre_process()
        self.buy_sell()
        self.data_source_name()
        self.date()
        self.execution_details_buy_sell_indicator()
        self.execution_details_order_type()
        self.execution_details_order_status()
        self.execution_details_outgoing_order_addl_info()
        self.execution_details_routing_strategy()
        self.execution_details_trading_capacity()
        self.id()
        self.market_identifiers_instrument()
        self.market_identifiers_parties()
        self.market_identifiers()
        self.meta_model()
        self.order_class()
        self.order_identifiers_order_id_code()
        self.order_identifiers_parent_order_id()
        self.order_identifiers_trading_venue_transaction_id_code()
        self.order_identifiers_transaction_ref_no()
        self.price_forming_data_initial_quantity()
        self.price_forming_data_price()
        self.price_forming_data_remaining_quantity()
        self.price_forming_data_traded_quantity()
        self.report_details_transaction_ref_no()
        self.source_key()
        self.timestamps_trading_date_time()
        self.timestamps_order_received()
        self.timestamps_order_status_updated()
        self.timestamps_order_submitted()
        self.transaction_details_buy_sell_indicator()
        self.transaction_details_quantity()
        self.transaction_details_quantity_currency()
        self.transaction_details_price()
        self.transaction_details_price_average()
        self.transaction_details_price_currency()
        self.transaction_details_price_notation()
        self.transaction_details_quantity_notation()
        self.transaction_details_settlement_date()
        self.transaction_details_trading_capacity()
        self.transaction_details_trading_date_time()
        self.transaction_details_ultimate_venue()
        self.transaction_details_venue()
        self.post_process()
        return self.target_df

    def _pre_process(self) -> NoReturn:
        # Security ID
        map_from_list_item_params = MapFromListItemsParams(
            source_attribute_for_pattern_search="SecurityAltIDSource",
            source_attribute_for_mapping="SecurityAltID",
            regex_pattern="^4$",
            target_attribute=self.__security_id__,
        )
        self.pre_process_df.loc[
            :, self.__security_id__
        ] = MapConditionalAttributeFromListItems(
            "MapConditionalAttributeFromListItems", params=map_from_list_item_params
        ).execute(
            source_frame=self.source_frame, params=map_from_list_item_params
        )

        self.pre_process_df.loc[:, self.__transact_time__] = ConvertDatetime.process(
            self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.TRANSACT_TIME,
                target_attribute=self.__transact_time__,
                convert_to=ConvertTo.DATETIME,
            ),
        )

        # TrdRegTimestamp
        map_trd_reg_timestamp_from_list_item_params = MapFromListItemsParams(
            source_attribute_for_pattern_search=SourceColumns.TRD_REG_TIMESTAMP_TYPE,
            source_attribute_for_mapping=SourceColumns.TRD_REG_TIMESTAMP,
            regex_pattern="^1$",
            target_attribute="trd_reg_timestamp",
        )

        trd_reg_timestamp = MapConditionalAttributeFromListItems.process(
            source_frame=self.source_frame,
            params=map_trd_reg_timestamp_from_list_item_params,
        )
        self.pre_process_df.loc[
            :, self.__trd_reg_timestamp__
        ] = ConvertDatetime.process(
            trd_reg_timestamp,
            params=ConvertDatetimeParams(
                source_attribute="trd_reg_timestamp",
                target_attribute=self.__trd_reg_timestamp__,
                convert_to=ConvertTo.DATETIME,
            ),
        )

    def _post_process(self):
        """Adds flag column to identify NEWO records"""
        self.target_df = pd.concat([self.target_df, self._temp_newo_in_file()], axis=1)
        self.target_df[self.__instrument_created_through_fb__] = True
        self.target_df.loc[:, TempColumns.INSTRUMENT_UNIQUE_IDENTIFIER] = (
            self.pre_process_df.loc[:, self.__security_id__]
            + self.target_df.loc[:, OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY]
            + self.target_df.loc[:, OrderColumns.TRANSACTION_DETAILS_VENUE]
        )

        self.get_instrument_full_name()

    def get_instrument_full_name(self) -> NoReturn:
        """
        Creates TempColumns.INSTRUMENT_FULL_NAME to be used downstream in instrument fallback
        """
        self.post_process_df.loc[:, SourceColumns.ISSUER] = self.source_frame.loc[
            :, SourceColumns.ISSUER
        ]
        self.post_process_df.loc[:, SourceColumns.COUPON_RATE] = (
            self.source_frame.loc[:, SourceColumns.COUPON_RATE] * 100
        )
        self.post_process_df.loc[:, SourceColumns.COUPON_RATE] = (
            self.post_process_df.loc[:, SourceColumns.COUPON_RATE]
            .astype(str)
            .replace(r"\.0*$", "", regex=True)
            + "%"
        )
        self.target_df.loc[:, TempColumns.INSTRUMENT_FULL_NAME] = (
            self.post_process_df.loc[:, SourceColumns.ISSUER]
            + " "
            + self.post_process_df.loc[:, SourceColumns.COUPON_RATE]
        )

    def _buy_sell(self) -> pd.DataFrame:
        """Populates OrderColumns.BUY_SELL from SourceColumns.SIDE"""
        return pd.concat(
            [
                MapValue.process(
                    source_frame=self.source_frame,
                    params=MapValueParams(
                        source_attribute=SourceColumns.SIDE,
                        target_attribute=add_prefix(
                            ModelPrefix.ORDER, OrderColumns.BUY_SELL
                        ),
                        value_map=BUY_SELL_MAP,
                    ),
                ),
                MapValue.process(
                    source_frame=self.source_frame,
                    params=MapValueParams(
                        source_attribute=SourceColumns.SIDE,
                        target_attribute=add_prefix(
                            ModelPrefix.ORDER_STATE, OrderColumns.BUY_SELL
                        ),
                        value_map=BUY_SELL_MAP,
                    ),
                ),
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        """Returns a data frame containing dataSourceName.
        This is populated with the static value 'ICE POF Exchange'"""
        return pd.DataFrame(
            data=self.__data_source_name__,
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """Returns a data frame containing date as the target column"""
        return ConvertDatetime.process(
            self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.TRANSACT_TIME,
                target_attribute=OrderColumns.DATE,
                convert_to=ConvertTo.DATE,
            ),
        )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Populates executionDetails.buySellIndicator column"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.SIDE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                value_map={"1": "BUYI", "2": "SELL"},
            ),
            auditor=self.auditor,
        )

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_order_status(self) -> pd.DataFrame:
        """Populates OrderColumns.EXECUTION_DETAILS_ORDER_STATUS"""
        return pd.concat(
            [
                MapValue.process(
                    source_frame=self.source_frame,
                    params=MapValueParams(
                        source_attribute=SourceColumns.ORD_STATUS,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        ),
                        case_insensitive=True,
                        value_map=static.ORD_STATUS_MAP,
                    ),
                    auditor=self.auditor,
                ),
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=MapStaticParams(
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        ),
                        target_value=OrderStatus.NEWO.value,
                    ),
                ),
            ],
            axis=1,
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """Populates with static value Market"""
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
                target_value="Market",
            ),
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.outgoingOrderAddlInfo."""
        return ConcatAttributes.process(
            source_frame=self.source_frame,
            params=ParamsConcatAttributes(
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                source_attributes=[
                    SourceColumns.COUPON_RATE,
                    SourceColumns.YIELD,
                ],
                delimiter=Delimiters.PIPE,
            ),
        )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        """Populates OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY"""
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY,
                cases=[
                    Case(
                        query=f"`{SourceColumns.FF_7014}`.str.fullmatch('3', case=False, na=False)",
                        value=self.__destination_trade__,
                    ),
                    Case(
                        query=f"`{SourceColumns.FF_7014}`.str.fullmatch('21', case=False, na=False)",
                        value=self.__submitter_trade__,
                    ),
                ],
            ),
        )

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_stop_price(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """Populates OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY from SourceColumns.LAST_CAPACITY"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.LAST_CAPACITY,
                target_attribute=OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY,
                value_map=TRADING_CAPACITY_MAP,
            ),
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _financing_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _hierarchy(self) -> pd.DataFrame:
        """Not Implemented"""

    def _id(self) -> pd.DataFrame:
        """Returns a data frame containing _order.id and _orderState.id.
        This is populated from SourceColumns.EXEC_ID"""
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.source_frame.loc[:, SourceColumns.EXEC_ID].values,
                    index=self.source_frame.index,
                    columns=[add_prefix(ModelPrefix.ORDER, OrderColumns.ID)],
                ),
                pd.DataFrame(
                    data=self.source_frame.loc[:, SourceColumns.EXEC_ID].values,
                    index=self.source_frame.index,
                    columns=[add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID)],
                ),
            ],
            axis=1,
        )

    def _is_discretionary(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_iceberg(self):
        """Not Implemented"""

    def _is_repo(self):
        """Not Implemented"""

    def _is_synthetic(self):
        """Not Implemented"""

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        """Not Implemented"""

    def _market_identifiers(self) -> pd.DataFrame:
        """Populates OrderColumns.MARKET_IDENTIFIERS"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=MergeMarketIdentifiersParams(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """
        Populates OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT
        """
        return InstrumentIdentifiers.process(
            source_frame=self.pre_process_df,
            params=InstrumentIdentifiersParams(
                isin_attribute=self.__security_id__,
                retain_task_inputs=True,
            ),
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Populates OrderColumns.MARKET_IDENTIFIERS_PARTIES"""
        map_executing_entity_from_list_item_params = MapFromListItemsParams(
            source_attribute_for_pattern_search=SourceColumns.PARTY_ROLE,
            source_attribute_for_mapping=SourceColumns.PARTY_ID,
            regex_pattern="^1$",
            target_attribute=self.__executing_entity__,
        )
        executing_entity = MapConditionalAttributeFromListItems.process(
            source_frame=self.source_frame,
            params=map_executing_entity_from_list_item_params,
        )
        executing_entity = PartyPrefix.ID + executing_entity[self.__executing_entity__]

        map_trader_from_list_item_params = MapFromListItemsParams(
            source_attribute_for_pattern_search=SourceColumns.PARTY_ROLE,
            source_attribute_for_mapping=SourceColumns.PARTY_ID,
            regex_pattern="^11$",
            target_attribute=self.__trader_pre_routing_flip__,
        )
        trader = MapConditionalAttributeFromListItems.process(
            source_frame=self.source_frame, params=map_trader_from_list_item_params
        )

        map_execution_within_firm_from_list_item_params = MapFromListItemsParams(
            source_attribute_for_pattern_search=SourceColumns.PARTY_ROLE,
            source_attribute_for_mapping=SourceColumns.PARTY_ID,
            regex_pattern="^12$",
            target_attribute=self.__execution_within_firm_id_pre_routing_flip__,
        )
        execution_within_firm_id = MapConditionalAttributeFromListItems.process(
            source_frame=self.source_frame,
            params=map_execution_within_firm_from_list_item_params,
        )

        trader_execution_within_firm_temp_df = pd.concat(
            [
                trader,
                execution_within_firm_id,
                self.target_df.loc[:, OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY],
            ],
            axis=1,
        )

        query_destination_trade = self.__destination_trade__.replace(
            r"(", r"\("
        ).replace(r")", r"\)")
        destination_trade_query = f"`{OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY}`.str.fullmatch('{query_destination_trade}', case=False, na=False)"
        trader = MapConditional.process(
            source_frame=trader_execution_within_firm_temp_df,
            params=MapConditionalParams(
                target_attribute=self.__trader__,
                cases=[
                    Case(
                        query=f"~({destination_trade_query})",
                        attribute=self.__trader_pre_routing_flip__,
                    ),
                    Case(
                        query=destination_trade_query,
                        attribute=self.__execution_within_firm_id_pre_routing_flip__,
                    ),
                ],
            ),
        )
        execution_within_firm_id = MapConditional.process(
            source_frame=trader_execution_within_firm_temp_df,
            params=MapConditionalParams(
                target_attribute=self.__execution_within_firm_id__,
                cases=[
                    Case(
                        query=f"~({destination_trade_query})",
                        attribute=self.__execution_within_firm_id_pre_routing_flip__,
                    ),
                    Case(
                        query=destination_trade_query,
                        attribute=self.__trader_pre_routing_flip__,
                    ),
                ],
            ),
        )

        trader = PartyPrefix.ID + trader[self.__trader__]
        execution_within_firm_id = (
            PartyPrefix.ID + execution_within_firm_id[self.__execution_within_firm_id__]
        )

        return GenericOrderPartyIdentifiers.process(
            source_frame=pd.concat(
                [
                    executing_entity,
                    trader,
                    execution_within_firm_id,
                    self._client(),
                    self._counterparty(),
                ],
                axis=1,
            ),
            params=PartyIdentifiersParams(
                executing_entity_identifier=self.__executing_entity__,
                trader_identifier=self.__trader__,
                execution_within_firm_identifier=self.__execution_within_firm_id__,
                client_identifier=self.__client__,
                counterparty_identifier=self.__counterparty__,
                buyer_identifier=self.__counterparty__,
                seller_identifier=self.__client__,
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _client(self) -> pd.DataFrame:
        """Maps clientIdentifiers.client needed for PartyIdentifiers task"""
        client = pd.DataFrame(index=self.source_frame.index, columns=[self.__client__])

        mask1 = self.source_frame[SourceColumns.FF_7014] == "21"
        mask2 = self.source_frame[SourceColumns.FF_7014] == "3"
        mask3 = ~(mask1 | mask2)

        masks = [mask1, mask2, mask3]
        patterns = ["^1$", "^13$", "^73$"]

        for mask, pattern in zip(masks, patterns):
            map_clients_from_list_item_params = MapFromListItemsParams(
                source_attribute_for_pattern_search=SourceColumns.PARTY_ROLE,
                source_attribute_for_mapping=SourceColumns.PARTY_ID,
                regex_pattern=pattern,
                target_attribute=self.__client__,
            )
            client_id = MapConditionalAttributeFromListItems.process(
                source_frame=self.source_frame[mask],
                params=map_clients_from_list_item_params,
            )
            client[mask] = client_id

        client = PartyPrefix.ID + client[self.__client__]
        return client

    def _counterparty(self) -> pd.DataFrame:
        """Maps counterparty needed for PartyIdentifiers task"""
        counterparty = pd.DataFrame(
            index=self.source_frame.index, columns=[self.__counterparty__]
        )

        mask1 = self.source_frame[SourceColumns.FF_7014] == "21"
        mask2 = self.source_frame[SourceColumns.FF_7014] == "3"
        mask3 = ~(mask1 | mask2)

        masks = [mask1, mask2, mask3]
        patterns = ["^13$", "^1", "^73$"]

        for mask, pattern in zip(masks, patterns):
            map_counterparty_from_list_item_params = MapFromListItemsParams(
                source_attribute_for_pattern_search=SourceColumns.PARTY_ROLE,
                source_attribute_for_mapping=SourceColumns.PARTY_ID,
                regex_pattern=pattern,
                target_attribute=self.__counterparty__,
            )
            counterparty_id = MapConditionalAttributeFromListItems.process(
                source_frame=self.source_frame[mask],
                params=map_counterparty_from_list_item_params,
            )
            counterparty[mask] = counterparty_id

        counterparty = PartyPrefix.ID + counterparty[self.__counterparty__]
        return counterparty

    def _meta_model(self) -> pd.DataFrame:
        """Returns a data frame containing _order.__meta_model__ and _orderState.__meta_model__.
        The columns are populated with the static values Order and OrderState respectively"""
        return pd.concat(
            [
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=MapStaticParams(
                        target_value="Order",
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.META_MODEL,
                        ),
                    ),
                ),
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=MapStaticParams(
                        target_value="OrderState",
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.META_MODEL,
                        ),
                    ),
                ),
            ],
            axis=1,
        )

    def _order_class(self) -> pd.DataFrame:
        """
        Populates OrderColumns.ORDER_CLASS from SourceColumns.SENDER_LOCATION_ID,
            if null, populates with 'Regular Trade'
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=OrderColumns.ORDER_CLASS,
                cases=[
                    Case(
                        query=f"`{SourceColumns.SENDER_LOCATION_ID}`.notnull()",
                        attribute=SourceColumns.SENDER_LOCATION_ID,
                    ),
                    Case(
                        query=f"`{SourceColumns.SENDER_LOCATION_ID}`.isnull()",
                        value="Regular Trade",
                    ),
                ],
            ),
        )

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        """Not implemented"""

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """Populates OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE using SourceColumns.EXEC_ID"""
        return pd.DataFrame(
            data=self.source_frame[SourceColumns.EXEC_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE],
        )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """Populates OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE using SourceColumns.ORDER_ID"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORDER_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
        )

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        """Not implemented"""

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        """Populates OrderColumns.ORDER_IDENTIFIERS_PARENT_ORDER_ID from SourceColumns.SECONDARY_ORDER_ID"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.SECONDARY_ORDER_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_PARENT_ORDER_ID],
        )

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        """Not implemented"""

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        """Not implemented"""

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        """Populates OrderColumns.ORDER_IDENTIFIERS_TRADING_VENUE_TRANSACTION_ID_CODE from SourceColumns.FF_1903"""
        return pd.DataFrame(
            data=self.source_frame[SourceColumns.FF_1903].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_TRADING_VENUE_TRANSACTION_ID_CODE],
        )

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """Populates OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO using SourceColumns.ORDER_ID"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORDER_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO],
        )

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        """Not implemented"""

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """Returns a df with the col priceFormingData.initialQuantity"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORDER_QTY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY],
        )

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        """Not implemented"""

    def _price_forming_data_price(self) -> pd.DataFrame:
        """Populates OrderColumns.PRICE_FORMING_DATA_PRICE from SourceColumns.LAST_PX"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LAST_PX].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_PRICE],
        )

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        """Not implemented"""

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        """Not implemented"""

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """Returns a df with the col priceFormingData.remainingQuantity"""
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.source_frame.loc[:, SourceColumns.LEAVES_QTY].values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
                        )
                    ],
                ),
                pd.DataFrame(
                    data=self.source_frame.loc[:, SourceColumns.ORDER_QTY].values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """Returns a df with the col priceFormingData.tradedQuantity"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LAST_QTY].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                )
            ],
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """Populates OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO using SourceColumns.ORDER_ID"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORDER_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO],
        )

    def _source_index(self) -> pd.DataFrame:
        """Not implemented"""

    def _source_key(self) -> pd.DataFrame:
        """Returns a df with the col sourceKey"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=StaticFields.FILE_URL,
                target_attribute=OrderColumns.SOURCE_KEY,
            ),
            auditor=self.auditor,
        )

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        """Not implemented"""

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        """Not implemented"""

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        """Not implemented"""

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        """Not implemented"""

    def _timestamps_order_received(self) -> pd.DataFrame:
        """Populates OrderColumns.TIMESTAMPS_ORDER_RECEIVED from TrdRegTimestamp"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, self.__trd_reg_timestamp__].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_RECEIVED],
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """Populates OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED from TrdRegTimestamp"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, self.__trd_reg_timestamp__].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED],
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """Populates OrderColumns.TIMESTAMPS_ORDER_SUBMITTED from TrdRegTimestamp"""
        voice_mask = self.source_frame[SourceColumns.SENDER_LOCATION_ID] == "Voice"
        trd_reg_timestamp_col = "trd_reg_timestamp"
        map_trd_reg_timestamp_from_list_item_params = MapFromListItemsParams(
            source_attribute_for_pattern_search=SourceColumns.TRD_REG_TIMESTAMP_TYPE,
            source_attribute_for_mapping=SourceColumns.TRD_REG_TIMESTAMP,
            regex_pattern="^10$",
            target_attribute=trd_reg_timestamp_col,
        )

        trd_reg_timestamp_where_type_10 = MapConditionalAttributeFromListItems.process(
            source_frame=self.source_frame.loc[~voice_mask, :],
            params=map_trd_reg_timestamp_from_list_item_params,
        )
        not_voice_timestamps = pd.Series(name=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED)
        if not trd_reg_timestamp_where_type_10.empty:
            not_voice_timestamps = ConvertDatetime.process(
                trd_reg_timestamp_where_type_10,
                params=ConvertDatetimeParams(
                    source_attribute=trd_reg_timestamp_col,
                    target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                    convert_to=ConvertTo.DATETIME,
                ),
            ).loc[:, OrderColumns.TIMESTAMPS_ORDER_SUBMITTED]
        voice_timestamps = pd.Series(
            data=self.pre_process_df.loc[voice_mask, self.__trd_reg_timestamp__].values,
            index=self.pre_process_df.loc[voice_mask, self.__trd_reg_timestamp__].index,
            name=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
        )
        return pd.concat([not_voice_timestamps, voice_timestamps])

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """Returns a df with col timestamps.tradingDateTime"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, self.__trd_reg_timestamp__].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TIMESTAMPS_TRADING_DATE_TIME],
        )

    def _timestamps_validity_period(self) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Populates OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR from SourceColumns.SIDE"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.SIDE,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                value_map={"1": "BUYI", "2": "SELL"},
            ),
            auditor=self.auditor,
        )

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_id(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_price(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_PRICE from OrderColumns.PRICE_FORMING_DATA_PRICE"""
        return MapAttribute.process(
            source_frame=self.target_df,
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_price_average(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.AVG_PX].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_AVERAGE],
        )

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY from SourceColumns.CURRENCY"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.CURRENCY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY],
        )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION from SourceColumns.PRICE_TYPE"""
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION,
                cases=[
                    Case(
                        query=f"`{SourceColumns.PRICE_TYPE}` == 2",
                        value=PriceNotation.MONE.value,
                    ),
                    Case(
                        query=f"`{SourceColumns.PRICE_TYPE}` == 4",
                        value=PriceNotation.PERC.value,
                    ),
                    Case(
                        query=f"`{SourceColumns.PRICE_TYPE}` == 1",
                        value=PriceNotation.PERC.value,
                    ),
                    Case(
                        query=f"`{SourceColumns.PRICE_TYPE}` == 9",
                        value=PriceNotation.YIEL.value,
                    ),
                    Case(
                        query=f"`{SourceColumns.PRICE_TYPE}` == 6",
                        value=PriceNotation.BAPO.value,
                    ),
                ],
            ),
        )

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.quantity"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LAST_QTY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY],
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY from SourceColumns.CURRENCY"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.CURRENCY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY],
        )

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION with QtyType"""
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
                cases=[
                    Case(
                        query=f"`{SourceColumns.QTY_TYPE}` == 0",
                        value=QuantityNotation.UNIT.value,
                    )
                ],
            ),
        )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_SETTLEMENT_DATE from SourceColumns.SETTL_DATE"""
        return ConvertDatetime.process(
            self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.SETTL_DATE,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_SETTLEMENT_DATE,
                convert_to=ConvertTo.DATE,
            ),
        )

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY from SourceColumns.LAST_CAPACITY"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.LAST_CAPACITY,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY,
                value_map=TRADING_CAPACITY_MAP,
            ),
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.transactionDetailsTradingDateTime"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, self.__trd_reg_timestamp__].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME],
        )

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE from SourceColumns.LAST_MKT"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LAST_MKT].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE],
        )

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_venue(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_VENUE from SourceColumns.LAST_MKT"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LAST_MKT].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_VENUE],
        )

    def _temp_newo_in_file(self) -> pd.DataFrame:
        """
        Temporary column for RemoveDuplicateNEWO
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=TempColumns.NEWO_IN_FILE,
                cases=[
                    Case(
                        query=f"`{SourceColumns.ORD_STATUS}` == 0",
                        value=True,
                    ),
                    Case(
                        query=f"`{SourceColumns.ORD_STATUS}` != 0",
                        value=False,
                    ),
                ],
            ),
        )

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""
