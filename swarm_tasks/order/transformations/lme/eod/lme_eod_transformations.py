import os
from typing import Optional

import pandas as pd
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.reference import OrderRecordType
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.generic.get_tenant_lei import Params as ParamsGetTenantLEI
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.datetime.join_date_and_time import JoinDateAndTimeFormat
from swarm_tasks.transform.datetime.join_date_and_time import (
    Params as ParamsJoinDateAndTimeFormat,
)
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)
from swarm_tasks.utilities.static import Delimiters


class SourceColumns:
    ALLEDED_BUY_SELL = "ALLEGEDB/S"
    BUY_SELL = "B/S"
    CLIENT_CODE = "CLIENTCODE"
    CONTRACT_CLASSIFICATION = "CONTRACTCLASSIFICATION"
    CONTRA_NOMINATED_CLEARER = "CONTRANOMINATEDCLEARER"
    COMMODITY = "COMMODITY"
    COUNTERPARTY = "COUNTERYPARTY"
    CURRENCY = "CURRENCY"
    DATA_ORIGIN = "DATAORIGIN"
    LCH_MATCH_NUMBER = "LCHMATCHNUMBER"
    LCH_PRIVATE_REFERENCE = "LCHPRIVATEREFERENCE"
    MATCH_NO = "MATCHNO"
    MATCH_SLIP_ID = "MATCHSLIPID"
    MATCHING_STATUS = "MATCHINGSTATUS"
    PRICE = "PRICE"
    PRICE_CODE = "PRICECODE"
    PRICE_TYPE = "PRICETYPE"
    PROMPT_DATE = "PROMPTDATE"
    PUBLIC_REFERENCE_NUMBER = "PUBLICREFERENCE(LONG)"
    SELECT_MATCH_NO = "SELECTMATCHNO"
    STRIKE_PRICE = "STRIKEPRICE"
    TRADE_CATEGORY = "TRADECATEGORY"
    TRADE_DATE = "TRADEDATE"
    TRADE_PREMIUM = "TRADEPREMIUM"
    TRADE_TIME = "TRADETIME"
    TYPE = "TYPE"
    VENUE = "VENUE"
    VOLUME = "VOLUME"


class TempColumns:
    ASSET_TYPE = "__asset_type__"
    COUNTERPARTY = "__counterparty__"
    CURRENCY = "__currency__"
    EXECUTING_ENTITY_WITH_LEI = "__executing_entity_with_lei__"
    EXPIRY_DATE = "__expiry_date__"
    ID = "__id__"
    ID_WITHOUT_CARRIES = "__id_without_carries__"
    ID_WITH_CARRIES = "__id_with_carries__"
    OPTION_TYPE = "__option_type__"
    SELLER = "__selller__"
    VENUE = "__venue__"


class LmeEodTransformations(AbstractOrderTransformations):
    def _pre_process(self):
        self.pre_process_df.loc[:, TempColumns.ID] = self._get_id()
        self.pre_process_df.loc[:, TempColumns.ASSET_TYPE] = self._get_asset_type()

    def process(self) -> pd.DataFrame:
        self.pre_process()
        self.buy_sell()
        self.data_source_name()
        self.date()
        self.execution_details_buy_sell_indicator()
        self.execution_details_order_status()
        self.execution_details_order_type()
        self.execution_details_outgoing_order_addl_info()
        self.execution_details_trading_capacity()
        self.hierarchy()
        self.id()
        self.transaction_details_ultimate_venue()
        self.transaction_details_venue()
        self.market_identifiers_instrument()  # needs output from transaction_details_ultimate_venue()
        self.market_identifiers_parties()
        self.market_identifiers()
        self.meta_model()
        self.order_class()
        self.order_identifiers_aggregated_order_id_code()
        self.order_identifiers_internal_order_id_code()
        self.order_identifiers_order_id_code()
        self.order_identifiers_trading_venue_transaction_id_code()
        self.order_identifiers_transaction_ref_no()
        self.price_forming_data_initial_quantity()
        self.price_forming_data_price()
        self.price_forming_data_traded_quantity()
        self.report_details_transaction_ref_no()
        self.source_index()
        self.source_key()
        self.transaction_details_trading_date_time()
        self.timestamps_order_received()
        self.timestamps_order_status_updated()
        self.timestamps_order_submitted()
        self.timestamps_trading_date_time()
        self.transaction_details_basket_id()
        self.transaction_details_buy_sell_indicator()
        self.transaction_details_price()
        self.transaction_details_price_currency()
        self.transaction_details_price_notation()
        self.transaction_details_quantity()
        self.transaction_details_quantity_notation()
        self.transaction_details_record_type()
        self.transaction_details_trading_capacity()
        self.post_process()
        return self.target_df

    def _post_process(self):
        self.target_df.loc[:, TempColumns.ASSET_TYPE] = self.pre_process_df.loc[
            :, TempColumns.ASSET_TYPE
        ]
        self.target_df.loc[:, TempColumns.CURRENCY] = self.source_frame.loc[
            :, SourceColumns.CURRENCY
        ]

    def _buy_sell(self) -> pd.DataFrame:
        """Returns a data frame containing _order.buySell and _orderState.buySell"""
        buy_sell_series = self.source_frame.loc[
            :, [SourceColumns.BUY_SELL, SourceColumns.ALLEDED_BUY_SELL]
        ].apply(
            lambda x: self._get_buy_sell(
                b_s=x[SourceColumns.BUY_SELL],
                alleged_b_s=x[SourceColumns.ALLEDED_BUY_SELL],
                buy_sell_indicator=False,
            ),
            axis=1,
        )
        order_buy_sell = pd.DataFrame(
            data=buy_sell_series.values,
            index=self.source_frame.index,
            columns=[
                add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.BUY_SELL)
            ],
        )
        order_state_buy_sell = pd.DataFrame(
            data=buy_sell_series.values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE, attribute=OrderColumns.BUY_SELL
                )
            ],
        )
        return pd.concat([order_buy_sell, order_state_buy_sell], axis=1)

    def _data_source_name(self) -> pd.DataFrame:
        """Returns a data frame containing dataSourceName.
        This is populated with the static value 'LME'"""
        return pd.DataFrame(
            data="LME",
            index=self.target_df.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """Returns a data frame containing date. This is populated from SourceColumns.TRADE_DATE"""
        return ConvertDatetime.process(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.TRADE_DATE,
                source_attribute_format="%d/%m/%Y",
                target_attribute=OrderColumns.DATE,
                convert_to="date",
            ),
        )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing"""
        buy_sell_series = self.source_frame.loc[
            :, [SourceColumns.BUY_SELL, SourceColumns.ALLEDED_BUY_SELL]
        ].apply(
            lambda x: self._get_buy_sell(
                b_s=x[SourceColumns.BUY_SELL],
                alleged_b_s=x[SourceColumns.ALLEDED_BUY_SELL],
                buy_sell_indicator=True,
            ),
            axis=1,
        )
        return pd.DataFrame(
            data=buy_sell_series.values,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_order_status(self) -> pd.DataFrame:
        """Returns a data frame containing _order.executionDetails.orderStatus and
        _orderState.executionDetails.orderStatus"""
        order_execution_details_order_status = pd.DataFrame(
            data=OrderStatus.NEWO.value,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                )
            ],
        )
        cases = [
            {
                "query": f"`{SourceColumns.MATCHING_STATUS}`.str.fullmatch('DE', case=False)",
                "value": OrderStatus.CAME.value,
            },
            {
                "query": f"`{SourceColumns.MATCHING_STATUS}`.str.fullmatch('MT', case=False)",
                "value": OrderStatus.FILL.value,
            },
        ]
        order_state_execution_details_order_status = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                ),
                cases=cases,
            ),
        )
        return pd.concat(
            [
                order_execution_details_order_status,
                order_state_execution_details_order_status,
            ],
            axis=1,
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.orderType.
        This is populated with the static value 'Market'"""
        return pd.DataFrame(
            data="Market",
            index=self.target_df.index,
            columns=[OrderColumns.EXECUTION_DETAILS_ORDER_TYPE],
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.outgoingOrderAddlInfo."""
        cols_used = [
            SourceColumns.TRADE_PREMIUM,
            SourceColumns.DATA_ORIGIN,
            SourceColumns.LCH_PRIVATE_REFERENCE,
            SourceColumns.VENUE,
            SourceColumns.PRICE_CODE,
            SourceColumns.PRICE_TYPE,
        ]
        return ConcatAttributes.process(
            source_frame=self.source_frame.loc[:, cols_used],
            params=ParamsConcatAttributes(
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                source_attributes=cols_used,
                delimiter=Delimiters.COMMA_SPACE,
            ),
        )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_stop_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        return pd.DataFrame(
            data="DEAL",
            index=self.target_df.index,
            columns=[OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY],
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _financing_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _hierarchy(self) -> pd.DataFrame:
        """Returns a data frame containing hierarchy"""
        return pd.DataFrame(
            data="Standalone",
            index=self.target_df.index,
            columns=[OrderColumns.HIERARCHY],
        )

    def _id(self) -> pd.DataFrame:
        """Returns a data frame containing _order.id and _orderState.id"""
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, TempColumns.ID].values,
                    index=self.pre_process_df.index,
                    columns=[
                        add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID)
                    ],
                ),
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, TempColumns.ID].values,
                    index=self.pre_process_df.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER_STATE, attribute=OrderColumns.ID
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _is_discretionary(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_iceberg(self):
        """Not Implemented"""

    def _is_repo(self):
        """Not Implemented"""

    def _is_synthetic(self):
        """Not Implemented"""

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        """Not Implemented"""

    def _market_identifiers(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_instrument() and _market_identifiers_parties() have been
        called earlier"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.instrument by calling InstrumentIdentifiers."""
        temp_instrument_df = pd.DataFrame(index=self.source_frame.index)
        temp_instrument_df.loc[:, TempColumns.EXPIRY_DATE] = ConvertDatetime.process(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.PROMPT_DATE,
                source_attribute_format="%d/%m/%Y",
                target_attribute=TempColumns.EXPIRY_DATE,
                convert_to="date",
            ),
        )[TempColumns.EXPIRY_DATE]

        option_type_map = {"TC": "CALL", "TP": "PUT"}

        temp_instrument_df.loc[:, TempColumns.OPTION_TYPE] = (
            self.source_frame.loc[:, SourceColumns.TYPE]
            .map(option_type_map)
            .fillna(pd.NA)
        )

        temp_instrument_df = pd.concat(
            [
                temp_instrument_df,
                self.source_frame.loc[
                    :,
                    [
                        SourceColumns.COMMODITY,
                        SourceColumns.CURRENCY,
                        SourceColumns.STRIKE_PRICE,
                    ],
                ],
                self.target_df.loc[
                    :, [OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE]
                ],
                self.pre_process_df.loc[:, [TempColumns.ASSET_TYPE]],
            ],
            axis=1,
        )

        return InstrumentIdentifiers.process(
            source_frame=temp_instrument_df,
            params=ParamsInstrumentIdentifiers(
                currency_attribute=SourceColumns.CURRENCY,
                expiry_date_attribute=TempColumns.EXPIRY_DATE,
                option_strike_price_attribute=SourceColumns.STRIKE_PRICE,
                underlying_symbol_attribute=SourceColumns.COMMODITY,
                asset_class_attribute=TempColumns.ASSET_TYPE,
                option_type_attribute=TempColumns.OPTION_TYPE,
                venue_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
            ),
            auditor=self.auditor,
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.parties by calling PartyIdentifiers"""
        executing_entity = self._get_executing_entity()
        client = PartyPrefix.ID + self.source_frame.loc[:, SourceColumns.CLIENT_CODE]
        counterparty = self.source_frame.loc[
            :, [SourceColumns.COUNTERPARTY, SourceColumns.CONTRA_NOMINATED_CLEARER]
        ].apply(
            lambda x: x[SourceColumns.COUNTERPARTY]
            if not pd.isna(x[SourceColumns.COUNTERPARTY])
            else x[SourceColumns.CONTRA_NOMINATED_CLEARER],
            axis=1,
        )
        counterparty = pd.Series(
            data=(PartyPrefix.ID + counterparty).values, name=TempColumns.COUNTERPARTY
        )
        seller = pd.Series(
            data=f"{PartyPrefix.ID}LME",
            name=TempColumns.SELLER,
            index=self.source_frame.index,
        )
        parties_source_frame = pd.concat(
            [
                client,
                counterparty,
                seller,
                executing_entity,
                self.target_df.loc[
                    :, [OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR]
                ],
            ],
            axis=1,
        )
        return GenericOrderPartyIdentifiers.process(
            source_frame=parties_source_frame,
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                counterparty_identifier=TempColumns.COUNTERPARTY,
                client_identifier=SourceColumns.CLIENT_CODE,
                executing_entity_identifier=TempColumns.EXECUTING_ENTITY_WITH_LEI,
                buyer_identifier=TempColumns.EXECUTING_ENTITY_WITH_LEI,
                seller_identifier=TempColumns.SELLER,
                trader_identifier=SourceColumns.CLIENT_CODE,
                buy_sell_side_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                use_buy_mask_for_buyer_seller=True,
                create_fallback_fields=True,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """Returns a data frame containing _order.__meta_model__ and _orderState.__meta_model__.
        The columns are populated with the static values Order and OrderState respectively"""
        return pd.DataFrame(
            data=[["Order", "OrderState"]],
            index=self.source_frame.index,
            columns=[
                add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.META_MODEL),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.META_MODEL,
                ),
            ],
        )

    def _order_class(self) -> pd.DataFrame:
        """Populates orderClass from SourceColumns.TRADE_CATEGORY."""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.TRADE_CATEGORY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_CLASS],
        )

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        """Returns a df with the col orderIdentifiers.aggregatedOrderId"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LCH_MATCH_NUMBER].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID],
        )

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """Returns a df with the col orderIdentifiers.internalOrderIdCode"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.PUBLIC_REFERENCE_NUMBER].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE],
        )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.orderIdCode"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.ID].values,
            index=self.target_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
        )

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        """Not implemented"""

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        """Returns a dataframe with the col orderIdentifiers.tradingVenueTransactionIdCode"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.SELECT_MATCH_NO].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_TRADING_VENUE_TRANSACTION_ID_CODE],
        )

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a df with the col orderIdentifiers.transactionRefNo"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.ID].values,
            index=self.target_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO],
        )

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """Returns a df with the col priceFormingData.initialQuantity"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.VOLUME].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY],
        )

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price(self) -> pd.DataFrame:
        """Returns a df with the col priceFormingData.price"""
        # Since the SourceColumns.PRICE might contain commas, it's required to remove them and then convert
        # to float
        price_col = (
            self.source_frame.loc[:, SourceColumns.PRICE]
            .str.replace(",", "")
            .astype("float")
        )
        cases = [
            {
                "query": f"`{SourceColumns.TYPE}`.str.fullmatch('F', case=False)",
                "attribute": SourceColumns.PRICE,
            },
            {
                "query": f"~`{SourceColumns.TYPE}`.str.fullmatch('F', case=False)",
                "attribute": SourceColumns.TRADE_PREMIUM,
            },
        ]
        source_frame = pd.concat(
            [
                price_col,
                self.source_frame.loc[
                    :, [SourceColumns.TYPE, SourceColumns.TRADE_PREMIUM]
                ],
            ],
            axis=1,
        )
        return MapConditional.process(
            source_frame=source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                cases=cases,
            ),
        )

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """Returns a df with the col priceFormingData.tradedQuantity"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.VOLUME].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY],
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a df with the col reportDetails.transactionRefNo"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.ID].values,
            index=self.target_df.index,
            columns=[OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO],
        )

    def _source_index(self) -> pd.DataFrame:
        """ "Returns a data frame containing the sourceIndex column"""
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_INDEX],
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceKey column from the SWARM_FILE_URL"""
        return pd.DataFrame(
            data=os.getenv("SWARM_FILE_URL"),
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_KEY],
        )

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_order_received(self) -> pd.DataFrame:
        """Returns a df with col timestamps.orderReceived"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_RECEIVED],
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """Returns a df with col timestamps.orderStatusUpdated"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED],
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """Returns a df with col timestamps.orderSubmitted"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_SUBMITTED],
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """Returns a df with col timestamps.tradingDateTime"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TIMESTAMPS_TRADING_DATE_TIME],
        )

    def _timestamps_validity_period(self) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        """Populates the basket id from SourceColumns.PRICE_TYPE]"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.PRICE_TYPE].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_BASKET_ID],
        )

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.buySellIndicator"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.price"""
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.PRICE_FORMING_DATA_PRICE].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE],
        )

    def _transaction_details_price_average(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.priceCurrency"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.CURRENCY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY],
        )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.priceNotation"""
        return pd.DataFrame(
            data=PriceNotation.MONE.value,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION],
        )

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.quantity"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.VOLUME].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY],
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.quantityNotation"""
        return pd.DataFrame(
            data=QuantityNotation.UNIT.value,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION],
        )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.recordType"""
        return pd.DataFrame(
            data=OrderRecordType.MARKET_SIDE.value,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE],
        )

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.tradingCapacity"""
        return pd.DataFrame(
            data="DEAL",
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.tradingDateTime as the only column"""
        return JoinDateAndTimeFormat.process(
            source_frame=self.source_frame,
            params=ParamsJoinDateAndTimeFormat(
                source_date_attribute=SourceColumns.TRADE_DATE,
                source_time_attribute=SourceColumns.TRADE_TIME,
                source_format="%d/%m/%Y%H:%M",
                target_attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
            ),
        )

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.ultimateVenue"""
        return pd.DataFrame(
            data="XLME",
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE],
        )

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_venue(self) -> pd.DataFrame:
        return pd.DataFrame(
            data="XLME",
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_VENUE],
        )

    def _get_id(self) -> pd.Series:
        """This method returns a series with the id of the orders. The reason this is being stored in
        pre_process is because this is being reused to populate multiple columns"""
        id_without_carries = ConcatAttributes.process(
            source_frame=self.source_frame.loc[
                :, [SourceColumns.MATCH_NO, SourceColumns.MATCH_SLIP_ID]
            ],
            params=ParamsConcatAttributes(
                target_attribute=TempColumns.ID_WITHOUT_CARRIES,
                source_attributes=[
                    SourceColumns.MATCH_NO,
                    SourceColumns.MATCH_SLIP_ID,
                ],
                delimiter=Delimiters.PIPE,
            ),
        )
        id_with_carries = pd.DataFrame(
            data=(
                "CARRIES|" + id_without_carries.loc[:, TempColumns.ID_WITHOUT_CARRIES]
            ).values,
            index=self.source_frame.index,
            columns=[TempColumns.ID_WITH_CARRIES],
        )

        ids_df = pd.concat(
            [
                id_without_carries,
                id_with_carries,
                self.source_frame.loc[:, SourceColumns.CONTRACT_CLASSIFICATION],
            ],
            axis=1,
        )
        cases = [
            {
                "query": f"`{SourceColumns.CONTRACT_CLASSIFICATION}`.str.fullmatch('CARRIES', case=False)",
                "attribute": TempColumns.ID_WITH_CARRIES,
            },
            {
                "query": f"~`{SourceColumns.CONTRACT_CLASSIFICATION}`.str.fullmatch('CARRIES', case=False)",
                "attribute": TempColumns.ID_WITHOUT_CARRIES,
            },
        ]
        return MapConditional.process(
            source_frame=ids_df,
            params=ParamsMapConditional(
                target_attribute=TempColumns.ID,
                cases=cases,
            ),
        )[TempColumns.ID]

    @staticmethod
    def _get_buy_sell(
        b_s: Optional[str], alleged_b_s: Optional[str], buy_sell_indicator: bool
    ):
        """
        Returns buy_sell value. If buy_sell_indicator is True, this method returns BUYI/SELL
        else it returns 1/2 for BUY & SELL respectively
        :param b_s: the source buy/sell value. Expected to be either B or S or null
        :param alleged_b_s: to be used if b_s is null.
        :param buy_sell_indicator: boolean value to determine if request is for buy_sell or buy_sell_indicator
        :return:
        """
        BUYI = "BUYI" if buy_sell_indicator else "1"
        SELL = "SELL" if buy_sell_indicator else "2"
        buy_sell_mapping = {"B": BUYI, "S": SELL}
        if not pd.isna(b_s):
            return buy_sell_mapping.get(b_s.upper(), pd.NA)
        if not pd.isna(alleged_b_s):
            return buy_sell_mapping.get(alleged_b_s, pd.NA)
        return pd.NA

    def _get_executing_entity(self) -> pd.DataFrame:
        """Gets the executing entity value to be used in PartyIdentifiers from the
        AccountFirm record"""
        return GetTenantLEI.process(
            source_frame=self.source_frame,
            params=ParamsGetTenantLEI(
                target_column_prefix=PartyPrefix.LEI,
                target_lei_column=TempColumns.EXECUTING_ENTITY_WITH_LEI,
            ),
        )

    def _get_asset_type(self):
        asset_map = {"F": "future", "TC": "option", "TP": "option"}
        return self.source_frame.loc[:, SourceColumns.TYPE].map(asset_map).fillna(pd.NA)

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""
