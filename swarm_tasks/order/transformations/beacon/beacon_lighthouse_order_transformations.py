import os

import pandas as pd
from se_core_tasks.currency.convert_minor_to_major import CastTo
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_attribute import CastTo as CastToMapAttribute
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.mifid2 import Buy<PERSON>ellIndicator
from se_elastic_schema.static.mifid2 import OptionType
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_elastic_schema.static.mifid2 import ValidityPeriod
from se_elastic_schema.static.reference import StrikePriceType
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)
from swarm_tasks.utilities.static import Delimiters
from swarm_tasks.utilities.static import SWARM_FILE_URL


class SourceColumns:
    ACCOUNT = "ACCOUNT"
    BBSYMBOL = "BBSYMBOL"
    BROKER_CODE = "BROKER_CODE"
    CLIENTORDERID = "CLIENTORDERID"
    CUMQTY = "CUMQTY"
    CURRENCY = "CURRENCY"
    EFFECTIVE_TIME = "EFFECTIVE_TIME"
    EXCHANGE_NAME = "EXCHANGE_NAME"
    EXPIRE_DATE = "EXPIRE_DATE"
    EXECUTION_TIME = "EXECUTION_TIME"
    ISCFD = "ISCFD"
    ISIN = "ISIN"
    LAST_MARKET = "LAST_MARKET"
    LEAVES_QTY = "LEAVES_QTY"
    LIMIT_PRICE = "LIMIT_PRICE"
    LOCATION = "LOCATION"
    MANAGER = "MANAGER"
    MATURITY_DAY = "MATURITY_DAY"
    MATURITY_MONTH_YEAR = "MATURITY_MONTH_YEAR"
    MIC = "MIC"
    ORDERQTY = "ORDERQTY"
    ORDER_STATUS = "ORDER_STATUS"
    ORDER_TYPE = "ORDER_TYPE"
    ORDER_TIME = "ORDER_TIME"
    PRICE = "PRICE"
    PRICE_NOTATION = "PRICE_NOTATION"
    PUTORCALL = "PUTORCALL"
    QUANTITY_NOTATION = "QUANTITY_NOTATION"
    SECURITY_NAME = "SECURITY_NAME"
    SECURITY_TYPE = "SECURITY_TYPE"
    SIDE = "SIDE"
    STRIKE_PRICE = "STRIKE_PRICE"
    TIME_IN_FORCE = "TIME_IN_FORCE"
    TRADE_DATE = "TRADE_DATE"
    TRADE_ID = "TRADE_ID"
    TRADER = "TRADER"
    TRANSACT_TIME = "TRANSACT_TIME"
    UNDERLYING_ISIN = "UNDERLYING_ISIN"
    UNDERLYING_TICKER = "UNDERLYING_TICKER"


class TempColumns:
    """
    Temporary columns used in the Transformation tasks
    only. These columns aren't sent to the downstream
    tasks.
    """

    BUY_SELL = "__buysell__"
    EXECUTING_ENTITY_WITH_LEI = "__executing_entity_with_lei__"
    EXECUTION_TIME = "__execution_time__"
    MATURITY_DATE = "__maturity_date__"
    MATURITY_DAY = "__maturity_day__"
    MATURITY_MONTH = "__maturity_date__"
    ORDER_TIME = "__order_time__"
    TRANSACT_TIME = "__transact_datetime__"
    VALIDITY_PERIOD = "__validity_period__"


class PostProcessTempColumns:
    """
    Temp columns which are inserted into the target_df (also retained in pre_process_df
    as needed) but need to be dropped in the bundle.
    """

    ASSET_CLASS = "__asset_class__"
    CURRENCY = "__currency__"
    EXPIRY = "__expiry__"
    EXPIRY_DATE = "__expiry_date__"
    INPUT_ASSET_CLASS = "__input_asset_class__"
    INPUT_VENUE = "__input_venue__"
    NEWO_ORDER_IN_FILE = "__newo_in_file__"
    OPTION_STRIKE_PRICE = "__option_strike_price__"
    OPTION_TYPE = "__option_type__"
    SRIKE_PRICE_TYPE = "__strike_price_type__"


class BeaconLighthouseOrderTransformations(AbstractOrderTransformations):
    """
    Primary Transformations for Lighthouse Beacon Orders.
    Specs: https://steeleye.atlassian.net/wiki/spaces/IN/pages/1691255070/Order+Lighthouse+Beacon
    """

    def _pre_process(self) -> None:
        """
        Temporary columns required to calculate the other schema columns in
        PrimaryTransformations. These columns are populated in self.pre_process
        dataframe.
        """
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_asset_class(),
                self._temp_buy_sell(),
                self._temp_currency(),
                self._temp_execution_time(),
                self._temp_expiry(),
                self._temp_input_venue(),
                self._temp_option_strike_price(),
                self._temp_option_type(),
                self._temp_order_time(),
            ],
            axis=1,
        )

        # Calculation of `__input_asset_class__` requires the `__asset_class__`
        # populated from `_temp_asset_class` method
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_input_asset_class(),
            ],
            axis=1,
        )

    def process(self) -> pd.DataFrame:
        """
        Populates all the required schema columns that are required in the
        self.target_df dataframe. It also calls the `pre_process` and `post_process`
        to populate the temp columns required in this task and in the downstream
        tasks.
        """
        self.pre_process()
        self.buy_sell()
        self.data_source_name()
        self.date()
        self.execution_details_buy_sell_indicator()
        self.execution_details_limit_price()
        self.execution_details_order_status()
        self.execution_details_order_type()
        self.execution_details_trading_capacity()
        self.execution_details_validity_period()
        self.id()
        # should be called before market_identifiers method
        self.market_identifiers_instrument()
        self.market_identifiers_parties()
        self.market_identifiers()
        self.meta_model()
        self.order_identifiers_order_id_code()
        self.order_identifiers_transaction_ref_no()
        self.price_forming_data_initial_quantity()
        self.price_forming_data_price()
        self.price_forming_data_remaining_quantity()
        self.price_forming_data_traded_quantity()
        self.report_details_transaction_ref_no()
        self.source_index()
        self.source_key()
        self.timestamps_order_received()
        self.timestamps_order_status_updated()
        self.timestamps_order_submitted()
        self.timestamps_trading_date_time()
        self.transaction_details_buy_sell_indicator()
        self.transaction_details_price_currency()
        self.transaction_details_price_notation()
        self.transaction_details_quantity()
        self.transaction_details_quantity_currency()
        self.transaction_details_quantity_notation()
        self.transaction_details_trading_capacity()
        self.transaction_details_trading_date_time()
        self.transaction_details_ultimate_venue()
        self.transaction_details_upfront_payment()
        self.transaction_details_venue()
        self.post_process()
        return self.target_df

    def _post_process(self) -> None:
        """
        Populates temporary columns which are required in the downstream
        tasks. Columns are populated in self.target_df dataframe.
        """
        self.target_df = pd.concat(
            [
                self.target_df,
                self.pre_process_df.loc[
                    :,
                    [
                        PostProcessTempColumns.ASSET_CLASS,
                        PostProcessTempColumns.CURRENCY,
                        PostProcessTempColumns.EXPIRY,
                        PostProcessTempColumns.INPUT_ASSET_CLASS,
                        PostProcessTempColumns.INPUT_VENUE,
                    ],
                ],
                self._post_temp_expiry_date(),
                self._post_temp_newo_in_file(),
                pd.DataFrame(
                    data=StrikePriceType.MNTRYVAL.value,
                    index=self.target_df.index,
                    columns=[PostProcessTempColumns.SRIKE_PRICE_TYPE],
                ),
            ],
            axis=1,
        )

    def _buy_sell(self) -> pd.DataFrame:
        """
        Returns a dataframe containing _order.buySell and _orderState.buySell.
        This is calculated from the `__buysell__` temporary column.
        See `_temp_buy_sell` method.
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[
                :, [TempColumns.BUY_SELL, TempColumns.BUY_SELL]
            ].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.BUY_SELL,
                ),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.BUY_SELL,
                ),
            ],
        )

    def _data_source_name(self) -> pd.DataFrame:
        """
        Returns a dataframe containing `dataSourceName` column
        """
        return pd.DataFrame(
            data="Beacon OMS",
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """
        Returns a dataframe containing `date` column
        calulated from `TRADE_DATE` source column.
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.TRADE_DATE,
                target_attribute=OrderColumns.DATE,
                source_attribute_format="%Y-%m-%d",
                convert_to=ConvertTo.DATE.value,
            ),
        )

    def _execution_details_additional_limit_price(self) -> None:
        """Not Implemented"""

    def _execution_details_aggregated_order(self) -> None:
        """Not Implemented"""

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Returns a dataframe containing `executionDetails.buySellIndicator`
        which is calculated from the `__buysell__` temporary column.
        See `_temp_buy_sell` method.
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.BUY_SELL].values,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _execution_details_client_additional_info(self) -> None:
        """Not Implemented"""

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """Returns a dataframe containing executionDetails.orderStatus"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_price_attribute=SourceColumns.LIMIT_PRICE,
                source_ccy_attribute=SourceColumns.CURRENCY,
                target_price_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE,
                ),
                cast_to=CastTo.ABS,
            ),
        )

    def _execution_details_liquidity_provision_activity(self) -> None:
        """Not Implemented"""

    def _execution_details_mes_first_execution_only(self) -> None:
        """Not Implemented"""

    def _execution_details_min_acceptable_quantity(self) -> None:
        """Not Implemented"""

    def _execution_details_min_executable_size(self) -> None:
        """Not Implemented"""

    def _execution_details_order_restriction(self) -> None:
        """Not Implemented"""

    def _execution_details_order_status(self) -> pd.DataFrame:
        """
        Returns a dataframe containing _order.executionDetails.orderStatus and
        _orderState.executionDetails.orderStatus
        """
        return pd.concat(
            [
                pd.DataFrame(
                    data=OrderStatus.NEWO.value,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        )
                    ],
                ),
                MapValue.process(
                    source_frame=self.source_frame,
                    params=ParamsMapValue(
                        source_attribute=SourceColumns.ORDER_STATUS,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        ),
                        case_insensitive=True,
                        value_map={
                            "cancel": OrderStatus.CAME.value,
                            "canceled": OrderStatus.CAME.value,
                            "cancelled": OrderStatus.CAME.value,
                            "done for day": OrderStatus.DNFD.value,
                            "doneforday": OrderStatus.DNFD.value,
                            "filled": OrderStatus.FILL.value,
                            "part filled": OrderStatus.PARF.value,
                            "partfilled": OrderStatus.PARF.value,
                            "rejected": OrderStatus.REMO.value,
                            "replaced": OrderStatus.REME.value,
                        },
                    ),
                ),
            ],
            axis=1,
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """Returns a dataframe containing executionDetails.orderType"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ORDER_TYPE,
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
                ),
            ),
        )

    def _execution_details_outgoing_order_addl_info(self) -> None:
        """Not Implemented"""

    def _execution_details_passive_aggressive_indicator(self) -> None:
        """Not Implemented"""

    def _execution_details_passive_only_indicator(self) -> None:
        """Not Implemented"""

    def _execution_details_pegged_limit_price(self) -> None:
        """Not Implemented"""

    def _execution_details_routing_strategy(self) -> None:
        """Not Implemented"""

    def _execution_details_securities_financing_txn_indicator(self) -> None:
        """Not Implemented"""

    def _execution_details_self_execution_prevention(self) -> None:
        """Not Implemented"""

    def _execution_details_settlement_amount(self) -> None:
        """Not Implemented"""

    def _execution_details_short_selling_indicator(self) -> None:
        """Not Implemented"""

    def _execution_details_stop_price(self) -> None:
        """Not Implemented"""

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """Returns a dataframe containing executionDetails.tradingCapacity"""
        return pd.DataFrame(
            data=TradingCapacity.AOTC.value,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY],
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """Returns a dataframe containing executionDetails.validityPeriod"""
        return MapAttribute.process(
            source_frame=MapValue.process(
                source_frame=self.source_frame,
                params=ParamsMapValue(
                    source_attribute=SourceColumns.TIME_IN_FORCE,
                    target_attribute=TempColumns.VALIDITY_PERIOD,
                    case_insensitive=True,
                    value_map={
                        "day": ValidityPeriod.DAVY.value,
                        "gtc": ValidityPeriod.GTCV.value,
                    },
                ),
            ),
            params=ParamsMapAttribute(
                source_attribute=TempColumns.VALIDITY_PERIOD,
                target_attribute=OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD,
                cast_to=CastToMapAttribute.STRING_LIST,
                list_delimiter=Delimiters.SEMI_COLON,
            ),
        )

    def _execution_details_waiver_indicator(self) -> None:
        """Not Implemented"""

    def _financing_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _hierarchy(self) -> None:
        """Not Implemented"""

    def _id(self) -> pd.DataFrame:
        """Returns a dataframe containing _order.id and _orderState.id"""
        return pd.DataFrame(
            data=self.source_frame.loc[
                :,
                [
                    SourceColumns.CLIENTORDERID,
                    SourceColumns.CLIENTORDERID,
                ],
            ].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.ID,
                ),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.ID,
                ),
            ],
        )

    def _is_discretionary(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_iceberg(self):
        """Not Implemented"""

    def _is_repo(self):
        """Not Implemented"""

    def _is_synthetic(self):
        """Not Implemented"""

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        """Not Implemented"""

    def _market_identifiers(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_instrument and _market_identifiers_parties have been
        called earlier"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.instrument by calling InstrumentIdentifiers"""
        instrument_source_df = pd.concat(
            [
                self.source_frame.loc[
                    :,
                    [
                        SourceColumns.BBSYMBOL,
                        SourceColumns.UNDERLYING_TICKER,
                        SourceColumns.UNDERLYING_ISIN,
                        SourceColumns.ISIN,
                    ],
                ],
                self.pre_process_df.loc[
                    :,
                    [
                        PostProcessTempColumns.INPUT_ASSET_CLASS,
                        PostProcessTempColumns.CURRENCY,
                        PostProcessTempColumns.EXPIRY,
                        PostProcessTempColumns.OPTION_STRIKE_PRICE,
                        PostProcessTempColumns.OPTION_TYPE,
                        PostProcessTempColumns.INPUT_VENUE,
                    ],
                ],
            ],
            axis=1,
        )
        return InstrumentIdentifiers.process(
            source_frame=instrument_source_df,
            params=ParamsInstrumentIdentifiers(
                asset_class_attribute=PostProcessTempColumns.INPUT_ASSET_CLASS,
                bbg_figi_id_attribute=SourceColumns.BBSYMBOL,
                currency_attribute=PostProcessTempColumns.CURRENCY,
                expiry_date_attribute=PostProcessTempColumns.EXPIRY,
                isin_attribute=SourceColumns.ISIN,
                option_strike_price_attribute=PostProcessTempColumns.OPTION_STRIKE_PRICE,
                option_type_attribute=PostProcessTempColumns.OPTION_TYPE,
                underlying_symbol_attribute=SourceColumns.UNDERLYING_TICKER,
                underlying_isin_attribute=SourceColumns.UNDERLYING_ISIN,
                venue_attribute=PostProcessTempColumns.INPUT_VENUE,
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                retain_task_inputs=True,
            ),
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.parties by calling PartyIdentifiers"""
        with_prefix_df = pd.concat(
            [
                # LEI for Lighthouse: 549300IL8TQT0JMDJJ80
                pd.DataFrame(
                    data=f"{PartyPrefix.LEI}549300IL8TQT0JMDJJ80",
                    index=self.source_frame.index,
                    columns=[TempColumns.EXECUTING_ENTITY_WITH_LEI],
                ),
                PartyPrefix.ID + self.source_frame.loc[:, SourceColumns.ACCOUNT],
                PartyPrefix.ID + self.source_frame.loc[:, SourceColumns.BROKER_CODE],
                PartyPrefix.ID + self.source_frame.loc[:, SourceColumns.TRADER],
                PartyPrefix.ID + self.source_frame.loc[:, SourceColumns.MANAGER],
            ],
            axis=1,
        )
        return GenericOrderPartyIdentifiers.process(
            source_frame=pd.concat(
                [with_prefix_df, self.pre_process_df.loc[:, TempColumns.BUY_SELL]],
                axis=1,
            ),
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=TempColumns.EXECUTING_ENTITY_WITH_LEI,
                client_identifier=SourceColumns.ACCOUNT,
                counterparty_identifier=SourceColumns.BROKER_CODE,
                buyer_identifier=SourceColumns.ACCOUNT,
                seller_identifier=SourceColumns.BROKER_CODE,
                buyer_decision_maker_identifier=TempColumns.EXECUTING_ENTITY_WITH_LEI,
                trader_identifier=SourceColumns.TRADER,
                investment_decision_within_firm_identifier=SourceColumns.MANAGER,
                execution_within_firm_identifier=SourceColumns.TRADER,
                buy_sell_side_attribute=TempColumns.BUY_SELL,
                use_buy_mask_for_buyer_seller=True,
                use_buy_mask_for_buyer_seller_decision_maker=True,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """Returns a data frame containing _order.__meta_model__ and _orderState.__meta_model__.
        The columns are populated with the static values Order and OrderState respectively"""
        return pd.DataFrame(
            data=[["Order", "OrderState"]],
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.META_MODEL,
                ),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.META_MODEL,
                ),
            ],
        )

    def _order_class(self) -> None:
        """Not Implemented"""

    def _order_identifiers_aggregated_order_id_code(self) -> None:
        """Not Implemented"""

    def _order_identifiers_initial_order_designation(self) -> None:
        """Not Implemented"""

    def _order_identifiers_internal_order_id_code(self) -> None:
        """Not Implemented"""

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """Returns a dataframe containing orderIdentifiers.orderIdCode"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.CLIENTORDERID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
        )

    def _order_identifiers_order_routing_code(self) -> None:
        """Not Implemented"""

    def _order_identifiers_parent_order_id(self) -> None:
        """Not Implemented"""

    def _order_identifiers_sequence_number(self) -> None:
        """Not Implemented"""

    def _order_identifiers_trading_venue_order_id_code(self) -> None:
        """Not Implemented"""

    def _order_identifiers_trading_venue_transaction_id_code(self) -> None:
        """Not Implemented"""

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a dataframe containing _orderState.orderIdentifiers.transactionRefNo"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.TRADE_ID].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                )
            ],
        )

    def _price_forming_data_display_quantity(self) -> None:
        """Not Implemented"""

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """Returns a dataframe containing priceFormingData.price"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORDERQTY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY],
        )

    def _price_forming_data_modified_quantity(self) -> None:
        """Not Implemented"""

    def _price_forming_data_price(self) -> pd.DataFrame:
        """"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_price_attribute=SourceColumns.PRICE,
                source_ccy_attribute=SourceColumns.CURRENCY,
                target_price_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                cast_to=CastTo.ABS,
            ),
        )

    def _price_forming_data_price_not_applicable(self) -> None:
        """Not Implemented"""

    def _price_forming_data_price_pending(self) -> None:
        """Not Implemented"""

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """Returns a dataframe containing _order.priceFormingData.remainingQuantity"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LEAVES_QTY].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
                )
            ],
        )

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """Returns a datafrane containing _order.priceFormingData.tradedQuantity and
        _orderState.priceFormingData.tradedQuantity
        """
        return pd.concat(
            [
                pd.DataFrame(
                    data="0.0",
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                        )
                    ],
                ),
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=SourceColumns.CUMQTY,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                        ),
                    ),
                ),
            ],
            axis=1,
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a dataframe containing reportDetails.transactionRefNo"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.TRADE_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO],
        )

    def _source_index(self) -> pd.DataFrame:
        """Returns a dataframe containing the sourceIndex of a record"""
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_INDEX],
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a dataframe containing the sourceKey"""
        return pd.DataFrame(
            data=os.getenv(SWARM_FILE_URL),
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_KEY],
        )

    def _timestamps_external_order_received(self) -> None:
        """Not Implemented"""

    def _timestamps_external_order_submitted(self) -> None:
        """Not Implemented"""

    def _timestamps_internal_order_received(self) -> None:
        """Not Implemented"""

    def _timestamps_internal_order_submitted(self) -> None:
        """Not Implemented"""

    def _timestamps_order_received(self) -> pd.DataFrame:
        """Returns a dataframe containing timestamps.orderReceived"""
        return MapConditional.process(
            source_frame=pd.concat(
                [
                    self.source_frame.loc[:, SourceColumns.ORDER_TIME],
                    self.pre_process_df.loc[
                        :,
                        [
                            TempColumns.EXECUTION_TIME,
                            TempColumns.ORDER_TIME,
                        ],
                    ],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                cases=[
                    Case(
                        query=f"`{SourceColumns.ORDER_TIME}`.isnull()",
                        attribute=TempColumns.EXECUTION_TIME,
                    ),
                    Case(
                        query=f"`{SourceColumns.ORDER_TIME}`.notnull()",
                        attribute=TempColumns.ORDER_TIME,
                    ),
                ],
            ),
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """
        Returns a dataframe with timestamps.orderStatusUpdated:

        Calculates the column value from `TRANSACT_TIME` source column
        and converts it into  "%Y-%m-%dT%H:%M:%S.%f" format
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.TRANSACT_TIME,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
                source_attribute_format="%Y-%m-%dT%H:%M:%S.%f",
                convert_to=ConvertTo.DATETIME.value,
            ),
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """Returns a dataframe containing timestamps.orderSubmitted"""
        return MapConditional.process(
            source_frame=pd.concat(
                [
                    self.source_frame.loc[:, SourceColumns.ORDER_TIME],
                    self.pre_process_df.loc[
                        :,
                        [
                            TempColumns.EXECUTION_TIME,
                            TempColumns.ORDER_TIME,
                        ],
                    ],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                cases=[
                    Case(
                        query=f"`{SourceColumns.ORDER_TIME}`.isnull()",
                        attribute=TempColumns.EXECUTION_TIME,
                    ),
                    Case(
                        query=f"`{SourceColumns.ORDER_TIME}`.notnull()",
                        attribute=TempColumns.ORDER_TIME,
                    ),
                ],
            ),
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """Returns a dataframe containing the timestamps.tradingDateTime"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.EXECUTION_TIME].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TIMESTAMPS_TRADING_DATE_TIME],
        )

    def _timestamps_validity_period(self) -> None:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(self) -> None:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> None:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> None:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(self) -> None:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> None:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> None:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> None:
        """Not Implemented"""

    def _transaction_details_basket_id(self) -> None:
        """Not Implemented"""

    def _transaction_details_branch_membership_country(self) -> None:
        """Not Implemented"""

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a dataframe containing the transactionDetails.buySellIndicator"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.BUY_SELL].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _transaction_details_commission_amount(self) -> None:
        """Not Implemented"""

    def _transaction_details_commission_amount_currency(self) -> None:
        """Not Implemented"""

    def _transaction_details_commission_amount_type(self) -> None:
        """Not Implemented"""

    def _transaction_details_complex_trade_component_id(self) -> None:
        """Not Implemented"""

    def _transaction_details_cross_indicator(self) -> None:
        """Not Implemented"""

    def _transaction_details_cumulative_quantity(self) -> None:
        """Not Implemented"""

    def _transaction_details_derivative_notional_change(self) -> None:
        """Not Implemented"""

    def _transaction_details_net_amount(self) -> None:
        """Not Implemented"""

    def _transaction_details_order_id_code(self) -> None:
        """Not Implemented"""

    def _transaction_details_outgoing_order_addl_info(self) -> None:
        """Not Implemented"""

    def _transaction_details_position_effect(self) -> None:
        """Not Implemented"""

    def _transaction_details_position_id(self) -> None:
        """Not Implemented"""

    def _transaction_details_price(self) -> None:
        """Not Implemented"""

    def _transaction_details_price_average(self) -> None:
        """Not Implemented"""

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """Returns a dataframe containing the transactionDetails.priceCurrency"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, PostProcessTempColumns.CURRENCY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY],
        )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """Returns a data frame containing mapped transactionDetails.priceNotation"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.PRICE_NOTATION,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION,
                case_insensitive=True,
                value_map={
                    "BAPO": PriceNotation.BAPO.value,
                    "BASE POINTS": PriceNotation.BAPO.value,
                    "MONE": PriceNotation.MONE.value,
                    "MONETARY": PriceNotation.MONE.value,
                    "MONEY": PriceNotation.MONE.value,
                    "PERC": PriceNotation.PERC.value,
                    "PERCENTAGE": PriceNotation.PERC.value,
                    "YIEL": PriceNotation.YIEL.value,
                    "YIELD": PriceNotation.YIEL.value,
                },
            ),
        )

    def _transaction_details_price_pending(self) -> None:
        """Not Implemented"""

    def _transaction_details_pricing_details_percent_vs_market(self) -> None:
        """Not Implemented"""

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """ """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORDERQTY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY],
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """ """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, PostProcessTempColumns.CURRENCY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY],
        )

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """ """
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.QUANTITY_NOTATION,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
                case_insensitive=True,
                value_map={
                    "MONE": QuantityNotation.MONE.value,
                    "MONETARY": QuantityNotation.MONE.value,
                    "MONEY": QuantityNotation.MONE.value,
                    "NOMI": QuantityNotation.NOML.value,
                    "NOMINAL": QuantityNotation.NOML.value,
                    "NOMINALVALUE": QuantityNotation.NOML.value,
                    "NOML": QuantityNotation.NOML.value,
                    "UNIT": QuantityNotation.UNIT.value,
                },
            ),
        )

    def _transaction_details_record_type(self) -> None:
        """Not Implemented"""

    def _transaction_details_settlement_amount(self) -> None:
        """Not Implemented"""

    def _transaction_details_settlement_amount_currency(self) -> None:
        """Not Implemented"""

    def _transaction_details_settlement_date(self) -> None:
        """Not Implemented"""

    def _transaction_details_settlement_type(self) -> None:
        """Not Implemented"""

    def _transaction_details_swap_directionalities(self) -> None:
        """Not Implemented"""

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """
        Returns a dataframe containing `transactionDetails.tradingCapacity` column
        with `AOTC` as static value.
        """
        return pd.DataFrame(
            data=TradingCapacity.AOTC.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """
        Returns a dataframe containing `transactionDetails.tradingDateTime` column
        mapped from `__execution_time__` temporary column.
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.EXECUTION_TIME].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME],
        )

    def _transaction_details_trail_id(self) -> None:
        """Not Implemented"""

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """
        Returns a dataframe containing `transactionDetails.ultimateVenue` column
        calculated conditionally from these source columns:
            1. LAST_MARKET
            2. MIC
            3. LOCATION
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                cases=[
                    Case(
                        query=f"`{SourceColumns.LAST_MARKET}`.notnull() & `{SourceColumns.LAST_MARKET}`.str.len() == 4",
                        attribute=SourceColumns.LAST_MARKET,
                    ),
                    Case(
                        query=f"`{SourceColumns.LAST_MARKET}`.isnull() | `{SourceColumns.LAST_MARKET}`.str.len() != 4",
                        attribute=SourceColumns.MIC,
                    ),
                    Case(
                        query=f"`{SourceColumns.ISIN}`.isnull()",
                        attribute=SourceColumns.LOCATION,
                    ),
                ],
            ),
        )

    def _transaction_details_upfront_payment(self) -> None:
        """Not Implemented"""

    def _transaction_details_upfront_payment_currency(self) -> None:
        """Not Implemented"""

    def _transaction_details_venue(self) -> pd.DataFrame:
        """
        Returns a dataframe containing `transactionDetails.venue` column
        mapped from the `LOCATION` source columns
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.LOCATION,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_VENUE,
            ),
        )

    def _temp_asset_class(self) -> pd.DataFrame:
        """
        Returns a dataframe containing `__asset_class__` temporary
        column which is used in calculatin of `__input_asset_class`
        and is also used in the `InstrumentExtStrikePriceType` downstream task.
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.SECURITY_TYPE,
                target_attribute=PostProcessTempColumns.ASSET_CLASS,
                case_insensitive=True,
                value_map={
                    "common stock": "cds single stock",
                    "fut": "future",
                    "index": "cds index",
                    "opt": "option",
                    "stock": "cds single stock",
                    "stock-etf": "equity forward",
                    "stock-subtype:93": "cds single stock",
                    "swap": "equity swap",
                },
            ),
        )

    def _temp_buy_sell(self) -> pd.DataFrame:
        """
        Populates the `__buysell__` temporary column which is
        used to populate these values:
            1. transactionDetails.buySellIndicator
            2. _order.buySell
            3. _orderState.buySell

        This is also used in the `_market_identifiers_instrument` method.
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.SIDE,
                target_attribute=TempColumns.BUY_SELL,
                case_insensitive=True,
                value_map={
                    "buy": BuySellIndicator.BUYI.value,
                    "cover": BuySellIndicator.BUYI.value,
                    "sell": BuySellIndicator.SELL.value,
                    "short": BuySellIndicator.SELL.value,
                },
            ),
        )

    def _temp_currency(self) -> pd.DataFrame:
        """
        Returns a dataframe containing `__currency__` temporary column which is
        used to populate these values:
            1. transactionDetails.priceCurrency
            2. transactionDetails.quantityCurrency

        Also, this value is used in the following downstream tasks:
            1. LinkInstrument
            2. InstrumentFallback
            3. InstrumentOverridesStrikePriceCurrency

        This is also used in the `_market_identifiers_instrument` method.
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.CURRENCY,
                target_ccy_attribute=PostProcessTempColumns.CURRENCY,
            ),
        )

    def _temp_execution_time(self) -> pd.DataFrame:
        """
        Returns a dataframe containing `__execution_time__` temporary column
        which is used to populate these values:
            1. timestamps.orderReceived
            2. timestamps.orderSubmitted
            3. timestamps.tradingDateTime
            4. transactionDetails.tradingDateTime
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.EXECUTION_TIME,
                target_attribute=TempColumns.EXECUTION_TIME,
                source_attribute_format="%Y-%m-%d %H:%M",
                convert_to=ConvertTo.DATETIME.value,
            ),
        )

    def _temp_expiry(self) -> pd.DataFrame:
        """
        Returns a dataframe containing `__expiry__` temporary column which
        is used in `_market_identifiers_instrument` method and also required in
        `InstrumentFallback` downstream task.

        It's calculated from the `MATURITY_DAY` source column conditionally
        and then later on converted to datetime.
        """
        return ConvertDatetime.process(
            source_frame=ConcatAttributes.process(
                source_frame=pd.concat(
                    [
                        self.source_frame.loc[:, SourceColumns.MATURITY_MONTH_YEAR],
                        MapConditional.process(
                            source_frame=self.source_frame,
                            params=ParamsMapConditional(
                                target_attribute=TempColumns.MATURITY_DAY,
                                cases=[
                                    Case(
                                        query="index == index",
                                        attribute=SourceColumns.MATURITY_DAY,
                                    ),
                                    Case(
                                        query=f"`{SourceColumns.MATURITY_DAY}` == '0'",
                                        value="01",
                                    ),
                                    Case(
                                        query=f"`{SourceColumns.MATURITY_DAY}`.isnull()",
                                        value="28",
                                    ),
                                ],
                            ),
                        ),
                    ],
                    axis=1,
                ),
                params=ParamsConcatAttributes(
                    source_attributes=[
                        SourceColumns.MATURITY_MONTH_YEAR,
                        TempColumns.MATURITY_DAY,
                    ],
                    target_attribute=TempColumns.MATURITY_DATE,
                    mask=f"`{SourceColumns.MATURITY_MONTH_YEAR}`.notnull()",
                ),
            ),
            params=ParamsConvertDatetime(
                source_attribute=TempColumns.MATURITY_DATE,
                target_attribute=PostProcessTempColumns.EXPIRY,
                source_attribute_format="%Y%m%d",
                convert_to=ConvertTo.DATE.value,
            ),
        )

    def _temp_input_asset_class(self) -> pd.DataFrame:
        """
        Returns a dataframe containing `__input_asset_class__` temporary column.
        It's used in `_market_identifiers_instrument` method and also used in the
        `LinkInstrument` downstream task.

        NOTE: `_temp_asset_class` should be called before this method.
        """
        return MapConditional.process(
            source_frame=pd.concat(
                [
                    self.pre_process_df.loc[:, PostProcessTempColumns.ASSET_CLASS],
                    self.source_frame.loc[:, SourceColumns.ISCFD],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=PostProcessTempColumns.INPUT_ASSET_CLASS,
                cases=[
                    Case(
                        query=f"`{SourceColumns.ISCFD}` == '1'",
                        value="cfd",
                    ),
                    Case(
                        query=f"`{SourceColumns.ISCFD}` == '0'",
                        attribute=PostProcessTempColumns.ASSET_CLASS,
                    ),
                ],
            ),
        )

    def _temp_input_venue(self) -> pd.DataFrame:
        """
        Returns a dataframe with the `__input_venue__` temporary column which
        is used in `_market_identifiers_instrument` method and also used in
        `LinkInstrument` downstream task.
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=PostProcessTempColumns.INPUT_VENUE,
                cases=[
                    Case(
                        query=f"`{SourceColumns.LAST_MARKET}`.notnull() & `{SourceColumns.LAST_MARKET}`.str.len() == 4",
                        attribute=SourceColumns.LAST_MARKET,
                    ),
                    Case(
                        query=f"`{SourceColumns.LAST_MARKET}`.isnull() | `{SourceColumns.LAST_MARKET}`.str.len() != 4",
                        attribute=SourceColumns.MIC,
                    ),
                    Case(
                        query=f"`{SourceColumns.ISIN}`.isnull()",
                        attribute=SourceColumns.LOCATION,
                    ),
                    Case(
                        query=f"(`{SourceColumns.ISIN}`.isnull()) & (`{SourceColumns.UNDERLYING_ISIN}`.isnull())",
                        attribute=SourceColumns.LAST_MARKET,
                    ),
                    # Extra check to cover XCME fully
                    Case(
                        query=f"`{SourceColumns.MIC}`.notnull() & `{SourceColumns.MIC}` == 'XCME'",
                        attribute=SourceColumns.MIC,
                    ),
                ],
            ),
        )

    def _temp_option_strike_price(self) -> pd.DataFrame:
        """
        Returns a dataframe with the __option_strike_price__ temporary column
        which is used in the `_market_identifiers_instrument` method.

        It's also used in the downstream tasks:
            1. InstrumentFallback
            2. InstrumentOverridesStrikePrice
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_price_attribute=SourceColumns.STRIKE_PRICE,
                source_ccy_attribute=SourceColumns.CURRENCY,
                target_price_attribute=PostProcessTempColumns.OPTION_STRIKE_PRICE,
                cast_to=CastTo.ABS,
            ),
        )

    def _temp_option_type(self) -> pd.DataFrame:
        """
        Returns a dataframe with the `__option_type__` temporary column.
        It's calculated conditionally based on the `PURORCALL` source column.

        It's used in `_market_identifiers_instrument` method and also required in
        `InstrumentFallback` downstream task.
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=PostProcessTempColumns.OPTION_TYPE,
                cases=[
                    Case(
                        query=f"`{SourceColumns.PUTORCALL}` == '0'",
                        value=OptionType.PUTO.value,
                    ),
                    Case(
                        query=f"`{SourceColumns.PUTORCALL}` == '1'",
                        value=OptionType.CALL.value,
                    ),
                ],
            ),
        )

    def _temp_order_time(self) -> pd.DataFrame:
        """
        Returns a dataframe with the __order_time__ temporary column. It is
        calculated from the `ORDER_TIME` source column.

        It is used to populate these columns:
            1. timestamps.orderReceived
            2. timestamps.orderSubmitted
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.ORDER_TIME,
                target_attribute=TempColumns.ORDER_TIME,
                source_attribute_format="%Y-%m-%d %H:%M",
                convert_to=ConvertTo.DATETIME.value,
            ),
        )

    def _post_temp_expiry_date(self) -> pd.DataFrame:
        """
        Returns a dataframe with the `__expiry_date__` column. It is
        calculated from the `EXPIRY_DATE` source column.

        This column is required in the `InstrumentFallback` downstream
        task.
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.EXPIRE_DATE,
                target_attribute=PostProcessTempColumns.EXPIRY_DATE,
                source_attribute_format="%Y-%m-%d",
                convert_to=ConvertTo.DATE.value,
            ),
        )

    def _post_temp_newo_in_file(self) -> pd.DataFrame:
        """
        Returns a dataframe with the `__newo_in_file__` column. It is
        calculated from the `ORDER_STATUS` source column.

        This column is required in the `RemoveDupNEWO` downstream task.
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=PostProcessTempColumns.NEWO_ORDER_IN_FILE,
                cases=[
                    Case(
                        query=f"`{SourceColumns.ORDER_STATUS}`.fillna('').str.lower() == 'new'",
                        value=True,
                    ),
                    Case(
                        query=f"`{SourceColumns.ORDER_STATUS}`.fillna('').str.lower() != 'new'",
                        value=False,
                    ),
                ],
            ),
        )

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""
