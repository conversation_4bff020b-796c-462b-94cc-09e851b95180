class SharedColumns:
    ASSET_CLASS = "Asset Class"
    CAPACITY = "Capacity"
    ORDER_TYPE = "Order Type"
    PARENT_ORDER_ID = "Parent Order ID"
    PLACEMENT_ID = "Placement ID"
    SIDE = "Side"
    TRADE_DATE = "Trade Date"
    BROKER = "Broker"
    DEALING_DESK = "Dealing Desk"
    EXECUTION_TIME = "Execution Time"


class EquityColumns:
    BROKER_PICKUP_TIME = "Broker Pickup Time"
    BROKER = "Broker"
    COMMISSION = "Commission"
    CURRENCY = "Currency"
    EXECUTION_DECISION_TIME = "Execution Decision Time"
    EXECUTION_LIMIT_PRICE = "Execution Limit Price"
    LAST_CAPACITY = "Last Capacity"
    LAST_MARKET = "Last Market"
    LAST_PX = "Last Px"
    LAST_QTY = "Last Qty"
    LIQUIDITY_FLAG = "Liquidity Flag"
    ORDER_LIMIT_PRICE = "Order Limit Price"
    ORDER_QUANTITY = "Order Quantity"
    ORDERED_SHARES = "Ordered Shares"
    PLACEMENT_SHARES = "Placement Shares"
    PM_ENTRY_TIME = "PM Entry Time"
    PM = "PM"
    SECURITY_ID_TYPE = "Security ID Type"
    SECURITY_ID = "Security ID"
    SISIN = "sISIN"
    STRATEGY_PARAM_1 = "Strategy Param 1"
    STRATEGY_PARAM_2 = "Strategy Param 2"
    STRATEGY_PARAM_3 = "Strategy Param 3"
    STRATEGY = "Strategy"
    TIME_IN_FORCE = "Time in Force"
    TRADER_PICKUP_TIME = "Trader Pickup Time"
    TRADER_SENT_TIME = "Trader Sent Time"
    TRADER = "Trader"
    VIA_FIX = "via FIX"


class FixedIncomeColumns:
    ALGO = "Algo"
    ALGO_NAME = "Algo Name"
    ASSET_SUB_CLASS = "Asset Sub-Class"
    ASSET_SUB_TYPE = "Asset Sub Type"
    ASSET_SUB_TYPE_HYPHEN = "Asset Sub-Type"
    ASSET_TYPE = "Asset Type"
    AUTHORISED_TS = "Authorised TS"
    AUTHORISING_PM = "Authorising PM"
    BROKER_DATE = "Broker Date"
    BROKER_FILL_TS = "Broker Fill TS"
    BROKER_ID = "Broker ID"
    BROKER_NAME = "Broker Name"
    CANCELLED_INDICATOR = "Cancelled Indicator"
    COMMENT_FIELDS = "Comment Fields"
    COMMISSION_FEE_CODE = "Commission Fee Code"
    COMMISSION_OVER_RIDE_CODE = "Commission Over-Ride Code"
    CUSIP = "Cusip"
    DEALER = "Dealer"
    DEALING_DESK = "Dealing Desk"
    DMA = "DMA"
    EXEC_BROKER_SPREAD = "Exec Broker Spread"
    EXEC_BROKER_YIELD = "Exec Broker Yield"
    EXTSYS_ACK_TS = "ExtSys Ack TS"
    FI_DEALING_CLASSIFICATION = "FI Dealing Classification"
    ISIN = "ISIN"
    LAST_BASKET = "Last Basket"
    LOANX = "LoanX"
    LOCAL_COMMISSION_AMOUNT = "Local Commission Amount"
    LOCAL_CURRENCY = "Local Currency"
    LOCAL_CURRENCY_CODE = "Local Currency Code"
    LOCAL_UNIT_PRICE = "Local Unit Price"
    MARKET_FEES_TAXES = "Market Fees/Taxes"
    MARKET_ORDER_TYPE = "Market Order Type"
    MI_DESK_LEVEL_2 = "MI Desk Level 2"
    MODIFIERS = "Modifiers"
    NARRATIVE_FIELDS = "Narrative Fields"
    ORDER_ID = "Order ID"
    OTHER_EXPENSES = "Other Expenses"
    PARENT_ORDER_SIZE = "Parent Order Size"
    PARENTORDERTIMESTAMP = "ParentOrderTimestamp"
    PM_ORDER_CREATION_TS = "PM Order Creation TS"
    PM_ORDER_INPUT_TS = "PM Order Input TS"
    PORTFOLIO_CODE = "Portfolio Code"
    QUANTITY_TRADED = "Quantity Traded"
    REASON = "Reason"
    SECURITY_NAME = "Security Name"
    SEDOL = "SEDOL"
    SENT_TO_EXTSYS_TS = "Sent to ExtSys TS"
    SETTLEMENT_DATE = "Settlement Date"
    SETTLEMENT_VALUE_LOCAL_CCY = "Settlement Value Local CCY"
    TIME_IN_FORCE = "Time In Force"
    TRADERID = "TraderID"
    UNDERLYING_SEDOL = "Underlying SEDOL"
    UNFUNDED = "Unfunded"
    VENUE = "Venue"
    WORKING_TS = "Working TS"


class FXColumns:
    VALUE_DATE_FWD = "Value Date Fwd"
    VALUE_DATE = "Value Date"
    CFI_CODE = "CFI code"
    ACCOUNT_FUND = "Account Fund"
    TRADE_DATE = "Trade Date"
    TIMESTAMP = "Timestamp (GMT)"
    TRADE_ID = "Trade Id"
    TRADER_ID = "Trader ID"
    SIDE = "Side"
    OUTRIGHT_RATE = "Outright Rate"
    BUY_CCY = "Buy Ccy"
    BUY_AMOUNT = "Buy Amount"
    SELL_AMOUNT = "Sell Amount"
    RATE_DIRECTION = "Rate Direction"
    EXECUTION_TYPE = "Execution Type"
    DEALING_DESK = "Dealing Desk"
    BROKER = "Broker"
    CUSTODIAN = "Custodian"
    CCY_INTENT = "Ccy Intent"
    ASSET_CLASS_FX = "AssetClass"


class TempColumns:
    ASSET_CLASS = "__asset_class__"
    BROKER = "__broker__"
    CLASSIFICATION = "__classification__"
    CLIENT = "__client__"
    EXECUTING_ENTITY_WITH_LEI = "__executing_entity_lei__"
    EXP_DATE = "__exp_date__"
    FULL_NAME = "__full_name__"
    FX_COUNTERPARTY = "__fx_counterparty__"
    FX_TRADER = "__fx_trader__"
    INVESTMENT_DECISION = "__investment_decision__"
    IS_CREATED_THROUGH_FALLBACK = "__is_created_through_fallback__"
    NEWO_IN_FILE = "__newo_in_file__"
    ONE_HUNDRED = "__one_hundred__"
    ORDER_STATUS_UPDATED = "__order_status_updated__"
    ORDER_STATUS_UPDATED_NEWO = "__order_status_updated_newo__"
    ORDER_STATUS_UPDATED_PARF = "__order_status_updated_parf__"
    PM = "__pm__"
    RATE_DIRECTION_LEFT = "__rate_direction_left__"
    RATE_DIRECTION_RIGHT = "__rate_direction_right__"
    SOURCE_INDEX = "__source_index__"
    SWAP_NEAR_LEG_DATE = "__swap_near_leg_date__"
    TEMP_COL_1 = "__temp_col_1__"
    TEMP_COL_2 = "__temp_col_2__"
    TEMP_COL_3 = "__temp_col_3__"
    TRADER = "__trader__"
    TRADE_DATE = "__trade_date__"
    ULTIMATE_VENUE = "__ultimate_venue__"
    VALUE_DATE = "__value_date__"
    VALUE_DATE_FWD = "__value_date_fwd__"


class FileNameBits:
    ADD = "ADD"
    ADD_UNDERSCORES = "_ADD_"
    MARKITFI = "MARKITFI"
    FILLS = "FILLS"
    FX = "FX"
    TRD_1 = "TRD_1"
    TRD_1_UNDERSCORES = "_TRD_1_"


class FileType:
    EQUITY = "Equity"
    FIXED_INCOME = "Fixed Income"
    FX = "FX"
