import re
from typing import <PERSON><PERSON>
from typing import <PERSON><PERSON>

from swarm_tasks.order.feed.thinkfolio.static import FileNameBits
from swarm_tasks.order.feed.thinkfolio.static import FileType


def get_file_type(file_name: str) -> Tuple[str, Optional[str]]:
    """
    Returns the File Type based on the file name,
        and if it's a `TRD` or `ADD` in the case of `Fixed Income`.
    Since file type is based on asset class, it's using the asset class as file type
    """
    file_type = re.search(
        f"({FileNameBits.MARKITFI}|{FileNameBits.FILLS})_([0-9]?)_?({FileNameBits.TRD_1}|{FileNameBits.ADD}|{FileNameBits.FX}|[0-9]*)",
        file_name,
    )

    if file_type is None:
        raise ValueError(f"File Type could not be determined for: {file_name}")

    group_1 = file_type.group(1)
    group_2 = file_type.group(2)
    group_3 = file_type.group(3)

    if group_1 == FileNameBits.MARKITFI:
        return FileType.FIXED_INCOME, group_3
    elif group_1 == FileNameBits.FILLS:
        if group_3 == FileNameBits.FX:
            return FileType.FX, None
        elif group_2.isdigit() or group_3.isdigit():
            return FileType.EQUITY, None

    raise ValueError(f"File Type could not be determined for: {file_name}")
