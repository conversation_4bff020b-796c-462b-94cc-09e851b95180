import logging

import pandas as pd
from se_trades_tasks.order.feed.lme.select.fix.lme_select_fix_prices_currencies import (
    Params as GenericParams,
)
from se_trades_tasks.order.feed.lme.select.fix.lme_select_fix_prices_currencies import (
    run_lme_select_fix_prices_currencies,
)
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

logger = logging.getLogger(__name__)


class Params(BaseParams, GenericParams):
    pass


class LmeSelectFixPricesCurrencies(TransformBaseTask):
    """
    This task updates the prices for multi-leg records and gets the currency
    from the instrument for all records (both multi-leg and single-leg).
    It returns all the columns in the source frame, but the aforementioned
    price and currency columns are updated
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            params=params,
            auditor=self.auditor,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame,
        params: <PERSON>ricPara<PERSON>,
        auditor=None,
    ) -> pd.DataFrame:
        return run_lme_select_fix_prices_currencies(
            source_frame=source_frame,
            params=params,
            auditor=auditor,
            logger=logger,
        )
