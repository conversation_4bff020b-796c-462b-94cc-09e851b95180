from typing import Dict

import pandas as pd
from se_elastic_schema.models import Order
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order.transformations.bbg.audt.static import SourceColumns
from swarm.conf import Settings
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.order.feed.shell.static import TempColumns
from swarm_tasks.order.generic.utils import es_scroll


class Resources(BaseResources):
    es_client_key: str


class FetchOrderSides(TransformBaseTask):
    """
    This task aggregates all values present in SourceColumns.AGGR_FROM and look up the tenant's
        elasticsearch in search of orders with that value as an `id`. It saves the information
        of the buySellIndicator to then apply it downstream to related orders.
    """

    resources_class = Resources

    def execute(
        self, source_frame: pd.DataFrame = None, resources: Resources = None, **kwargs
    ) -> pd.DataFrame:
        return self.process(source_frame=source_frame, resources=resources)

    @classmethod
    def process(
        cls, source_frame: pd.DataFrame = None, resources: Resources = None
    ) -> pd.DataFrame:

        es = Settings.connections.get(resources.es_client_key)

        query = cls.get_query(source_frame=source_frame, es_client=es)

        index = Order.get_elastic_index_alias(tenant=Settings.tenant)

        hits = es_scroll(es_client=es, query=query, index=index)

        hits = hits.drop_duplicates(
            subset=[
                OrderColumns.ID,
                OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
            ]
        ).replace(BuySellIndicator.BUYI.value, "BUY")

        if hits.empty:
            return pd.DataFrame(
                data=pd.NA,
                index=source_frame.index,
                columns=[TempColumns.ES_ORDER_SIDES],
            )

        hits_to_dict = hits.set_index(OrderColumns.ID).to_dict()[
            OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
        ]

        side_from_elastic = (
            source_frame.loc[:, SourceColumns.AGGR_FROM].map(hits_to_dict).fillna(pd.NA)
        )

        return pd.DataFrame(
            data=side_from_elastic.values,
            index=source_frame.index,
            columns=[TempColumns.ES_ORDER_SIDES],
        )

    @staticmethod
    def get_query(source_frame: pd.DataFrame, es_client) -> Dict:
        """
        Generates query to look for orders in elastic that have `id` and `buySellIndicator` and are not expired
        :param source_frame: input source_frame with SourceColumns.AGGR_FROM column
        :param es_client: elastic search instance
        :return: query dictionary
        """
        aggr_from_values = source_frame.loc[
            source_frame.loc[:, SourceColumns.AGGR_FROM].notnull(),
            SourceColumns.AGGR_FROM,
        ].to_list()

        query = {
            "query": {
                "bool": {
                    "filter": [{"terms": {"id": aggr_from_values}}],
                    "must_not": [{"exists": {"field": es_client.meta.expiry}}],
                    "must": [
                        {"exists": {"field": OrderColumns.ID}},
                        {
                            "exists": {
                                "field": OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
                            }
                        },
                    ],
                }
            },
            "_source": {
                "includes": [
                    OrderColumns.ID,
                    OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                ]
            },
        }

        return query
