from typing import Dict

import pandas as pd
from se_elastic_schema.models import Order
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order.transformations.bbg.audt.static import SourceColumns
from se_trades_tasks.order_and_tr.party.static import PartiesFields
from swarm.conf import Settings
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.order.feed.shell.static import ES_COL_PREFIX
from swarm_tasks.order.feed.shell.static import ES_COLUMNS_FOR_AGGREGATE_BACKFILL
from swarm_tasks.order.feed.shell.static import (
    ES_COLUMNS_FOR_AGGREGATE_BACKFILL_WITH_PREFIX,
)
from swarm_tasks.order.generic.utils import es_scroll


class Resources(BaseResources):
    es_client_key: str


class FetchAggregatedOrderInfo(TransformBaseTask):
    """
    This task aggregates all values present in SourceColumns.AGGR_FROM and looks up the tenant's
        elasticsearch in search of orders with that value as an `id`.
    It saves the information of the fields in the list ES_COLUMNS_FOR_AGGREGATE_BACKFILL
        to then apply it downstream to related orders

    """

    resources_class = Resources

    def execute(
        self, source_frame: pd.DataFrame = None, resources: Resources = None, **kwargs
    ) -> pd.DataFrame:
        return self.process(source_frame=source_frame, resources=resources)

    @classmethod
    def process(
        cls, source_frame: pd.DataFrame = None, resources: Resources = None
    ) -> pd.DataFrame:

        es = Settings.connections.get(resources.es_client_key)

        query = cls.get_query(source_frame=source_frame, es_client=es)

        index = Order.get_elastic_index_alias(tenant=Settings.tenant)

        hits = es_scroll(es_client=es, query=query, index=index)

        if hits.empty:
            return pd.DataFrame(
                data=pd.NA,
                index=source_frame.index,
                columns=ES_COLUMNS_FOR_AGGREGATE_BACKFILL_WITH_PREFIX,
            )

        hits = hits.groupby(OrderColumns.ID, as_index=False).apply(
            lambda x: x.fillna(x.ffill().bfill())
        )

        hits = hits.drop_duplicates(
            [
                col
                for col in hits.columns
                if col != OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD
            ]
        )

        for col in ES_COLUMNS_FOR_AGGREGATE_BACKFILL:
            if col not in hits.columns:
                hits[col] = pd.NA

        hits = hits.rename(
            mapper=dict(
                zip(
                    ES_COLUMNS_FOR_AGGREGATE_BACKFILL,
                    ES_COLUMNS_FOR_AGGREGATE_BACKFILL_WITH_PREFIX,
                )
            ),
            axis=1,
        )

        hits_in_the_correct_row = pd.merge(
            right=source_frame.loc[:, SourceColumns.AGGR_FROM],
            right_on=SourceColumns.AGGR_FROM,
            left=hits,
            left_on=ES_COL_PREFIX + OrderColumns.ID,
            left_index=True,
        ).drop(SourceColumns.AGGR_FROM, axis=1)

        hits_in_the_correct_row = hits_in_the_correct_row.sort_values(
            by=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS, key=lambda x: x == "NEWO"
        )

        hits_in_the_correct_row = hits_in_the_correct_row.drop_duplicates(
            subset=[ES_COL_PREFIX + OrderColumns.ID], keep="last"
        ).drop(axis=1, columns=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS)

        # swap buyer and seller for sells
        sells = (
            hits_in_the_correct_row.loc[:, ES_COL_PREFIX + OrderColumns.BUY_SELL] == "2"
        )

        buyer_sells = hits_in_the_correct_row.loc[
            sells, ES_COL_PREFIX + PartiesFields.PARTIES_BUYER_FILE_ID
        ]
        seller_sells = hits_in_the_correct_row.loc[
            sells, ES_COL_PREFIX + PartiesFields.PARTIES_SELLER_FILE_ID
        ]

        hits_in_the_correct_row.loc[
            sells, ES_COL_PREFIX + PartiesFields.PARTIES_BUYER_FILE_ID
        ] = seller_sells
        hits_in_the_correct_row.loc[
            sells, ES_COL_PREFIX + PartiesFields.PARTIES_SELLER_FILE_ID
        ] = buyer_sells

        return hits_in_the_correct_row

    @staticmethod
    def get_query(source_frame: pd.DataFrame, es_client) -> Dict:
        """
        Generates query to look for orders in elastic that have `id` and `buySellIndicator` and are not expired
        :param source_frame: input source_frame with SourceColumns.AGGR_FROM column
        :param es_client: elastic search instance
        :return: query dictionary
        """
        aggr_from_values = (
            source_frame.loc[
                source_frame.loc[:, SourceColumns.AGGR_FROM].notnull(),
                SourceColumns.AGGR_FROM,
            ]
            .drop_duplicates()
            .to_list()
        )

        cols_to_fetch = ES_COLUMNS_FOR_AGGREGATE_BACKFILL + [
            OrderColumns.EXECUTION_DETAILS_ORDER_STATUS
        ]

        query = {
            "query": {
                "bool": {
                    "filter": [{"terms": {OrderColumns.ID: aggr_from_values}}],
                    "must_not": [{"exists": {"field": es_client.meta.expiry}}],
                    "must": [
                        {"exists": {"field": OrderColumns.ID}},
                    ],
                }
            },
            "_source": {"includes": cols_to_fetch},
        }

        return query
