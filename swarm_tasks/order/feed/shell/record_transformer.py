from pathlib import Path
from typing import Any
from typing import List

import numpy as np
import pandas as pd
from prefect import context
from prefect.engine.signals import SKIP
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_elastic_schema.static.mifid2 import OrderStatus
from se_trades_tasks.order.transformations.bbg.audt.static import SourceColumns
from swarm.conf import Settings
from swarm.task.transform.base import BaseTask

from swarm_tasks.order.feed.shell.static import TempColumns


logger = context.get("logger")


class ShellFrameTransformer(BaseTask):
    """
    This task is used to create a new frame with more records than it had previously.
    Logic:
      - Filters all records which do not have SourceColumns.TS_ORD_NUM populated.
      - Assigns CHMO/CAME order status to them
      - Adds the aforementioned with records which have SourceColumns.TS_ORD_NUM populated.

    Specs: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2642083893/DRAFT+2+Order+Bloomberg+AUDT+SHELL-SAMCo#MAPPING
    """

    def execute(
        self,
        file_splitter_result_list: List[FileSplitterResult] = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            file_splitter_result_list=file_splitter_result_list,
        )

    @classmethod
    def process(
        cls,
        file_splitter_result_list: List[FileSplitterResult] = None,
    ) -> Any:
        if not file_splitter_result_list:
            raise SKIP("Upstream file_splitter_result_list is empty")

        result_list = list()
        for file_splitter_result in file_splitter_result_list:
            file_name = file_splitter_result.path.stem
            source_frame = pd.read_csv(file_splitter_result.path, dtype=str)
            transformed_df = cls.transform_file(source_frame=source_frame)

            transformed_df = transformed_df.reset_index(drop=True)

            logger.info(f"Transformed record view's shape is {transformed_df.shape}")

            source_dir = cls._get_source_dir()
            csv_file_path = source_dir.joinpath(f"{file_name}.csv")
            transformed_df.to_csv(csv_file_path, index=False, encoding="utf-8", sep=",")
            result_list.append(
                FileSplitterResult(
                    path=csv_file_path, batch_index=file_splitter_result.batch_index
                )
            )

        return result_list

    @classmethod
    def transform_file(cls, source_frame: pd.DataFrame) -> pd.DataFrame:
        """
        Takes in the reference dataframe and then processes it according
        to order flow record_creation specs
        :param source_frame: Input DataFrame
        :returns: Transformed df
        """
        if source_frame.empty:
            return source_frame

        source_frame[SourceColumns.TS_ORD_NUM] = source_frame[
            SourceColumns.TS_ORD_NUM
        ].replace({0: pd.NA, "0": pd.NA})
        null_order_num_masks = source_frame[SourceColumns.TS_ORD_NUM].isnull()
        null_order_num_df = source_frame[null_order_num_masks]

        # Create CHMO and CANCEL records
        chmo_cancel_df = null_order_num_df.assign(
            **{TempColumns.ORDER_STATUS: OrderStatus.CHMO.value}
        )
        chmo_cancel_df[TempColumns.ORDER_STATUS] = np.where(
            chmo_cancel_df[SourceColumns.EVENT].str.startswith("CANCEL", na=False),
            OrderStatus.CAME.value,
            chmo_cancel_df[TempColumns.ORDER_STATUS],
        )

        # Adds duplicate records with order status CHMO and CAME where SourceColumns.TS_ORD_NUM is null,
        # and keeps records with non-null TS_ORD_NUM
        transformed_df = pd.concat(
            [source_frame[~null_order_num_masks], chmo_cancel_df]
        )

        return transformed_df

    @staticmethod
    def _get_source_dir() -> Path:
        return Path(Settings.context.sources_dir)
