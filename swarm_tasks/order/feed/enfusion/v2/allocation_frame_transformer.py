import numpy as np
import pandas as pd
from se_elastic_schema.static.mifid2 import OrderStatus
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.order.feed.enfusion.v2.static import RecordType
from swarm_tasks.order.feed.enfusion.v2.static import SourceColumns
from swarm_tasks.order.feed.enfusion.v2.static import TempColumns


class AllocationFrameTransformer(TransformBaseTask):
    """
    This task is invoked for an Allocation file in the Enfusion order flow.
    We are creating new rows based on the input file and mentioned specs,
    hence the resultant shape of the dataframe changes.

    This task also populates the record_type for each record.

    Examples and specs:
    https://steeleye.atlassian.net/wiki/spaces/IN/pages/2267938859/Order+Enfusion+V2#complex-Logic
    """

    def execute(self, source_frame: pd.DataFrame = None, **kwargs) -> pd.DataFrame:
        # Change every record's Order State to NEWO
        newo_records = source_frame.assign(
            **{TempColumns.ORDER_STATUS: OrderStatus.NEWO.value}
        )

        # Create records with status FILL where Order Total Quantity == Order Cumulative Quantity
        # or there isn't an Order ID
        partial_status_records = newo_records.copy()
        conditions = [
            (
                (partial_status_records[SourceColumns.ORDER_ID].isnull())
                & (
                    ~partial_status_records[SourceColumns.REPORT_STATUS].str.fullmatch(
                        "CANC", na=False, case=False
                    )
                )
            ).to_list(),
            (
                (
                    partial_status_records[SourceColumns.ORDER_CUMULATIVE_QUANTITY]
                    == partial_status_records[SourceColumns.ORDER_TOTAL_QUANTITY]
                )
                & (
                    ~partial_status_records[SourceColumns.REPORT_STATUS].str.fullmatch(
                        "CANC", na=False, case=False
                    )
                )
            )
            .fillna(False)
            .to_list(),
            (
                (
                    partial_status_records[SourceColumns.ORDER_CUMULATIVE_QUANTITY]
                    != partial_status_records[SourceColumns.ORDER_TOTAL_QUANTITY]
                )
                & (partial_status_records[SourceColumns.QUANTITY] > 0)
                & (
                    ~partial_status_records[SourceColumns.REPORT_STATUS].str.fullmatch(
                        "CANC", na=False, case=False
                    )
                )
            )
            .fillna(False)
            .to_list(),
            (
                partial_status_records[SourceColumns.REPORT_STATUS]
                .str.fullmatch("CANC", na=False, case=False)
                .to_list()
            ),
        ]
        values = [
            OrderStatus.FILL.value,
            OrderStatus.FILL.value,
            OrderStatus.PARF.value,
            OrderStatus.CAME.value,
        ]
        partial_status_records[TempColumns.ORDER_STATUS] = np.select(
            condlist=conditions, choicelist=values, default=pd.NA
        )
        partial_status_records = partial_status_records[
            partial_status_records[TempColumns.ORDER_STATUS].notnull()
        ]

        # Marking the concat of the 2 frames as allocation records, assuming in the aforementioned transformation
        # we will always have a PARF/FILL for each NEWO
        allocation_records = pd.concat([newo_records, partial_status_records])
        allocation_records = allocation_records.assign(
            **{TempColumns.RECORD_TYPE: RecordType.ALLOCATION}
        )

        # Create new NEWO for each unique order id and assigning it a record type of Market
        market_records = newo_records.groupby(
            SourceColumns.ORDER_ID, as_index=False
        ).agg(
            {
                col: "first"
                for col in set(newo_records.columns) - {SourceColumns.ORDER_ID}
            }
        )
        market_records = market_records.assign(
            **{TempColumns.RECORD_TYPE: RecordType.MARKET}
        )

        result = pd.concat([allocation_records, market_records], ignore_index=True)
        return result
