from enum import Enum


class SourceColumns:
    BB_YELLOW = "BBYELLOW"
    BRANCH_LOCATION = "BRANCHLOCATION"
    BUYER_DATE_OF_BIRTH = "BUYER-DATEOFBIRTH"
    BUYER_FIRST_NAME = "BUYER-FIRSTNAME(S)"
    BUYER_SURNAME = "BUYER-SURNAME(S)"
    BUYER_DECISION_MAKER_DATE_OF_BIRTH = "BUYERDECISIONMAKER-DATEOFBIRTH"
    BUYER_DECISION_MAKER_FIRST_NAME = "BUYERDECISIONMAKER-FIRSTNAME(S)"
    BUYER_DECISION_MAKER_SURNAME = "BUYERDECISIONMAKER-SURNAME(S)"
    BUYER_DECISION_MAKER_CODE = "BUYERDECISIONMAKERCODE"
    BUYER_DECISION_MAKER_CODE_TYPE = "BUYERDECISIONMAKERCODETYPE"
    BUYER_DECISION_MAKER_NP_CODE = "BUYERDECISIONMAKERNPCODE"
    BUYER_IDENTIFICATION_CODE = "BUYERIDENTIFICATIONCODE"
    BUYER_IDENTIFICATION_CODE_TYPE = "BUYERIDENTIFICATIONCODETYPE"
    BUYER_NP_CODE = "BUYERNPCODE"
    COMMISSION_TYPE = "COMMISSIONTYPE"
    COMMISSIONS = "COMMISSIONS"
    COMMODITY_DERIVATIVE_INDICATOR = "COMMODITYDERIVATIVEINDICATOR"
    COMPLEX_TRADE_COMPONENT_ID = "COMPLEXTRADECOMPONENTID"
    COUNTERPARTY = "COUNTERPARTY"
    COUNTRY_OF_BRANCH_MEMBERSHIP = "COUNTRYOFBRANCHMEMBERSHIP"
    COUNTRY_OF_THE_BRANCH_BUYER = "COUNTRYOFTHEBRANCHFORTHEBUYER"
    COUNTRY_OF_THE_BRANCH_SELLER = "COUNTRYOFTHEBRANCHFORTHESELLER"
    COUNTRY_OF_THE_BRANCH_INVESTMENT_DECISION = (
        "COUNTRYOFTHEBRANCHRESPONSIBLEFORTHEPERSONMAKINGTHEINVESTMENTDECISION"
    )
    COUNTRY_OF_THE_BRANCH_SUPERVISOR = (
        "COUNTRYOFTHEBRANCHSUPERVISINGTHEPERSONRESPONSIBLEFORTHEEXECUTION"
    )
    CURRENCY = "CURRENCY"
    CURRENCY_2 = "CURRENCY2"
    CUSIP = "CUSIP"
    DELIVERY_TYPE = "DELIVERYTYPE"
    DERIVATIVE_NOTIONAL_INCREASE_DECREASE = "DERIVATIVENOTIONALINCREASE/DECREASE"
    DESCRIPTION = "DESCRIPTION"
    EVENT_TYPE = "EVENTTYPE"
    EXCHANGE_MIC_CODE = "EXCHANGEMICCODE"
    EXCHANGE_SHORT_NAME = "EXCHANGESHORTNAME"
    EXEC_ID = "EXECID"
    EXEC_REF_ID = "EXECREFID"
    EXECUTION_DATE = "EXECUTIONDATE"
    EXECUTION_ENTITY_IDENTIFICATION_CODE = "EXECUTIONENTITYIDENTIFICATIONCODE"
    EXECUTION_ORDER_TYPE = "EXECUTIONORDERTYPE"
    EXECUTION_TIME = "EXECUTIONTIME"
    EXECUTION_WITHIN_FIRM = "EXECUTIONWITHINFIRM"
    EXECUTION_WITHIN_FIRM_TYPE = "EXECUTIONWITHINFIRM-TYPE"
    EXECUTION_WITHIN_FIRM_NP_CODE = "EXECUTIONWITHINFIRMNPCODE"
    EXPIRY_DATE = "EXPIRYDATE"
    FILLER_NET_AMOUNT = "FILLER/NETAMOUNT"
    FUTURE_BLOOMBERG_ROOT = "FUTUREBLOOMBERGROOT"
    FUTURE_EXPIRATION_DATE = "FUTUREEXPIRATIONDATE"
    INSTRUCTIONS = "INSTRUCTIONS"
    INSTRUMENT_CLASSIFICATION = "INSTRUMENTCLASSIFICATION"
    INSTRUMENT_FULL_NAME = "INSTRUMENTFULLNAME"
    INSTRUMENT_IDENTIFICATION_CODE = "INSTRUMENTIDENTIFICATIONCODE"
    INVESTMENT_DECISION_WITH_FIRM = "INVESTMENTDECISIONWITHFIRM"
    INVESTMENT_DECISION_WITH_FIRM_TYPE = "INVESTMENTDECISIONWITHFIRM-TYPE"
    INVESTMENT_DECISION_WITH_FIRM_NP_CODE = "INVESTMENTDECISIONWITHINFIRMNPCODE"
    INVESTMENT_FIRM_COVERED = "INVESTMENTFIRMCONVEREDBY2014/65/EU"
    ISIN = "ISIN"
    LAST_FILL_TIME = "LASTFILLTIME"
    LAST_PX = "LASTPX"
    LAST_QTY = "LASTQTY"
    LE_NAME = "LENAME"
    LIFECYCLE_EVENT = "LIFECYCLEEVENT"
    MATURITY_DATE = "MATURITYDATE"
    NET_AMOUNT = "NETAMOUNT"
    NOTIONAL_CURRENCY_1 = "NOTIONALCURRENCY1"
    NOTIONAL_CURRENCY_2 = "NOTIONALCURRENCY2"
    OPTION_CONTRACT_BBG_ROOT_CODE = "OPTIONCONTRACTBLOOMBERGROOTCODE"
    OPTION_EXERCISE_STYLE = "OPTIONEXERCISESTYLE"
    OPTION_EXPIRATION_DATE = "OPTIONEXPIRATIONDATE"
    OPTION_STRIKE = "OPTIONSTRIKE"
    OPTION_TYPE = "OPTIONTYPE"
    ORDER_CUMULATIVE_QUANTITY = "ORDERCUMULATIVEQUANTITY"
    ORDER_DATE = "ORDERDATE"
    ORDER_EXECUTION_DESTINATION = "ORDEREXECUTIONDESTINATION"
    ORDER_ID = "ORDERID"
    ORDER_LIMIT_PRICE = "ORDERLIMITPRICE"
    ORDER_REFERENCE = "ORDERREFERENCE"
    ORDER_REMAINING_QUANTITY = "ORDERREMAININGQUANTITY"
    ORDER_SIDE = "ORDERSIDE"
    ORDER_STATUS = "ORDERSTATUS"
    ORDER_STATUS_TEXT = "ORDERSTATUSTEXT"
    ORDER_STOP_PRICE = "ORDERSTOPPRICE"
    ORDER_TIME_IN_FORCE = "ORDERTIMEINFORCE"
    ORDER_TIMESTAMP = "ORDERTIMESTAMP"
    ORDER_TOTAL_QUANTITY = "ORDERTOTALQUANTITY"
    ORDER_TYPE = "ORDERTYPE"
    OSI = "OSI"
    OTC_POST_TRADE_INDICATOR = "OTCPOST-TRADEINDICATOR"
    PARENT_ORDER_ID = "PARENTORDERID"
    PARENT_ORDER_REMAINING_QUANTITY = "PARENTORDERREMAININGQUANTITY"
    PORTFOLIO_MANAGER = "PORTFOLIOMANAGER"
    PRICE = "PRICE"
    PRICE_TYPE = "PRICE-TYPE"
    PRICE_CURRENCY = "PRICECURRENCY"
    PRICE_MULTIPLIER = "PRICEMULTIPLIER"
    PROGRAM_ID = "PROGRAMID"
    QUANTITY = "QUANTITY"
    QUANTITY_CURRENCY = "QUANTITYCURRENCY"
    QUANTITY_TYPE = "QUANTITYTYPE"
    REPORT_STATUS = "REPORTSTATUS"
    RIC = "RIC"
    SECURITIES_FINANCING_TRANSACTION_INDICATOR = (
        "SECURITIESFINANCINGTRANSACTIONINDICATOR"
    )
    SEDOL = "SEDOL"
    SELLER_DATE_OF_BIRTH = "SELLER-DATEOFBIRTH"
    SELLER_FIRST_NAME = "SELLER-FIRSTNAME(S)"
    SELLER_SURNAME = "SELLER-SURNAME(S)"
    SELLER_DECISION_MAKER_DATE_OF_BIRTH = "SELLERDECISIONMAKER-DATEOFBIRTH"
    SELLER_DECISION_MAKER_FIRST_NAME = "SELLERDECISIONMAKER-FIRSTNAME(S)"
    SELLER_DECISION_MAKER_SURNAME = "SELLERDECISIONMAKER-SURNAME(S)"
    SELLER_DECISION_MAKER_CODE = "SELLERDECISIONMAKERCODE"
    SELLER_DECISION_MAKER_CODE_TYPE = "SELLERDECISIONMAKERCODETYPE"
    SELLER_DECISION_MAKER_NP_CODE = "SELLERDECISIONMAKERNPCODE"
    SELLER_IDENTIFICATION_CODE = "SELLERIDENTIFICATIONCODE"
    SELLER_IDENTIFICATION_CODE_TYPE = "SELLERIDENTIFICATIONCODETYPE"
    SELLER_NP_CODE = "SELLERNPCODE"
    SEND_TO_STEELEYE = "SENDTOSTEELEYE"
    SEND_TO_STEELEYE_ELIGIBLE = "SENDTOSTEELEYEELIGIBLE"
    SHORT_SELLING_INDICATOR = "SHORTSELLINGINDICATOR"
    STRIKE_PRICE = "STRIKEPRICE"
    STRIKE_PRICE_CURRENCY = "STRIKEPRICECURRENCY"
    STRIKE_PRICE_TYPE = "STRIKEPRICETYPE"
    TERM_OF_UNDERLYING_INDEX = "TERMOFUNDERLYINGINDEX-VALUE"
    TICKER = "TICKER"
    TRADE_CANCELED = "TRADECANCELED"
    TRADER = "TRADER"
    TRADING_CAPACITY = "TRADINGCAPACITY"
    TRADING_DATE_TIME = "TRADINGDATETIME"
    TRADING_VENUE_TRANSACTION_IDENTIFICATION_CODE = (
        "TRADINGVENUETRANSACTIONIDENTIFICATIONCODE"
    )
    TRANSACTION_REFERENCE_NUMBER = "TRANSACTIONREFERENCENUMBER"
    TRANSACTION_TYPE = "TRANSACTIONTYPE"
    TRANSACTION_OF_ORDER_INDICATOR = "TRANSMISSIONOFORDERINDICATOR"
    TRANSMITTING_FIRM_IDENTIFICATION_CODE_BUYER = (
        "TRANSMITTINGFIRMIDENTIFICATIONCODEFORTHEBUYER"
    )
    TRANSMITTING_FIRM_IDENTIFICATION_CODE_SELLER = (
        "TRANSMITTINGFIRMIDENTIFICATIONCODEFORTHESELLER"
    )
    UNDERLYING_INDEX_NAME = "UNDERLYINGINDEXNAME"
    UNDERLYING_INSTRUMENT_CODE = "UNDERLYINGINSTRUMENTCODE"
    UP_FRONT_PAYMENT = "UP-FRONTPAYMENT"
    UP_FRONT_PAYMENT_CURRENCY = "UP-FRONTPAYMENTCURRENCY"
    VENUE = "VENUE"
    WAIVE_INDICATOR = "WAIVEINDICATOR"


class TempColumns:
    AGGREGATED_QUANTITY = "__aggregation_quantity__"
    AGGREGATED_PRICE = "__aggregated_price__"
    AGGREGATION_CODE = "__aggregation_code__"
    ALLOCATION_ORDER_ID = "__allocation_order_id__"
    ASSET_CLASS = "__asset_class__"
    BBGID = "__bbgid__"
    BBYELLOW = "BBYellow"
    BUYER = "__buyer__"
    BUYER_DECISION_MAKER = "__buyer_decision_maker__"
    BUY_SELL = "__buy_sell__"
    BUY_SELL_INDICATOR = "__buy_sell_indicator__"
    CLIENT = "__client__"
    COUNTERPARTY = "__counterparty__"
    CURRENCY = "__currency__"
    CURRENCY_2 = "__currency2__"
    CREATED_THROUGH_FALLBACK = "__created_through_fallback__"
    DESCRIPTION = "Description"
    DESCRIPTION_CURRENCY_1 = "__description_currency_1__"
    DESCRIPTION_CURRENCY_2 = "__description_currency_2__"
    DESCRIPTION_DATE = "__description_date__"
    EEIC_COUNTERPARTY_BUYER = "__eeic_or_counterparty_buyer__"
    EEIC_COUNTERPARTY_SELLER = "__eeic_or_counterparty_seller__"
    EEIC_COUNTERPARTY_BUYER_FILL_NA = "__eeic_or_counterparty_buyer_fill_na__"
    EEIC_COUNTERPARTY_SELLER_FILL_NA = "__eeic_or_counterparty_seller_fill_na__"
    EXECUTING_ENTITY = "__temp_executing_entity__"
    EXECUTION_DATETIME = "__execution_datetime__"
    EXECUTION_WITHIN_FIRM = "__execution_within_firm__"
    EXEC_ID = "__exec_id__"
    EXPIRY_DATE = "__expiry_date__"
    EXPIRY_DATE_SOURCE = "__expiry_date_src__"
    FLAG = "__flag__"
    FUTURE_EXPIRATION_DATE = "__future_expiration_date"
    FX_FORWARDS_DESCRIPTION = "__fx_forwards_descriptions__"
    INITIAL_QUANTITY = "__initial_quantity__"
    INSTRUCTIONS = "Instructions"
    INSTR_IDS_CURRENCY = "__isntr_ids_currency__"
    INSTR_IDS_NOTIONAL_CURRENCY_2 = "__isntr_ids_notional_currency_2__"
    INSTRUMENT_CLASSIFICATION = "__instrument_classification__"
    INSTRUMENT_FULL_NAME = "__instrument_full_name__"
    INSTRUMENT_UNIQUE_IDENTIFIER = "__instrument_unique_identifier__"
    INVESTMENT_DECISION_MAKER = "__investment_decision_maker__"
    ISIN = "__isin__"
    LAST_FILL_TIME = "__last_fill_time__"
    LAST_PX = "__last_px__"
    NEWO_CANCEL_INDICATOR = "__newo_cancel_indicator__"
    NEWO_IN_FILE = "__newo_in_file__"
    NOTIONAL_CURRENCY_2 = "__notional_currency_2__"
    OPTION_EXPIRATION_DATE = "__option_expiration_date__"
    OPTION_STRIKE_PRICE = "__option_strike_price__"
    OPTION_TYPE = "__option_type__"
    ORDER_DATE_ORDER_TIMESTAMP = "__order_date_order_timestamp__"
    ORDER_DATE_STRIPPED = "__order_date_stripped__"
    ORDER_RECEIVED = "__order_received__"
    ORDER_RECEIVED_DATE = "__order_received_date__"
    ORDERSIDE_OR_TRANSACTIONTYPE = "__orderside_or_transactiontype__"
    ORDER_STATUS = "__order_status__"
    ORDER_STATUS_TEXT = "Order Status Text"
    ORDER_TYPE = "Order Type"
    PARTY_EXECUTING_ENTITY = "__party_executing_entity__"
    PRICE = "__price__"
    PRICE_CURRENCY = "__price_currency__"
    PRICE_REFERENCE_RIC = "__price_reference_ric__"
    QUANTITY_CURRENCY = "__quantity_currency__"
    QUANTITY_TYPE = "__quantity_notation__"
    RECORD_TYPE = "__record_type__"
    REME_FLAG = "__reme_flag__"
    SELLER = "__seller__"
    SELLER_DECISION_MAKER = "__seller_decision_maker__"
    SLICED_DESCRIPTION = "__sliced_description__"
    SHORT_SELL = "__short_sell__"
    TRADED_QUANTITY = "__traded_quantity__"
    TRADER = "__trader__"
    TRADING_DATE_TIME = "__trading_date_time__"
    TRADING_DATE_TIME_EXECUTION_DATETIME = "__trading_date_time_execution_datetime__"
    TRANSACTION_REFERENCE_NUMBER = "__transaction_reference_number__"
    TRANS_REF_NUMBER_ORDER_DATE = "__trans_ref_number_order_date__"
    UNDERLYING_ISIN = "__underlying_isin__"
    UNDERLYING_SYMBOL = "__underlying_symbol__"
    UNDERLYING_SYMBOL_EXPIRY_CODE = "__underlying_symbol_expiry_code__"
    VALIDITY_PERIOD = "__validity_period__"
    VENUE = "__venue__"


class RecordType:
    ALLOCATION = "allocation_record"
    MARKET = "market_record"


class FileTypes(Enum):
    ALLOCATIONS: str = "Allocations"
    EXECUTIONS: str = "Executions"


class EventTypes:
    NEW = "New"
    CANCEL = "Cancel"
    CORRECT = "Correct"


class AlloMapKeys:
    BUY_SELL = "buy_sell_indicator"
    EARLIER_TRADING_DATE_TIME = "earlier_trading_date_time"
    FIRST_TRANSACTION_REF_NUMBER = "first_transaction_ref_number"
    MEAN_PRICE = "mean_price"
    NET_QUANTITY = "net_quantity"
    TOTAL_QUANTITY = "total_quantity"


class ExecMapKeys:
    EARLIER_EXECUTION_DATE = "earlier_execution_date"
    MEAN_PRICE = "mean_price"
    TOTAL_QUANTITY = "total_quantity"
    FIRST_EXEC_ID = "first_exec_id"
