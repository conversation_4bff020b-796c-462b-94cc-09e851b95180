import logging
from typing import Any
from typing import Dict
from typing import Union

from prefect import context
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask
from swarm.task.io.read.result import ExtractPathResult

from swarm_tasks.order.feed.enfusion.v2.static import AlloMapKeys
from swarm_tasks.order.feed.enfusion.v2.utils import read_file_generator

BUY_TYPE_LIST = ["Buy", "BuyToCover", "cover"]


class SourceEnfusionV2Columns:
    INSTRUMENT_FULL_NAME = "Instrument Full Name"
    INSTRUMENT_ID_CODE = "Instrument Identification Code"
    PRICE = "Price"
    QUANTITY = "Quantity"
    TICKER = "Ticker"
    TRADING_DATE_TIME = "Trading Date Time"
    TRANSACTION_REF_NUMBER = "Transaction Reference Number"
    TRANSACTION_TYPE = "Transaction Type"
    UNDERL_INSTRUMENT_CODE = "Underlying Instrument Code"


class Params(BaseParams):
    allocation_aggregation_flag: bool = Field(
        False,
        description="This enables the aggregation of all Allocation rows into a single record."
        "If True, relevant information will be mapped for the aggregate those records.",
    )
    delimiter: str = Field(
        ",", description="Delimiter to be used when parsing the csv file"
    )


class AllocationRecordMapper(BaseTask):
    """
    If allocation_aggregation_flag is True, this task collect four specific information and group it
    by instrument, creating an aggregation_code. It returns a dictionary with the four keys:
     - earlier_trading_date_time: the earlier trading date time in the group;
     - first_transaction_ref_number: the first transaction ref number in the group;
     - mean_price: mean price in all records within the group, weighted by quantity;
     - net_quantity: the total quantity traded in the group, considering positive all BUYI records
     and negative all SELL records.

    The second level of the dictionary in each key is the aggregated_code that holds the final
    information. The result will be used later in PrimaryTransformations.
    """

    params_class = Params

    def execute(
        self,
        file_url: Union[ExtractPathResult, str] = None,
        params: Params = None,
        **kwargs,
    ) -> Dict[str, Any]:
        return self.process(
            extractor_result=file_url, params=params, logger=self.logger
        )

    @classmethod
    def process(
        cls,
        extractor_result: Union[ExtractPathResult, str] = None,
        params: Params = None,
        logger: logging.Logger = context.get("logger"),
    ) -> Dict[str, Any]:

        if not params.allocation_aggregation_flag:
            return {}

        order_map = {}

        input_lines = read_file_generator(
            extractor_result.path.as_posix(), params.delimiter
        )

        # Get all indexes from relevant headers
        header = next(input_lines)
        instrument_fullname_pos = header.index(
            SourceEnfusionV2Columns.INSTRUMENT_FULL_NAME
        )
        instrument_id_code_pos = header.index(
            SourceEnfusionV2Columns.INSTRUMENT_ID_CODE
        )
        price_pos = header.index(SourceEnfusionV2Columns.PRICE)
        quantity_pos = header.index(SourceEnfusionV2Columns.QUANTITY)
        ticker_pos = header.index(SourceEnfusionV2Columns.TICKER)
        trading_date_time_pos = header.index(SourceEnfusionV2Columns.TRADING_DATE_TIME)
        transaction_ref_number_pos = header.index(
            SourceEnfusionV2Columns.TRANSACTION_REF_NUMBER
        )
        transaction_type_pos = header.index(SourceEnfusionV2Columns.TRANSACTION_TYPE)
        underl_instrument_code_pos = header.index(
            SourceEnfusionV2Columns.UNDERL_INSTRUMENT_CODE
        )

        for line in input_lines:
            aggregation_code = (
                line[instrument_id_code_pos]
                or line[instrument_fullname_pos]
                or line[underl_instrument_code_pos]
                or line[ticker_pos]
            )
            if not aggregation_code:
                continue

            aggregation_code += line[trading_date_time_pos][:10]
            if aggregation_code not in order_map:
                order_map.update(
                    {
                        aggregation_code: {
                            AlloMapKeys.FIRST_TRANSACTION_REF_NUMBER: None,
                            AlloMapKeys.MEAN_PRICE: [],
                            AlloMapKeys.NET_QUANTITY: 0.0,
                            AlloMapKeys.TOTAL_QUANTITY: [],
                            AlloMapKeys.BUY_SELL: "1",
                        }
                    }
                )

            trading_date_time = line[trading_date_time_pos]
            transaction_ref_number = line[transaction_ref_number_pos]
            if (
                not order_map[aggregation_code][
                    AlloMapKeys.FIRST_TRANSACTION_REF_NUMBER
                ]
                or trading_date_time
                < order_map[aggregation_code][AlloMapKeys.EARLIER_TRADING_DATE_TIME]
            ):
                order_map[aggregation_code][
                    AlloMapKeys.FIRST_TRANSACTION_REF_NUMBER
                ] = transaction_ref_number
                order_map[aggregation_code][
                    AlloMapKeys.EARLIER_TRADING_DATE_TIME
                ] = trading_date_time

            price = float(line[price_pos])
            quantity = float(line[quantity_pos])
            if quantity:
                order_map[aggregation_code][AlloMapKeys.MEAN_PRICE] += [price]
                order_map[aggregation_code][AlloMapKeys.TOTAL_QUANTITY] += [quantity]

                operation = 1 if line[transaction_type_pos] in BUY_TYPE_LIST else -1
                order_map[aggregation_code][AlloMapKeys.NET_QUANTITY] += (
                    quantity * operation
                )

        final_map = {
            AlloMapKeys.BUY_SELL: {},
            AlloMapKeys.EARLIER_TRADING_DATE_TIME: {},
            AlloMapKeys.FIRST_TRANSACTION_REF_NUMBER: {},
            AlloMapKeys.MEAN_PRICE: {},
            AlloMapKeys.NET_QUANTITY: {},
        }
        for code in order_map.keys():
            if len(order_map[code][AlloMapKeys.TOTAL_QUANTITY]) <= 1:
                continue

            # Define BUY_SELL = '2' for negative net quantities
            if order_map[code][AlloMapKeys.NET_QUANTITY] < 0:
                order_map[code][AlloMapKeys.BUY_SELL] = "2"

            # Net quantity should be always positive
            order_map[code][AlloMapKeys.NET_QUANTITY] = abs(
                order_map[code][AlloMapKeys.NET_QUANTITY]
            )

            # Calculate the average weighted price from the list of 'Price' and 'Quantity'
            total_price = 0.0
            for qty, px in zip(
                order_map[code][AlloMapKeys.TOTAL_QUANTITY],
                order_map[code][AlloMapKeys.MEAN_PRICE],
            ):
                total_price += qty * px

            total_quantity = sum(order_map[code][AlloMapKeys.TOTAL_QUANTITY])
            order_map[code][AlloMapKeys.MEAN_PRICE] = 0.0
            if total_quantity > 0:
                order_map[code][AlloMapKeys.MEAN_PRICE] = total_price / total_quantity

            # Invert map from aggregation_code.map_key to map_key.aggregation_code
            for key in [
                AlloMapKeys.BUY_SELL,
                AlloMapKeys.EARLIER_TRADING_DATE_TIME,
                AlloMapKeys.FIRST_TRANSACTION_REF_NUMBER,
                AlloMapKeys.MEAN_PRICE,
                AlloMapKeys.NET_QUANTITY,
            ]:
                final_map[key].update({code: order_map[code][key]})

        return final_map
