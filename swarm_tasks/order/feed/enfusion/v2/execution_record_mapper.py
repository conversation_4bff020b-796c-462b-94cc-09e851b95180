import logging
from typing import Any
from typing import Dict
from typing import Union

from prefect import context
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask
from swarm.task.io.read.result import ExtractPathResult

from swarm_tasks.order.feed.enfusion.v2.static import ExecMapKeys
from swarm_tasks.order.feed.enfusion.v2.utils import read_file_generator


class SourceEnfusionV2Columns:
    ORDER_ID = "Order Id"
    EXECUTION_DATE = "Execution Date"
    LAST_PX = "Last Px"
    LAST_QTY = "Last Qty"
    EXEC_ID = "Exec ID"


class Params(BaseParams):
    fill_by_fill_flag: bool = Field(
        True,
        description="This param enables the processing of Average Execution Data instead of Fill by"
        "Fill. When True, records are ingested as usual, when False, all PARF/FILL per order id"
        "will be merged into a single record.",
    )
    delimiter: str = Field(
        ",", description="Delimiter to be used when parsing the csv file"
    )


class ExecutionRecordMapper(BaseTask):
    """
    If fill_by_fill_flag is not True, this task collect four specific information and
    organize it by order_id to be used later in PrimaryTransformations. It returns a
    dictionary with the four keys:
     - earlier_execution_date: the earlier execution time in the order id;
     - mean_price: mean price in all records with the same order id;
     - total_quantity: sum of quantity in all records with the same order id;
     - first_exec_id: earlier exec_id in the order id.

    The second level of the dictionary in each key is the order_id that holds the final
    information.
    """

    params_class = Params

    def execute(
        self,
        file_url: Union[ExtractPathResult, str] = None,
        params: Params = None,
        **kwargs,
    ) -> Dict[str, Any]:
        return self.process(
            extractor_result=file_url, params=params, logger=self.logger
        )

    @classmethod
    def process(
        cls,
        extractor_result: Union[ExtractPathResult, str] = None,
        params: Params = None,
        logger: logging.Logger = context.get("logger"),
    ) -> Dict[str, Any]:
        if params.fill_by_fill_flag:
            return {}

        order_map = {}

        input_lines = read_file_generator(
            extractor_result.path.as_posix(), params.delimiter
        )

        # Get all indexes from relevant headers
        header = next(input_lines)
        order_id_pos = header.index(SourceEnfusionV2Columns.ORDER_ID)
        execution_date_pos = header.index(SourceEnfusionV2Columns.EXECUTION_DATE)
        exec_id_pos = header.index(SourceEnfusionV2Columns.EXEC_ID)
        last_px_pos = header.index(SourceEnfusionV2Columns.LAST_PX)
        last_qty_pos = header.index(SourceEnfusionV2Columns.LAST_QTY)

        for line in input_lines:
            order_id = line[order_id_pos]
            if order_id not in order_map:
                order_map.update(
                    {
                        order_id: {
                            ExecMapKeys.EARLIER_EXECUTION_DATE: None,
                            ExecMapKeys.MEAN_PRICE: [],
                            ExecMapKeys.TOTAL_QUANTITY: [],
                            ExecMapKeys.FIRST_EXEC_ID: None,
                        }
                    }
                )

            execution_date = line[execution_date_pos]
            fill_id = line[exec_id_pos]
            if (
                not order_map[order_id][ExecMapKeys.EARLIER_EXECUTION_DATE]
                or execution_date
                < order_map[order_id][ExecMapKeys.EARLIER_EXECUTION_DATE]
            ):
                order_map[order_id][ExecMapKeys.EARLIER_EXECUTION_DATE] = execution_date
                order_map[order_id][ExecMapKeys.FIRST_EXEC_ID] = fill_id

            last_px = float(line[last_px_pos])
            last_qty = float(line[last_qty_pos])
            if last_px and last_qty:
                order_map[order_id][ExecMapKeys.MEAN_PRICE] += [last_px]
                order_map[order_id][ExecMapKeys.TOTAL_QUANTITY] += [last_qty]

        final_map = {
            ExecMapKeys.EARLIER_EXECUTION_DATE: {},
            ExecMapKeys.MEAN_PRICE: {},
            ExecMapKeys.TOTAL_QUANTITY: {},
            ExecMapKeys.FIRST_EXEC_ID: {},
        }
        for id in order_map.keys():
            # Calculate the average weighted price from the list of 'Last Px' and 'Last Qty'
            total_px = 0.0
            for qty, px in zip(
                order_map[id][ExecMapKeys.TOTAL_QUANTITY],
                order_map[id][ExecMapKeys.MEAN_PRICE],
            ):
                total_px += qty * px

            total_qty = sum(order_map[id][ExecMapKeys.TOTAL_QUANTITY])
            if total_qty <= 0:
                order_map[id][ExecMapKeys.TOTAL_QUANTITY] = 0.0
                order_map[id][ExecMapKeys.MEAN_PRICE] = 0.0
            else:
                order_map[id][ExecMapKeys.TOTAL_QUANTITY] = total_qty
                order_map[id][ExecMapKeys.MEAN_PRICE] = total_px / total_qty

            # Invert map from order_id.map_key to map_key.order_id, to be used in transformations
            for key in [
                ExecMapKeys.TOTAL_QUANTITY,
                ExecMapKeys.MEAN_PRICE,
                ExecMapKeys.FIRST_EXEC_ID,
                ExecMapKeys.EARLIER_EXECUTION_DATE,
            ]:
                final_map[key].update({id: order_map[id][key]})

        return final_map
