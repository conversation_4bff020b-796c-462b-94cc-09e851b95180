import logging
import re
from pathlib import Path

import pandas as pd
from prefect import context
from prefect.engine.signals import FAIL
from prefect.engine.signals import SKIP
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import add_prefix as add_str_prefix
from swarm.conf import Settings
from swarm.task.auditor import Auditor
from swarm.task.base import BaseTask
from swarm.task.io.read.result import ExtractPathResult

from swarm_tasks.order.feed.aladdin.static import CustomOrdType
from swarm_tasks.order.feed.aladdin.static import DevColumns
from swarm_tasks.order.feed.aladdin.static import FileTypes
from swarm_tasks.order.feed.aladdin.static import FillSourceColumns
from swarm_tasks.order.feed.aladdin.static import OrderDetailSourceColumns
from swarm_tasks.order.feed.aladdin.static import OrderSourceColumns
from swarm_tasks.order.feed.aladdin.static import PlacementSourceColumns
from swarm_tasks.order.feed.aladdin.static import TransactionSourceColumns


class AladdinOrderFrameMerger(BaseTask):
    """This task is used to merge data from the five files expected to process Aladdin trades
    - AladdinTCA.cfs_DERIV.Order.YYYYMMDD.csv
    - AladdinTCA.cfs_DERIV.OrderDetail.YYYYMMDD.csv
    - AladdinTCA.cfs_DERIV.Transaction.YYYYMMDD.csv
    - AladdinTCA.cfs_DERIV.Placement.YYYYMMDD.csv
    - AladdinTCA.cfs_DERIV.Fill.YYYYMMDD.csv

    The task processes these files, adds necessary column prefixes, performs various merges, and outputs a
    final CSV file.

    Returns:
    ExtractPathResult: The result contains the path to the merged CSV file, which is returned as
    an ExtractPathResult object.
    """

    def execute(
        self, pre_process_result: dict = None, file_url: str = None, **kwargs
    ) -> ExtractPathResult:
        return self.process(
            pre_process_result=pre_process_result,
            file_url=file_url,
            logger=self.logger,
            auditor=self.auditor,
        )

    @classmethod
    def process(
        cls,
        pre_process_result: dict,
        file_url: str,
        auditor: Auditor = None,
        logger=context.get("logger"),
    ):

        expected_file_types = [
            file_type
            for key, file_type in FileTypes.__dict__.items()
            if isinstance(key, str) and not key.startswith("__")
        ]
        # Raise a failure if all the expected files are not present in result
        if len(expected_file_types) != len(pre_process_result):
            raise FAIL(
                f"Expected file types not received, missing file_types : "
                f"{set(expected_file_types) - set(pre_process_result.keys())} "
            )

        # Add file_type as prefix to all columns in that file. For example, the columns
        # id, date, qty in order file is renamed to order.id, order.date, order.qty
        pre_process_result = cls.add_file_type_as_col_prefix(
            pre_process_result=pre_process_result, auditor=auditor
        )

        expected_order_columns = [
            add_str_prefix(FileTypes.ORDER, col_name)
            for key, col_name in OrderSourceColumns.__dict__.items()
            if isinstance(key, str) and not key.startswith("__")
        ]

        # Only keep columns which are required downstream
        target_df = pre_process_result[FileTypes.ORDER][expected_order_columns]

        target_df = cls.merge_order_details_df(
            target_df=target_df,
            order_details_df=pre_process_result[FileTypes.ORDER_DETAILS],
            logger=logger,
        )

        # Add col OrderType to identify market side and client allocation orders
        market_side_order_mask = cls.get_market_side_order_identifier(target_df)

        # Merge target_df with transaction_df
        target_df = cls.merge_transaction_df(
            target_df=target_df,
            transaction_df=pre_process_result[FileTypes.TRANSACTION],
            market_side_order_mask=market_side_order_mask,
            logger=logger,
        )

        target_df = cls.merge_placement_df(
            target_df=target_df,
            placement_df=pre_process_result[FileTypes.PLACEMENT],
            logger=logger,
        )

        target_df = cls.merge_fill_df(
            target_df=target_df,
            fill_df=pre_process_result[FileTypes.FILL],
            logger=logger,
        )

        target_df = cls.post_processing(target_df=target_df)

        # Extract the base filename from the file_url by removing any file type suffix,
        # as the output is a merge of all 5 files.
        filename = re.sub(
            r"\.(Transaction|Fill|Placement|Order|OrderDetail)\.",
            ".",
            Path(file_url).name,
        )

        path = Settings.context.sources_dir.joinpath(filename)

        target_df.fillna(pd.NA).to_csv(path_or_buf=path)
        return ExtractPathResult(path=path)

    @staticmethod
    def post_processing(target_df: pd.DataFrame) -> pd.DataFrame:
        """
        This method executes an aggregation on the orderdetail.portfolioId,
        per transaction.orderId column which needs to be done before the next task
        CSVFileSplitter is executed.
        """

        order_detail_portfolio_id = add_prefix(
            FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.PORTFOLIO_ID
        )
        transaction_order_id = add_prefix(
            FileTypes.TRANSACTION, TransactionSourceColumns.ORDER_ID
        )

        portfolio_id_per_transaction_order_id_dict = (
            target_df.dropna(subset=[order_detail_portfolio_id])
            .drop_duplicates(subset=[order_detail_portfolio_id, transaction_order_id])
            .groupby(transaction_order_id)[order_detail_portfolio_id]
            .agg(list)
            .to_dict()
        )

        target_df[DevColumns.AGGREGATE_PORTFOLIO_ID] = (
            target_df[transaction_order_id]
            .map(portfolio_id_per_transaction_order_id_dict)
            .fillna(pd.NA)
        )

        return target_df

    @staticmethod
    def get_market_side_order_identifier(target_df) -> pd.Series:
        """
        This method adds a new col '__order_type__' to target_df. This col indicates whether the order
        is a MarketSide order or a Client Allocation
        :param target_df: the target dataframe to which the new col should be added
        :return: a mask to identify market_side orders
        """
        market_side_order_mask = target_df.loc[
            :,
            add_str_prefix(
                FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID
            ),
        ].isnull()
        # Identify orders as Market Side or Client Allocation
        target_df.loc[
            market_side_order_mask, DevColumns.ORDER_TYPE
        ] = CustomOrdType.MARKET
        target_df.loc[
            ~market_side_order_mask, DevColumns.ORDER_TYPE
        ] = CustomOrdType.CLIENT
        return market_side_order_mask

    @classmethod
    def add_file_type_as_col_prefix(
        cls, pre_process_result: dict, auditor: Auditor
    ) -> dict:
        """
        Adds the file_type as the prefix to each col in the dataframes. For example, in the order
        dataframe, each column will get prefixed by "order."
        :param auditor:
        :param pre_process_result:
        :return:
        """
        empty_file_types = []
        for file_type, df in pre_process_result.items():
            if df.empty:
                empty_file_types.append(file_type)
                continue
            pre_process_result[file_type] = df.add_prefix(
                file_type + "."
            ).convert_dtypes()
        if len(empty_file_types) > 0:
            msg = f"[!] Skipping processing \n" f"empty {empty_file_types} received"
            auditor.add(msg)
            raise SKIP(f"Skipping the flow run since {empty_file_types} is empty")
        return pre_process_result

    @classmethod
    def merge_order_details_df(
        cls,
        target_df: pd.DataFrame,
        order_details_df: pd.DataFrame,
        logger: logging.Logger,
    ) -> pd.DataFrame:
        """This method merges the data from order_details file to target_df"""
        logger.info("Merging with OrderDetails file")
        order_details_required_cols = [
            add_str_prefix(FileTypes.ORDER_DETAILS, col_name)
            for key, col_name in OrderDetailSourceColumns.__dict__.items()
            if isinstance(key, str) and not key.startswith("__")
        ]
        target_df = target_df.merge(
            order_details_df[order_details_required_cols],
            left_on=add_str_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_ID),
            right_on=add_str_prefix(
                FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID
            ),
            how="left",
        )
        return target_df

    @classmethod
    def merge_transaction_df(
        cls,
        target_df: pd.DataFrame,
        transaction_df: pd.DataFrame,
        market_side_order_mask: pd.Series,
        logger: logging.Logger,
    ) -> pd.DataFrame:
        """This method merges the data from transaction file to target_df"""
        logger.info("Merging with Transaction file")

        # For client allocations, the logic is to match based on both OrderID & portfolioId
        # MarketSideOrderID (from orderDetails) and portfolio_id across both the dataframes
        transaction_df_required_cols = [
            add_str_prefix(FileTypes.TRANSACTION, col_name)
            for key, col_name in TransactionSourceColumns.__dict__.items()
            if isinstance(key, str) and not key.startswith("__")
        ]
        client_target_df_with_transaction_data = target_df.loc[
            ~market_side_order_mask
        ].merge(
            transaction_df[transaction_df_required_cols],
            left_on=[
                add_str_prefix(
                    FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID
                ),
                add_str_prefix(
                    FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.PORTFOLIO_ID
                ),
            ],
            right_on=[
                add_str_prefix(
                    FileTypes.TRANSACTION, TransactionSourceColumns.ORDER_ID
                ),
                add_str_prefix(
                    FileTypes.TRANSACTION, TransactionSourceColumns.PORTFOLIO_ID
                ),
            ],
            how="left",
        )

        # For MarketSide orders, any match with just matching the order ids should work
        market_target_df_with_transaction_data = target_df.loc[
            market_side_order_mask
        ].merge(
            transaction_df[transaction_df_required_cols].drop_duplicates(
                add_str_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.ORDER_ID)
            ),
            left_on=add_str_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_ID),
            right_on=add_str_prefix(
                FileTypes.TRANSACTION, TransactionSourceColumns.ORDER_ID
            ),
            how="left",
        )
        return pd.concat(
            [
                client_target_df_with_transaction_data,
                market_target_df_with_transaction_data,
            ]
        ).reset_index(drop=True)

    @classmethod
    def merge_placement_df(
        cls, target_df: pd.DataFrame, placement_df: pd.DataFrame, logger: logging.Logger
    ) -> pd.DataFrame:
        """This method merges the data from placement file to target_df"""
        logger.info("Merging with Placement file")
        placement_df_required_cols = [
            add_str_prefix(FileTypes.PLACEMENT, col_name)
            for key, col_name in PlacementSourceColumns.__dict__.items()
            if isinstance(key, str) and not key.startswith("__")
        ]

        target_df = target_df.merge(
            placement_df[placement_df_required_cols],
            left_on=add_str_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_ID),
            right_on=add_str_prefix(
                FileTypes.PLACEMENT, PlacementSourceColumns.ORDER_ID
            ),
            how="left",
        )
        return target_df

    @classmethod
    def merge_fill_df(
        cls, target_df: pd.DataFrame, fill_df: pd.DataFrame, logger: logging.Logger
    ) -> pd.DataFrame:
        """This method merges the data from fill file to target_df"""
        logger.info("Merging with Fill file")
        fill_df_required_cols = [
            add_str_prefix(FileTypes.FILL, col_name)
            for key, col_name in FillSourceColumns.__dict__.items()
            if isinstance(key, str) and not key.startswith("__")
        ]

        target_df = target_df.merge(
            fill_df[fill_df_required_cols],
            left_on=add_str_prefix(
                FileTypes.PLACEMENT, PlacementSourceColumns.PLACEMENT_ID
            ),
            right_on=add_str_prefix(FileTypes.FILL, FillSourceColumns.PLACEMENT_ID),
            how="left",
        )

        return target_df
