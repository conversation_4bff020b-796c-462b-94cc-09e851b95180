import logging
from datetime import datetime
from pathlib import Path
from typing import Dict
from typing import List

import numpy as np
import pandas as pd
from prefect import context
from prefect.engine.signals import SKIP
from pydantic import Field
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.feeds.order.bbg.emsi.instrument_store import (
    fetch_data_from_instrument_store,
)
from se_core_tasks.utils.frame_manipulation import add_missing_columns
from se_elastic_schema.static.mifid2 import OrderStatus
from swarm.conf import Settings
from swarm.task.auditor import Auditor
from swarm.task.base import BaseParams
from swarm.task.transform.base import BaseTask

from swarm_tasks.order.feed.bbg.emsi.static import DATE_TIME_FORMAT_CAXTON
from swarm_tasks.order.feed.bbg.emsi.static import SourceColumns
from swarm_tasks.order.feed.bbg.emsi.static import TempColumns
from swarm_tasks.order.generic.utils import replace_parfs_with_flat_average

logger_ = context.get("logger")


class Params(BaseParams):
    fill_by_fill_flag: bool = Field(
        True,
        description="This param enables the processing of Average Execution Data instead of Fill by Fill."
        "When True, records are ingested as usual, "
        "When False, all PARF/FILL per order id will be merged into a single record.",
    )
    remove_parf_fill_before_zero_cancel: bool = Field(
        default=False,
        description="When active this will look for Cancels with zero Filled Quantity. If for the same"
        "order id there are any PARF or FILL with an earlier Receive Date Time. Those are removed",
    )
    datetime_format: str = Field(
        default=DATE_TIME_FORMAT_CAXTON,
        description="Datetime Format used to compare dates for the remove_parf_fill_before_zero_cancel param",
    )


class BbgEmsiFrameTransformer(BaseTask):
    """
    Merges and transforms te BBG EMSI orders and instrument data

    Specs: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2745696257/Order+BBG+EMSI+EOD+File+V2
    """

    params_class = Params

    def execute(
        self,
        result: Dict[str, pd.DataFrame] = None,
        params: Params = None,
        file_paths: pd.DataFrame = pd.DataFrame(),
        **kwargs,
    ) -> ExtractPathResult:

        return self.process(
            result=result,
            file_paths=file_paths,
            params=params,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        result: Dict[str, pd.DataFrame] = None,
        file_paths: pd.DataFrame = pd.DataFrame(),
        params: Params = None,
        auditor: Auditor = None,
        logger: logging.Logger = logger_,
    ) -> ExtractPathResult:
        if not result:
            raise SKIP("Upstream result is empty")

        merged_frame = cls.merge_instrument_data_from_instrument_file(
            df_dict=result,
            auditor=auditor,
            logger=logger,
        )
        merged_frame = cls.merge_instrument_data_from_instrument_store(
            df=merged_frame, logger=logger
        )
        transformed_df = cls.transform_file(df=merged_frame)
        if params.remove_parf_fill_before_zero_cancel:
            transformed_df = cls.remove_parf_fill_before_zero_cancel(
                dataframe=transformed_df, params=params
            )

        if not params.fill_by_fill_flag:
            transformed_df = replace_parfs_with_flat_average(df=transformed_df)

        transformed_df = transformed_df.sort_values(
            by=[
                SourceColumns.ORDER_ID,
                SourceColumns.ROUTE_ID,
                SourceColumns.RECEIVE_DATE_TIME,
            ],
            ascending=[1, 1, 1],
        )
        transformed_df = transformed_df.reset_index(drop=True)
        logger.info(f"Transformed record view's shape is {transformed_df.shape}")

        # Skipping the flow when there is no data present after applying transformation
        if transformed_df.empty:
            raise SKIP("Skipping Flow: No data present after applying transformation")

        source_dir = cls._get_source_dir()

        # Resolve file_path of the orders file
        trigger_file_path = (
            file_paths["s3_orders_file_url"].values[0]
            if "s3_orders_file_url" in file_paths.columns
            else "transformed_file"
        )
        csv_file_path = source_dir.joinpath(f"{Path(trigger_file_path).name}")
        transformed_df.to_csv(csv_file_path, index=False, sep=",")
        return ExtractPathResult(path=csv_file_path)

    @classmethod
    def merge_instrument_data_from_instrument_file(
        cls,
        df_dict: Dict[str, pd.DataFrame],
        auditor: Auditor = None,
        logger: logging.Logger = logger_,
    ) -> pd.DataFrame:
        """
        Contextually merges the instrument data to the order data
        :param df_dict: Dict containing the input dataframes
        :returns: Merged df
        """
        instrument_data = df_dict.get("instrument", pd.DataFrame())
        order_data = df_dict.get("orders", pd.DataFrame())

        if order_data.empty:
            raise SKIP("Orders data not found")

        instrument_data = add_missing_columns(
            dataframe=instrument_data,
            dataframe_columns=SourceColumns.get_required_instrument_columns(),
        )

        # Instrument records without ParseKey and Order Number or without ParseKey will be skipped in the merge
        # and we are auditing the number of records for those cases before the merge
        cls.audit_invalid_instrument_records(instrument_data, auditor, logger)

        # ORDER_NUMBER value is converted to string there can be a case of empty ORDER_NUMBER
        # we will need to fill it with a temp string value. For consistency need to change
        # this column to String for the later on merge with ORDER_HASH.
        instrument_data[SourceColumns.ORDER_NUMBER] = (
            pd.to_numeric(
                instrument_data[SourceColumns.ORDER_NUMBER],
                errors="coerce",
                downcast="integer",
            )
            .astype("Int64")
            .astype("string")
        )
        # Handles the case when encountering empty ORDER_NUMBER
        instrument_data[
            [SourceColumns.PARSE_KEY, SourceColumns.ORDER_NUMBER]
        ] = instrument_data[
            [SourceColumns.PARSE_KEY, SourceColumns.ORDER_NUMBER]
        ].fillna(
            "temp-fillna-value"
        )

        # Group by SourceColumns.ORDER_NUMBER and fill null values with the first non-null value
        # in each group for columns SourceColumns.PM_NAME and SourceColumns.TRAN_ACCOUNT.
        # These values will be used in a merge at the end of this function
        instrument_data[SourceColumns.PM_NAME] = instrument_data.groupby(
            SourceColumns.ORDER_NUMBER
        )[SourceColumns.PM_NAME].transform(lambda x: x.ffill().bfill().iloc[0])
        instrument_data[SourceColumns.TRAN_ACCOUNT] = instrument_data.groupby(
            SourceColumns.ORDER_NUMBER
        )[SourceColumns.TRAN_ACCOUNT].transform(lambda x: x.ffill().bfill().iloc[0])

        instrument_data = instrument_data.groupby(
            SourceColumns.PARSE_KEY, as_index=False
        ).nth(0)
        merged_df = order_data.merge(
            instrument_data.loc[
                :,
                [
                    SourceColumns.CONTRACT_EXPIRATION,
                    SourceColumns.FULL_EXCH_SYMBOL,
                    SourceColumns.ISIN_INSTRUMENT,
                    SourceColumns.LOCAL_EXCH_SYMBOL,
                    SourceColumns.LAST_MARKET_INSTRUMENT,
                    SourceColumns.OCC_SYMBOL,
                    SourceColumns.PARSE_KEY,
                    SourceColumns.SECURITY_NAME,
                ],
            ],
            left_on=SourceColumns.TICKER,
            right_on=SourceColumns.PARSE_KEY,
            how="left",
            suffixes=("", "_instr"),
        )
        # Get additional columns from instrument_data based on the order ID
        # Note that we have already made sure that rows with the same SourceColumns.ORDER_NUMBER
        # have the first non-null value of SourceColumns.PM_NAME and SourceColumns.TRAN_ACCOUNT
        instrument_data = instrument_data.drop_duplicates(
            subset=[SourceColumns.ORDER_NUMBER]
        )

        if SourceColumns.ORDER_HASH in merged_df.columns:
            merged_df["ORDER_HASH_TEMP"] = (
                pd.to_numeric(
                    merged_df[SourceColumns.ORDER_HASH],
                    errors="coerce",
                    downcast="integer",
                )
                .astype("Int64")
                .astype("string")
            )

            merged_df = merged_df.merge(
                instrument_data.loc[
                    :,
                    [
                        SourceColumns.PM_NAME,
                        SourceColumns.TRAN_ACCOUNT,
                        SourceColumns.ORDER_NUMBER,
                    ],
                ],
                left_on="ORDER_HASH_TEMP",
                right_on=SourceColumns.ORDER_NUMBER,
                how="left",
                suffixes=("", "_second_join"),
            )
            merged_df = merged_df.drop(columns=["ORDER_HASH_TEMP"])

        return merged_df

    @classmethod
    def merge_instrument_data_from_instrument_store(
        cls, df: pd.DataFrame, logger: logging.Logger
    ) -> pd.DataFrame:
        """
        Contextually merges the instrument data from instrument store to the order data
        for records which did not have instrument data in current day's instrument file

        :param df: DF where the instrument data is to be merged
        :param logger: Logger instance
        :returns: Merged df
        """
        if all(df[SourceColumns.ISIN_INSTRUMENT].notnull()):
            return df

        tickers_with_no_isin = df.loc[
            df[SourceColumns.ISIN_INSTRUMENT].isnull()
            & df[SourceColumns.TICKER].notnull()
        ][SourceColumns.TICKER].unique()

        data_store_response = fetch_data_from_instrument_store(
            ticker_list=tickers_with_no_isin, bundle=Settings.bundle
        )

        instrument_data_list = list(data_store_response.values())

        if not instrument_data_list:
            return df

        instrument_data = pd.concat(instrument_data_list)

        instrument_data = instrument_data.groupby(
            SourceColumns.PARSE_KEY, as_index=False
        ).nth(0)

        merged_df = df.merge(
            instrument_data,
            left_on=SourceColumns.TICKER,
            right_on=SourceColumns.PARSE_KEY,
            how="left",
            suffixes=("", "_temp"),
        )
        merged_df[SourceColumns.ISIN_INSTRUMENT] = merged_df[
            SourceColumns.ISIN_INSTRUMENT
        ].fillna(merged_df[f"{SourceColumns.ISIN_INSTRUMENT}_temp"])
        merged_df[SourceColumns.LOCAL_EXCH_SYMBOL] = merged_df[
            SourceColumns.LOCAL_EXCH_SYMBOL
        ].fillna(merged_df[f"{SourceColumns.LOCAL_EXCH_SYMBOL}_temp"])
        merged_df[SourceColumns.LAST_MARKET_INSTRUMENT] = merged_df[
            SourceColumns.LAST_MARKET_INSTRUMENT
        ].fillna(merged_df[f"{SourceColumns.LAST_MARKET_INSTRUMENT}_temp"])
        merged_df[SourceColumns.SECURITY_NAME] = merged_df[
            SourceColumns.SECURITY_NAME
        ].fillna(merged_df[f"{SourceColumns.SECURITY_NAME}_temp"])

        return merged_df

    @classmethod
    def transform_file(cls, df: pd.DataFrame) -> pd.DataFrame:
        """
        Takes in the reference dataframe and then processes it according
        to order flow record_creation specs
        :param df: Input DataFrame
        :returns: Transformed df
        """
        if df.empty:
            return df

        # Sort before we transform, as we want to ffill by timestamp for some fields.
        df = df.sort_values(
            by=[
                SourceColumns.ORDER_ID,
                SourceColumns.ROUTE_ID,
                SourceColumns.RECEIVE_DATE_TIME,
            ],
            ascending=[True, True, True],
        ).reset_index(drop=True)

        # Assign timestamps.orderSubmitted to NEWOs from other Order records
        condition = (
            (
                df[SourceColumns.ACTIVITY].str.fullmatch(
                    "Exec-Acknowledged", case=False, na=False
                )
            )
            & (df[SourceColumns.STATUS].str.fullmatch("Working", case=False, na=False))
            & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
        )
        cls._propagate_field_conditionally_from_group(
            source_frame=df,
            condition=condition,
            source_column=SourceColumns.RECEIVE_DATE_TIME,
            group_columns=[SourceColumns.ORDER_ID, SourceColumns.ROUTE_ID],
            target_column=TempColumns.ORDER_SUBMITTED_NEWO,
        )

        # Assign timestamps.orderSubmitted to CAMEs from other Cancel records
        condition = (
            (
                df[SourceColumns.ACTIVITY].str.fullmatch(
                    "Client-Cancel-Request", case=False, na=False
                )
            )
            & (df[SourceColumns.STATUS].str.fullmatch("CXLREQ", case=False, na=False))
            & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
        )
        cls._propagate_field_conditionally_from_group(
            source_frame=df,
            condition=condition,
            source_column=SourceColumns.RECEIVE_DATE_TIME,
            group_columns=[SourceColumns.ORDER_ID, SourceColumns.ROUTE_ID],
            target_column=TempColumns.ORDER_SUBMITTED_CANCEL,
        )

        # Assign timestamps.orderReceived to NEWOs from other Order records
        condition = (
            (
                df[SourceColumns.ACTIVITY].str.fullmatch(
                    "Bloomberg-Sent", case=False, na=False
                )
            )
            & (df[SourceColumns.STATUS].str.fullmatch("Sent", case=False, na=False))
            & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
        )
        cls._propagate_field_conditionally_from_group(
            source_frame=df,
            condition=condition,
            source_column=SourceColumns.RECEIVE_DATE_TIME,
            group_columns=[SourceColumns.ORDER_ID, SourceColumns.ROUTE_ID],
            target_column=TempColumns.ORDER_RECEIVED_NEWO,
        )

        # Assign timestamps.orderReceived to CAMEs from other Cancel records
        condition = (
            (
                df[SourceColumns.ACTIVITY].str.fullmatch(
                    "Exec-Pending-Cancel", case=False, na=False
                )
            )
            & (df[SourceColumns.STATUS].str.fullmatch("CxlPend", case=False, na=False))
            & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
        )
        cls._propagate_field_conditionally_from_group(
            source_frame=df,
            condition=condition,
            source_column=SourceColumns.RECEIVE_DATE_TIME,
            group_columns=[SourceColumns.ORDER_ID, SourceColumns.ROUTE_ID],
            target_column=TempColumns.ORDER_RECEIVED_CANCEL,
        )

        # Assign timestamps.tradingDateTime to NEWOs from other Order records
        condition = (
            (
                df[SourceColumns.ACTIVITY].str.fullmatch(
                    "Exec-Trade", case=False, na=False
                )
            )
            & (df[SourceColumns.STATUS].str.fullmatch("Filled", case=False, na=False))
            & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
        )
        cls._propagate_field_conditionally_from_group(
            source_frame=df,
            condition=condition,
            source_column=SourceColumns.RECEIVE_DATE_TIME,
            group_columns=[SourceColumns.ORDER_ID, SourceColumns.ROUTE_ID],
            target_column=TempColumns.TRADING_DATE_TIME_NEWO,
        )

        # Assign timestamps.tradingDateTime to CAMEs from other Cancel records
        condition = (
            (
                df[SourceColumns.ACTIVITY].str.fullmatch(
                    "Exec-Cancel", case=False, na=False
                )
            )
            & (df[SourceColumns.STATUS].str.fullmatch("Cancel", case=False, na=False))
            & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
        )
        cls._propagate_field_conditionally_from_group(
            source_frame=df,
            condition=condition,
            source_column=SourceColumns.RECEIVE_DATE_TIME,
            group_columns=[SourceColumns.ORDER_ID, SourceColumns.ROUTE_ID],
            target_column=TempColumns.TRADING_DATE_TIME_CANCEL,
        )

        # Assign timestamps.orderStatusUpdated from CAME/NEWO/REME to other records.
        # This will be assigned to the orderStatusUpdated for fills/parfs downstream
        condition = (
            (
                (
                    df[SourceColumns.ACTIVITY].str.fullmatch(
                        "New-Route", case=False, na=False
                    )
                )
                & (df[SourceColumns.STATUS].str.fullmatch("Sent", case=False, na=False))
                & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
            )
            | (
                (
                    df[SourceColumns.ACTIVITY].str.fullmatch(
                        "Exec-Done", case=False, na=False
                    )
                )
                & (
                    df[SourceColumns.STATUS].str.fullmatch(
                        "Cancel", case=False, na=False
                    )
                )
                & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
            )
            | (
                (
                    df[SourceColumns.ACTIVITY].str.fullmatch(
                        "Exec-Replace", case=False, na=False
                    )
                )
                & (
                    df[SourceColumns.STATUS].str.fullmatch(
                        "Replaced", case=False, na=False
                    )
                )
                & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
            )
            | (
                (
                    df[SourceColumns.ACTIVITY].str.fullmatch(
                        "Exec-Unmatched-Replace", case=False, na=False
                    )
                )
                & (
                    df[SourceColumns.STATUS].str.fullmatch(
                        "Replaced", case=False, na=False
                    )
                )
                & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
            )
        )

        cls._propagate_field_conditionally_from_group(
            source_frame=df,
            condition=condition,
            source_column=SourceColumns.RECEIVE_DATE_TIME,
            group_columns=[SourceColumns.ORDER_ID, SourceColumns.ROUTE_ID],
            target_column=TempColumns.ORDER_STATUS_UPDATED_PARF_FILL,
            bfill_after_ffill=False,
        )

        # If order status updated is null for PARF/FILL (this can happen when there are no
        # NEWOs/CAMEs/REMEs present in the file), use the trading date time
        df[TempColumns.ORDER_STATUS_UPDATED_PARF_FILL] = df[
            TempColumns.ORDER_STATUS_UPDATED_PARF_FILL
        ].fillna(df[TempColumns.TRADING_DATE_TIME_NEWO])

        # Assign timestamps.externalOrderReceived to NEWOs from other records
        condition = (
            (
                df[SourceColumns.ACTIVITY].str.fullmatch(
                    "Exec-Acknowledged", case=False, na=False
                )
            )
            & (df[SourceColumns.STATUS].str.fullmatch("Working", case=False, na=False))
            & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
        )
        cls._propagate_field_conditionally_from_group(
            source_frame=df,
            condition=condition,
            source_column=SourceColumns.RECEIVE_DATE_TIME,
            group_columns=[SourceColumns.ORDER_ID, SourceColumns.ROUTE_ID],
            target_column=TempColumns.EXTERNAL_ORDER_RECEIVED_NEWO,
        )

        # Populate TempColumns.LAST_FILL_PRICE, TempColumns.LAST_FILL_FILLED_QUANTITY
        # and TempColumns.LAST_FILL_RECEIVE_DATE_TIME
        # These are only used by certain clients, e.g., Arisaig, in the processor flow.
        cls._get_values_from_last_exec_trade_or_exec_trade_cancel(
            df=df,
        )
        # Assign timestamps.externalOrderReceived to CAMEs from other records
        condition = (
            (
                df[SourceColumns.ACTIVITY].str.fullmatch(
                    "Exec-Pending-Cancel", case=False, na=False
                )
            )
            & (df[SourceColumns.STATUS].str.fullmatch("Working", case=False, na=False))
            & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
        )
        cls._propagate_field_conditionally_from_group(
            source_frame=df,
            condition=condition,
            source_column=SourceColumns.RECEIVE_DATE_TIME,
            group_columns=[SourceColumns.ORDER_ID, SourceColumns.ROUTE_ID],
            target_column=TempColumns.EXTERNAL_ORDER_RECEIVED_CANCEL,
        )

        # Assign timestamps.externalOrderReceived to REMEs from other records
        condition = (
            (
                df[SourceColumns.ACTIVITY].str.fullmatch(
                    "Exec-Pending-Replace", case=False, na=False
                )
            )
            & (df[SourceColumns.STATUS].str.fullmatch("ReplPend", case=False, na=False))
            & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
        )
        cls._propagate_field_conditionally_from_group(
            source_frame=df,
            condition=condition,
            source_column=SourceColumns.RECEIVE_DATE_TIME,
            group_columns=[SourceColumns.ORDER_ID, SourceColumns.ROUTE_ID],
            target_column=TempColumns.EXTERNAL_ORDER_RECEIVED_REME,
        )

        # Assign timestamps.internalOrderSubmitted from NEWOs to other records
        condition = (
            (
                df[SourceColumns.ACTIVITY].str.fullmatch(
                    "New-Route", case=False, na=False
                )
            )
            & (df[SourceColumns.STATUS].str.fullmatch("Sent", case=False, na=False))
            & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
        )
        cls._propagate_field_conditionally_from_group(
            source_frame=df,
            condition=condition,
            source_column=SourceColumns.RECEIVE_DATE_TIME,
            group_columns=[SourceColumns.ORDER_ID, SourceColumns.ROUTE_ID],
            target_column=TempColumns.INTERNAL_ORDER_SUBMITTED_NEWO,
        )

        # Assign Quantity from NEWO to other records
        cls._propagate_field_conditionally_from_group(
            source_frame=df,
            condition=condition,
            source_column=SourceColumns.QUANTITY,
            group_columns=[SourceColumns.ORDER_ID, SourceColumns.ROUTE_ID],
            target_column=TempColumns.QUANTITY_NEWO,
        )

        # Assign timestamps.internalOrderSubmitted to CAMEs from other records
        condition = (
            (
                df[SourceColumns.ACTIVITY].str.fullmatch(
                    "Client-Cancel-Request", case=False, na=False
                )
            )
            & (df[SourceColumns.STATUS].str.fullmatch("Working", case=False, na=False))
            & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
        )
        cls._propagate_field_conditionally_from_group(
            source_frame=df,
            condition=condition,
            source_column=SourceColumns.RECEIVE_DATE_TIME,
            group_columns=[SourceColumns.ORDER_ID, SourceColumns.ROUTE_ID],
            target_column=TempColumns.INTERNAL_ORDER_SUBMITTED_CANCEL,
        )

        # Assign timestamps.internalOrderSubmitted to REMEs from other records
        condition = (
            (
                df[SourceColumns.ACTIVITY].str.fullmatch(
                    "Client-Replace-Request", case=False, na=False
                )
            )
            & (df[SourceColumns.STATUS].str.fullmatch("CXLREP", case=False, na=False))
            & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
        )
        cls._propagate_field_conditionally_from_group(
            source_frame=df,
            condition=condition,
            source_column=SourceColumns.RECEIVE_DATE_TIME,
            group_columns=[SourceColumns.ORDER_ID, SourceColumns.ROUTE_ID],
            target_column=TempColumns.INTERNAL_ORDER_SUBMITTED_REME,
        )

        # Create NEWOs
        df[TempColumns.TEMP_COL_1] = df[SourceColumns.ROUTE_ID].fillna(
            df[SourceColumns.ORDER_ID]
        )

        newo_conditions = [
            (
                df[SourceColumns.ACTIVITY].str.fullmatch(
                    "New-Route", case=False, na=False
                )
            )
            & (df[SourceColumns.STATUS].str.fullmatch("Sent", case=False, na=False))
            & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False)),
        ]
        df[TempColumns.ORDER_STATUS] = np.select(
            newo_conditions,
            [OrderStatus.NEWO.value] * len(newo_conditions),
            default=pd.NA,
        )
        newo_df = (
            df.loc[df[TempColumns.ORDER_STATUS].notnull()]
            .groupby([SourceColumns.ORDER_ID, TempColumns.TEMP_COL_1], as_index=False)
            .agg({column: "first" for column in df.columns})
        )

        # Create FILLs

        fill_conditions = [
            (
                (
                    df[SourceColumns.ACTIVITY].str.fullmatch(
                        "Exec-Trade", case=False, na=False
                    )
                )
                & (
                    ~df[SourceColumns.STATUS].str.fullmatch(
                        "Filled", case=False, na=False
                    )
                )
                & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
            ),
            (
                (
                    df[SourceColumns.ACTIVITY].str.fullmatch(
                        "Exec-Trade", case=False, na=False
                    )
                )
                & (
                    df[SourceColumns.STATUS].str.fullmatch(
                        "Filled", case=False, na=False
                    )
                )
                & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
            ),
        ]
        fill_values = [
            OrderStatus.PARF.value,
            OrderStatus.FILL.value,
        ]
        df[TempColumns.ORDER_STATUS] = np.select(
            fill_conditions,
            fill_values,
            default=pd.NA,
        )
        fill_df = df.loc[df[TempColumns.ORDER_STATUS].notnull()]
        fill_id_not_null_mask = fill_df.loc[:, SourceColumns.FILL_ID].notnull()
        fill_df = fill_df.loc[fill_id_not_null_mask]

        # Create CAMEs
        came_conditions = [
            (
                (
                    df[SourceColumns.ACTIVITY].str.fullmatch(
                        "Exec-Done", case=False, na=False
                    )
                )
                & (
                    df[SourceColumns.STATUS].str.fullmatch(
                        "Cancel", case=False, na=False
                    )
                )
                & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
            ),
        ]
        df[TempColumns.ORDER_STATUS] = np.select(
            came_conditions,
            [OrderStatus.CAME.value] * len(came_conditions),
            default=pd.NA,
        )
        came_df = df.loc[df[TempColumns.ORDER_STATUS].notnull()]

        # Create REMEs
        reme_conditions = [
            (
                (
                    df[SourceColumns.ACTIVITY].str.fullmatch(
                        "Exec-Replace", case=False, na=False
                    )
                )
                & (
                    df[SourceColumns.STATUS].str.fullmatch(
                        "Replaced", case=False, na=False
                    )
                )
                & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
            ),
            (
                (
                    df[SourceColumns.ACTIVITY].str.fullmatch(
                        "Exec-Unmatched-Replace", case=False, na=False
                    )
                )
                & (
                    df[SourceColumns.STATUS].str.fullmatch(
                        "Replaced", case=False, na=False
                    )
                )
                & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
            ),
        ]
        df[TempColumns.ORDER_STATUS] = np.select(
            reme_conditions,
            [OrderStatus.REME.value] * len(reme_conditions),
            default=pd.NA,
        )
        reme_df = df.loc[df[TempColumns.ORDER_STATUS].notnull()]

        # Concatenating the aforementioned views into a consolidated view containing allocation and market records
        transformed_df = pd.concat([newo_df, fill_df, came_df, reme_df])

        # Aggregate all the timestamps columns wrt to their order status
        transformed_df[TempColumns.EXTERNAL_ORDER_RECEIVED] = np.select(
            [
                transformed_df[TempColumns.ORDER_STATUS] == OrderStatus.NEWO.value,
                transformed_df[TempColumns.ORDER_STATUS] == OrderStatus.CAME.value,
                transformed_df[TempColumns.ORDER_STATUS] == OrderStatus.REME.value,
            ],
            [
                transformed_df[TempColumns.EXTERNAL_ORDER_RECEIVED_NEWO],
                transformed_df[TempColumns.EXTERNAL_ORDER_RECEIVED_CANCEL],
                transformed_df[TempColumns.EXTERNAL_ORDER_RECEIVED_REME],
            ],
            default=pd.NA,
        )

        transformed_df[TempColumns.INTERNAL_ORDER_SUBMITTED] = np.select(
            [
                transformed_df[TempColumns.ORDER_STATUS] == OrderStatus.NEWO.value,
                transformed_df[TempColumns.ORDER_STATUS] == OrderStatus.CAME.value,
                transformed_df[TempColumns.ORDER_STATUS] == OrderStatus.REME.value,
            ],
            [
                transformed_df[TempColumns.INTERNAL_ORDER_SUBMITTED_NEWO],
                transformed_df[TempColumns.INTERNAL_ORDER_SUBMITTED_CANCEL],
                transformed_df[TempColumns.INTERNAL_ORDER_SUBMITTED_REME],
            ],
            default=pd.NA,
        )

        # NOTE: NEWO Order Submitted timestamps are to be used in FILL/PARF records.
        transformed_df[TempColumns.ORDER_SUBMITTED] = np.select(
            [
                transformed_df[TempColumns.ORDER_STATUS] == OrderStatus.NEWO.value,
                (
                    (transformed_df[TempColumns.ORDER_STATUS] == OrderStatus.FILL.value)
                    | (
                        transformed_df[TempColumns.ORDER_STATUS]
                        == OrderStatus.PARF.value
                    )
                ),
                transformed_df[TempColumns.ORDER_STATUS] == OrderStatus.CAME.value,
            ],
            [
                transformed_df[TempColumns.ORDER_SUBMITTED_NEWO],
                transformed_df[TempColumns.ORDER_SUBMITTED_NEWO],
                transformed_df[TempColumns.ORDER_SUBMITTED_CANCEL],
            ],
            default=pd.NA,
        )
        transformed_df[TempColumns.ORDER_SUBMITTED] = transformed_df[
            TempColumns.ORDER_SUBMITTED
        ].fillna(transformed_df[SourceColumns.RECEIVE_DATE_TIME])

        # NOTE: NEWO Order Received timestamps are to be used in FILL/PARF records.
        transformed_df[TempColumns.ORDER_RECEIVED] = np.select(
            [
                transformed_df[TempColumns.ORDER_STATUS] == OrderStatus.NEWO.value,
                (
                    (transformed_df[TempColumns.ORDER_STATUS] == OrderStatus.FILL.value)
                    | (
                        transformed_df[TempColumns.ORDER_STATUS]
                        == OrderStatus.PARF.value
                    )
                ),
                transformed_df[TempColumns.ORDER_STATUS] == OrderStatus.CAME.value,
            ],
            [
                transformed_df[TempColumns.ORDER_RECEIVED_NEWO],
                transformed_df[TempColumns.ORDER_RECEIVED_NEWO],
                transformed_df[TempColumns.ORDER_RECEIVED_CANCEL],
            ],
            default=pd.NA,
        )
        transformed_df[TempColumns.ORDER_RECEIVED] = transformed_df[
            TempColumns.ORDER_RECEIVED
        ].fillna(transformed_df[SourceColumns.RECEIVE_DATE_TIME])

        transformed_df[TempColumns.TRADING_DATE_TIME] = np.select(
            [
                transformed_df[TempColumns.ORDER_STATUS] == OrderStatus.NEWO.value,
                transformed_df[TempColumns.ORDER_STATUS] == OrderStatus.CAME.value,
            ],
            [
                transformed_df[TempColumns.TRADING_DATE_TIME_NEWO],
                transformed_df[TempColumns.TRADING_DATE_TIME_CANCEL],
            ],
            default=pd.NA,
        )
        transformed_df[TempColumns.TRADING_DATE_TIME] = transformed_df[
            TempColumns.TRADING_DATE_TIME
        ].fillna(transformed_df[SourceColumns.RECEIVE_DATE_TIME])

        transformed_df[TempColumns.ORDER_STATUS_UPDATED] = np.select(
            [
                ~transformed_df[TempColumns.ORDER_STATUS].str.fullmatch(
                    f"{OrderStatus.FILL.value}|{OrderStatus.PARF.value}",
                    case=False,
                    na=False,
                ),
                transformed_df[TempColumns.ORDER_STATUS].str.fullmatch(
                    f"{OrderStatus.FILL.value}|{OrderStatus.PARF.value}",
                    case=False,
                    na=False,
                ),
            ],
            [
                transformed_df[SourceColumns.RECEIVE_DATE_TIME],
                transformed_df[TempColumns.ORDER_STATUS_UPDATED_PARF_FILL],
            ],
            default=pd.NA,
        )
        return transformed_df

    @classmethod
    def _propagate_field_conditionally_from_group(
        cls,
        source_frame: pd.DataFrame,
        condition: pd.Series,
        source_column: str,
        group_columns: List[str],
        target_column: str,
        bfill_after_ffill: bool = True,
    ):
        """
        Helper function which propagates a timestamp or any other value
        within the aggregating group.
        It applies 'condition' such that only rows which satisfy the condition
        are non-null, and then does a forward fill + backward fill for the null
        values.

        :param source_frame: Input DataFrame
        :param condition: Condition to be applied in the input dataframe to set the initial values
        :param source_column: Source column name
        :param group_columns: Group columns names
        :param target_column: Target column name
        :param bfill_after_ffill: bfill after ffill if True
        """
        source_frame[target_column] = np.where(
            condition,
            source_frame[source_column],
            pd.NA,
        )
        if bfill_after_ffill:
            source_frame[target_column] = source_frame.groupby(group_columns)[
                [target_column]
            ].apply(lambda x: x.ffill().bfill())
        else:
            source_frame[target_column] = source_frame.groupby(group_columns)[
                [target_column]
            ].apply(lambda x: x.ffill())

    @classmethod
    def _get_values_from_last_exec_trade_or_exec_trade_cancel(
        cls,
        df: pd.DataFrame,
    ):
        """
        This function sorts by Fill ID, groups by Order ID and Route ID,
        and gets the Quantity, Average Price and Receive Date Time from the last
        row in each group.
        Rows where the Fill ID is null are ignored during this process.

        :param df: Input DataFrame
        :returns None, mutates the df with 2 new columns, TempColumns.LAST_FILL_PRICE
        and TempColumns.LAST_FILL_FILLED_QUANTITY
        """

        class FunctionTempColumns:
            """
            Temp DF columns created and dropped in this function
            """

            FILL_NUMBER = "__fill_number__"
            MAX_FILL_NUMBER = "__max_fill_number__"
            TEMP_FILL_ID = "__temp_fill_id__"

            @classmethod
            def get_columns(cls):
                return [
                    val_
                    for key_, val_ in cls.__dict__.items()
                    if not key_.startswith("__") and isinstance(val_, str)
                ]

        exec_trade_cancel_condition = (
            (
                df[SourceColumns.ACTIVITY].str.fullmatch(
                    "Exec-Trade|Exec-Trade-Cancel", case=False, na=False
                )
            )
            & (df[SourceColumns.TYPE].str.fullmatch("R", case=False, na=False))
            & (df[SourceColumns.FILL_ID].notnull())
        )
        # Make sure that FunctionTempColumns.TEMP_FILL_ID contains non-null Fill IDs
        # only for cases where exec_trade_cancel_condition is satisfied
        df[FunctionTempColumns.TEMP_FILL_ID] = np.where(
            exec_trade_cancel_condition,
            df[SourceColumns.FILL_ID],
            pd.NA,
        )
        # Get the fill number -- this is the last part of the fill, and convert it to an int
        # E.g. 54 in F.EMS.55286.1712571198-277872650.54
        df[FunctionTempColumns.FILL_NUMBER] = (
            df[FunctionTempColumns.TEMP_FILL_ID].str.split(".").str.get(-1)
        )
        df[FunctionTempColumns.FILL_NUMBER] = (
            pd.to_numeric(df[FunctionTempColumns.FILL_NUMBER], errors="coerce")
            .fillna(pd.NA)
            .astype("Int64")
        )
        # Get the max fill number for a group and create a bool column to indicate when
        # the fill number = max fill number
        df[FunctionTempColumns.MAX_FILL_NUMBER] = df.groupby(
            [SourceColumns.ORDER_ID, SourceColumns.ROUTE_ID]
        )[FunctionTempColumns.FILL_NUMBER].transform("max")

        max_fill_number_condition = (
            df[FunctionTempColumns.FILL_NUMBER]
            == df[FunctionTempColumns.MAX_FILL_NUMBER]
        ).fillna(False)

        # Propagate values from the max fill number row to other rows for the columns below

        source_target_column_mapping = {
            SourceColumns.RECEIVE_DATE_TIME: TempColumns.LAST_FILL_RECEIVE_DATE_TIME,
            SourceColumns.QUANTITY: TempColumns.LAST_FILL_QUANTITY,
            SourceColumns.AVERAGE_PRICE: TempColumns.LAST_FILL_PRICE,
            SourceColumns.FILLED_QUANTITY: TempColumns.LAST_FILL_FILLED_QUANTITY,
            SourceColumns.FILL_ID: TempColumns.LAST_FILL_FILL_ID,
            SourceColumns.LIMIT_PRICE: TempColumns.LAST_FILL_LIMIT_PRICE,
        }

        for source_column, target_column in source_target_column_mapping.items():
            cls._propagate_field_conditionally_from_group(
                source_frame=df,
                condition=max_fill_number_condition,
                source_column=source_column,
                group_columns=[SourceColumns.ORDER_ID, SourceColumns.ROUTE_ID],
                target_column=target_column,
            )

        df.drop(columns=FunctionTempColumns.get_columns(), inplace=True)

    @classmethod
    def remove_parf_fill_before_zero_cancel(
        cls, dataframe: pd.DataFrame, params: Params
    ) -> pd.DataFrame:
        """
        Removes PARF/FILL that happen before a Cancel with zero quantity by

        create temp __order_id__: Route ID.fillna(Order ID)

        get dataframe where __order_status__ == CAME and Filled Quantity == 0
        sort by Receive Date Time
        Drop duplicates by __order_id__ and keep latest time
        create dict as {__order_id__: latest time}

        remove any PARF/FILL where __order_id__ with lower than respective time
        """

        dataframe.loc[:, TempColumns.ORDER_ID] = dataframe.loc[
            :, SourceColumns.ROUTE_ID
        ].fillna(dataframe.loc[:, SourceColumns.ORDER_ID])

        cancel = dataframe.loc[:, TempColumns.ORDER_STATUS] == OrderStatus.CAME

        zero = dataframe.loc[:, SourceColumns.FILLED_QUANTITY] == 0

        if not (cancel & zero).any():
            return dataframe

        has_time = dataframe.loc[:, SourceColumns.RECEIVE_DATE_TIME].notnull()

        zero_cancels_time_dict = (
            dataframe.loc[
                cancel & zero & has_time,
                [TempColumns.ORDER_ID, SourceColumns.RECEIVE_DATE_TIME],
            ]
            .sort_values(by=SourceColumns.RECEIVE_DATE_TIME, ascending=False)
            .drop_duplicates(subset=TempColumns.ORDER_ID, keep="last")
            .set_index(keys=TempColumns.ORDER_ID)[SourceColumns.RECEIVE_DATE_TIME]
            .to_dict()
        )

        parf_or_fill = dataframe.loc[:, TempColumns.ORDER_STATUS].isin(
            [OrderStatus.PARF, OrderStatus.FILL]
        )

        lowest_date = "01/01/0001 00:00:00.000000"

        before_latest_time = pd.Series(
            data=[False] * dataframe.shape[0], index=dataframe.index
        )

        before_latest_time.loc[has_time] = dataframe.loc[has_time, :].apply(
            lambda row: datetime.strptime(
                zero_cancels_time_dict.get(row[TempColumns.ORDER_ID], lowest_date),
                params.datetime_format,
            )
            > datetime.strptime(
                row[SourceColumns.RECEIVE_DATE_TIME], params.datetime_format
            ),
            axis=1,
        )

        return dataframe.loc[~(parf_or_fill & before_latest_time)].drop(
            labels=TempColumns.ORDER_ID, axis=1
        )

    @staticmethod
    def _get_source_dir() -> Path:
        """
        Returns the path of the sources dir
        """
        return Path(Settings.context.sources_dir)

    @staticmethod
    def audit_invalid_instrument_records(
        data: pd.DataFrame, auditor: Auditor, logger: logging.Logger
    ):
        """
        This method checks for invalid records in the input DataFrame based on the presence of
        null values in the order number and parse key columns. It identifies two cases of invalid
        records:
        1. Records with both null parse key and null order number.
        2. Records with null parse key but a non-null order number.
        """
        null_record_order_number = data[SourceColumns.ORDER_NUMBER].isna()
        null_record_parse_key = data[SourceColumns.PARSE_KEY].isna()
        # Case 1: No parseKey and no order_number
        case1 = null_record_order_number & null_record_parse_key
        if case1.any():
            audit_message = f"{case1.sum()} instrument record(s) with no parseKey and no order number were skipped."
            auditor.add(audit_message)
            logger.warning(audit_message)

        # Case 2: No parseKey
        case2 = null_record_parse_key & ~null_record_order_number
        if case2.any():
            audit_message = (
                f"{case2.sum()} instrument record(s) with no parseKey were skipped."
            )
            auditor.add(audit_message)
            logger.warning(audit_message)
