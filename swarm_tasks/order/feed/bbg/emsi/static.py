class SourceColumns:
    ACCOUNT = "Account (Tag 1)"
    ACTIVITY = "Activity"
    AS_OF_DATE_TIME = "As Of Date Time (Tag 60)"
    AVERAGE_PRICE = "Average Price (Tag 6)"
    BROKER = "Broker"
    BUYSIDE_LEI = "Buyside LEI"
    CONTRACT_EXPIRATION = "Contract Expiration"
    EXECUTION_PRICE = "Execution Price (Tag 31)"
    FIGI = "FIGI"
    FILL_ID = "Fill ID"
    FILL_QUANTITY = "Fill Quantity (Tag 32)"
    FILLED_QUANTITY = "Filled Quantity (Tag 14)"
    FULL_EXCH_SYMBOL = "Full Exch Symbol"
    GTD_DATE = "GTD Date (Tag 126)"
    INSTRUCTION = "Instruction (Tag 58)"
    ISIN_INSTRUMENT = "ISIN"
    LAST_CAPACITY = "Last Capacity (Tag 29)"
    LAST_MARKET = "Last Market (Tag 30)"
    LAST_MARKET_INSTRUMENT = "Last Market"
    LIMIT_PRICE = "Limit Price (Tag 44)"
    LIQUIDITY = "Liquidity (Tag 851)"
    LOCAL_EXCH_SYMBOL = "Local Exch Symbol"
    OCC_SYMBOL = "OCC_Symbol"
    ORDER_HASH = "Order #"
    ORDER_ID = "Order ID"
    ORDER_NUMBER = "Order Number"
    ORDER_TYPE = "Order Type (Tag 40)"
    ORIGINATOR_LEI = "Originator LEI"
    PARSE_KEY = "Parsekey"
    PM_NAME = "PM Name"
    PRICE_CURRENCY = "Price Currency"
    PRODUCT = "Product"
    QUANTITY = "Quantity (Tag 53)"
    RECEIVE_DATE_TIME = "Receive Date Time"
    ROUTE_ID = "Route ID"
    SECURITY_NAME = "Security Name"
    SIDE = "Side (Tag 54)"
    STATUS = "Status (Tag 39)"
    STOP_PRICE = "Stop Price (Tag 99)"
    STRATEGY_NAME = "Strategy Name"
    TICKER = "Ticker (Tag 55)"
    TIF = "TIF (Tag59)"
    TRADER_NAME = "Trader Name"
    TRAN_ACCOUNT = "Tran Account"
    TRANSACTION_REPORTING_MIC = "Transaction Reporting MIC"
    TYPE = "Type"

    @classmethod
    def get_required_instrument_columns(cls):
        return [
            SourceColumns.CONTRACT_EXPIRATION,
            SourceColumns.FULL_EXCH_SYMBOL,
            SourceColumns.ISIN_INSTRUMENT,
            SourceColumns.LOCAL_EXCH_SYMBOL,
            SourceColumns.LAST_MARKET_INSTRUMENT,
            SourceColumns.OCC_SYMBOL,
            SourceColumns.PARSE_KEY,
            SourceColumns.SECURITY_NAME,
            SourceColumns.PM_NAME,
            SourceColumns.TRAN_ACCOUNT,
            SourceColumns.ORDER_NUMBER,
        ]


class TempColumns:
    ACCOUNT_FIRM_LEI = "__account_firm_lei__"
    ASSET_CLASS = "__asset_class__"
    BOND_MATURITY_DATE = "__bond_maturity_date__"
    BUYER = "__buyer__"
    BUY_SELL = "__buy_sell__"
    BUY_SELL_INDICATOR = "__buy_sell_indicator__"
    CLIENT = "__client__"
    COUNTERPARTY = "__counterparty__"
    CURRENCY_CODE = "__currency_code__"
    DATE = "__date__"
    DATE_TIME = "__date_time__"
    DELIVERY_TYPE = "__delivery_type__"
    EXECUTING_ENTITY = "__executing_entity__"
    EXECUTION_DETAILS_VALIDITY_PERIOD = "__execution_details_validity_period__"
    EXECUTION_WITHIN_FIRM = "__execution_within_firm__"
    EXPIRY_DATE = "__expiry_date__"
    EXTERNAL_ORDER_RECEIVED = "__external_order_received__"
    EXTERNAL_ORDER_RECEIVED_CANCEL = "__external_order_received_cancel__"
    EXTERNAL_ORDER_RECEIVED_NEWO = "__external_order_received_newo__"
    EXTERNAL_ORDER_RECEIVED_REME = "__external_order_received_reme__"
    ID = "__id__"
    INSTR_QUANTITY_NOTATION = "__instr_quantity_notation__"
    INSTRUCTION_PARSED = "__instruction_parsed__"
    INSTR_UNIQUE_IDENTIFIER = "__instr_unique_identifier__"
    INTERNAL_ORDER_ID = "__internal_order_id__"
    INTERNAL_ORDER_SUBMITTED = "__internal_order_submitted__"
    INTERNAL_ORDER_SUBMITTED_CANCEL = "__internal_order_submitted_cancel__"
    INTERNAL_ORDER_SUBMITTED_NEWO = "__internal_order_submitted_newo__"
    INTERNAL_ORDER_SUBMITTED_REME = "__internal_order_submitted_reme__"
    INVESTMENT_DECISION_MAKER = "__investment_decision_maker__"
    IS_CREATED_THROUGH_FALLBACK = "__is_created_through_fallback__"
    ISIN = "__isin__"
    INSTRUMENT_FULL_NAME = "__instrument_full_name__"
    INSTRUMENT_SHORT_NAME = "__instrument_short_name__"
    LAST_FILL_FILLED_QUANTITY = "__last_fill_filled_quantity__"
    LAST_FILL_FILL_ID = "__last_fill_fill_id__"
    LAST_FILL_LIMIT_PRICE = "__last_fill_limit_price__"
    LAST_FILL_PRICE = "__last_fill_price__"
    LAST_FILL_QUANTITY = "__last_fill_quantity__"
    LAST_FILL_RECEIVE_DATE_TIME = "__last_fill_receive_date_time__"
    LIMIT_PRICE = "__limit_price__"
    LINK_INSTRUMENT_VENUE = "__link_instrument_venue_attribute__"
    TRANSFORMED_ISIN = "__transformed_isin__"
    NEWO_IN_FILE = "__newo_in_file__"
    MONTH = "__month__"
    NOTIONAL_CURRENCY_1 = "__notional_currency_1__"
    NOTIONAL_CURRENCY_2 = "__notional_currency_2__"
    OPTION_STRIKE_PRICE = "__option_strike_price__"
    OPTION_TYPE = "__option_type__"
    OPTION_TYPE_MAPPED = "__option_type_mapped__"
    ORDER_ID = "__order_id__"
    ORDER_RECEIVED = "__order_received__"
    ORDER_RECEIVED_CANCEL = "__order_received_cancel__"
    ORDER_RECEIVED_NEWO = "__order_received_newo__"
    ORDER_SUBMITTED = "__order_submitted__"
    ORDER_SUBMITTED_CANCEL = "__order_submitted_cancel__"
    ORDER_SUBMITTED_NEWO = "__order_submitted_newo__"
    ORDER_STATUS = "__order_status__"
    ORDER_STATUS_UPDATED = "__order_status_updated__"
    ORDER_STATUS_UPDATED_CANCEL = "__order_status_updated_cancel__"
    ORDER_STATUS_UPDATED_PARF_FILL = "__order_status_updated_parf_fill__"
    PARENT_SYS_DATE_TIME = "__parent_sys_date_time__"
    PRICE = "__price__"
    QUANTITY_NEWO = "__quantity_newo__"
    RECORD_TYPE = "__record_type__"
    ROUTE = "__route__"
    ROUTE_ID_WITH_SOURCE_INDEX = "__route_id_with_source_index__"
    SECURITY_NAME_CONVERTED = "__security_name_converted__"
    SELLER = "__seller__"
    SETTLE_DT = "__settle_dt__"
    TEMP_COL_1 = "__temp_col_1__"
    TEMP_COL_2 = "__temp_col_2__"
    TEMP_COL_3 = "__temp_col_3__"
    TEMP_COL_4 = "__temp_col_4__"
    TEMP_COL_5 = "__temp_col_5__"
    TRADED_QUANTITY = "__traded_quantity__"
    TRADER = "__trader__"
    TRADING_CAPACITY = "__trading_capacity__"
    TRADING_DATE_TIME = "__trading_date_time__"
    TRADING_DATE_TIME_CONVERTED = "__trading_date_time_converted__"
    TRADING_DATE_TIME_CANCEL = "__trading_date_time_newo_cancel__"
    TRADING_DATE_TIME_NEWO = "__trading_date_time_newo__"
    TRANSACTION_REFERENCE_NUMBER = "__transaction_ref_no__"
    UNDERLYING_ISIN = "__underlying_isin__"
    UNDERLYING_SYMBOL = "__underlying_symbol__"
    VENUE = "__venue__"
    YEAR = "__year__"


class AddlInfoColumns:
    ORDER_INSTR = "Order Instructions"


TIME_ZONE = "Europe/London"
DATE_TIME_FORMAT_PARTNERS = "%m/%d/%Y %H:%M:%S"
DATE_TIME_FORMAT_CAXTON = "%m/%d/%Y %H:%M:%S.%f"
EXPIRY_DATE_FORMAT_CAXTON = "%Y/%m/%d"
EXPIRY_DATE_FORMAT_PARTNERS = "%m/%d/%y"
OPTION_EXPIRY_DATE_FORMAT = "%y%m%d"
