import logging
from typing import Dict

import pandas as pd
from prefect import context
from prefect.engine.signals import SKIP
from se_core_tasks.feeds.order.bbg.emsi.instrument_store import (
    populate_instrument_store,
)
from se_core_tasks.feeds.order.bbg.emsi.instrument_store import SkipIfInstrumentDfEmpty
from se_core_tasks.feeds.order.bbg.emsi.instrument_store import (
    SkipIfRequiredColumnsNotPopulated,
)
from swarm.task.auditor import Auditor
from swarm.task.transform.base import BaseTask

logger_ = context.get("logger")


class PopulateInstrumentStore(BaseTask):
    """
    Transforms EMSI Instrument File and stores it in S3 as a central instrument store
    """

    def execute(
        self,
        result: Dict[str, pd.DataFrame] = None,
        **kwargs,
    ):

        return self.process(
            result=result,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        result: Dict[str, pd.DataFrame] = None,
        auditor: Auditor = None,
        logger: logging.Logger = logger_,
    ):
        try:
            instrument_data = result.get("instrument")
            populate_instrument_store(
                instrument_data=instrument_data, auditor=auditor, logger=logger
            )
        except (SkipIfInstrumentDfEmpty, SkipIfRequiredColumnsNotPopulated):
            raise SKIP("Flow skipped")
