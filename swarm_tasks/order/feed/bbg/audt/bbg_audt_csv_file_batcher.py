import logging
from typing import Any
from typing import List

from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_trades_tasks.order.feed.bbg.audt.bbg_audt_csv_file_batcher import (
    Params as GenericParams,
)
from se_trades_tasks.order.feed.bbg.audt.bbg_audt_csv_file_batcher import (
    run_bbg_audt_csv_file_batcher,
)
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams, GenericParams):
    pass


class BBGAudtCSVFileBatcher(BaseTask):

    params_class = Params

    def execute(
        self,
        extractor_result: ExtractPathResult = None,
        params: Params = None,
        **kwargs,
    ) -> List[FileSplitterResult]:

        return self.process(
            extractor_result=extractor_result,
            params=params,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        extractor_result: ExtractPathResult,
        params: Params,
        auditor: Any = None,
        logger: logging.Logger = None,
    ):

        return run_bbg_audt_csv_file_batcher(
            extractor_result=extractor_result,
            params=params,
            csv_file_splitter_realm=Settings.realm,
            csv_file_splitter_sources_dir=Settings.context.sources_dir,
            auditor=auditor,
            logger=logger,
        )
