from typing import Union

from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_trades_tasks.order.feed.bbg.audt.data_source_name_mapper import (
    run_data_source_name_mapper,
)
from swarm.task.base import BaseTask


class DataSourceNameMapper(BaseTask):
    def execute(
        self,
        extractor_result: Union[ExtractPathResult, str] = None,
        **kwargs,
    ):
        return self.process(
            extractor_result=extractor_result,
        )

    @classmethod
    def process(cls, extractor_result):
        return run_data_source_name_mapper(
            csv_path=extractor_result
            if isinstance(extractor_result, str)
            else extractor_result.path.as_posix(),
        )
