import pandas as pd
from se_trades_tasks.order.feed.bbg.audt.inherit_parties import run_inherit_parties
from swarm.task.transform.base import TransformBaseTask


class InheritParties(TransformBaseTask):
    """
    Wrapper for the InheritParties BBG se-trades-tasks task
    """

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        tenant: str = None,
        es_client=None,
        **kwargs
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            tenant=tenant,
            es_client=es_client,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame,
        tenant: str,
        es_client,
    ):

        return run_inherit_parties(
            source_frame=source_frame, tenant=tenant, es_client=es_client
        )
