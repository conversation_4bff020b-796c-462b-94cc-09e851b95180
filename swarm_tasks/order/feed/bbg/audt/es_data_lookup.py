import pandas as pd
from se_trades_tasks.order.feed.bbg.audt.es_data_lookup import (
    run_bbg_audt_es_data_lookup,
)
from swarm.task.transform.base import TransformBaseTask


class BBGAudtESDataLookup(TransformBaseTask):
    """
    Wrapper for the BBGAudtESDataLookup se-trades-tasks task
    """

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        tenant: str = None,
        es_client=None,
        **kwargs
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame, tenant=tenant, es_client=es_client
        )

    @classmethod
    def process(cls, source_frame: pd.DataFrame, tenant: str, es_client):

        return run_bbg_audt_es_data_lookup(
            source_frame=source_frame, tenant=tenant, es_client=es_client
        )
