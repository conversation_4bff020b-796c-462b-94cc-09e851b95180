from pathlib import Path
from typing import List
from typing import Optional

import chardet
import numpy as np
import pandas as pd
from botocore.exceptions import Client<PERSON>rror
from prefect.engine.signals import FAIL
from prefect.engine.signals import SKIP
from pydantic import Field
from se_core_tasks.core.core_dataclasses import Extract<PERSON><PERSON><PERSON><PERSON>ult
from se_core_tasks.utils.data_manipulation import csv_has_header
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask
from swarm.task.io.read.result import FrameProducerResult

from swarm_tasks.cloud.aws.s3.utils import get_csv_header_from_s3
from swarm_tasks.cloud.aws.s3.utils import s3_download_file
from swarm_tasks.io.read.aws.df_from_s3_csv import DfFromS3Csv
from swarm_tasks.io.read.aws.df_from_s3_csv import Params as ParamsDfFromS3Csv
from swarm_tasks.order.feed.fidessa import static
from swarm_tasks.order.feed.fidessa.static import ArcEventsColumns
from swarm_tasks.order.feed.fidessa.static import DerivedColumns
from swarm_tasks.order.feed.fidessa.static import FidessaControllerColumns
from swarm_tasks.order.feed.fidessa.static import MarketOrderColumns
from swarm_tasks.order.feed.fidessa.static import TempColumns
from swarm_tasks.order.transformations.fidessa.order_progress.fidessa_order_progress_transformations import (
    MappingTableColumns,
)
from swarm_tasks.transform.map.map_from_mapping_table import MapFromMappingTable
from swarm_tasks.transform.map.map_from_mapping_table import (
    Params as ParamsMapFromMappingTable,
)


class Params(BaseParams):
    mic_code_mapping_s3_key: str = Field(
        "mapping_tables/order-feed-fidessa-processor/fidessa-mic-code-mapping.csv",
        description="S3 key for the mic code mapping to populate venue.",
    )
    market_order_header_s3_path: str = Field(
        "mapping_tables/order-feed-fidessa-processor/fidessa-market-order-header.csv",
        description="S3 key for the headers for market order file.",
    )
    default_encoding: str = Field(
        default="ISO-8859-1", description="Encoding to be passed into pandas.read_csv"
    )


class PreProcessFidessaData(BaseTask):
    """
    PreProcess task for Fidessa data.
    Confluence page link:
    https://steeleye.atlassian.net/wiki/spaces/IN/pages/1940750459/Order+Fidessa
    """

    params_class = Params

    def execute(
        self,
        producer_result: FrameProducerResult = None,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        **kwargs,
    ) -> ExtractPathResult:
        source_frame = producer_result.frame
        if source_frame.empty:
            raise FAIL("Empty source_frame")
        cols_used = [
            FidessaControllerColumns.ARC_EVENTS_FILE_PATH,
            FidessaControllerColumns.MARKET_ORDER_FILE_PATH,
        ]
        df = source_frame.loc[:, source_frame.columns.intersection(cols_used)]
        for col in cols_used:
            if col not in df.columns:
                raise FAIL(
                    "ARC_EVENTS_FILE_PATH or MARKET_ORDER_FILE_PATH not present in "
                    "source_frame"
                )
        arc_events_df = self._read_data_from_s3(
            s3_key=df.loc[0, FidessaControllerColumns.ARC_EVENTS_FILE_PATH],
            required_columns=ArcEventsColumns().get_columns(),
            types_dict=static.ARC_EVENTS_TYPE_DICT,
            params=params,
        )

        market_order_df = self._read_data_from_s3(
            s3_key=df.loc[0, FidessaControllerColumns.MARKET_ORDER_FILE_PATH],
            required_columns=MarketOrderColumns().get_columns(),
            types_dict=static.MARKET_ORDER_TYPE_DICT,
            is_market_order_file=True,
            params=params,
        )

        if arc_events_df.empty and market_order_df.empty:
            raise SKIP("Empty files")

        arc_events_df = self._skip_invalid_event_type_rows(arc_events_df=arc_events_df)
        self.logger.info("Deriving necessary fields")

        for col in [ArcEventsColumns.PRIMARY_ID, ArcEventsColumns.EXCHANGE_ORDER_ID]:
            if arc_events_df[col].dropna().empty:
                raise FAIL(f"No data for required column:{col} in source data frame")

        arc_events_df.loc[
            :, TempColumns.PRIMARY_ID_EO_REMOVED
        ] = self._get_primary_id_eo_removed(df=arc_events_df)

        arc_events_and_market_merged = arc_events_df.merge(
            market_order_df,
            how="left",
            left_on=TempColumns.PRIMARY_ID_EO_REMOVED,
            right_on=MarketOrderColumns.ORDER_ID,
        )

        # Create a derived column from exchange order id and related objects. This will be used for linking
        link_columns = [
            ArcEventsColumns.EXCHANGE_ORDER_ID,
            ArcEventsColumns.RELATED_OBJECTS,
        ]
        with_data_mask = (
            arc_events_and_market_merged[link_columns].notnull().any(axis=1)
        )
        arc_events_and_market_merged.loc[
            with_data_mask, DerivedColumns.DERIVED_EXCHANGE_ORDER_ID_RELATED_OBJECTS
        ] = arc_events_and_market_merged.loc[with_data_mask, link_columns].apply(
            lambda x: "|".join(x.dropna().astype("str").tolist()),
            axis=1,
        )

        # HIERARCHY
        arc_events_and_market_merged.loc[
            :, DerivedColumns.DERIVED_HIERARCHY
        ] = self._get_hierarchy(df=arc_events_and_market_merged)

        # order_id
        arc_events_and_market_merged.loc[
            :, DerivedColumns.DERIVED_ORDER_ID
        ] = self._get_order_id(df=arc_events_and_market_merged)

        # parent_order_id
        arc_events_and_market_merged.loc[
            :, DerivedColumns.DERIVED_PARENT_ORDER_ID
        ] = self._get_parent_order_id(df=arc_events_and_market_merged)

        # order_received_dt
        arc_events_and_market_merged.loc[
            :, DerivedColumns.DERIVED_ORDER_RECEIVED_DT
        ] = self._get_order_received_dt(df=arc_events_and_market_merged)

        # order_submitted_dt
        arc_events_and_market_merged.loc[
            :, DerivedColumns.DERIVED_ORDER_SUBMITTED_DT
        ] = self._get_order_submitted_dt(df=arc_events_and_market_merged)

        # aggregate order id
        arc_events_and_market_merged.loc[
            :, DerivedColumns.DERIVED_AGGREGATE_ORDER_ID
        ] = self._get_agg_order_id(df=arc_events_and_market_merged)

        # ISIN_Code
        arc_events_and_market_merged.loc[
            :, DerivedColumns.DERIVED_ISIN_CODE
        ] = self._get_isin(df=arc_events_and_market_merged)

        # BUY_SELL
        arc_events_and_market_merged.loc[
            :, DerivedColumns.DERIVED_BUY_SELL
        ] = self._get_buy_sell(df=arc_events_and_market_merged)

        # LimitPrice
        arc_events_and_market_merged.loc[
            :, DerivedColumns.DERIVED_LIMIT_PRICE
        ] = self._get_limit_price(df=arc_events_and_market_merged)

        # StopPrice
        arc_events_and_market_merged.loc[
            :, DerivedColumns.DERIVED_STOP_PRICE
        ] = self._get_stop_price(df=arc_events_and_market_merged)

        # initialQty
        arc_events_and_market_merged.loc[
            :, DerivedColumns.DERIVED_INITIAL_QTY
        ] = self._get_initial_qty(df=arc_events_and_market_merged)

        # Book View Code
        arc_events_and_market_merged.loc[
            :, DerivedColumns.DERIVED_BOOK_VIEW_CODE
        ] = self._get_book_view_code(df=arc_events_and_market_merged)

        # Entered By (Trader)
        arc_events_and_market_merged.loc[
            :, DerivedColumns.DERIVED_ENTERED_BY
        ] = self._get_entered_by(df=arc_events_and_market_merged)

        # Counterparty code (mapped to client)
        arc_events_and_market_merged.loc[
            :, DerivedColumns.DERIVED_COUNTERPARTY_CODE
        ] = self._get_counterparty_code(df=arc_events_and_market_merged)

        # Market ID (mapped to Counterparty)
        arc_events_and_market_merged.loc[
            :, DerivedColumns.DERIVED_MARKET_ID
        ] = self._get_market_id(df=arc_events_and_market_merged)

        # Investment decision maker
        arc_events_and_market_merged.loc[
            :, DerivedColumns.DERIVED_INVESTMENT_DECISION
        ] = self._get_investment_decision(df=arc_events_and_market_merged)

        # Execution within firm
        arc_events_and_market_merged.loc[
            :, DerivedColumns.DERIVED_EXECUTION_DECISION
        ] = self._get_execution_decision(df=arc_events_and_market_merged)

        # Venue
        arc_events_and_market_merged.loc[
            :, DerivedColumns.DERIVED_VENUE
        ] = self._get_venue(df=arc_events_and_market_merged, params=params)

        # write arc_events_and_market_merged to a csv and return the path
        source_dir = self._get_source_dir()
        csv_file_path = source_dir.joinpath("arc_events_and_market_merged.csv")
        arc_events_and_market_merged.to_csv(
            csv_file_path, index=False, encoding="utf-8", sep=","
        )
        return ExtractPathResult(path=csv_file_path)

    def _read_data_from_s3(
        self,
        s3_key: str,
        required_columns: List[str],
        types_dict: dict = {},
        names: Optional[List] = None,
        params: Optional[Params] = None,
        is_market_order_file: bool = False,
    ) -> pd.DataFrame:
        """
        :param s3_key: s3_key of the file on s3
        :param required_columns: List[str] required_columns list
        :param names: Optional[List]
        :return: pd.DataFrame
        """
        try:
            temp_file_path = s3_download_file(
                bucket=Settings.realm, key=s3_key, logger=self.logger
            )
            # 'Guess' the encoding using chardet
            with open(temp_file_path, "rb") as f:
                chardet_result = chardet.detect(f.read())
                encoding = chardet_result.get("encoding", params.default_encoding)
            if is_market_order_file:
                # checks if the csv file has header
                has_header = csv_has_header(
                    csv_file_path=temp_file_path, encoding=encoding
                )
                if not has_header:
                    # Get the CSV file header from S3
                    names = get_csv_header_from_s3(
                        s3_bucket=Settings.realm,
                        s3_key=params.market_order_header_s3_path,
                        logger=self.logger,
                    )
                    if not names:
                        raise FAIL(f"No header could be fetched from CSV file {s3_key}")
            result_df = pd.read_csv(
                temp_file_path,
                encoding=encoding,
                names=names,
                dtype="str",
            )

            if result_df.empty:
                return result_df

            # check for required columns
            for col in required_columns:
                if col not in result_df.columns:
                    result_df.loc[:, col] = pd.NA

            for col, col_type in types_dict.items():
                mask = result_df[col].notnull()
                result_df.loc[mask, col] = result_df.loc[mask, col].astype(col_type)
            return result_df
        except ClientError as e:
            raise FAIL(f"Error fetching data from S3. s3_key:{s3_key}." f"Error:{e}")

    def _skip_invalid_event_type_rows(
        self, arc_events_df: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Removes unwanted `Event Type` rows
        :param arc_events_df: pd.DataFrame
        :return: pd.DataFrame
        """
        self.logger.info("Removing invalid EVENT_TYPES from ARC_EVENTS file.")
        # group by primary id and remove groups where there is only entry_requested and
        # entry_rejected rows
        ereq_or_erej_mask = arc_events_df[ArcEventsColumns.EVENT_TYPE].str.fullmatch(
            f"{static.EventType.EXCHANGE_ORDER_ENTRY_REQUESTED}|"
            f"{static.EventType.EXCHANGE_ORDER_ENTRY_REJECTED}",
            case=False,
            na=False,
        )
        invalid_group_mask = (
            arc_events_df.where(ereq_or_erej_mask)
            .groupby(ArcEventsColumns.PRIMARY_ID)[ArcEventsColumns.EVENT_TYPE]
            .transform("nunique")
            == 2
        )
        arc_events_df = arc_events_df.loc[~invalid_group_mask]

        pattern = "|".join(static.INVALID_EVENT_TYPE_LIST)
        invalid_event_type_mask = arc_events_df[
            ArcEventsColumns.EVENT_TYPE
        ].str.contains(pattern, case=False, na=False)

        return arc_events_df.loc[~invalid_event_type_mask]

    def _get_hierarchy(self, df: pd.DataFrame) -> pd.Series:
        """
        1.Filter the dataframe to only consider,
        [Event Type] = "ExchangeOrderEntryAccepted".
        2.If the [Primary ID] is not unique (i.e. it
        occurs more than once) then hierarchy = 'Child'.Else: hierarchy = 'Standalone'
        3.If a EA is determine to be a “Child”/"Standalone" then this applies to all
        other records that are linked to that EA.
        """
        for col in [
            ArcEventsColumns.EXCHANGE_ORDER_ID,
            ArcEventsColumns.PRIMARY_ID,
            ArcEventsColumns.INTERNAL_ID,
        ]:
            df[col] = df[col].str.strip()
        entry_accepted_mask = df[ArcEventsColumns.EVENT_TYPE].str.match(
            static.EventType.EXCHANGE_ORDER_ENTRY_ACCEPTED, case=False, na=False
        )
        entry_requested_mask = df[ArcEventsColumns.EVENT_TYPE].str.match(
            static.EventType.EXCHANGE_ORDER_ENTRY_REQUESTED, case=False, na=False
        )
        value_counts = df.loc[
            entry_accepted_mask, ArcEventsColumns.PRIMARY_ID
        ].value_counts()
        primary_id_unique_mask = df.loc[
            entry_accepted_mask, ArcEventsColumns.PRIMARY_ID
        ].isin(value_counts.index[value_counts.eq(1)])

        df.loc[
            entry_accepted_mask & primary_id_unique_mask,
            DerivedColumns.DERIVED_HIERARCHY,
        ] = static.HIERARCHY.STANDALONE
        df.loc[
            entry_accepted_mask & ~primary_id_unique_mask,
            DerivedColumns.DERIVED_HIERARCHY,
        ] = static.HIERARCHY.CHILD

        # Link EA(Entry Accepted) to other Event Types to fetch Hierarchy
        self._link_ea_for_hierarchy(df=df, hierarchy=static.HIERARCHY.CHILD)
        self._link_ea_for_hierarchy(df=df, hierarchy=static.HIERARCHY.STANDALONE)

        # Link EREQ(Entry Requested) to EA(Entry Accepted) to fetch Hierarchy
        hierarchy_for_ea = (
            df[DerivedColumns.DERIVED_HIERARCHY]
            .where(entry_accepted_mask)
            .groupby(df[TempColumns.PRIMARY_ID_EO_REMOVED])
            .transform("first")
        )
        df.loc[entry_requested_mask, DerivedColumns.DERIVED_HIERARCHY] = df[
            DerivedColumns.DERIVED_HIERARCHY
        ].mask(entry_requested_mask, hierarchy_for_ea)

        # cancel accepted
        cancel_accepted_mask = (
            df[ArcEventsColumns.EVENT_TYPE].str.upper()
            == static.EventType.EXCHANGE_ORDER_CANCEL_ACCEPTED
        )
        df.loc[:, TempColumns.EXCHANGE_ORDER_CODE_FROM_ALT_REF] = df.loc[
            :, ArcEventsColumns.ALTERNATE_REFERENCES
        ].apply(self._get_exhange_order_from_alt_ref)

        primary_id_series = (
            df[DerivedColumns.DERIVED_HIERARCHY]
            .where(entry_accepted_mask)
            .groupby(df[TempColumns.EXCHANGE_ORDER_CODE_FROM_ALT_REF])
            .transform("first")
        )
        df.loc[cancel_accepted_mask, DerivedColumns.DERIVED_HIERARCHY] = df[
            DerivedColumns.DERIVED_HIERARCHY
        ].mask(cancel_accepted_mask, primary_id_series)

        return df.loc[:, DerivedColumns.DERIVED_HIERARCHY]

    @staticmethod
    def _link_ea_for_hierarchy(df: pd.DataFrame, hierarchy: str) -> pd.DataFrame:
        """
        Links record to EA using `EXCHANGE_ORDER_ID` and populates HIERARCHY.
        :param df: pd.DataFrame
        :param hierarchy: str
        :return: pd.DataFrame
        """
        hierarchy_mask = df[DerivedColumns.DERIVED_HIERARCHY] == hierarchy
        exchange_order_id_related_objects_list = df.loc[
            hierarchy_mask, DerivedColumns.DERIVED_EXCHANGE_ORDER_ID_RELATED_OBJECTS
        ].to_list()
        link_ea_hierarchy = df[
            DerivedColumns.DERIVED_EXCHANGE_ORDER_ID_RELATED_OBJECTS
        ].isin(exchange_order_id_related_objects_list) & (
            df[ArcEventsColumns.EVENT_TYPE]
            != static.EventType.EXCHANGE_ORDER_ENTRY_REQUESTED
        )
        df.loc[link_ea_hierarchy, DerivedColumns.DERIVED_HIERARCHY] = hierarchy
        return df

    def _get_order_id(self, df: pd.DataFrame) -> pd.Series:
        """
        Logic to populate "Order_ID".
        :param df: pd.DataFrame
        :return: pd.Series
        """
        result = pd.Series(pd.NA, index=df.index)
        # Necessary masks
        child_mask = df[DerivedColumns.DERIVED_HIERARCHY].eq(static.HIERARCHY.CHILD)
        fill_entered_mask = df[ArcEventsColumns.EVENT_TYPE].str.fullmatch(
            static.EventType.EXCHANGE_ORDER_FILL_ENTERED, case=False, na=False
        )
        cancel_accepted_mask = df[ArcEventsColumns.EVENT_TYPE].str.fullmatch(
            static.EventType.EXCHANGE_ORDER_CANCEL_ACCEPTED, case=False, na=False
        )
        entry_requested_mask = df[ArcEventsColumns.EVENT_TYPE].str.fullmatch(
            static.EventType.EXCHANGE_ORDER_ENTRY_REQUESTED, case=False, na=False
        )

        # child mask
        result.loc[child_mask] = (
            df.loc[child_mask, TempColumns.PRIMARY_ID_EO_REMOVED]
            + "|"
            + df.loc[child_mask, ArcEventsColumns.EXCHANGE_ORDER_ID]
        )

        # child fill
        child_fill_mask = child_mask & fill_entered_mask
        exchange_order_mask = (
            df[ArcEventsColumns.OBJECT_TYPE].str.fullmatch(
                static.ObjectType.EXCHANGE_ORDER, case=False, na=False
            )
        ) & (
            df[ArcEventsColumns.EVENT_TYPE].str.fullmatch(
                static.EventType.EXCHANGE_ORDER_ENTRY_ACCEPTED, case=False, na=False
            )
        )
        primary_id_series = (
            df[TempColumns.PRIMARY_ID_EO_REMOVED]
            .where(exchange_order_mask)
            .groupby(df[DerivedColumns.DERIVED_EXCHANGE_ORDER_ID_RELATED_OBJECTS])
            .transform("first")
        )
        df.loc[child_fill_mask, TempColumns.EA_LINKED_PRIMARY_ID_CHILD] = df[
            TempColumns.PRIMARY_ID_EO_REMOVED
        ].mask(child_fill_mask, primary_id_series)
        if child_fill_mask.any():
            result.loc[child_fill_mask] = (
                df.loc[child_fill_mask, TempColumns.EA_LINKED_PRIMARY_ID_CHILD]
                + "|"
                + df.loc[child_fill_mask, ArcEventsColumns.EXCHANGE_ORDER_ID]
            )

        # child cancel accepted
        child_ca_mask = child_mask & cancel_accepted_mask
        if child_ca_mask.any():
            result.loc[child_ca_mask] = (
                df.loc[child_ca_mask, TempColumns.PRIMARY_ID_EO_REMOVED]
                + "|"
                + df.loc[child_ca_mask, TempColumns.EXCHANGE_ORDER_CODE_FROM_ALT_REF]
            )

        # child entry requested
        child_ereq_mask = child_mask & entry_requested_mask
        if child_ereq_mask.any():
            df.loc[child_ereq_mask, TempColumns.EA_LINKED_EXCHANGE_ORDER_ID] = df.loc[
                child_ereq_mask
            ].apply(
                self._get_ea_linked_exchange_order,
                source_df=df.loc[child_mask],
                axis=1,
            )
            result.loc[child_ereq_mask] = (
                df.loc[child_ereq_mask, TempColumns.PRIMARY_ID_EO_REMOVED]
                + "|"
                + df.loc[child_ereq_mask, TempColumns.EA_LINKED_EXCHANGE_ORDER_ID]
            )

        # not child
        if (~child_mask).any():
            result.loc[~child_mask] = df.loc[
                ~child_mask, TempColumns.PRIMARY_ID_EO_REMOVED
            ]

        # not child fill entered
        not_child_fill_entered_mask = ~child_mask & fill_entered_mask
        if not_child_fill_entered_mask.any():
            result.loc[not_child_fill_entered_mask] = df[
                TempColumns.PRIMARY_ID_EO_REMOVED
            ].mask(not_child_fill_entered_mask, primary_id_series)
        return result

    @staticmethod
    def _get_ea_linked_exchange_order(
        x: pd.Series, source_df: pd.DataFrame
    ) -> Optional[str]:
        """Return the EXCHANGE ORDER LINKED TO THE RESPECTIVE ENTRY_ACCEPTED RECORD."""
        mask = (
            source_df[ArcEventsColumns.PRIMARY_ID] == x[ArcEventsColumns.PRIMARY_ID]
        ) & (
            source_df[ArcEventsColumns.EVENT_TYPE].str.upper()
            == static.EventType.EXCHANGE_ORDER_ENTRY_ACCEPTED
        )
        internal_seq = x[ArcEventsColumns.INTERNAL_SEQUENCE] + 1
        internal_seq_mask = (
            source_df[ArcEventsColumns.INTERNAL_SEQUENCE] == internal_seq
        )
        final_mask = mask & internal_seq_mask
        if not final_mask.any():
            return pd.NA
        source_df = source_df.loc[final_mask]
        if len(source_df.index) > 1:
            source_df = source_df[:1]
        result_series = source_df.squeeze()
        return result_series.get(ArcEventsColumns.EXCHANGE_ORDER_ID, pd.NA)

    @staticmethod
    def _get_order_received_dt(df: pd.DataFrame) -> pd.Series:
        """Logic for ORDER_RECEIVED_DT"""
        result = pd.Series(pd.NA, index=df.index)

        entry_requested_mask = df[ArcEventsColumns.EVENT_TYPE].str.fullmatch(
            static.EventType.EXCHANGE_ORDER_ENTRY_REQUESTED, case=False, na=False
        )
        if entry_requested_mask.any():
            result.loc[entry_requested_mask] = df.loc[
                entry_requested_mask, ArcEventsColumns.EVENT_TIMESTAMP
            ]

        if (~entry_requested_mask).any():
            event_timestamp_ereq_series = (
                df[ArcEventsColumns.EVENT_TIMESTAMP]
                .where(entry_requested_mask)
                .groupby(df[DerivedColumns.DERIVED_ORDER_ID])
                .transform("first")
            )
            result.loc[~entry_requested_mask] = df[
                ArcEventsColumns.EVENT_TIMESTAMP
            ].mask(~entry_requested_mask, event_timestamp_ereq_series)

        return result

    @staticmethod
    def _get_order_submitted_dt(df: pd.DataFrame) -> pd.Series:
        """Logic for ORDER_SUBMITTED_DT"""
        result = pd.Series(pd.NA, index=df.index)

        entry_accepted_mask = df[ArcEventsColumns.EVENT_TYPE].str.fullmatch(
            static.EventType.EXCHANGE_ORDER_ENTRY_ACCEPTED, case=False, na=False
        )
        if entry_accepted_mask.any():
            result.loc[entry_accepted_mask] = df.loc[
                entry_accepted_mask, ArcEventsColumns.EVENT_TIMESTAMP
            ]

        if (~entry_accepted_mask).any():
            event_timestamp_ea_series = (
                df[ArcEventsColumns.EVENT_TIMESTAMP]
                .where(entry_accepted_mask)
                .groupby(df[DerivedColumns.DERIVED_ORDER_ID])
                .transform("first")
            )
            result.loc[~entry_accepted_mask] = df[
                ArcEventsColumns.EVENT_TIMESTAMP
            ].mask(~entry_accepted_mask, event_timestamp_ea_series)
        return result

    @staticmethod
    def _get_agg_order_id(df: pd.DataFrame) -> pd.Series:
        """Logic for populating aggregate order id."""
        result = pd.Series(pd.NA, index=df.index)

        fill_entered_mask = df[ArcEventsColumns.EVENT_TYPE].str.fullmatch(
            static.EventType.EXCHANGE_ORDER_FILL_ENTERED, case=False, na=False
        )
        if fill_entered_mask.any():
            exchange_order_ea_mask = (
                df[ArcEventsColumns.OBJECT_TYPE].str.upper()
                == static.ObjectType.EXCHANGE_ORDER
            ) & (
                df[ArcEventsColumns.EVENT_TYPE].str.upper()
                == static.EventType.EXCHANGE_ORDER_ENTRY_ACCEPTED
            )
            market_linked_client_order_id_series = (
                df[MarketOrderColumns.CLIENT_ORDER_ID]
                .where(exchange_order_ea_mask)
                .groupby(df[DerivedColumns.DERIVED_EXCHANGE_ORDER_ID_RELATED_OBJECTS])
                .transform("first")
            )
            df.loc[fill_entered_mask, TempColumns.MARKET_LINKED_CLIENT_ORDER_ID] = df[
                ArcEventsColumns.PRIMARY_ID
            ].mask(fill_entered_mask, market_linked_client_order_id_series)
            result.loc[fill_entered_mask] = (
                df.loc[fill_entered_mask, TempColumns.MARKET_LINKED_CLIENT_ORDER_ID]
                + "|"
                + df.loc[fill_entered_mask, ArcEventsColumns.EXCHANGE_ORDER_ID]
            )
        if (~fill_entered_mask).any():
            result.loc[~fill_entered_mask] = df.loc[
                ~fill_entered_mask, MarketOrderColumns.CLIENT_ORDER_ID
            ]
        return result

    @staticmethod
    def _get_primary_id_eo_removed(df: pd.DataFrame) -> pd.Series:
        """Returns `Primary ID` by stripping '-EO' at the end if any."""
        result = pd.Series(pd.NA, index=df.index)
        mask = df[ArcEventsColumns.PRIMARY_ID].str.endswith(static.EO_STR)
        if mask.any():
            result.loc[mask] = df.loc[mask, ArcEventsColumns.PRIMARY_ID].str[:-3]
        if (~mask).any():
            result.loc[~mask] = df.loc[~mask, ArcEventsColumns.PRIMARY_ID]

        return result

    @staticmethod
    def _get_exhange_order_from_alt_ref(alternate_ref: str) -> Optional[str]:
        """Extract EXCHANGE_ORDER_CODE from alternate_ref"""
        result = pd.NA
        if pd.isna(alternate_ref):
            return result
        for ref in alternate_ref.split(static.ALTERNATE_REF_SEPARATOR):
            if static.EXCHANGE_ORDER_CODE_STR in ref.upper():
                result = ref.split(static.EXCHANGE_ORDER_CODE_SEPARATOR)
                return result[1] if len(result) >= 1 else pd.NA
        return result

    @staticmethod
    def _get_source_dir() -> Path:
        return Path(Settings.context.sources_dir)

    @staticmethod
    def _get_isin(df: pd.DataFrame) -> pd.Series:
        """
        :param df: pd.DataFrame - source df
        :return: pd.Series - derived ISIN as series
        For Fills link to the corresponding entry_accepted and derive the ISIN_CODE
        """
        # default
        result = df.loc[:, MarketOrderColumns.ISIN_Code]

        entry_accepted_mask = df[ArcEventsColumns.EVENT_TYPE].str.fullmatch(
            static.EventType.EXCHANGE_ORDER_ENTRY_ACCEPTED, case=False, na=False
        )
        fill_mask = df[ArcEventsColumns.EVENT_TYPE].str.fullmatch(
            static.EventType.EXCHANGE_ORDER_FILL_ENTERED, case=False, na=False
        )

        if fill_mask.any():
            isin_series = (
                df[MarketOrderColumns.ISIN_Code]
                .where(entry_accepted_mask)
                .groupby(df[DerivedColumns.DERIVED_EXCHANGE_ORDER_ID_RELATED_OBJECTS])
                .transform("first")
            )
            result.loc[fill_mask] = df[MarketOrderColumns.ISIN_Code].mask(
                fill_mask, isin_series
            )
        return result

    def _get_buy_sell(self, df: pd.DataFrame) -> pd.Series:
        """
        :param df: pd.DataFrame - source df
        :return: pd.Series - derived BUY_SELL as series
        For CancelAccepted rows link to corresponding entry_accepted row and derive the
        BUY_SELL value.
        """
        return self._get_value_from_first_ea_for_ca(
            df=df, from_column=ArcEventsColumns.BUY_SELL
        )

    @staticmethod
    def _get_parent_order_id(df: pd.DataFrame) -> pd.Series:
        """
        if hierarchy = 'Child' then
            This would be the same as taking the orderIdentifiers.orderIdCode of the
            record, splitting the string by “|” and taking the first value
            i.e orderIdentifiers.orderIdCode = 00011782528FQLO1|335628651
            then orderIdentifiers.parentOrderId = 00011782528FQLO1
        elif hierarchy = 'Standalone' then
            orderIdentifiers.parentOrderId will be empty
        :param df: pd.DataFrame: source dataframe
        :return: pd.Series: derived parent_order_id as series
        """
        result = pd.Series(pd.NA, index=df.index)

        child_mask = df.loc[:, DerivedColumns.DERIVED_HIERARCHY].eq(
            static.HIERARCHY.CHILD
        )
        if child_mask.any():
            result.loc[child_mask] = df.loc[
                child_mask, DerivedColumns.DERIVED_ORDER_ID
            ].apply(lambda x: x.split("|")[0] if not pd.isna(x) else pd.NA)
        return result

    @staticmethod
    def _get_limit_price(df: pd.DataFrame) -> pd.Series:
        """
        This method gets the limit price as follows
        For non-FE rows, get the limit price from the ArcEventsColumns.LIMIT_PRICE column
        For FE rows, get the limit price from the ArcEventsColumns.LIMIT_PRICE column of
        the previous EA or RA rows with the same order id
        :param df: pd.DataFrame - source df
        :return: pd.Series - derived LimitPrice as series
        """
        # default
        result = df.loc[:, ArcEventsColumns.LIMIT_PRICE]

        fill_entered_mask = df.loc[:, ArcEventsColumns.EVENT_TYPE].str.fullmatch(
            static.EventType.EXCHANGE_ORDER_FILL_ENTERED, case=False, na=False
        )

        if fill_entered_mask.any():
            # Create a temp df with required columns and manipulate it to get the required results
            temp_df = df.loc[
                :,
                [
                    ArcEventsColumns.LIMIT_PRICE,
                    ArcEventsColumns.EVENT_TYPE,
                    ArcEventsColumns.EVENT_TIMESTAMP,
                    DerivedColumns.DERIVED_ORDER_ID,
                ],
            ]
            # Convert to datetime, discard any tzinfo, trailing 's', spaces.
            temp_df.loc[:, ArcEventsColumns.EVENT_TIMESTAMP] = pd.to_datetime(
                temp_df.loc[:, ArcEventsColumns.EVENT_TIMESTAMP].map(lambda x: x[:24]),
                yearfirst=True,
            )
            # Sort so we have the rows in the required timestamp order (per order id)
            temp_df = temp_df.sort_values(by=["DERIVED_ORDER_ID", "Event Timestamp"])
            # Convert all non-EA and non-RA prices to NaNs
            limit_price_series = temp_df.loc[:, ArcEventsColumns.LIMIT_PRICE].where(
                temp_df[ArcEventsColumns.EVENT_TYPE].str.fullmatch(
                    f"{static.EventType.EXCHANGE_ORDER_ENTRY_ACCEPTED}|"
                    f"{static.EventType.EXCHANGE_ORDER_REPLACE_ACCEPTED}",
                    case=False,
                    na=False,
                )
            )
            # For FE rows, ffill prices from the previous EA/RA rows. For other rows, keep the same values
            temp_df[ArcEventsColumns.LIMIT_PRICE] = np.where(
                temp_df[ArcEventsColumns.EVENT_TYPE].str.fullmatch(
                    static.EventType.EXCHANGE_ORDER_FILL_ENTERED, case=False, na=False
                ),
                limit_price_series.groupby(
                    temp_df[DerivedColumns.DERIVED_ORDER_ID]
                ).ffill(),
                temp_df[ArcEventsColumns.LIMIT_PRICE],
            )
            # Sort index to undo the previous sort_values and get back the original ordering
            temp_df = temp_df.sort_index()
            result = temp_df.loc[:, ArcEventsColumns.LIMIT_PRICE]

        return result

    @staticmethod
    def _get_counterparty_code(df: pd.DataFrame) -> pd.Series:
        """
        For non-Fill entered rows, link to corresponding Fill entered row and get the
        value from COUNTERPARTY_CODE in that row. For Fill entered rows, get the value
        directly from the COUNTERPARTY_CODE in the same row. Linking is done based on
        DERIVED_ORDER_ID

        :param df: pd.DataFrame - source df
        :return: pd.Series - derived column as a pd.Series
        """
        # default
        result = df.loc[:, ArcEventsColumns.COUNTERPARTY_CODE]

        fill_entered_mask = df.loc[:, ArcEventsColumns.EVENT_TYPE].str.fullmatch(
            static.EventType.EXCHANGE_ORDER_FILL_ENTERED, case=False, na=False
        )

        if fill_entered_mask.any():
            fill_entered_series = (
                df.loc[:, ArcEventsColumns.COUNTERPARTY_CODE]
                .where(fill_entered_mask)
                .groupby(df.loc[:, DerivedColumns.DERIVED_ORDER_ID])
                .transform("first")
            )
            # For non-FE rows, get the value from fill_entered_series
            result.loc[~fill_entered_mask] = df.loc[
                :, ArcEventsColumns.COUNTERPARTY_CODE
            ].mask(~fill_entered_mask, fill_entered_series)
        return result

    def _get_stop_price(self, df: pd.DataFrame) -> pd.Series:
        """
        For Fill rows link to corresponding entry_accepted row and get the
        value from the STOP_PRICE col in that row. For non Fill entered rows,
        get the value directly from the STOP_PRICE column in the same row
        :param df: pd.DataFrame - source df
        :return: pd.Series - derived StopPrice as a pd.Series
        """
        return self._get_value_from_first_ea_for_fe(
            df=df, from_column=ArcEventsColumns.STOP_PRICE
        )

    def _get_initial_qty(self, df: pd.DataFrame) -> pd.Series:
        """
        For Fill rows link to corresponding entry_accepted row and get the
        value from the TRADING_QUANTITY col in that row. For non Fill entered rows,
        get the value directly from the TRADING_QUANTITY column in the same row
        :param df: pd.DataFrame - source df
        :return: pd.Series - derived initialQty as a pd.Series
        """
        return self._get_value_from_first_ea_for_fe(
            df=df, from_column=ArcEventsColumns.TRADING_QUANTITY
        )

    def _get_book_view_code(self, df: pd.DataFrame) -> pd.Series:
        """
        For Fill rows link to corresponding entry_accepted row and get the
        value from the BOOK_VIEW_CODE col in that row. For non Fill entered rows,
        get the value directly from the BOOK_VIEW_CODE column in the same row
        :param df: pd.DataFrame - source df
        :return: pd.Series - derived book view code as a pd.Series
        """
        return self._get_value_from_first_ea_for_fe(
            df=df, from_column=ArcEventsColumns.BOOK_VIEW_CODE
        )

    def _get_entered_by(self, df: pd.DataFrame) -> pd.Series:
        """
        For Fill rows link to corresponding entry_accepted row and get the
        value from the ENTERED_BY col in that row . For non Fill entered rows,
        get the value directly from the ENTERED_BY column in the same row.
        This is used to populate the trader downstream
        :param df: pd.DataFrame - source df
        :return: pd.Series - derived ENTERED_BY as a pd.Series
        """
        return self._get_value_from_first_ea_for_fe(
            df=df, from_column=ArcEventsColumns.ENTERED_BY
        )

    def _get_investment_decision(self, df: pd.DataFrame) -> pd.Series:
        """
        For Fill rows link to corresponding entry_accepted row and get the
        value from the INVESTMENT_DECISION col in that row . For non Fill entered rows,
        get the value directly from the INVESTMENT_DECISION column in the same row.
        This is used to populate the investment decision maker downstream
        :param df: pd.DataFrame - source df
        :return: pd.Series - derived investment decision maker as a pd.Series
        """
        return self._get_value_from_first_ea_for_fe(
            df=df, from_column=ArcEventsColumns.INVESTMENT_DECISION
        )

    def _get_execution_decision(self, df: pd.DataFrame) -> pd.Series:
        """
        For Fill rows link to corresponding entry_accepted row and get the
        value from the EXECUTION_DECISION col in that row . For non Fill entered rows,
        get the value directly from the EXECUTION_DECISION column in the same row.
        This is used to populate the execution within firm downstream.
        :param df: pd.DataFrame - source df
        :return: pd.Series - derived execution within firm as a pd.Series
        """
        return self._get_value_from_first_ea_for_fe(
            df=df, from_column=ArcEventsColumns.EXECUTION_DECISION
        )

    def _get_market_id(self, df: pd.DataFrame) -> pd.Series:
        """
        For Fill rows link to corresponding entry_accepted row and get the
        value from the MARKET_ID col in that row . For non Fill entered rows,
        get the value directly from the MARKET_ID column in the same row.
        This is used to populate the counterparty downstream
        :param df: pd.DataFrame - source df
        :return: pd.Series - derived market id as a pd.Series
        """
        return self._get_value_from_first_ea_for_ca(
            df=df, from_column=ArcEventsColumns.MARKET_ID
        )

    @staticmethod
    def _get_value_from_first_ea_for_fe(
        df: pd.DataFrame, from_column: str
    ) -> pd.Series:
        """
        For Fill entered rows, link to corresponding entry_accepted row and get the
        value from from_column in that row. For non Fill entered rows, get the value
        directly from the from_column in the same row. Linking is done based on
        DERIVED_ORDER_ID

        :param df: pd.DataFrame - source df
        :param from_column: str - Column from which values are fetched, either from the
                            EA or the same FE row
        :return: pd.Series - derived column as a pd.Series
        """
        # default
        result = df.loc[:, from_column]

        entry_accepted_mask = df.loc[:, ArcEventsColumns.EVENT_TYPE].str.fullmatch(
            static.EventType.EXCHANGE_ORDER_ENTRY_ACCEPTED, case=False, na=False
        )
        fill_entered_mask = df.loc[:, ArcEventsColumns.EVENT_TYPE].str.fullmatch(
            static.EventType.EXCHANGE_ORDER_FILL_ENTERED, case=False, na=False
        )

        if fill_entered_mask.any():
            ea_series = (
                df.loc[:, from_column]
                .where(entry_accepted_mask)
                .groupby(df.loc[:, DerivedColumns.DERIVED_ORDER_ID])
                .transform("first")
            )
            # For Fill Entered (FE) rows, get the value from the corresponding Entry Accepted row
            result.loc[fill_entered_mask] = df.loc[:, from_column].mask(
                fill_entered_mask, ea_series
            )
        return result

    @staticmethod
    def _get_value_from_first_ea_for_ca(
        df: pd.DataFrame, from_column: str
    ) -> pd.Series:
        """
        For Cancel Accepted rows, link to corresponding entry_accepted row and get the
        value from from_column in that row. For non Cancel accepted rows, get the value
        directly from the from_column in the same row. Linking is done based on DERIVED_ORDER_ID

        :param df: pd.DataFrame - source df
        :param from_column: str - Column from which values are fetched, either from the
                            EA or the same CA row
        :return: pd.Series - derived column as a pd.Series
        """
        # default
        result = df.loc[:, from_column]

        entry_accepted_mask = df.loc[:, ArcEventsColumns.EVENT_TYPE].str.fullmatch(
            static.EventType.EXCHANGE_ORDER_ENTRY_ACCEPTED, case=False, na=False
        )
        cancel_accepted_mask = df.loc[:, ArcEventsColumns.EVENT_TYPE].str.fullmatch(
            static.EventType.EXCHANGE_ORDER_CANCEL_ACCEPTED, case=False, na=False
        )

        if cancel_accepted_mask.any():
            ea_series = (
                df[from_column]
                .where(entry_accepted_mask)
                .groupby(df[DerivedColumns.DERIVED_ORDER_ID])
                .transform("first")
            )
            # For Cancel Accepted (CA) rows, get the value from the corresponding Entry Accepted row
            result.loc[cancel_accepted_mask] = df[from_column].mask(
                cancel_accepted_mask, ea_series
            )
        return result

    @staticmethod
    def _get_venue(df: pd.DataFrame, params: Params) -> pd.Series:
        """
        For non-Fill entered rows, link to corresponding Fill entered row and get the
        value from EXECUTION_VENUE in that row. For Fill entered rows, get the value
        directly from the EXECUTION_VENUE in the same row. Linking is done based on
        DERIVED_ORDER_ID

        :param df: pd.DataFrame - source df
        :param params: Params - parameters for the task
        :return: pd.Series - derived column as a pd.Series
        """
        # default
        result = df.loc[:, ArcEventsColumns.EXECUTION_VENUE]

        fill_entered_mask = df.loc[:, ArcEventsColumns.EVENT_TYPE].str.fullmatch(
            static.EventType.EXCHANGE_ORDER_FILL_ENTERED, case=False, na=False
        )

        if fill_entered_mask.any():
            fill_entered_series = (
                df.loc[:, ArcEventsColumns.EXECUTION_VENUE]
                .where(fill_entered_mask)
                .groupby(df.loc[:, DerivedColumns.DERIVED_ORDER_ID])
                .transform("first")
            )
            # For non-FE rows, get the value from fill_entered_series
            result.loc[~fill_entered_mask] = df.loc[
                :, ArcEventsColumns.EXECUTION_VENUE
            ].mask(~fill_entered_mask, fill_entered_series)

        # Fallback in case venue could not be computed based on previous logic
        if result.isnull().any():
            mic_mapping = DfFromS3Csv.process(
                params=ParamsDfFromS3Csv(s3_key=params.mic_code_mapping_s3_key)
            )
            mic_attributed_venue = (
                MapFromMappingTable.process(
                    source_frame=df,
                    mapping_table=mic_mapping,
                    params=ParamsMapFromMappingTable(
                        source_attribute=ArcEventsColumns.DESTINATION_EXCHANGE_ID,
                        target_attribute=TempColumns.MIC_MAPPED_VENUE,
                        matching_column=MappingTableColumns.CODE,
                        output_column=MappingTableColumns.MIC,
                    ),
                )
                .fillna("XOFF")
                .loc[:, TempColumns.MIC_MAPPED_VENUE]
            )
            result = result.fillna(mic_attributed_venue)

        return result
