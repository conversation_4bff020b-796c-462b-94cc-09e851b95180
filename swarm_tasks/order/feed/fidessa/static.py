from swarm_tasks.utilities.task_utils import BaseColumns


class FidessaControllerColumns:
    ARC_EVENTS_FILE_PATH = "ARC_EVENTS_FILE_PATH"
    MARKET_ORDER_FILE_PATH = "MARKET_ORDER_FILE_PATH"
    REALM = "REALM"


class ArcEventsColumns(BaseColumns):
    ALTERNATE_REFERENCES = "Alternate References"
    BOOK_VIEW_CODE = "BOOK_VIEW_CODE"
    BUY_SELL = "BUY_SELL"
    CFI_CODE = "CFI_CODE"
    COUNTERPARTY_CODE = "COUNTERPARTY_CODE"
    DEALING_CAPACITY = "DEALING_CAPACITY"
    DEALT_CURRENCY_ID = "DEALT_CURRENCY_ID"
    DESTINATION_EXCHANGE_ID = "DESTINATION_EXCHANGE_ID"
    ENTERED_BY = "ENTERED_BY"
    EVENT_TIMESTAMP = "Event Timestamp"
    EVENT_TYPE = "Event Type"
    EXCHANGE_ORDER_ID = "EXCHANGE_ORDER_ID"
    EXCHANGE_TRADE_CODE = "EXCHANGE_TRADE_CODE"
    EXECUTION_DECISION = "EXECUTION_DECISION"
    EXECUTION_DECISION_SHORT_CODE = "EXECUTION_DECISION_SHORT_CODE"
    EXECUTION_VENUE = "EXECUTION_VENUE"
    EXPIRY_TYPE = "EXPIRY_TYPE"
    GROSS_PRICE = "GROSS_PRICE"
    INSTRUMENT_DESCRIPTION = "INSTRUMENT_DESCRIPTION"
    INSTRUMENT_EXPIRY_DATE = "INSTRUMENT_EXPIRY_DATE"
    INSTRUMENT_FIM_CODE = "INSTRUMENT_FIM_CODE"
    INSTRUMENT_TRADED_CCY = "INSTRUMENT_TRADED_CCY"
    INTERNAL_ID = "Internal ID"
    INTERNAL_SEQUENCE = "Internal sequence"
    INVESTMENT_DECISION = "INVESTMENT_DECISION"
    INVESTMENT_DECISION_SHORT_CODE = "INVESTMENT_DECISION_SHORT_CODE"
    LIMIT_PRICE = "LIMIT_PRICE"
    MARKET_ID = "MARKET_ID"
    OBJECT_TYPE = "Object Type"
    OPTION_TYPE = "OPTION_TYPE"
    ORDER_PRICE_TYPE = "ORDER_PRICE_TYPE"
    PRIMARY_ID = "Primary ID"
    QUANTITY = "QUANTITY"
    REASON_TEXT = "REASON_TEXT"
    REGULATORY_ALGO_ID = "REGULATORY_ALGO_ID"
    RELATED_OBJECTS = "Related Objects"
    STOP_PRICE = "STOP_PRICE"
    STRIKE_PRICE = "STRIKE_PRICE"
    TRADING_ENTITY_ID = "TRADING_ENTITY_ID"
    TRADING_QUANTITY = "TRADING_QUANTITY"
    TRADING_VENUE_TRANSACTION_ID = "TRADING_VENUE_TRANSACTION_ID"


INVALID_EVENT_TYPE_LIST = [
    "ExchangeOrderReplaceRequested",
    "ExchangeOrderReplaceRejected",
    "ExchangeOrderCancelRequested",
    "ExchangeOrderCancelRejected",
    "OrderCompleted",
    "ExchangeOrderUpdated",
    "ExchangeOrphanFillEntered",
    "ExchangeOrphanFillAmended",
    "ExchangeOrphanFillCancelled",
    "ExchangeOrphanFillEntered",
    "ExchangeOrphanFillAmended",
    "ExchangeOrphanFillCancelled",
    "ExchangeOrderFillAmended",
]


class MarketOrderColumns(BaseColumns):
    CLIENT_ORDER_ID = "Client_Order_Id"
    EPIC_Code = "EPIC_Code"
    Exercise_Type = "Exercise_Type"
    GROSS_FILL_PRICE = "Gross_Fill_Price"
    INSTRUMENT_SEDOL_CODE = "INSTRUMENT_SEDOL_CODE"
    ISIN_Code = "ISIN_Code"
    ORDER_ID = "Order_Id"
    ORDER_PRICE_TYPE_QUALIFIER = "Order_Price_Type_Qualifier"


class MicCodeMappingColumns(BaseColumns):
    CODE = "Code"
    MIC = "MIC"


class DerivedColumns:
    DERIVED_AGGREGATE_ORDER_ID = "DERIVED_AGGREGATE_ORDER_ID"
    DERIVED_BOOK_VIEW_CODE = "DERIVED_BOOK_VIEW_CODE"
    DERIVED_BUY_SELL = "DERIVED_BUY_SELL"
    DERIVED_COUNTERPARTY_CODE = "DERIVED_COUNTERPARTY_CODE"
    DERIVED_ENTERED_BY = "DERIVED_ENTERED_BY"
    DERIVED_EXCHANGE_ORDER_ID_RELATED_OBJECTS = (
        "DERIVED_EXCHANGE_ORDER_ID_RELATED_OBJECTS"
    )
    DERIVED_EXECUTION_DECISION = "DERIVED_EXECUTION_DECISION"
    DERIVED_HIERARCHY = "DERIVED_HIERARCHY"
    DERIVED_INITIAL_QTY = "DERIVED_INITIAL_QTY"
    DERIVED_INVESTMENT_DECISION = "DERIVED_INVESTMENT_DECISION"
    DERIVED_ISIN_CODE = "DERIVED_ISIN_CODE"
    DERIVED_MARKET_ID = "DERIVED_MARKET_ID"
    DERIVED_LIMIT_PRICE = "DERIVED_LIMIT_PRICE"
    DERIVED_ORDER_ID = "DERIVED_ORDER_ID"
    DERIVED_ORDER_RECEIVED_DT = "DERIVED_ORDER_RECEIVED_DT"
    DERIVED_ORDER_SUBMITTED_DT = "DERIVED_ORDER_SUBMITTED_DT"
    DERIVED_PARENT_ORDER_ID = "DERIVED_PARENT_ORDER_ID"
    DERIVED_STOP_PRICE = "DERIVED_STOP_PRICE"
    DERIVED_VENUE = "DERIVED_VENUE"


class TempColumns(BaseColumns):
    EA_LINKED_EXCHANGE_ORDER_ID = "EA_LINKED_EXCHANGE_ORDER_ID"
    EA_LINKED_PRIMARY_ID_CHILD = "EA_LINKED_PRIMARY_ID_CHILD"
    EXCHANGE_ORDER_CODE_FROM_ALT_REF = "EXCHANGE_ORDER_CODE_FROM_ALT_REF"
    MARKET_LINKED_CLIENT_ORDER_ID = "MARKET_LINKED_CLIENT_ORDER_ID"
    PRIMARY_ID_EO_REMOVED = "PRIMARY_ID_EO_REMOVED"
    MIC_MAPPED_VENUE = "MIC_MAPPED_VENUE"


class EventType:
    EXCHANGE_ORDER_CANCEL_ACCEPTED = "EXCHANGEORDERCANCELACCEPTED"
    EXCHANGE_ORDER_ENTRY_ACCEPTED = "EXCHANGEORDERENTRYACCEPTED"
    EXCHANGE_ORDER_ENTRY_REQUESTED = "EXCHANGEORDERENTRYREQUESTED"
    EXCHANGE_ORDER_FILL_ENTERED = "EXCHANGEORDERFILLENTERED"
    EXCHANGE_ORDER_REPLACE_ACCEPTED = "EXCHANGEORDERREPLACEACCEPTED"
    EXCHANGE_ORDER_REPLACE_REJECTED = "EXCHANGEORDERREPLACEREJECTED"
    EXCHANGE_ORDER_ENTRY_REJECTED = "EXCHANGEORDERENTRYREJECTED"


class ObjectType:
    EXCHANGE_ORDER = "EXCHANGEORDER"
    EXCHANGE_ORDER_FILL = "EXCHANGEORDERFILL"


class HIERARCHY:
    CHILD = "Child"
    STANDALONE = "Standalone"


ALTERNATE_REF_SEPARATOR = "^"
EO_STR = "-EO"
EXCHANGE_ORDER_CODE_SEPARATOR = ":"
EXCHANGE_ORDER_CODE_STR = "EXCHANGE_ORDER_CODE"
ARC_EVENTS_TYPE_DICT = {
    ArcEventsColumns.LIMIT_PRICE: float,
    ArcEventsColumns.STOP_PRICE: float,
    ArcEventsColumns.TRADING_QUANTITY: float,
    ArcEventsColumns.QUANTITY: float,
    ArcEventsColumns.INTERNAL_SEQUENCE: int,
    ArcEventsColumns.GROSS_PRICE: float,
    ArcEventsColumns.STRIKE_PRICE: float,
}
MARKET_ORDER_TYPE_DICT = {
    MarketOrderColumns.GROSS_FILL_PRICE: float,
}
