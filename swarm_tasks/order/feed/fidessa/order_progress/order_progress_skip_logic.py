import os
from pathlib import Path

import pandas as pd
from prefect import context
from prefect.engine import signals
from pydantic import Field
from pytz import timezone
from swarm.task.auditor import Auditor
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.datetime.join_date_and_time import JoinDateAndTimeFormat
from swarm_tasks.transform.datetime.join_date_and_time import (
    Params as ParamsJoinDateAndTimeFormat,
)


class Params(BaseParams):
    date_attribute: str = Field(
        default="ENTERED_DATE",
        description="Name of the date column used in the skip logic",
    )
    time_attribute: str = Field(
        default="ENTERED_TIME",
        description="Name of the time column used in the skip logic",
    )
    timezone: str = Field(
        default="Europe/London", description="Pytz Timezone of the timestamps"
    )


class TempColumns:
    ENTERED_DATETIME = "__entered_datetime__"


class SourceColumns:
    ENTERED_DATE = "ENTERED_DATE"
    ENTERED_TIME = "ENTERED_TIME"
    TOP_LEVEL = "TOP_LEVEL"


class OrderProgressSkipLogic(TransformBaseTask):
    """
    This task filters out orders which were in the previous Order Progress File by comparing
    the previous business day of the datetime in the file name with the datetime in
    2 input file columns

    Logic:
    Step 1: Get the report_from_datetimedatetime from the file name: file name is
    of the format ORDER_PROGRESS-YYYYMMDDHHMM.csv.

    Step 2: Get the Order entered date time from the [Entered_Date] & [Entered_Time] columns

    Step 3:
    If [Entered_Date] + [Entered_Time] < datetime in report_from_datetime, discard the
    record.

    It also filters out records where Top_Level = 'N'.

    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:
        return self.process(
            source_frame=source_frame,
            params=params,
            logger=self.logger,
            auditor=self.auditor,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        logger=context.get("logger"),
        auditor: Auditor = None,
    ) -> pd.DataFrame:

        if (
            source_frame.empty
            or params.date_attribute not in source_frame.columns
            or params.time_attribute not in source_frame.columns
        ):
            raise signals.SKIP("Source frame empty or required columns not present")

        file_name = Path(os.getenv("SWARM_FILE_URL")).stem
        try:
            reporting_to_datetime = pd.Timestamp(file_name.split("-")[1])
        except IndexError as ie:
            error_msg = (
                f"Invalid file name. It needs to be of the format 'ORDER_PROGRESS-YYYYMMDDHHMM.csv'."
                f" Exception: {ie} "
            )
            auditor.add(error_msg)
            raise signals.FAIL(error_msg)
        except ValueError as ve:
            error_msg = (
                f"Invalid date in file name. It needs to be of the format"
                f" 'ORDER_PROGRESS-YYYYMMDDHHMM.csv'. Exception: {ve} "
            )
            auditor.add(error_msg)
            raise signals.FAIL(error_msg)
        reporting_from_datetime = reporting_to_datetime - pd.offsets.BusinessDay()
        timezone_info = timezone(params.timezone)
        reporting_from_datetime = timezone_info.localize(
            reporting_from_datetime
        ).astimezone("UTC")
        # Join the date and time columns and create a series with the target column
        entered_date_time_series = JoinDateAndTimeFormat.process(
            source_frame=source_frame,
            params=ParamsJoinDateAndTimeFormat(
                source_date_attribute=SourceColumns.ENTERED_DATE,
                source_time_attribute=SourceColumns.ENTERED_TIME,
                target_format="%Y-%m-%dT%H:%M:%SZ",
                target_attribute=TempColumns.ENTERED_DATETIME,
                timezone_info=params.timezone,
                source_format="%Y%m%d%H:%M:%S",
            ),
        )[TempColumns.ENTERED_DATETIME]

        entered_date_time_series = pd.to_datetime(entered_date_time_series)
        date_less_than_reporting_from_date_mask = (
            entered_date_time_series < reporting_from_datetime
        )
        data = source_frame.loc[~date_less_than_reporting_from_date_mask]

        skipped_records = date_less_than_reporting_from_date_mask.sum()

        # Skip records where Top_Level = 'N'
        top_level_n_mask = data.loc[:, SourceColumns.TOP_LEVEL].eq("N")

        skipped_records += top_level_n_mask.sum()

        data = data.loc[~top_level_n_mask]
        if data.empty:
            skip_message = (
                "All rows were skipped because they have already"
                " been processed in a previous Order Progress file or have"
                "Top_Level='N'."
            )
            auditor.add(skip_message)
            raise signals.SKIP(skip_message)

        if skipped_records > 0:
            skip_message = (
                f"{skipped_records} were skipped because they have already been processed"
                f" in a previous Order Progress file or have Top_Level='N'"
            )
            logger.info(skip_message)
            auditor.add(skip_message)
        return data
