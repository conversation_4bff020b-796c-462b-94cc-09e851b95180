import re
from pathlib import Path
from typing import Any
from typing import Optional

import pandas as pd
from prefect import context
from prefect.engine.signals import SKIP
from pydantic import Field
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_elastic_schema.static.mifid2 import OrderStatus
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.transform.base import BaseTask

from swarm_tasks.order.feed.charles_river.static import FileTypes
from swarm_tasks.order.feed.charles_river.static import SourceColumns
from swarm_tasks.order.feed.charles_river.static import TempColumns
from swarm_tasks.utilities.task_utils import match_enum_value


logger = context.get("logger")


class Params(BaseParams):
    trigger_file_type: str = Field(
        ...,
        description="File type which triggers downstream flows. "
        "This value is added as static column in the result dataframe."
        "Eg: order, fill, place triggers Order Flow"
        "allocation triggers Allocation Flow",
    )
    allocation_account_id_delimiter: str = Field(
        ";",
        description=(
            "Allocation has multiple records/orders for the same order id,"
            " this allows to choose the delimiter to which the multiple account"
            " ids should be concatenated with into a string"
        ),
    )


class CharlesRiverFrameTransformer(BaseTask):
    """
    This task is used to create new frame based on different file trigger flows
    (This task also computes the file type which triggered this flow based on last_modified_time on s3).

    Specs: https://steeleye.atlassian.net/wiki/spaces/IN/pages/**********/Order+Charles+River#Mapping
    """

    params_class = Params

    def execute(
        self,
        pre_process_result: dict = None,
        params: Optional[Params] = None,
        file_url: str = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            pre_process_result=pre_process_result,
            file_url=file_url,
            params=params,
        )

    @classmethod
    def process(
        cls,
        pre_process_result: dict = None,
        file_url: str = None,
        params: Optional[Params] = None,
    ) -> Any:
        if not pre_process_result:
            raise SKIP("Upstream pre process dict is empty")

        # ON-3404 remove leading and trailing spaces in column names and convert to uppercase
        pre_process_result = {
            k: cls.format_column_names(v) for k, v in pre_process_result.items()
        }

        file_type = match_enum_value(params.trigger_file_type, FileTypes)
        if file_type == "":
            raise SKIP(
                f"{params.trigger_file_type}: Invalid file_type provided. "
                f"Should be values among order, place, fills or allocation"
            )

        if file_type == FileTypes.ALLOCATIONS:
            transformed_df = cls.transform_allocation_file(
                source_frame_dict=pre_process_result,
            )
        else:
            transformed_df = cls.transform_order_file(
                source_frame_dict=pre_process_result,
                allocation_account_id_delimiter=params.allocation_account_id_delimiter,
            )

        if file_url:
            parsed_file_name = re.sub(
                f"_{'|'.join(pre_process_result.keys())}",
                "",
                Path(file_url).stem,
            )
        else:
            logger.info(
                f"Original file_name could not be persisted since file_url task value is {file_url}"
            )
            parsed_file_name = pd.NA

        transformed_df = transformed_df.assign(
            **{
                TempColumns.FILE_TYPE: file_type.value,
                TempColumns.SOURCE_FILE: parsed_file_name,
            }
        )

        # Sorting and resetting index based on orderid so that batches are created in such a
        # way which reduces probable missed in AssignMetaParent task
        transformed_df = transformed_df.sort_values(
            by=SourceColumns.ORDER_ID, ascending=True
        )
        transformed_df = transformed_df.reset_index(drop=True)

        logger.info(f"Transformed record view's shape is {transformed_df.shape}")

        source_dir = cls._get_source_dir()
        csv_file_path = source_dir.joinpath(
            f"data_transformed_{params.trigger_file_type}.csv"
        )
        transformed_df.to_csv(csv_file_path, index=False, encoding="utf-8", sep=",")
        result = ExtractPathResult(path=csv_file_path)

        return result

    @classmethod
    def transform_order_file(
        cls,
        source_frame_dict: dict,
        allocation_account_id_delimiter: str,
    ) -> pd.DataFrame:
        """
        Takes in the reference dataframe and then processes it according
        to order flow record_creation specs
        :param source_frame_dict: Dict containing the required dfs for transformation
        :param allocation_account_id_delimiter: the delimiter to which allocation ids list
        should be delimited with when converting into string
        :returns: Transformed df
        """
        order_df = source_frame_dict.get("order", pd.DataFrame())
        fill_df = source_frame_dict.get("fills", pd.DataFrame())
        place_df = source_frame_dict.get("place", pd.DataFrame())
        allocation_df = source_frame_dict.get("allocation", pd.DataFrame())

        if order_df.empty or fill_df.empty or place_df.empty or allocation_df.empty:
            raise SKIP(
                "Could not transform non allocation file as order/fill/place/allocation df is empty. "
                f"Counts:\n order: {order_df.shape}, "
                f"fill: {fill_df.shape}, "
                f"place: {place_df.shape}, "
                f"allocation: {allocation_df.shape}"
            )

        # Sort and take first record of place_df to preserve 1:1 mapping
        place_df = place_df.sort_values(
            by=[SourceColumns.ORDER_ID, SourceColumns.PLACE_DATE], ascending=[1, 1]
        )
        place_df = place_df.groupby(SourceColumns.ORDER_ID, as_index=False).nth(0)

        allocation_df.loc[
            :, SourceColumns.ACCOUNT_IDS
        ] = CharlesRiverFrameTransformer._group_account_ids_by_order_id(
            df=allocation_df,
            allocation_account_id_delimiter=allocation_account_id_delimiter,
        )

        # Take first record of allocation_df to preserve 1:1 mapping
        allocation_df = allocation_df.groupby(
            SourceColumns.ORDER_ID, as_index=False
        ).nth(0)

        # Merging order on fill df to get the necessary context for downstream processing
        # in primary transformations
        # Create FILL records
        fill_df = fill_df.merge(order_df, on=SourceColumns.ORDER_ID, how="left")
        fill_df = fill_df.assign(**{TempColumns.ORDER_STATUS: OrderStatus.PARF.value})

        # Create CAME records
        cancel_df = order_df[
            order_df[SourceColumns.ORDER_STATUS].str.upper().isin(["CNCLACCT", "CNCL"])
        ]
        cancel_df = cancel_df.assign(
            **{TempColumns.ORDER_STATUS: OrderStatus.CAME.value}
        )

        # Create NEWO records
        order_df = order_df.assign(**{TempColumns.ORDER_STATUS: OrderStatus.NEWO.value})

        transformed_df = pd.concat([order_df, fill_df, cancel_df])

        # Merge place_df based on orderid
        transformed_df = transformed_df.merge(
            place_df, on=SourceColumns.ORDER_ID, how="left", suffixes=("", "_place")
        )
        transformed_df = transformed_df.merge(
            allocation_df,
            on=SourceColumns.ORDER_ID,
            how="left",
            suffixes=("", "_allocation"),
        )

        return transformed_df

    @classmethod
    def transform_allocation_file(cls, source_frame_dict: dict) -> pd.DataFrame:
        """
        Takes in the reference dataframe and then processes it according
        to allocation flow record_creation specs
        :param source_frame_dict: Dict containing the required dfs for transformation
        should be delimited with when converting into string
        :returns: Transformed df
        """
        allocation_df = source_frame_dict.get("allocation", pd.DataFrame())
        order_df = source_frame_dict.get("order", pd.DataFrame())
        place_df = source_frame_dict.get("place", pd.DataFrame())

        if order_df.empty or place_df.empty or allocation_df.empty:
            raise SKIP(
                "Could not transform non allocation file as order/place/allocation df is empty. "
                f"Counts:\n order: {order_df.shape}, "
                f"place: {place_df.shape}, "
                f"allocation: {allocation_df.shape}"
            )

        # Sort and take first record of place_df to preserve 1:1 mapping
        place_df = place_df.sort_values(
            by=[SourceColumns.ORDER_ID, SourceColumns.PLACE_DATE], ascending=[1, 1]
        )
        place_df = place_df.groupby(SourceColumns.ORDER_ID, as_index=False).nth(0)

        filter_df = allocation_df.loc[allocation_df[SourceColumns.ORDER_EXEC_QTY] > 0]

        transformed_df = pd.concat(
            [
                filter_df.assign(**{TempColumns.ORDER_STATUS: OrderStatus.NEWO.value}),
                filter_df.assign(**{TempColumns.ORDER_STATUS: OrderStatus.FILL.value}),
            ]
        )

        # Merge place_df based on orderid
        transformed_df = transformed_df.merge(
            order_df, on=SourceColumns.ORDER_ID, how="left", suffixes=("", "_order")
        )
        transformed_df = transformed_df.merge(
            place_df, on=SourceColumns.ORDER_ID, how="left", suffixes=("", "_place")
        )

        return transformed_df

    @staticmethod
    def _get_source_dir() -> Path:
        return Path(Settings.context.sources_dir)

    @staticmethod
    def format_column_names(df: pd.DataFrame) -> pd.DataFrame:
        """
        :param df: pd.DataFrame
        :return: pd.DataFrame
        Convert dataframe columns to upper case and remove leading and trailing spaces
        """
        df.columns = df.columns.str.strip().str.upper()
        return df

    @staticmethod
    def _group_account_ids_by_order_id(
        df: pd.DataFrame, allocation_account_id_delimiter: str
    ) -> pd.Series:
        """
        Groups multiple account ids together into a string where values are delimited by
        `allocation_account_id_delimiter`.

        :param df: the dataframe where this should be applied to
        :param allocation_account_id_delimiter: the delimiter to which allocation ids list
            should be delimited with when converting into string
        :return: a series with the order id and the list of account ids as a string delimited
            by `allocation_account_id_delimiter`
        """
        account_ids_per_order_id_series: pd.Series = (
            df.groupby(SourceColumns.ORDER_ID)[SourceColumns.ACCOUNT_ID]
            .agg(list)
            .apply(lambda x: allocation_account_id_delimiter.join(sorted(list(set(x)))))
        )

        # [ON-3469] - Gets a unique list of client identifiers
        #            | based on the order id
        return df.loc[:, [SourceColumns.ORDER_ID]].merge(
            right=account_ids_per_order_id_series, on=SourceColumns.ORDER_ID, how="left"
        )[SourceColumns.ACCOUNT_ID]
