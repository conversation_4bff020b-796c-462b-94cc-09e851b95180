from enum import Enum

BROKER_CODE_TO_MIC_S3_FILE_KEY = "resource/charles_river/brokercode_to_mic_code.csv"


class FileTypes(Enum):
    ALLOCATIONS: str = "allocation"
    FILLS: str = "fills"
    ORDER: str = "order"
    PLACE: str = "place"


class SourceColumns:
    ACCOUNT_ID = "ACCOUNTID"
    ACCOUNT_IDS = "__ACCOUNT_IDS__"  # populated in controller flow
    ALGO_STRATEGY = "ALGOSTRATEGY"
    ALLO_BROKER_REASON = "ALLOCATIONBROKERREASON"
    ALLO_CLEARING_BROKER = "ALLOCAT<PERSON>CLEARINGBROKER"
    ALLO_DIRECTED_BROKER = "ALLOCATIONDIRECTEDBROKER"
    ALLO_DIRECTED_BROKER_TYPE = "ALLOCATIONDIRECTEDBROKERTYPE"
    ALLO_EXEC_BROKER = "ALLOCATIONEXECUTINGBROKER"
    BROKER_CODE = "BROKERCODE"
    BROKER_NAME = "BROKERNAME"
    CRD_INTERNAL_SECURITY_ID = "CRDINTERNALSECURITYID"
    CURRENCY_CODE = "CURRENCYCODE"
    FROM_CURRENCY_CODE = "FROMCURRENCYCODE"
    EXCHANGE_CODE = "EXCHANGECODE"
    EXEC_DECISION_MAKER = "EXECDECISIONMAKER"
    FILL_AMOUNT = "FILLAMOUNT"
    FILE_TYPE = "__FILE_TYPE__"  # populated in controller flow
    FILL_CREATE_DATE = "FILLCREATEDATE"
    FILL_DATE = "FILLDATE"
    FILL_QTY = "FILLQTY"
    FILL_PRICE = "FILLPRICE"
    FIX_EXECUTION_ID = "FIXEXECUTIONID"
    IPO = "IPO"
    ISIN = "ISIN"
    INVESTMENT_DECISION_MAKER = "INVESTMENTDECISIONMAKER"
    LAST_MARKET = "LASTMARKET"
    MANAGER_LIMIT = "MANAGERLIMIT"
    MIC = "MIC"
    NET_TRADE_IND = "NETTRADEIND"
    ORDER_NET_AMOUNT = "ORDERNETAMOUNT"
    PLACE_BROKER_ID = "PLACEBROKERID"
    PLACE_CREATE_DATE = "PLACECREATEDATE"
    PLACE_DATE = "PLACEDATE"
    PLACE_FIX_CLIENT_ORDER_ID = "PLACEFIXCLIENTORDERID"
    PLACE_ID = "PLACEID"
    PLACEMENT_ID = "PLACEMENTID"
    PLACE_REASON = "PLACEREASEON"
    QUANT_IND = "QUANT_IND"
    ORDER_AVERAGE_PRICE = "ORDERAVERAGEPRICE"
    ORDER_CREATE_DATE = "ORDERCREATEDATE"
    ORDER_EXEC_QTY = "ORDEREXECQTY"
    ORDER_EXEC_BROKER = "ORDEREXECUTINGBROKER"
    ORDER_ID = "ORDERID"
    ORDER_INSTRUCTION = "ORDERINSTRUCTION"
    ORDER_LAST_UPDATE_DATE = "ORDERLASTUPDATEDATE"
    ORDER_MANAGER_ID = "ORDERMANAGERID"
    ORIGINAL_ORDER_ID = "ORIGINALORDERID"
    ORIGINAL_ORDER_TARGET_QTY = "ORIGINALORDERTARGETQTY"
    ORDER_REASON = "ORDERREASON"
    ORDER_RELEASE_DATE = "ORDERRELEASEDATE"
    ORDER_STATUS = "ORDERSTATUS"
    ORDER_STATUS_TRANSFORMED = "__ORDER_STATUS__"  # transformed in controller flow
    ORDER_TARGET_QUANTITY = "ORDERTARGETQTY"
    ORDER_TRADER_ID = "ORDERTRADERID"
    SECURITY_NAME = "SECURITYNAME"
    SECURITY_TYPE = "SECURITYTYPE"
    SOURCE_FILE = "__SOURCE_FILE__"  # populated in controller flow
    SPECIAL_INST = "SPECIALINST"
    SIDE = "SIDE"
    STOP_PRICE = "STOPPRICE"
    TICKER = "TICKER"
    TIME_IN_FORCE = "TIMEINFORCE"
    TIME_ZONE_NAME = "TIMEZONENAME"
    TIME_ZONE_REGION_NAME = "TIMEZONEREGIONNAME"
    TO_CURRENCY_CODE = "TOCURRENCYCODE"
    TRADE_ALLOC_ID = "TRADEALLOCID"
    TRADE_DATE = "TRADEDATE"


class TempColumns:
    ALGO_STRATEGY = "__algo_strategy__"
    ALLO_BROKER_REASON = "__allo_broker_reason__"
    ALLO_CLEARING_BROKER = "__allo_clearing_broker"
    ALLO_DIRECTED_BROKER = "__allo_directed_broker__"
    ALLO_DIRECTED_BROKER_TYPE = "__allo_directed_broker_type"
    ASSET_CLASS = "__asset_class__"
    BUYER = "__buyer__"
    BUY_SELL = "__buy_sell__"
    BUY_SELL_INDICATOR = "__buy_sell_indicator__"
    CLIENT = "__client__"
    COUNTERPARTY = "__counterparty__"
    CRD_INTERNAL_SECURITY_ID = "__crd_internal_security_id__"
    CURRENCY_CODE = "__currency_code__"
    DELIMITER = "__delimiter__"
    EXECUTION_DETAILS_VALIDITY_PERIOD = "__execution_details_validity_period__"
    EXECUTING_ENTITY = "__executing_entity__"
    EXECUTION_WITHIN_FIRM = "__execution_within_firm__"
    EXPIRY_DATE = "__expiry_date__"
    FROM_CURRENCY_CODE = "__from_currency_code__"
    FILL_AMOUNT = "__fill_amount__"
    FILL_CREATE_DATE = "__fill_create_date__"
    FILL_DATE = "__fill_date__"
    FILL_PRICE = "__fillprice__"
    FILE_TYPE = "__file_type__"
    INST_ALT_ID_CODE = "__inst_alt_id_code__"
    INST_ID_CODE = "__inst_id_code__"
    INST_FB_BEST_EX_ASSET_CLASS_MAIN = "__inst_fb_best_ex_asset_class_main__"
    INST_FB_BEST_EX_ASSET_CLASS_SUB = "__inst_fb_best_ex_asset_class_sub__"
    INSTR_QUANTITY_NOTATION = "__instr_quantity_notation__"
    IPO = "__ipo__"
    INVESTMENT_DECISION_MAKER = "__investment_decision_maker__"
    IS_CREATED_THROUGH_FALLBACK = "__is_created_through_fallback__"
    MIC_CODE_FROM_LOOKUP_TABLE = "__mic_code_from_lookup_table__"
    NOTIONAL_CURRENCY_2 = "__notional_currency_2__"
    ORDER_NET_AMOUNT = "__order_net_amount__"
    PARTIES_FB_EXEC_ENTIY = "__parties_fb_exec_entiy__"
    PARTIES_FB_TRADER = "__parties_fb_trader__"
    PARTIES_FB_EXEC_WITHIN_FIRM = "__parties_fb_exec_within_firm__"
    PARTIES_FB_INV_DEC_MAKER = "__parties_fb_inv_dec_maker__"
    PARTIES_FB_CLIENT = "__parties_fb_client__"
    PARTIES_FB_COUNTERPARTY = "__parties_fb_counterparty__"
    PARTIES_FB_BUYER = "__parties_fb_buyer__"
    PARTIES_FB_SELLER = "__parties_fb_seller__"
    PLACE_CREATE_DATE = "__place_create_date__"
    PLACE_DATE = "__place_date__"
    PLACE_REASON = "__place_reason__"
    PREFIXED_ORDER_ID_CODE = "__prefixed_order_id_code__"
    PREFIXED_ORDER_STATUS = "__prefixed_order_status__"
    PRICE_CURRENCY = "__price_currency__"
    SELLER = "__seller__"
    SOURCE_FILE = "__source_file__"
    TEMP_COL_1 = "__temp_col_1__"
    TEMP_ID = "__id__"
    TRADE_DATE = "__trade_date__"
    TRADE_DATE_FORMATTED = "__trade_date_formatted__"
    TRADED_QUANTITY = "__traded_quantity__"
    TRADER = "__trader__"
    TRANSACTION_REFERENCE_NUMBER = "__transaction_ref_no__"
    TRANSACTION_REFERENCE_NUMBER_FIX = "__transaction_ref_no_fix__"
    TRANSACTION_REFERENCE_NUMBER_ORDER = "__transaction_ref_no_order__"
    OPTION_STRIKE_PRICE = "__option_strike_price__"
    OPTION_TYPE = "__option_type__"
    OPTION_TYPE_MAPPED = "__option_type_mapped__"
    ORDER_AVERAGE_PRICE = "__order_average_price__"
    ORDER_CREATE_DATE = "__order_create_date__"
    ORDER_CREATE_DATE_FORMATTED = "__order_create_date_formatted__"
    ORDER_RELEASE_DATE_FORMATTED = "__order_release_date_formatted__"
    ORDER_EXEC_QTY = "__order_exec_qty__"
    ORDER_ID_CODE = "__order_id_code__"
    ORDER_LAST_UPDATE_DATE = "__order_last_update_date__"
    ORIGINAL_ORDER_ID = "__original_order_id__"
    ORDER_RELEASE_DATE = "__order_release_date__"
    ORDER_RECEIVED = "__order_received__"
    ORDER_REASON = "__order_reason__"
    ORDER_STATUS = "__order_status__"
    ORDER_TRADING_DATE_TIME = "__order_trading_date_time__"
    REFERENCE_ORDER_ID = "__reference_order_id__"
    NEWO_IN_FILE = "__newo_in_file__"
    TEMP_PREFIX = "__temp_prefix__"
    TEMP_OPTION_PRICE = "__temp__option__price__"
    TEMP_UNDERLYING_SYMBOL_OPTION = "__underlying_symbol_option__"
    TEMP_UNDERLYING_SYMBOL_FUTURE = "__underlying_symbol_future__"
    TICKER_DATE_FUTURE = "__ticker_date_future__"
    TICKER_DATE_ECO = "__ticker_date_eco__"
    TICKER_DATE_ICO_IPO = "__ticker_date_ico_ipo__"
    TO_CURRENCY_CODE = "__to_currency_code__"
    SECURITY_NAME_CURRENCY_CODE_2ND_FIELD = "__security_name_currency_code_2nd_field__"
    SECURITY_NAME_CURRENCY_CODE_4TH_FIELD = "__security_name_currency_code_4th_field__"
    SECURITY_NAME_DATE_FORWARD = "__security_name_date_forward__"
    SOURCE_INDEX = "__source_index__"
    TIME_ZONE_REASON_NAME = "__time_zone_reason_name__"
    ULTIMATE_VENUE = "__ultimate_venue__"
    UNDERLYING_ISIN = "__underlying_isin__"
    UNDERLYING_SYMBOL = "__underlying_symbol__"


ID_LEI_REGEX = r"^(id|lei):"


class BrokerCodeLookupColumns:
    BROKER_CODE = "brokercode"
    BROKER_NAME = "Broker Name"
    MIC_CODE = "mic_code"
