class FreetradeLiquidMetrixColumns:
    """
    Columns present in the csv/excel Freetrade - Liquid-Metrix template
    """

    CURRENCY = "Currency"
    ISIN = "ISIN"
    VENUE = "Venue"


class InstrumentFallbackFields:

    # Ext
    EXT_INSTRUMENT_ID_TYPE = "ext.instrumentIdCodeType"
    EXT_INSTRUMENT_UNIQUE_ID = "ext.instrumentUniqueIdentifier"
    EXT_ON_FIRDS = "ext.onFIRDS"
    EXT_PRICE_NOTATION = "ext.priceNotation"
    EXT_QUANTITY_NOTATION = "ext.quantityNotation"

    # non-nested fields
    CFI_ATTRIBUTE_1 = "cfiAttribute1"
    CFI_ATTRIBUTE_2 = "cfiAttribute2"
    CFI_ATTRIBUTE_3 = "cfiAttribute3"
    CFI_ATTRIBUTE_4 = "cfiAttribute4"
    CFI_CATEGORY = "cfiCategory"
    CFI_GROUP = "cfiGroup"
    INSTRUMENT_CLASSIFICATION = "instrumentClassification"
    INSTRUMENT_FULL_NAME = "instrumentFullName"
    INSTRUMENT_ID_CODE = "instrumentIdCode"
    IS_CREATED_THROUGH_FALLBACK = "isCreatedThroughFallback"
    NOTIONAL_CURRENCY_1 = "notionalCurrency1"
    TRADING_VENUE = "venue.tradingVenue"


class FallbackStaticValues:

    COMMON_ORDINARY_SHARES = "Common / Ordinary shares"
    DEFAULT_INSTRUMENT_CLASSIFICATION = "ESXXXX"
    EQUITY = "Equity"
    ID = "ID"
    MONE = "MONE"
    NOT_APP_UNDEF = "Not Applicable/Undefined"
    UNIT = "UNIT"
