import pandas as pd
from se_core_tasks.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.order.feed.liquid_metrix.freetrade.resources.static import (
    FallbackStaticValues,
)
from swarm_tasks.order.feed.liquid_metrix.freetrade.resources.static import (
    FreetradeLiquidMetrixColumns,
)
from swarm_tasks.order.feed.liquid_metrix.freetrade.resources.static import (
    InstrumentFallbackFields,
)
from swarm_tasks.transform.steeleye.orders.common_utils import static

INSTRUMENT_PATH = "instrumentDetails.instrument"


class InstrumentFallback(TransformBaseTask):
    """Instrument details are populated here for the instruments
    that are not found/linked on SRP"""

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index, columns=[INSTRUMENT_PATH])
        if source_frame.empty:
            return target

        cols_used = [
            FreetradeLiquidMetrixColumns.ISIN,
            FreetradeLiquidMetrixColumns.CURRENCY,
            FreetradeLiquidMetrixColumns.VENUE,
            INSTRUMENT_PATH,
        ]

        df = source_frame.loc[:, source_frame.columns.intersection(cols_used)]

        for col in cols_used:
            if col not in df.columns:
                df[col] = pd.NA

        target = source_frame.loc[:, INSTRUMENT_PATH]

        # Run fallback where there is no instrument
        without_instrument_mask = df.loc[:, INSTRUMENT_PATH].isnull()

        if not without_instrument_mask.any():
            return target

        data = df.loc[without_instrument_mask, :]
        data = self._process_data(df=data)

        # Synthetic instruments
        synthetic_instruments = self._get_synthetic_instruments(df=data)

        target.loc[without_instrument_mask] = synthetic_instruments

        return target

    @staticmethod
    def _process_data(df: pd.DataFrame) -> pd.DataFrame:
        """
        Necessary transformations to the source data
        :param df: source frame with the columns needed for fallback
        :return: `df` with source columns after transformation
        """
        # Currency Conversion
        # read the static json file with minor currencies and conversion map
        minor_ccy_price_data = ConvertMinorToMajor.read_minor_ccy_and_price(
            static.MINOR_CURRENCIES_FILE_PATH
        )

        ccy_not_null_mask = df[FreetradeLiquidMetrixColumns.CURRENCY].notnull()
        if ccy_not_null_mask.any():
            df.loc[ccy_not_null_mask, FreetradeLiquidMetrixColumns.CURRENCY] = df.loc[
                ccy_not_null_mask, FreetradeLiquidMetrixColumns.CURRENCY
            ].apply(
                (
                    lambda x: ConvertMinorToMajor.convert_currency(
                        source_ccy=x, conversion_map_list=minor_ccy_price_data
                    )
                )
            )

        return df

    @staticmethod
    def _get_synthetic_instruments(df: pd.DataFrame) -> pd.Series:
        """
        Populate "instrumentDetails.instrument" dict field with the data
        generated here for all instruments that failed to link up with SRP
        :param df: source frame with raw columns necessary for instrument data mapping
        :return: "instrumentDetails.instrument" Series with dict records
        """
        synthetic_instruments = pd.DataFrame(index=df.index)

        # attribute mapping from source file fields
        attribute_map = [
            (
                FreetradeLiquidMetrixColumns.ISIN,
                InstrumentFallbackFields.INSTRUMENT_ID_CODE,
            ),
            (
                FreetradeLiquidMetrixColumns.CURRENCY,
                InstrumentFallbackFields.NOTIONAL_CURRENCY_1,
            ),
            (
                FreetradeLiquidMetrixColumns.VENUE,
                InstrumentFallbackFields.TRADING_VENUE,
            ),
        ]

        for entry in attribute_map:
            synthetic_instruments[entry[1]] = df[entry[0]]

        # static mapping
        static_map = [
            (FallbackStaticValues.EQUITY, InstrumentFallbackFields.CFI_CATEGORY),
            (
                FallbackStaticValues.COMMON_ORDINARY_SHARES,
                InstrumentFallbackFields.CFI_GROUP,
            ),
            (
                FallbackStaticValues.NOT_APP_UNDEF,
                InstrumentFallbackFields.CFI_ATTRIBUTE_1,
            ),
            (
                FallbackStaticValues.NOT_APP_UNDEF,
                InstrumentFallbackFields.CFI_ATTRIBUTE_2,
            ),
            (
                FallbackStaticValues.NOT_APP_UNDEF,
                InstrumentFallbackFields.CFI_ATTRIBUTE_3,
            ),
            (
                FallbackStaticValues.NOT_APP_UNDEF,
                InstrumentFallbackFields.CFI_ATTRIBUTE_4,
            ),
            (
                FallbackStaticValues.ID,
                InstrumentFallbackFields.EXT_INSTRUMENT_ID_TYPE,
            ),
            (
                FallbackStaticValues.MONE,
                InstrumentFallbackFields.EXT_PRICE_NOTATION,
            ),
            (
                FallbackStaticValues.UNIT,
                InstrumentFallbackFields.EXT_QUANTITY_NOTATION,
            ),
            (
                FallbackStaticValues.DEFAULT_INSTRUMENT_CLASSIFICATION,
                InstrumentFallbackFields.INSTRUMENT_CLASSIFICATION,
            ),
            (True, InstrumentFallbackFields.IS_CREATED_THROUGH_FALLBACK),
        ]

        for entry in static_map:
            synthetic_instruments[entry[1]] = entry[0]

        # Populate instrumentFullName from the concatenation of ISIN, Currency and Venue values
        # Null values are populated as empty strings
        synthetic_instruments.loc[:, InstrumentFallbackFields.INSTRUMENT_FULL_NAME] = [
            isin + " " + currency + " " + venue
            for isin, currency, venue in zip(
                df.loc[:, FreetradeLiquidMetrixColumns.ISIN].fillna(""),
                df.loc[:, FreetradeLiquidMetrixColumns.CURRENCY].fillna(""),
                df.loc[:, FreetradeLiquidMetrixColumns.VENUE].fillna(""),
            )
        ]

        # Populate ext.instrumentUniqueIdentifier from instrumentFullName without separators
        synthetic_instruments.loc[
            :, InstrumentFallbackFields.EXT_INSTRUMENT_UNIQUE_ID
        ] = synthetic_instruments.loc[
            :, InstrumentFallbackFields.INSTRUMENT_FULL_NAME
        ].str.replace(
            " ", ""
        )

        instrument_details_instrument = synthetic_instruments.loc[:, :].apply(
            lambda x: x.dropna().to_dict(), axis=1
        )

        return instrument_details_instrument
