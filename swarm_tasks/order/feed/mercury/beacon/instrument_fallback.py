""" Instrument fallback task for Mercury Beacon
Mapping Document in Confluence:
https://steeleye.atlassian.net/wiki/spaces/IN/pages/1436909577/Order+Mercury+Beacon
"""
import pandas as pd
from se_core_tasks.currency.convert_minor_to_major import ConvertMinorToMajor
from se_elastic_schema.static.mifid2 import BestExAssetClassMain
from se_elastic_schema.static.mifid2 import OptionExerciseStyle
from se_elastic_schema.static.mifid2 import OptionType
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.reference import StrikePriceType
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.order.feed.mercury.beacon.resources.static import FallbackStaticValues
from swarm_tasks.order.feed.mercury.beacon.resources.static import (
    InstrumentFallbackFields,
)
from swarm_tasks.order.feed.mercury.beacon.resources.static import MercuryBeaconColumns
from swarm_tasks.order.feed.mercury.beacon.resources.static import (
    MercuryBeaconTempColumns,
)
from swarm_tasks.transform.steeleye.orders.common_utils import static

INSTRUMENT_PATH = "instrumentDetails.instrument"


class DictionaryMaps:
    """Contains dictionaries used in pandas map functions"""

    BESTEX_ASSET_CLASS_MAIN_MAP = {
        "C": BestExAssetClassMain.CURRENCY_DERIVATIVES.value,
        "F": BestExAssetClassMain.EQUITY_DERIVATIVES.value,
        "P": BestExAssetClassMain.CURRENCY_DERIVATIVES.value,
        "X": BestExAssetClassMain.CURRENCY_DERIVATIVES.value,
    }

    BESTEX_ASSET_CLASS_SUB_MAP = {
        "C": "Swaps and other currency derivatives",
        "F": "Futures and options admitted to trading on a trading venue",
        "P": "Swaps and other currency derivatives",
        "X": "Swaps and other currency derivatives",
    }

    INSTRUMENT_CLASSIFICATION_MAP = {
        "C": "OCEFPS",
        "F": "FFIPXX",
        "P": "OPEFPS",
        "X": "JFTXCC",
    }

    OPTION_EXERCISE_STYLE_MAP = {
        "C": OptionExerciseStyle.EURO.value,
        "P": OptionExerciseStyle.EURO.value,
    }

    OPTION_TYPE_MAP = {
        "C": OptionType.CALL.value,
        "P": OptionType.PUTO.value,
    }

    STRIKE_PRICE_TYPE_MAP = {
        "C": StrikePriceType.MNTRYVAL.value,
        "P": StrikePriceType.MNTRYVAL.value,
    }


class InstrumentFallback(TransformBaseTask):
    """Instrument details are populated here for the instruments
    that are not found/linked on SRP"""

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index, columns=[INSTRUMENT_PATH])
        if source_frame.empty:
            return target

        cols_used = [
            INSTRUMENT_PATH,
            MercuryBeaconColumns.EXPIRATION,
            MercuryBeaconColumns.STRIKE,
            MercuryBeaconColumns.SYMBOL,
            MercuryBeaconColumns.TYPE,
        ]

        df = source_frame.loc[:, source_frame.columns.intersection(cols_used)]

        for col in cols_used:
            if col not in df.columns:
                df[col] = pd.NA

        target.loc[:, INSTRUMENT_PATH] = source_frame.loc[:, INSTRUMENT_PATH]

        # Run fallback where there is no instrument
        without_instrument_mask = df.loc[:, INSTRUMENT_PATH].isnull()

        if not without_instrument_mask.any():
            return target

        data = df.loc[without_instrument_mask, :]
        data = self._process_data(df=data)

        # Synthetic instruments
        synthetic_instruments = self._get_synthetic_instruments(df=data)

        target.loc[without_instrument_mask, INSTRUMENT_PATH] = synthetic_instruments

        return target

    @staticmethod
    def _process_data(df: pd.DataFrame) -> pd.DataFrame:
        """
        Necessary transformations to the source data, does all the
        dict-based mappings required.
        :return: `df` with source columns after transformation
        """

        # Add temp columns
        symbol_present_mask = df[MercuryBeaconColumns.SYMBOL].notnull()
        df.loc[symbol_present_mask, MercuryBeaconTempColumns.TEMP_CURRENCY] = df.loc[
            symbol_present_mask, MercuryBeaconColumns.SYMBOL
        ].apply(lambda symbol: symbol[:3])
        expiry_date_present_mask = df[MercuryBeaconColumns.EXPIRATION].notnull()
        df.loc[:, MercuryBeaconTempColumns.TEMP_EXPIRY_DATE_MMMYYYY] = pd.to_datetime(
            df.loc[:, MercuryBeaconColumns.EXPIRATION]
        )
        df.loc[
            expiry_date_present_mask, MercuryBeaconTempColumns.TEMP_EXPIRY_DATE_MMMYYYY
        ] = df.loc[
            expiry_date_present_mask, MercuryBeaconTempColumns.TEMP_EXPIRY_DATE_MMMYYYY
        ].dt.strftime(
            "%b%Y"
        )

        # Currency Conversion
        # read the static json file with minor currencies and conversion map
        minor_ccy_price_data = ConvertMinorToMajor.read_minor_ccy_and_price(
            static.MINOR_CURRENCIES_FILE_PATH
        )
        ccy_not_null_mask = df[MercuryBeaconTempColumns.TEMP_CURRENCY].notnull()
        if ccy_not_null_mask.any():
            df.loc[ccy_not_null_mask, MercuryBeaconTempColumns.TEMP_CURRENCY] = df.loc[
                ccy_not_null_mask, MercuryBeaconTempColumns.TEMP_CURRENCY
            ].apply(
                (
                    lambda x: ConvertMinorToMajor.convert_currency(
                        source_ccy=x, conversion_map_list=minor_ccy_price_data
                    )
                )
            )

        # Upper
        type_present_mask = df[MercuryBeaconColumns.TYPE].notnull()
        df.loc[type_present_mask, MercuryBeaconColumns.TYPE] = df.loc[
            type_present_mask, MercuryBeaconColumns.TYPE
        ].str.upper()
        return df

    def _get_synthetic_instruments(self, df: pd.DataFrame) -> pd.Series:
        """
        Populate "instrumentDetails.instrument" dict field with the data
        generated here for all instruments that failed to link up with SRP.
        :param df: source frame with raw columns necessary for instrument data mapping
        :return: "instrumentDetails.instrument" Series with dict records
        """
        synthetic_instruments = pd.DataFrame(index=df.index)

        # Map Expiry date
        expiry_date_present_mask = df[MercuryBeaconColumns.EXPIRATION].notnull()
        synthetic_instruments.loc[
            expiry_date_present_mask, InstrumentFallbackFields.DERIVATIVE_EXPIRY_DATE
        ] = df.loc[expiry_date_present_mask, MercuryBeaconColumns.EXPIRATION]

        # Dict mappings
        type_options_mask = df[MercuryBeaconColumns.TYPE].isin(
            FallbackStaticValues.TYPE_OPTIONS
        )
        synthetic_instruments.loc[
            type_options_mask, InstrumentFallbackFields.DERIVATIVE_OPTION_EXERCISE_STYLE
        ] = df.loc[type_options_mask, MercuryBeaconColumns.TYPE].map(
            DictionaryMaps.OPTION_EXERCISE_STYLE_MAP
        )
        synthetic_instruments.loc[
            type_options_mask, InstrumentFallbackFields.DERIVATIVE_OPTION_TYPE
        ] = df.loc[type_options_mask, MercuryBeaconColumns.TYPE].map(
            DictionaryMaps.OPTION_TYPE_MAP
        )
        synthetic_instruments.loc[
            type_options_mask, InstrumentFallbackFields.EXT_STRIKE_PRICE_TYPE
        ] = df.loc[type_options_mask, MercuryBeaconColumns.TYPE].map(
            DictionaryMaps.STRIKE_PRICE_TYPE_MAP
        )

        type_present_mask = df[MercuryBeaconColumns.TYPE].notnull()
        synthetic_instruments.loc[
            type_present_mask, InstrumentFallbackFields.INSTRUMENT_CLASSIFICATION
        ] = df.loc[type_present_mask, MercuryBeaconColumns.TYPE].map(
            DictionaryMaps.INSTRUMENT_CLASSIFICATION_MAP
        )
        synthetic_instruments.loc[
            type_present_mask, InstrumentFallbackFields.EXT_BEST_EX_ASSET_CLASS_MAIN
        ] = df.loc[type_present_mask, MercuryBeaconColumns.TYPE].map(
            DictionaryMaps.BESTEX_ASSET_CLASS_MAIN_MAP
        )
        synthetic_instruments.loc[
            type_present_mask, InstrumentFallbackFields.EXT_BEST_EX_ASSET_CLASS_SUB
        ] = df.loc[type_present_mask, MercuryBeaconColumns.TYPE].map(
            DictionaryMaps.BESTEX_ASSET_CLASS_SUB_MAP
        )
        # Map strike price
        synthetic_instruments.loc[
            type_options_mask, InstrumentFallbackFields.DERIVATIVE_STRIKE_PRICE
        ] = df.loc[type_options_mask, MercuryBeaconColumns.STRIKE]

        # Map fx derivatives notional currency 2
        type_fxcfd_mask = (
            df[MercuryBeaconColumns.TYPE] == FallbackStaticValues.TYPE_FXCFD
        )
        synthetic_instruments.loc[
            type_fxcfd_mask, InstrumentFallbackFields.FX_DERIVATIVES_NOTIONAL_CURRENCY_2
        ] = df.loc[type_fxcfd_mask, MercuryBeaconTempColumns.TEMP_CURRENCY]

        # static mapping
        static_map = [
            (
                FallbackStaticValues.USD,
                InstrumentFallbackFields.DERIVATIVE_STRIKE_PRICE_CURRENCY,
            ),
            (1, InstrumentFallbackFields.DERIVATIVE_PRICE_MULTIPLIER),
            (
                PriceNotation.MONE.value,
                InstrumentFallbackFields.EXT_PRICE_NOTATION,
            ),
            (
                QuantityNotation.UNIT.value,
                InstrumentFallbackFields.EXT_QUANTITY_NOTATION,
            ),
            (
                FallbackStaticValues.USD,
                InstrumentFallbackFields.NOTIONAL_CURRENCY_1,
            ),
            (
                True,
                InstrumentFallbackFields.IS_CREATED_THROUGH_FALLBACK,
            ),
            (
                FallbackStaticValues.DEFAULTVENUE,
                InstrumentFallbackFields.TRADING_VENUE,
            ),
            (FallbackStaticValues.DEFAULTVENUE, InstrumentFallbackFields.EXT_AII_MIC),
        ]

        for entry in static_map:
            synthetic_instruments[entry[1]] = entry[0]

        # Map instrument fullname
        fullname_columns = [
            MercuryBeaconColumns.STRIKE,
            MercuryBeaconColumns.TYPE,
            MercuryBeaconTempColumns.TEMP_CURRENCY,
            MercuryBeaconTempColumns.TEMP_EXPIRY_DATE_MMMYYYY,
        ]
        fullname_columns_mask = df.columns.isin(fullname_columns)
        synthetic_instruments.loc[
            type_present_mask, InstrumentFallbackFields.INSTRUMENT_FULL_NAME
        ] = df.loc[type_present_mask, fullname_columns_mask].apply(
            self._instrument_full_name, axis=1
        )

        instrument_details_instrument = synthetic_instruments.loc[:, :].apply(
            lambda x: x.dropna().to_dict(), axis=1
        )

        return instrument_details_instrument

    @staticmethod
    def _instrument_full_name(row: pd.Series) -> str:
        if row[MercuryBeaconColumns.TYPE] in FallbackStaticValues.TYPE_OPTIONS:
            return (
                f"Options on {row[MercuryBeaconTempColumns.TEMP_CURRENCY]} "
                f"Futures, {row[MercuryBeaconTempColumns.TEMP_EXPIRY_DATE_MMMYYYY]} "
                f"{row[MercuryBeaconColumns.TYPE]} {row[MercuryBeaconColumns.STRIKE]}"
            )
        if row[MercuryBeaconColumns.TYPE] == FallbackStaticValues.TYPE_FUTURES:
            return (
                f"{row[MercuryBeaconTempColumns.TEMP_CURRENCY]} Futures, "
                f"{row[MercuryBeaconTempColumns.TEMP_EXPIRY_DATE_MMMYYYY]}"
            )
        if row[MercuryBeaconColumns.TYPE] == FallbackStaticValues.TYPE_FXCFD:
            return f"USD/{row[MercuryBeaconTempColumns.TEMP_CURRENCY]} CFD"
