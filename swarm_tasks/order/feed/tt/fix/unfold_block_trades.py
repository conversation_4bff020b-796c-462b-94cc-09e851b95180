import pandas as pd
from prefect import context
from se_trades_tasks.order.feed.tt.fix.unfold_block_trades import (
    run_unfold_block_trades,
)
from swarm.task.transform.base import TransformBaseTask


class UnfoldBlockTrades(TransformBaseTask):
    def execute(
        self,
        source_frame: pd.DataFrame = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame,
        auditor=None,
        logger=context.get("logger"),
    ) -> pd.DataFrame:

        return run_unfold_block_trades(
            source_frame=source_frame,
            auditor=auditor,
            logger=logger,
        )
