from typing import TypeVar
from typing import Union

import pandas as pd
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.order.feed.tt.fix.resources.static import EXCHANGE_MAP

PD_NA = TypeVar("pd.NA")


class SourceColumns:
    LAST_MKT = "LastMkt"
    SECURITY_EXCHANGE = "SecurityExchange"


class Params(BaseParams):
    security_exchange: str = Field(
        default="SecurityExchange", description="SecurityExchange"
    )
    last_mkt: str = Field(default="LastMkt", description="LastMkt field")
    target_attribute: str = Field(
        default="transactionDetails.ultimateVenue", description="Target Col"
    )


class TargetColumn:
    ULTIMATE_VENUE = "transactionDetails.ultimateVenue"


class SecurityExchange(TransformBaseTask):
    """
    This task creates the "transactionDetails.ultimateVenue" column based on the Bloomberg/TT trade sink handler logic
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ):

        target = pd.DataFrame(index=source_frame.index)

        if source_frame.empty:
            return target

        source_columns = [params.last_mkt, params.security_exchange]
        source_columns_mask = source_frame.columns.isin(source_columns)
        df = source_frame.loc[:, source_columns_mask]

        target.loc[:, params.target_attribute] = df.loc[:, :].apply(
            lambda x: self._get_security_exchange(x, params=params), axis=1
        )

        return target

    @staticmethod
    def _get_security_exchange(row: pd.Series, params: Params) -> Union[str, PD_NA]:
        """
        Logic to get the valid security exchange field
        Priority rank is as follows:
        - SecurityExchange (fix tag 207) is populated and contains 4 characters only
        - LastMkt (fix tag 30) is populated and contains 4 characters only
        - SecurityExchange has a match in the resources EXCHANGE_MAP
        - LastMkt has a match in the resources EXCHANGE_MAP
        - SecurityExchange is populated
        - LastMkt is populated
        - Return pd.NA if none of the above conditions are met
        :param row: Series with the columns necessary for determining the security exchange
        :return: security exchange string if any condition is met, pd.NA otherwise
        """

        security_exchange = (
            row[params.security_exchange]
            if not pd.isnull(row[params.security_exchange])
            else ""
        )
        last_mkt = row[params.last_mkt] if not pd.isnull(row[params.last_mkt]) else ""
        valid_security_exchange = pd.NA

        if len(security_exchange) == 4:
            valid_security_exchange = security_exchange

        elif len(last_mkt) == 4:
            valid_security_exchange = last_mkt

        elif EXCHANGE_MAP.get(security_exchange):
            valid_security_exchange = EXCHANGE_MAP.get(security_exchange)

        elif EXCHANGE_MAP.get(last_mkt):
            EXCHANGE_MAP.get(last_mkt)

        elif security_exchange:
            valid_security_exchange = security_exchange

        elif last_mkt:
            valid_security_exchange = last_mkt

        return valid_security_exchange
