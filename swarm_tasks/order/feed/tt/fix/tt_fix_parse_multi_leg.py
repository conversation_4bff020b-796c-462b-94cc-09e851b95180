import logging
import math
from typing import Any
from typing import List
from typing import Optional
from typing import Union

import pandas as pd
from se_trades_tasks.order_and_tr.fix.static import FixTagNames
from se_trades_tasks.order_and_tr.fix.static import S3_FILE_URL
from swarm.task.auditor import Auditor
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class DerivedFields:
    DERIVED_ALT_SEC_ID_MULTILEG = "DERIVED_ALT_SEC_ID_MULTILEG"
    DERIVED_ISIN_MULTILEG = "DERIVED_ISIN_MULTILEG"
    DERIVED_SIDE = "DERIVED_SIDE"


class TTFixParseMultiLeg(TransformBaseTask):
    """
    Parses MultiLeg trades from the source dataframe as per
    https://steeleye.atlassian.net/wiki/spaces/IN/pages/1439465640/Order+TT+FIX#Multi-leg-trades
    """

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[BaseParams] = None,
        resources: Optional[BaseResources] = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame, logger=self.logger, auditor=self.auditor
        )

    @classmethod
    def process(
        cls, source_frame: pd.DataFrame, auditor: Auditor, logger: logging.Logger
    ) -> pd.DataFrame:

        target = pd.DataFrame(
            data=pd.NA, index=source_frame.index, columns=source_frame.columns.to_list()
        )
        if source_frame.empty:
            return target

        records = []
        for idx, row in source_frame.iterrows():
            # ff_555 represents number of legs
            # default to 1 if not present
            ff_555 = cls.get_field_escape_na(
                row=row, source_field=FixTagNames.NO_LEGS, default="1"
            )
            # ff_442=3 represents MultiLeg or when MsgType=AE & SecurityType=MLEG & TrdType=1
            ff_442 = cls.get_field_escape_na(
                row=row, source_field=FixTagNames.MULTI_LEG_REPORTING_TYPE, default=""
            )
            msg_type = cls.get_field_escape_na(
                row=row, source_field=FixTagNames.MSG_TYPE, default=""
            )
            sec_type = cls.get_field_escape_na(
                row=row, source_field=FixTagNames.SECURITY_TYPE, default=""
            )
            trade_type = cls.get_field_escape_na(
                row=row, source_field=FixTagNames.TRD_TYPE, default=""
            )
            if (ff_442 == "3") | (
                (msg_type == "AE") & (sec_type == "MLEG") & (str(trade_type) == "1")
            ):
                isin_list = cls._get_security_id_list(
                    source_series=row, ff_606_value="4", logger=logger, auditor=auditor
                )
                sec_alt_id_list = cls._get_security_id_list(
                    source_series=row, ff_606_value="8", logger=logger, auditor=auditor
                )
                for leg_index in range(int(ff_555)):
                    data: dict = row.to_dict()
                    data[FixTagNames.ORDER_QTY] = cls._get_order_qty(
                        data=data,
                        leg_index=leg_index,
                        logger=logger,
                    )
                    data[DerivedFields.DERIVED_SIDE] = cls._get_side(
                        data=data, leg_index=leg_index, logger=logger, auditor=auditor
                    )
                    data[
                        DerivedFields.DERIVED_ISIN_MULTILEG
                    ] = cls._get_sec_id_multi_leg(
                        data=data,
                        leg_index=leg_index,
                        sec_id_list=isin_list,
                        logger=logger,
                        auditor=auditor,
                    )
                    data[
                        DerivedFields.DERIVED_ALT_SEC_ID_MULTILEG
                    ] = cls._get_sec_id_multi_leg(
                        data=data,
                        leg_index=leg_index,
                        sec_id_list=sec_alt_id_list,
                        logger=logger,
                        auditor=auditor,
                    )
                    data[FixTagNames.SECURITY_TYPE] = cls._get_field_based_on_leg_index(
                        data=data,
                        target_field_name=FixTagNames.LEG_SECURITY_TYPE,
                        leg_index=leg_index,
                        logger=logger,
                        auditor=auditor,
                    )
                    data[FixTagNames.CURRENCY] = cls._get_field_based_on_leg_index(
                        data=data,
                        target_field_name=FixTagNames.LEG_CURRENCY,
                        leg_index=leg_index,
                        logger=logger,
                        auditor=auditor,
                    )
                    data[FixTagNames.CFI_CODE] = cls._get_field_based_on_leg_index(
                        data=data,
                        target_field_name=FixTagNames.LEG_CFI_CODE,
                        leg_index=leg_index,
                        logger=logger,
                        auditor=auditor,
                    )
                    data[FixTagNames.STRIKE_PRICE] = cls._get_field_based_on_leg_index(
                        data=data,
                        target_field_name=FixTagNames.LEG_STRIKE_PRICE,
                        leg_index=leg_index,
                        logger=logger,
                        auditor=auditor,
                    )
                    data[FixTagNames.MATURITY_DATE] = cls._get_field_based_on_leg_index(
                        data=data,
                        target_field_name=FixTagNames.LEG_MATURITY_DATE,
                        leg_index=leg_index,
                        logger=logger,
                        auditor=auditor,
                    )
                    data[FixTagNames.SYMBOL] = cls._get_field_based_on_leg_index(
                        data=data,
                        target_field_name=FixTagNames.LEG_SYMBOL,
                        leg_index=leg_index,
                        logger=logger,
                        auditor=auditor,
                    )
                    data[
                        FixTagNames.SECURITY_EXCHANGE
                    ] = cls._get_field_based_on_leg_index(
                        data=data,
                        target_field_name=FixTagNames.LEG_EX_DESTINATION,
                        leg_index=leg_index,
                        logger=logger,
                        auditor=auditor,
                    )
                    records.append(data)

        # Convert list of multi-leg records into a dataframe
        if records:
            target = pd.DataFrame(records)
        return target

    @classmethod
    def _get_order_qty(
        cls,
        data: dict,
        leg_index: int,
        logger: logging.Logger,
    ) -> float:
        """Calculates and returns the order qty
        :param data: pd.Series order row
        :param leg_index: int index of the leg
        :param logger: Logger instance
        :return: float calculated order qty(Rounds to next whole number)
        """
        ff_623_leg_ratio_qty = data.get(FixTagNames.LEG_RATIO_QTY, list())
        order_qty = 0.0
        qty_value = data.get(FixTagNames.ORDER_QTY, 0.0)
        # Handle cases where qty value is pd.NA by returning 0.0
        if pd.isna(qty_value):
            return order_qty
        try:
            order_qty = qty_value * ff_623_leg_ratio_qty[leg_index]
        except Exception as e:
            msg = (
                f"Error calculating order_qty for order_id:"
                f"{data.get(FixTagNames.ORDER_ID)}. Error:{e},"
                f"order_id:{data.get(FixTagNames.ORDER_ID)}. file_url:"
                f"{data.get(S3_FILE_URL)}"
            )
            logger.error(msg)
        # ceil() returns next integer, so converting it to float
        return float(math.ceil(order_qty))

    @classmethod
    def _get_side(
        cls, data: dict, leg_index: int, logger: logging.Logger, auditor: Auditor
    ) -> str:
        """Derives Side
        :param data: pd.Series order row
        :param leg_index: int index of the leg
        :return: str Side
        """
        ff_624_leg_side = data.get(FixTagNames.LEG_SIDE, list())
        side = pd.NA
        try:
            side = ff_624_leg_side[leg_index]
        except Exception as e:
            msg = (
                f"Error calculating side for order_id:"
                f"{data.get(FixTagNames.ORDER_ID)}. "
                f"Error:{e},"
                f"order_id:{data.get(FixTagNames.ORDER_ID)}.file_url:"
                f"{data.get(S3_FILE_URL)}"
            )
            logger.error(msg)
            auditor.add(msg)
        return side

    @classmethod
    def _get_sec_id_multi_leg(
        cls,
        data: dict,
        leg_index: int,
        sec_id_list: List[str],
        logger: logging.Logger,
        auditor: Auditor,
    ) -> str:
        """Derives isin from leg_index
        :param leg_index: int
        :param sec_id_list: List[str]
        :param logger: logging.Logger
        :param auditor: Auditor
        :return:
        """
        result = pd.NA
        try:
            result = sec_id_list[leg_index]
        except Exception as e:
            msg = (
                f"Error fetching isin for multi-leg:"
                f"isin_list:{sec_id_list}.leg_index:{leg_index}"
                f"Error:{e},"
                f"order_id:{data.get(FixTagNames.ORDER_ID)}.file_url:"
                f"{data.get(S3_FILE_URL)}"
            )
            logger.error(msg)
            auditor.add(msg)
        return result

    @classmethod
    def _get_security_id_list(
        cls,
        source_series: pd.Series,
        ff_606_value: str,
        logger: logging.Logger,
        auditor: Auditor,
    ) -> List[str]:
        """
        For multi-legs value in fix_tag 605 where fix-tag 606==4 represents ISIN and
        606==8 represents alternate_security_id.
        Here we derive ISIN/alternate_security_id based on ff_606_value for each leg and
        return as list.
        :param source_series: pd.Series
        :param logger: logging.logger
        :param auditor: Auditor
        :return:
        """
        sec_id_list = []
        ff_605_leg_security_alt_id = source_series.get(
            FixTagNames.LEG_SECURITY_ALT_ID, list()
        )
        ff_606_leg_security_alt_id_source = source_series.get(
            FixTagNames.LEG_SECURITY_ALT_ID_SOURCE, list()
        )
        try:
            indices_ff_606 = [
                i
                for i, x in enumerate(ff_606_leg_security_alt_id_source)
                if x == ff_606_value
            ]
            sec_id_list = [ff_605_leg_security_alt_id[x] for x in indices_ff_606]
        except Exception as e:
            msg = (
                f"Error parsing ISIN from multilegs. Error:{e},"
                f"ff_605_leg_security_alt_id:{ff_605_leg_security_alt_id},"
                f"ff_606_leg_security_alt_id_source:{ff_606_leg_security_alt_id_source},"
                f"order_id:{source_series.get(FixTagNames.ORDER_ID)}. file_url:"
                f"{source_series.get(S3_FILE_URL)}."
            )
            logger.error(msg)
            auditor.add(msg)
        return sec_id_list

    @classmethod
    def _get_field_based_on_leg_index(
        cls,
        data: dict,
        target_field_name: str,
        leg_index: int,
        logger: logging.Logger,
        auditor: Auditor,
    ) -> Union[str, float]:
        """Returns target_field based on leg_index
        :param data: pd.Series
        :param target_field_name: str
        :param leg_index: int
        :return: str
        """
        target_field_list = data.get(target_field_name, list())
        target_field = pd.NA
        try:
            if isinstance(target_field_list, (str, float)):
                return target_field_list
            if not isinstance(target_field_list, list) and pd.isna(target_field_list):
                return target_field
            target_field = target_field_list[leg_index]
        except Exception as e:
            msg = (
                f"Error fetching {target_field_name} for leg_index:{leg_index},"
                f"order_id:{data.get(FixTagNames.ORDER_ID)}. file_url:"
                f"{data.get(S3_FILE_URL)}."
                f"Error:{e}"
            )
            logger.error(msg)
            auditor.add(msg)
        return target_field

    @staticmethod
    def get_field_escape_na(row: pd.Series, source_field: str, default: Any):
        if isinstance(row.get(source_field), list):
            return default
        return (
            default
            if pd.isna(row.get(source_field))
            else row.get(source_field, default)
        )
