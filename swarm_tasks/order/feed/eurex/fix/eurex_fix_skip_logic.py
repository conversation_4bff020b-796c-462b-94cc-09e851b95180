import pandas as pd
from prefect.engine import signals
from swarm.task.transform.base import TransformBaseTask


class SourceColumns:
    EXEC_TYPE = "ExecType"
    MSG_TYPE = "MsgType"
    ORDER_ID = "OrderID"
    POSS_DUP_FLAG = "PossDupFlag"
    TRD_MATCH_ID = "TrdMatchID"


class EurexFixSkipLogic(TransformBaseTask):
    def execute(self, source_frame: pd.DataFrame = None, **kwargs) -> pd.DataFrame:

        return self.process(source_frame=source_frame)

    @classmethod
    def process(cls, source_frame: pd.DataFrame = None) -> pd.DataFrame:

        if source_frame.empty:
            raise signals.SKIP("Empty source frame.")

        # Skip rows where 'PossDupFlag' == 'Y'
        poss_dup_flag_not_y = ~(
            source_frame.loc[:, SourceColumns.POSS_DUP_FLAG].str.fullmatch(
                "Y", case=False, na=False
            )
        )
        source_frame = source_frame.loc[poss_dup_flag_not_y, :]

        if source_frame.empty:
            raise signals.SKIP(
                f"Source frame empty after '{SourceColumns.POSS_DUP_FLAG}' != 'Y' query."
            )

        # For rows where 'OrderID' and 'TrdMatchID' match. Skip rows where 'MsgType' == '8' and 'ExecType' == 'F'
        order_id_and_trd_match_id_not_null = (
            source_frame.loc[:, SourceColumns.ORDER_ID].notnull()
            & source_frame.loc[:, SourceColumns.TRD_MATCH_ID].notnull()
        )

        duplicates = (
            source_frame.duplicated(
                [SourceColumns.ORDER_ID, SourceColumns.TRD_MATCH_ID], keep=False
            )
            & order_id_and_trd_match_id_not_null
        )

        non_duplicates_df = source_frame.loc[~duplicates, :]
        duplicates_df = source_frame.loc[duplicates, :]

        msg_ae_mask = duplicates_df.loc[:, SourceColumns.MSG_TYPE].str.fullmatch(
            "AE", case=False, na=False
        )

        duplicates_df_filtered = duplicates_df.loc[msg_ae_mask, :]

        target = pd.concat([non_duplicates_df, duplicates_df_filtered], axis=0)

        if target.empty:
            raise signals.SKIP(
                f"Source frame empty after '{SourceColumns.MSG_TYPE}' == '8' and '{SourceColumns.EXEC_TYPE}' == 'F' query."
            )

        return target
