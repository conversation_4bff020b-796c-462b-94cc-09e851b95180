import pandas as pd
from prefect.engine.signals import SKIP
from pydantic import Field
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.cloud.aws.s3.utils import read_csv_from_s3_download


class CsvColumnNames:
    LOOKUP_COLUMN = "INPUT_CODE"
    LOOKUP_DATA_COLUMN = "OUTPUT_MIC_CODE"


class Params(BaseParams):
    lookup_file_s3_key: str = Field(..., description="S3 key of the MIC lookup file")
    source_venue_column: str = Field(
        ..., description="Name of the venue column in the source frame"
    )
    source_ultimate_venue_column: str = Field(
        ..., description="Name of the ultimate venue column in the source frame"
    )
    target_venue_column: str = Field(..., description="Name of target venue column")
    target_ultimate_venue_column: str = Field(
        ..., description="Name of target ultimate venue column"
    )


class LookupMicCodesInS3(TransformBaseTask):
    """
    This task takes as input a source frame containing values for venue and ultimate venue,
    along with the s3 key of a lookup file.
    For each of the source venue and ultimate venue columns, it does a (case-insensitive)
    lookup based on INPUT_CODE in the lookup file, and fetches the value in OUTPUT_MIC_CODE.
    If there is a match, the OUTPUT_MIC_CODE is populated in the target venue attribute (and
    the target ultimate venue attribute) If there is no match, it uses the source venue
    and ultimate venue attributes to populate the target venue and ultimate venue attributes
    respectively.

    NOTE that the lookup column is expected to have unique values. If all values aren't
    unique, duplicate values are discarded (only the first occurrence is kept).
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:
        if (
            source_frame.empty
            or params.source_venue_column not in source_frame.columns
            or params.source_ultimate_venue_column not in source_frame.columns
        ):
            raise SKIP(
                "Task processing skipped as source frame was empty/source columns not found!"
            )

        target = pd.DataFrame(index=source_frame.index)
        target[params.target_venue_column] = pd.NA
        target[params.target_ultimate_venue_column] = pd.NA

        # Fetch the lookup table from S3
        s3_bucket = Settings.realm
        lookup_table = read_csv_from_s3_download(
            bucket=s3_bucket,
            key=params.lookup_file_s3_key,
            logger=self.logger,
        )
        if (
            lookup_table.empty
            or CsvColumnNames.LOOKUP_COLUMN not in lookup_table.columns
            or CsvColumnNames.LOOKUP_DATA_COLUMN not in lookup_table.columns
        ):
            self.logger.warning(
                "Lookup table empty/lookup columns not found. Returned source file"
                " values for venue and ultimate venue"
            )
            target[params.target_venue_column] = source_frame[
                params.source_venue_column
            ]
            target[params.target_ultimate_venue_column] = source_frame[
                params.source_ultimate_venue_column
            ]
            return target

        # Drop duplicates as we want to use lookup_table_no_duplicates as
        # a lookup table with index LOOKUP_COLUMN
        lookup_table = lookup_table.drop_duplicates(subset=CsvColumnNames.LOOKUP_COLUMN)
        lookup_table[CsvColumnNames.LOOKUP_COLUMN] = lookup_table[
            CsvColumnNames.LOOKUP_COLUMN
        ].str.lower()

        # Set index=CsvColumnNames.SYMBOL so we can do a lookup easily
        lookup_table = lookup_table.set_index(CsvColumnNames.LOOKUP_COLUMN, drop=True)
        lookup_map = lookup_table.squeeze()

        # Lookup venue in the lookup table
        venue_mics = self._get_output_mic_codes(
            df=source_frame,
            mic_code_map=lookup_map,
            source_column=params.source_venue_column,
        )
        ultimate_venue_mics = self._get_output_mic_codes(
            df=source_frame,
            mic_code_map=lookup_map,
            source_column=params.source_ultimate_venue_column,
        )
        # Lookup ultimate venue in the lookup table
        target[params.target_venue_column] = venue_mics
        target[params.target_ultimate_venue_column] = ultimate_venue_mics
        return target

    @staticmethod
    def _get_output_mic_codes(
        df: pd.DataFrame, mic_code_map: pd.Series, source_column: str
    ) -> pd.Series:
        """Maps source_column (venue/ultimate venue) to the output mic code
        ()in the s3 file by doing a lookup on CsvColumnNames.LOOKUP_COLUMN.
        Returns a Series with the output mic code if there is a successful match
        or the source_column value if there is no match.
        :param: df: data frame derived from the source_frame
        :type: df: pd.DataFrame
        :param: mic_code_map: Series with index=lookup column and data=Output MIC code
        :type: mic_code_map: pd.Series
        :param: source_column: Name of Venue/Ultimate venue column
        :type: params: str
        :returns: a Series containing the output mic if lookup was successful or the
                  source value if it was not
        :rtype: pd.Series
        """
        ids = (
            df.loc[:, source_column]
            .str.lower()
            .map(mic_code_map)
            .fillna(df[source_column])
        )
        return ids
