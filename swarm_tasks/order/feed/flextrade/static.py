from enum import Enum


class FileType(Enum):
    CXL = "CXL"
    EXE = "EXE"
    PRIM = "PRIM"
    PRIM_RPL = "PRIM_RPL"
    RPL = "RPL"


FILE_TYPE_DICT = {
    "#CXL": FileType.CXL.value,
    "#EXE": FileType.EXE.value,
    "#PRIM/RPL": FileType.PRIM_RPL.value,
}


class FlextradeSourceColumns:
    CXL_CXL_TIME = "cxl_cxl-time"
    CXL_CXL_ORDER_ID = "cxl_cxl-orderid"
    CXL_DESTINATION = "cxl_destination"
    CXL_PORTFOLIO = "cxl_portfolio"
    CXL_PORTFOLIO_SIDE = "cxl_portfolio-side"
    CXL_REFERENCE_ORDER_ID = "cxl_reference-orderid"
    CXL_SYMBOL = "cxl_symbol"
    EXE_BROKER_ORDER_ID = "exe_broker-orderid"
    EXE_DESTINATION = "exe_destination"
    EXE_EXEC_ID = "exe_exec-id"
    EXE_EXEC_PRICE = "exe_exec-price"
    EXE_EXEC_SHARES = "exe_exec-shares"
    EXE_ORDER_ID = "exe_order-id"
    EXE_PORTFOLIO = "exe_portfolio"
    EXE_PORTFOLIO_SIDE = "exe_portfolio-side"
    EXE_PRIM_ORDER_ID = "exe_prim-orderid"
    EXE_SYMBOL = "exe_symbol"
    EXE_STATUS = "exe_status"
    EXE_TRANSACTION_TIME = "exe_transaction-time"
    EXE_USER_FIX_TAG = "exe_userfixtag"
    EXE_USER_INITIALS = "exe_user-initials"
    PRIM_RPL_DESTINATION = "prim_rpl_destination"
    PRIM_RPL_FIX_TAGS = "prim_rpl_fixtags"
    PRIM_RPL_INCOMING_FIX_ORDER_ID_STRATEGY_ID = (
        "prim_rpl_incoming-fix-orderid/strategyid"
    )
    PRIM_RPL_ISIN = "prim_rpl_isin"
    PRIM_RPL_LIMIT_PRICE = "prim_rpl_limit-price"
    PRIM_RPL_ORDER_ID = "prim_rpl_order-id"
    PRIM_RPL_ORDERED_SHRS = "prim_rpl_ordered-shrs"
    PRIM_RPL_ORDER_STATUS = "prim_rpl_order-status"
    PRIM_RPL_ORDER_TIME = "prim_rpl_order-time"
    PRIM_RPL_PORTFOLIO = "prim_rpl_portfolio"
    PRIM_RPL_PORTFOLIO_SIDE = "prim_rpl_portfolio-side"
    PRIM_RPL_REFERENCE_ORDER_ID = "prim_rpl_reference-orderid"
    PRIM_RPL_SYMBOL = "prim_rpl_symbol"
    PRIM_RPL_USER_INITIALS = "prim_rpl_user-initials"


class FlextradeFixColumns:
    EXE_USER_FIX_TAG_CURRENCY = "exe_user_fix_tag_Currency"
    EXE_USER_FIX_TAG_LAST_MKT = "exe_user_fix_tag_LastMkt"
    EXE_USER_FIX_TAG_LEAVES_QTY = "exe_user_fix_tag_LeavesQty"
    EXE_USER_FIX_TAG_SECURITY_ID = "exe_user_fix_tag_SecurityID"
    EXE_USER_FIX_TAG_SECURITY_ID_SOURCE = "exe_user_fix_tag_SecurityIDSource"
    EXE_USER_FIX_TAG_TRANSACT_TIME = "exe_user_fix_tag_TransactTime"
    EXE_USER_FIX_TAG_SECURITY_TYPE = "exe_user_fix_tag_SecurityType"
    EXE_USER_FIX_TAG_SECURITY_ALT_ID = "exe_user_fix_tag_SecurityAltID"
    EXE_USER_FIX_TAG_SECURITY_ALT_ID_SOURCE = "exe_user_fix_tag_SecurityAltIDSource"
    EXE_USER_FIX_TAG_FF_20001 = "exe_user_fix_tag_ff_20001"
    PRIM_RPL_FIX_TAG_CURRENCY = "prim_rpl_fix_tag_Currency"


class FlextradeTempColumns:
    ASSET_CLASS = "__asset_class__"
    ASSET_CLASS_MAIN = "__asset_class_main__"
    DATE = "__date__"
    PLACEHOLDER = "placeholder"
    PRICE_CURRENCY = "__price_currency__"
    PRICE_REFERENCE_RIC = "__price_reference_ric__"
    EXE_TRANSACTION_TIME = "__exe_transaction_time__"
    EXE_FIX_TRANSACT_TIME = "__exe_fix_transact_time__"
    EXECUTING_ENTITY_WITH_LEI = "__executing_entity_with_lei__"
    IS_CREATED_THROUGH_FALLBACK = "__is_created_through_fallback__"
    TRADER_WITH_ID = "__trader_with_id__"

    # Instrument Columns
    INSTR_EXPIRY_DATE = "__instr_expiry_date__"
    INSTR_ISIN = "__instr_isin__"
    INSTR_RIC = "__instr_ric__"
    INSTR_SEDOL = "__instr_sedol__"
    INSTR_UNDERLYING_SYMBOL_RAW = "__instr_underlying_symbol_raw__"
    INSTR_UNDERLYING_SYMBOL_NO_SPL_CHARS = "__instr_underlying_symbol_no_spl__"
    INSTR_UNDERLYING_SYMBOL = "__instr_underlying_symbol__"
    INSTR_UNIQUE_IDENTIFIER = "__instr_unique_identifier__"


class FlexTradeDateFormats:
    FILENAME_DATE_FORMAT = "%Y%m%d"
    SOURCE_DATETIME_FORMAT = "%Y-%m-%d %H:%M:%s"


class FlextradeExeStatus(Enum):
    DONE = "DONE"
