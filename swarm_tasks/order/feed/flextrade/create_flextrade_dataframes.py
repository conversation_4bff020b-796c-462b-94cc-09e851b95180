import logging
import re
from datetime import datetime
from typing import Dict
from typing import List
from typing import <PERSON>Ret<PERSON>
from typing import Optional
from typing import Union

import pandas as pd
from prefect.engine import signals
from pydantic import Field
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.utils.frame_manipulation import add_missing_columns
from swarm.task.auditor import Auditor
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask

from swarm_tasks.order.feed.flextrade.static import FILE_TYPE_DICT
from swarm_tasks.order.feed.flextrade.static import FileType
from swarm_tasks.order.feed.flextrade.static import FlexTradeDateFormats
from swarm_tasks.order.feed.flextrade.static import FlextradeExeStatus
from swarm_tasks.order.feed.flextrade.static import FlextradeSourceColumns
from swarm_tasks.order.feed.flextrade.static import FlextradeTempColumns


FILE_DATE_REGEX = re.compile(r"(\d{4}(0[1-9]|1[012])(0[1-9]|1[0-9]|2[0-9]|3[0-1]))")


class Params(BaseParams):
    num_headers: int = Field(
        default=3,
        description="Number of headers in the file",
    )
    delimiter: str = Field(
        default=",",
        description="File delimiter",
    )
    file_type_column_name: str = Field(
        ...,
        description="Name of the first column in the file, which contains the file type",
    )
    prim_rpl_dataframe_columns: Optional[list] = Field(
        default=None,
        description="List of expected columns in the PRIM/RPL dataframe."
        " This is used to add missing columns as null. If remove_unknown_columns is True,"
        " any columns which are not in this list are removed from the output frame",
    )
    cxl_dataframe_columns: Optional[list] = Field(
        default=None,
        description="List of expected columns in the CXL dataframe."
        " This is used to add missing columns as null. If remove_unknown_columns is True,"
        " any columns which are not in this list are removed from the output frame",
    )
    exe_dataframe_columns: Optional[list] = Field(
        default=None,
        description="List of expected columns in the EXE dataframe."
        " This is used to add missing columns as null. If remove_unknown_columns is True,"
        " any columns which are not in this list are removed from the output frame",
    )
    remove_unknown_columns: bool = Field(
        default=True,
        description="True by default. If True, only the columns present in each of prim_rpl_dataframe_columns,"
        "cxl_dataframe_columns and exe_dataframe_columns are kept in the respective prim/rpl,"
        "cxl and exe data frames",
    )
    output_source_key_column: str = Field(
        ..., description="Name of the target column containing the original source key"
    )


class CreateFlextradeDataFrames(BaseTask):
    """
    This bespoke Flextrade task is used with a CSV file containing multiple headers and multiple types of
    data (the type of data is identified based on the value in the first column).
    It gets each individual header and safely links it to 3 separate dataframes created based on the value in
    the first column (PRIM&RPL/CXL/EXE).
    Then, it merges the dataframes together in various combinations, so they can be used in downstream
    tasks/flows.

    Finally, it applies the skip logic for PRIM/RPL based on the date in the file name. And it skips EXE records
    if their status is DONE.

    It returns a dictionary with the type of file as keys and the relevant dataframe as values.

    NOTE: Headers are all converted to lower case and spaces are removed
    """

    params_class = Params

    # Define class attributes for each of the three dataframes. These will be mutated in the class method
    prim_rpl_df = pd.DataFrame()
    cxl_df = pd.DataFrame()
    exe_df = pd.DataFrame()

    def execute(
        self,
        extractor_result: Union[ExtractPathResult, str] = None,
        params: Optional[Params] = None,
        **kwargs,
    ) -> Dict[FileType, pd.DataFrame]:
        return self.process(
            extractor_result=extractor_result,
            params=params,
            logger=self.logger,
            auditor=self.auditor,
        )

    @classmethod
    def process(
        cls,
        extractor_result: Union[ExtractPathResult, str],
        params: Optional[Params],
        logger: logging.Logger = logging.getLogger(__name__),
        auditor: Auditor = None,
    ) -> Dict[FileType, pd.DataFrame]:

        # Create a dict containing file type to
        file_header_dict = cls._get_all_headers(
            extractor_result=extractor_result, params=params
        )
        max_header_length = max(len(header) for header in file_header_dict.values())
        df = pd.read_csv(
            extractor_result.path,
            header=None,
            skiprows=range(params.num_headers),
            names=range(max_header_length + 2),
            dtype=str,
        )

        # The first column contains the type of file. Use this to split df into 3 dfs (based on
        # the 3 file types)

        cls.prim_rpl_df = df[
            df.loc[:, 0].str.fullmatch(
                f"{FileType.PRIM.value}|{FileType.RPL.value}", case=False
            )
        ].reset_index(drop=True)

        cls.cxl_df = df[
            df.loc[:, 0].str.fullmatch(FileType.CXL.value, case=False)
        ].reset_index(drop=True)
        cls.exe_df = df[
            df.loc[:, 0].str.fullmatch(FileType.EXE.value, case=False)
        ].reset_index(drop=True)

        # Add the correct headers to each df after adding extra column names according to the
        # number of columns which are mismatched
        cls._add_headers_to_data_frames(file_header_dict=file_header_dict)

        cls._get_final_prim_rpl_df(params=params, logger=logger)
        cls._get_final_exe_df(params=params, logger=logger, auditor=auditor)
        cls._get_final_cxl_df(params=params, logger=logger, auditor=auditor)

        # SKIP PRIM/RPL rows based on the date AFTER we have used data from PRIM/RPL to populate
        # CXL and EXE data frames.
        cls._prim_rpl_skip_logic(
            extractor_result=extractor_result, logger=logger, auditor=auditor
        )

        # Add source key to all 3 dfs
        for df in [cls.prim_rpl_df, cls.exe_df, cls.cxl_df]:
            df[params.output_source_key_column] = extractor_result.path.as_posix()

        return {
            FileType.CXL.value.lower(): cls.cxl_df,
            FileType.EXE.value.lower(): cls.exe_df,
            FileType.PRIM_RPL.value.lower(): cls.prim_rpl_df,
        }

    @staticmethod
    def _get_all_headers(
        extractor_result: ExtractPathResult, params: Params
    ) -> Dict[FileType, List[str]]:
        """Gets all 3 headers by reading the first 3 lines of the file. It then creates
        a dictionary with values from FileType as keys, and the respective headers as
        values.
        :param extractor_result: ExtractPathResult containing the input file
        :param params: Params instance
        :returns Dictionary with values from FileType as keys, and the respective headers as
                 values
        """
        try:
            with open(extractor_result.path, "r") as f:
                headers = [next(f) for _ in range(params.num_headers)]
        except FileNotFoundError as fnfe:
            raise signals.FAIL(f"File does not exist. Exception: {fnfe}")
        # Remove \n \r, and create a list of lists
        headers = [
            re.sub(r"\s+", "", header).split(params.delimiter) for header in headers
        ]

        file_header_dict = {}
        for header in headers:
            file_type = FILE_TYPE_DICT.get(header[0])
            file_header_dict[file_type] = [params.file_type_column_name] + header[1:]

        return file_header_dict

    @classmethod
    def _prim_rpl_skip_logic(
        cls,
        extractor_result: ExtractPathResult,
        logger: logging.Logger,
        auditor: Auditor,
    ) -> NoReturn:
        """Skips PRIM and RPL records where the file_date parsed from the file name is
        greater than prim_rpl.[order-time]

        :param extractor_result: ExtractPathResult from which we need to parse the date
        :param logger: Logger
        :param auditor: Auditor
        :return None, it mutates the class attribute cls.prim_rpl_df

        """

        file_date = FILE_DATE_REGEX.search(string=extractor_result.path.name).group(0)
        # Convert file_date to datetime.date
        file_date = datetime.strptime(
            file_date, FlexTradeDateFormats.FILENAME_DATE_FORMAT
        ).date()
        skip_mask = (
            pd.to_datetime(
                cls.prim_rpl_df[FlextradeSourceColumns.PRIM_RPL_ORDER_TIME]
            ).dt.date
            < file_date
        )

        num_records_skipped = skip_mask.sum()
        if num_records_skipped > 0:
            skip_message = f"{num_records_skipped} PRIM/RPL records were skipped because order-time < date in file name"
            logger.info(skip_message)
            auditor.add(skip_message)
        cls.prim_rpl_df = cls.prim_rpl_df.loc[~skip_mask].reset_index(drop=True)

    @classmethod
    def _get_final_prim_rpl_df(
        cls,
        params: Params,
        logger: logging.Logger,
    ) -> NoReturn:
        """
        Merges the EXE data frame to the PRIM/RPL data frame, adds missing columns
        and removes unknown columns

        :param params: Params instance
        :param logger: Logger
        :return: None, but mutates the cls.prim_rpl_df class attributes
        """
        # Merge EXE to PRIM. Make sure to get only those exe columns from prim_rpl_df which are
        # included in params.prim_rpl_dataframe_columns. There are multiple exe records with the same
        # order id, we need only the unique records

        exe_cols_in_prim_rpl_param = [
            col
            for col in params.prim_rpl_dataframe_columns
            if col.startswith(FileType.EXE.value.lower())
        ]
        cls.prim_rpl_df = cls.prim_rpl_df.merge(
            right=cls.exe_df[
                exe_cols_in_prim_rpl_param + [FlextradeSourceColumns.EXE_ORDER_ID]
            ].drop_duplicates(subset=[FlextradeSourceColumns.EXE_ORDER_ID]),
            how="left",
            left_on=FlextradeSourceColumns.PRIM_RPL_ORDER_ID,
            right_on=FlextradeSourceColumns.EXE_ORDER_ID,
        )
        # Log the no. of PRIM records which did not join to an EXE record
        no_exe_data_in_prim = (
            cls.prim_rpl_df.loc[:, FlextradeSourceColumns.EXE_ORDER_ID].isnull().sum()
        )
        logger.warning(
            f"{no_exe_data_in_prim} PRIM/RPL records did not join to any EXE records, and will"
            f" therefore not populate target fields which are fetched from EXE"
        )

        # Add missing columns
        cls.prim_rpl_df = add_missing_columns(
            dataframe=cls.prim_rpl_df,
            dataframe_columns=params.prim_rpl_dataframe_columns,
        )
        # Drop unknown columns
        if params.remove_unknown_columns:
            unknown_columns = set(cls.prim_rpl_df.columns) - set(
                params.prim_rpl_dataframe_columns
            )
            cls.prim_rpl_df = cls.prim_rpl_df.drop(list(unknown_columns), axis=1)

    @classmethod
    def _get_final_exe_df(
        cls,
        params: Params,
        logger: logging.Logger,
        auditor: Auditor,
    ):
        """
        Merges the exe_df to the prim_df, adds missing columns, removes unknown columns.

        NOTE that exe rows which DO NOT JOIN are skipped

        :param params: Params instance
        :param logger: Logger
        :param auditor: Auditor
        :return: None, but mutates the cls.exe_df class attributes

        """
        initial_exe_rows = cls.exe_df.shape[0]
        # Merge PRIM to EXE. Make sure to get only those prim/rpl columns from prim_rpl_df which are
        # included in params.exe_dataframe_columns

        prim_rpl_cols_in_exe_param = [
            col
            for col in params.exe_dataframe_columns
            if col.startswith(FileType.PRIM_RPL.value.lower())
        ]
        cls.exe_df = cls.exe_df.merge(
            right=cls.prim_rpl_df[
                prim_rpl_cols_in_exe_param + [FlextradeSourceColumns.PRIM_RPL_ORDER_ID]
            ],
            how="inner",
            left_on=FlextradeSourceColumns.EXE_ORDER_ID,
            right_on=FlextradeSourceColumns.PRIM_RPL_ORDER_ID,
        )

        # Skip logic for EXE
        done_mask = cls.exe_df[FlextradeSourceColumns.EXE_STATUS].str.fullmatch(
            FlextradeExeStatus.DONE.value, case=False, na=False
        )
        cls.exe_df = cls.exe_df.loc[~done_mask, :]

        # Log and audit number of skipped records
        skipped_records = initial_exe_rows - cls.exe_df.shape[0]
        if skipped_records > 0:
            audit_message = (
                f"{skipped_records} EXE records were skipped because they could"
                f" not be joined to a PRIM/RPL record or had execution status=DONE"
            )
            logger.info(audit_message)
            auditor.add(audit_message)

        # Add missing columns
        cls.exe_df = add_missing_columns(
            dataframe=cls.exe_df, dataframe_columns=params.cxl_dataframe_columns
        )
        # Drop unknown columns
        if params.remove_unknown_columns:
            unknown_columns = set(cls.exe_df.columns) - set(
                params.exe_dataframe_columns
            )
            cls.exe_df = cls.exe_df.drop(list(unknown_columns), axis=1)

        cls.exe_df = cls.exe_df.reset_index(drop=True)

    @classmethod
    def _get_final_cxl_df(
        cls,
        params: Params,
        logger: logging.Logger,
        auditor: Auditor,
    ):
        """
        Merges the prim_rpl_df and the exe_df to the cxl_df, adds missing columns and removes
        unknown columns

        NOTE that cxl rows which DO NOT JOIN are skipped

        :param params: Params instance
        :param logger: Logger
        :param auditor: Auditor
        :return: None, but mutates the cls.cxl_df class attributes
        """
        initial_cxl_rows = cls.cxl_df.shape[0]
        # Merge PRIM to CXL. Make sure to get only those prim/rpl columns from prim_rpl_df which are
        # included in params.cxl_dataframe_columns

        prim_rpl_cols_in_cxl_param = [
            col
            for col in params.cxl_dataframe_columns
            if col.startswith(FileType.PRIM_RPL.value.lower())
        ]
        cls.cxl_df = cls.cxl_df.merge(
            right=cls.prim_rpl_df[
                prim_rpl_cols_in_cxl_param + [FlextradeSourceColumns.PRIM_RPL_ORDER_ID]
            ],
            how="inner",
            left_on=FlextradeSourceColumns.CXL_REFERENCE_ORDER_ID,
            right_on=FlextradeSourceColumns.PRIM_RPL_ORDER_ID,
        )
        # Log and audit number of skipped records
        skipped_records = initial_cxl_rows - cls.cxl_df.shape[0]
        if skipped_records > 0:
            audit_message = (
                f"{initial_cxl_rows - cls.cxl_df.shape[0]} CXL records were skipped because they could"
                f" not be joined to a PRIM/RPL record"
            )
            logger.info(audit_message)
            auditor.add(audit_message)
        # Merge EXE to CXL. Make sure to get only those exe columns from exe_df which are
        # included in params.cxl_dataframe_columns
        exe_cols_in_cxl_param = [
            col
            for col in params.cxl_dataframe_columns
            if col.startswith(FileType.EXE.value.lower())
        ]
        cls.cxl_df = cls.cxl_df.merge(
            right=cls.exe_df[
                exe_cols_in_cxl_param + [FlextradeSourceColumns.EXE_ORDER_ID]
            ].drop_duplicates(subset=[FlextradeSourceColumns.EXE_ORDER_ID]),
            how="left",
            left_on=FlextradeSourceColumns.CXL_REFERENCE_ORDER_ID,
            right_on=FlextradeSourceColumns.EXE_ORDER_ID,
        )

        # Add missing columns
        cls.cxl_df = add_missing_columns(
            dataframe=cls.cxl_df, dataframe_columns=params.cxl_dataframe_columns
        )
        # Drop unknown columns
        if params.remove_unknown_columns:
            unknown_columns = set(cls.cxl_df.columns) - set(
                params.cxl_dataframe_columns
            )
            cls.cxl_df = cls.cxl_df.drop(list(unknown_columns), axis=1)

        cls.cxl_df = cls.cxl_df.reset_index(drop=True)

    @classmethod
    def _add_headers_to_data_frames(cls, file_header_dict: dict) -> NoReturn:
        """
        Adds appropriate headers to all 3 data frames: PRIM/RPL, EXE and CXL. Headers
        are added based on the previously constructed file_header_dict.

        :param file_header_dict: Dictionary containing headers for each key
        :return: None, mutates class attributes
        """
        cls._add_header_to_df(
            df=cls.prim_rpl_df,
            header=file_header_dict.get(FileType.PRIM_RPL.value),
            file_type=FileType.PRIM_RPL,
        )

        cls._add_header_to_df(
            df=cls.cxl_df,
            header=file_header_dict.get(FileType.CXL.value),
            file_type=FileType.CXL,
        )
        cls._add_header_to_df(
            df=cls.exe_df,
            header=file_header_dict.get(FileType.EXE.value),
            file_type=FileType.EXE,
        )

    @staticmethod
    def _add_header_to_df(
        df: pd.DataFrame, header: list, file_type: FileType
    ) -> NoReturn:
        """This method adds the header in the 'header' argument to the data frame in the 'df'
        argument. All the column names are prefixed by the file_type.
        It adds extra placeholder columns when the number of elements in the header list is less than
        the number of columns in the dataframe. By design, the data frame will ALWAYS have
        more columns than the header has elements. Adding placeholder columns is necessary as otherwise,
        there will be a column mismatch while assigning the header. These placeholder columns are
        subsequently removed.
        NOTE! This method does not return anything, it mutates the dataframe df by adding
        a column header to it

        NOTE 2: Column names are lower-cased, and spaces are removed

        :param df: DataFrame containing data, but no header
        :param header: header which needs to be added to df
        :param file_type: Type of file (PRIM/RPL/EXE/CXL)
        :returns None, but mutates df by adding header to it.
        """
        # Add file_type prefix and lower-case all column names, remove spaces from them
        header = [
            f"{file_type.value.lower()}_{h.lower().replace(' ', '')}" for h in header
        ]
        num_cols_to_add = df.shape[1] - len(header)
        placeholder_cols = [
            f"{FlextradeTempColumns.PLACEHOLDER}_{idx}"
            for idx in range(num_cols_to_add)
        ]
        header += placeholder_cols
        df.columns = header
        # Drop placeholder columns
        to_drop_placeholder_cols = [
            col
            for col in df.columns
            if col.startswith(FlextradeTempColumns.PLACEHOLDER)
        ]
        df.drop(to_drop_placeholder_cols, axis=1, inplace=True)
