import os

from swarm.conf import Settings


class InstrumentFields:
    EXT_PRICE_NOTATION = "ext.priceNotation"
    EXT_QTY_NOTATION = "ext.quantityNotation"
    DERIVATIVE_LEGS = "derivative.legs"
    DERIVATIVE_USER_DEFINED_SPREAD = "derivative.isUserDefinedSpread"
    INSTRUMENT_FULL_NAME = "instrumentFullName"
    LEG_OPTION_DELTA = "legOptionDelta"
    LEG_RATIO_QTY = "legRatioQty"
    LEG_SECURITY_ID = "legSecurityId"
    LEG_SIDE = "legSide"
    NOTIONAL_CURRENCY = "notionalCurrency1"
    PRICE_DISPLAY_FACTOR = "derivative.priceDisplayFactor"
    TIMESTAMP = "&timestamp"
    VENUE = "venue.tradingVenue"


class ExchangeVenues:
    CME = "cme"
    EUREX = "eurex"
    ICE = "ice"
    ICE_L = "ice_l"
    ICE_POF = "ice_pof"
    XLME = "xlme"

    CME_VENUES = [
        "CBCM",
        "DUMX",
        "GLBX",
        "MGCB",
        "NYUM",
        "XCBT",
        "XCEC",
        "XCME",
        "XFXS",
        "XKLS",
        "XMGE",
        "XNYM",
    ]

    ICE_VENUES = [
        "IEPA",
        "IFAD",
        "IFED",
        "IFEU",
        "IFLL",
        "IFLO",
        "IFUS",
        "NDCM",
        "NDEX",
        "NDXS",
    ]

    EUREX_VENUES = ["XEUR", "XEEE"]

    XLME_VENUES = ["XLME"]

    EXCHANGE_VENUE_MAP = {
        CME: CME_VENUES,
        EUREX: EUREX_VENUES,
        ICE: ICE_VENUES,
        ICE_L: ICE_VENUES,
        ICE_POF: ICE_VENUES,
        XLME: XLME_VENUES,
    }


RESOURCE_TYPE = (
    "tenant-data"
    if bool(int(os.getenv("USE_TENANT_DATA_IN_VDI", "0")))
    else "reference-data"
)
VENUE_DIRECT_INSTRUMENT = (
    f"{Settings.tenant}-instrument-alias"
    if bool(int(os.getenv("USE_TENANT_DATA_IN_VDI", "0")))
    else ".venueDirectInstrument"
)
