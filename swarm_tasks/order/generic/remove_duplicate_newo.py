import pandas as pd
from se_trades_tasks.order.remove_duplicates.remove_duplicate_newo import (
    Params as GenericParams,
)
from se_trades_tasks.order.remove_duplicates.remove_duplicate_newo import (
    run_remove_duplicate_newo,
)
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class Resources(BaseResources):
    es_client_key: str


class RemoveDuplicateNEWO(TransformBaseTask):
    """This Task removes duplicate NEWO records as follows:

    1. It uses the params.newo_in_file_col boolean column column (which
    indicates whether the record was actually a NEWO in the original source file)
    to make sure the original NEWO record for a given order id is retained and not
    the 'synthetic' NEWO records created for PARF/FILL/other status OrderState records.
    This is done by sorting the data frame based on newo_in_file_col before
    removing duplicate NEWO records.

    2. It keeps only synthetic NEWO Order records which are not already in Elasticsearch.
    In other words, it discards any synthetic NEWO records which are in Elasticsearch

    NOTE: A 'synthetic' NEWO record is an Order record created corresponding to OrderState
    records. These synthetic NEWO Order records are created upstream to avoid the situation
    where we have an OrderState in the database without a parent Order.
    """

    params_class = Params
    resources_class = Resources

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: Resources = None,
        **kwargs,
    ) -> pd.DataFrame:
        if source_frame.empty:
            return source_frame

        return run_remove_duplicate_newo(
            source_frame=source_frame,
            params=params,
            tenant=Settings.tenant,
            es_client=self.clients.get(resources.es_client_key),
        )
