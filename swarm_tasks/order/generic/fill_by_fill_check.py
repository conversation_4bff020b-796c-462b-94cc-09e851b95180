from typing import Optional

import pandas as pd
from pydantic import Field
from pydantic import validator
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.order.generic.utils import replace_parfs_with_flat_average


class Params(BaseParams):
    fill_by_fill_flag: bool = Field(
        ...,
        description="This param enables the processing of Average Execution Data instead"
        " of Fill by Fill. When True, records are ingested as usual, wWhen False, "
        "all PARF/FILL per order id will be merged into a single record.",
    )
    initial_quantity: Optional[str] = Field(
        None, description="Initial value of asset quantity in the order"
    )
    remaining_quantity: Optional[str] = Field(
        None, description="Asset quantity left to trade after the transaction"
    )
    fill_quantity: Optional[str] = Field(
        None, description="Assets quantity traded in the transaction"
    )
    order_id: Optional[str] = Field(None, description="Order ID")
    execution_price: Optional[str] = Field(
        None, description="Asset price executed by the transaction"
    )
    order_status: Optional[str] = Field(None, description="Order status")
    trading_date_time: Optional[str] = Field(
        None, description="Datetime when the transaction were executed"
    )
    route_id: Optional[str] = Field(None, description="Route ID (if absent, Order ID)")

    @validator(
        "initial_quantity",
        "remaining_quantity",
        "fill_quantity",
        "order_id",
        "execution_price",
        "order_status",
        "trading_date_time",
        "route_id",
        pre=False,
        always=True,
    )
    def validate_attributes(cls, v, values, **kwargs):
        """Validate all Params attributes: if ´fill_by_fill_flag´ is True all
        attributes are optional, if it is False all attributes are required"""
        fill_by_fill_attr = "fill_by_fill_flag"
        if not values.get(fill_by_fill_attr, True) and (v is None or v == ""):
            raise ValueError(
                f"If ´{fill_by_fill_attr}´ is ´False´, ´{kwargs['field'].name}´ must "
                "have a valid value."
            )

        return v


class FillByFillCheck(TransformBaseTask):
    """
    This task reduce the volume of data converting all FILLS/PARF into a single record
    if fill_by_fill_flag is False.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        if not params.fill_by_fill_flag:
            source_frame = replace_parfs_with_flat_average(
                df=source_frame,
                execution_price_col=params.execution_price,
                fill_quantity_col=params.fill_quantity,
                order_id_col=params.order_id,
                route_id_col=params.route_id,
                order_status_temp_col=params.order_status,
                trading_date_time_temp_col=params.trading_date_time,
                clean_temp_column=True,
            )
            source_frame[params.remaining_quantity] = source_frame[
                params.initial_quantity
            ].astype(float) - pd.to_numeric(
                source_frame[params.fill_quantity], errors="coerce"
            ).fillna(
                0
            )

        return source_frame
