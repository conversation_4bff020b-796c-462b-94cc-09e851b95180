from typing import Optional

import pandas as pd
from prefect import context
from pydantic import Field
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from swarm.task.auditor import Auditor
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.order.generic.static import InstrumentFields


class Params(BaseParams):
    security_id_col: str = Field(
        default="SecurityID",
        description="Security ID Column",
    )
    exchange_symbol_col: str = Field(
        default="ext.exchangeSymbolLocal",
        description="exchangeSymbolLocal of the instrument in srp",
    )
    instruments: str = Field(
        default="instrumentDetails.instrument",
        description="Name of the column containing all the instruents details",
    )
    trade_date: str = Field(..., description="column having date of the trade")
    trade_date_format: str = Field(
        default="%Y-%m-%d", description="format of the trade date"
    )


class ConvertMultilegTradesToSingleLeg(TransformBaseTask):
    """
    This tasks breaks down the multileg trades into individual legs so that
    downstream these can be transformed as normal single leg trades.
    In addition to create single leg trades from the source multileg ones,
    this task also updates some of the field mappings as per the logic mentioned here:
    https://steeleye.atlassian.net/wiki/spaces/IN/pages/2126446611/Order+CME+Drop+Copy+FIX#Step-3---Split-Strategy-into-multiple-individual-legs
    """

    params_class = Params

    def execute(
        self, source_frame: pd.DataFrame = None, params: Params = None, **kwargs
    ) -> pd.DataFrame:
        return self.process(
            source_frame=source_frame,
            params=params,
            logger=self.logger,
            auditor=self.auditor,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        logger=context.get("logger"),
        auditor: Auditor = None,
    ):
        if source_frame.empty:
            return pd.DataFrame()

        instruments_exist = source_frame.loc[:, params.instruments].notnull()
        if (~instruments_exist).any():
            instrument_na_order_list = source_frame.loc[
                ~instruments_exist, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE
            ].tolist()
            instrument_na_indices = source_frame.loc[~instruments_exist].index.tolist()
            msg = (
                f"[!] Skipping rows where instruments not found...\n"
                f"[!] Count of rows skipped: "
                f"[!] {len(source_frame.loc[~instruments_exist])}.\n"
                f"[!] Source Orders: {set(instrument_na_order_list)}.\n"
                f"[!] Source Indices: {instrument_na_indices}"
            )
            audit_ctx = {"error_count": len(source_frame.loc[~instruments_exist])}
            auditor.add(message=msg, ctx=audit_ctx)
            logger.warning(msg)
        single_leg_records = list()

        source_frame.loc[instruments_exist].apply(
            lambda row: cls.explode_multileg_trades_apply(
                row=row, new_records=single_leg_records, params=params
            ),
            axis=1,
        )
        single_leg_df = pd.DataFrame(single_leg_records).reset_index(drop=True)
        return single_leg_df

    @classmethod
    def explode_multileg_trades_apply(
        cls, row: pd.Series, new_records: list, params: Params = None
    ) -> Optional:
        """
        This method explodes a multileg trade into single legs. It loops over each leg
        found from the instrument of the trade and creates single leg trades for each of them.
        :param row: each multileg record alongwith the linked instruments
        :param new_records: the new_records list that this method will append a new record to
        :param params: the params passed to this task
        :return: returns a new df with each trade being converted to single leg
        """
        if InstrumentFields.DERIVATIVE_LEGS not in row[
            params.instruments
        ] or not isinstance(
            row[params.instruments].get(InstrumentFields.DERIVATIVE_LEGS), list
        ):
            return
        for leg in row[params.instruments].get(InstrumentFields.DERIVATIVE_LEGS):
            single_leg_record = row.copy()
            del single_leg_record[params.instruments]
            if leg.get(InstrumentFields.LEG_SECURITY_ID) is None:
                continue
            cls.update_id_fields(
                record=single_leg_record,
                parent_security_id=single_leg_record[params.security_id_col],
                leg_security_id=leg.get(InstrumentFields.LEG_SECURITY_ID),
            )
            single_leg_record[params.security_id_col] = leg.get(
                InstrumentFields.LEG_SECURITY_ID
            )
            leg_ratio = float(
                leg.get(InstrumentFields.LEG_OPTION_DELTA)
                or leg.get(InstrumentFields.LEG_RATIO_QTY)
                or 1
            )
            initial_quantity = cls.get_field_if_exists(
                record=row,
                field=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
            )
            single_leg_record[OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY] = (
                pd.NA
                if pd.isna(initial_quantity)
                else int(initial_quantity * leg_ratio)
            )
            remaining_quantity = cls.get_field_if_exists(
                record=row,
                field=OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
            )
            single_leg_record[OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY] = (
                pd.NA
                if pd.isna(remaining_quantity)
                else int(remaining_quantity * leg_ratio)
            )
            cls.update_buy_sell_fields(
                single_leg_record, leg_buy_sell=leg.get(InstrumentFields.LEG_SIDE)
            )
            new_records.append(single_leg_record.copy())

    @classmethod
    def update_buy_sell_fields(cls, record: pd.Series, leg_buy_sell: str):
        """
        Updates all the buy_sell fields. This is done based on the
        individual leg buysell extracted from the multileg trade's instrument. The buysell fields
        updated are:
            - _order.buySell
            - _orderState.buySell
            - executionDetails.buySellIndicator
            - transactionDetails.buySellIndicator
        The logic can be found here:
        https://steeleye.atlassian.net/wiki/spaces/IN/pages/2126446611/Order+CME+Drop+Copy+FIX#Step-3---Split-Strategy-into-multiple-individual-legs
        :param record: single leg record to update
        :param leg_buy_sell: buy_sell value extracted from instrument for the resp leg
        :return: None (Updates the record itslelf)
        """
        ml_buy_sell = record.get(
            OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR, pd.NA
        )
        record[
            add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.BUY_SELL)
        ] = cls.flip_buy_sell_if_ml_sell(
            ml_buy_sell=ml_buy_sell, leg_buy_sell=leg_buy_sell
        )
        record[
            add_prefix(prefix=ModelPrefix.ORDER_STATE, attribute=OrderColumns.BUY_SELL)
        ] = cls.flip_buy_sell_if_ml_sell(
            ml_buy_sell=ml_buy_sell, leg_buy_sell=leg_buy_sell
        )
        record[
            OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
        ] = cls.flip_buy_sell_if_ml_sell(
            ml_buy_sell=ml_buy_sell, leg_buy_sell=leg_buy_sell
        )
        record[
            OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
        ] = cls.flip_buy_sell_if_ml_sell(
            ml_buy_sell=ml_buy_sell, leg_buy_sell=leg_buy_sell
        )

    @staticmethod
    def flip_buy_sell_if_ml_sell(ml_buy_sell: str, leg_buy_sell: str) -> str:
        """
        The leg buy_sell value extracted for the resp legs in the instruments
        needs to be flipped if the ML buy_sell val is SELL. Otherwise the extracted
        buy_sell value can be used directly. This method implements the flip logic if
        required.
        :param ml_buy_sell:
        :param leg_buy_sell:
        :return:
        """
        if pd.isna(ml_buy_sell):
            return ml_buy_sell
        elif ml_buy_sell == BuySellIndicator.BUYI.value:
            return leg_buy_sell
        else:
            return (
                BuySellIndicator.SELL.value
                if leg_buy_sell == BuySellIndicator.BUYI.value
                else BuySellIndicator.BUYI.value
            )

    @staticmethod
    def update_id_fields(
        record: pd.Series, parent_security_id: str, leg_security_id: str
    ):
        """
        The ids for the converted single leg orders are changed based on the SecurityID
        extracted for each legs. This method updates the following id fields:
            - orderIdentifiers.orderIdCode
            - _order.id
            - _orderState.id

        :param record: the record to be updated
        :param parent_security_id: the securityID of the actual ML trade
        :param leg_security_id: SecurityID extract for the leg from srp
        :return: None (updates the record itself)
        """
        replaced_id = record[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE].replace(
            parent_security_id, leg_security_id
        )
        record[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE] = replaced_id
        record[
            OrderColumns.ORDER_IDENTIFIERS_TRADING_VENUE_TRANSACTION_ID_CODE
        ] = replaced_id
        record[
            add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID)
        ] = replaced_id
        record[
            add_prefix(prefix=ModelPrefix.ORDER_STATE, attribute=OrderColumns.ID)
        ] = replaced_id

    @staticmethod
    def get_field_if_exists(record: pd.Series, field: str) -> Optional[int]:
        if field in record:
            if pd.isna(record[field]):
                return pd.NA
            return int(record[field])
        else:
            return pd.NA
