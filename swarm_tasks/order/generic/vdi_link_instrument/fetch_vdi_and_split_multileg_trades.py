import logging
from typing import List
from typing import Optional

import numpy as np
import pandas as pd
from prefect import context
from pydantic import Field
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import MultiLegReportingType
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from swarm.task.base import Auditor
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.order.generic.fetch_instruments import FetchInstruments
from swarm_tasks.order.generic.fetch_instruments import Params as ParamsFetchInstruments
from swarm_tasks.order.generic.static import InstrumentFields


class Params(BaseParams):
    security_id_col: str = Field(
        default="SecurityID",
        description="Security ID Column",
    )
    exchange_symbol_col: str = Field(
        default="ext.exchangeSymbolLocal",
        description="exchangeSymbolLocal of the instrument in srp",
    )
    trade_date: str = Field(..., description="column having date of the trade")
    trade_date_format: str = Field(
        default="%Y-%m-%d", description="format of the trade date"
    )
    exchange: Optional[str] = Field(
        default=None,
        description="Exchange for which instruments are to be fetcheg, e.g. CME, EUREX etc.",
    )
    exchange_venue_col: Optional[str] = Field(
        default=None,
        description="For feeds like TT where the trades"
        "which has trades from multiple exchanges, each row must represent the exchange venue",
    )
    instrument = Field(
        default="instrumentDetails.instrument",
        description="column having date of the trade",
    )
    audit_and_remove_missing_instruments: bool = Field(
        default=True,
        description="If True, audits and removes orders with missing" "instruments",
    )
    convert_buysell_from_enum_to_number: bool = Field(
        default=False,
        description="If True, converts buySell values from the instrument from BUYI"
        "to 1 and SELL to 2. This applies to the _order.buySell and"
        "_orderState.buySell columns",
    )
    add_multi_leg_reporting_type: bool = Field(
        default=False,
        description="If True, adds the multiLegReportingType schema column."
        "NOTE that if the Multi-leg reporting type is already populated"
        "before this task, it will NOT be overwritten!",
    )


class InstrumentDetailsField:
    CFI_CATEGORY = "cfiCategory"


class MultiLegFields:
    LEG_RATIO_QTY = "legRatioQty"
    LEG_SIDE = "legSide"
    LEG_SECURITY_ID = "legSecurityId"
    LEG_OPTION_DELTA = "legOptionDelta"


class TempColumns:
    MULTILEG_INDICATOR = "__is_multileg__"
    LEGS_SERIES = "__legs_series__"
    ORIG_SECURITY_ID = "__orig_security_id__"
    OPTION_DELTA_VALUE = "__option_delta_value__"


class FetchVDIAndSplitMultiLegTrades(TransformBaseTask):
    """
    This task FetchVDIAndSplitMultiLegTrades is responding for fetching VenueDirectInstruments
    for fix trades.
    The task is responsible for the following:
        - Fetch VDI from SRP using the FetchInstruments task
        - Recursively look for instruments, after splitting the multileg into
          single leg orders. This is to cover multileg inside multileg sceanrio (explained
          with illustration later)
        - For each multileg trade, update the order_id fields after splitting them into
          single legs. The OrderID usually has an element representing the symbol. The
          symbol part of the order ID is replaced with the newly fetched symbol for each single
          leg
        - The initialOrderQty is multiplied with legOptionDelta/legRatioQty if present.
        - Flip buySell if required based on the order's side and the flip on each level
          based on the parents' leg side.

    Example of a multileg within a multileg
    6814592 (multileg order with 2 legs)
        - 5652882 (again a multileg with 3 legs)
            - 5652854
            - 5652856
            - 5652857
        - 5652884 (again a multileg with 3 legs)
            - 5652860
            - 5652862
            - 5652864
    """

    params_class = Params

    def execute(
        self, source_frame: pd.DataFrame = None, params: Params = None, **kwargs
    ):
        return self.process(
            source_frame=source_frame,
            params=params,
            logger=self.logger,
            auditor=self.auditor,
        )

    def process(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        logger=context.get("logger"),
        auditor: Auditor = None,
    ):
        if source_frame.empty:
            return pd.DataFrame()

        # A copy is created to avoid changing the source_frame
        self.target_df = source_frame.copy()
        self.logger.info(
            f"Number of input rows before splitting multilegs: {source_frame.shape[0]}"
        )
        # Create a new col to retain the original SecurityID (symbol). This is done
        # so that finally we can use it to determine if the trade is a multileg or not
        self.target_df.loc[:, TempColumns.ORIG_SECURITY_ID] = self.target_df.loc[
            :, params.security_id_col
        ]
        # Fetch Instruments recursively
        self.recursively_lookup_if_user_defined_spread(
            params=params,
            fetch_instument_params=self._get_fetch_instruments_params(params=params),
        )

        # Add legOptionDelta for future legs to support the quantity calculation
        future_mask = self.target_df[params.instrument].apply(self._is_future)
        self.target_df[future_mask].apply(self._add_option_delta_value, axis=1)

        # Check which orders are multileg, based on whether the new security id is same as
        # original security id. The fillna '0' is only applied on-flight so that if
        # security ids were null, they're not marked as multilegs. This is beacuse
        # null values aren't equals for pandas.
        self.target_df.loc[:, TempColumns.MULTILEG_INDICATOR] = self.target_df.loc[
            :, params.security_id_col
        ].fillna("0") != self.target_df.loc[:, TempColumns.ORIG_SECURITY_ID].fillna("0")
        if any(self.target_df.loc[:, TempColumns.MULTILEG_INDICATOR]):
            self._update_order_ids(
                multileg_mask=self.target_df.loc[:, TempColumns.MULTILEG_INDICATOR],
                params=params,
            )
            self._update_order_qty(
                multileg_mask=self.target_df.loc[:, TempColumns.MULTILEG_INDICATOR],
            )

        # Add multi-lge reporting type schema field IF the param add_multi_leg_reporting_type
        # is passed, and OrderColumns.MULTI_LEG_REPORTING_TYPE is not present. If the
        # OrderColumns.MULTI_LEG_REPORTING_TYPE is already present, it will NOT be overwritten.
        if (
            params.add_multi_leg_reporting_type
            and OrderColumns.MULTI_LEG_REPORTING_TYPE not in self.target_df.columns
        ):
            multi_leg_mask = self.target_df.loc[:, TempColumns.MULTILEG_INDICATOR]
            self.target_df.loc[
                multi_leg_mask, OrderColumns.MULTI_LEG_REPORTING_TYPE
            ] = MultiLegReportingType.SINGLE_LEG_OF_MULTI_LEG.value
            self.target_df.loc[
                ~multi_leg_mask, OrderColumns.MULTI_LEG_REPORTING_TYPE
            ] = MultiLegReportingType.OUTRIGHT.value

        temp_columns_added = [
            TempColumns.ORIG_SECURITY_ID,
            TempColumns.LEGS_SERIES,
            params.security_id_col,
            TempColumns.MULTILEG_INDICATOR,
            TempColumns.OPTION_DELTA_VALUE,
        ]

        self.target_df = self.target_df.drop(
            temp_columns_added, axis=1, errors="ignore"
        )

        if params.audit_and_remove_missing_instruments:
            self._audit_and_remove_missing_instruments(
                auditor=auditor, logger=logger, params=params
            )
        self.logger.info(
            f"Number of input rows post splitting multilegs: {self.target_df.shape[0]}"
        )
        return self.target_df

    def _is_future(self, instrument_details):
        """Identify if the instrument is a Future based on th cfiCategory"""
        if not isinstance(instrument_details, dict):
            return False

        cfi_category = instrument_details.get(InstrumentDetailsField.CFI_CATEGORY, "")
        return cfi_category.lower() == "futures"

    def _audit_and_remove_missing_instruments(
        self, auditor: Auditor, logger: logging.Logger, params: Params
    ):
        """Audits, logs and removes orders with missing instrument"""
        missing_instruments = self.target_df.loc[:, params.instrument].isnull()
        if (missing_instruments).any():
            instrument_na_order_list = self.target_df.loc[
                missing_instruments, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE
            ].tolist()
            instrument_na_indices = self.target_df.loc[
                missing_instruments
            ].index.tolist()
            msg = (
                f"[!] Skipping rows where instruments not found...\n"
                f"[!] Count of rows skipped: "
                f"[!] {len(self.target_df.loc[missing_instruments])}.\n"
                f"[!] Source Orders: {set(instrument_na_order_list)}.\n"
                f"[!] Source Indices: {instrument_na_indices}"
            )
            audit_ctx = {"error_count": len(self.target_df.loc[missing_instruments])}
            auditor.add(message=msg, ctx=audit_ctx)
            logger.warning(msg)
        self.target_df = self.target_df.loc[~missing_instruments].reset_index(drop=True)

    def recursively_lookup_if_user_defined_spread(
        self, params: Params, fetch_instument_params: ParamsFetchInstruments
    ):
        """
        This method fetches instruments recursively till there are no multileg instrument
        in the dataframe
        """
        if TempColumns.MULTILEG_INDICATOR not in self.target_df:
            # Initially setting everything to True, since we want to fetch instruments for
            # all the rows
            should_fetch_instrument = pd.Series(index=self.target_df.index, data=True)
        else:
            should_fetch_instrument = self.target_df.loc[
                :, TempColumns.MULTILEG_INDICATOR
            ].fillna(False)

        self.target_df.loc[
            should_fetch_instrument, [params.instrument, TempColumns.MULTILEG_INDICATOR]
        ] = FetchInstruments.process(
            source_frame=self.target_df.loc[should_fetch_instrument],
            params=fetch_instument_params,
        )

        multileg_mask = self.target_df.loc[:, TempColumns.MULTILEG_INDICATOR].fillna(
            False
        )

        if not any(multileg_mask):
            return

        self.target_df.loc[multileg_mask, TempColumns.LEGS_SERIES] = self.target_df.loc[
            multileg_mask, params.instrument
        ].str.get(InstrumentFields.DERIVATIVE_LEGS)

        self.target_df.loc[:, TempColumns.LEGS_SERIES] = self.target_df.loc[
            :, TempColumns.LEGS_SERIES
        ].apply(lambda x: [x] if not isinstance(x, list) else x)

        self.target_df.loc[
            multileg_mask, TempColumns.OPTION_DELTA_VALUE
        ] = self.target_df.loc[multileg_mask, TempColumns.LEGS_SERIES].apply(
            self._get_option_delta_value
        )
        self.target_df = self.target_df.explode(TempColumns.LEGS_SERIES).reset_index(
            drop=True
        )

        new_multileg_mask = self.target_df.loc[
            :, TempColumns.MULTILEG_INDICATOR
        ].fillna(False)
        new_security_ids = self.target_df.loc[
            new_multileg_mask, TempColumns.LEGS_SERIES
        ].str.get(MultiLegFields.LEG_SECURITY_ID)
        # If parent is present as one of the child legs in the multileg instrument, it can lead
        # to an infinite loop. So, discard those and set them as not multileg
        new_multileg_mask = (
            self.target_df.loc[new_multileg_mask, params.security_id_col]
            != new_security_ids
        ) & new_multileg_mask

        self.target_df.loc[new_multileg_mask, params.security_id_col] = new_security_ids

        self._flip_buy_sell_indicator(params=params)

        return self.recursively_lookup_if_user_defined_spread(
            params=params, fetch_instument_params=fetch_instument_params
        )

    def _add_option_delta_value(self, row):
        """Add the optionDeltaValue kept on OPTION_DELTA_VALUE to the leg details"""
        if TempColumns.LEGS_SERIES in row and isinstance(
            row[TempColumns.LEGS_SERIES], dict
        ):
            row[TempColumns.LEGS_SERIES].update(
                {MultiLegFields.LEG_OPTION_DELTA: row[TempColumns.OPTION_DELTA_VALUE]}
            )

    def _get_option_delta_value(self, legs_details: List[dict]) -> Optional[float]:
        """Get the optionDeltaValue from the leg options and transform it to a percentage value"""
        for leg in legs_details:
            if MultiLegFields.LEG_OPTION_DELTA in leg:
                return float(leg.pop(MultiLegFields.LEG_OPTION_DELTA)) / 100.0

        return None

    def _flip_buy_sell_indicator(self, params: Params):
        """
        This method is responsible to flip the side of the order. This is done conditionally
        If the parent leg's side is SELL, the child legs' sides are supposed to be flipped.
        Else use the child legs' side.
        for example -
        6814592 = SELL (the parent order, with a multileg symbol, with side = SELLL)

            - 5652882 B (leg Side) <-> S flip since parent(6814592) is SELL

                - 5652854 B <-> S flipped since parent (5652882) is SELL
                - 5652856 B <-> S flipped since parent (5652882)  is SELL
                - 5652858 B <-> S flipped since parent (5652882)  is SELL
            - 5652884 S (leg side) <-> B flip since parent(6814592) is SELL

                - 5652860 B no flip since parent (5652884) is BUY
                - 5652862 B no flip since parent (5652884) is BUY
                - 5652864 B no flip since parent (5652884) is BUY
        :param params:
        :return:
        """

        flip_map = {
            BuySellIndicator.BUYI.value: BuySellIndicator.SELL.value,
            BuySellIndicator.SELL.value: BuySellIndicator.BUYI.value,
            "1": "2",
            "2": "1'",
        }

        flip_buy_sell_mask = (
            self.target_df.loc[:, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR]
            == BuySellIndicator.SELL.value
        ) & (self.target_df.loc[:, TempColumns.MULTILEG_INDICATOR])
        no_flip_buy_sell_mask = (
            self.target_df.loc[:, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR]
            == BuySellIndicator.BUYI.value
        ) & (self.target_df.loc[:, TempColumns.MULTILEG_INDICATOR])

        leg_buy_sell = self.target_df.loc[:, TempColumns.LEGS_SERIES].str.get(
            MultiLegFields.LEG_SIDE
        )

        self.target_df.loc[
            no_flip_buy_sell_mask,
            [
                add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.BUY_SELL),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE, attribute=OrderColumns.BUY_SELL
                ),
                OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
            ],
        ] = leg_buy_sell

        self.target_df.loc[
            flip_buy_sell_mask,
            [
                add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.BUY_SELL),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE, attribute=OrderColumns.BUY_SELL
                ),
                OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
            ],
        ] = leg_buy_sell.map(flip_map)

        # For buySell, convert it back to 0s and 1s
        if params.convert_buysell_from_enum_to_number:
            buy_sell_to_number_map = {
                BuySellIndicator.BUYI.value: "1",
                BuySellIndicator.SELL.value: "2",
                "1": "1",
                "2": "2",
            }
            for col in {
                add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.BUY_SELL),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE, attribute=OrderColumns.BUY_SELL
                ),
            }:
                self.target_df.loc[:, col] = self.target_df.loc[:, col].map(
                    buy_sell_to_number_map
                )

    def _update_order_ids(self, multileg_mask: pd.Series, params: Params):
        """
        Replaces the original symbol of the multileg order in the orderId
        with the fetched symbol for the single legs.
        For example if the orderId is `abcd|1234`
        and 1234 has to legs "4444" and "5555"
        the two orders will have new orderIds as
            - abcd|1234.replace("1234", "4444") = "abcd|4444"
            - abcd|1234.replace("1234", "5555") = "abcd|5555"
        :param multileg_mask: indicator of multileg orders
        :param params: params passed to task
        :return:
        """
        order_id_columns = [
            OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
            OrderColumns.ORDER_IDENTIFIERS_TRADING_VENUE_TRANSACTION_ID_CODE,
            add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID),
            add_prefix(prefix=ModelPrefix.ORDER_STATE, attribute=OrderColumns.ID),
        ]

        self.target_df.loc[multileg_mask, order_id_columns] = self.target_df.loc[
            multileg_mask,
            [
                OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
                params.security_id_col,
                TempColumns.ORIG_SECURITY_ID,
            ],
        ].apply(
            lambda x: x[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE].replace(
                x[TempColumns.ORIG_SECURITY_ID], x[params.security_id_col]
            )
            if not pd.isna(x[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE])
            else x[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
            axis=1,
        )

    def _update_order_qty(self, multileg_mask):
        """
        Updates initialQuantity by multiplying with legOptionDelta or
        legRatioQty if present.
        :param multileg_mask: indicator of multileg orders
        :return:
        """
        multiplier_series = (
            self.target_df.loc[multileg_mask, TempColumns.LEGS_SERIES]
            .str.get(MultiLegFields.LEG_OPTION_DELTA)
            .fillna(
                self.target_df.loc[multileg_mask, TempColumns.LEGS_SERIES].str.get(
                    MultiLegFields.LEG_RATIO_QTY
                )
            )
            .fillna(1)
            .astype("float")
        )

        self.target_df.loc[
            multileg_mask, OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY
        ] = (
            self.target_df.loc[
                multileg_mask, OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY
            ]
            * multiplier_series
        ).apply(
            np.ceil
        )

    @staticmethod
    def _get_fetch_instruments_params(params: Params):
        return ParamsFetchInstruments(
            security_id_col=params.security_id_col,
            exchange_symbol_col=params.exchange_symbol_col,
            trade_date=params.trade_date,
            trade_date_format=params.trade_date_format,
            get_multileg_indicator=True,
            multileg_indicator=TempColumns.MULTILEG_INDICATOR,
            exchange=params.exchange,
            instrument=params.instrument,
            exchange_venue_col=params.exchange_venue_col,
            default_multileg_indicator=False,
        )
