from datetime import datetime
from typing import List
from typing import Optional

import numpy as np
import pandas as pd
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.mifid2 import OrderStatus

from swarm_tasks.order.generic.static import ExchangeVenues
from swarm_tasks.order.generic.static import InstrumentFields
from swarm_tasks.order.generic.static import VENUE_DIRECT_INSTRUMENT
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional


def get_venue_direct_instrument(
    es_client,
    security_ids_dict: dict,
    exchange_symbol_local: str,
) -> pd.DataFrame:
    """
    This function queries SRP for .venue_direct_instrument index based on
    ext.exchangeSymbolLocal and returns the matches as a dataframe
    :param es_client: elasticsearch client
    :param security_ids_list: List of SecurityIDs to query elastic
    :param exchange_symbol_local: exchangeSymbolLocal field
    :param venues: Trading venue

    :return: returns the instruments dataframe from .venue_direct_instrument inddex as per query
    """
    result = []
    max_terms_size = 1000
    for exchange, security_ids in security_ids_dict.items():
        security_id_chunks = [
            security_ids[ix : ix + max_terms_size]
            for ix in range(0, len(security_ids), max_terms_size)
        ]
        index = VENUE_DIRECT_INSTRUMENT
        for chunk in security_id_chunks:
            query = get_query(
                security_id_list=chunk,
                exchange_symbol_local=exchange_symbol_local,
                venues=ExchangeVenues.EXCHANGE_VENUE_MAP.get(exchange.lower(), None),
            )
            hits = es_scroll(es_client, index=index, query=query)
            result.append(hits)
    result = pd.concat(result).reset_index(drop=True)
    if result.empty:
        return pd.DataFrame()

    # Remove the meta fields except for &id and &key
    cols_mask = ~result.columns.str.startswith("&") | result.columns.isin(
        [es_client.meta.id, es_client.meta.key, InstrumentFields.TIMESTAMP]
    )
    return result.loc[:, cols_mask]


def get_query(
    security_id_list: str, exchange_symbol_local: str, venues: Optional[List]
) -> dict:
    """
    Returns the query to be executed to search for the list of given SecurityIDs
    :param security_id_list: list of SecurityIDs
    :param exchange_symbol_local: the field on elastic to be matches with given list of SecurityIDs
    :param venue: the trading venue to fitler on
    :return:
    """
    terms = [{"terms": {exchange_symbol_local: security_id_list}}]
    if venues:
        terms.append({"terms": {"venue.tradingVenue": venues}})
    query = {"query": {"bool": {"must": terms}}}
    return query


def es_scroll(es_client, index, query: dict) -> pd.DataFrame:
    """
    Performs given elastic query on a given index
    :param es_client: elasticsearch client
    :param index: index to query on
    :param query: query to be executed
    :return: retuns query resule in the form of a DataFrame
    """
    hits = es_client.scroll(query=query, index=index, as_dataframe=True)
    return hits


def get_instrument_from_exch_symbol_local(
    security_id: str,
    instruments: pd.DataFrame,
    trade_date: str,
    date_format: str,
    instrument_symbol_field: str = "ext.exchangeSymbolLocal",
) -> dict:
    """
    For a given trade, there might be multiple matches on srp .venue_direct_instrument index.
    The logic to select the instrument corresponding to a given trade is :
    Pick the instrument with least time diff between the instrument &timestamp and tradedate.
    AND the instrument must've been created AFTER the trade.
    If no matches found for instrument created AFTER trade-date pick the instrument based on this logic:
    (https://steeleye.atlassian.net/browse/ON-3245?focusedCommentId=107875)
        1. All the matches have isUserDefinedSpread = False
        2. AND all the matches in prev days have identical instrumentFullName
    :param instrument_symbol_field:
    :param security_id: the SecurityID
    :param instruments: instruments DataFrame
    :param trade_date: date of trade
    :param date_format: trade_data format
    :return: instrument as a dict
    """
    if instruments.empty:
        return pd.NA
    matched_instruments = instruments[instrument_symbol_field] == security_id
    if not any(matched_instruments):
        return pd.NA
    if sum(matched_instruments) > 1:
        # Make sure we always have the timestamp in milliseconds. This is to prevent cases where
        # we have 13-digit epochs in some rows and 10-digit epochs in others.
        instruments.loc[:, InstrumentFields.TIMESTAMP] = instruments.loc[
            :, InstrumentFields.TIMESTAMP
        ].astype(str)
        instruments.loc[matched_instruments, InstrumentFields.TIMESTAMP] = np.where(
            instruments.loc[matched_instruments, InstrumentFields.TIMESTAMP].str.len()
            == 10,
            instruments.loc[matched_instruments, InstrumentFields.TIMESTAMP]
            + "000",  # Adds 3 trailing zeros
            instruments.loc[matched_instruments, InstrumentFields.TIMESTAMP],
        )

        instrument_timestamps = pd.to_datetime(
            instruments[matched_instruments][InstrumentFields.TIMESTAMP], unit="ms"
        ).to_dict()
        time_delta = {}
        negative_time_delta = {}
        for index in instrument_timestamps.keys():
            delta = (
                instrument_timestamps[index]
                - (datetime.strptime(trade_date, date_format))
            ).days
            if delta >= 0:
                time_delta[index] = delta
            else:
                negative_time_delta[index] = delta
        if time_delta:
            min_delta = min(time_delta, key=time_delta.get)
            return (
                instruments.drop(columns=[InstrumentFields.TIMESTAMP])
                .iloc[min_delta]
                .to_dict()
            )
        else:
            # Check that all instruments have the same instrumentFullName AND
            # none of the instruments are isUserDefinedSpread
            if (
                len(
                    instruments.loc[matched_instruments][
                        InstrumentFields.INSTRUMENT_FULL_NAME
                    ].unique()
                )
                == 1
                and InstrumentFields.DERIVATIVE_USER_DEFINED_SPREAD
                in instruments.columns
                and instruments.loc[matched_instruments][
                    InstrumentFields.DERIVATIVE_USER_DEFINED_SPREAD
                ].sum()
                == 0
            ):
                max_delta = max(negative_time_delta, key=negative_time_delta.get)
                return (
                    instruments.drop(columns=[InstrumentFields.TIMESTAMP])
                    .iloc[max_delta]
                    .to_dict()
                )
            else:
                return pd.NA
            # return pd.NA
    return (
        instruments[matched_instruments]
        .drop(columns=[InstrumentFields.TIMESTAMP])
        .to_dict("r")[0]
    )


def replace_parfs_with_flat_average(
    df: pd.DataFrame,
    execution_price_col: str = "Execution Price (Tag 31)",
    fill_quantity_col: str = "Fill Quantity (Tag 32)",
    order_id_col: str = "Order ID",
    route_id_col: str = "Route ID",
    order_status_temp_col: str = "__order_status__",
    trading_date_time_temp_col: str = "__trading_date_time__",
    clean_temp_column: bool = False,
) -> pd.DataFrame:
    """
    This method will remove all PARF/FILL rows and replace them by a single row per order id
    that represents the average of the PARF/FILLs for that order id.

    It uses the following logic:
        `tradingDateTime`: keep the earliest
        `orderStatus`: keep FILL if present
        `quantity`: sum
        `price`: weighted average based on quantity
    """

    order_id_temp_col: str = "__order_id__"
    df.loc[:, order_id_temp_col] = MapConditional.process(
        source_frame=df,
        params=ParamsMapConditional(
            target_attribute=order_id_temp_col,
            cases=[
                Case(
                    query=f"`{route_id_col}`.notnull()",
                    attribute=route_id_col,
                ),
                Case(
                    query=f"`{route_id_col}`.isnull()",
                    attribute=order_id_col,
                ),
            ],
        ),
    ).loc[:, order_id_temp_col]
    parf_or_fill = df.loc[:, order_status_temp_col].isin(
        [OrderStatus.PARF, OrderStatus.FILL]
    )

    non_parf_or_fill_df = df.loc[~parf_or_fill, :]
    parf_or_fill_df = df.loc[parf_or_fill, :]

    # Keep the earliest timestamp
    average_parf_or_fill_df = (
        parf_or_fill_df.sort_values(by=trading_date_time_temp_col)
        .drop_duplicates(subset=route_id_col, keep="first")
        .sort_index()
    )

    # If both PARF or FILL exist, chose FILL
    order_status_dict = (
        parf_or_fill_df.sort_values(by=[order_status_temp_col])
        .drop_duplicates(subset=order_id_temp_col, keep="first")
        .set_index(keys=order_id_temp_col)
        .loc[:, order_status_temp_col]
        .to_dict()
    )
    average_parf_or_fill_df.loc[:, order_status_temp_col] = average_parf_or_fill_df.loc[
        :, order_id_temp_col
    ].map(order_status_dict)

    # Required as the following columns seems to contain non-numeric data
    parf_or_fill_df[execution_price_col] = pd.to_numeric(
        parf_or_fill_df[execution_price_col], errors="coerce"
    )
    parf_or_fill_df[fill_quantity_col] = pd.to_numeric(
        parf_or_fill_df[fill_quantity_col], errors="coerce"
    )

    # Price Weighted Average
    parf_or_fill_df.loc[:, "weighted_price"] = (
        parf_or_fill_df.loc[:, fill_quantity_col]
        * parf_or_fill_df.loc[:, execution_price_col]
    )
    group_by_id = parf_or_fill_df.groupby(order_id_temp_col)
    group_by_id_sum = group_by_id.agg(
        {fill_quantity_col: "sum", "weighted_price": "sum"}
    )
    weighted_average_dict = (
        group_by_id_sum.loc[:, "weighted_price"]
        / group_by_id_sum.loc[:, fill_quantity_col]
    ).to_dict()
    average_parf_or_fill_df.loc[:, execution_price_col] = average_parf_or_fill_df.loc[
        :, order_id_temp_col
    ].map(weighted_average_dict)

    # Sum quantities
    quantities_dict = group_by_id[fill_quantity_col].sum().to_dict()
    average_parf_or_fill_df.loc[:, fill_quantity_col] = average_parf_or_fill_df.loc[
        :, order_id_temp_col
    ].map(quantities_dict)

    joined_df = pd.concat(
        objs=[non_parf_or_fill_df, average_parf_or_fill_df], axis=0
    ).sort_index()

    if clean_temp_column:
        joined_df = joined_df.drop(columns=order_id_temp_col)

    return joined_df
