import pandas as pd
from pydantic import Field
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.order.generic.static import InstrumentFields


class Params(BaseParams):
    source_price: str = Field(
        default="Price", description="Column having price of the trade"
    )
    source_last_px: str = Field(
        default="LastPx", description="Column having LastPx of the trade"
    )
    source_stop_px: str = Field(
        default="StopPx", description="Column having StopPx of the trade"
    )
    instrument: str = Field(
        default="instrumentDetails.instrument", description="Target Column for venue"
    )
    target_venue = Field(
        default=OrderColumns.TRANSACTION_DETAILS_VENUE,
        description="Target Column for venue",
    )
    target_ultimate_venue = Field(
        default=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
        description="Target Column for ultimate venue",
    )
    target_price_currency = Field(
        default=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
        description="Target Column for transaction details price currency",
    )
    target_transaction_details_price = Field(
        default=OrderColumns.TRANSACTION_DETAILS_PRICE,
        description="Target Column for transaction details price field",
    )
    target_price_forming_data_price = Field(
        default=OrderColumns.PRICE_FORMING_DATA_PRICE,
        description="Target Column for price forming data price field. ",
    )
    target_stop_price = Field(
        default=OrderColumns.EXECUTION_DETAILS_STOP_PRICE,
        description="Target Column for execution details stop price",
    )
    target_limit_price = Field(
        default=OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE,
        description="Target Column for execution details limit price",
    )
    ignore_price_display_factor = Field(
        default=False,
        description="For few feeds, the multiplier from the instrument should not be used, "
        "with this parameter, it will be hard-coded to 1 to nullify the effect of"
        "multiplier",
    )


class MapVenueAndPriceFields(TransformBaseTask):
    """
    This task populates venue and price related fields making use of the extracted
    instrument for each trade. The logic used can be found here:
    https://steeleye.atlassian.net/wiki/spaces/IN/pages/2126446611/Order+CME+Drop+Copy+FIX#Step-4---Link-Fill-Messages-to-New-Orders-%2B-Map-Venue%2C-Price-and-Currency
    """

    params_class = Params

    def execute(
        self, source_frame: pd.DataFrame = None, params: Params = None, **kwargs
    ) -> pd.DataFrame:
        return self.process(source_frame=source_frame, params=params)

    @classmethod
    def process(cls, source_frame: pd.DataFrame, params: Params) -> pd.DataFrame:
        target_df = pd.DataFrame(index=source_frame.index)
        if source_frame.empty:
            return target_df
        cls.get_transaction_details_ultimate_venue(
            target_df=target_df, source_frame=source_frame, params=params
        )
        cls.get_transaction_details_venue(target_df=target_df)
        cls.transaction_details_price_currency(
            target_df=target_df, source_frame=source_frame, params=params
        )
        cls.get_price_display_factor(
            target_df=target_df, source_frame=source_frame, params=params
        )
        cls.get_price_forming_data_price(
            target_df=target_df, source_frame=source_frame, params=params
        )
        cls.get_transaction_details_price(target_df=target_df)
        cls.get_execution_details_stop_price(
            target_df=target_df, source_frame=source_frame, params=params
        )
        cls.execution_details_limit_price(
            target_df=target_df, source_frame=source_frame, params=params
        )
        cls.tranasction_details_price_notation(
            target_df=target_df, source_frame=source_frame, params=params
        )
        cls.transaction_details_quantity_notation(
            target_df=target_df, source_frame=source_frame, params=params
        )
        target_df.drop([InstrumentFields.PRICE_DISPLAY_FACTOR], axis=1, inplace=True)
        return target_df

    @staticmethod
    def get_transaction_details_ultimate_venue(
        target_df: pd.DataFrame, source_frame: pd.DataFrame, params: Params
    ):
        """Updates the transactionDetails.ultimateVenue field using instrument.venue.tradingVenue"""
        target_df[OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE] = source_frame[
            params.instrument
        ].apply(lambda x: x.get(InstrumentFields.VENUE) if not pd.isna(x) else pd.NA)

    @staticmethod
    def get_transaction_details_venue(target_df: pd.DataFrame):
        """
        Updates the transactionDetails.ultimateVenue field using transactionDetails.ultimateVenue.
        Assumes get_transaction_details_ultimate_venue() is called before this method
        """
        target_df[OrderColumns.TRANSACTION_DETAILS_VENUE] = target_df[
            OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE
        ]

    @staticmethod
    def transaction_details_price_currency(
        target_df: pd.DataFrame, source_frame: pd.DataFrame, params: Params
    ):
        """
        Populates transactionDetails.priceCurrency field using instrument.notionalCurrency1
        """
        target_df[OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY] = source_frame[
            params.instrument
        ].apply(
            lambda x: x.get(InstrumentFields.NOTIONAL_CURRENCY)
            if not pd.isna(x)
            else pd.NA
        )

    @staticmethod
    def get_price_display_factor(
        target_df: pd.DataFrame, source_frame: pd.DataFrame, params: Params
    ):
        """
        This method populates a temporary field called derivative.priceDisplayFactor. This field is
        used as a multiplier for price, stopprice and limitprice. And once these price fields are calculated,
        this temp field is removed from the target df. 1 is used as the default value if the field is missing
        in the instrument.
        """
        target_df[InstrumentFields.PRICE_DISPLAY_FACTOR] = source_frame[
            params.instrument
        ].apply(
            lambda x: x.get(InstrumentFields.PRICE_DISPLAY_FACTOR, 1)
            if not pd.isna(x)
            else 1
        )
        if params.ignore_price_display_factor:
            # Hard-code and overwrite the values to 1 to nullify any effect of multiplier
            target_df[InstrumentFields.PRICE_DISPLAY_FACTOR] = 1

    @staticmethod
    def get_price_forming_data_price(
        target_df: pd.DataFrame, source_frame: pd.DataFrame, params: Params
    ):
        """Populates _orderState.priceFormingData.price
        Logic: params.source_last_px * derivative.priceDisplayFactor(default=1)"""
        target_df[
            add_prefix(
                prefix=ModelPrefix.ORDER_STATE,
                attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
            )
        ] = source_frame.loc[:, params.source_last_px].dropna().astype(
            float
        ) * target_df[
            InstrumentFields.PRICE_DISPLAY_FACTOR
        ].astype(
            float
        )

    @staticmethod
    def get_transaction_details_price(target_df):
        """
        Populates _orderState.transactionDetails.price using _ordrestate.priceFormingData.price
        Assumes the method get_price_forming_data_price() is called before
        """
        target_df[
            add_prefix(
                prefix=ModelPrefix.ORDER_STATE,
                attribute=OrderColumns.TRANSACTION_DETAILS_PRICE,
            )
        ] = target_df[
            add_prefix(
                prefix=ModelPrefix.ORDER_STATE,
                attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
            )
        ]

    @staticmethod
    def get_execution_details_stop_price(
        target_df: pd.DataFrame, source_frame: pd.DataFrame, params: Params
    ):
        """Populates executionDetails.stopPrice
        Logic: params.source_stop_px * derivative.priceDisplayFactor(default=1)"""
        target_df[OrderColumns.EXECUTION_DETAILS_STOP_PRICE] = source_frame.loc[
            :, params.source_stop_px
        ].dropna().astype(float) * target_df[
            InstrumentFields.PRICE_DISPLAY_FACTOR
        ].astype(
            float
        )

    @staticmethod
    def execution_details_limit_price(
        target_df: pd.DataFrame, source_frame: pd.DataFrame, params: Params
    ):
        """Populates executionDetails.stopPrice
        Logic: params.source_price * derivative.priceDisplayFactor(default=1)"""
        target_df[OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE] = source_frame.loc[
            :, params.source_price
        ].dropna().astype(float) * target_df[
            InstrumentFields.PRICE_DISPLAY_FACTOR
        ].astype(
            float
        )

    @staticmethod
    def tranasction_details_price_notation(
        target_df: pd.DataFrame, source_frame: pd.DataFrame, params: Params
    ):
        """Populates transactionDetails.priceNotation"""
        target_df[OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION] = source_frame.loc[
            :, params.instrument
        ].apply(
            lambda x: x.get(InstrumentFields.EXT_PRICE_NOTATION)
            if not pd.isna(x)
            else pd.NA
        )

    @staticmethod
    def transaction_details_quantity_notation(
        target_df: pd.DataFrame, source_frame: pd.DataFrame, params: Params
    ):
        """Populates transactionDetails.quantityNotation"""
        target_df[
            OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION
        ] = source_frame.loc[:, params.instrument].apply(
            lambda x: x.get(InstrumentFields.EXT_QTY_NOTATION)
            if not pd.isna(x)
            else pd.NA
        )
