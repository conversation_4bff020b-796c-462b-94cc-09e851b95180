import pandas as pd
from se_trades_tasks.order.party.fallback.party_fallback_with_lei_lookup import (
    Params as GenericParams,
)
from se_trades_tasks.order.party.fallback.party_fallback_with_lei_lookup import (
    run_party_fallback_with_lei_lookup,
)
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class PartyFallbackWithLeiLookup(TransformBaseTask):
    """
    The PartyFallbackWithLeiLookup task, which is to be used only by order flows,
    does the following:
    1. By default, it uses the fallback identifier fields created in the
    PartyIdentifiers task, and uses it to create a fake fallback party
    whenever the real party is null.
    2. For identifier fields which might contain firms, it does an additional
     step. Whenever there is an LEI in the firm identifier column, it looks
     up the LeiRecord model to get the corresponding name. This name is
     populated in the fake fallback party (again, provided the real party
     ie null).
    3. While the task uses fallback identifier fields created in PartyIdentifiers
    by default, this can be overridden by passing in params for fields which
    you DON'T want to be populated from the PartyIdentifiers fallback fields.

    NOTE: PartyIdentifiers fallback fields are file identifiers without prefixes
    NOTE 2: 'Fake' fallback parties contain the name (from the identifier column
    value), meta_key and meta_id (same as the name) of the party, as well as a
    field 'isCreatedThroughPartyFallback, which is set to True.
    """

    params_class = Params

    def execute(
        self, source_frame: pd.DataFrame = None, params: Params = None, **kwargs
    ) -> pd.DataFrame:
        return self.process(source_frame=source_frame, params=params)

    @classmethod
    def process(cls, source_frame: pd.DataFrame = None, params: Params = None):

        return run_party_fallback_with_lei_lookup(
            source_frame=source_frame,
            params=params,
        )
