from enum import Enum
from typing import Dict
from typing import List
from typing import Optional
from typing import Union

import pandas as pd
from pydantic import Field
from se_elastic_schema.static.mifid2 import BuySellIndicator
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class PartiesFields(str, Enum):
    PARTIES_BUYER = "buyer"
    PARTIES_BUYER_DECISION_MAKER = "buyerDecisionMaker"
    PARTIES_CLIENT = "clientIdentifiers.client"
    PARTIES_COUNTERPARTY = "counterparty"
    PARTIES_EXECUTING_ENTITY = "reportDetails.executingEntity"
    PARTIES_EXECUTION_WITHIN_FIRM = "tradersAlgosWaiversIndicators.executionWithinFirm"
    PARTIES_INVEST_DEC_WITHIN_FIRM = (
        "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm"
    )
    PARTIES_SELLER = "seller"
    PARTIES_SELLER_DECISION_MAKER = "sellerDecisionMaker"
    PARTIES_TRADER = "trader"


class Resources(BaseResources):
    es_client_key: str


class PartyField:
    NAME = "name"


class PartyType:
    FIRM = "FIRM"
    PERSON = "PERSON"


class TargetDataType:
    SINGLE = "SINGLE"
    LIST = "LIST"


class Params(BaseParams):
    buyer_attribute: str = Field(
        "__buyer_id__",
        description="Buyer Attribute",
    )
    seller_attribute: str = Field(
        "__seller_id__",
        description="Seller Attribute",
    )
    trader_attribute: str = Field(
        "__trader_id__",
        description="Trader Attribute",
    )
    buyer_decision_maker_attribute: str = Field(
        "__buyer_decision_maker_id__",
        description="Buyer decision maker Attribute",
    )
    seller_decision_maker_attribute: str = Field(
        "__seller_decision_maker_id__",
        description="Seller decision maker Attribute",
    )
    executing_entity_attribute: str = Field(
        "__executing_entity_id__",
        description="Execution within firm Attribute",
    )
    counterparty_attribute: str = Field(
        "__counterparty_id__",
        description="Execution within firm Attribute",
    )
    execution_within_firm_attribute: str = Field(
        "__execution_within_firm_id__",
        description="Execution within firm Attribute",
    )
    investment_decision_maker_attribute: str = Field(
        "__investment_decision_maker_id__",
        description="Investment decision within firm Attribute",
    )
    buy_sell_side_attribute: str = Field(
        "__buy_sell__", description="Buy/Sell Attribute"
    )
    use_buy_mask_for_buyer_seller: bool = Field(
        False,
        description="If true then buy_mask will be used to create party",
    )
    client_attribute: str = Field(
        "__client_id__",
        description="Client Attribute",
    )


# columns from upstream tasks (e.g. LinkParties) that should be passed to the target frame
PASS_THROUGH_COLUMNS = list(map(lambda x: x.value, PartiesFields))
MARKET_PERSON_META_KEY = "MarketPerson:"
MARKET_COUNTERPARTY_META_KEY = "MarketCounterparty:"


class PartiesFallback(TransformBaseTask):
    """
    populate party fields using data directly from the input file
    if a row has an existing party e.g. parties.buyer, it will not be updated
    Note: This task just populates the name, meta_key and meta_id of the party.
    """

    resources_class = Resources
    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: Resources = None,
        **kwargs
    ) -> pd.DataFrame:
        target = pd.DataFrame(index=source_frame.index, columns=PASS_THROUGH_COLUMNS)
        if source_frame.empty:
            return target

        es = self.clients.get(resources.es_client_key)
        df = source_frame.copy()

        # copy over any existing parties from source_frame
        for col in PASS_THROUGH_COLUMNS:
            if col in source_frame.columns:
                target[col] = source_frame[col]

        cols_used = [
            params.buyer_attribute,
            params.seller_attribute,
            params.buyer_decision_maker_attribute,
            params.seller_decision_maker_attribute,
            params.executing_entity_attribute,
            params.execution_within_firm_attribute,
            params.investment_decision_maker_attribute,
            params.trader_attribute,
            params.buy_sell_side_attribute,
            params.counterparty_attribute,
            params.client_attribute,
            *PASS_THROUGH_COLUMNS,
        ]
        # make sure required cols exist
        for col in cols_used:
            if col not in df.columns:
                df[col] = pd.NA

        buy_mask = df[params.buy_sell_side_attribute] == BuySellIndicator.BUYI
        target.loc[:, PartiesFields.PARTIES_BUYER] = self._fill_missing_party(
            df=df,
            party=PartiesFields.PARTIES_BUYER.value,
            buy_mask=buy_mask,
            source_attribute=params.buyer_attribute,
            source_attribute_sell_side=params.seller_attribute,
            meta_key=es.meta.key,
            meta_id=es.meta.id,
            params=params,
        )
        target.loc[:, PartiesFields.PARTIES_SELLER] = self._fill_missing_party(
            df=df,
            party=PartiesFields.PARTIES_SELLER.value,
            buy_mask=buy_mask,
            source_attribute=params.seller_attribute,
            source_attribute_sell_side=params.buyer_attribute,
            meta_key=es.meta.key,
            meta_id=es.meta.id,
            params=params,
        )
        target.loc[
            :, PartiesFields.PARTIES_BUYER_DECISION_MAKER
        ] = self._fill_missing_party(
            df=df,
            party=PartiesFields.PARTIES_BUYER_DECISION_MAKER.value,
            source_attribute=params.buyer_decision_maker_attribute,
            meta_key=es.meta.key,
            meta_id=es.meta.id,
            params=params,
        )
        target.loc[
            :, PartiesFields.PARTIES_SELLER_DECISION_MAKER
        ] = self._fill_missing_party(
            df=df,
            party=PartiesFields.PARTIES_SELLER_DECISION_MAKER.value,
            source_attribute=params.seller_decision_maker_attribute,
            meta_key=es.meta.key,
            meta_id=es.meta.id,
            params=params,
        )
        target.loc[
            :, PartiesFields.PARTIES_INVEST_DEC_WITHIN_FIRM
        ] = self._fill_missing_party(
            df=df,
            party=PartiesFields.PARTIES_INVEST_DEC_WITHIN_FIRM.value,
            party_type=PartyType.PERSON,
            target_data_type=TargetDataType.SINGLE,
            source_attribute=params.investment_decision_maker_attribute,
            meta_key=es.meta.key,
            meta_id=es.meta.id,
            params=params,
        )
        target.loc[
            :, PartiesFields.PARTIES_EXECUTION_WITHIN_FIRM
        ] = self._fill_missing_party(
            df=df,
            party=PartiesFields.PARTIES_EXECUTION_WITHIN_FIRM.value,
            target_data_type=TargetDataType.SINGLE,
            party_type=PartyType.PERSON,
            source_attribute=params.execution_within_firm_attribute,
            meta_key=es.meta.key,
            meta_id=es.meta.id,
            params=params,
        )
        target.loc[
            :, PartiesFields.PARTIES_EXECUTING_ENTITY
        ] = self._fill_missing_party(
            df=df,
            party=PartiesFields.PARTIES_EXECUTING_ENTITY.value,
            party_type=PartyType.FIRM,
            target_data_type=TargetDataType.SINGLE,
            source_attribute=params.executing_entity_attribute,
            meta_key=es.meta.key,
            meta_id=es.meta.id,
            params=params,
        )
        target.loc[:, PartiesFields.PARTIES_COUNTERPARTY] = self._fill_missing_party(
            df=df,
            party=PartiesFields.PARTIES_COUNTERPARTY.value,
            party_type=PartyType.FIRM,
            target_data_type=TargetDataType.SINGLE,
            source_attribute=params.counterparty_attribute,
            meta_key=es.meta.key,
            meta_id=es.meta.id,
            params=params,
        )
        target.loc[:, PartiesFields.PARTIES_TRADER] = self._fill_missing_party(
            df=df,
            party=PartiesFields.PARTIES_TRADER.value,
            party_type=PartyType.PERSON,
            target_data_type=TargetDataType.LIST,
            source_attribute=params.trader_attribute,
            meta_key=es.meta.key,
            meta_id=es.meta.id,
            params=params,
        )

        target.loc[:, PartiesFields.PARTIES_CLIENT] = self._fill_missing_party(
            df=df,
            party=PartiesFields.PARTIES_CLIENT.value,
            party_type=PartyType.FIRM,
            target_data_type=TargetDataType.LIST,
            source_attribute=params.client_attribute,
            meta_key=es.meta.key,
            meta_id=es.meta.id,
            params=params,
        )

        return target

    def _fill_missing_party(
        self,
        df: pd.DataFrame,
        party: str,
        source_attribute: str,
        meta_key: str,
        meta_id: str,
        params: Params,
        buy_mask: Optional[pd.Series.bool] = None,
        source_attribute_sell_side: Optional[str] = None,
        party_type: str = PartyType.PERSON,
        target_data_type: str = TargetDataType.LIST,
    ) -> pd.Series:
        """
        :param df: pd.DataFrame
        :param party: str
        :param source_attribute: str
        :param meta_key: str
        :param meta_id: str
        :return: pd.Series
        """
        result = df.loc[:, party]
        if not source_attribute_sell_side:
            source_attribute_sell_side = source_attribute
        if not params.use_buy_mask_for_buyer_seller or buy_mask is None:
            buy_mask = pd.Series(True, index=df.index)

        buy_side_mask = (
            (buy_mask) & (df[source_attribute].notnull()) & (df[party].isna())
        )

        format_buyer_parties_as_list: bool = target_data_type == TargetDataType.LIST

        if buy_side_mask.any():
            result.loc[buy_side_mask] = PartiesFallback._format_parties(
                source=df.loc[buy_side_mask],
                name=source_attribute,
                meta_key=meta_key,
                meta_id=meta_id,
                party_type=party_type,
                mask=buy_side_mask,
                as_list=format_buyer_parties_as_list,
            )

        sell_side_mask = (
            (~buy_mask)
            & (df[source_attribute_sell_side].notnull())
            & (df[party].isna())
        )

        format_sell_parties_as_list: bool = party_type == PartyType.PERSON

        if sell_side_mask.any():
            result.loc[sell_side_mask] = PartiesFallback._format_parties(
                source=df.loc[sell_side_mask],
                name=source_attribute_sell_side,
                meta_key=meta_key,
                meta_id=meta_id,
                party_type=party_type,
                mask=sell_side_mask,
                as_list=format_sell_parties_as_list,
            )

        return result

    @staticmethod
    def _format_party(
        source: pd.DataFrame,
        name: str,
        party_type: str,
        meta_key: str = None,
        meta_id: str = None,
    ) -> pd.DataFrame:
        """
        :param source: pd.DataFrame
        :param name: str
        :param meta_key: str
        :param meta_id: str
        :return: None
        map columns from source df to fields for MarketPerson to be used in party
        fields for Order Model
        """
        df = pd.DataFrame(index=source.index)
        df.loc[:, PartyField.NAME] = source.loc[:, name]

        df.loc[:, PartyField.NAME] = df[PartyField.NAME].replace(" ", pd.NA)

        df.loc[:, meta_id] = df.loc[:, PartyField.NAME]
        df.loc[:, meta_key] = (
            MARKET_PERSON_META_KEY
            if party_type == PartyType.PERSON
            else MARKET_COUNTERPARTY_META_KEY
        )

        return df

    @staticmethod
    def _format_parties(
        source: pd.DataFrame,
        name: str,
        party_type: str,
        mask: pd.Series,
        meta_key: str,
        meta_id: str,
        as_list: bool,
    ) -> pd.Series:
        """
        This method formats the parties for buyers/sellers.

        :param source: the dataframe which data have the required data.
        :param name: dataframe column that contains the buyer/seller parties
        :param party_type: type of the party
        :param mask: the mask that should be applied to the dataframe
        :param meta_key: name of the meta key
        :param meta_id: name of the meta id
        :param as_list: if a party should be convert/returned as list
        :return: a series containing the seller parties
        """
        return PartiesFallback._format_party(
            source=source.loc[mask],
            name=name,
            meta_key=meta_key,
            meta_id=meta_id,
            party_type=party_type,
        ).apply(
            lambda x: PartiesFallback._format_single_party(
                party=x, meta_key=meta_key, meta_id=meta_id, as_list=as_list
            ),
            axis=1,
        )

    @staticmethod
    def _format_single_party(
        party: pd.Series,
        meta_key: str,
        meta_id: str,
        as_list: bool = False,
    ) -> Union[Dict, List]:
        """
        Handles the party data.
        1. It preserves the old behaviour.
        2. Allows that buyer/seller to be a list
        - https://steeleye.atlassian.net/browse/ON-3469

        :param party: a pandas series with the party data
        :param meta_key: name of the meta key
        :param meta_id: name of the meta id
        :param as_list: whether a list should be returned
        :return: individual party or a list of parties
        """

        if type(party[PartyField.NAME]) == list:
            return [
                {
                    meta_id: str(id_val),
                    meta_key: str(party[meta_key]),
                    PartyField.NAME: str(name_val),
                }
                for id_val, name_val in zip(party[meta_id], party[PartyField.NAME])
            ]

        if as_list:
            return [party.dropna().to_dict()]

        return {**party.dropna().to_dict()}
