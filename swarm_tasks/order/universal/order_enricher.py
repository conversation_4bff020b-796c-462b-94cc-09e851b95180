import logging
import os
from typing import Optional

import pandas as pd
from prefect import context
from prefect.engine.signals import SKIP
from pydantic import Field
from se_trades_tasks.order.universal.order_eod_stats_enricher import (
    run_order_eod_stats_enricher,
)
from swarm.task.auditor import Auditor
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


logger = context.get("logger")
logging.getLogger("market_data_sdk").setLevel(logging.WARNING)
logging.getLogger("httpx").setLevel(logging.WARNING)

DEFAULT_BATCH_SIZE = 100

COGNITO_CLIENT_ID = os.environ.get("COGNITO_CLIENT_ID")
COGNITO_CLIENT_SECRET = os.environ.get("COGNITO_CLIENT_SECRET")
COGNITO_AUTH_URL = os.environ.get("COGNITO_AUTH_URL")
MASTER_DATA_API_HOST = os.environ.get("MASTER_DATA_API_HOST")


class RetryableException(Exception):
    pass


class Params(BaseParams):
    batch_size = Field(
        default=DEFAULT_BATCH_SIZE,
        description="Batch size at which the Market APIs are to be called",
    )


class MetaKeys:
    ID = "&id"


class OrderFields:
    INSTRUMENT_DETAILS_INSTRUMENT = "instrumentDetails.instrument"


class TempCols:
    RIC = "__ric__"
    EOD_DATA = "__eod_data__"


class OrderEODStatsEnricher(BaseTask):
    """
    Task to get Market EOD_Data for an Order (Instrument).
    This currently returns a reference dataframe to be used downstream processes.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[BaseParams] = None,
        **kwargs,
    ) -> pd.DataFrame:
        return self.process(
            source_frame=source_frame, params=params, auditor=self.auditor
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Optional[Params] = None,
        auditor: Auditor = None,
    ) -> pd.DataFrame:
        if source_frame.empty:
            raise SKIP("Task processing skipped as source frame was empty!")

        return run_order_eod_stats_enricher(
            source_frame=source_frame,
            params=params,
        )
