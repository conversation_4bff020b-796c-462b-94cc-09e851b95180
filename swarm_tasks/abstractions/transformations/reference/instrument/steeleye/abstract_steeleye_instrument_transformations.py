from abc import ABC
from abc import abstractmethod

import pandas as pd
from se_core_tasks.abstractions.transformations.abstract_transformations import (
    AbstractTransformations,
)


class SEInstrumentColumns:
    CFI_ATTRIBUTE_1 = "cfiAttribute1"
    CFI_ATTRIBUTE_2 = "cfiAttribute2"
    CFI_ATTRIBUTE_3 = "cfiAttribute3"
    CFI_ATTRIBUTE_4 = "cfiAttribute4"
    CFI_CATEGORY = "cfiCategory"
    CFI_GROUP = "cfiGroup"
    COMMODITIES_OR_EMISSION_ALLOWANCE_DERIVATIVE_IND = (
        "commoditiesOrEmissionAllowanceDerivativeInd"
    )
    DERIVATIVE_DELIVERY_TYPE = "derivative.deliveryType"
    DERIVATIVE_EXPIRY_DATE = "derivative.expiryDate"
    DERIVATIVE_PRICE_MULTIPLIER = "derivative.priceMultiplier"
    DERIVATIVE_UNDERLYING_INSTRUMENTS_UNDERLYING_INSTRUMENT_CLASSIFICATION = (
        "derivative.underlyingInstruments.0.underlyingInstrumentClassification"
    )
    DERIVATIVE_UNDERLYING_INSTRUMENTS_UNDERLYING_INSTRUMENT_CODE = (
        "derivative.underlyingInstruments.0.underlyingInstrumentCode"
    )
    EXT_ALTERNATIVE_INSTRUMENT_IDENTIFIER = "ext.alternativeInstrumentIdentifier"
    EXT_BEST_EX_ASSET_CLASS_MAIN = "ext.bestExAssetClassMain"
    EXT_BEST_EX_ASSET_CLASS_SUB = "ext.bestExAssetClassSub"
    EXT_EMIR_ELIGIBLE = "ext.emirEligible"
    EXT_INSTRUMENT_ID_CODE_TYPE = "ext.instrumentIdCodeType"
    EXT_INSTRUMENT_UNIQUE_IDENTIFIER = "ext.instrumentUniqueIdentifier"
    EXT_PRICE_NOTATION = "ext.priceNotation"
    EXT_PRICING_REFERENCES_RIC = "ext.pricingReferences.RIC"
    EXT_QUANTITY_NOTATION = "ext.quantityNotation"
    EXT_UNDERLYING_INSTRUMENTS = "ext.underlyingInstruments"
    EXT_UNDERLYING_INSTRUMENTS_DERIVATIVE_EXPIRY_DATE = (
        "ext.underlyingInstruments.0.derivative.expiryDate"
    )
    EXT_UNDERLYING_INSTRUMENTS_INSTRUMENT_ID_CODE = (
        "ext.underlyingInstruments.0.instrumentIdCode"
    )
    EXT_UNDERLYING_INSTRUMENTS_VENUE_TRADING_VENUE = (
        "ext.underlyingInstruments.0.venue.tradingVenue"
    )
    INSTRUMENT_CLASSIFICATION = "instrumentClassification"
    INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS = (
        "instrumentClassificationEMIRAssetClass"
    )
    INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE = (
        "instrumentClassificationEMIRContractType"
    )
    INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_YPE = (
        "instrumentClassificationEMIRProductType"
    )
    INSTRUMENT_FULL_NAME = "instrumentFullName"
    INSTRUMENT_ID_CODE = "instrumentIdCode"
    META_MODEL = "__meta_model__"
    NOTIONAL_CURRENCY_1 = "notionalCurrency1"
    SOURCE_KEY = "sourceKey"
    VENUE_FINANCIAL_INSTRUMENT_SHORT_NAME = "venue.financialInstrumentShortName"
    VENUE_TRADING_VENUE = "venue.tradingVenue"


class AbstractSteelEyeInstrumentTransformations(AbstractTransformations, ABC):
    def _post_process(self):
        pass

    def process(self) -> pd.DataFrame:
        """

        :return: target_df
        """

        self.pre_process()

        self.cfi_attribute_1()
        self.cfi_attribute_2()
        self.cfi_attribute_3()
        self.cfi_attribute_4()
        self.cfi_category()
        self.cfi_group()
        self.commodities_or_emission_allowance_derivative_ind()
        self.derivative_delivery_type()
        self.derivative_price_multiplier()
        self.derivative_underlying_instruments_underlying_instrument_classification()
        self.derivative_underlying_instruments_underlying_instrument_code()
        self.ext_alternative_instrument_identifier()
        self.ext_best_ex_asset_class_main()
        self.ext_best_ex_asset_class_sub()
        self.ext_emir_eligible()
        self.ext_instrument_id_code_type()
        self.ext_instrument_unique_identifier()
        self.ext_price_notation()
        self.ext_pricing_references_ric()
        self.ext_quantity_notation()
        self.ext_underlying_instruments_derivative_expiry_date()
        self.ext_underlying_instruments_instrument_id_code()
        self.ext_underlying_instruments_venue_trading_venue()
        self.instrument_classification()
        self.instrument_classification_emir_asset_class()
        self.instrument_classification_emir_contract_type()
        self.instrument_classification_emir_product_type()
        self.instrument_full_name()
        self.meta_model()
        self.notional_currency_1()
        self.source_key()
        self.venue_financial_instrument_short_name()
        self.venue_trading_venue()

        self.post_process()
        return self.target_df

    def _pre_process(self):
        pass

    def cfi_attribute_1(self):
        self.target_df = pd.concat([self.target_df, self._cfi_attribute_1()], axis=1)

    @abstractmethod
    def _cfi_attribute_1(self):
        pass

    def cfi_attribute_2(self):
        self.target_df = pd.concat([self.target_df, self._cfi_attribute_2()], axis=1)

    @abstractmethod
    def _cfi_attribute_2(self):
        pass

    def cfi_attribute_3(self):
        self.target_df = pd.concat([self.target_df, self._cfi_attribute_3()], axis=1)

    @abstractmethod
    def _cfi_attribute_3(self):
        pass

    def cfi_attribute_4(self):
        self.target_df = pd.concat([self.target_df, self._cfi_attribute_4()], axis=1)

    @abstractmethod
    def _cfi_attribute_4(self):
        pass

    def cfi_category(self):
        self.target_df = pd.concat([self.target_df, self._cfi_category()], axis=1)

    @abstractmethod
    def _cfi_category(self):
        pass

    def cfi_group(self):
        self.target_df = pd.concat([self.target_df, self._cfi_group()], axis=1)

    @abstractmethod
    def _cfi_group(self):
        pass

    def commodities_or_emission_allowance_derivative_ind(self):
        self.target_df = pd.concat(
            [self.target_df, self._commodities_or_emission_allowance_derivative_ind()],
            axis=1,
        )

    @abstractmethod
    def _commodities_or_emission_allowance_derivative_ind(self):
        pass

    def derivative_delivery_type(self):
        self.target_df = pd.concat(
            [self.target_df, self._derivative_delivery_type()], axis=1
        )

    @abstractmethod
    def _derivative_delivery_type(self):
        pass

    def derivative_price_multiplier(self):
        self.target_df = pd.concat(
            [self.target_df, self._derivative_price_multiplier()], axis=1
        )

    @abstractmethod
    def _derivative_price_multiplier(self):
        pass

    def derivative_underlying_instruments_underlying_instrument_classification(self):
        self.target_df = pd.concat(
            [
                self.target_df,
                self._derivative_underlying_instruments_underlying_instrument_classification(),
            ],
            axis=1,
        )

    @abstractmethod
    def _derivative_underlying_instruments_underlying_instrument_classification(self):
        pass

    def derivative_underlying_instruments_underlying_instrument_code(self):
        self.target_df = pd.concat(
            [
                self.target_df,
                self._derivative_underlying_instruments_underlying_instrument_code(),
            ],
            axis=1,
        )

    @abstractmethod
    def _derivative_underlying_instruments_underlying_instrument_code(self):
        pass

    def ext_alternative_instrument_identifier(self):
        self.target_df = pd.concat(
            [self.target_df, self._ext_alternative_instrument_identifier()], axis=1
        )

    @abstractmethod
    def _ext_alternative_instrument_identifier(self):
        pass

    def ext_best_ex_asset_class_main(self):
        self.target_df = pd.concat(
            [self.target_df, self._ext_best_ex_asset_class_main()], axis=1
        )

    @abstractmethod
    def _ext_best_ex_asset_class_main(self):
        pass

    def ext_best_ex_asset_class_sub(self):
        self.target_df = pd.concat(
            [self.target_df, self._ext_best_ex_asset_class_sub()], axis=1
        )

    @abstractmethod
    def _ext_best_ex_asset_class_sub(self):
        pass

    def ext_emir_eligible(self):
        self.target_df = pd.concat([self.target_df, self._ext_emir_eligible()], axis=1)

    @abstractmethod
    def _ext_emir_eligible(self):
        pass

    def ext_instrument_id_code_type(self):
        self.target_df = pd.concat(
            [self.target_df, self._ext_instrument_id_code_type()], axis=1
        )

    @abstractmethod
    def _ext_instrument_id_code_type(self):
        pass

    def ext_instrument_unique_identifier(self):
        self.target_df = pd.concat(
            [self.target_df, self._ext_instrument_unique_identifier()], axis=1
        )

    @abstractmethod
    def _ext_instrument_unique_identifier(self):
        pass

    def ext_price_notation(self):
        self.target_df = pd.concat([self.target_df, self._ext_price_notation()], axis=1)

    @abstractmethod
    def _ext_price_notation(self):
        pass

    def ext_pricing_references_ric(self):
        self.target_df = pd.concat(
            [self.target_df, self._ext_pricing_references_ric()], axis=1
        )

    @abstractmethod
    def _ext_pricing_references_ric(self):
        pass

    def ext_quantity_notation(self):
        self.target_df = pd.concat(
            [self.target_df, self._ext_quantity_notation()], axis=1
        )

    @abstractmethod
    def _ext_quantity_notation(self):
        pass

    def ext_underlying_instruments_derivative_expiry_date(self):
        self.target_df = pd.concat(
            [self.target_df, self._ext_underlying_instruments_derivative_expiry_date()],
            axis=1,
        )

    @abstractmethod
    def _ext_underlying_instruments_derivative_expiry_date(self):
        pass

    def ext_underlying_instruments_instrument_id_code(self):
        self.target_df = pd.concat(
            [self.target_df, self._ext_underlying_instruments_instrument_id_code()],
            axis=1,
        )

    @abstractmethod
    def _ext_underlying_instruments_instrument_id_code(self):
        pass

    def ext_underlying_instruments_venue_trading_venue(self):
        self.target_df = pd.concat(
            [self.target_df, self._ext_underlying_instruments_venue_trading_venue()],
            axis=1,
        )

    @abstractmethod
    def _ext_underlying_instruments_venue_trading_venue(self):
        pass

    def instrument_classification(self):
        self.target_df = pd.concat(
            [self.target_df, self._instrument_classification()], axis=1
        )

    @abstractmethod
    def _instrument_classification(self):
        pass

    def instrument_classification_emir_asset_class(self):
        self.target_df = pd.concat(
            [self.target_df, self._instrument_classification_emir_asset_class()], axis=1
        )

    @abstractmethod
    def _instrument_classification_emir_asset_class(self):
        pass

    def instrument_classification_emir_contract_type(self):
        self.target_df = pd.concat(
            [self.target_df, self._instrument_classification_emir_contract_type()],
            axis=1,
        )

    @abstractmethod
    def _instrument_classification_emir_contract_type(self):
        pass

    def instrument_classification_emir_product_type(self):
        self.target_df = pd.concat(
            [self.target_df, self._instrument_classification_emir_product_type()],
            axis=1,
        )

    @abstractmethod
    def _instrument_classification_emir_product_type(self):
        pass

    def instrument_full_name(self):
        self.target_df = pd.concat(
            [self.target_df, self._instrument_full_name()], axis=1
        )

    @abstractmethod
    def _instrument_full_name(self):
        pass

    def meta_model(self):
        self.target_df = pd.concat([self.target_df, self._meta_model()], axis=1)

    @abstractmethod
    def _meta_model(self):
        pass

    def notional_currency_1(self):
        self.target_df = pd.concat(
            [self.target_df, self._notional_currency_1()], axis=1
        )

    @abstractmethod
    def _notional_currency_1(self):
        pass

    def source_key(self):
        self.target_df = pd.concat([self.target_df, self._source_key()], axis=1)

    @abstractmethod
    def _source_key(self):
        pass

    def venue_financial_instrument_short_name(self):
        self.target_df = pd.concat(
            [self.target_df, self._venue_financial_instrument_short_name()], axis=1
        )

    @abstractmethod
    def _venue_financial_instrument_short_name(self):
        pass

    def venue_trading_venue(self):
        self.target_df = pd.concat(
            [self.target_df, self._venue_trading_venue()], axis=1
        )

    @abstractmethod
    def _venue_trading_venue(self):
        pass
