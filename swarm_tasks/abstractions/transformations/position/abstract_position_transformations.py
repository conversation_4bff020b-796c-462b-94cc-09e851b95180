from abc import abstractmethod

import pandas as pd
from se_core_tasks.abstractions.transformations.abstract_transformations import (
    AbstractTransformations,
)


class AbstractPositionTransformations(AbstractTransformations):
    """This is the abstract base class for all Position Primary Transformations. It contains
    a process, pre_process and post_process methods, along with methods corresponding to
    each field in the Position schema.
    pre_process(): used to populate temp columns (columns which are used in multiple
                     places) in the pre_process_df data frame
    process(): Used to call the public methods for each of the fields in the Position
                 schema. At the end of process(), target_df should be filled with values for the
                 schema fields.
    post_process(): Used to populate target_df with temp columns, drop columns, and for any
                      other post processing.
    Field-specific methods:
    There are 2 methods for each schema field -- a public method and a private method.

    Private Schema Field methods:
    The private methods for schema fields are abstract and need to be implemented in the child class.
    These private methods should always return a Pandas DataFrame (if the schema column is required)
    or be passed (if the schema column isn't required).
    Typically, private methods call tasks or populate static values.

    Public Schema Field methods:

    These methods actually populate the Position schema fields by calling the appropriate private
    methods. Private methods and public methods for a field have the same name. The only difference is that
    the private method has a '_' prefix.
    Public methods should NOT be implemented in the child classes.

    """

    def pre_process(self) -> pd.DataFrame:
        """Used for pre-processing. It simply calls the private method _pre_process"""
        return self._pre_process()

    @abstractmethod
    def _pre_process(self):
        """This pre-processing method should be used to populate temporary columns in pre_process_df.
        pre_process_df should in general only be populated for temporary columns which might be
        required to populate at least 2 other fields.
        For e.g.
        1. if __strike_price__ is used in market_identifiers_instrument() and is required
        in downstream tasks, it should be populated in pre_process_df.
        2. if __price__ is used in transaction_details_price() and market_identifiers_instrument(), it
        should be populated in pre_process_df.
        3. if __price__ is only used in transaction_details_price(), populate it in the _transaction_details_price()
           method, NOT in pre_process_df.

        """
        raise NotImplementedError

    def process(self) -> pd.DataFrame:
        """All the schema target columns which need to be populated are populated in
        self.target_df by the public methods called by process(). 3 temp columns are
        populated here as well as these are present in all Position flows: __meta_model__,
        marketIdentifiers.parties and marketIdentifiers.instrument.

        The process() method should generally be overridden by child classes by including only
        the public methods that are required for that particular flow in the appropriate order.

        process() always calls pre_process() at the beginning and post_process() at the end and
        returns self.target_df.
        """
        self.pre_process()
        self.additional_information()
        self.amount_native()
        self.amount_native_currency()
        self.amount_in_chf()
        self.amount_in_eur()
        self.amount_in_gbp()
        self.amount_in_jpy()
        self.amount_in_usd()
        self.data_source_name()
        self.date()
        self.direction()
        self.level()
        self.market_identifiers_instrument()
        self.market_identifiers_parties()
        self.market_identifiers()
        self.meta_model()
        self.party_type()
        self.quantity()
        self.quantity_notation()
        self.source_index()
        self.source_key()
        self.post_process()
        return self.target_df

    def post_process(self) -> pd.DataFrame:
        """Used for post-processing. It simply calls the private method _post_process"""
        return self._post_process()

    @abstractmethod
    def _post_process(self):
        """The post-processing method should be used to populate temporary columns in target_df,
        and any other post-processing tasks.
        E.g. if __strike_price__ is used in market_identifiers_instrument() and is required
        in downstream tasks, it should be populated in pre_process_df in _pre_process() and copied
        over to target_df in _post_process()
        """
        raise NotImplementedError

    def additional_information(self):
        """Populates additionalInformation in target_df by calling _additional_information()"""
        self.target_df = pd.concat(
            [self.target_df, self._additional_information()], axis=1
        )

    @abstractmethod
    def _additional_information(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        additionalInformation columns"""
        raise NotImplementedError

    def amount_native(self):
        """Populates amount.native in target_df by calling _amount()"""
        self.target_df = pd.concat([self.target_df, self._amount_native()], axis=1)

    @abstractmethod
    def _amount_native(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        amount.native column"""
        raise NotImplementedError

    def amount_native_currency(self):
        """Populates amount.nativeCurrency in target_df by calling _amount_native_currency()"""
        self.target_df = pd.concat(
            [self.target_df, self._amount_native_currency()], axis=1
        )

    @abstractmethod
    def _amount_native_currency(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        amount.nativeCurrency column"""
        raise NotImplementedError

    def amount_in_chf(self):
        """Populates amount.amountInCHF in target_df by calling _amount_in_chf()"""
        self.target_df = pd.concat([self.target_df, self._amount_in_chf()], axis=1)

    @abstractmethod
    def _amount_in_chf(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        amount.amountInCHF column"""
        raise NotImplementedError

    def amount_in_eur(self):
        """Populates amount.amountInEUR in target_df by calling _amount_in_eur()"""
        self.target_df = pd.concat([self.target_df, self._amount_in_eur()], axis=1)

    @abstractmethod
    def _amount_in_eur(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        amount.amountInEUR column"""
        raise NotImplementedError

    def amount_in_gbp(self):
        """Populates amount.amountInGBP in target_df by calling _amount_in_gbp()"""
        self.target_df = pd.concat([self.target_df, self._amount_in_gbp()], axis=1)

    @abstractmethod
    def _amount_in_gbp(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        amount.amountInGBP column"""
        raise NotImplementedError

    def amount_in_jpy(self):
        """Populates amount.amountInJPY in target_df by calling _amount_in_jpy()"""
        self.target_df = pd.concat([self.target_df, self._amount_in_jpy()], axis=1)

    @abstractmethod
    def _amount_in_jpy(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        amount.amountInJPY column"""
        raise NotImplementedError

    def amount_in_usd(self):
        """Populates amount.amountInUSD in target_df by calling _amount_in_usd()"""
        self.target_df = pd.concat([self.target_df, self._amount_in_usd()], axis=1)

    @abstractmethod
    def _amount_in_usd(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        amount.amountInUSD column"""
        raise NotImplementedError

    def pnl_amount_native(self):
        """Populates pnlAmount.native in target_df by calling _amount()"""
        self.target_df = pd.concat([self.target_df, self._pnl_amount_native()], axis=1)

    @abstractmethod
    def _pnl_amount_native(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        pnlAmount.native column"""
        raise NotImplementedError

    def pnl_amount_native_currency(self):
        """Populates pnlAmount.nativeCurrency in target_df by calling _pnl_amount_native_currency()"""
        self.target_df = pd.concat(
            [self.target_df, self._pnl_amount_native_currency()], axis=1
        )

    @abstractmethod
    def _pnl_amount_native_currency(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        pnlAmount.nativeCurrency column"""
        raise NotImplementedError

    def pnl_amount_in_chf(self):
        """Populates pnlAmount.amountInCHF in target_df by calling _pnl_amount_in_chf()"""
        self.target_df = pd.concat([self.target_df, self._pnl_amount_in_chf()], axis=1)

    @abstractmethod
    def _pnl_amount_in_chf(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        pnlAmount.amountInCHF column"""
        raise NotImplementedError

    def pnl_amount_in_eur(self):
        """Populates pnlAmount.amountInEUR in target_df by calling _pnl_amount_in_eur()"""
        self.target_df = pd.concat([self.target_df, self._pnl_amount_in_eur()], axis=1)

    @abstractmethod
    def _pnl_amount_in_eur(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        pnlAmount.amountInEUR column"""
        raise NotImplementedError

    def pnl_amount_in_gbp(self):
        """Populates pnlAmount.amountInGBP in target_df by calling _pnl_amount_in_gbp()"""
        self.target_df = pd.concat([self.target_df, self._pnl_amount_in_gbp()], axis=1)

    @abstractmethod
    def _pnl_amount_in_gbp(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        pnlAmount.amountInGBP column"""
        raise NotImplementedError

    def pnl_amount_in_jpy(self):
        """Populates pnlAmount.amountInJPY in target_df by calling _pnl_amount_in_jpy()"""
        self.target_df = pd.concat([self.target_df, self._pnl_amount_in_jpy()], axis=1)

    @abstractmethod
    def _pnl_amount_in_jpy(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        pnlAmount.amountInJPY column"""
        raise NotImplementedError

    def pnl_amount_in_usd(self):
        """Populates pnlAmount.amountInUSD in target_df by calling _pnl_amount_in_usd()"""
        self.target_df = pd.concat([self.target_df, self._pnl_amount_in_usd()], axis=1)

    @abstractmethod
    def _pnl_amount_in_usd(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        pnlAmount.amountInUSD column"""
        raise NotImplementedError

    def data_source_name(self):
        """Populates dataSourceName in target_df by calling _data_source_name()"""
        self.target_df = pd.concat([self.target_df, self._data_source_name()], axis=1)

    @abstractmethod
    def _data_source_name(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        dataSourceName column"""
        raise NotImplementedError

    def date(self):
        """Populates date in target_df by calling _date()"""
        self.target_df = pd.concat([self.target_df, self._date()], axis=1)

    @abstractmethod
    def _date(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        date column"""
        raise NotImplementedError

    def direction(self):
        """Populates date in target_df by calling _direction()"""
        self.target_df = pd.concat([self.target_df, self._direction()], axis=1)

    @abstractmethod
    def _direction(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        direction column"""
        raise NotImplementedError

    def level(self):
        """Populates date in target_df by calling _level()"""
        self.target_df = pd.concat([self.target_df, self._level()], axis=1)

    @abstractmethod
    def _level(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        level column"""
        raise NotImplementedError

    def market_identifiers(self):
        """Populates marketIdentifiers in target_df by calling _market_identifiers().
        This is done by calling the MergeMarketIdentifiers task
        """
        self.target_df = pd.concat([self.target_df, self._market_identifiers()], axis=1)

    @abstractmethod
    def _market_identifiers(self) -> pd.DataFrame:
        """Abstract Method which needs to be implemented in the child class to populate
        marketIdentifiers"""
        raise NotImplementedError

    def market_identifiers_instrument(self):
        """Populates marketIdentifiers.instrument in target_df by calling _market_identifiers_instrument().
        This is done by calling the InstrumentIdentifiers task. marketIdentifiers.instrument is not
        a schema column, so has to be dropped downstream after the LinkInstrument task"""
        self.target_df = pd.concat(
            [self.target_df, self._market_identifiers_instrument()], axis=1
        )

    @abstractmethod
    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """Abstract Method which needs to be implemented in the child class to populate
        marketIdentifiers.instrument"""
        raise NotImplementedError

    def market_identifiers_parties(self):
        """Populates marketIdentifiers.parties in target_df by calling _market_identifiers_parties().
        This is done by calling the PartyIdentifiers task. marketIdentifiers.parties is not
        a schema column, so has to be dropped downstream after the LinkParties task"""
        self.target_df = pd.concat(
            [self.target_df, self._market_identifiers_parties()], axis=1
        )

    @abstractmethod
    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Abstract Method which needs to be implemented in the child class to populate
        marketIdentifiers.parties"""
        raise NotImplementedError

    def meta_model(self):
        """Populates __meta_model__ in target_df by calling _meta_model().
         __meta_model__ is a temporary column required for AssignMeta and has to be
        dropped downstream"""
        self.target_df = pd.concat([self.target_df, self._meta_model()], axis=1)

    @abstractmethod
    def _meta_model(self) -> pd.DataFrame:
        """Abstract Method which needs to be implemented in the child class to
        populate __meta_model__ . It is a temporary column required for AssignMeta
        and has to be dropped downstream"""
        raise NotImplementedError

    def party_type(self):
        """Populates parties.type in target_df by calling _party_type()"""
        self.target_df = pd.concat([self.target_df, self._party_type()], axis=1)

    @abstractmethod
    def _party_type(self) -> pd.DataFrame:
        """Abstract Method which needs to be implemented in the child class to populate
        parties.type"""
        raise NotImplementedError

    def quantity(self):
        """Populates date in target_df by calling _quantity()"""
        self.target_df = pd.concat([self.target_df, self._quantity()], axis=1)

    @abstractmethod
    def _quantity(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        quantity column"""
        raise NotImplementedError

    def quantity_notation(self):
        """Populates date in target_df by calling _quantity_notation()"""
        self.target_df = pd.concat([self.target_df, self._quantity_notation()], axis=1)

    @abstractmethod
    def _quantity_notation(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        quantityNotation column"""
        raise NotImplementedError

    def source_index(self):
        """Populates sourceIndex in target_df by calling _source_index()"""
        self.target_df = pd.concat([self.target_df, self._source_index()], axis=1)

    @abstractmethod
    def _source_index(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        mandatory column sourceIndex"""
        raise NotImplementedError

    def source_key(self):
        """Populates sourceKey in target_df by calling _source_key()"""
        self.target_df = pd.concat([self.target_df, self._source_key()], axis=1)

    @abstractmethod
    def _source_key(self) -> pd.DataFrame:
        """Abstract method which needs to be implemented in the child class to populate the
        mandatory column sourceKey"""
        raise NotImplementedError
