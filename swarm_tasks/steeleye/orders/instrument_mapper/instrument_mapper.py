import logging

import pandas as pd
from indict import Indict
from se_trades_tasks.order.universal.instrument_mapper.exceptions import (
    FailIfInstrumentsNotSubmittedToMasterDataApi,
)
from se_trades_tasks.order.universal.instrument_mapper.instrument_mapper import (
    run_instrument_mapper,
)
from se_trades_tasks.order.universal.instrument_mapper.schema import (
    InstrumentMapperResult,
)
from swarm.conf import Settings
from swarm.task.io.write.elastic.result import ElasticBulkWriterResult
from swarm.task.io.write.elastic.static import WriteStatus
from swarm.task.transform.base import BaseTask

logger_ = logging.getLogger(__name__)


class InstrumentMapper(BaseTask):
    """
    This task runs the InstrumentMapper task for each row in the final Orders Dataframe.
    This only runs for the orders which are successfully created in ElasticBulkWriter task.
    This deprecates the GenerateKafkaMessages task, which was earlier being consumed by
    KafkaMessageProducer task.
    """

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        bulk_writer_result: ElasticBulkWriterResult = None,
        **kwargs,
    ) -> InstrumentMapperResult:
        instrument_mapper_result = InstrumentMapperResult(
            number_of_submitted_instruments=0, master_data_api_response={}
        )
        bulk_writer_result_frame = bulk_writer_result.frame
        source_frame = source_frame.target

        apply_meta_fields = ["&id", "&key", "&model"]
        if not all([field in source_frame.columns for field in apply_meta_fields]):
            logger_.error(
                "Missing metadata in the resulting dataset. Downstream transformations won't be "
                "executed."
            )
            return instrument_mapper_result
        if bulk_writer_result_frame.empty or source_frame.empty:
            self.logger.info("Empty source, no instruments to map")
            return instrument_mapper_result
        status_frame = bulk_writer_result_frame[["status"]]
        if status_frame.empty:
            self.logger.info("Empty bulk_writer status frame, no instruments to map")
            return instrument_mapper_result
        data = pd.concat(
            [source_frame.reset_index(drop=True), status_frame.reset_index(drop=True)],
            axis=1,
        )
        total_orders = data.shape[0]
        orders_created_mask = data["status"] == WriteStatus.CREATED
        data = pd.DataFrame(
            Indict(
                obj=data.loc[orders_created_mask],
                from_dataframe_params={"date_format": "iso", "orient": "records"},
            )
            .remove_empty()
            .unflatten()
            .to_dict()
            .get("data")
        )
        try:
            instrument_mapper_result = run_instrument_mapper(
                unflattened_ingested_orders_df=data,
                tenant=Settings.tenant,
                stack=Settings.STACK,
            )

            self.logger.info(
                f"Instruments already mapped :{len(instrument_mapper_result.master_data_api_response.get('already_mapped', []))}"
            )
            self.logger.info(
                f"Instruments submitted for mapping :{len(instrument_mapper_result.master_data_api_response.get('submitted_for_map', []))}"
            )
        except FailIfInstrumentsNotSubmittedToMasterDataApi as err:
            # ToDo: When this is migrated to Conductor, we'd want this task to fail
            # This is because this can be an optional task and we'd want it to FAIL
            # so that the failures can be tracked. Here we don't want the prefect flow
            # to fail, hence we are logging the error and continuing with the flow.
            message = f"Failed to submit instruments to master-data-api due to: {err}"
            logger_.error(message)
        except Exception as e:
            logger_.error(f"Failed to map instruments due to: {e}")

        self.logger.info(f"Messages for Instruments Mapper :{data.shape[0]}")
        self.logger.info(
            f"Messages not eligible for Instruments Mapper:{total_orders - data.shape[0]}"
        )
        return instrument_mapper_result
