import os

import pandas as pd
from se_core_tasks.abstractions.transformations.abstract_market_counterparty_transformations import (
    AbstractMarketCounterpartyTransformations,
)
from se_core_tasks.abstractions.transformations.abstract_market_counterparty_transformations import (
    TradeFileIdLabels,
)
from se_core_tasks.abstractions.transformations.abstract_market_counterparty_transformations import (
    TradeFileIdsKeys,
)

from swarm_tasks.steeleye.market_counterparty.transformations.aladdin.static import (
    AladdinBrokerDerivedColumns,
)
from swarm_tasks.steeleye.market_counterparty.transformations.aladdin.static import (
    AladdinBrokerSourceColumns,
)
from swarm_tasks.utilities.static import MetaModel


class AladdinBrokerFirmTransformations(AbstractMarketCounterpartyTransformations):
    """
    Task which creates new firms from the Aladdin broker file. The cptyDeskId values have
    already been grouped together into a list for a single issuerLongName.

    Note that updates are handled separately outside this task.
    """

    def process(self):
        """Function which calls all the required functions and thus populates all required
        columns in the target df"""
        self.pre_process()
        self.name()
        self.meta_model()
        self.sink_identifiers_trade_file_identifiers()
        self.source_index()
        self.source_key()
        self.unique_ids()
        self.post_process()
        return self.target_df

    def _pre_process(self):
        """Not implemented"""

    def _post_process(self):
        """Not implemented"""

    def _details_firm_type(self) -> pd.Series:
        """Not populated"""

    def _details_mifid_registered(self) -> pd.Series:
        """Not populated"""

    def _details_retail_or_professional(self) -> pd.Series:
        """Not populated"""

    def _details_client_mandate(self) -> pd.Series:
        """Not populated"""

    def _details_firm_status(self) -> pd.Series:
        """Not Implemented"""

    def _details_in_eea(self) -> pd.Series:
        """Not populated"""

    def _name(self) -> pd.Series:
        """Populates MarketColumns.NAME using AladdinBrokerSourceColumns.ISSUER_LONG_NAME"""
        return self.source_frame.loc[:, AladdinBrokerSourceColumns.ISSUER_LONG_NAME]

    def _meta_model(self) -> pd.Series:
        """Populates aux/temp column MarketColumns.META_MODEL"""
        return pd.Series(
            data=MetaModel.MARKET_COUNTERPARTY, index=self.source_frame.index
        )

    def _firm_identifiers_branch_country(self) -> pd.Series:
        """Not implemented"""

    def _firm_identifiers_lei(self) -> pd.Series:
        """Not Implemented"""

    def _sink_identifiers_trade_file_identifiers(self) -> pd.Series:
        """Populated from cptyDeskId"""
        return self.source_frame.loc[:, AladdinBrokerDerivedColumns.ALL_CPTY_IDS].apply(
            lambda cpty_ids_list: [
                {
                    TradeFileIdsKeys.LABEL: TradeFileIdLabels.ID,
                    TradeFileIdsKeys.ID: cpty_id,
                }
                for cpty_id in cpty_ids_list
            ]
        )

    def _source_index(self) -> pd.Series:
        return pd.Series(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
        )

    def _source_key(self) -> pd.Series:
        return pd.Series(
            data=os.getenv("SWARM_FILE_URL"),
            index=self.source_frame.index,
        )

    def _unique_ids(self) -> pd.Series:
        """Populated from AladdinBrokerDerivedColumns.CPTY_PREFIX_IDS"""
        return self.source_frame.loc[:, AladdinBrokerDerivedColumns.CPTY_PREFIX_IDS]
