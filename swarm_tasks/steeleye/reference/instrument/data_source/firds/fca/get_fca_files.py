import json
from datetime import datetime
from typing import List
from typing import Optional

from prefect import context
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask

from swarm_tasks.utilities import requests_wrapper


logger = context.get("logger")


class Params(BaseParams):
    download_date: Optional[str] = None
    url: str = "https://api.data.fca.org.uk/fca_data_firds_files?q=((publication_date:%5b{download_date}%20TO%20{download_date}%5d))&from=0&size=100&pretty=true&sort=file_name:asc"


class GetFCAFiles(BaseTask):
    """
    Query FCA and get list of files published for given `download_date`.
    `download_date` can be passed in `flow_args` or `params` and defaults to run_date

    Params:
        download_date: file publication date
        url: FCA query URL for list of files published on given date
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        flow_args: Optional[str] = None,
        **kwargs,
    ) -> List[str]:

        global logger
        logger = context.get("logger")

        args = json.loads(flow_args) if flow_args else dict()

        # priority for download date: flow_args -> params -> run date
        download_date = args.get(
            "download_date", params.download_date or datetime.now().strftime("%Y-%m-%d")
        )
        logger.info(f"querying FCA for files published on `{download_date}`")

        url = params.url.format(download_date=download_date)

        response = requests_wrapper.get(url).json()
        file_urls = [i["_source"]["download_link"] for i in response["hits"]["hits"]]

        logger.info(f"list of files published on `{download_date}`: \n{file_urls}")

        return file_urls
