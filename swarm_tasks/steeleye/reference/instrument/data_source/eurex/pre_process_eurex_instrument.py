from typing import Dict
from typing import List
from typing import Optional

import pandas as pd
from prefect.engine.signals import FAIL
from se_trades_tasks.order_and_tr.instrument.fallback.instrument_fallback import (
    InstrumentFallback as Ifb,
)
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.generic.instrument_fallback.static import InstrumentFields
from swarm_tasks.steeleye.reference.instrument.data_source.eurex.static import (
    EurexXMLColumns,
)


class Resources(BaseResources):
    es_client_key: str


class DFColumns:
    ALT_ID_SRC_FOUR = "4"
    INST_ATTRB_VAL_106 = "106"


class PreProcessEurexInstrument(TransformBaseTask):
    """
    This task reads EUREX Security Reference Data - dataframe created from source xml file
    and filters and extracts required columns from it and returns filtered dataframe.
    Confluence page:https://steeleye.atlassian.net/wiki/spaces/IN/pages/1766785602/Eurex+Security+Reference+Data
    """

    resources_class = Resources

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[BaseParams] = None,
        resources: Optional[Resources] = None,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index)
        if source_frame.empty:
            return target

        columns_used = [
            EurexXMLColumns.INSTRMT,
            EurexXMLColumns.INSTRMTEXT,
            EurexXMLColumns.MKT_SEG_GRP,
        ]
        cols_mask = source_frame.columns.isin(columns_used)
        data = source_frame.loc[:, cols_mask]

        for col in columns_used:
            if col not in data.columns:
                error = f"Missing Column:{col} in source frame"
                self.logger.error(error)
                raise FAIL(error)

        data = self._derive_required_cols(data=data)

        target_columns = [
            *EurexXMLColumns().get_columns(),
            InstrumentFields.INSTRUMENT_ID_CODE,
            InstrumentFields.INSTRUMENT_CLASSIFICATION,
            InstrumentFields.VENUE_TRADING_VENUE,
            InstrumentFields.EXT_BEST_EX_ASSET_CLASS_SUB,
            InstrumentFields.EXT_BEST_EX_ASSET_CLASS_MAIN,
        ]

        target_columns_mask = data.columns.isin(target_columns)
        return data.loc[:, target_columns_mask]

    def _derive_required_cols(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        :param data: pd.Dataframe
        :return: pd.Dataframe
        This method extracts and assigns required columns to data.
        """
        for col in [
            EurexXMLColumns.INSTRMT,
            EurexXMLColumns.INSTRMTEXT,
            EurexXMLColumns.MKT_SEG_GRP,
        ]:
            data = pd.concat(
                [
                    data.drop([col], axis=1),
                    data[col].apply(lambda x: eval(x)).apply(pd.Series),
                ],
                axis=1,
            )
        # instrumentIdCode
        data.loc[:, InstrumentFields.INSTRUMENT_ID_CODE] = pd.NA
        aid_not_null_mask = data[EurexXMLColumns.AID].notnull()
        if aid_not_null_mask.any():
            data.loc[aid_not_null_mask, InstrumentFields.INSTRUMENT_ID_CODE] = data.loc[
                aid_not_null_mask, EurexXMLColumns.AID
            ].apply(
                lambda x: self._get_target_val(
                    source_col=x,
                    source_key=EurexXMLColumns.ALT_ID_SRC,
                    target_key=EurexXMLColumns.ALT_ID,
                    comparison_val=DFColumns.ALT_ID_SRC_FOUR,
                )
            )

        # CFI
        data.loc[:, InstrumentFields.INSTRUMENT_CLASSIFICATION] = data.loc[
            :, EurexXMLColumns.CFI
        ]

        # venue.tradingVenue
        data.loc[:, InstrumentFields.VENUE_TRADING_VENUE] = pd.NA
        inst_attrb_not_null_mask = data[EurexXMLColumns.INSTRATTRIB].notnull()
        if inst_attrb_not_null_mask.any():
            data.loc[
                inst_attrb_not_null_mask, InstrumentFields.VENUE_TRADING_VENUE
            ] = data.loc[inst_attrb_not_null_mask, EurexXMLColumns.INSTRATTRIB].apply(
                lambda x: self._get_target_val(
                    source_col=x,
                    source_key=EurexXMLColumns.TYP,
                    target_key=EurexXMLColumns.VAL,
                    comparison_val=DFColumns.INST_ATTRB_VAL_106,
                )
            )

        # EXT_BEST_EX_ASSET_CLASS_MAIN
        data.loc[:, InstrumentFields.EXT_BEST_EX_ASSET_CLASS_MAIN] = data.loc[
            :, EurexXMLColumns.CFI
        ].apply(
            lambda x: Ifb.get_best_ex_asset_class_main(cfi=x)
            if not pd.isna(x)
            else pd.NA
        )

        # EXT_BEST_EX_ASSET_CLASS_SUB
        data.loc[:, InstrumentFields.EXT_BEST_EX_ASSET_CLASS_SUB] = data.loc[
            :,
            [
                InstrumentFields.INSTRUMENT_CLASSIFICATION,
                InstrumentFields.EXT_BEST_EX_ASSET_CLASS_MAIN,
            ],
        ].apply(
            lambda x: Ifb.get_best_ex_asset_class_sub(
                cfi=x[InstrumentFields.INSTRUMENT_CLASSIFICATION],
                best_ex_asset_class_main=x[
                    InstrumentFields.EXT_BEST_EX_ASSET_CLASS_MAIN
                ],
            )
            if x.notnull().all()
            else pd.NA,
            axis=1,
        )

        required_cols = EurexXMLColumns().get_columns()
        for col in required_cols:
            if col not in data.columns:
                data.loc[:, col] = pd.NA

        # convert STRKPX and MULT cols to float
        for col in [EurexXMLColumns.STRKPX, EurexXMLColumns.MULT]:
            col_not_null_mask = data[col].notnull()
            data.loc[col_not_null_mask, col] = data.loc[col_not_null_mask, col].astype(
                float
            )

        return data

    @staticmethod
    def _get_target_val(
        source_col: List[Dict],
        source_key: str,
        target_key: str,
        comparison_val: str,
    ) -> Optional[str]:
        """
        :param source_col: List[Dict]
        :param source_key:  str
        :param target_key: str
        :param comparison_val: str
        :return: Optional[str]
        This method reads a list of Dicts and extracts result by comparing the source_key with comparison value
        from the source_col. Returns pd.NA if data not found.
        eg., source_col = [{'@AltID': '17236010', '@AltIDSrc': 'M'},
                           {'@AltID': 'DE000C5RQD52', '@AltIDSrc': '4'}]
        consider source_key = 'AltIDSrc'
                 comparison_value = '4'
                 target_key = '@AltID'
            then result =  'DE000C5RQD52'
        """
        result = next(
            (
                item.get(target_key, pd.NA)
                for item in source_col
                if isinstance(item, Dict) and item.get(source_key) == comparison_val
            ),
            pd.NA,
        )
        return result
