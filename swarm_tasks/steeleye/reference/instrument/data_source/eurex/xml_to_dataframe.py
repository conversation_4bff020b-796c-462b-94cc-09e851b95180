from typing import Dict
from typing import List
from typing import Optional

import pandas as pd
import xmltodict
from prefect.engine import signals
from pydantic import Field
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


class Params(BaseParams):
    process_namespaces: bool = False

    data_path: Optional[str] = Field(
        None,
        description="Dot delineated path to start reading from a nested point "
        "in the xml document. All xml tags on the same level as `data_path` will have "
        "all of its nested fields as a OrderedDict, on each column"
        "of the output DataFrame",
    )
    namespaces: Optional[dict] = Field(
        None,
        description="dictionary of namespaces to collapse to shorthand prefixes, "
        "or to skip altogether. Format: {namespace_to_collapse: replace_value}",
    )
    encoding: Optional[str] = Field(
        "utf-8", description="Encoding to be passed into path.open()"
    )


class TempCols:
    MKT_SEG_ID = "@MktSegID"
    CURRENCY = "@Ccy"


class XMLToDataframe(BaseTask):
    """
    Read and parse an XML file, load it into a Pandas DataFrame and returns the DataFrame
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        extractor_result: ExtractPathResult = None,
        **kwargs,
    ) -> pd.DataFrame:

        xml_config = dict(
            namespaces=params.namespaces, process_namespaces=params.process_namespaces
        )
        with extractor_result.path.open(encoding=params.encoding) as f:
            raw_data = f.read()

        if not raw_data:
            raise signals.SKIP("Empty xml or no data to convert to DF")

        xml = xmltodict.parse(raw_data, **xml_config)

        data = self._nested_read(xml, *params.data_path.split("."))

        df = pd.DataFrame(data)
        required_cols = [TempCols.MKT_SEG_ID, TempCols.CURRENCY]
        cols_mask = df.columns.isin(required_cols)
        return df.loc[:, cols_mask]

    def _parse_nested_xml(self, data_path: str, xml_content: Dict) -> List[Dict]:
        """
        Parse the contents of a nested XML file by flattening all nested tags (concatenate parent tags by "." separator)
        Example:
            data_path = "REPORT.DATA.ROW"
            xml_content = Dictionary from raw data:

            <REPORT xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <ISSUE_CODE>N</ISSUE_CODE>
                <DATA>
                    <ROW>
                        <ISIN>GB00H1111111</ISIN>
                        <CONTRACT_TYPE>ALUM</CONTRACT_TYPE>
                    </ROW>
                <ROW>
                    <ISIN>GB00H2222222</ISIN>
                    <CONTRACT_TYPE>ALUM</CONTRACT_TYPE>
                </ROW>

        Returns:
            [{"REPORT.DATA.ROW.ISIN":"GB00H1111111", "REPORT.DATA.ROW.CONTRACT_TYPE":"ALUM"},
            {"REPORT.DATA.ROW.ISIN":"GB00H2222222", "REPORT.DATA.ROW.CONTRACT_TYPE":"ALUM"}]

        :param data_path: Dot separated nested path towards the XML tag whose elements will be parsed
        :param xml_content: XML content as dictionary
        :return: List of flattened dictionaries, where each key:value pair is the flattened nested XML tag name
        (dot separated) and the associated value. The number of dictionaries is the same as the number of `data_path`
        XML Tag occurrences
        """

        data_path_nested_fields = data_path.split(".")
        nested_data = xml_content.get(data_path_nested_fields.pop(0), {})

        for item in data_path_nested_fields:
            nested_data = nested_data.get(item, {})

        if not isinstance(nested_data, list):
            nested_data = [nested_data]

        data = [
            self._flatten_dict(
                nested_xml_element, parent_key=data_path, xml_category_text_suffix=True
            )
            for nested_xml_element in nested_data
        ]

        return data

    @staticmethod
    def _flatten_dict(
        input_dict: Dict,
        output_dict: Optional[Dict] = None,
        parent_key: Optional[str] = None,
        separator=".",
        xml_category_text_suffix=False,
    ) -> Dict:
        """
        Flattens a nested dictionary - nested keys will always be prefixed by the parent key separated by `separator`

        :param input_dict: Nested dictionary to be flattened
        :param output_dict: Recursive call to one of the input_dict nested dictionaries
        :param parent_key: Parent key prefix that will be present in `output_dict`
        :param separator: Separator between parent and child dictionary keys
        :param xml_category_text_suffix: Hack to remove the #text suffix for XML elements with a specified category
        example - <NmnlVal Ccy="EUR">10159500</NmnlVal> (parsing this element generates the #text suffix for `NmlVal`)
        :return: Flattened dictionary
        """

        output_dict_result = {} if output_dict is None else output_dict

        for key, value in input_dict.items():
            key = f"{parent_key}{separator}{key}" if parent_key else key

            if isinstance(value, dict):
                XMLToDataframe._flatten_dict(
                    input_dict=value,
                    output_dict=output_dict_result,
                    parent_key=key,
                    xml_category_text_suffix=xml_category_text_suffix,
                )
                continue

            key = (
                key.replace(f"{separator}#text", "")
                if xml_category_text_suffix
                else key
            )
            output_dict_result[key] = value

        return output_dict_result

    def _nested_read(
        self, dict_obj: dict, next_key: str, *remaining_keys: tuple
    ) -> list:

        next_step = dict_obj.get(next_key)
        if isinstance(next_step, dict) and remaining_keys:
            return self._nested_read(next_step, *remaining_keys)
        else:
            return next_step
