from typing import List
from typing import Optional

import pandas as pd
from prefect.engine.signals import FAIL
from prefect.engine.signals import SKIP
from se_elastic_schema.models.reference.instrument import Instrument
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.generic.instrument_fallback.static import InstrumentFields
from swarm_tasks.steeleye.reference.instrument.data_source.eurex import static
from swarm_tasks.steeleye.reference.instrument.data_source.eurex.static import (
    EurexXMLColumns,
)
from swarm_tasks.steeleye.reference.instrument.data_source.eurex.static import (
    SecurityTypeRegex,
)


class Resources(BaseResources):
    es_client_key: str


class Params(BaseParams):
    currency: str = "__currency__"


class FilterExistingInstruments(TransformBaseTask):
    """
    This tasks filter the instruments already present.
    This is used to filter out data that is not relevant.
    Filter conditions:
            1. Should be Options or Futures.
            2. Derived ext.instrumentUniqueIdentifier should not be null.
            3. Filters out existing instruments from tenant-data.
    """

    params_class = Params
    resources_class = Resources

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[BaseParams] = None,
        resources: Optional[Resources] = None,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index)
        if source_frame.empty:
            return target

        data = source_frame.copy()

        data.loc[:, InstrumentFields.EXT_INSTRUMENT_UNIQUE_ID] = (
            data.loc[:, InstrumentFields.VENUE_TRADING_VENUE].fillna(static.XEUR)
            + data.loc[:, InstrumentFields.INSTRUMENT_ID_CODE]
            + data.loc[:, params.currency]
        )
        filtered_data: pd.DataFrame = self._filter_data(data=data, resources=resources)
        if filtered_data.empty:
            raise SKIP("No new valid instruments to process.")

        return filtered_data

    def _filter_data(self, data: pd.DataFrame, resources: Resources) -> pd.DataFrame:
        """
        This method is used to filter out data that is not relevant.
        Filter conditions:
            1. Should be Options or Futures.
            2. Derived ext.instrumentUniqueIdentifier should not be null.
            3. Filters out existing instruments from tenant-data.

        :param data: pd.Dataframe
        :param resources: Resources
        :return: pd.Dataframe
        """
        # Step 1 & 2
        option_and_future_with_id_mask = data[
            InstrumentFields.EXT_INSTRUMENT_UNIQUE_ID
        ].notnull() & data[EurexXMLColumns.SECTYP].str.match(
            f"{SecurityTypeRegex.OPTION}|{SecurityTypeRegex.FUTURE}",
            case=False,
            na=False,
        )
        if not option_and_future_with_id_mask.any():
            raise SKIP(
                "No instruments(Options/Futures) with valid EXT_INSTRUMENT_UNIQUE_ID."
            )

        data = data.loc[option_and_future_with_id_mask]

        existing_instruments: List[str] = self._fetch_existing_instruments(
            data=data,
            option_and_future_with_id_mask=option_and_future_with_id_mask,
            resources=resources,
        )

        if not existing_instruments:
            return data

        self.logger.info(
            f"Skipping {len(existing_instruments)}:existing instruments and processing rest."
        )
        existing_instrument_mask = data[InstrumentFields.EXT_INSTRUMENT_UNIQUE_ID].isin(
            existing_instruments
        )

        # Step 3
        return data.loc[~existing_instrument_mask]

    def _fetch_existing_instruments(
        self,
        data: pd.DataFrame,
        option_and_future_with_id_mask: pd.Series.bool,
        resources: Resources,
    ) -> List[str]:
        """
        This method fetches existing instruments from tenant-data as a list.

        :param data: pd.DataFrame
        :param option_and_future_with_id_mask: pd.Series.bool
        :param resources: Resources
        :return: List[str]
        """
        existing_instruments = []
        tenant = Settings.tenant
        alias = Instrument.get_elastic_index_alias(tenant=tenant)
        es_client = Settings.connections.get(resources.es_client_key)
        batch_size = 1000
        try:
            self.logger.info(
                f"Fetching existing instruments from tenant-data for tenant:{tenant}"
            )
            unique_instrument_ids = data.loc[
                option_and_future_with_id_mask,
                InstrumentFields.EXT_INSTRUMENT_UNIQUE_ID,
            ].to_list()
            for i in range(0, len(unique_instrument_ids), batch_size):
                self.logger.info(
                    f"Looking for instruments in batches of 1000, batch no. : {int(i/1000) + 1}"
                )
                batch_instrument_ids = unique_instrument_ids[i : i + batch_size]
                query = self._get_query(
                    batch_instrument_ids=batch_instrument_ids, batch_size=batch_size
                )
                response_df = self.es_scroll(alias, es_client, query)
                if response_df.empty:
                    continue
                existing_instruments.extend(
                    response_df[InstrumentFields.EXT_INSTRUMENT_UNIQUE_ID].to_list()
                )
            if not existing_instruments:
                self.logger.info(
                    "No existing instruments found. Processing all source instruments"
                )
            return existing_instruments
        except Exception as e:
            self.logger.error(f"Error fetching existing instruments. Exception: {e}.")
            raise FAIL(str(e))

    def es_scroll(self, alias, es_client, query):
        response_df = es_client.scroll(query=query, index=alias)
        return response_df

    @staticmethod
    def _get_query(batch_instrument_ids: List[str], batch_size: int) -> dict:
        """
        Returns the query required to fetch existing instruments from tenant 'Instrument' model.

        :param batch_instrument_ids: List[str]
        :param batch_size: int
        :return: dict
        """
        query = {
            "size": batch_size,
            "_source": [InstrumentFields.EXT_INSTRUMENT_UNIQUE_ID],
            "query": {
                "bool": {
                    "must": [
                        {
                            "terms": {
                                "ext.instrumentUniqueIdentifier": batch_instrument_ids
                            }
                        }
                    ]
                }
            },
        }
        return query
