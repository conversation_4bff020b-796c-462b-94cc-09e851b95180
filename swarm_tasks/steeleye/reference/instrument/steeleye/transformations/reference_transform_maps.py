from se_core_tasks.abstractions.transformations.transform_map import TransformMap

from swarm_tasks.steeleye.reference.instrument.steeleye.cfd_sb_transformer.cfd_sb_transformer_commodity_transformations import (
    CFDSBTransformerCommodityTransformations,
)
from swarm_tasks.steeleye.reference.instrument.steeleye.cfd_sb_transformer.cfd_sb_transformer_index_transformations import (
    CFDSBTransformerIndexTransformations,
)

cfdsb_commodity_transformer_transform_map = TransformMap(
    default=CFDSBTransformerCommodityTransformations,
    map={},
)

cfdsb_index_transformer_transform_map = TransformMap(
    default=CFDSBTransformerIndexTransformations,
    map={},
)
