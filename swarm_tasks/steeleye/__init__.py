from typing import Any
from typing import Optional

from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


class DummyTask(BaseTask):
    def execute(
        self,
        params: Optional[BaseParams] = None,
        resources: Optional[BaseResources] = None,
        **kwargs,
    ) -> Any:
        self.logger.warning(f"{self.__class__.__name__} ran!")
        return True
