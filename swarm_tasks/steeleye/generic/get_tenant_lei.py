import logging

import pandas as pd
from se_core_tasks.feeds.generic.get_tenant_lei import Params as GenericParams
from se_core_tasks.feeds.generic.get_tenant_lei import run_get_tenant_lei
from se_elasticsearch.repository import AnyElasticsearchRepositoryType
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


logger_ = logging.getLogger(__name__)


class Params(BaseParams, GenericParams):
    pass


class GetTenantLEI(TransformBaseTask):
    """This task fetches the LEI from the tenant's AccountFirm record, and populates
    it in every row of the target column params.target_lei_column.
    Each tenant has one AccountFirm record, and the LEI can be fetched from the
    firmIdentifiers.lei field in that record.

    Note: Normally, the bundle author is expected to set the param
    target_column_prefix = "lei:". Only leave it blank if you're using a bespoke
    PartyIdentifiers task which automatically adds a prefix.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:
        return self.process(source_frame=source_frame, params=params)

    @classmethod
    def process(cls, source_frame: pd.DataFrame = None, params: Params = None):
        es_client: AnyElasticsearchRepositoryType = Settings.connections.get(
            "tenant-data"
        )

        return run_get_tenant_lei(
            source_frame=source_frame,
            params=params,
            tenant=Settings.tenant,
            es_client=es_client,
        )
