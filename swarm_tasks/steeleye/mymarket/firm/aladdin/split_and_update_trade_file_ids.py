import logging
from enum import Enum
from typing import Dict
from typing import List

import pandas as pd
from prefect.engine import signals
from se_core_tasks.abstractions.transformations.abstract_market_counterparty_transformations import (
    MarketColumns,
)
from se_core_tasks.abstractions.transformations.abstract_market_counterparty_transformations import (
    TradeFileIdLabels,
)
from se_core_tasks.abstractions.transformations.abstract_market_counterparty_transformations import (
    TradeFileIdsKeys,
)
from se_core_tasks.utils.es_utils import get_terms_query
from se_elastic_schema.models import MarketCounterparty
from se_trades_tasks.order_and_tr.static import PartyPrefix
from swarm.conf import Settings
from swarm.task.base import BaseTask
from swarm.task.io.read import FrameProducerResult
from swarm.task.transform.result import TransformResult

from swarm_tasks.steeleye.market_counterparty.transformations.aladdin.static import (
    AladdinBrokerDerivedColumns,
)
from swarm_tasks.steeleye.market_counterparty.transformations.aladdin.static import (
    AladdinBrokerSourceColumns,
)
from swarm_tasks.utilities.es.query_utils import es_scroll
from swarm_tasks.utilities.static import MetaModel

logger = logging.getLogger(__name__)


class TempColumns:
    MERGE_INDICATOR_COLUMN = "merge_indicator_column"


class ActionType(Enum):
    CREATE = "create"
    UPDATE = "update"


class SplitAndUpdateTradeFileIds(BaseTask):
    """
    This task receives a FrameProducerResult containing firm data which
    may or may not be present in Elasticsearch.
    First, it groups together the df by issuerLongName and concatenates
    all the cptyDeskIds and cptyIds (which is actually only 1 for
    a firm)of a group in a list.

    It then looks up the issuerLongName in the MarketCounterparty model
     in Elasticsearch (using MarketCounterparty.name)
    If there is a match, the trade file identifiers in Elasticsearch are updated
    (if any trade file ids are present) or added from the source file (the
    trade file ids in the source file are in cptyDeskId and cptyId columns,
    which have now been concatenated into a single list).

    The task returns a dictionary with either one or two keys.
    If there are records to be created and updated, a dict with 2 keys is returned
    {'create': TransfromResult(create_df), 'update': TransfromResult(update_df)}.

    If all the records are new, a dict with only the key 'create' is returned.
    {'create': TransfromResult(create_df)}
    In this case, the update branch of the flow is skipped downstream.
    Similarly, if all the records are updated, a dict with only the key 'update'
    is returned.
    {'update': TransfromResult(update_df)}
    In this case, the 'create' branch of the flow is skipped downstream.
    """

    def execute(
        self,
        source_frame: FrameProducerResult = None,
        **kwargs,
    ) -> Dict[ActionType, TransformResult]:
        return self.process(
            source_frame=source_frame,
        )

    @classmethod
    def process(
        cls,
        source_frame: FrameProducerResult = None,
    ) -> Dict[ActionType, TransformResult]:
        """Class method which filters out duplicate MarketCounterparty
        records based on the LEI or name, and removes rows without a name.
        """
        if source_frame.frame.empty or any(
            col not in source_frame.frame.columns
            for col in [
                AladdinBrokerSourceColumns.CPTY_DESK_ID,
                AladdinBrokerSourceColumns.CPTY_ID,
                AladdinBrokerSourceColumns.ISSUER_LONG_NAME,
            ]
        ):
            raise signals.SKIP("Source frame empty or required columns not present")

        # Group by name and get the
        grouped_df = (
            source_frame.frame.groupby(AladdinBrokerSourceColumns.ISSUER_LONG_NAME)[
                AladdinBrokerSourceColumns.CPTY_DESK_ID,
                AladdinBrokerSourceColumns.CPTY_ID,
            ]
            .agg(set)
            .reset_index()
        )

        grouped_df[AladdinBrokerDerivedColumns.ALL_CPTY_IDS] = grouped_df[
            AladdinBrokerSourceColumns.CPTY_DESK_ID
        ].combine(
            grouped_df[AladdinBrokerSourceColumns.CPTY_ID],
            lambda x, y: list()
            if pd.isnull(x) and pd.isnull(y)
            else list(y)
            if pd.isnull(x)
            else list(x)
            if pd.isnull(y)
            else list(x.union(y)),
        )
        grouped_df = grouped_df.drop(
            columns=[
                AladdinBrokerSourceColumns.CPTY_DESK_ID,
                AladdinBrokerSourceColumns.CPTY_ID,
            ]
        )

        grouped_df.loc[:, AladdinBrokerDerivedColumns.CPTY_PREFIX_IDS] = grouped_df.loc[
            :, AladdinBrokerDerivedColumns.ALL_CPTY_IDS
        ].apply(lambda x: [f"{PartyPrefix.ID}{cpty_id}" for cpty_id in x])

        # Combine
        unique_ids_to_lookup = (
            grouped_df.loc[:, AladdinBrokerSourceColumns.ISSUER_LONG_NAME]
            .astype("str")
            .tolist()
        )
        es_client = Settings.connections.get("tenant-data")
        fetched_df = cls._lookup_elasticsearch(
            unique_ids=unique_ids_to_lookup,
            tenant=Settings.tenant,
            es_client=es_client,
            lookup_column=MarketColumns.NAME,
        )

        if fetched_df.empty:
            logger.info("All records are new, nothing to update")
            # All records need to be created. No merge required. Return
            # a dict with only a 'create' key. Updates will be skipped downstream
            return {ActionType.CREATE.value: TransformResult(target=grouped_df)}

        source_columns = grouped_df.columns
        firm_columns = fetched_df.columns

        # Drop duplicates in fetched_df in case there are 2 firms with the same name
        fetched_df = fetched_df.drop_duplicates(subset=[MarketColumns.NAME])

        grouped_df = pd.merge(
            left=grouped_df,
            right=fetched_df,
            left_on=AladdinBrokerSourceColumns.ISSUER_LONG_NAME,
            right_on=MarketColumns.NAME,
            how="left",
            indicator=TempColumns.MERGE_INDICATOR_COLUMN,
        )
        updated_mask = grouped_df.loc[:, TempColumns.MERGE_INDICATOR_COLUMN].eq("both")
        update_df = grouped_df.loc[updated_mask].reset_index(drop=True)
        update_df = cls._process_updates(update_df=update_df, es_client=es_client)
        # Drop all non-Elastic columns
        update_df = update_df.drop(
            columns=list(source_columns) + [TempColumns.MERGE_INDICATOR_COLUMN]
        )

        created_mask = grouped_df.loc[:, TempColumns.MERGE_INDICATOR_COLUMN].eq(
            "left_only"
        )
        create_df = grouped_df.loc[created_mask].reset_index(drop=True)
        # All updates
        if create_df.empty:
            # Return a dict with only updates. Creates will be skipped downstream
            return {
                ActionType.UPDATE.value: TransformResult(target=update_df),
            }

        # We cannot have an empty update_df, as at least one record will have
        # TempColumns.MERGE_INDICATOR_COLUMN=both. If there are no updates, we would
        # have already gone into the if condition fetched_df.empty

        create_df = create_df.drop(
            columns=list(firm_columns) + [TempColumns.MERGE_INDICATOR_COLUMN]
        )
        return {
            ActionType.CREATE.value: TransformResult(target=create_df),
            ActionType.UPDATE.value: TransformResult(target=update_df),
        }

    @classmethod
    def _process_updates(cls, update_df: pd.DataFrame, es_client):
        """
        Returns an Elastic record after updating the trade file identifiers
        if there is a new value in params.source_trade_file_id_column

        :param update_df: MarketCounterparty record with all fields
        :param es_client: ES Client used to get unique Props col name
        :returns: update_df with sinkIdentifiers.tradeFileIdentifiers
        updated
        """
        if MarketColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS not in update_df:
            update_df[MarketColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS] = pd.NA

        update_df.loc[
            :, MarketColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS
        ] = update_df.loc[
            :,
            [
                MarketColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS,
                AladdinBrokerDerivedColumns.ALL_CPTY_IDS,
            ],
        ].apply(
            lambda x: cls._update_trade_file_ids(row=x), axis=1
        )

        # All Unique Ids are numbers, so we don't have to convert them to lower case
        unique_ids_null_mask = update_df.loc[:, MarketColumns.UNIQUE_IDS].isnull()
        update_df.loc[~unique_ids_null_mask, MarketColumns.UNIQUE_IDS] = update_df.loc[
            ~unique_ids_null_mask,
            [MarketColumns.UNIQUE_IDS, AladdinBrokerDerivedColumns.CPTY_PREFIX_IDS],
        ].apply(
            lambda x: list(
                set(
                    x[MarketColumns.UNIQUE_IDS]
                    + x[AladdinBrokerDerivedColumns.CPTY_PREFIX_IDS]
                )
            ),
            axis=1,
        )

        update_df.loc[unique_ids_null_mask, MarketColumns.UNIQUE_IDS] = update_df.loc[
            unique_ids_null_mask, AladdinBrokerDerivedColumns.CPTY_PREFIX_IDS
        ]

        # Set uniqueProps = uniqueIds
        update_df.loc[:, es_client.meta.unique_props] = update_df.loc[
            :, MarketColumns.UNIQUE_IDS
        ]

        return update_df

    @staticmethod
    def _update_trade_file_ids(row: pd.Series) -> List[dict]:
        """
        Gets a data frame containing a trade file identifiers column and
        params.source_trade_file_id_column, and does the following:
        1. If MarketColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS is null,
        create a list with 1 dict foe each new value of ALL_CPTY_IDS
        2. If MarketColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS exists,
        create a new dict value = row[ALL_CPTY_IDS] and add
        it to the existing list

        :param row: Series

        """
        # Check if the id in df1 is present in any of the dicts in TradeFileIds
        cpty_ids_list = row[AladdinBrokerDerivedColumns.ALL_CPTY_IDS]
        trade_file_identifiers = row[
            MarketColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS
        ]
        if not isinstance(trade_file_identifiers, list) and pd.isna(
            trade_file_identifiers
        ):
            return [
                {
                    TradeFileIdsKeys.LABEL: TradeFileIdLabels.ID,
                    TradeFileIdsKeys.ID: cpty_id,
                }
                for cpty_id in cpty_ids_list
            ]

        trade_file_ids_set = set(
            item[TradeFileIdsKeys.ID] for item in trade_file_identifiers
        )

        new_dicts = [
            {
                TradeFileIdsKeys.LABEL: TradeFileIdLabels.ID,
                TradeFileIdsKeys.ID: cpty_id,
            }
            for cpty_id in cpty_ids_list
            if cpty_id not in trade_file_ids_set
        ]

        trade_file_identifiers.extend(new_dicts)
        return trade_file_identifiers

    @classmethod
    def _lookup_elasticsearch(
        cls,
        unique_ids: List[str],
        tenant: str,
        lookup_column: str,
        es_client,
    ):
        """
        Looks up the message model in Elasticsearch for the given tenant for the given
        set of ids.
        :param unique_ids: Unique list of ids to lookup
        :param tenant: Tenant to look up
        :param lookup_column: Lookup column
        :param es_client: Elasticsearch client
        :return: Dataframe containing elastic records for the given ids
        """
        query = get_terms_query(
            ids=unique_ids,
            es_client=es_client,
            lookup_field=lookup_column,
            model_field=MetaModel.MARKET_COUNTERPARTY,
        )
        alias = MarketCounterparty.get_elastic_index_alias(tenant=tenant)
        logger.info(f"Querying Elasticsearch for {len(unique_ids)} ids")
        return es_scroll(es_client=es_client, index=alias, query=query)
