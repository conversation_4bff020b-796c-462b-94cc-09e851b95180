import logging
from typing import Dict
from typing import List
from typing import Optional
from typing import Tuple

import pandas as pd
from prefect import context
from prefect.engine import signals
from pydantic import Field
from swarm.conf import Settings
from swarm.task.auditor import Auditor
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.steeleye.market_counterparty.transformations.kyte.kyte_transformations import (
    SourceColumns,
)
from swarm_tasks.steeleye.mymarket.firm.helper_functions import (
    audit_duplicate_ids_in_file,
)
from swarm_tasks.steeleye.mymarket.firm.helper_functions import (
    audit_ids_already_in_elastic,
)
from swarm_tasks.steeleye.mymarket.firm.helper_functions import (
    fetch_counterparty_records_from_elastic,
)
from swarm_tasks.steeleye.mymarket.firm.utils import UpdateColumns
from swarm_tasks.utilities.static import MetaModel


class Params(BaseParams):
    source_name_column: str = Field(
        ..., description="Source column which contains the name"
    )
    source_lei_column: str = Field(
        ...,
        description="Source column which contains the LEI",
    )
    elastic_lei_lookup_column: str = Field(
        default="firmIdentifiers.lei",
        description="MarketCounterparty LEI column against which the source_lei_column is matched",
    )
    keep_firms_to_update: bool = Field(
        default=False,
        description="If `True` do not discard Firms found with the same LEI in ES that have different identifiers."
        "These will be kept to be updated downstream, along with a mask under the column `__to_update__`",
    )


class FilterOutDuplicateAndInvalidFirms(TransformBaseTask):
    """
    This task receives a dataframe containing MarketCounterparty data. It
    performs 3 tasks: (1) It removes rows with null name AND null LEI.
    (2) It removes any duplicate rows in the source frame based on the LEI field.
    (3) It looks up the LEIs in Elasticsearch (in MarketCounterparty) and keeps only
    those records which are NOT found.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:
        return self.process(
            source_frame=source_frame,
            params=params,
            logger=self.logger,
            auditor=self.auditor,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        logger=context.get("logger"),
        auditor: Auditor = None,
    ) -> pd.DataFrame:
        """Class method which filters out duplicate MarketCounterparty
        records based on the LEI or name, and removes rows without a name.
        """
        if (
            source_frame.empty
            or params.source_name_column not in source_frame.columns
            or params.source_lei_column not in source_frame.columns
        ):
            raise signals.SKIP("Source frame empty or required column not present")

        es_client = Settings.connections.get("tenant-data")
        # Remove rows with neither a name nor an LEI
        name_and_lei_null_mask = (
            source_frame.loc[:, params.source_name_column].isnull()
            & source_frame.loc[:, params.source_lei_column].isnull()
        )
        num_no_name_no_lei_records = name_and_lei_null_mask.sum()
        # Audit the number of records with null names and LEIs
        if num_no_name_no_lei_records > 0:
            no_name_message = (
                f"{num_no_name_no_lei_records} record(s) in the file have neither a name nor an LEI."
                f" They have been dropped."
            )
            auditor.add(message=no_name_message)
            logger.warning(no_name_message)

        df = source_frame.loc[~name_and_lei_null_mask].reset_index(drop=True)

        # Remove duplicate rows based on the LEI
        not_null_lei_mask = df.loc[:, params.source_lei_column].notnull()
        duplicated_lei_mask = df.duplicated(subset=[params.source_lei_column])

        # If there are 2 NAs, they are treated as duplicates, this additional mask
        # prevents that. We don't want to drop null LEIs
        duplicated_lei_mask = not_null_lei_mask & duplicated_lei_mask

        # Audit duplicate records based on the LEI column
        audit_duplicate_ids_in_file(
            df=df,
            duplicated_mask=duplicated_lei_mask,
            source_col=params.source_lei_column,
            logger=logger,
            auditor=auditor,
        )

        df = df.loc[~duplicated_lei_mask].reset_index(drop=True)

        target = cls._process_counterparty_records(
            df=df,
            es_client=es_client,
            source_lei_column=params.source_lei_column,
            lookup_lei_column=params.elastic_lei_lookup_column,
            params=params,
            logger=logger,
            auditor=auditor,
        )

        return target.fillna(pd.NA)

    @classmethod
    def _process_counterparty_records(
        cls,
        df: pd.DataFrame,
        es_client,
        source_lei_column: str,
        lookup_lei_column: str,
        params: Params,
        logger: logging.Logger,
        auditor: Auditor,
    ) -> pd.DataFrame:
        """Processes counterparty records. It retrieves the records from Elastic
        which have the same LEIs as the records in the source data frame.
        It then returns only records in the source frame which DO NOT have the same
        LEI as a record from Elastic.
        If params.keep_firms_to_update is True avoids discarding LEIs already in ES
            and includes a column mask indicating they shuold be updated.
        :param: df: source_df containing records for a client
        :param: es_client: ElasticsearchClient instance
        :param: source_lei_column: name of the field containing the LEI, values from this
                column are searched in MarketCounterparty
        :param: lookup_lei_column: name of the LEI lookup column in MarketCounterparty
        :param: params: task Params
        :param: logger: Logger for logging messages
        :param: auditor: Auditor to write into SinkFileAudit
        :returns: DataFrame containing records where the records were not found
                  in Elasticsearch
        """
        # Part 1: drop LEIs that are already in Elastic
        leis_list = list(set(df.loc[:, source_lei_column].dropna().tolist()))
        logger.info(
            f"Fetching {len(leis_list)} LEI(s) from {MetaModel.MARKET_COUNTERPARTY}"
        )
        fetched_lei_counterparty_records = fetch_counterparty_records_from_elastic(
            ids=leis_list,
            es_client=es_client,
            lookup_column=lookup_lei_column,
            logger=logger,
        )
        if fetched_lei_counterparty_records.empty:
            if leis_list:
                logger.info(
                    f"No records found in Elastic with the following LEI(s): {leis_list}"
                )
            return df

        else:
            join_lei_target = df.reset_index(drop=True).merge(
                fetched_lei_counterparty_records,
                how="left",
                left_on=[source_lei_column],
                right_on=[lookup_lei_column],
                indicator="ind",
            )

            # drop_duplicates() is necessary because each LEI in df might match with
            # 2 LEIs from fetched_counterparty_records (if the SRP LeiRecord model has
            # duplicate entries for an lei. But we need to keep null LEIs
            dup_in_join_lei_target_mask = join_lei_target.duplicated(
                subset=[source_lei_column]
            ) & (join_lei_target[source_lei_column].notnull())
            join_lei_target = join_lei_target.loc[~dup_in_join_lei_target_mask]

            # This is where we actually filter out records which are already in Elastic based on LEI
            duplicates_mask = join_lei_target.loc[:, "ind"] == "both"

            if params.keep_firms_to_update:
                # get masks to distinguish orders that need to be updated and orders that are actual duplicates
                to_update_mask, duplicates_mask = cls.get_update_and_duplicate_mask(
                    firms_dataframe=join_lei_target, lei_in_es_mask=duplicates_mask
                )

            # Audit LEIs which are already in Elastic
            audit_ids_already_in_elastic(
                duplicates_df=join_lei_target.loc[duplicates_mask, :],
                source_col=source_lei_column,
                logger=logger,
                auditor=auditor,
            )

            target = join_lei_target.loc[~duplicates_mask, :].drop(
                ["ind", lookup_lei_column], axis=1
            )

            if target.empty:
                all_dup_message = "All records in input file are already in the database. Skipping rest of the flow"
                auditor.add(message=all_dup_message)
                raise signals.SKIP(all_dup_message)

            if params.keep_firms_to_update:
                target = pd.concat(
                    [
                        target,
                        pd.Series(
                            data=to_update_mask,
                            index=target.index,
                            name=UpdateColumns.TO_UPDATE,
                        ),
                    ],
                    axis=1,
                )

            # During the Merge above the column name from left df will be renamed to name_x as the right df also
            # contains the column with same name. So after the merge and keeping columns only from left we need to
            # rename the source column back so that it can be processed properly in downstream tasks.
            return target.rename(columns={"name_x": SourceColumns.NAME}).reset_index(
                drop=True
            )

    @staticmethod
    def format_source_ids(row: pd.Series) -> List[str]:
        """
        Formats the source file Ids into a sorted list
        :param row: Series with `firmwideClientId` and `groupwideClientId`
        :return: Sorted id list
        """
        return sorted(
            [
                x
                for x in [
                    row[SourceColumns.FIRMWIDE_CLIENT_ID],
                    row[SourceColumns.GROUPWIDE_CLIENT_ID],
                ]
                if not pd.isna(x)
            ]
        )

    @staticmethod
    def format_es_ids(es_id: Optional[List[Dict[str, str]]]) -> List[str]:
        """
        Formats ids fetched from ElasticSearch into a sorted list
        :param es_id: Id list fetched from Es
        :return: Sorted id list
        """
        if isinstance(es_id, List):
            return sorted(
                [
                    identifier.get("id")
                    for identifier in es_id
                    if identifier.get("label") == "id"
                ]
            )
        elif pd.isna(es_id) or not es_id:
            return []

    @staticmethod
    def check_if_new_ids(row: pd.Series) -> bool:
        """
        Compares if any of the `source_ids` is missing from `es_ids`
        :param row: dataframe row with `source_ids` and `es_ids`
        :return: bool indicating if any id from `source_ids` is missing from `es_ids`
        """
        source_ids = row.get("source_ids")
        es_ids = row.get("es_ids")
        for source_id in source_ids:
            if source_id not in es_ids:
                return True
        return False

    @classmethod
    def get_update_and_duplicate_mask(
        cls, firms_dataframe: pd.DataFrame, lei_in_es_mask: pd.Series
    ) -> Tuple[pd.Series, pd.Series]:
        """
        It compares the identifiers present in the source file with the identifiers
            already present in the matching LEI firm in elasticsearch. Providing a more
            acurate view on which are duplicates
        Creates two masks:
            one indicating if the record should be updated,
            one indicating if the record is, in fact, a duplicate
        :param firms_dataframe: dataframe with necessary columns
        :param lei_in_es_mask: mask indicating matching LEI
        :return: update mask and duplicates mask
        """
        duplicate_status_df = pd.DataFrame(index=firms_dataframe.index)
        duplicate_status_df["source_ids"] = firms_dataframe.loc[
            :, [SourceColumns.FIRMWIDE_CLIENT_ID, SourceColumns.GROUPWIDE_CLIENT_ID]
        ].apply(lambda x: cls.format_source_ids(x), axis=1)
        duplicate_status_df["es_ids"] = (
            firms_dataframe.loc[:, UpdateColumns.SINK_IDENTIFIERS_TRADE_IDS]
            .fillna(pd.NA)
            .apply(cls.format_es_ids)
        )
        ids_to_update_mask = duplicate_status_df.loc[:, ["source_ids", "es_ids"]].apply(
            lambda x: cls.check_if_new_ids(x), axis=1
        )
        to_update_mask = lei_in_es_mask & ids_to_update_mask
        duplicates_mask = lei_in_es_mask & ~ids_to_update_mask

        return to_update_mask, duplicates_mask
