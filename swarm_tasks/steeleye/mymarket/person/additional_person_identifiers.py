from typing import List
from typing import Tuple

import pandas as pd
from prefect import context
from pydantic import Field
from se_elastic_schema.components.reference.identifier import Identifier
from se_elastic_schema.components.reference.national_id import NationalId
from se_elastic_schema.components.reference.person_official_identifiers import (
    PersonOfficialIdentifiers,
)
from se_schema.models.reference.person.utils.concat_id_builder import ConcatIdBuilder
from se_schema.models.reference.person.utils.mifir_id_builder import MifirIdBuilder
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class DictKeys:
    ID = "id"
    LABEL = "label"


class MifirIdType:
    N = "N"


class TempColumns:
    MIFIR_ID_AND_SUBTYPE_LIST = "mifir_id_subtype_list"


class Params(BaseParams):
    source_dob_col: str = Field(
        ...,
        description="Column containing the Date of birth (YYMMDD)",
    )
    source_first_name_col: str = Field(
        ...,
        description="Column containing the first name",
    )
    source_last_name_col: str = Field(
        ...,
        description="Column containing the last name",
    )
    source_nationality_col: str = Field(
        ...,
        description="Column containing the nationality (2-letter iso code)",
    )
    source_national_id_col: str = Field(
        default="officialIdentifiers.nationalIds",
        description="Column containing the national ids as per the schema",
    )
    source_passport_col: str = Field(
        default="officialIdentifiers.passports",
        description="Column containing the passports as per the schema",
    )
    target_concat_id_column: str = Field(
        default="officialIdentifiers.concatId",
        description="Target column which contains the concat ID",
    )
    target_mifir_id_column: str = Field(
        default="officialIdentifiers.mifirId",
        description="Target column which contains the mifir ID",
    )
    target_mifir_id_type_column: str = Field(
        default="officialIdentifiers.mifirIdType",
        description="Target column which contains the mifir ID type",
    )
    target_mifir_id_subtype_column: str = Field(
        default="officialIdentifiers.mifirIdSubType",
        description="Target column which contains the mifir ID subtype",
    )

    def input_columns(self):
        """Returns a list of required source columns."""
        return [
            self.source_dob_col,
            self.source_first_name_col,
            self.source_last_name_col,
            self.source_nationality_col,
            self.source_national_id_col,
            self.source_passport_col,
        ]

    def target_columns(self):
        """Returns a list of required source columns."""
        return [
            self.target_concat_id_column,
            self.target_mifir_id_column,
            self.target_mifir_id_type_column,
            self.target_mifir_id_subtype_column,
        ]


class AdditionalPersonIdentifiers(TransformBaseTask):
    """
    This task calculates important additional identifiers from certain fields.
    1. officialIdentifiers.concatId is calculated from the first name, last name, dob
       and nationality (2-digit iso code)
    2. officialIdentifiers.mifirId, officialIdentifiers.mifirIdSubType and
       officialIdentifiers.mifirIdType: these fields are populated from nationality,
       passports and the already-calculated concat id
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources=None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            params=params,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        logger=context.get("logger"),
    ) -> pd.DataFrame:
        """Process class method which populates additional person identifiers --
        concat ID and mifir id columns"""

        if source_frame.empty:
            logger.warning("Source frame empty, returning empty data frame")
            return source_frame

        required_columns = params.input_columns()
        required_columns_mask = source_frame.columns.isin(required_columns)
        df = source_frame.loc[:, required_columns_mask]
        # Add required columns if not present
        for col in required_columns:
            if col not in df.columns:
                df.loc[:, col] = pd.NA

        # Populate Concat ID
        concatid_reqd_cols_mask = df.columns.isin(
            [
                params.source_dob_col,
                params.source_first_name_col,
                params.source_last_name_col,
                params.source_nationality_col,
            ]
        )
        concat_id_cols_not_null_mask = (
            df.loc[:, params.source_dob_col].notnull()
            & (df.loc[:, params.source_first_name_col].notnull())
            & (df.loc[:, params.source_last_name_col].notnull())
            & ((df.loc[:, params.source_nationality_col]).notnull())
        )

        logger.warning(
            f"Concat ID was not populated for {concat_id_cols_not_null_mask.sum()} rows"
        )

        df.loc[concat_id_cols_not_null_mask, params.target_concat_id_column] = df.loc[
            concat_id_cols_not_null_mask, concatid_reqd_cols_mask
        ].apply(
            # This calls the build_concat_id fuction in the schema, which builds the concat id
            #  as per the ESMA specifications
            lambda x: ConcatIdBuilder.build_concat_id(
                nationality=x[params.source_nationality_col],
                date_of_birth=x[params.source_dob_col],
                first_name=x[params.source_first_name_col],
                last_name=x[params.source_last_name_col],
            ),
            axis=1,
        )

        # Populate Mifir ID columns
        mifir_id_required_columns = [
            params.source_nationality_col,
            params.target_concat_id_column,
            params.source_passport_col,
            params.source_national_id_col,
        ]
        # Populate Mifir ID and Mifir ID Subtype
        target_mifir_columns = [
            params.target_mifir_id_column,
            params.target_mifir_id_subtype_column,
        ]
        df.loc[:, TempColumns.MIFIR_ID_AND_SUBTYPE_LIST] = df.loc[
            :, mifir_id_required_columns
        ].apply(
            lambda identifiers_series: cls._create_mifir_id(
                nationality=identifiers_series[params.source_nationality_col],
                concat_id=identifiers_series[params.target_concat_id_column],
                national_ids=identifiers_series[params.source_national_id_col],
                passports=identifiers_series[params.source_passport_col],
            ),
            axis=1,
        )
        # Split the list column into 2 columns
        df[target_mifir_columns] = pd.DataFrame(
            df[TempColumns.MIFIR_ID_AND_SUBTYPE_LIST].tolist(), index=df.index
        )
        # Populate Mifir Id type
        mifir_id_not_null_mask = df.loc[:, params.target_mifir_id_column].notnull()
        df.loc[
            mifir_id_not_null_mask, params.target_mifir_id_type_column
        ] = MifirIdType.N
        logger.warning(
            f"Mifir ID was not populated for {df.loc[:, params.target_mifir_id_column].isnull().sum()} rows"
        )

        target_columns_mask = df.columns.isin(params.target_columns())
        return df.loc[:, target_columns_mask].fillna(pd.NA)

    @classmethod
    def _create_mifir_id(
        cls,
        nationality: str,
        concat_id: str,
        national_ids: List[dict],
        passports: List[dict],
    ) -> Tuple:
        """
        NOTE: This function calls the build_mifir_id in the schema, which
        creates the Mifir ID and Mifir ID Subtype.

        :param: nationality: The Nationality of the Person, in ISO Alpha-2 country code format
        :param: concat_id: The concat_id of the record
        :param: national_ids: national identifiers in the required schema format
        :param: passports: passports in the required schema format
        :returns: A Tuple containing the MiFIR ID and MiFIR ID Sub Type
                 if the MiFIR ID can be determined, otherwise (None, None)
        """
        if pd.isna(nationality):
            return None, None
        person_identifiers = PersonOfficialIdentifiers()
        # The input file is currently expected to contain 1 passport number, so
        # pass the first dict to Identifier
        person_identifiers.passports = (
            [Identifier(**passports[0])] if not pd.isna(passports) else None
        )
        person_identifiers.concatId = concat_id if not pd.isna(concat_id) else None
        person_identifiers.nationalIds = (
            [NationalId(**national_ids[0])] if not pd.isna(national_ids) else None
        )
        mifir_id_tuple = MifirIdBuilder.build_mifir_id(
            nationality=nationality, official_identifiers=person_identifiers
        )
        return mifir_id_tuple
