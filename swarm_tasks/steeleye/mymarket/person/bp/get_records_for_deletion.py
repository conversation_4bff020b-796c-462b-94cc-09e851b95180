import logging
from datetime import datetime
from pathlib import Path
from typing import List
from typing import Tuple

import pandas as pd
from prefect.engine import signals
from pydantic import Field
from se_core_tasks.mymarket.static import PersonColumns
from se_core_tasks.utils.fsspec_utils import fsspec_read_json
from se_core_tasks.utils.fsspec_utils import fsspec_write_json
from se_elastic_schema.models import AccountPerson
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.utilities.es.query_utils import es_scroll
from swarm_tasks.utilities.es.query_utils import get_terms_query
from swarm_tasks.utilities.static import MetaModel


DATE_FORMAT = "%Y%m%d%H%m"
OUTPUT_S3_FILE_PATH = "state_info/mymarket-bp-person-processor/ntid_list.json"
ARCHIVE_FILE_PREFIX = "state_info/mymarket-bp-person-processor/processed/"


class Params(BaseParams):
    ntid_column_in_json: str = Field(
        default="ntids_list",
        description="Name of the json key which contains a list of all NTIDs",
    )


class GetRecordsForDeletion(TransformBaseTask):
    """
    This task does the following:
    It gets a list of all PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID values (in BP terminology,
    these are 'NTIDs'. It then compares the NTIDs from the previous day (previous run) with the
    current NTIDs.
    To do this, it writes a file in S3 (prefix "state_info/mymarket-bp-person-processor/ntid_list.json")
    every time the flow runs. Each time, this file is overwritten with the latest run's NTIDs. On the next run,
    it fetches this file (which will contain data from the previous run). If there are any NTIDs in the
    previous file which are not in the current file, the records corresponding to these NTIDs need to
    be deleted.
    So, the &id, &hash and &model are fetched for these records, and they are sent to downstream
    ElasticBulkTransformer and ElasticBulkDeleter tasks to actually delete the records.

    Note that this task also writes each day's IDs in state_info/mymarket-bp-person-processor/processed/.
    These files are timestamped and not overwritten. This is just to maintain a list of NTIDs for each day
    for debugging purposes.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:
        return self.process(
            source_frame=source_frame, params=params, logger=self.logger
        )

    @classmethod
    def process(
        cls, source_frame: pd.DataFrame, params: Params, logger: logging.Logger
    ):

        if (
            source_frame.empty
            or PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID
            not in source_frame.columns
        ):
            raise signals.FAIL("Source frame empty or required columns not present")

        es_client = Settings.connections.get("tenant-data")
        ntids_set = set(
            source_frame.loc[:, PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID].unique()
        )
        ntids_list = list(ntids_set)

        file_path, archive_file_path = cls.create_output_file_paths()

        # Read the previous ntid_list json file

        try:
            json_content = fsspec_read_json(file_path=file_path)
        except FileNotFoundError:
            # For the first ever run, the file will not be present. Crete the same content
            # as the current file (i.e., all the ntids in the current file should be present
            # in json_content). Nothing will get deleted in this run.
            json_content = {params.ntid_column_in_json: ntids_list}

        ntids_in_prev_file = set(json_content.get(params.ntid_column_in_json))

        # Write the current list of ntids with current time stamp to OUTPUT_S3_FILE_PATH/processed/ as well
        # as the 'normal' ntid_list path

        cls.write_ntids_to_s3(
            archive_file_path=archive_file_path,
            file_path=file_path,
            logger=logger,
            ntids_list=ntids_list,
            params=params,
        )

        removed_ntids = list(ntids_in_prev_file - ntids_set)

        # Get the meta fields of the records to be deleted
        to_delete_df = cls._get_person_frame(
            ids=removed_ntids, es_client=es_client, logger=logger
        )

        if to_delete_df.empty:
            raise signals.SKIP("No records to delete")

        return to_delete_df

    @classmethod
    def create_output_file_paths(cls) -> Tuple[str, str]:
        """This method returns 2 output file paths: a 'normal' file path and an archive file path.
        :returns Tuple with 2 file paths
        """
        bucket = Settings.realm
        file_path = f"s3://{bucket}/{OUTPUT_S3_FILE_PATH}"
        file_name = f"ntid_list_{datetime.utcnow().strftime('%Y%m%d%H%m')}.json"
        archive_file_path = (
            f"s3://{bucket}/{Path(ARCHIVE_FILE_PREFIX).joinpath(file_name).as_posix()}"
        )
        return file_path, archive_file_path

    @classmethod
    def write_ntids_to_s3(
        cls,
        archive_file_path: str,
        file_path: str,
        logger,
        ntids_list: List[str],
        params: Params,
    ):
        """
        The list of NTIDs is written to 2 S3 paths:
        1. The 'main' ntid_list.json file is overwritten for each run. This is the file that is read
           to get the previous list of NTIDs
        2. The 'archive' path. This is written with the current timestamp in the file name. This is used
           just to maintain a list of ntids for each run.
        :param archive_file_path: archive file path (with current timestamp)
        :param file_path: file path of the ntid file which will be overwritten on each run
        :param logger: Logger instance
        :param ntids_list: List of ntids
        :param params: Params instance
        :return: None
        """
        new_json_content = {params.ntid_column_in_json: ntids_list}
        fsspec_write_json(
            json_content=new_json_content,
            file_path=file_path,
        )
        logger.info(f"{file_path} successfully updated with the latest list of NTIDs")
        fsspec_write_json(
            json_content=new_json_content,
            file_path=archive_file_path,
        )
        logger.info(
            f"Latest list of NTIDs successfully written to archive path {archive_file_path}"
        )

    @classmethod
    def _get_person_frame(
        cls,
        ids: list,
        es_client,
        logger: logging.Logger,
    ) -> pd.DataFrame:
        """
        Gets the Person records from ElasticSearch corresponding to the employee ids which
        need to be deleted. We get only the &id, &hash and &model as these are required by
        the ElasticBulkTranformer to create a suitable ndjson file which can be used by
        ElasticBulkDeleter
        :param es_client: Elasticsearch client
        :param logger: Logger instance
        :return: All matching people from Elasticsearch
        """

        if not ids:
            return pd.DataFrame()
        query = get_terms_query(
            ids=list(ids),
            es_client=es_client,
            lookup_field=PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID,
            model_field=MetaModel.ACCOUNT_PERSON,
            source_field=[
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID,
                es_client.meta.id,
                es_client.meta.hash,
                es_client.meta.model,
            ],
        )
        alias = AccountPerson.get_elastic_index_alias(tenant=Settings.tenant)
        logger.info(
            f"Querying Elasticsearch to get the &ids of {len(ids)} people who need to be deleted"
        )
        fetched_df = es_scroll(es_client=es_client, query=query, index=alias)

        return fetched_df
