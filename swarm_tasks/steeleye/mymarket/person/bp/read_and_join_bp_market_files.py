import logging
from typing import Optional

import pandas as pd
from prefect import context
from prefect.engine import signals
from pydantic import Field
from se_core_tasks.utils.frame_manipulation import add_missing_columns
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.cloud.aws.s3.utils import read_csv_from_s3_download
from swarm_tasks.utilities.s3 import s3_bucket_from_url
from swarm_tasks.utilities.s3 import s3_key_from_url


class Params(BaseParams):
    bp_file_column: str = Field(
        default="s3_bpistmrl_file_url",
        description="Name of the column which contains the S3 URL of the BP file",
    )
    kpmg_file_column: str = Field(
        default="s3_kpmgrmrl_file_url",
        description="Name of the column which contains the S3 URL of the KPMG file",
    )
    ntid_column: str = Field(
        default="NTID",
        description="Name of the NTID column which is present in both the BP and KPMG data frames",
    )
    dataframe_columns: Optional[list] = Field(
        default=None,
        description="List of expected columns in the output dataframe."
        " This is used to add missing columns as null. Note that"
        "the output df may have extra columns which are not in this list",
    )


class ReadAndJoinBpMarketFiles(TransformBaseTask):
    """
    This task reads the 2 S3 Market files (BP+KPMG) after downloading them based on the S3 links
    in the 2 columns of the input data frame.
    It then drops duplicates in both files based on the NTID field. Finally, it combines
    the 2 files together to produce a single data frame.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[Params] = None,
        **kwargs,
    ) -> pd.DataFrame:
        return self.process(
            source_frame=source_frame, params=params, logger=self.logger
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Optional[Params] = None,
        logger=context.get("logger"),
    ) -> pd.DataFrame:

        if source_frame.empty:
            raise signals.SKIP("Source frame empty, no files to read and join")

        # The source_frame has 1 row with 2 columns. Get the file urls from each of the
        # cells in the row and download the data frames

        bp_df = cls._get_df_from_s3(
            source_frame=source_frame, col_name=params.bp_file_column, logger=logger
        )
        kpmg_df = cls._get_df_from_s3(
            source_frame=source_frame, col_name=params.kpmg_file_column, logger=logger
        )

        # Convert the params.ntid_column to lower case in both frames so that the merge works correctly
        bp_df.loc[:, params.ntid_column] = bp_df.loc[:, params.ntid_column].str.lower()
        kpmg_df.loc[:, params.ntid_column] = kpmg_df.loc[
            :, params.ntid_column
        ].str.lower()

        # Deduplicate both data frames based on the NTID key
        bp_df = bp_df.drop_duplicates(subset=[params.ntid_column])
        kpmg_df = kpmg_df.drop_duplicates(subset=[params.ntid_column])

        # Left join as the 'email' rows from the second file might or might not be present
        target = bp_df.merge(right=kpmg_df, how="left", on=params.ntid_column)

        # Add missing columns
        target = add_missing_columns(
            dataframe=target, dataframe_columns=params.dataframe_columns
        )
        return target.fillna(pd.NA)

    @staticmethod
    def _get_df_from_s3(
        source_frame: pd.DataFrame, col_name: str, logger: logging.Logger
    ) -> pd.DataFrame:
        """
        Gets the S3 CSV file pointed to by source_frame.loc[0, col_name], downloads
        it and returns the dataframe obtained by reading the csv using pd.read_csv
        :param source_frame: Source frame containing a column called col_name
        :param col_name: column containing link to an S3 CSV file
        :param logger: logger instance
        :return: DataFrame obtained by downloading the S3 CSV file in the col_name column
                 of source_frame
        """
        s3_file_url = source_frame.loc[0, col_name]
        bucket = s3_bucket_from_url(s3_url=s3_file_url)
        key_ = s3_key_from_url(s3_url=s3_file_url)
        df = read_csv_from_s3_download(
            bucket=bucket,
            key=key_,
            logger=logger,
        )
        return df
