import logging
from enum import Enum
from typing import Optional

import pandas as pd
from prefect.engine.signals import FAIL
from pydantic import Field
from se_core_tasks.mymarket.static import PersonColumns
from se_elastic_schema.models import AccountPerson
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.utilities.es.query_utils import es_scroll
from swarm_tasks.utilities.es.query_utils import get_terms_query
from swarm_tasks.utilities.static import MetaModel


class ActionType(Enum):
    CREATE = "create"
    UPDATE = "update"


class Params(BaseParams):
    create_update_indicator_column: str = Field(
        ...,
        description="Name of the output column which indicates"
        "whether a record needs to be created or updated",
    )


class TempColumns:
    MERGE_INDICATOR_COLUMN = "merge_indicator_column"


class FetchPersonRecordsFromElastic(TransformBaseTask):
    """
    This task fetches records already in Elastic using the officialIdentifiers.employeeId
    field. If for an input record, there is no match in Elastic, it signifies that it is
    a new record. In this case, the params.create_update_indicator_column is set to 'create'
    in the output frame, and a new record is created downstream.

    If for an input record, there is a match in Elastic, it signifies that it is an existing
    record. In this case,  the params.create_update_indicator_column is set to 'update' in
    the output frame, and the record is updated downstream. This is done by getting the &id
    and &key from the matching Elastic record and replacing the &id and &key in the input record.

    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[Params] = None,
        **kwargs,
    ) -> pd.DataFrame:
        return self.process(
            source_frame=source_frame, params=params, logger=self.logger
        )

    @classmethod
    def process(
        cls, source_frame: pd.DataFrame, params: Params, logger: logging.Logger
    ):
        es_client = Settings.connections.get("tenant-data")
        if (
            source_frame.empty
            or PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID
            not in source_frame.columns
            or es_client.meta.id not in source_frame.columns
        ):
            error = "Source frame empty / required columns not found in source frame"
            raise FAIL(error)

        df = source_frame.copy()
        df.loc[:, params.create_update_indicator_column] = pd.NA
        fetched_person_df = cls._get_person_frame(
            df=df,
            es_client=es_client,
            logger=logger,
        )
        if fetched_person_df.empty:
            # All records need to be created. No merge required
            df.loc[:, params.create_update_indicator_column] = ActionType.CREATE.value
            return df

        # If code reaches here, at least one row in df is guaranteed to join with a row in fetched_person_df
        # (otherwise, fetched_person_df would have been empty).
        # The rows that are joined successfully are rows that need to be updated. The rows which are only in
        # the left frame are new rows which have to be created
        df = df.merge(
            right=fetched_person_df,
            on=PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID,
            how="left",
            suffixes=[None, "_elastic"],
            indicator=TempColumns.MERGE_INDICATOR_COLUMN,
        )

        # Created rows
        updated_mask = df.loc[:, TempColumns.MERGE_INDICATOR_COLUMN].eq("both")
        created_mask = df.loc[:, TempColumns.MERGE_INDICATOR_COLUMN].eq("left_only")
        df.loc[
            updated_mask, params.create_update_indicator_column
        ] = ActionType.UPDATE.value
        df.loc[
            created_mask, params.create_update_indicator_column
        ] = ActionType.CREATE.value

        # For updated rows, we need to replace the &id and &key with the corresponding values from Elastic
        for col in {es_client.meta.id, es_client.meta.key}:
            df.loc[updated_mask, col] = df.loc[updated_mask, f"{col}_elastic"]

        # Drop columns
        df = df.drop(
            columns=[
                f"{es_client.meta.key}_elastic",
                f"{es_client.meta.id}_elastic",
                TempColumns.MERGE_INDICATOR_COLUMN,
            ],
            axis=1,
        )
        return df

    @classmethod
    def _get_person_frame(
        cls,
        df: pd.DataFrame,
        es_client,
        logger: logging.Logger,
    ) -> pd.DataFrame:
        """
        Gets the Person records from ElasticSearch corresponding to the employee ids in the input
        file
        :param df: data frame containing the source data
        :param es_client: Elasticsearch client
        :param logger: Logger instance
        :return: All matching people from Elasticsearch
        """
        unique_emp_id_list = (
            df.loc[:, PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID].unique().tolist()
        )
        query = get_terms_query(
            ids=unique_emp_id_list,
            es_client=es_client,
            lookup_field=PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID,
            model_field=MetaModel.ACCOUNT_PERSON,
            source_field=[
                es_client.meta.id,
                es_client.meta.key,
                PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID,
            ],
        )
        alias = AccountPerson.get_elastic_index_alias(tenant=Settings.tenant)
        logger.info(f"Querying Elasticsearch for {len(unique_emp_id_list)} people")
        fetched_df = es_scroll(es_client=es_client, query=query, index=alias)

        return fetched_df
