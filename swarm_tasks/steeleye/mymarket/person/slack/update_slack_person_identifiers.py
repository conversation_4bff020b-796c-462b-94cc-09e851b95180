import logging
from typing import List
from typing import Optional

import pandas as pd
from prefect import context
from prefect.engine import signals
from pydantic import Field
from se_core_tasks.mymarket.static import PersonColumns
from se_core_tasks.utils.frame_manipulation import add_missing_columns
from se_elastic_schema.models import Account<PERSON>erson
from se_elastic_schema.static.reference import ImAccountType
from swarm.conf import Settings
from swarm.task.auditor import Auditor
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.steeleye.mymarket.person.static import ImAccountKeys
from swarm_tasks.utilities.es.query_utils import es_scroll
from swarm_tasks.utilities.es.query_utils import get_terms_query_multiple_lookup_fields
from swarm_tasks.utilities.static import MetaModel


class Params(BaseParams):

    source_email_column: str = Field(
        ...,
        description="Name of the column which contains the email address",
    )
    source_slack_id_column: str = Field(
        ...,
        description="Name of the column which contains the Slack ID",
    )


class TempColumns:
    ELASTIC_EMAIL = "__elastic_email__"
    LOWERCASE_EMAIL = "__lower_case_email__"
    LOWERCASE_SLACK_ID = "__lower_case_slack_id__"
    SLACK_DICT = "__slack_dict__"


UNIQUE_PROPS = "&uniqueProps"


class UpdateSlackPersonIdentifiers(TransformBaseTask):
    """
    This task updates the Slack ID, uniqueProps and uniqueIds in Elastic records
    where the lookup based on the source email id is successful (elastic lookup field: &uniqueProps).
    If the lookup is not successful, no action is taken.
    Also, if the record already has the same slack id in communications.imAccounts, it is
    not updated again.

    It returns a df with valid AccountPerson Elastic records which need to be updated by the downstream
    ElasticBulkTransformer and ElasticBulkWriter.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            params=params,
            logger=self.logger,
            auditor=self.auditor,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        logger=context.get("logger"),
        auditor: Auditor = None,
    ) -> pd.DataFrame:

        if (
            source_frame.empty
            or params.source_email_column not in source_frame.columns
            or params.source_slack_id_column not in source_frame.columns
        ):
            raise signals.FAIL("Source frame empty or required columns not present.")

        unique_ids_to_lookup = (
            source_frame.loc[:, params.source_email_column]
            .astype("str")
            .str.lower()
            .unique()
            .tolist()
        )
        fetched_df = cls._lookup_elasticsearch(
            unique_ids=unique_ids_to_lookup, tenant=Settings.tenant, logger=logger
        )

        # Add required Elastic columns (which will be used for the join, during update) with value NA
        # if not present
        required_cols = [
            PersonColumns.UNIQUE_IDS,
            PersonColumns.COMMUNICATIONS_EMAILS,
            PersonColumns.COMMUNICATIONS_IMACCOUNTS,
            UNIQUE_PROPS,
        ]
        fetched_df = add_missing_columns(
            dataframe=fetched_df, dataframe_columns=required_cols
        )

        if fetched_df.empty:
            skip_message = "No records found with the given email addresses. No further processing required"
            auditor.add(message=skip_message)
            raise signals.SKIP(skip_message)

        return cls._update_fields_in_elastic_records(
            source_df=source_frame,
            elastic_fetched_df=fetched_df,
            params=params,
            logger=logger,
            auditor=auditor,
        )

    @classmethod
    def _lookup_elasticsearch(
        cls, unique_ids: List[str], tenant: str, logger: logging.Logger
    ):
        """
        Looks up the message model in Elasticsearch for the given tenant for the given
        set of ids.
        :param unique_ids: Unique list of ids to lookup
        :param tenant: Tenant to lookup
        :param logger: Logger instance
        :return: Dataframe containing elastic records for the given ids
        """
        es_client = Settings.connections.get("tenant-data")
        query = get_terms_query_multiple_lookup_fields(
            ids=unique_ids,
            es_client=es_client,
            lookup_fields=[UNIQUE_PROPS, PersonColumns.COMMUNICATIONS_EMAILS],
            model_field=MetaModel.ACCOUNT_PERSON,
        )
        alias = AccountPerson.get_elastic_index_alias(tenant=tenant)
        logger.info(f"Querying Elasticsearch for {len(unique_ids)} email addresses")
        return es_scroll(es_client=es_client, index=alias, query=query)

    @classmethod
    def _update_fields_in_elastic_records(
        cls,
        source_df: pd.DataFrame,
        elastic_fetched_df: pd.DataFrame,
        params: Params,
        logger: logging.Logger,
        auditor: Auditor,
    ):
        """
        This function takes as input 2 data frames: one from Elastic and one from the source. It updates
        the identifiers in the Elastic records after doing a case-insensitive merge on source email
        and the exploded uniqueProps + communications.emails from Elastic

        :param source_df: Dataframe containing email ids and slack ids
        :param elastic_fetched_df: dataframe fetched from Elasticsearch for the required ids
        :returns: Data frame containing complete records which need to be updated
        """

        # Get the values from communications.emails and uniqueProps (both list columns) into one list
        # after checking if either of them is null.
        communications_emails_not_null_mask = elastic_fetched_df.loc[
            :, PersonColumns.COMMUNICATIONS_EMAILS
        ].notnull()
        unique_props_not_null_mask = elastic_fetched_df.loc[:, UNIQUE_PROPS].notnull()
        elastic_fetched_df[TempColumns.ELASTIC_EMAIL] = pd.NA
        elastic_fetched_df.loc[
            communications_emails_not_null_mask & unique_props_not_null_mask,
            TempColumns.ELASTIC_EMAIL,
        ] = (
            elastic_fetched_df.loc[
                communications_emails_not_null_mask & unique_props_not_null_mask,
                PersonColumns.COMMUNICATIONS_EMAILS,
            ]
            + elastic_fetched_df.loc[
                communications_emails_not_null_mask & unique_props_not_null_mask,
                UNIQUE_PROPS,
            ]
        )
        elastic_fetched_df.loc[
            communications_emails_not_null_mask & ~unique_props_not_null_mask,
            TempColumns.ELASTIC_EMAIL,
        ] = elastic_fetched_df.loc[
            communications_emails_not_null_mask & ~unique_props_not_null_mask,
            PersonColumns.COMMUNICATIONS_EMAILS,
        ]
        elastic_fetched_df.loc[
            ~communications_emails_not_null_mask & unique_props_not_null_mask,
            TempColumns.ELASTIC_EMAIL,
        ] = elastic_fetched_df.loc[
            ~communications_emails_not_null_mask & unique_props_not_null_mask,
            UNIQUE_PROPS,
        ]

        # Explode and remove duplicates
        elastic_fetched_df = elastic_fetched_df.explode(
            column=TempColumns.ELASTIC_EMAIL
        )
        elastic_fetched_df = elastic_fetched_df.drop_duplicates(
            subset=[TempColumns.ELASTIC_EMAIL], keep="first"
        )
        # Convert email cols to lowercase before the merge
        elastic_fetched_df.loc[:, TempColumns.ELASTIC_EMAIL] = elastic_fetched_df.loc[
            :, TempColumns.ELASTIC_EMAIL
        ].str.lower()
        source_df.loc[:, TempColumns.LOWERCASE_EMAIL] = source_df.loc[
            :, params.source_email_column
        ].str.lower()

        # Inner join with the exploded email ids: there is no possibility of 0 matches as fetched_df is non-empty.
        update_df = source_df.merge(
            right=elastic_fetched_df,
            left_on=TempColumns.LOWERCASE_EMAIL,
            right_on=TempColumns.ELASTIC_EMAIL,
            how="inner",
        )

        not_joined_count = source_df.shape[0] - update_df.shape[0]
        if not_joined_count > 0:
            audit_message = (
                f"For {not_joined_count} records in the source, no corresponding AccountPerson records"
                f" could be found. This could be either because the AccountPerson records don't exist, or because"
                f" they don't have matching email addresses"
            )

            auditor.add(message=audit_message)
            logger.info(audit_message)
        im_accounts_null_mask = update_df.loc[
            :, PersonColumns.COMMUNICATIONS_IMACCOUNTS
        ].isnull()

        update_df[TempColumns.LOWERCASE_SLACK_ID] = update_df.loc[
            :, params.source_slack_id_column
        ].str.lower()

        update_df.loc[:, TempColumns.SLACK_DICT] = update_df.loc[
            :, TempColumns.LOWERCASE_SLACK_ID
        ].apply(
            lambda x: {
                ImAccountKeys.ID: x,
                ImAccountKeys.LABEL: ImAccountType.SLACK.value,
            }
        )

        update_df.loc[
            im_accounts_null_mask, PersonColumns.COMMUNICATIONS_IMACCOUNTS
        ] = update_df.loc[im_accounts_null_mask, TempColumns.SLACK_DICT].apply(
            lambda x: [x]
        )

        # Update IM accounts if the current Slack id doesn't exist
        update_df.loc[
            ~im_accounts_null_mask, PersonColumns.COMMUNICATIONS_IMACCOUNTS
        ] = update_df.loc[
            ~im_accounts_null_mask,
            [
                TempColumns.SLACK_DICT,
                TempColumns.LOWERCASE_SLACK_ID,
                PersonColumns.COMMUNICATIONS_IMACCOUNTS,
            ],
        ].apply(
            lambda x: cls._add_im_account_if_not_present(
                im_accounts_list=x[PersonColumns.COMMUNICATIONS_IMACCOUNTS],
                slack_id=x[TempColumns.LOWERCASE_SLACK_ID],
                slack_dict=x[TempColumns.SLACK_DICT],
            ),
            axis=1,
        )

        # Drop rows where the new imAccount is now None. This signifies that this row does not need
        # to be updated as the Slack ID is already present in the Elastic imAccounts field.
        # This is safe to do as any record which was earlier empty in Elastic WILL NOT be empty after
        # _add_im_account_if_not_present.
        update_df = update_df.drop(
            update_df.loc[
                update_df.loc[:, PersonColumns.COMMUNICATIONS_IMACCOUNTS].isnull()
            ].index
        )

        # If no rows are left, raise a SKIP
        if update_df.empty:
            raise signals.SKIP(
                "All records dropped as they already have Slack IDs. Nothing to update"
            )

        unique_ids_null_mask = update_df.loc[:, PersonColumns.UNIQUE_IDS].isnull()
        update_df.loc[~unique_ids_null_mask, PersonColumns.UNIQUE_IDS] = update_df.loc[
            ~unique_ids_null_mask,
            [PersonColumns.UNIQUE_IDS, TempColumns.LOWERCASE_SLACK_ID],
        ].apply(
            lambda x: list(
                set(x[PersonColumns.UNIQUE_IDS] + [x[TempColumns.LOWERCASE_SLACK_ID]])
            ),
            axis=1,
        )

        update_df.loc[unique_ids_null_mask, PersonColumns.UNIQUE_IDS] = update_df.loc[
            unique_ids_null_mask, TempColumns.LOWERCASE_SLACK_ID
        ].apply(lambda x: [x])

        # Set uniqueProps = uniqueIds
        update_df.loc[:, UNIQUE_PROPS] = update_df.loc[:, PersonColumns.UNIQUE_IDS]

        # Drop non-elastic columns
        cols_to_drop = [
            v for k, v in TempColumns.__dict__.items() if not k.startswith("_")
        ] + [
            params.source_slack_id_column,
            params.source_email_column,
        ]
        update_df = update_df.drop(columns=cols_to_drop)

        return update_df.fillna(pd.NA).reset_index(drop=True)

    @classmethod
    def _add_im_account_if_not_present(
        cls, im_accounts_list: List[dict], slack_id: str, slack_dict: dict
    ) -> Optional[List[dict]]:
        """Inserts Slack id as a dict into an communications.imAccounts list if it isn't already present.
        If the Slack ID is already present (with the right label), it returns None.

        :param im_accounts_list: IM Accounts column value (List of dictionaries) from Elastic
        :param slack_id: Slack ID of the current row
        :param slack_dict: im_account dictionary for Slack in the format expected by the schema.
        :returns Updated im_accounts_list after adding the slack ID. If the slack ID is already present,
        None is returned
        """

        # If slack id is already present as one of the ID keys in imAccounts, return None
        if any(
            im_accounts_dict.get(ImAccountKeys.ID).lower() == slack_id
            and im_accounts_dict.get(ImAccountKeys.LABEL) == ImAccountType.SLACK.value
            for im_accounts_dict in im_accounts_list
        ):
            return

        # Slack id not present. Append slack_dict to list
        im_accounts_list.append(slack_dict)
        return im_accounts_list
