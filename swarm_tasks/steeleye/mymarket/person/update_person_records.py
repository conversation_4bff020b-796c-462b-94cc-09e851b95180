from enum import Enum
from typing import Any
from typing import Dict
from typing import List

import pandas as pd
from prefect.engine import signals
from swarm.task.base import BaseTask
from swarm.task.transform.result import TransformResult


class SplitPersonResultKeys(str, Enum):
    UPDATE = "update"
    ES_DATA = "elastic_data"


class TempColumns(str, Enum):
    DUPLICATE_ACTION = "__update_action__"
    GROUP_BY_INDEX = "__groupby_index__"
    MATCHED_FEED_INDEX = "__matched_feed_index__"
    IM_ACCOUNT_IDS = "__im_account_ids__"
    PHONE_NUMBERS = "__phone_numbers__"
    TRADE_FILE_IDS = "__trade_file_ids__"


class PersonColumns(str, Enum):
    ID = "&id"
    HASH = "&hash"
    TIMESTAMP = "&timestamp"
    MODEL = "&model"
    USER = "&user"
    VERSION = "&version"
    UNIQUE_PROPS = "&uniqueProps"
    UNIQUE_IDS = "uniqueIds"
    EMAILS = "communications.emails"
    IM_ACCOUNT = "communications.imAccounts"
    PHONE_NUMBERS = "communications.phoneNumbers"
    TRADE_FILE_IDS = "sinkIdentifiers.tradeFileIdentifiers"


# List of all fields in the model that can handle multiple values (list values)
model_list_values = [
    "communications.emails",
    "personalDetails.nationality",
    "structure.desks",
    "communications.imAccounts",
    "communications.phoneNumbers",
    "officialIdentifiers.nationalIds",
    "officialIdentifiers.passports",
    "sinkIdentifiers.tradeFileIdentifiers",
]


class DuplicateAction:
    KEEP_ORIGINAL_RECORDS = "Keep Original Record"
    OVERRIDE_ALL_FIELDS = "Override All Fields - inc Blanks"
    OVERRIDE_WITHOUT_BLANKS = "Override All Fields - exc Blanks"
    ADD_NEW_FIELDS = "Add New Fields"


class UpdatePersonRecords(BaseTask):
    """
    Task that apply a criteria on the DataFrame to update it with corresponding data from elastic search.
    """

    def execute(
        self,
        split_person_result: Dict = None,
        **kwargs,
    ):
        return self.process(
            split_person_result=split_person_result,
        )

    def process(self, split_person_result: Dict[str, pd.DataFrame]) -> TransformResult:
        """
        Update the DataFrame received with the corresponding data from elastic search based in
        using four different criteria.

        :params split_person_result: Dictionary containing the feed and the elastic DataFrame.

        :returns: A DataFrame with values updated
        """
        update_df = split_person_result.get(
            SplitPersonResultKeys.UPDATE, pd.DataFrame()
        )
        elastic_df = split_person_result.get(
            SplitPersonResultKeys.ES_DATA, pd.DataFrame()
        )
        # All elastic records should have a correspondent record in the feed df
        if TempColumns.MATCHED_FEED_INDEX not in elastic_df.columns:
            raise signals.SKIP(
                "Source frame source required columns not present, skipping execution"
            )
        elastic_df = elastic_df[~pd.isna(elastic_df[TempColumns.MATCHED_FEED_INDEX])]

        if any([update_df.empty, elastic_df.empty]):
            raise signals.SKIP("Source frame empty, skipping execution")

        # Set rule 2 'keep original records' as default rule and copy it to elastic df
        update_df[TempColumns.DUPLICATE_ACTION] = update_df[
            TempColumns.DUPLICATE_ACTION
        ].fillna(value=DuplicateAction.ADD_NEW_FIELDS)

        # Align rows by GROUP_BY_INDEX in the feed and MATCHED_FEED_INDEX in the elastic
        elastic_df.set_index(TempColumns.MATCHED_FEED_INDEX, inplace=True)
        elastic_df.index = elastic_df.index.astype(int)
        update_df.set_index(TempColumns.GROUP_BY_INDEX, inplace=True)
        update_df.index = update_df.index.astype(int)

        # Avoids duplicated matches between the feed and elastic records
        update_df = update_df.loc[~update_df.index.duplicated(keep="last"), :]
        # Duplicated rows in elastic_df means that there is some wrong data on ES
        duplicated_rows_mask = elastic_df.index.duplicated(keep="last")
        total_rows = elastic_df[duplicated_rows_mask].shape[0]
        if total_rows > 0:
            self.auditor.add(
                message=f"{total_rows} duplicated row(s) have been found in the system.",
                ctx={
                    "error": f"{elastic_df[duplicated_rows_mask][PersonColumns.UNIQUE_PROPS].tolist()}"
                },
            )
            elastic_df = elastic_df[~duplicated_rows_mask]

        elastic_df[TempColumns.DUPLICATE_ACTION] = update_df[
            TempColumns.DUPLICATE_ACTION
        ]

        # 1. Keep Original Record: filter records that will not be changed
        elastic_df = elastic_df[
            elastic_df[TempColumns.DUPLICATE_ACTION]
            != DuplicateAction.KEEP_ORIGINAL_RECORDS
        ]
        update_df = update_df[
            update_df[TempColumns.DUPLICATE_ACTION]
            != DuplicateAction.KEEP_ORIGINAL_RECORDS
        ]
        if any([update_df.empty, elastic_df.empty]):
            raise signals.SKIP(
                "All records will keep original data, skipping execution."
            )

        # 2. Add New Values
        add_if_new_value_df = self.__add_if_new_value(
            feed_df=update_df,
            elastic_df=elastic_df,
        )

        # 3. Override All Fields - inc Blanks
        override_all_df = self.__override_all_fields(
            feed_df=update_df,
            elastic_df=elastic_df,
        )

        # 4. Override All Fields - exc Blanks
        override_non_blank_df = self.__override_non_blank(
            feed_df=update_df,
            elastic_df=elastic_df,
        )
        final_df = pd.concat(
            [override_all_df, add_if_new_value_df, override_non_blank_df]
        )

        # Update uniqueIds column after updates
        final_df = self.__update_unique_ids(final_df)

        # Drop temporary columns
        final_df = final_df.drop(
            errors="ignore",
            columns=[
                TempColumns.DUPLICATE_ACTION,
                TempColumns.GROUP_BY_INDEX,
                TempColumns.MATCHED_FEED_INDEX,
                TempColumns.IM_ACCOUNT_IDS,
                TempColumns.PHONE_NUMBERS,
                TempColumns.TRADE_FILE_IDS,
                PersonColumns.HASH,
                PersonColumns.TIMESTAMP,
                PersonColumns.MODEL,
                PersonColumns.USER,
                PersonColumns.VERSION,
                PersonColumns.UNIQUE_PROPS,
            ],
        )
        return TransformResult(target=final_df)

    @classmethod
    def __add_if_new_value(
        cls, feed_df: pd.DataFrame, elastic_df: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Function to add all values from the feed to the elastic DataFrame, when the elastic value is None.
        In case of list fields the lists should be merged.
        """
        filtered_elastic_df = elastic_df[
            elastic_df[TempColumns.DUPLICATE_ACTION] == DuplicateAction.ADD_NEW_FIELDS
        ]
        filtered_feed_df = feed_df[
            feed_df[TempColumns.DUPLICATE_ACTION] == DuplicateAction.ADD_NEW_FIELDS
        ]
        if filtered_feed_df.empty or filtered_elastic_df.empty:
            return pd.DataFrame()

        # In fields that support list values, add elements from the feed to the model - merge lists
        for column in model_list_values:
            if (
                column in filtered_elastic_df.columns
                and column in filtered_feed_df.columns
            ):
                filtered_elastic_df[column] = filtered_elastic_df[column].apply(
                    lambda v: v if isinstance(v, list) else []
                )
                filtered_feed_df[column] = filtered_feed_df[column].apply(
                    lambda v: v if isinstance(v, list) else []
                )
                filtered_elastic_df[column] = (
                    filtered_elastic_df[column] + filtered_feed_df[column]
                )
                filtered_elastic_df[column] = filtered_elastic_df[column].apply(
                    cls.__drop_repeated_elements
                )

        combined_elastic_df = filtered_elastic_df.combine_first(filtered_feed_df)
        return combined_elastic_df

    @classmethod
    def __override_all_fields(
        cls, feed_df: pd.DataFrame, elastic_df: pd.DataFrame
    ) -> pd.DataFrame:
        """
        This function returns all fields from the feed to override all values in elastic,
        even if the feed values are None.
        """
        filtered_feed_df = feed_df[
            feed_df[TempColumns.DUPLICATE_ACTION] == DuplicateAction.OVERRIDE_ALL_FIELDS
        ]
        filtered_elastic_df = elastic_df[
            elastic_df[TempColumns.DUPLICATE_ACTION]
            == DuplicateAction.OVERRIDE_ALL_FIELDS
        ]
        if filtered_feed_df.empty or filtered_elastic_df.empty:
            return pd.DataFrame()

        # Save elastic-only columns, they should not change
        elastic_only_columns_df = filtered_elastic_df.drop(
            columns=filtered_feed_df.columns,
            errors="ignore",
        )
        final_df = pd.merge(
            filtered_feed_df, elastic_only_columns_df, left_index=True, right_index=True
        )
        return final_df

    @classmethod
    def __override_non_blank(
        cls, feed_df: pd.DataFrame, elastic_df: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Function to override all values from the feed to the elastic DataFrame, except for None values on the feed.
        In case of list fields the lists from the feed will override the elastic value.
        """
        filtered_elastic_df = elastic_df[
            elastic_df[TempColumns.DUPLICATE_ACTION]
            == DuplicateAction.OVERRIDE_WITHOUT_BLANKS
        ]
        filtered_feed_df = feed_df[
            feed_df[TempColumns.DUPLICATE_ACTION]
            == DuplicateAction.OVERRIDE_WITHOUT_BLANKS
        ]
        if filtered_feed_df.empty or filtered_elastic_df.empty:
            return pd.DataFrame()

        combined_feed_df = filtered_feed_df.combine_first(filtered_elastic_df)
        return combined_feed_df

    @classmethod
    def __drop_repeated_elements(cls, value_list: List[Any]):
        if not isinstance(value_list, list) or len(value_list) == 0:
            return pd.NA

        if isinstance(value_list[0], dict):
            unique_items_set = {tuple(sorted(item.items())) for item in value_list}
            return [dict(t) for t in unique_items_set]

        return list(set(value_list))

    @classmethod
    def __update_unique_ids(cls, no_ids_df: pd.DataFrame) -> pd.DataFrame:
        """Update uniqueIds column after all updates. This columns is used
        to populate &uniqueProps that is not populated by AssignMeta.
        :params no_ids_df: DataFrame to be updated.

        :returns: A DataFrame with updated uniqueIds
        """
        for column in [
            PersonColumns.IM_ACCOUNT,
            PersonColumns.TRADE_FILE_IDS,
            PersonColumns.PHONE_NUMBERS,
        ]:
            if column not in no_ids_df.columns:
                no_ids_df[column] = pd.NA

        # Prepare imAccount ids in a temporary column 'IM_ACCOUNT_IDS'
        no_ids_df[TempColumns.IM_ACCOUNT_IDS] = no_ids_df[
            PersonColumns.IM_ACCOUNT
        ].apply(cls.__get_ids_from_im_account_column)
        # Prepare 'tradeFileIdentifiers' ids in a temporary column 'TRADE_FILE_IDS'
        no_ids_df[TempColumns.TRADE_FILE_IDS] = no_ids_df[
            PersonColumns.TRADE_FILE_IDS
        ].apply(cls.__get_ids_from_trade_file_ids_column)
        # Prepare 'phoneNumbers' ids in a temporary column 'PHONE_NUMBERS'
        no_ids_df[TempColumns.PHONE_NUMBERS] = no_ids_df[
            PersonColumns.PHONE_NUMBERS
        ].apply(cls.__get_ids_from_phone_numbers_column)
        no_ids_df[PersonColumns.UNIQUE_IDS.value] = no_ids_df.apply(
            cls.__concat_ids, axis=1
        )
        return no_ids_df

    @classmethod
    def __get_ids_from_im_account_column(cls, account: List):
        if not isinstance(account, list):
            return []

        return [account.get("id") for account in account if not pd.isna(account)]

    @classmethod
    def __get_ids_from_trade_file_ids_column(cls, trade_files: List):
        if not isinstance(trade_files, list):
            return []

        return [
            f"{trade_file.get('label', '')}:{trade_file.get('id', '')}".replace(
                " ", ""
            ).lower()
            for trade_file in trade_files
        ]

    @classmethod
    def __get_ids_from_phone_numbers_column(cls, phone_numbers: List):
        if not isinstance(phone_numbers, list):
            return []

        phone_ids = []
        for phone in phone_numbers:
            phone_id = phone.get("number") or phone.get("extension")
            if not phone_id or not isinstance(phone_id, str):
                continue

            phone_id = phone_id.replace(" ", "")
            phone_ids.append(phone_id)

        return phone_ids

    @classmethod
    def __concat_ids(cls, row: pd.Series) -> List:
        ids_list = []
        # Verify if EMAILS field has a valid list (avoid pd.NA values)
        ids_list.extend(
            row[PersonColumns.EMAILS]
            if isinstance(row[PersonColumns.EMAILS], list)
            else []
        )
        # All others fields are lists at this point
        ids_list.extend(
            row[TempColumns.IM_ACCOUNT_IDS]
            + row[TempColumns.PHONE_NUMBERS]
            + row[TempColumns.TRADE_FILE_IDS]
        )
        ids_set = set(ids_list)
        ids_set.discard(None)
        ids_set.discard("")
        ids_set = {x.lower() for x in ids_set}
        return list(ids_set)
