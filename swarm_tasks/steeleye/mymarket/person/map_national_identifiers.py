from typing import List

import pandas as pd
from prefect.engine import signals
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.steeleye.mymarket.person.static import NATIONAL_IDS_LABEL_MAP


class Params(BaseParams):
    source_alpha2_nationality_col: str = Field(
        ...,
        description="Name of the column which contains the nationality (2-letter ISO code)",
    )

    source_national_id_col: str = Field(
        ...,
        description="Name of the column which contains the nationality identifier value",
    )
    target_national_id_col: str = Field(
        default="officialIdentifiers.nationalIds",
        description="Name of the target list column which contains the nationality identifier value",
    )
    source_passport_column: str = Field(
        ...,
        description="Name of the column which contains the passport number",
    )
    target_passport_column: str = Field(
        default="officialIdentifiers.passports",
        description="Name of the target list column which contains the passport number",
    )

    def input_columns(self):
        """Returns a list of required source columns."""
        return [
            self.source_alpha2_nationality_col,
            self.source_national_id_col,
            self.source_passport_column,
        ]


class DictKeys:
    ID = "id"
    LABEL = "label"


class TempColumns:
    NATIONAL_ID_LABEL = "national_id_label"


class MapNationalIdentifiers(TransformBaseTask):
    """
    This task populates the following columns:
    (a) officialIdentifiers.nationalIds, (b) officialIdentifiers.passports

    The passport and national ids are populated based on the alpha-2 nationality,
    as well as other source columns. The alpha-2 nationality should have been
    populated upstream using the Pycountry library. All invalid nationalities
    should have been discarded prior to this task.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources=None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            params=params,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Params = None,
    ) -> pd.DataFrame:
        """Class method which populates national identifiers and passports
        based on the values in the respective columns specified in the params.
        """

        if source_frame.empty:
            message = "Source frame empty, raising SKIP signal"
            raise signals.SKIP(message)

        required_source_columns = params.input_columns()
        # Add all columns to the target as this task will be upstream to other tasks
        target = pd.DataFrame(index=source_frame.index)

        # Add columns if not present
        for col in required_source_columns:
            if col not in source_frame.columns:
                source_frame[col] = pd.NA

        # Map national ids along with the appropriate labels. Get the labels
        # from nationality based on NATIONAL_IDS_LABEL_MAP. The source frame will never
        # have null nationality as these have been filtered out beforehand
        source_frame.loc[:, TempColumns.NATIONAL_ID_LABEL] = source_frame.loc[
            :, params.source_alpha2_nationality_col
        ].map(NATIONAL_IDS_LABEL_MAP)

        national_ids_reqd_cols_mask = source_frame.columns.isin(
            [params.source_national_id_col, TempColumns.NATIONAL_ID_LABEL]
        )

        # Note: as the label is an enum, we have no choice but to drop the
        # rows where label is pd.NA, even if the national_id is not null. However, this
        # is very unlikely to happen as all nationalities are in NATIONAL_IDS_LABEL_MAP
        national_ids_and_label_not_null_mask = (
            source_frame.loc[:, params.source_national_id_col].notnull()
        ) & (source_frame.loc[:, TempColumns.NATIONAL_ID_LABEL].notnull())

        target.loc[
            national_ids_and_label_not_null_mask, params.target_national_id_col
        ] = source_frame.loc[
            national_ids_and_label_not_null_mask, national_ids_reqd_cols_mask
        ].apply(
            lambda x: cls._populate_national_ids(
                national_id=x[params.source_national_id_col],
                national_id_label=x[TempColumns.NATIONAL_ID_LABEL],
            ),
            axis=1,
        )
        # Map passport number
        passport_reqd_cols_mask = source_frame.columns.isin(
            [params.source_passport_column, params.source_alpha2_nationality_col]
        )
        passport_number_not_null_mask = source_frame.loc[
            :, params.source_passport_column
        ].notnull()

        target.loc[
            passport_number_not_null_mask, params.target_passport_column
        ] = source_frame.loc[
            passport_number_not_null_mask, passport_reqd_cols_mask
        ].apply(
            lambda x: cls._populate_passport(
                passport_number=x[params.source_passport_column],
                passport_label=x[params.source_alpha2_nationality_col],
            ),
            axis=1,
        )

        return target.fillna(pd.NA)

    @staticmethod
    def _populate_national_ids(national_id: str, national_id_label: str) -> List[dict]:
        """
        Function which populates the national ids list column. The id is populated from
        the source national id value and the label is populated based on the
        2-letter nationality
        :param: national_id: national id of the person
        :param: national_id_label: label corresponding to the id (this is based on
                the nationality)
        :returns: a list containing the national identifier in the required schema format
        """
        return [{DictKeys.ID: national_id, DictKeys.LABEL: national_id_label}]

    @staticmethod
    def _populate_passport(passport_number, passport_label) -> List[dict]:
        """
        Function which populates the passport column. The id is populated from
        the source passport number value and the label is populated from the
        2-letter nationality
        :param: passport_number: passport number of the person
        :param: passport_label: label corresponding to the id (this is based on
                the 2-letter iso nationality)
        :returns: a list containing the passport in the required schema format
        """
        return [{DictKeys.ID: passport_number, DictKeys.LABEL: passport_label}]
