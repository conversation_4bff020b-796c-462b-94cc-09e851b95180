import os
import re
from datetime import datetime
from typing import List
from typing import Optional

import pandas as pd
from e164_converter.converter import Converter
from se_core_tasks.abstractions.transformations.abstract_person_transformations import (
    AbstractPersonTransformations,
)
from se_core_tasks.map.map_attribute import CastTo
from se_core_tasks.mymarket.static import PersonColumns
from se_elastic_schema.static.reference import PersonStructureType
from se_elastic_schema.static.reference import RetailOrProfessional

from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_iso_3166_country_code import CountryCodeType
from swarm_tasks.transform.map.map_iso_3166_country_code import MapIso3166CountryCode
from swarm_tasks.transform.map.map_iso_3166_country_code import (
    Params as ParamsMapIso3166CountryCode,
)


class SourceColumns:
    BUILDING = "BUILDING"
    BUSINESS_UNIT = "BUSINESS_UNIT"
    CITY = "CITY"
    COUNTRY = "COUNTRY"
    DESK = "DESK"
    FIRST_NAME = "FIRST_NAME"
    FLOOR = "FLOOR"
    GRBU = "GRBU"
    LAST_NAME = "LAST_NAME"
    MOBILE_NUMBER = "MOBILE_NUMBER"
    MONITORING_GROUP = "MONITORING_GROUP"
    NTID = "NTID"
    PRIMARY_SMTP_ADDRESS = "Primary SMTP address"
    USER = "User"


class TempColumns:
    CONCAT_NAME = "concat_name"
    CONCAT_NAME_WITH_COMMA = "concat_name_with_comma"
    DESK_WITH_PREFIX = "desk_with_prefix"
    E164_CONVERTER_MOBILE = "e164_converter_mobile"
    E164_CONVERTER_WORK = "e164_converter_work"
    FIRST_PHONE_NUMBER = "first_phone_number"
    FLOOR_WITH_PREFIX = "floor_with_prefix"
    LOWER_CASE_EMAIL = "lower_case_email"
    LOWER_CASE_FIRST_NAME = "lower_case_first_name"
    LOWER_CASE_LAST_NAME = "lower_case_last_name"
    MOBILE_PHONE = "mobile_phone"
    MOBILE_PHONE_DIALING_CODE = "mobile_phone_dialing_code"
    MOBILE_LIST = "mobile_list"
    NAME_FROM_EMAIL = "name_from_email"
    SECOND_PHONE_NUMBER = "second_phone_number"
    TITLE_CASE_FIRST_NAME = "title_case_first_name"
    TITLE_CASE_LAST_NAME = "title_case_last_name"
    USER_WITHOUT_SPACES = "user_without_spaces"
    WORK_PHONE = "work_phone"
    WORK_PHONE_DIALING_CODE = "work_phone_dialing_code"


class ColumnKeys:
    DIALING_CODE = "dialingCode"
    ID = "id"
    LABEL = "label"
    NUMBER = "number"


class ColumnLabels:
    BBG = "BBG"
    MOBILE = "MOBILE"
    LIVECHAT = "Live Chat"
    WORK = "WORK"


DATE_FORMAT = "%Y-%m-%d"
converter = Converter()


class BpPersonTransformations(AbstractPersonTransformations):
    def _pre_process(self):
        """Creates temporary fields which can be used by other methods"""
        self.pre_process_df.loc[
            :, TempColumns.TITLE_CASE_FIRST_NAME
        ] = self.source_frame.loc[:, SourceColumns.FIRST_NAME].str.title()
        self.pre_process_df.loc[
            :, TempColumns.TITLE_CASE_LAST_NAME
        ] = self.source_frame.loc[:, SourceColumns.LAST_NAME].str.title()
        self.pre_process_df.loc[
            :, TempColumns.LOWER_CASE_FIRST_NAME
        ] = self.source_frame.loc[:, SourceColumns.FIRST_NAME].str.lower()
        self.pre_process_df.loc[
            :, TempColumns.LOWER_CASE_LAST_NAME
        ] = self.source_frame.loc[:, SourceColumns.LAST_NAME].str.lower()
        self.pre_process_df.loc[:, TempColumns.USER_WITHOUT_SPACES] = (
            self.source_frame.loc[:, SourceColumns.USER]
            .str.replace(r"\s", "")
            .str.lower()
        )
        smtp_address_not_null_mask = self.source_frame.loc[
            :, SourceColumns.PRIMARY_SMTP_ADDRESS
        ].notnull()

        self.pre_process_df.loc[
            smtp_address_not_null_mask, TempColumns.LOWER_CASE_EMAIL
        ] = self.source_frame.loc[
            smtp_address_not_null_mask, SourceColumns.PRIMARY_SMTP_ADDRESS
        ].str.lower()
        self.pre_process_df.loc[
            smtp_address_not_null_mask, TempColumns.NAME_FROM_EMAIL
        ] = self.source_frame.loc[
            smtp_address_not_null_mask, SourceColumns.PRIMARY_SMTP_ADDRESS
        ].map(
            lambda x: self._create_name_identifier_from_email(x)
        )

        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                ConcatAttributes.process(
                    source_frame=self.pre_process_df,
                    params=ParamsConcatAttributes(
                        target_attribute=TempColumns.CONCAT_NAME,
                        source_attributes=[
                            TempColumns.LOWER_CASE_LAST_NAME,
                            TempColumns.LOWER_CASE_FIRST_NAME,
                        ],
                    ),
                ),
                ConcatAttributes.process(
                    source_frame=self.pre_process_df,
                    params=ParamsConcatAttributes(
                        target_attribute=TempColumns.CONCAT_NAME_WITH_COMMA,
                        source_attributes=[
                            TempColumns.LOWER_CASE_LAST_NAME,
                            TempColumns.LOWER_CASE_FIRST_NAME,
                        ],
                        delimiter=",",
                    ),
                ),
            ],
            axis=1,
        )
        self.pre_process_df = pd.concat(
            [self.pre_process_df, self._populate_phone_numbers()], axis=1
        )

    def process(self):
        """Function which calls all the required functions and thus populates all required
        columns in the target df"""
        self.pre_process()
        self.communications_emails()
        self.communications_im_accounts()
        self.communications_phone_numbers()
        self.location_home_address_country()
        self.location_office_address_address()
        self.location_office_address_city()
        self.location_office_address_country()
        self.meta_model()
        self.name()
        self.official_identifiers_branch_country()
        self.official_identifiers_employee_id()
        self.personal_details_first_name()
        self.personal_details_last_name()
        self.retail_or_professional()
        self.source_index()
        self.source_key()
        self.structure_client_date_account_opened()
        self.structure_department()
        self.structure_role()
        self.structure_type()
        self.unique_ids()
        self.post_process()
        return self.target_df

    def _post_process(self):
        """Not Implemented"""

    def _communications_emails(self) -> pd.Series:
        """Populates PersonColumns.COMMUNICATIONS_EMAILS from TempColumns.LOWER_CASE_EMAIL"""
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.LOWER_CASE_EMAIL,
                target_attribute=PersonColumns.COMMUNICATIONS_EMAILS,
                cast_to=CastTo.STRING_LIST.value,
                list_delimiter=";",
            ),
            auditor=self.auditor,
        ).loc[:, PersonColumns.COMMUNICATIONS_EMAILS]

    def _communications_im_accounts(self) -> pd.Series:
        """Populates PersonColumns.COMMUNICATIONS_IMACCOUNTS"""
        im_accounts_source_df = pd.concat(
            [
                self.pre_process_df.loc[:, TempColumns.LOWER_CASE_EMAIL],
                self.source_frame.loc[:, SourceColumns.NTID],
                self.pre_process_df.loc[:, TempColumns.CONCAT_NAME],
                self.pre_process_df.loc[:, TempColumns.CONCAT_NAME_WITH_COMMA],
                self.pre_process_df.loc[:, TempColumns.USER_WITHOUT_SPACES],
                self.pre_process_df.loc[:, TempColumns.NAME_FROM_EMAIL],
            ],
            axis=1,
        )
        result = pd.Series(index=im_accounts_source_df.index)
        # Populate im accounts
        result.loc[:] = im_accounts_source_df.apply(
            lambda x: self._concatenate_im_accounts(
                im_accounts=x,
            ),
            axis=1,
        ).fillna(pd.NA)
        return result

    @staticmethod
    def _concatenate_im_accounts(im_accounts: pd.Series) -> Optional[List[dict]]:
        """
        Concatenates all the im accounts in the im_accounts Series into a list
        of dictionaries. Each of the dictionaries has a label and id as per
        the schema.
        :param: im_accounts: Series containing the values for all im account columns
                for 1 row. The index of the series comes from the im account column
                names in the original df
        :returns: List of concatenated im_accounts in the required schema format
        """
        concat_list = []
        if not pd.isna(im_accounts[TempColumns.LOWER_CASE_EMAIL]):
            concat_list.extend(
                [
                    {
                        ColumnKeys.LABEL: ColumnLabels.BBG,
                        ColumnKeys.ID: im_accounts[TempColumns.LOWER_CASE_EMAIL],
                    },
                    {
                        ColumnKeys.LABEL: ColumnLabels.LIVECHAT,
                        ColumnKeys.ID: im_accounts[TempColumns.LOWER_CASE_EMAIL],
                    },
                    {
                        ColumnKeys.LABEL: ColumnLabels.LIVECHAT,
                        ColumnKeys.ID: im_accounts[TempColumns.NAME_FROM_EMAIL],
                    },
                ]
            )
        if not pd.isna(im_accounts[TempColumns.CONCAT_NAME]):
            # No need to check pd.isna(TempColumns.CONCAT_NAME_WITH_COMMA) as they have the same
            # input fields
            concat_list.extend(
                [
                    {
                        ColumnKeys.LABEL: ColumnLabels.LIVECHAT,
                        ColumnKeys.ID: im_accounts[TempColumns.CONCAT_NAME],
                    },
                    {
                        ColumnKeys.LABEL: ColumnLabels.LIVECHAT,
                        ColumnKeys.ID: im_accounts[TempColumns.CONCAT_NAME_WITH_COMMA],
                    },
                ]
            )

        if not pd.isna(im_accounts[TempColumns.USER_WITHOUT_SPACES]):
            concat_list.append(
                {
                    ColumnKeys.LABEL: ColumnLabels.LIVECHAT,
                    ColumnKeys.ID: im_accounts[TempColumns.USER_WITHOUT_SPACES],
                }
            )

        # Append the Unique NTID as an ImAccount id.
        concat_list.append(
            {
                ColumnKeys.LABEL: ColumnLabels.LIVECHAT,
                ColumnKeys.ID: im_accounts[SourceColumns.NTID],
            }
        )

        if not concat_list:
            return

        # Drop duplicate dicts (dicts with same label and id)
        return [dict(t) for t in {tuple(dict_.items()) for dict_ in concat_list}]

    def _communications_phone_numbers(self) -> pd.Series:
        """Populates PersonColumns.COMMUNICATIONS_PHONE_NUMBERS from the E164-normalised
        phone numbers in the pre_process_df
        """
        return self.pre_process_df.loc[:, PersonColumns.COMMUNICATIONS_PHONE_NUMBERS]

    def _counterparty_name(self) -> pd.Series:
        """Not Implemented"""

    def _details_client_mandate(self) -> pd.Series:
        """Not Implemented"""

    def _location_home_address_address(self) -> pd.Series:
        """Not Implemented"""

    def _location_home_address_city(self) -> pd.Series:
        """Not Implemented"""

    def _location_home_address_country(self) -> pd.Series:
        """Populates PersonColumns.LOCATION_HOME_ADDRESS_COUNTRY from SourceColumns.MONITORING_GROUP"""
        return self.source_frame.loc[:, SourceColumns.MONITORING_GROUP]

    def _location_home_address_postal_code(self) -> pd.Series:
        """Not Implemented"""

    def _location_office_address_address(self) -> pd.Series:
        """Populates PersonColumns.LOCATION_OFFICE_ADDRESS_ADDRESS from SourceColumns.DESK,
        SourceColumns.FLOOR and SourceColumns.Building
        """
        address_df = pd.concat(
            [
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=SourceColumns.DESK,
                        target_attribute=TempColumns.DESK_WITH_PREFIX,
                        prefix="Desk: ",
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=SourceColumns.FLOOR,
                        target_attribute=TempColumns.FLOOR_WITH_PREFIX,
                        prefix="Floor: ",
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

        return ConcatAttributes.process(
            source_frame=pd.concat(
                [address_df, self.source_frame.loc[:, SourceColumns.BUILDING]], axis=1
            ),
            params=ParamsConcatAttributes(
                target_attribute=PersonColumns.LOCATION_OFFICE_ADDRESS_ADDRESS,
                source_attributes=[
                    TempColumns.DESK_WITH_PREFIX,
                    TempColumns.FLOOR_WITH_PREFIX,
                    SourceColumns.BUILDING,
                ],
                delimiter=", ",
            ),
        ).loc[:, PersonColumns.LOCATION_OFFICE_ADDRESS_ADDRESS]

    def _location_office_address_city(self) -> pd.Series:
        """Populates PersonColumns.LOCATION_OFFICE_ADDRESS_CITY from SourceColumns.CITY"""
        return self.source_frame.loc[:, SourceColumns.CITY]

    def _location_office_address_country(self) -> pd.Series:
        """Populates PersonColumns.LOCATION_OFFICE_ADDRESS_COUNTRY from SourceColumns.COUNTRY"""
        return self.source_frame.loc[:, SourceColumns.COUNTRY]

    def _location_office_address_postal_code(self) -> pd.Series:
        """Not Implemented"""

    def _meta_model(self) -> pd.Series:
        """Abstract Method which needs to be implemented in the child class to populate __meta_model__
        __meta_model__ is a temporary column required for AssignMeta and has to be dropped downstream"""
        return pd.Series(
            data="AccountPerson",
            index=self.source_frame.index,
            name=PersonColumns.META_MODEL,
        )

    def _monitoring_custom_1(self) -> pd.Series:
        """Not Implemented"""

    def _monitoring_custom_2(self) -> pd.Series:
        """Not Implemented"""

    def _monitoring_custom_3(self) -> pd.Series:
        """Not Implemented"""

    def _monitoring_is_monitored(self) -> pd.Series:
        """Not Implemented"""

    def _monitoring_is_monitored_for(self) -> pd.Series:
        """Not Implemented"""

    def _monitoring_jurisdiction_business_line(self) -> pd.Series:
        """Not Implemented"""

    def _monitoring_jurisdiction_country(self) -> pd.Series:
        """Not Implemented"""

    def _monitoring_jurisdiction_legal_entity(self) -> pd.Series:
        """Not Implemented"""

    def _monitoring_jurisdiction_region(self) -> pd.Series:
        """Not Implemented"""

    def _monitoring_risk(self) -> pd.Series:
        """Not Implemented"""

    def _name(self) -> pd.Series:
        """Populates PersonColumns.NAME from TempColumns.TITLE_CASE_FIRST_NAME and TempColumns.TITLE_CASE_LAST_NAME"""
        return ConcatAttributes.process(
            source_frame=self.pre_process_df,
            params=ParamsConcatAttributes(
                target_attribute=PersonColumns.NAME,
                source_attributes=[
                    TempColumns.TITLE_CASE_FIRST_NAME,
                    TempColumns.TITLE_CASE_LAST_NAME,
                ],
                delimiter=" ",
            ),
        ).loc[:, PersonColumns.NAME]

    def _official_identifiers_branch_country(self) -> pd.Series:
        """Maps the 2 digit ISO-3166 country code in PersonColumns.OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY
        from the SourceColumns.COUNTRY field (which contains complete country names)"""
        return MapIso3166CountryCode.process(
            source_frame=self.source_frame,
            params=ParamsMapIso3166CountryCode(
                source_country_column=SourceColumns.COUNTRY,
                target_country_column=PersonColumns.OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY,
                target_country_code_type=CountryCodeType.ALPHA_2.value,
            ),
        ).loc[:, PersonColumns.OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY]

    def _official_identifiers_client_mandate(self) -> pd.Series:
        """Not Implemented"""

    def _official_identifiers_concat_id(self) -> pd.Series:
        """Not Implemented"""

    def _official_identifiers_employee_id(self) -> pd.Series:
        """Populates PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID from SourceColumns.NTID"""
        return self.source_frame.loc[:, SourceColumns.NTID]

    def _official_identifiers_mifir_id(self) -> pd.Series:
        """Not Implemented"""

    def _official_identifiers_mifir_id_sub_type(self) -> pd.Series:
        """Not Implemented"""

    def _official_identifiers_mifir_id_type(self) -> pd.Series:
        """Not Implemented"""

    def _official_identifiers_national_ids(self) -> pd.Series:
        """Not Implemented"""

    def _official_identifiers_passports(self) -> pd.Series:
        """Not Implemented"""

    def _official_identifiers_trader_ids(self) -> pd.Series:
        """Not Implemented"""

    def _personal_details_dob(self) -> pd.Series:
        """Not Implemented"""

    def _personal_details_first_name(self) -> pd.Series:
        """Populates PersonColumns.PERSONAL_DETAILS_FIRST_NAME from TempColumns.TITLE_CASE_FIRST_NAME"""
        return self.pre_process_df.loc[:, TempColumns.TITLE_CASE_FIRST_NAME]

    def _personal_details_middle_name(self) -> pd.Series:
        """Not Implemented"""

    def _personal_details_last_name(self) -> pd.Series:
        """Populates PersonColumns.PERSONAL_DETAILS_LAST_NAME from TempColumns.TITLE_CASE_LAST_NAME"""
        return self.pre_process_df.loc[:, TempColumns.TITLE_CASE_LAST_NAME]

    def _personal_details_nationality(self) -> pd.Series:
        """Not Implemented"""

    def _retail_or_professional(self) -> pd.Series:
        """Populates PersonColumns.RETAIL_OR_PROFESSIONAL with the static value 'Professional'"""
        return pd.Series(
            RetailOrProfessional.PROFESSIONAL.value,
            index=self.source_frame.index,
            name=PersonColumns.RETAIL_OR_PROFESSIONAL,
        )

    def _sink_file_identifiers_order_file_identifiers(self) -> pd.Series:
        """Not Implemented"""

    def _sink_file_identifiers_trade_file_identifiers(self) -> pd.Series:
        """Not Implemented"""

    def _source_index(self) -> pd.Series:
        """Returns a data frame containing the sourceIndex column"""
        return pd.Series(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            name=PersonColumns.SOURCE_INDEX,
        )

    def _source_key(self) -> pd.Series:
        """Returns a data frame containing the sourceKey column from the SWARM_FILE_URL"""
        return pd.Series(
            data=os.getenv("SWARM_FILE_URL"),
            index=self.source_frame.index,
            name=PersonColumns.SOURCE_KEY,
        )

    def _structure_client_date_account_opened(self) -> pd.Series:
        """Populates PersonColumns.STRUCTURE_CLIENT_DATE_ACCOUNT_OPENED with the current date"""
        return pd.Series(
            data=datetime.utcnow().strftime(DATE_FORMAT),
            index=self.source_frame.index,
            name=PersonColumns.STRUCTURE_CLIENT_DATE_ACCOUNT_OPENED,
        )

    def _structure_department(self) -> pd.Series:
        """Populates PersonColumns.STRUCTURE_DEPARTMENT from SourceColumns.BUSINESS_UNIT"""
        return self.source_frame.loc[:, SourceColumns.BUSINESS_UNIT]

    def _structure_desks(self) -> pd.Series:
        """Not Implemented"""

    def _structure_role(self) -> pd.Series:
        """Populates PersonColumns.STRUCTURE_ROLE from SourceColumns.GRBU"""
        return self.source_frame.loc[:, SourceColumns.GRBU]

    def _structure_type(self) -> pd.Series:
        """Populates PersonColumns.STRUCTURE_TYPE with the static value 'Contact'"""
        return pd.Series(
            PersonStructureType.CONTACT.value,
            index=self.source_frame.index,
            name=PersonColumns.STRUCTURE_TYPE,
        )

    def _unique_ids(self) -> pd.Series:
        """Populates e164-normalised phone numbers, im account ids and emails in uniqueIds"""
        unique_ids_source_df = pd.concat(
            [
                self.pre_process_df.loc[:, TempColumns.CONCAT_NAME],
                self.pre_process_df.loc[:, TempColumns.CONCAT_NAME_WITH_COMMA],
                self.pre_process_df.loc[:, TempColumns.USER_WITHOUT_SPACES],
                self.pre_process_df.loc[:, TempColumns.NAME_FROM_EMAIL],
                self.pre_process_df.loc[:, TempColumns.LOWER_CASE_EMAIL],
                self.pre_process_df.loc[:, TempColumns.MOBILE_PHONE],
                self.pre_process_df.loc[:, TempColumns.WORK_PHONE],
                self.source_frame.loc[:, SourceColumns.NTID],
            ],
            axis=1,
        )
        unique_ids_series = pd.Series(index=unique_ids_source_df.index)
        unique_ids_series.loc[:] = unique_ids_source_df.values.tolist()
        unique_ids_series = unique_ids_series.map(
            lambda x: list(
                set([ele.replace(" ", "").lower() for ele in x if not pd.isna(ele)])
            )
        )
        return unique_ids_series

    def _populate_phone_numbers(
        self,
    ) -> pd.DataFrame:
        """This method takes as input a source df containing all the source columns
        required for populating phone numbers. It E164-normalises each of these phone
        numbers (other than the extension, which is left as it is). It returns three
        temp columns containing e164-normalised mobile and phone numbers and the
        extension. It also populates the target communications.phoneNumbers column
        as per the schema.
        :returns: DataFrame with 3 normalised temp phone number columns, and a
                  consolidated phonenumbers column as per the schema
        """

        phone_df = pd.DataFrame(index=self.source_frame.index)
        phone_df.loc[:, TempColumns.MOBILE_LIST] = self.source_frame.loc[
            :, SourceColumns.MOBILE_NUMBER
        ].str.split("/")

        phone_df = pd.DataFrame(
            [
                num_list if isinstance(num_list, list) else []
                for num_list in phone_df.loc[:, TempColumns.MOBILE_LIST].to_list()
            ],
            index=phone_df.index,
            columns=[TempColumns.FIRST_PHONE_NUMBER, TempColumns.SECOND_PHONE_NUMBER],
        ).fillna(pd.NA)

        phone_df.loc[:, [TempColumns.MOBILE_PHONE, TempColumns.WORK_PHONE]] = pd.NA
        both_nums_not_null_mask = (
            phone_df.loc[
                :, [TempColumns.FIRST_PHONE_NUMBER, TempColumns.SECOND_PHONE_NUMBER]
            ]
            .notnull()
            .all(axis=1)
        )
        first_num_notnull_second_null_mask = (
            phone_df.loc[:, TempColumns.FIRST_PHONE_NUMBER].notnull()
            & phone_df.loc[:, TempColumns.SECOND_PHONE_NUMBER].isnull()
        )

        phone_df.loc[
            first_num_notnull_second_null_mask, TempColumns.MOBILE_PHONE
        ] = phone_df.loc[
            first_num_notnull_second_null_mask, TempColumns.FIRST_PHONE_NUMBER
        ]
        phone_df.loc[both_nums_not_null_mask, TempColumns.WORK_PHONE] = phone_df.loc[
            both_nums_not_null_mask, TempColumns.FIRST_PHONE_NUMBER
        ]
        phone_df.loc[both_nums_not_null_mask, TempColumns.MOBILE_PHONE] = phone_df.loc[
            both_nums_not_null_mask, TempColumns.SECOND_PHONE_NUMBER
        ]

        self._populate_e164_phone_and_dialing_code(
            phone_df=phone_df,
            phone_col=TempColumns.MOBILE_PHONE,
            e164_converter_col=TempColumns.E164_CONVERTER_MOBILE,
            target_dialing_code_col=TempColumns.MOBILE_PHONE_DIALING_CODE,
        )

        self._populate_e164_phone_and_dialing_code(
            phone_df=phone_df,
            phone_col=TempColumns.WORK_PHONE,
            e164_converter_col=TempColumns.E164_CONVERTER_WORK,
            target_dialing_code_col=TempColumns.WORK_PHONE_DIALING_CODE,
        )

        normalised_phone_number_cols = [
            TempColumns.MOBILE_PHONE,
            TempColumns.MOBILE_PHONE_DIALING_CODE,
            TempColumns.WORK_PHONE,
            TempColumns.WORK_PHONE_DIALING_CODE,
        ]

        phone_no_columns_mask = phone_df.columns.isin(normalised_phone_number_cols)
        phone_df.loc[:, PersonColumns.COMMUNICATIONS_PHONE_NUMBERS] = (
            phone_df.loc[:, phone_no_columns_mask]
            .apply(self._concatenate_phone_numbers, axis=1)
            .fillna(pd.NA)
        )
        # Drop all temp columns columns other than the phone number columns
        columns_to_drop = [
            TempColumns.MOBILE_PHONE_DIALING_CODE,
            TempColumns.WORK_PHONE_DIALING_CODE,
            TempColumns.E164_CONVERTER_MOBILE,
            TempColumns.E164_CONVERTER_WORK,
            TempColumns.FIRST_PHONE_NUMBER,
            TempColumns.SECOND_PHONE_NUMBER,
        ]
        phone_df = phone_df.drop(columns_to_drop, axis=1)
        return phone_df

    @staticmethod
    def _populate_e164_phone_and_dialing_code(
        phone_df: pd.DataFrame,
        phone_col: str,
        e164_converter_col: str,
        target_dialing_code_col: str,
    ) -> None:
        """
        Populates the phone number and dialling code in separate columns after E164-normalising
        the input phone column (phone_col)
        :param phone_df: DataFrame containing the phone number
        :param phone_col: Input phone number column. After normalisation, this column is overwritten with
        the e164-normalised number
        :param e164_converter_col: Name of the temporary e164-converter column
        :param target_dialing_code_col: Column containing the dialing code
        :return: None (it mutates the phone_df dataframe by adding columns)
        """

        num_present_mask = phone_df.loc[:, phone_col].notnull()

        phone_df.loc[num_present_mask, e164_converter_col] = phone_df.loc[
            num_present_mask, phone_col
        ].map(lambda x: converter.convert(x))

        phone_df.loc[num_present_mask, phone_col] = phone_df.loc[
            num_present_mask, e164_converter_col
        ].map(
            lambda e164_result: e164_result.transformed
            if e164_result.e164_derived
            else e164_result.raw
        )

        # Note: the dialing code is obtained from the country code field.
        phone_df.loc[num_present_mask, target_dialing_code_col] = phone_df.loc[
            num_present_mask, e164_converter_col
        ].map(
            lambda e164_result: e164_result.country_code
            if e164_result.e164_derived
            else pd.NA
        )

    @staticmethod
    def _concatenate_phone_numbers(phone_numbers: pd.Series) -> Optional[List[dict]]:
        """
        Concatenates all the phone numbers in the im_accounts Series into a list
        of dictionaries. The work and mobile phone dictionaries have a label and
        id, while the extension dictionary has a single key 'extension'.
        :param: phone_numbers: Series containing the values for all phone no. columns for
                1 row. The index of the series comes from the column names of the temp
                phone number columns created in the calling function
        :returns: List of concatenated phone numbers in the required format
        """
        concat_list = []
        if not pd.isna(phone_numbers[TempColumns.MOBILE_PHONE]):
            mobile_dict = {
                ColumnKeys.LABEL: ColumnLabels.MOBILE,
                ColumnKeys.NUMBER: phone_numbers[TempColumns.MOBILE_PHONE],
            }
            # Add dialling code if not null
            if not pd.isna(phone_numbers[TempColumns.MOBILE_PHONE_DIALING_CODE]):
                mobile_dict[ColumnKeys.DIALING_CODE] = phone_numbers[
                    TempColumns.MOBILE_PHONE_DIALING_CODE
                ]

            concat_list.append(mobile_dict)
        if not pd.isna(phone_numbers[TempColumns.WORK_PHONE]):
            work_dict = {
                ColumnKeys.LABEL: ColumnLabels.WORK,
                ColumnKeys.NUMBER: phone_numbers[TempColumns.WORK_PHONE],
            }
            if not pd.isna(phone_numbers[TempColumns.WORK_PHONE_DIALING_CODE]):
                work_dict[ColumnKeys.DIALING_CODE] = phone_numbers[
                    TempColumns.WORK_PHONE_DIALING_CODE
                ]
            concat_list.append(work_dict)

        return None if not concat_list else concat_list

    @staticmethod
    def _create_name_identifier_from_email(email_address: str) -> str:
        """
        For an <NAME_EMAIL>, return a comma-separate
        flipped username without special characters/numbers, i.e. doe,john
        :param email_address: email address (str)
        :return:
        """
        username = email_address.split("@")[0]
        # Split the user name by '.' and reverse the resulting names
        final_name = ",".join(
            [re.sub("[^A-Za-z]+", "", name) for name in username.split(".")[::-1]]
        )
        return final_name.lower()
