import pandas as pd
from se_elastic_schema.static.reference import ImAccountType

from swarm_tasks.abstractions.transformations.mymarket.abstract_market_suggestion_transformations import (
    AbstractMarketSuggestionTransformations,
)
from swarm_tasks.abstractions.transformations.mymarket.static import (
    MarketSuggestionColumns,
)
from swarm_tasks.utilities.static import MetaModel


class SlackSourceColumns:
    EMAIL = "email"
    NAME = "name"
    SLACK_ID = "slack_id"


class ImAccountKeys:
    ID = "id"
    LABEL = "label"


class SlackMarketSuggestionTransformations(AbstractMarketSuggestionTransformations):
    def _pre_process(self) -> pd.DataFrame:
        """Not Implemented"""

    def process(self) -> pd.DataFrame:
        self.pre_process()

        self.emails()
        self.im_accounts()
        self.meta_model()
        self.name()

        self.post_process()
        return self.target_df

    def _company(self) -> pd.Series:
        """Not Implemented"""

    def _emails(self) -> pd.Series:
        """
        Populates the email in a list from SlackSourceColumns.EMAIL
        """
        source_emails_not_null_mask = self.source_frame.loc[
            :, SlackSourceColumns.EMAIL
        ].notnull()
        return (
            self.source_frame.loc[source_emails_not_null_mask, SlackSourceColumns.EMAIL]
            .str.lower()
            .apply(lambda x: [x])
        )

    def _name(self) -> pd.Series:
        """
        Populates the name from SlackSourceColumns.NAME
        """
        return self.source_frame.loc[:, SlackSourceColumns.NAME]

    def _sink_identifiers_order_file_identifiers(self) -> pd.Series:
        """Not Implemented"""

    def _sink_identifiers_trade_file_identifiers(self) -> pd.Series:
        """Not Implemented"""

    def _source(self) -> pd.Series:
        """Not Implemented"""

    def _im_accounts(self) -> pd.Series:
        """Populates the imAccounts field in the expected schema format.
        Note that this is used to create the field 'imAccountIds` (format: [Slack_<slack_id>,...] by the
        foll. pre-validator in se-schema:
        https://github.com/steeleye/schema/blob/0470a23e71aae060ef35f1eda342d5e38605244e/se_schema/models/market/suggestion/model.py#L53
        """

        return (
            self.source_frame.loc[:, SlackSourceColumns.SLACK_ID]
            .str.lower()
            .apply(
                lambda x: [
                    {
                        ImAccountKeys.ID: x,
                        ImAccountKeys.LABEL: ImAccountType.SLACK.value,
                    }
                ]
            )
        )

    def _meta_model(self) -> pd.Series:
        """Populates the __meta_model__ field"""
        return pd.Series(
            data=MetaModel.MARKET_SUGGESTION,
            index=self.source_frame.index,
            name=MarketSuggestionColumns.META_MODEL,
        )

    def _post_process(self) -> pd.DataFrame:
        """Not Implemented"""
