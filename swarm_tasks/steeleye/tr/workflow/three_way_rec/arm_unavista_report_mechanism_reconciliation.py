from typing import List
from typing import Optional
from typing import Tuple

import pandas as pd
from addict import Dict
from se_elastic_schema.validators.iso.lei import LEI
from se_elastic_schema.validators.iso.lei import LEIError

from swarm_tasks.steeleye.tr.static import ARMReport
from swarm_tasks.steeleye.tr.static import Re<PERSON><PERSON>ields
from swarm_tasks.steeleye.tr.static import ReportDetailsIdFields
from swarm_tasks.steeleye.tr.static import RTS22Transaction
from swarm_tasks.steeleye.tr.static import StaticValues
from swarm_tasks.steeleye.tr.static import TimeFormats
from swarm_tasks.steeleye.tr.workflow.three_way_rec.report_mechanism_reconciliation import (
    ReportMechanismReconciliation,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.enums import ReportTypeEnum
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    ARM_BUYER_SELLER_AND_DECISION_MAKER_MAP,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    ARM_GENERAL_ONE_TO_ONE_MAP,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    ARM_INSTRUMENT_ONE_TO_ONE_MAP,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    ARM_PARTY_CONDITIONAL_LIST_MAP,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    ARM_PARTY_LIST_MAP,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    ARM_PARTY_ONE_TO_ONE_MAP,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    ARM_PARTY_TR_PI_ENRICHMENT_BUILD_MAP,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.twr_dataclasses import (
    ReconcilationNestedPartyList,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.twr_utils import (
    clnt_nore_check,
)


class ArmUnavistaReportMechanismReconciliation(ReportMechanismReconciliation):
    def __init__(self):
        super().__init__(report_type=ReportTypeEnum.ARM_UNAVISTA)

    @staticmethod
    def link_preprocess_data(df: pd.DataFrame) -> pd.DataFrame:
        return df

    @staticmethod
    def get_report_status_masks(report_transactions: pd.DataFrame) -> List:
        return [
            report_transactions.loc[:, ARMReport.REPORT_STATUS] == status
            for status in [ReportDetailsIdFields.NEWT, ReportDetailsIdFields.CANC]
        ]

    @staticmethod
    def get_unique_transaction_ids(
        report_transactions: pd.DataFrame,
        report_status_newt_mask: pd.Series,
        report_status_cancel_mask: pd.Series,
    ) -> List:

        return [
            report_transactions.loc[mask, ARMReport.TRANSACTION_REF_NO].dropna()
            for mask in [report_status_newt_mask, report_status_cancel_mask]
        ]

    @staticmethod
    def merge_transactions(
        report_transactions: pd.DataFrame, rts22_transactions: pd.DataFrame
    ) -> pd.DataFrame:

        return report_transactions.merge(
            rts22_transactions,
            how="left",
            left_on=[ARMReport.TRANSACTION_REF_NO, ARMReport.REPORT_STATUS],
            right_on=[
                ReportDetailsIdFields.TRANSACTION_REF_NO,
                ReportDetailsIdFields.REPORT_DETAILS_REPORT_STATUS,
            ],
        )

    @staticmethod
    def reconcile_preprocess_data(source_df: pd.DataFrame, target_df: pd.DataFrame):

        target_df[RecFields.RECONCILIATION_ARM_FIELD_BREAK] = False
        target_df[RecFields.RECONCILIATION_ARM_MATCH] = True
        target_df[RecFields.RECONCILIATION_ARM_SOURCE_KEY] = source_df.loc[
            :, RecFields.TEMP_RECONCILIATION_ARM_SOURCE_KEY
        ]

    def reconcile_workflow_fields(
        self,
        workflow_mapping_result: dict,
        data_to_reconcile: pd.DataFrame,
        target_df: pd.DataFrame,
    ):
        return

    @staticmethod
    def get_cancelled_one_to_one_map() -> dict:

        return {
            ARMReport.EXECUTING_ENTITY_ID: RTS22Transaction.PARTIES_EXECUTING_ENTITY_FIRM_IDENTIFIERS_LEI,
        }

    @staticmethod
    def _sanitize_report_series(report_series: pd.Series):
        """
        Sanitize ARM_UNAVISTA report data
        :param report_series: Pandas Series with data from a given column of the ARM_UNAVISTA report
        """

        not_null_mask = report_series.notnull()

        if not_null_mask.any():

            # Convert bools to lowercase strings
            report_series.loc[not_null_mask] = report_series.loc[not_null_mask].map(
                lambda x: str(x).lower()
                if str(x).lower() in ["true", "false"]
                else (x if isinstance(x, list) else str(x))
            )

            # Remove trailing zeros from numerical strings
            report_series.loc[not_null_mask] = report_series.loc[not_null_mask].map(
                lambda x: str(float(x))
                if x.count(".") == 1 and x.replace(".", "").isnumeric()
                else x
            )

    @staticmethod
    def get_general_one_to_one_map() -> dict:

        return ARM_GENERAL_ONE_TO_ONE_MAP

    @staticmethod
    def get_party_one_to_one_reconcile_map() -> dict:

        return ARM_PARTY_ONE_TO_ONE_MAP

    @staticmethod
    def get_party_one_to_one_build_map() -> Tuple:

        return ARM_PARTY_ONE_TO_ONE_MAP, ARM_PARTY_TR_PI_ENRICHMENT_BUILD_MAP

    @staticmethod
    def get_instrument_one_to_one_map() -> dict:

        return ARM_INSTRUMENT_ONE_TO_ONE_MAP

    def reconcile_compound_general_fields(
        self, data_to_reconcile: pd.DataFrame, target: pd.DataFrame
    ):
        return

    def reconcile_compound_instrument_fields(
        self, data_to_reconcile: pd.DataFrame, target: pd.DataFrame
    ):
        return

    def get_party_list_map(self) -> List:
        return ARM_PARTY_LIST_MAP

    def _add_buyer_and_sell_intl_alternative(
        self, report_field_name, report_series, data_to_reconcile
    ):
        return

    @staticmethod
    def get_party_conditional_list_map() -> List:
        return ARM_PARTY_CONDITIONAL_LIST_MAP

    @staticmethod
    def get_price_pending_report_field() -> str:
        return ARMReport.PRICE_TYPE

    @staticmethod
    def get_price_pending_report_dictionary() -> dict:
        return {
            RTS22Transaction.TRANSACTION_DETAILS_PRICE_PENDING: StaticValues.PRICE_PENDING,
            RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOT_APPLICABLE: StaticValues.PRICE_NOT_AVAILABLE,
        }

    @staticmethod
    def get_source_waiver_column() -> str:
        return ARMReport.WAIVER_INDICATOR

    @staticmethod
    def parse_one_to_list_mapping(
        source_data: pd.DataFrame,
        source_column: str,
        target_column: str,
        delimiter=";",
    ) -> dict:
        """
        Build a Pandas Series with report `source_column` data to compare with a RTS22Transaction
        `target_column` field

        :param source_data: Pandas DataFrame with data to reconcile
        :param source_column: Report column to reconcile
        :param target_column: Name of the RTS22Transaction field to reconcile
        :param delimiter: Delimiter used in ARM_UNAVISTA reports to separate multiple values in the same cell
        :return:
        """

        result = {}
        result_series = pd.Series(index=source_data.index)

        if source_column in source_data.columns:
            not_null_mask = source_data.loc[:, source_column].notnull()
            if not_null_mask.any():

                result_series.loc[not_null_mask] = source_data.loc[
                    not_null_mask, source_column
                ].apply(lambda x: x.split(delimiter) if delimiter in x else [x])

        result[target_column] = result_series

        return result

    @staticmethod
    def get_source_otc_column():
        return ARMReport.OTC_POST_TRADE_INDICATOR

    def parse_additional_custom_fields(
        self, source_data: pd.DataFrame, custom_mapping_result: dict
    ):

        # `transactionDetails.tradingDateTime`
        custom_mapping_result.update(
            self.parse_trading_datetime(source_data=source_data)
        )

        # `transactionDetails.priceNotation`
        custom_mapping_result.update(self.parse_price_notation(source_data=source_data))

        # `instrumentDetails.instrument.derivative.underlyingIndexTerm`
        custom_mapping_result.update(
            self.parse_underlying_index_term(source_data=source_data)
        )

        # `transactionDetails.quantityNotation`
        custom_mapping_result.update(
            self.parse_quantity_notation(source_data=source_data)
        )

        # `instrumentDetails.instrument.ext.strikePriceType`
        custom_mapping_result.update(
            self.parse_strike_price_type(source_data=source_data)
        )

    @staticmethod
    def parse_trading_datetime(source_data: pd.DataFrame) -> dict:
        """
        Return a dictionary with a Pandas Series with data from the ARM_UNAVISTA report to populate the
        `transactionDetails.tradingDateTime` field. The datetime value is parsed to match exactly what
        is stored in SteelEye records.

        :param source_data: Pandas DataFrame with Data to be reconciled
        :return: Dictionary with a Pandas Series
        """
        result = {}
        trading_datetime_column = ARMReport.TRADING_DATE_TIME
        mapped_report_column = pd.Series(data=pd.NaT, index=source_data.index)

        if trading_datetime_column in source_data.columns:

            trading_datetime_not_null_mask = (
                source_data.loc[:, trading_datetime_column]
            ).notnull()

            mapped_report_column.loc[trading_datetime_not_null_mask] = (
                source_data.loc[trading_datetime_not_null_mask, trading_datetime_column]
                .map(
                    lambda x: pd.to_datetime(x, format=TimeFormats.ARM_DATETIME_FORMAT)
                )
                .dt.strftime(TimeFormats.DATETIME)
            )

        result[
            RTS22Transaction.TRANSACTION_DETAILS_TRADING_DATE_TIME
        ] = mapped_report_column

        return result

    @staticmethod
    def parse_strike_price_type(source_data: pd.DataFrame) -> dict:
        """
        Return a dictionary with a Pandas Series with data from the ARM_UNAVISTA report to populate the
        `instrumentDetails.instrument.ext.strikePriceType` field. The value is parsed to match exactly what
        is stored in SteelEye records.

        :param source_data: Pandas DataFrame with Data to be reconciled
        :return: Dictionary with a Pandas Series
        """
        result = {}
        strike_price_type_column = ARMReport.STRIKE_PRICE_TYPE
        mapped_report_column = pd.Series(data=pd.NaT, index=source_data.index)

        if strike_price_type_column in source_data.columns:
            strike_price_type_not_null_mask = (
                source_data.loc[:, strike_price_type_column]
            ).notnull()

            mapped_report_column.loc[strike_price_type_not_null_mask] = source_data.loc[
                strike_price_type_not_null_mask, strike_price_type_column
            ].replace(
                {
                    StaticValues.PCTG: StaticValues.PCTG.upper(),
                    StaticValues.YLD: StaticValues.YLD,
                    StaticValues.MNTRYVAL_AMT: StaticValues.MNTRYVAL,
                    StaticValues.BSISPTS: StaticValues.BSISPTS.upper(),
                }
            )

        result[
            RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_EXT_STRIKE_PRICE_TYPE
        ] = mapped_report_column

        return result

    @staticmethod
    def parse_price_notation(source_data: pd.DataFrame) -> dict:
        """
         Return a dictionary with a Pandas Series with data from the ARM_UNAVISTA report to populate the
        `transactionDetails.priceNotation` field.

        :param source_data: Pandas DataFrame with Data to be reconciled
        :return: Dictionary with Pandas Series
        """

        result = {}
        price_type_column = ARMReport.PRICE_TYPE
        mapped_report_column = pd.Series(data=pd.NA, index=source_data.index)

        if price_type_column in source_data.columns:

            price_type_not_null_mask = (source_data.loc[:, price_type_column]).notnull()

            mapped_report_column.loc[price_type_not_null_mask] = source_data.loc[
                price_type_not_null_mask, price_type_column
            ].map(
                {
                    StaticValues.MNTRYVAL_AMT: StaticValues.MONE,
                    StaticValues.PCTG: StaticValues.PERC,
                    StaticValues.YLD: StaticValues.YIEL,
                    StaticValues.BSISPTS: StaticValues.BAPO,
                }
            )

        result[
            RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOTATION
        ] = mapped_report_column

        return result

    @staticmethod
    def parse_underlying_index_term(source_data: pd.DataFrame) -> dict:
        """
        Return a dictionary with a Pandas Series with data from the ARM_UNAVISTA report to populate the
        `instrumentDetails.instrument.derivative.underlyingIndexTerm`
         and `instrumentDetails.instrument.derivative.underlyingIndexTermValue` fields.

        :param source_data: Pandas DataFrame with Data to be reconciled
        :return: Dictionary with Pandas Series
        """

        result = {}
        underlying_it_column = ARMReport.UNDERLYING_INDEX_TERM
        underlying_index_term = pd.Series(data=pd.NA, index=source_data.index)
        underlying_index_term_value = pd.Series(data=pd.NA, index=source_data.index)

        if underlying_it_column in source_data.columns:

            underlying_it_not_null_mask = (
                source_data.loc[:, underlying_it_column]
            ).notnull()

            # keep alphabetic letters only
            underlying_index_term.loc[underlying_it_not_null_mask] = source_data.loc[
                underlying_it_not_null_mask, underlying_it_column
            ].str.replace("[^0-9] ", "", regex=True)

            # keep digits only
            underlying_index_term_value.loc[
                underlying_it_not_null_mask
            ] = source_data.loc[
                underlying_it_not_null_mask, underlying_it_column
            ].str.replace(
                "[A-Za-z]", "", regex=True
            )

        result[
            RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_TERM
        ] = underlying_index_term
        result[
            RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_TERM_VALUE
        ] = underlying_index_term_value

        return result

    @staticmethod
    def get_ul_instrument_id_report_columns() -> List[str]:
        """
        Get List of Underlying Instrument ID columns from ARM_UNAVISTA report

        :return: List of the names of the columns that might contain Underlying Instrument ID data
        """
        return [ARMReport.UNDERLYING_INSTRUMENT_ID]

    @classmethod
    def parse_ul_instrument_report_series(
        cls,
        report_columns: List[str],
        source_data: pd.DataFrame,
        nested_ordered_dict_field,
        delimiter=";",
    ) -> pd.Series:
        """
        For ARM_UNAVISTA, `report_columns` should be a list with a single column

        Build a Pandas Series with report `report_columns` data to compare with a RTS22Transaction field.
        For NCA_FCA, if there are multiple underlying instruments they will be stored in `report_columns` as
        Ordered Dicts, but not all values within these dicts are necessary, thus the `nested_ordered_dict_field`
        is used to retrieve the relevant field (i.e `ISIN` field from
        `FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Sngl`)

        :param report_columns: List of report column/s that might hold underlying instrument data
        :param source_data: Pandas DataFrame with data that will be reconciled
        :param nested_ordered_dict_field: Name of the field that contains the relevant data
            within the nested ordered dicts
        :param delimiter:
        :return: Delimiter used in ARM_UNAVISTA reports to separate multiple values in the same cell
        """

        # If there is only one underlying instrument, we will have a column with the
        # `nested_ordered_dict_field` suffix  with that value alone,
        # however, for multiple underlying instruments, we will have a column without the suffix, containing
        # multiple OrderedDict, where the suffix is the name of the dict key

        report_cols_mask = source_data.columns.isin(report_columns)
        report_df = source_data.loc[:, report_cols_mask]
        report_series = pd.Series(data=pd.NA, index=source_data.index)
        report_df_not_null_mask = report_df.loc[:, :].notnull().any(1)

        if report_df_not_null_mask.any():

            report_series.loc[report_df_not_null_mask] = report_df.loc[
                report_df_not_null_mask, report_columns[0]
            ].apply(
                lambda x: cls.convert_empty_set_to_pd_na(
                    sorted(set(x.split(delimiter)))
                )
            )

        return report_series

    @staticmethod
    def get_ul_index_id_report_columns() -> List[str]:
        return [ARMReport.UNDERLYING_INDEX_ID]

    @staticmethod
    def get_ul_index_name_report_columns() -> List[str]:

        return [ARMReport.UNDERLYING_INDEX_NAME]

    def build_preprocess_data(self, source_frame: pd.DataFrame, target: pd.DataFrame):

        target.loc[:, RecFields.RECONCILIATION_ARM_MATCH] = False
        target.loc[:, RecFields.RECONCILIATION_ARM_SOURCE_KEY] = source_frame.loc[
            :, RecFields.TEMP_RECONCILIATION_ARM_SOURCE_KEY
        ]

    def populate_compound_mappings(
        self, source_data: pd.DataFrame, target: pd.DataFrame
    ):
        return

    @staticmethod
    def get_buyer_seller_and_decision_maker_map():
        return ARM_BUYER_SELLER_AND_DECISION_MAKER_MAP

    def _populate_firm_identifiers_lei(
        self,
        row: pd.Series,
        party_mapping_data: ReconcilationNestedPartyList,
        mapping_result: dict,
    ):
        return

    def _populate_branch_country(
        self, mapping_result: dict, branch_country: Optional[str]
    ):

        try:

            LEI.validate(mapping_result.get(RTS22Transaction.FIRM_IDENTIFIERS_LEI))
            mapping_result[
                RTS22Transaction.FIRM_IDENTIFIERS_BRANCH_COUNTRY
            ] = branch_country
            valid_lei = True

        except (TypeError, LEIError):

            mapping_result[
                RTS22Transaction.OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY
            ] = branch_country
            valid_lei = False

        return valid_lei

    @staticmethod
    def _populate_model_prefix(mapping_result: dict, valid_lei: bool) -> str:

        model_prefix = "MarketCounterparty" if valid_lei else "AccountPerson"
        mapping_result[RTS22Transaction.OFFICIAL_IDENTIFIERS_MIFIR_ID] = (
            pd.NA
            if valid_lei
            else mapping_result[RTS22Transaction.FIRM_IDENTIFIERS_LEI]
        )
        mapping_result[RTS22Transaction.FIRM_IDENTIFIERS_LEI] = (
            pd.NA
            if not valid_lei
            else mapping_result[RTS22Transaction.FIRM_IDENTIFIERS_LEI]
        )
        return model_prefix

    def populate_compound_instrument_mappings(
        self, source_data: pd.DataFrame, target: pd.DataFrame
    ):
        return

    def populate_additional_custom_fields(
        self, source_data: pd.DataFrame, target: pd.DataFrame
    ):

        # transactionDetails.quantityNotation
        notation_map = {
            StaticValues.UNIT: StaticValues.UNIT,
            StaticValues.NOMINAL_VALUE: StaticValues.NOML,
            StaticValues.MONETARY_VALUE: StaticValues.MONE,
        }

        if ARMReport.QUANTITY_TYPE in source_data.columns:
            target.loc[:, RTS22Transaction.TRANSACTION_DETAILS_QUANTITY_NOTATION] = (
                source_data.loc[:, ARMReport.QUANTITY_TYPE]
                .str.upper()
                .replace(notation_map)
            )

        # transactionDetails.tradingDateTime
        if ARMReport.TRADING_DATE_TIME in source_data.columns:
            target.loc[
                :, RTS22Transaction.TRANSACTION_DETAILS_TRADING_DATE_TIME
            ] = self.parse_trading_datetime(source_data=source_data).get(
                RTS22Transaction.TRANSACTION_DETAILS_TRADING_DATE_TIME
            )

        # transactionDetails.priceNotation
        if ARMReport.PRICE_TYPE in source_data.columns:
            target.loc[
                :, RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOTATION
            ] = self.parse_price_notation(source_data=source_data).get(
                RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOTATION
            )

        # instrumentDetails.instrument.derivative.underlyingIndexTerm
        # & instrumentDetails.instrument.derivative.underlyingIndexTermValue
        if ARMReport.UNDERLYING_INDEX_TERM in source_data.columns:
            underlying_index_term = self.parse_underlying_index_term(
                source_data=source_data
            )
            target.loc[
                :,
                RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_TERM,
            ] = underlying_index_term.get(
                RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_TERM
            )
            target.loc[
                :,
                RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_TERM_VALUE,
            ] = underlying_index_term.get(
                RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_TERM_VALUE
            )

        # instrumentDetails.instrument.ext.strikePriceType
        if ARMReport.STRIKE_PRICE_TYPE in source_data.columns:
            target.loc[
                :, RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_EXT_STRIKE_PRICE_TYPE
            ] = source_data.loc[:, ARMReport.STRIKE_PRICE_TYPE].replace(
                {
                    StaticValues.PCTG: StaticValues.PCTG.upper(),
                    StaticValues.YLD: StaticValues.YLD,
                    StaticValues.MNTRYVAL_AMT: StaticValues.MNTRYVAL,
                    StaticValues.BSISPTS: StaticValues.BSISPTS.upper(),
                }
            )

    @staticmethod
    def get_trading_datetime_field() -> str:
        return ARMReport.TRADING_DATE_TIME

    @staticmethod
    def get_trading_datetime_format() -> str:
        return "%Y-%m-%d %H:%M:%S.%f"

    def parse_execution_within_firm_country_branch(
        self,
        source_data: pd.DataFrame,
        tenant_configuration: Dict,
    ) -> pd.Series:

        result = pd.Series(data=pd.NA, index=source_data.index)
        # The easiest way to `not reconcile` is to reconcile with values that will not create field breaks
        se_branch_country = (
            source_data.loc[
                :,
                RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM_OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY,
            ]
            if RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM_OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY
            in source_data.columns
            else pd.Series(data=pd.NA, index=source_data.index)
        )
        result.loc[:] = se_branch_country

        if ARMReport.FIRM_EXECUTION_ID_TYPE in source_data.columns:
            firm_execution_id_type_not_a_mask = (
                source_data.loc[:, ARMReport.FIRM_EXECUTION_ID_TYPE].str.upper() != "A"
            )
        else:
            firm_execution_id_type_not_a_mask = pd.Series(
                data=True, index=source_data.index
            )

        report_branch_country = (
            source_data.loc[:, ARMReport.FIRM_EXECUTION_COUNTRY_OF_BRANCH]
            if ARMReport.FIRM_EXECUTION_COUNTRY_OF_BRANCH in source_data.columns
            else pd.Series(data=pd.NA, index=source_data.index)
        )
        result.loc[firm_execution_id_type_not_a_mask] = report_branch_country.loc[
            firm_execution_id_type_not_a_mask
        ]

        return result

    def parse_investment_decision_within_firm_country_branch(
        self,
        source_data: pd.DataFrame,
        tenant_configuration: Dict,
    ) -> pd.Series:

        result = pd.Series(data=pd.NA, index=source_data.index)
        # The easiest way to `not reconcile` is to reconcile with values that will not create field breaks
        se_branch_country = (
            source_data.loc[
                :,
                RTS22Transaction.PARTIES_INV_DEC_WITHIN_FIRM_OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY,
            ]
            if RTS22Transaction.PARTIES_INV_DEC_WITHIN_FIRM_OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY
            in source_data.columns
            else pd.Series(data=pd.NA, index=source_data.index)
        )
        result.loc[:] = se_branch_country

        if ARMReport.INVESTMENT_DECISION_ID_TYPE in source_data.columns:
            firm_inv_dec_id_type_not_a_mask = (
                source_data.loc[:, ARMReport.INVESTMENT_DECISION_ID_TYPE].str.upper()
                != "A"
            )
        else:
            firm_inv_dec_id_type_not_a_mask = pd.Series(
                data=True, index=source_data.index
            )

        report_branch_country = (
            source_data.loc[:, ARMReport.INVESTMENT_DECISION_COUNTRY_OF_BRANCH]
            if ARMReport.INVESTMENT_DECISION_COUNTRY_OF_BRANCH in source_data.columns
            else pd.Series(data=pd.NA, index=source_data.index)
        )
        result.loc[firm_inv_dec_id_type_not_a_mask] = report_branch_country.loc[
            firm_inv_dec_id_type_not_a_mask
        ]

        return result

    def reconcile_compound_party_fields(
        self,
        data_to_reconcile: pd.DataFrame,
        target: pd.DataFrame,
        tenant_configuration: Dict,
    ):

        """
        Reconcile Compound Party fields which are mapped by a combination of fields

        :param data_to_reconcile: Source Frame which will be reconciled
        :param target: DataFrame with reconciliation results
        :param tenant_configuration: Tenant Configuration Addict
        """

        if (
            data_to_reconcile.empty
            or tenant_configuration.trPIEnrichmentEnabled is True
        ):
            return

        report_map = {
            RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM_MIFIR_ID: ARMReport.FIRM_EXECUTION_ID,
            RTS22Transaction.PARTIES_INV_DEC_WITHIN_FIRM_MIFIR_ID: ARMReport.INVESTMENT_DECISION_ID,
        }

        algo_variants_map = {
            RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM_MIFIR_ID: [
                RTS22Transaction.PARTIES_EXEC_WT_FIRM_STRCT_EXEC_WT_FIRM_ID_TYPE,
                RTS22Transaction.PARTIES_EXEC_WT_FIRM_STRCT_EXEC_WT_FIRM_ID,
                RTS22Transaction.MARKET_IDENTIFIERS,
            ],
            RTS22Transaction.PARTIES_INV_DEC_WITHIN_FIRM_MIFIR_ID: [
                RTS22Transaction.PARTIES_INV_DEC_WT_FIRM_STRCT_EXEC_WT_FIRM_ID_TYPE,
                RTS22Transaction.PARTIES_INV_DEC_WT_FIRM_STRCT_EXEC_WT_FIRM_ID,
                "dummy",
            ],
        }

        for key in report_map:

            report_mapped_column = (
                data_to_reconcile.loc[:, report_map[key]]
                if report_map[key] in data_to_reconcile.columns
                else pd.Series(data=pd.NA, index=data_to_reconcile.index)
            )

            steeleye_series_field_names = pd.Series(
                data=key, index=data_to_reconcile.index
            )
            steeleye_series = (
                data_to_reconcile.loc[:, key]
                if key in data_to_reconcile.columns
                else pd.Series(data=pd.NA, index=data_to_reconcile.index)
            )

            id_type_column = algo_variants_map[key][0]
            id_column = algo_variants_map[key][1]

            if (
                len({id_type_column, id_column}.intersection(data_to_reconcile.columns))
                == 2
            ):
                id_type_a_mask = (
                    data_to_reconcile.loc[:, id_type_column].str.upper() == "A"
                )
                steeleye_series.loc[id_type_a_mask] = data_to_reconcile.loc[
                    id_type_a_mask, id_column
                ]
                steeleye_series_field_names.loc[id_type_a_mask] = id_column

            clnt_nore_column = algo_variants_map[key][2]

            if clnt_nore_column in data_to_reconcile.columns.to_list():
                clnt_nore_check(
                    clnt_nore_column=clnt_nore_column,
                    data_to_reconcile=data_to_reconcile,
                    steeleye_series=steeleye_series,
                    steeleye_series_field_names=steeleye_series_field_names,
                )

            steeleye_series = self._remove_non_significant_zero(steeleye_series)

            self.compare_report_and_steeleye_fields(
                report_series=report_mapped_column,
                steeleye_series=steeleye_series,
                target=target,
                steeleye_field_names=steeleye_series_field_names,
            )

    def populate_compound_party_mappings(
        self, source_data: pd.DataFrame, target: pd.DataFrame
    ):
        return

    def get_cancels_count(self, dataframe: pd.DataFrame) -> int:

        count = 0
        cancel_col = ARMReport.REPORT_STATUS
        if cancel_col in dataframe.columns:
            cancel_mask = (
                dataframe.loc[:, cancel_col].str.upper() == ReportDetailsIdFields.CANC
            )
            count += dataframe.loc[cancel_mask, cancel_col].shape[0]
        return count

    def parse_quantity_notation(self, source_data: pd.DataFrame) -> dict:

        """
         Return a dictionary with a Pandas Series with data from the ARM_UNAVISTA report to populate the
        `transactionDetails.quantityNotation` field.

        :param source_data: Pandas DataFrame with Data to be reconciled
        :return: Dictionary with Pandas Series
        """

        result = {}
        quantity_type_column = ARMReport.QUANTITY_TYPE
        mapped_report_column = pd.Series(data=pd.NA, index=source_data.index)
        notation_map = {
            StaticValues.UNIT: StaticValues.UNIT,
            StaticValues.NOMINAL_VALUE: StaticValues.NOML,
            StaticValues.MONETARY_VALUE: StaticValues.MONE,
        }

        if quantity_type_column in source_data.columns:

            quantity_type_not_null_mask = (
                source_data.loc[:, quantity_type_column]
            ).notnull()

            mapped_report_column.loc[quantity_type_not_null_mask] = (
                source_data.loc[quantity_type_not_null_mask, quantity_type_column]
                .str.upper()
                .replace(notation_map)
            )

        result[
            RTS22Transaction.TRANSACTION_DETAILS_QUANTITY_NOTATION
        ] = mapped_report_column

        return result

    @staticmethod
    def validate_strike_price_type(
        custom_mapping_result_series: pd.Series, steeleye_series: pd.Series
    ):
        custom_mapping_null_mask = custom_mapping_result_series.isnull()
        steeleye_series[custom_mapping_null_mask] = custom_mapping_result_series[
            custom_mapping_null_mask
        ]
