import os
from datetime import datetime
from typing import List
from typing import Optional
from typing import <PERSON>ple
from typing import Union

import addict
import numpy as np
import pandas as pd
from generator.three_way_rec_result_email_generator import (
    ThreeWayRecResultEmailGenerator,
)
from pydantic import Field
from se_elastic_schema.models import AccountUser
from se_elastic_schema.static.reference import Permission
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask
from swarm.task.transform.result import TransformResult

from swarm_tasks.steeleye.tr.static import RecFields
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.enums import ReportTypeEnum
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    REPORT_TYPE_MAPPING,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.report_mechanism_mappings import (
    REPORT_MECHANISM_MAP,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.twr_dataclasses import (
    ThreeWayRecResultStats,
)
from swarm_tasks.tr.workflow.generic.utils import amazon_ses_send_email


class AccountUserFields:

    EMAIL = "email"
    PERMISSIONS = "permissions"


class Params(BaseParams):
    email_from: str = "<EMAIL>"
    source_arn: str = "arn:aws:ses:eu-west-1:************:identity/steel-eye.com"
    report_type: ReportTypeEnum = Field(
        ...,
        description="ReportTypeEnum enumerator to control the task logic flow for either "
        "ARM_UNAVISTA or NCA_FCA reports",
    )


class Resources(BaseResources):
    es_client_key: str


class NotificationEmail(BaseTask):

    params_class = Params
    resources_class = Resources
    report_mechanism_reconciliation = None

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[Resources] = None,
        tenant_configuration: addict.Dict = None,
        deduped_input_result: Union[List[TransformResult], TransformResult] = None,
        reconciliation_result: Union[List[TransformResult], TransformResult] = None,
        ingestion_result: Union[List[TransformResult], TransformResult] = None,
        **kwargs,
    ):
        """
        This task is the final step of the 3-Way-Rec workflow. After reconciling/ingesting the report transactions,
        this task will collect the 3-Way-Rec statistics and send an email to all of the realm's users
        (i.e tr.uat.steeleye.co) with ADMIN and TRANSACTION_REPORTING_ADMIN permissions.

        :param params: `email_from` and `source_arn` for the email metadata and `report_type` to distinguish
            between the NCA_FCA and ARM_UNAVISTA email template
        :param resources: Resources to fetch data from ES
        :param tenant_configuration: TenantConfiguration to fetch list of email recipients
        :param deduped_input_result: TransformResult after removing duplicate TRNs from report data
        :param reconciliation_result: TransformResult after reconciling report transactions that linked up
            with transactions from SteelEye
        :param ingestion_result: TransformResult after building RTS22Transaction records from report transactions
            absent from SteelEye
        :param kwargs:
        """

        self.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            params.report_type
        ]()

        source_file = os.environ.get("SWARM_FILE_URL").split("/")[-1]

        if not isinstance(deduped_input_result, list):
            deduped_input_result = [deduped_input_result]
            reconciliation_result = [reconciliation_result]
            ingestion_result = [ingestion_result]

        three_way_rec_stats = self._calculate_three_way_rec_stats(
            three_way_rec_input_deduped=deduped_input_result,
            reconciliation_result=reconciliation_result,
            ingestion_result=ingestion_result,
        )

        email_generator = ThreeWayRecResultEmailGenerator(
            realm=tenant_configuration.tenantId
        )

        message = email_generator.generate_email(
            subject="SteelEye: MiFIR 3-Way-Reconciliation: Results",
            report_type=REPORT_TYPE_MAPPING[
                self.report_mechanism_reconciliation.report_mechanism
            ].upper(),
            source_file=source_file,
            number_of_records=three_way_rec_stats.records_count,
            earliest_transaction_date=three_way_rec_stats.earliest_transaction_date,
            latest_transaction_date=three_way_rec_stats.latest_transaction_date,
            number_of_complete_matches=three_way_rec_stats.complete_matches_count,
            number_of_matches_with_field_breaks=three_way_rec_stats.matches_with_field_breaks_count,
            number_of_trn_breaks=three_way_rec_stats.trn_breaks_count,
            number_of_trades_previously_created_through_3_way_rec=three_way_rec_stats.ingested_trn_breaks_count,
        )

        es_client = self.clients.get(resources.es_client_key)
        recipients = self._fetch_tenant_admins(
            tenant=Settings.tenant, es_client=es_client
        )

        if message:
            amazon_ses_send_email(
                source=params.email_from,
                source_arn=params.source_arn,
                destinations=recipients,
                raw_message=dict(Data=message.as_string()),
                logger=self.logger,
            )

    def _calculate_three_way_rec_stats(
        self,
        three_way_rec_input_deduped: List[TransformResult],
        reconciliation_result: List[TransformResult],
        ingestion_result: List[TransformResult],
    ) -> ThreeWayRecResultStats:
        """
        Calculate 3-Way-Rec statistics for email notification

        :param three_way_rec_input_deduped: List of TransformResults from every execution of the
            `FilterThreeWayRecColumns` task, before reconciliation/ingestion
        :param reconciliation_result: List of TransformResults from every execution of the
            `ReconcileReportTransactions` task, after reconciliation
        :param ingestion_result: List of TransformResults from every execution of the
        `BuildTransactionsFromReport` task, after ingestion
        :return: ThreeWayRecResultStats dataclass
        """

        datetime_column = self.report_mechanism_reconciliation.trading_datetime_field
        utc_datetime_format = (
            self.report_mechanism_reconciliation.trading_datetime_format
        )
        day_month_year_format = "%d-%b-%Y"

        earliest_transaction_date = datetime.utcnow().strftime(utc_datetime_format)
        latest_transaction_date = "0001-01-01T00:00:00.000000Z"

        cancels_count = 0
        records_count = 0
        complete_matches_count = 0
        matches_with_field_breaks_count = 0
        trn_breaks_count = 0

        for index in range(len(three_way_rec_input_deduped)):

            deduped_input_df = three_way_rec_input_deduped[index].target
            records_count += deduped_input_df.shape[0]
            cancels_count += self.report_mechanism_reconciliation.get_cancels_count(
                dataframe=deduped_input_df
            )

            # [EU-9047] When there are only CANC transactions in the three-way-rec report
            # latest and earliest transaction date won't be able to find any trn date, so populating
            # latest and earliest trn date as -
            try:
                latest_transaction_date = self._get_limit_datetime(
                    source_df=deduped_input_df,
                    datetime_column=datetime_column,
                    current_transaction_datetime=latest_transaction_date,
                )
                earliest_transaction_date = self._get_limit_datetime(
                    source_df=deduped_input_df,
                    datetime_column=datetime_column,
                    current_transaction_datetime=earliest_transaction_date,
                    latest=False,
                )
            except KeyError:
                latest_transaction_date = earliest_transaction_date = "-"

            reconciliation_df = reconciliation_result[index].target
            ingestion_df = ingestion_result[index].target

            if not reconciliation_df.empty:
                (
                    complete_matches_count,
                    matches_with_field_breaks_count,
                ) = self._count_breaks(
                    source_df=reconciliation_df,
                    complete_matches_count=complete_matches_count,
                    matches_with_field_breaks_count=matches_with_field_breaks_count,
                )

            if not ingestion_df.empty:
                trn_breaks_count += ingestion_df.shape[0]

        # [EU-9047] If latest and earliest transaction date is '-', this means all the
        # transactions are CANC and only '-' should go out into the mail
        if latest_transaction_date != "-":
            earliest_transaction_date = self._convert_datetime_format_as_string(
                source_datetime=earliest_transaction_date,
                source_format=utc_datetime_format,
                target_format=day_month_year_format,
            )

            latest_transaction_date = self._convert_datetime_format_as_string(
                source_datetime=latest_transaction_date,
                source_format=utc_datetime_format,
                target_format=day_month_year_format,
            )

        ingested_trn_breaks_count = records_count - (
            complete_matches_count
            + matches_with_field_breaks_count
            + trn_breaks_count
            + cancels_count
        )

        ingested_trn_breaks_count = (
            0 if ingested_trn_breaks_count < 0 else ingested_trn_breaks_count
        )

        return ThreeWayRecResultStats(
            latest_transaction_date=latest_transaction_date,
            earliest_transaction_date=earliest_transaction_date,
            records_count=records_count,
            complete_matches_count=complete_matches_count,
            trn_breaks_count=trn_breaks_count,
            matches_with_field_breaks_count=matches_with_field_breaks_count,
            ingested_trn_breaks_count=ingested_trn_breaks_count,
        )

    def _fetch_tenant_admins(self, tenant: str, es_client) -> List[Optional[str]]:

        """
        Fetch the list of email recipients from users with ADMIN and TRANSACTION_REPORTING_ADMIN permissions

        :param tenant: Name of the tenant to filter the ElasticSearch index
        :param es_client: ElasticSearch client
        :return: List of unique emails
        """

        unique_emails = []
        query = self._build_account_user_fetch_query(es_client=es_client)
        alias = AccountUser.get_elastic_index_alias(tenant=tenant)
        account_users = es_client.scroll(query=query, index=alias)

        if account_users.empty:
            return unique_emails

        emails_with_valid_permissions_mask = (
            account_users.loc[:, AccountUserFields.PERMISSIONS]
            .astype(str)
            .str.contains(
                f"{Permission.ADMIN.value}|{Permission.TRANSACTION_REPORTING_ADMIN.value}",
                regex=True,
            )
        )

        return list(
            np.unique(
                account_users.loc[
                    emails_with_valid_permissions_mask, AccountUserFields.EMAIL
                ]
            )
        )

    @staticmethod
    def _build_account_user_fetch_query(es_client) -> dict:
        """
        Build a ES query to fetch user emails and permissions from `AccountUser` records

        :param es_client: ElasticSearch client
        :return: Dictionary with query to fetch user emails and permissions
        """

        filters = [
            {"term": {es_client.meta.model: "AccountUser"}},
            {"bool": {"must_not": {"exists": {"field": es_client.meta.expiry}}}},
        ]

        query = {
            "query": {"bool": {"filter": filters}},
            "size": es_client.MAX_QUERY_SIZE,
            "_source": {
                "includes": [AccountUserFields.EMAIL, AccountUserFields.PERMISSIONS]
            },
        }

        return query

    @staticmethod
    def _get_limit_datetime(
        source_df: pd.DataFrame,
        datetime_column: str,
        current_transaction_datetime: str,
        latest=True,
    ) -> str:
        """
        Get the latest/earliest datetime value of a given Pandas DataFrame column, compare it with
        `current_transaction_datetime` and return the latest/earliest value.

        :param source_df: Source dataframe with report data
        :param datetime_column: Name of the column with transaction datetime data
        :param current_transaction_datetime: Transaction datetime that will be compared with report data
        :param latest: Return latest datetime if True, earliest otherwise
        :return: Datetime as string, with the latest/earliest value from the `datetime_column` compared to input
            `transaction_datetime`
        """

        transaction_datetime_series = (
            source_df.loc[:, datetime_column].sort_values(ascending=False).dropna()
        )

        if latest:
            limit_datetime = (
                transaction_datetime_series.iloc[0]
                if transaction_datetime_series.iloc[0] > current_transaction_datetime
                else current_transaction_datetime
            )

        else:
            limit_datetime = (
                transaction_datetime_series.iloc[-1]
                if transaction_datetime_series.iloc[0] < current_transaction_datetime
                else current_transaction_datetime
            )

        return limit_datetime

    def _count_breaks(
        self,
        source_df: pd.DataFrame,
        complete_matches_count: int,
        matches_with_field_breaks_count: int,
    ) -> Tuple[int, int]:
        """
        Calculate 3-Way-Rec TRN matches counts

        :param source_df: Pandas DataFrame with reconciliation data
        :param complete_matches_count: Number of complete TRN matches
        :param matches_with_field_breaks_count: Number of TRN matches with field breaks
        :return: (`complete_matches_count`, `matches_with_field_breaks_count`)
        """

        source_field_break_dict = {
            ReportTypeEnum.NCA_FCA: RecFields.RECONCILIATION_NCA_FIELD_BREAK,
            ReportTypeEnum.ARM_UNAVISTA: RecFields.RECONCILIATION_ARM_FIELD_BREAK,
        }
        source_field_break_field_name = source_field_break_dict[
            self.report_mechanism_reconciliation.report_mechanism
        ]

        no_field_breaks = False

        perfect_match_mask = (
            source_df.loc[:, source_field_break_field_name] == no_field_breaks
        )

        complete_matches_count += source_df.loc[
            perfect_match_mask, source_field_break_field_name
        ].shape[0]
        matches_with_field_breaks_count += source_df.loc[
            ~perfect_match_mask, source_field_break_field_name
        ].shape[0]

        return complete_matches_count, matches_with_field_breaks_count

    @staticmethod
    def _convert_datetime_format_as_string(
        source_datetime: str, source_format: str, target_format: str
    ) -> str:
        """
        Convert a datetime as string value to a given `target_format` datetime format.

        :param source_datetime: Datetime as string, to be converted
        :param source_format: Source datetime format
        :param target_format: Target datetime format
        :return: `source_datime` value converted to the `target_format` format
        """

        try:
            res = datetime.strptime(source_datetime, source_format).strftime(
                target_format
            )
        except ValueError:
            try:
                # If the timestamp does not follow the expected format
                # we use the pandas.to_datetime() method to "guess" it
                res = pd.to_datetime(source_datetime).strftime(target_format)

            except Exception:
                res = ""

        return res
