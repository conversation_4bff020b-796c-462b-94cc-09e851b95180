from typing import List
from typing import Optional
from typing import Tuple

import pandas as pd
from addict import Dict

from swarm_tasks.steeleye.tr.static import NCAReport
from swarm_tasks.steeleye.tr.static import RecFields
from swarm_tasks.steeleye.tr.static import ReportDetailsIdFields
from swarm_tasks.steeleye.tr.static import RTS22Transaction
from swarm_tasks.steeleye.tr.static import StaticValues
from swarm_tasks.steeleye.tr.workflow.three_way_rec.report_mechanism_reconciliation import (
    ReportMechanismReconciliation,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.enums import ReportTypeEnum
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    FEEDBACK_STATUS_MAP,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    NCA_BUYER_SELLER_AND_DECISION_MAKER_MAP,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    NCA_GENERAL_COMPOUND_MAP,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    NCA_GENERAL_ONE_TO_ONE_MAP,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    NCA_INSTRUMENT_COMPOUND_MAP,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    NCA_INSTRUMENT_ONE_TO_ONE_MAP,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    NCA_PARTY_CONDITIONAL_LIST_MAP,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    NCA_PARTY_LIST_MAP,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    NCA_PARTY_ONE_TO_ONE_MAP,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    NCA_PARTY_TR_PI_ENRICHMENT_MAP,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.twr_dataclasses import (
    ReconcilationNestedPartyList,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.twr_utils import (
    clnt_nore_check,
)


class NcaFcaReportMechanismReconciliation(ReportMechanismReconciliation):
    def __init__(self):
        super().__init__(report_type=ReportTypeEnum.NCA_FCA)

    @staticmethod
    def link_preprocess_data(df: pd.DataFrame) -> pd.DataFrame:
        """
        Create auxiliary ID and Report Status columns to merge data from CANCEL and NEWT transactions
        in the same columns (this data is split across different XML tags in the source file)

        :param df: Pandas DataFrame with transactions parsed from the XML NCA_FCA report file
        :return: `df` with added auxiliary columns
        """
        temp_cols = [
            ReportDetailsIdFields.TEMP_REPORT_STATUS,
            ReportDetailsIdFields.TEMP_ID,
        ]
        for col in temp_cols:
            df.loc[:, col] = pd.NA

        if NCAReport.TXID in df.columns:
            new_transaction_mask = df.loc[:, NCAReport.TXID].notnull()

            df.loc[
                new_transaction_mask, ReportDetailsIdFields.TEMP_REPORT_STATUS
            ] = ReportDetailsIdFields.NEWT

            df.loc[new_transaction_mask, ReportDetailsIdFields.TEMP_ID] = df.loc[
                new_transaction_mask, NCAReport.TXID
            ]

        if NCAReport.CANC_TXID in df.columns:
            cancel_transaction_mask = df.loc[:, NCAReport.CANC_TXID].notnull()

            df.loc[
                cancel_transaction_mask, ReportDetailsIdFields.TEMP_REPORT_STATUS
            ] = ReportDetailsIdFields.CANC

            df.loc[cancel_transaction_mask, ReportDetailsIdFields.TEMP_ID] = df.loc[
                cancel_transaction_mask, NCAReport.CANC_TXID
            ]

        return df

    @staticmethod
    def get_report_status_masks(report_transactions: pd.DataFrame) -> List:
        return [
            report_transactions.loc[:, ReportDetailsIdFields.TEMP_REPORT_STATUS]
            == status
            for status in [ReportDetailsIdFields.NEWT, ReportDetailsIdFields.CANC]
        ]

    @staticmethod
    def get_unique_transaction_ids(
        report_transactions: pd.DataFrame,
        report_status_newt_mask: pd.Series,
        report_status_cancel_mask: pd.Series,
    ) -> List:

        return [
            report_transactions.loc[mask, ReportDetailsIdFields.TEMP_ID].dropna()
            for mask in [report_status_newt_mask, report_status_cancel_mask]
        ]

    @staticmethod
    def merge_transactions(
        report_transactions: pd.DataFrame, rts22_transactions: pd.DataFrame
    ) -> pd.DataFrame:

        return report_transactions.merge(
            rts22_transactions,
            how="left",
            left_on=[
                ReportDetailsIdFields.TEMP_ID,
                ReportDetailsIdFields.TEMP_REPORT_STATUS,
            ],
            right_on=[
                ReportDetailsIdFields.TRANSACTION_REF_NO,
                ReportDetailsIdFields.REPORT_DETAILS_REPORT_STATUS,
            ],
        )

    @staticmethod
    def reconcile_preprocess_data(source_df: pd.DataFrame, target_df: pd.DataFrame):

        target_df[RecFields.RECONCILIATION_NCA_FIELD_BREAK] = False
        target_df[RecFields.RECONCILIATION_NCA_MATCH] = True
        target_df[RecFields.RECONCILIATION_NCA_SOURCE_KEY] = source_df.loc[
            :, RecFields.TEMP_RECONCILIATION_NCA_SOURCE_KEY
        ]

    def reconcile_workflow_fields(
        self,
        workflow_mapping_result: dict,
        data_to_reconcile: pd.DataFrame,
        target_df: pd.DataFrame,
    ):

        # workflow.nca.status
        workflow_mapping_result[RTS22Transaction.WORKFLOW_NCA_STATUS] = (
            data_to_reconcile.loc[:, NCAReport.FEEDBACK_STS].map(FEEDBACK_STATUS_MAP)
            if NCAReport.FEEDBACK_STS in data_to_reconcile.columns
            else pd.Series(index=data_to_reconcile.index)
        )

        # workflow.nca.checks[].ruleId
        workflow_nca_checks_series = self.parse_workflow_checks(
            source_frame=data_to_reconcile
        )

        workflow_nca_checks_as_unique_list_series = pd.Series(
            index=data_to_reconcile.index
        )

        self._filter_unique_values_from_list_of_dicts_in_series(
            unique_list_series=workflow_nca_checks_as_unique_list_series,
            series_with_list_of_dicts=workflow_nca_checks_series,
            dict_key=RecFields.RULE_ID,
        )

        steeleye_checks_series = (
            data_to_reconcile.loc[:, RTS22Transaction.WORKFLOW_NCA_CHECKS]
            if RTS22Transaction.WORKFLOW_NCA_CHECKS in data_to_reconcile.columns
            else pd.Series(index=data_to_reconcile.index)
        )

        steeleye_checks_as_unique_list_series = pd.Series(index=data_to_reconcile.index)
        steeleye_checks_as_unique_list_series.name = (
            f"{RTS22Transaction.WORKFLOW_NCA_CHECKS}[].{RecFields.RULE_ID}"
        )

        self._filter_unique_values_from_list_of_dicts_in_series(
            unique_list_series=steeleye_checks_as_unique_list_series,
            series_with_list_of_dicts=steeleye_checks_series,
            dict_key=RecFields.RULE_ID,
        )

        self.compare_report_and_steeleye_fields(
            report_series=workflow_nca_checks_as_unique_list_series,
            steeleye_series=steeleye_checks_as_unique_list_series,
            target=target_df,
        )

    def parse_workflow_checks(self, source_frame: pd.DataFrame) -> pd.Series:
        """
        Populate "workflow.nca.checks" reconciliation values. These values may be present
        in the NCA_FCA report as a list of values within the XML tag "FinInstrmRptgTxRpt.Tx.Feedback.VldtnRule.Id"

        :param source_frame: Dataframe with report_type data to populate workflow checks
        """
        result = pd.DataFrame(
            data=[pd.NA] * source_frame.shape[0],
            index=source_frame.index,
            columns=[RecFields.WORKFLOW_NCA_CHECKS],
        )

        if NCAReport.FEEDBACK_VLDTNRULE_ID in source_frame.columns:
            feedback_validation_rules_mask = source_frame.loc[
                :, NCAReport.FEEDBACK_VLDTNRULE_ID
            ].notnull()

            if feedback_validation_rules_mask.any():

                result.loc[
                    feedback_validation_rules_mask,
                    RecFields.WORKFLOW_NCA_CHECKS,
                ] = (
                    source_frame.loc[
                        feedback_validation_rules_mask,
                        NCAReport.FEEDBACK_VLDTNRULE_ID,
                    ]
                    .apply(lambda x: [{RecFields.RULE_ID: x}])
                    .to_frame()
                    .apply(
                        lambda x: self._extend_list_as_series_value(
                            list_as_series=x,
                            target_col_name=RecFields.WORKFLOW_NCA_CHECKS,
                            target=result,
                        ),
                        axis=1,
                    )
                )

        return result[RecFields.WORKFLOW_NCA_CHECKS]

    @staticmethod
    def _extend_list_as_series_value(
        list_as_series: pd.Series, target_col_name: str, target: pd.DataFrame
    ) -> List:
        """
        Access the `target` dataframe at column `target_col_name` and index `list_as_series`
        and extend the preexisting list with `list_as_series` content. Note that `list_as_series` is a Series
        with a single element, which is a list of values.

        :param list_as_series: Pandas Series with exactly one element, which is a list of values
            The reason why we pass a Pandas Series, instead of the list itself is to preserve the row index
            to access it by ".name"
        :param target_col_name: Column which we want to introduce the list of values in `new_list_value`
        :param target: DataFrame where we will check if there preexisting list values to extend with the
            list within `list_as_series`
        :return: List within `list_as_series` with the values from the preexisting list_of_values at `target_col_name`
            column and `list_as_series.name` index
        """

        previous_list_values = target.loc[list_as_series.name, target_col_name]

        if not isinstance(previous_list_values, list):
            return list_as_series[0]

        else:
            return previous_list_values + list_as_series[0]

    @staticmethod
    def _filter_unique_values_from_list_of_dicts_in_series(
        unique_list_series: pd.Series,
        series_with_list_of_dicts: pd.Series,
        dict_key: str,
    ):
        """
        Fetch the unique `dict_key` key values of the dictionaries within the lists of the
        `series_with_list_of_dicts` Pandas Series

        :param unique_list_series: Pandas Series which will contain the unique values of the lists within
            the `series_with_list_of_dicts` dictionaries, regarding the `dict_key` key
        :param series_with_list_of_dicts: Pandas Series composed of lists of dictionaries
        :param dict_key: Relevant key of the dictionaries within the lists of `series_with_list_of_dicts`
        """

        checks_not_null_mask = series_with_list_of_dicts.notnull()

        if checks_not_null_mask.any():
            unique_list_series[checks_not_null_mask] = series_with_list_of_dicts[
                checks_not_null_mask
            ].apply(
                lambda x: sorted(
                    set(
                        [
                            check_dict.get(dict_key)
                            for check_dict in x
                            if dict_key in check_dict
                        ]
                    )
                )
            )

    @staticmethod
    def _sanitize_report_series(report_series: pd.Series):
        return

    @staticmethod
    def get_cancelled_one_to_one_map() -> dict:

        return {
            NCAReport.CANC_EXCTGPTY: RTS22Transaction.PARTIES_EXECUTING_ENTITY_FIRM_IDENTIFIERS_LEI,
        }

    @staticmethod
    def get_general_one_to_one_map() -> dict:

        return NCA_GENERAL_ONE_TO_ONE_MAP

    @staticmethod
    def get_party_one_to_one_reconcile_map() -> dict:

        return NCA_PARTY_ONE_TO_ONE_MAP

    @staticmethod
    def get_party_one_to_one_build_map() -> Tuple:

        return NCA_PARTY_ONE_TO_ONE_MAP, NCA_PARTY_TR_PI_ENRICHMENT_MAP

    @staticmethod
    def get_instrument_one_to_one_map() -> dict:

        return NCA_INSTRUMENT_ONE_TO_ONE_MAP

    def reconcile_compound_general_fields(
        self, data_to_reconcile: pd.DataFrame, target: pd.DataFrame
    ):

        """
        Reconcile Compound General fields which are mapped by a combination of fields
        Example : transactionDetails.priceCurrency
        May populate
        FinInstrmRptgTxRpt.Tx.New.Tx.Pric.Pric.MntryVal.Amt@Ccy
        OR
        FinInstrmRptgTxRpt.Tx.New.Tx.Pric.NoPric@Ccy

        :param data_to_reconcile: Source Frame which will be reconciled
        :param target: DataFrame with reconciliation results
        """

        if data_to_reconcile.empty:
            return

        compound_map = NCA_GENERAL_COMPOUND_MAP

        self.run_generic_compound_comparison(
            compound_map=compound_map,
            data_to_reconcile=data_to_reconcile,
            target=target,
        )

    def reconcile_compound_party_fields(
        self,
        data_to_reconcile: pd.DataFrame,
        target: pd.DataFrame,
        tenant_configuration: Dict,
    ):

        """
        Reconcile Compound Party fields which are mapped by a combination of fields

        :param data_to_reconcile: Source Frame which will be reconciled
        :param target: DataFrame with reconciliation results
        :param tenant_configuration: Tenant Configuration Addict
        """

        if (
            data_to_reconcile.empty
            or tenant_configuration.trPIEnrichmentEnabled is True
        ):
            return

        compound_map = {
            RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM_MIFIR_ID: [
                (NCAReport.EXCTGPRSN_PRSN_OTHR_ID, None),
                (NCAReport.EXCTGPRSN_ALGO, None),
                (NCAReport.EXCTGPRSN_CLNT, None),
            ],
            RTS22Transaction.PARTIES_INV_DEC_WITHIN_FIRM_MIFIR_ID: [
                (NCAReport.INVSTMTDCSNPRSN_PRSN_OTHR_ID, None),
                (NCAReport.INVSTMTDCSNPRSN_ALGO, None),
            ],
        }

        algo_variants_map = {
            RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM_MIFIR_ID: [
                RTS22Transaction.PARTIES_EXEC_WT_FIRM_STRCT_EXEC_WT_FIRM_ID_TYPE,
                RTS22Transaction.PARTIES_EXEC_WT_FIRM_STRCT_EXEC_WT_FIRM_ID,
                RTS22Transaction.MARKET_IDENTIFIERS,
            ],
            RTS22Transaction.PARTIES_INV_DEC_WITHIN_FIRM_MIFIR_ID: [
                RTS22Transaction.PARTIES_INV_DEC_WT_FIRM_STRCT_EXEC_WT_FIRM_ID_TYPE,
                RTS22Transaction.PARTIES_INV_DEC_WT_FIRM_STRCT_EXEC_WT_FIRM_ID,
                "dummy",
            ],
        }

        for key in compound_map:

            report_mapped_column = self.populate_data_by_combinations(
                compound_map=compound_map,
                rts22_field_name=key,
                data_to_reconcile=data_to_reconcile,
            )

            steeleye_series_field_names = pd.Series(
                data=key, index=data_to_reconcile.index
            )
            steeleye_series = (
                data_to_reconcile.loc[:, key]
                if key in data_to_reconcile.columns
                else pd.Series(data=pd.NA, index=data_to_reconcile.index)
            )

            id_type_column = algo_variants_map[key][0]
            id_column = algo_variants_map[key][1]

            if (
                len({id_type_column, id_column}.intersection(data_to_reconcile.columns))
                == 2
            ):
                id_type_a_mask = (
                    data_to_reconcile.loc[:, id_type_column].str.upper() == "A"
                )
                steeleye_series.loc[id_type_a_mask] = data_to_reconcile.loc[
                    id_type_a_mask, id_column
                ]
                steeleye_series_field_names.loc[id_type_a_mask] = id_column

            clnt_nore_column = algo_variants_map[key][2]

            if clnt_nore_column in data_to_reconcile.columns.to_list():
                clnt_nore_check(
                    clnt_nore_column=clnt_nore_column,
                    data_to_reconcile=data_to_reconcile,
                    steeleye_series=steeleye_series,
                    steeleye_series_field_names=steeleye_series_field_names,
                )

            steeleye_series = self._remove_non_significant_zero(steeleye_series)

            self.compare_report_and_steeleye_fields(
                report_series=report_mapped_column,
                steeleye_series=steeleye_series,
                target=target,
                steeleye_field_names=steeleye_series_field_names,
            )

    def reconcile_compound_instrument_fields(
        self, data_to_reconcile: pd.DataFrame, target: pd.DataFrame
    ):
        """
        Reconcile Compound Instrument fields which are mapped by a combination of fields
        Example : instrumentDetails.instrument.fxDerivatives.notionalCurrency2
        May populate
        FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.AsstClssSpcfcAttrbts.Intrst.OthrNtnlCcy
        OR
        FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.AsstClssSpcfcAttrbts.FX.OthrNtnlCcy

        :param data_to_reconcile: Source Frame which will be reconciled
        :param target: DataFrame with reconciliation results
        """

        # RTS22Transaction field may be populated from any one of the associated list of report fields
        # The list of report fields are stored as 2-element tuples, where the first element is the report field name,
        # and the second element is None when we want to use the actual value from the report field, or a
        # static value to be used when the report field is populated
        if data_to_reconcile.empty:
            return

        compound_map = NCA_INSTRUMENT_COMPOUND_MAP

        self.run_generic_compound_comparison(
            compound_map=compound_map,
            data_to_reconcile=data_to_reconcile,
            target=target,
        )

    def get_party_list_map(self) -> List:
        return NCA_PARTY_LIST_MAP

    def _add_buyer_and_sell_intl_alternative(
        self, report_field_name, report_series, data_to_reconcile
    ):
        """
        AccountOwnerIDLei report fields may be absent, and the associated data may be populated in the related INTL
        fields. This methods returns a Pandas Series with the INTL values (if it exists) for the records
        where the ID_LEI fields were not populated

        :param report_field_name: Report field name to inferr the INTL field name from
        :param report_series: Pandas Series with Report data which will be updated with the INTL alternatives
        :param data_to_reconcile: Pandas DataFrame with data to reconcile
        """
        if report_field_name in [
            NCAReport.BUYR_ACCTOWNR_ID_LEI,
            NCAReport.SELLR_ACCTOWNR_ID_LEI,
        ]:
            intl_mapping = {
                NCAReport.BUYR_ACCTOWNR_ID_LEI: NCAReport.BUYR_ACCTOWNR_ID_INTL,
                NCAReport.SELLR_ACCTOWNR_ID_LEI: NCAReport.SELLR_ACCTOWNR_ID_INTL,
            }

            nca_intl_id = intl_mapping[report_field_name]
            if nca_intl_id in data_to_reconcile.columns:
                report_null_mask = report_series.isnull()
                report_series[report_null_mask] = data_to_reconcile.loc[
                    report_null_mask, nca_intl_id
                ]

    @staticmethod
    def get_party_conditional_list_map() -> List:
        return NCA_PARTY_CONDITIONAL_LIST_MAP

    @staticmethod
    def get_price_pending_report_field() -> str:
        return NCAReport.TX_PRIC_NOPRIC_PDG

    @staticmethod
    def get_price_pending_report_dictionary() -> dict:
        return {
            RTS22Transaction.TRANSACTION_DETAILS_PRICE_PENDING: StaticValues.PNDG,
            RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOT_APPLICABLE: StaticValues.NOAP,
        }

    @staticmethod
    def get_source_waiver_column() -> str:
        return NCAReport.ADDTLATTRBTS_WVRIND

    @staticmethod
    def parse_one_to_list_mapping(
        source_data: pd.DataFrame,
        source_column: str,
        target_column: str,
        delimiter=";",
    ) -> dict:
        """
        Build a Pandas Series with report `source_column` data to compare with a RTS22Transaction
        `target_column` field

        :param source_data: Pandas DataFrame with data to reconcile
        :param source_column: Report column to reconcile
        :param target_column: Name of the RTS22Transaction field to reconcile
        :param delimiter: Delimiter used in ARM_UNAVISTA reports to separate multiple values in the same cell
        :return:
        """
        result = {}
        result_series = pd.Series(index=source_data.index)

        if source_column in source_data.columns:
            not_null_mask = source_data.loc[:, source_column].notnull()
            if not_null_mask.any():
                result_series.loc[not_null_mask] = source_data.loc[
                    not_null_mask, source_column
                ].apply(
                    lambda x: eval(x) if x.startswith("[") and x.endswith("]") else [x]
                )

        result[target_column] = result_series

        return result

    @staticmethod
    def get_source_otc_column():
        return NCAReport.ADDTLATTRBTS_OTCPSTTRADIND

    def parse_additional_custom_fields(
        self, source_data: pd.DataFrame, custom_mapping_result: dict
    ):
        return

    @staticmethod
    def get_ul_instrument_id_report_columns() -> List[str]:
        """
        Get List of Underlying Instrument ID columns from NCA_FCA report
        :return: List of the names of the columns that might contain Underlying Instrument ID data
        """
        return [
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_SNGL,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_BSKT,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_SNGL,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_BSKT,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_SNGL,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_BSKT,
        ]

    @classmethod
    def parse_ul_instrument_report_series(
        cls,
        report_columns: List[str],
        source_data: pd.DataFrame,
        nested_ordered_dict_field,
        delimiter=";",
    ) -> pd.Series:
        """
        For NCA_FCA, `report_columns` should be a list of all of the columns to search for underlying data

        Build a Pandas Series with report `report_columns` data to compare with a RTS22Transaction field.
        For NCA_FCA, if there are multiple underlying instruments they will be stored in `report_columns` as
        Ordered Dicts, but not all values within these dicts are necessary, thus the `nested_ordered_dict_field`
        is used to retrieve the relevant field (i.e `ISIN` field from
        `FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Sngl`)

        :param report_columns: List of report column/s that might hold underlying instrument data
        :param source_data: Pandas DataFrame with data that will be reconciled
        :param nested_ordered_dict_field: Name of the field that contains the relevant data
            within the nested ordered dicts
        :param delimiter:
        :return: Delimiter used in ARM_UNAVISTA reports to separate multiple values in the same cell
        """

        # If there is only one underlying instrument, we will have a column with the
        # `nested_ordered_dict_field` suffix  with that value alone,
        # however, for multiple underlying instruments, we will have a column without the suffix, containing
        # multiple OrderedDict, where the suffix is the name of the dict key

        report_columns = report_columns + [
            f"{col}.{nested_ordered_dict_field}" for col in report_columns
        ]
        report_cols_mask = source_data.columns.isin(report_columns)
        report_df = source_data.loc[:, report_cols_mask]
        report_series = pd.Series(data=pd.NA, index=source_data.index)
        report_df_not_null_mask = report_df.loc[:, :].notnull().any(1)

        if report_df_not_null_mask.any():

            report_series.loc[report_df_not_null_mask] = report_df.loc[
                report_df_not_null_mask, :
            ].apply(
                lambda x: cls.convert_empty_set_to_pd_na(
                    sorted(
                        set(
                            cls._parse_ordered_dict_nested_fields(
                                row=x,
                                nested_ordered_dict_field=nested_ordered_dict_field,
                            )
                        )
                    )
                ),
                axis=1,
            )

        return report_series

    @staticmethod
    def get_ul_index_id_report_columns() -> List[str]:
        return [
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_SNGL_INDX_ISIN,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_BSKT_INDX_ISIN,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_SNGL_INDX_ISIN,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_BSKT_INDX_ISIN,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_SNGL_INDX_ISIN,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_BSKT_INDX_ISIN,
        ]

    @staticmethod
    def get_ul_index_name_report_columns() -> List[str]:

        return [
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_SNGL_INDX_NM_REFRATE_INDX,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_SNGL_INDX_NM_REFRATE_NM,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_BSKT_INDX_NM_REFRATE_INDX,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_BSKT_INDX_NM_REFRATE_NM,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_SNGL_INDX_NM_REFRATE_INDX,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_SNGL_INDX_NM_REFRATE_NM,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_BSKT_INDX_NM_REFRATE_INDX,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_BSKT_INDX_NM_REFRATE_NM,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_SNGL_INDX_NM_REFRATE_INDX,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_SNGL_INDX_NM_REFRATE_NM,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_BSKT_INDX_NM_REFRATE_INDX,
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_BSKT_INDX_NM_REFRATE_NM,
        ]

    def build_preprocess_data(self, source_frame: pd.DataFrame, target: pd.DataFrame):

        target.loc[:, RecFields.RECONCILIATION_NCA_MATCH] = False
        target.loc[:, RecFields.RECONCILIATION_NCA_SOURCE_KEY] = source_frame.loc[
            :, RecFields.TEMP_RECONCILIATION_NCA_SOURCE_KEY
        ]

        target.loc[:, RecFields.WORKFLOW_NCA_STATUS] = (
            source_frame.loc[:, NCAReport.FEEDBACK_STS].map(FEEDBACK_STATUS_MAP)
            if NCAReport.FEEDBACK_STS in source_frame.columns
            else pd.Series(index=source_frame.index)
        )

        target.loc[:, RecFields.WORKFLOW_NCA_CHECKS] = self.parse_workflow_checks(
            source_frame=source_frame
        )

    def populate_compound_mappings(
        self, source_data: pd.DataFrame, target: pd.DataFrame
    ):
        """
        Populate Compound General RTS22Transaction fields which are mapped by a combination of fields
        Example : transactionDetails.priceCurrency
        May be populated by
        FinInstrmRptgTxRpt.Tx.New.Tx.Pric.Pric.MntryVal.Amt@Ccy
        OR
        FinInstrmRptgTxRpt.Tx.New.Tx.Pric.NoPric@Ccy

        :param source_data: Pandas DataFrame with Report data
        :param target: Pandas DataFrame with RTS22Transaction fields
        """

        self._generic_populate_compound_mappings(
            compound_map=NCA_GENERAL_COMPOUND_MAP,
            source_data=source_data,
            target=target,
        )

    def populate_compound_party_mappings(
        self, source_data: pd.DataFrame, target: pd.DataFrame
    ):
        """
        Populate Compound Party RTS22Transaction fields which are mapped by a combination of fields

        :param source_data: Pandas DataFrame with Report data
        :param target: Pandas DataFrame with RTS22Transaction fields
        """

        compound_map = {
            RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM_MIFIR_ID: [
                (NCAReport.EXCTGPRSN_PRSN_OTHR_ID, None),
                (NCAReport.EXCTGPRSN_ALGO, None),
                (NCAReport.EXCTGPRSN_CLNT, None),
            ],
            RTS22Transaction.PARTIES_INV_DEC_WITHIN_FIRM_MIFIR_ID: [
                (NCAReport.INVSTMTDCSNPRSN_PRSN_OTHR_ID, None),
                (NCAReport.INVSTMTDCSNPRSN_ALGO, None),
            ],
        }
        self._generic_populate_compound_mappings(
            compound_map=compound_map,
            source_data=source_data,
            target=target,
        )

    @staticmethod
    def get_buyer_seller_and_decision_maker_map():
        return NCA_BUYER_SELLER_AND_DECISION_MAKER_MAP

    def _populate_firm_identifiers_lei(
        self,
        row: pd.Series,
        party_mapping_data: ReconcilationNestedPartyList,
        mapping_result: dict,
    ):

        if pd.isna(mapping_result.get(RTS22Transaction.FIRM_IDENTIFIERS_LEI, pd.NA)):

            if (
                party_mapping_data.rts22_party_root_field
                == RTS22Transaction.PARTIES_BUYER
            ):

                mapping_result[RTS22Transaction.FIRM_IDENTIFIERS_LEI] = (
                    row.loc[NCAReport.BUYR_ACCTOWNR_ID_INTL]
                    if NCAReport.BUYR_ACCTOWNR_ID_INTL in row.index
                    else pd.NA
                )

            elif (
                party_mapping_data.rts22_party_root_field
                == RTS22Transaction.PARTIES_SELLER
            ):

                mapping_result[RTS22Transaction.FIRM_IDENTIFIERS_LEI] = (
                    row.loc[NCAReport.SELLR_ACCTOWNR_ID_INTL]
                    if NCAReport.SELLR_ACCTOWNR_ID_INTL in row.index
                    else pd.NA
                )

    def _populate_branch_country(
        self, mapping_result: dict, branch_country: Optional[str]
    ) -> bool:

        if pd.isna(mapping_result.get(RTS22Transaction.FIRM_IDENTIFIERS_LEI, pd.NA)):
            mapping_result[
                RTS22Transaction.OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY
            ] = branch_country
        else:
            mapping_result[
                RTS22Transaction.FIRM_IDENTIFIERS_BRANCH_COUNTRY
            ] = branch_country

        return False

    @staticmethod
    def _populate_model_prefix(mapping_result: dict, valid_lei: bool) -> str:

        # If the Party has an LEI it must use the MarketCounterparty model, AccountPerson otherwise
        model_prefix = (
            "AccountPerson"
            if pd.isna(mapping_result.get(RTS22Transaction.FIRM_IDENTIFIERS_LEI, pd.NA))
            else "MarketCounterparty"
        )
        return model_prefix

    def populate_compound_instrument_mappings(
        self, source_data: pd.DataFrame, target: pd.DataFrame
    ):
        self._generic_populate_compound_mappings(
            compound_map=NCA_INSTRUMENT_COMPOUND_MAP,
            source_data=source_data,
            target=target,
        )

    def populate_additional_custom_fields(
        self, source_data: pd.DataFrame, target: pd.DataFrame
    ):
        return

    @staticmethod
    def get_trading_datetime_field() -> str:
        return NCAReport.TX_TRADT

    @staticmethod
    def get_trading_datetime_format() -> str:
        return "%Y-%m-%dT%H:%M:%S.%fZ"

    def parse_execution_within_firm_country_branch(
        self,
        source_data: pd.DataFrame,
        tenant_configuration: Dict,
    ) -> pd.Series:

        result = pd.Series(data=pd.NA, index=source_data.index)
        # The easiest way to `not reconcile` is to reconcile with values that will not create field breaks
        se_branch_country = (
            source_data.loc[
                :,
                RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM_OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY,
            ]
            if RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM_OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY
            in source_data.columns
            else pd.Series(data=pd.NA, index=source_data.index)
        )
        result.loc[:] = se_branch_country

        if NCAReport.EXCTGPRSN_ALGO in source_data.columns:
            algo_null_mask = source_data.loc[:, NCAReport.EXCTGPRSN_ALGO].isnull()
        else:
            algo_null_mask = pd.Series(data=True, index=source_data.index)

        report_branch_country = (
            source_data.loc[:, NCAReport.EXCTGPRSN_PRSN_CTRYOFBRNCH]
            if NCAReport.EXCTGPRSN_PRSN_CTRYOFBRNCH in source_data.columns
            else pd.Series(data=pd.NA, index=source_data.index)
        )
        result.loc[algo_null_mask] = report_branch_country.loc[algo_null_mask]

        return result

    def parse_investment_decision_within_firm_country_branch(
        self,
        source_data: pd.DataFrame,
        tenant_configuration: Dict,
    ) -> pd.Series:

        result = pd.Series(data=pd.NA, index=source_data.index)
        # The easiest way to `not reconcile` is to reconcile with values that will not create field breaks
        se_branch_country = (
            source_data.loc[
                :,
                RTS22Transaction.PARTIES_INV_DEC_WITHIN_FIRM_OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY,
            ]
            if RTS22Transaction.PARTIES_INV_DEC_WITHIN_FIRM_OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY
            in source_data.columns
            else pd.Series(data=pd.NA, index=source_data.index)
        )
        result.loc[:] = se_branch_country

        if NCAReport.EXCTGPRSN_ALGO in source_data.columns:
            algo_null_mask = source_data.loc[:, NCAReport.EXCTGPRSN_ALGO].isnull()
        else:
            algo_null_mask = pd.Series(data=True, index=source_data.index)

        report_branch_country = (
            source_data.loc[:, NCAReport.INVSTMTDCSNPRSN_PRSN_CTRYOFBRNCH]
            if NCAReport.INVSTMTDCSNPRSN_PRSN_CTRYOFBRNCH in source_data.columns
            else pd.Series(data=pd.NA, index=source_data.index)
        )
        result.loc[algo_null_mask] = report_branch_country.loc[algo_null_mask]

        return result

    def get_cancels_count(self, dataframe: pd.DataFrame) -> int:

        count = 0
        cancel_col = NCAReport.CANC_TXID

        if cancel_col in dataframe.columns:
            not_null_mask = dataframe.loc[:, cancel_col].notnull()
            count += dataframe.loc[not_null_mask, cancel_col].shape[0]

        return count

    @staticmethod
    def validate_strike_price_type(
        custom_mapping_result_series: pd.Series, steeleye_series: pd.Series
    ):
        pass
