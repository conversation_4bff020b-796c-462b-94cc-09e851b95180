from swarm_tasks.steeleye.tr.static import ARMRep<PERSON>
from swarm_tasks.steeleye.tr.static import NCAReport
from swarm_tasks.steeleye.tr.static import PartyModels
from swarm_tasks.steeleye.tr.static import RTS22Transaction
from swarm_tasks.steeleye.tr.static import StaticValues
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.enums import ReportTypeEnum
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.twr_dataclasses import (
    PartyReconciliationFields,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.twr_dataclasses import (
    ReconcilationNestedPartyList,
)

BOOL_MAP = {"true": True, "false": False}

REPORT_TYPE_MAPPING = {
    ReportTypeEnum.NCA_FCA: "nca",
    ReportTypeEnum.ARM_UNAVISTA: "arm",
}

FEEDBACK_STATUS_MAP = {"ACPT": "ACCEPTED", "RJCT": "REJECTED", "PNDG": "PENDING"}

NCA_GENERAL_ONE_TO_ONE_MAP = {
    NCAReport.ADDTLATTRBTS_RSKRDCGTX: RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_COMMODITY_DRV_IND,
    NCAReport.ADDTLATTRBTS_SCTIES_FINCGTXIND: RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_SECRT_FNC_TXN_IND,
    NCAReport.ADDTLATTRBTS_SHRTSELGIND: RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_SHORT_SELLING_INDICATOR,
    NCAReport.ORDRTRNSMSSN_TRNSMSSNIND: RTS22Transaction.TRANSMISSION_DETAILS_ORDER_TRANSMISSION_INDICATOR,
    NCAReport.TX_CMPLXTRADCMPNTID: RTS22Transaction.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID,
    NCAReport.TX_CTRYOFBRNCH: RTS22Transaction.TRANSACTION_DETAILS_BRANCH_MEMBERSHIP_COUNTRY,
    NCAReport.TX_DERVNTLCHNG: RTS22Transaction.TRANSACTION_DETAILS_DERIVATIVE_NOTIONAL_CHANGE,
    NCAReport.TX_NETAMT: RTS22Transaction.TRANSACTION_DETAILS_NET_AMOUNT,
    NCAReport.TX_TRADGCPCTY: RTS22Transaction.TRANSACTION_DETAILS_TRADING_CAPACITY,
    NCAReport.TX_TRADPLCMTCHGID: RTS22Transaction.REPORT_DETAILS_TRADING_VENUE_TRANSACTION_ID_CODE,
    NCAReport.TX_TRADT: RTS22Transaction.TRANSACTION_DETAILS_TRADING_DATE_TIME,
    NCAReport.TX_TRADVN: RTS22Transaction.TRANSACTION_DETAILS_VENUE,
    NCAReport.TX_UPFRNTPMT_AMT: RTS22Transaction.TRANSACTION_DETAILS_UPFRONT_PAYMENT,
    NCAReport.TX_UPFRNTPMT_AMT_CCY: RTS22Transaction.TRANSACTION_DETAILS_UPFRONT_PAYMENT_CURRENCY,
    NCAReport.TXID: RTS22Transaction.REPORT_DETAILS_TRANSACTION_REF_NO,
}

ARM_GENERAL_ONE_TO_ONE_MAP = {
    ARMReport.COMMODITY_DERIVATIVE_INDICATOR: RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_COMMODITY_DRV_IND,
    ARMReport.COMPLEX_TRADE_COMPONENT_ID: RTS22Transaction.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID,
    ARMReport.COUNTRY_OF_BRANCH: RTS22Transaction.TRANSACTION_DETAILS_BRANCH_MEMBERSHIP_COUNTRY,
    ARMReport.DERIVATIVE_NOTIONAL_CHANGE: RTS22Transaction.TRANSACTION_DETAILS_DERIVATIVE_NOTIONAL_CHANGE,
    ARMReport.NET_AMOUNT: RTS22Transaction.TRANSACTION_DETAILS_NET_AMOUNT,
    ARMReport.ORDER_TRANSMISSION_INDICATOR: RTS22Transaction.TRANSMISSION_DETAILS_ORDER_TRANSMISSION_INDICATOR,
    ARMReport.PRICE: RTS22Transaction.TRANSACTION_DETAILS_PRICE,
    ARMReport.PRICE_CURRENCY: RTS22Transaction.TRANSACTION_DETAILS_PRICE_CURRENCY,
    ARMReport.QUANTITY: RTS22Transaction.TRANSACTION_DETAILS_QUANTITY,
    ARMReport.QUANTITY_CURRENCY: RTS22Transaction.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
    ARMReport.SFT_INDICATOR: RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_SECRT_FNC_TXN_IND,
    ARMReport.SHORT_SELLING_INDICATOR: RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_SHORT_SELLING_INDICATOR,
    ARMReport.TRADING_CAPACITY: RTS22Transaction.TRANSACTION_DETAILS_TRADING_CAPACITY,
    ARMReport.TRANSACTION_REF_NO: RTS22Transaction.REPORT_DETAILS_TRANSACTION_REF_NO,
    ARMReport.UP_FRONT_PAYMENT: RTS22Transaction.TRANSACTION_DETAILS_UPFRONT_PAYMENT,
    ARMReport.UP_FRONT_PAYMENT_CURRENCY: RTS22Transaction.TRANSACTION_DETAILS_UPFRONT_PAYMENT_CURRENCY,
    ARMReport.VENUE: RTS22Transaction.TRANSACTION_DETAILS_VENUE,
    ARMReport.VENUE_TRANSACTION_ID: RTS22Transaction.REPORT_DETAILS_TRADING_VENUE_TRANSACTION_ID_CODE,
}

# RTS22Transaction field may be populated from any one of the associated list of report fields
# The list of report fields are stored as 2-element tuples, where the first element is the report field name,
# and the second element is None when we want to use the actual value from the report field, or a
# static value to be used when the report field is populated
NCA_GENERAL_COMPOUND_MAP = {
    RTS22Transaction.TRANSACTION_DETAILS_QUANTITY: [
        (NCAReport.TX_QTY_UNIT, None),
        (NCAReport.TX_QTY_NMNLVAL, None),
        (NCAReport.TX_QTY_MNTRYVAL, None),
    ],
    RTS22Transaction.TRANSACTION_DETAILS_QUANTITY_CURRENCY: [
        (NCAReport.TX_QTY_NMNLVAL_CCY, None),
        (NCAReport.TX_QTY_MNTRYVAL_CCY, None),
    ],
    RTS22Transaction.TRANSACTION_DETAILS_QUANTITY_NOTATION: [
        (NCAReport.TX_QTY_UNIT, StaticValues.UNIT),
        (NCAReport.TX_QTY_NMNLVAL, StaticValues.NOML),
        (NCAReport.TX_QTY_MNTRYVAL, StaticValues.MONE),
    ],
    RTS22Transaction.TRANSACTION_DETAILS_PRICE: [
        (NCAReport.TX_PRIC_PRIC_MNTRYVAL_AMT, None),
        (NCAReport.TX_PRIC_PRIC_PCTG, None),
        (NCAReport.TX_PRIC_PRIC_YLD, None),
        (NCAReport.TX_PRIC_PRIC_BSISPTS, None),
    ],
    RTS22Transaction.TRANSACTION_DETAILS_PRICE_CURRENCY: [
        (NCAReport.TX_PRIC_PRIC_MNTRYVAL_AMT_CCY, None),
        (NCAReport.TX_PRIC_NOPRIC_CCY, None),
    ],
    RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOTATION: [
        (NCAReport.TX_PRIC_PRIC_MNTRYVAL_AMT, StaticValues.MONE),
        (NCAReport.TX_PRIC_PRIC_PCTG, StaticValues.PERC),
        (NCAReport.TX_PRIC_PRIC_YLD, StaticValues.YIEL),
        (NCAReport.TX_PRIC_PRIC_BSISPTS, StaticValues.BAPO),
    ],
}

NCA_PARTY_ONE_TO_ONE_MAP = {
    NCAReport.EXCTGPTY: RTS22Transaction.PARTIES_EXECUTING_ENTITY_FIRM_IDENTIFIERS_LEI,
    NCAReport.INVSTMTPTYIND: RTS22Transaction.PARTIES_EXECUTING_ENTITY_DETAILS_MIFID_REGISTERED,
    NCAReport.ORDRTRNSMSSN_TRNSMTTGBUYR: RTS22Transaction.PARTIES_BUYER_TRANSMITTING_FIRM_FIRM_IDENTIFIERS_LEI,
    NCAReport.ORDRTRNSMSSN_TRNSMTTGSELLR: RTS22Transaction.PARTIES_SELLER_TRANSMITTING_FIRM_FIRM_IDENTIFIERS_LEI,
}

NCA_PARTY_TR_PI_ENRICHMENT_MAP = {
    NCAReport.EXCTGPRSN_PRSN_CTRYOFBRNCH: RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM_OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY,
    NCAReport.INVSTMTDCSNPRSN_PRSN_CTRYOFBRNCH: RTS22Transaction.PARTIES_INV_DEC_WITHIN_FIRM_OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY,
}

ARM_PARTY_ONE_TO_ONE_MAP = {
    ARMReport.BUYER_TRANSMITTER_ID: RTS22Transaction.PARTIES_BUYER_TRANSMITTING_FIRM_FIRM_IDENTIFIERS_LEI,
    ARMReport.EXECUTING_ENTITY_ID: RTS22Transaction.PARTIES_EXECUTING_ENTITY_FIRM_IDENTIFIERS_LEI,
    ARMReport.INVESTMENT_FIRM_INDICATOR: RTS22Transaction.PARTIES_EXECUTING_ENTITY_DETAILS_MIFID_REGISTERED,
    ARMReport.SELLER_TRANSMITTER_ID: RTS22Transaction.PARTIES_SELLER_TRANSMITTING_FIRM_FIRM_IDENTIFIERS_LEI,
}

ARM_PARTY_TR_PI_ENRICHMENT_BUILD_MAP = {
    ARMReport.FIRM_EXECUTION_COUNTRY_OF_BRANCH: RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM_OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY,
    ARMReport.FIRM_EXECUTION_ID: RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM_MIFIR_ID,
    ARMReport.INVESTMENT_DECISION_COUNTRY_OF_BRANCH: RTS22Transaction.PARTIES_INV_DEC_WITHIN_FIRM_OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY,
    ARMReport.INVESTMENT_DECISION_ID: RTS22Transaction.PARTIES_INV_DEC_WITHIN_FIRM_MIFIR_ID,
}

NCA_INSTRUMENT_ONE_TO_ONE_MAP = {
    NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_DLVRYTP: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_DELIVERY_TYPE,
    NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_MTRTYDT: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_BOND_MATURITY_DATE,
    NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_OPTNEXRCSTYLE: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_OPTION_EXERCISE_STYLE,
    NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_OPTNTP: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_OPTION_TYPE,
    NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_PRICMLTPL: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_PRICE_MULTIPLIER,
    NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_XPRYDT: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_EXPIRY_DATE,
    NCAReport.FININSTRM_OTHR_FININSTRMGNLATTRBTS_CLSSFCTNTP: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_INSTRUMENT_CLASSIFICATION,
    NCAReport.FININSTRM_OTHR_FININSTRMGNLATTRBTS_FULLNM: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_INSTRUMENT_FULL_NAME,
    NCAReport.FININSTRM_OTHR_FININSTRMGNLATTRBTS_NTNLCCY: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_NOTIONAL_CURRENCY_1,
}

ARM_INSTRUMENT_ONE_TO_ONE_MAP = {
    ARMReport.DELIVERY_TYPE: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_DELIVERY_TYPE,
    ARMReport.EXPIRY_DATE: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_EXPIRY_DATE,
    ARMReport.INSTRUMENT_CLASSIFICATION: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_INSTRUMENT_CLASSIFICATION,
    ARMReport.INSTRUMENT_ID: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_INSTRUMENT_ID_CODE,
    ARMReport.INSTRUMENT_NAME: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_INSTRUMENT_FULL_NAME,
    ARMReport.MATURIY_DATE: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_BOND_MATURITY_DATE,
    ARMReport.NOTIONAL_CURRENCY_1: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_NOTIONAL_CURRENCY_1,
    ARMReport.NOTIONAL_CURRENCY_2: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_FX_DERIVATIVES_NOTIONAL_CURRENCY2,
    ARMReport.OPTION_STYLE: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_OPTION_EXERCISE_STYLE,
    ARMReport.OPTION_TYPE: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_OPTION_TYPE,
    ARMReport.PRICE_MULTIPLIER: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_PRICE_MULTIPLIER,
    ARMReport.STRIKE_PRICE: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_STRIKE_PRICE,
    ARMReport.STRIKE_PRICE_CURRENCY: RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_STRIKE_PRICE_CURRENCY,
}

NCA_INSTRUMENT_COMPOUND_MAP = {
    RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_INSTRUMENT_ID_CODE: [
        (NCAReport.FININSTRM_ID, None),
        (NCAReport.FININSTRM_OTHR_FININSTRMGNLATTRBTS_ID, None),
    ],
    RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_FX_DERIVATIVES_NOTIONAL_CURRENCY2: [
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_ASSTCLSSSPCFCATTRBTS_INTRST_OTHRNTNLCCY,
            None,
        ),
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_ASSTCLSSSPCFCATTRBTS_FX_OTHRNTLCCY,
            None,
        ),
    ],
    RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_STRIKE_PRICE: [
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_STRKPRIC_PRIC_MNTYRYVAL_AMT,
            None,
        ),
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_STRKPRIC_PRIC_PCTG,
            None,
        ),
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_STRKPRIC_PRIC_YLD,
            None,
        ),
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_STRKPRIC_PRIC_BSISPTS,
            None,
        ),
    ],
    RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_EXT_STRIKE_PRICE_TYPE: [
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_STRKPRIC_PRIC_MNTYRYVAL_AMT,
            StaticValues.MNTRYVAL,
        ),
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_STRKPRIC_PRIC_PCTG,
            StaticValues.PCTG,
        ),
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_STRKPRIC_PRIC_YLD,
            StaticValues.YLD,
        ),
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_STRKPRIC_PRIC_BSISPTS,
            StaticValues.BSISPTS,
        ),
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_STRKPRIC_NOPRIC_PDG,
            StaticValues.NOAP,
        ),
    ],
    RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_STRIKE_PRICE_CURRENCY: [
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_STRKPRIC_PRIC_MNTYRYVAL_AMT_CCY,
            None,
        ),
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_STRKPRIC_NOPRIC_CCY,
            None,
        ),
    ],
    RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_TERM_VALUE: [
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_SNGL_INDX_NM_TERM_VAL,
            None,
        ),
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_BSKT_INDX_NM_TERM_VAL,
            None,
        ),
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_SNGL_INDX_NM_TERM_VAL,
            None,
        ),
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_BSKT_INDX_NM_TERM_VAL,
            None,
        ),
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_SNGL_INDX_NM_TERM_VAL,
            None,
        ),
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_BSKT_INDX_NM_TERM_VAL,
            None,
        ),
    ],
    RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_TERM: [
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_SNGL_INDX_NM_TERM_UNIT,
            None,
        ),
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_BSKT_INDX_NM_TERM_UNIT,
            None,
        ),
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_SNGL_INDX_NM_TERM_UNIT,
            None,
        ),
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_BSKT_INDX_NM_TERM_UNIT,
            None,
        ),
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_SNGL_INDX_NM_TERM_UNIT,
            None,
        ),
        (
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_BSKT_INDX_NM_TERM_UNIT,
            None,
        ),
    ],
}

NCA_PARTY_LIST_MAP = {
    NCAReport.BUYR_ACCTOWNR_ID_LEI: (
        RTS22Transaction.PARTIES_BUYER,
        RTS22Transaction.FIRM_IDENTIFIERS_LEI,
    ),
    NCAReport.BUYR_ACCTOWNR_ID_PRSN_BIRTHDT: (
        RTS22Transaction.PARTIES_BUYER,
        RTS22Transaction.PERSONAL_DETAILS_DOB,
    ),
    NCAReport.BUYR_ACCTOWNR_ID_PRSN_FRSTNM: (
        RTS22Transaction.PARTIES_BUYER,
        RTS22Transaction.PERSONAL_DETAILS_FIRST_NAME,
    ),
    NCAReport.BUYR_ACCTOWNR_ID_PRSN_NM: (
        RTS22Transaction.PARTIES_BUYER,
        RTS22Transaction.PERSONAL_DETAILS_LAST_NAME,
    ),
    NCAReport.BUYR_ACCTOWNR_ID_PRSN_OTHR_ID: (
        RTS22Transaction.PARTIES_BUYER,
        RTS22Transaction.OFFICIAL_IDENTIFIERS_MIFIR_ID,
    ),
    NCAReport.BUYR_DCSNMAKR_LEI: (
        RTS22Transaction.PARTIES_BUYER_DECISION_MAKER,
        RTS22Transaction.FIRM_IDENTIFIERS_LEI,
    ),
    NCAReport.BUYR_DCSNMAKR_PRSN_BIRTHDT: (
        RTS22Transaction.PARTIES_BUYER_DECISION_MAKER,
        RTS22Transaction.PERSONAL_DETAILS_DOB,
    ),
    NCAReport.BUYR_DCSNMAKR_PRSN_FRSTNM: (
        RTS22Transaction.PARTIES_BUYER_DECISION_MAKER,
        RTS22Transaction.PERSONAL_DETAILS_FIRST_NAME,
    ),
    NCAReport.BUYR_DCSNMAKR_PRSN_NM: (
        RTS22Transaction.PARTIES_BUYER_DECISION_MAKER,
        RTS22Transaction.PERSONAL_DETAILS_LAST_NAME,
    ),
    NCAReport.BUYR_DCSNMAKR_PRSN_OTHR_ID: (
        RTS22Transaction.PARTIES_BUYER_DECISION_MAKER,
        RTS22Transaction.OFFICIAL_IDENTIFIERS_MIFIR_ID,
    ),
    NCAReport.SELLR_ACCTOWNR_ID_LEI: (
        RTS22Transaction.PARTIES_SELLER,
        RTS22Transaction.FIRM_IDENTIFIERS_LEI,
    ),
    NCAReport.SELLR_ACCTOWNR_ID_PRSN_BIRTHDT: (
        RTS22Transaction.PARTIES_SELLER,
        RTS22Transaction.PERSONAL_DETAILS_DOB,
    ),
    NCAReport.SELLR_ACCTOWNR_ID_PRSN_ID_FRSTNM: (
        RTS22Transaction.PARTIES_SELLER,
        RTS22Transaction.PERSONAL_DETAILS_FIRST_NAME,
    ),
    NCAReport.SELLR_ACCTOWNR_ID_PRSN_ID_NM: (
        RTS22Transaction.PARTIES_SELLER,
        RTS22Transaction.PERSONAL_DETAILS_LAST_NAME,
    ),
    NCAReport.SELLR_ACCTOWNR_ID_PRSN_OTHR_ID: (
        RTS22Transaction.PARTIES_SELLER,
        RTS22Transaction.OFFICIAL_IDENTIFIERS_MIFIR_ID,
    ),
    NCAReport.SELLR_DCSNMAKR_LEI: (
        RTS22Transaction.PARTIES_SELLER_DECISION_MAKER,
        RTS22Transaction.FIRM_IDENTIFIERS_LEI,
    ),
    NCAReport.SELLR_DCSNMAKR_PRSN_BIRTHDT: (
        RTS22Transaction.PARTIES_SELLER_DECISION_MAKER,
        RTS22Transaction.PERSONAL_DETAILS_DOB,
    ),
    NCAReport.SELLR_DCSNMAKR_PRSN_FRSTNM: (
        RTS22Transaction.PARTIES_SELLER_DECISION_MAKER,
        RTS22Transaction.PERSONAL_DETAILS_FIRST_NAME,
    ),
    NCAReport.SELLR_DCSNMAKR_PRSN_NM: (
        RTS22Transaction.PARTIES_SELLER_DECISION_MAKER,
        RTS22Transaction.PERSONAL_DETAILS_LAST_NAME,
    ),
    NCAReport.SELLR_DCSNMAKR_PRSN_OTHR_ID: (
        RTS22Transaction.PARTIES_SELLER_DECISION_MAKER,
        RTS22Transaction.OFFICIAL_IDENTIFIERS_MIFIR_ID,
    ),
}

ARM_PARTY_LIST_MAP = {
    ARMReport.BUYER_DECISION_MAKER_DOB: (
        RTS22Transaction.PARTIES_BUYER_DECISION_MAKER,
        RTS22Transaction.PERSONAL_DETAILS_DOB,
    ),
    ARMReport.BUYER_DECISION_MAKER_FIRST_NAME: (
        RTS22Transaction.PARTIES_BUYER_DECISION_MAKER,
        RTS22Transaction.PERSONAL_DETAILS_FIRST_NAME,
    ),
    ARMReport.BUYER_DECISION_MAKER_SURNAME: (
        RTS22Transaction.PARTIES_BUYER_DECISION_MAKER,
        RTS22Transaction.PERSONAL_DETAILS_LAST_NAME,
    ),
    ARMReport.BUYER_DOB: (
        RTS22Transaction.PARTIES_BUYER,
        RTS22Transaction.PERSONAL_DETAILS_DOB,
    ),
    ARMReport.BUYER_FIRST_NAME: (
        RTS22Transaction.PARTIES_BUYER,
        RTS22Transaction.PERSONAL_DETAILS_FIRST_NAME,
    ),
    ARMReport.BUYER_SURNAME: (
        RTS22Transaction.PARTIES_BUYER,
        RTS22Transaction.PERSONAL_DETAILS_LAST_NAME,
    ),
    ARMReport.SELLER_DECISION_MAKER_DOB: (
        RTS22Transaction.PARTIES_SELLER_DECISION_MAKER,
        RTS22Transaction.PERSONAL_DETAILS_DOB,
    ),
    ARMReport.SELLER_DECISION_MAKER_FIRST_NAME: (
        RTS22Transaction.PARTIES_SELLER_DECISION_MAKER,
        RTS22Transaction.PERSONAL_DETAILS_FIRST_NAME,
    ),
    ARMReport.SELLER_DECISION_MAKER_SURNAME: (
        RTS22Transaction.PARTIES_SELLER_DECISION_MAKER,
        RTS22Transaction.PERSONAL_DETAILS_LAST_NAME,
    ),
    ARMReport.SELLER_DOB: (
        RTS22Transaction.PARTIES_SELLER,
        RTS22Transaction.PERSONAL_DETAILS_DOB,
    ),
    ARMReport.SELLER_FIRST_NAME: (
        RTS22Transaction.PARTIES_SELLER,
        RTS22Transaction.PERSONAL_DETAILS_FIRST_NAME,
    ),
    ARMReport.SELLER_SURNAME: (
        RTS22Transaction.PARTIES_SELLER,
        RTS22Transaction.PERSONAL_DETAILS_LAST_NAME,
    ),
}

NCA_PARTY_CONDITIONAL_LIST_MAP = {
    NCAReport.BUYR_ACCTOWNR_CTRYOFBRNCH: {
        RTS22Transaction.PARTIES_BUYER: (
            {
                PartyModels.MARKET_COUNTERPARTY: RTS22Transaction.FIRM_IDENTIFIERS_BRANCH_COUNTRY,
                PartyModels.ACCOUNT_FIRM: RTS22Transaction.FIRM_IDENTIFIERS_BRANCH_COUNTRY,
            },
            RTS22Transaction.OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY,
        )
    },
    NCAReport.SELLR_ACCTOWNR_CTRYOFBRNCH: {
        RTS22Transaction.PARTIES_SELLER: (
            {
                PartyModels.MARKET_COUNTERPARTY: RTS22Transaction.FIRM_IDENTIFIERS_BRANCH_COUNTRY,
                PartyModels.ACCOUNT_FIRM: RTS22Transaction.FIRM_IDENTIFIERS_BRANCH_COUNTRY,
            },
            RTS22Transaction.OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY,
        )
    },
}

ARM_PARTY_CONDITIONAL_LIST_MAP = {
    ARMReport.BUYER_ID: {
        RTS22Transaction.PARTIES_BUYER: (
            {
                PartyModels.MARKET_COUNTERPARTY: RTS22Transaction.FIRM_IDENTIFIERS_LEI,
                PartyModels.ACCOUNT_FIRM: RTS22Transaction.FIRM_IDENTIFIERS_LEI,
            },
            RTS22Transaction.OFFICIAL_IDENTIFIERS_MIFIR_ID,
        )
    },
    ARMReport.SELLER_ID: {
        RTS22Transaction.PARTIES_SELLER: (
            {
                PartyModels.MARKET_COUNTERPARTY: RTS22Transaction.FIRM_IDENTIFIERS_LEI,
                PartyModels.ACCOUNT_FIRM: RTS22Transaction.FIRM_IDENTIFIERS_LEI,
            },
            RTS22Transaction.OFFICIAL_IDENTIFIERS_MIFIR_ID,
        )
    },
    ARMReport.BUYER_DECISION_MAKER_ID: {
        RTS22Transaction.PARTIES_BUYER_DECISION_MAKER: (
            {
                PartyModels.MARKET_COUNTERPARTY: RTS22Transaction.FIRM_IDENTIFIERS_LEI,
                PartyModels.ACCOUNT_FIRM: RTS22Transaction.FIRM_IDENTIFIERS_LEI,
            },
            RTS22Transaction.OFFICIAL_IDENTIFIERS_MIFIR_ID,
        )
    },
    ARMReport.SELLER_DECISION_MAKER_ID: {
        RTS22Transaction.PARTIES_SELLER_DECISION_MAKER: (
            {
                PartyModels.MARKET_COUNTERPARTY: RTS22Transaction.FIRM_IDENTIFIERS_LEI,
                PartyModels.ACCOUNT_FIRM: RTS22Transaction.FIRM_IDENTIFIERS_LEI,
            },
            RTS22Transaction.OFFICIAL_IDENTIFIERS_MIFIR_ID,
        )
    },
    ARMReport.BUYER_COUNTRY_OF_BRANCH: {
        RTS22Transaction.PARTIES_BUYER: (
            {
                PartyModels.MARKET_COUNTERPARTY: RTS22Transaction.FIRM_IDENTIFIERS_BRANCH_COUNTRY,
                PartyModels.ACCOUNT_FIRM: RTS22Transaction.FIRM_IDENTIFIERS_BRANCH_COUNTRY,
            },
            RTS22Transaction.OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY,
        )
    },
    ARMReport.SELLER_COUNTRY_OF_BRANCH: {
        RTS22Transaction.PARTIES_SELLER: (
            {
                PartyModels.MARKET_COUNTERPARTY: RTS22Transaction.FIRM_IDENTIFIERS_BRANCH_COUNTRY,
                PartyModels.ACCOUNT_FIRM: RTS22Transaction.FIRM_IDENTIFIERS_BRANCH_COUNTRY,
            },
            RTS22Transaction.OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY,
        )
    },
}

NCA_BUYER_SELLER_AND_DECISION_MAKER_MAP = [
    # Buyer
    ReconcilationNestedPartyList(
        rts22_party_root_field=RTS22Transaction.PARTIES_BUYER,
        reconciliation_fields=[
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.FIRM_IDENTIFIERS_LEI,
                report_field=NCAReport.BUYR_ACCTOWNR_ID_LEI,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.OFFICIAL_IDENTIFIERS_MIFIR_ID,
                report_field=NCAReport.BUYR_ACCTOWNR_ID_PRSN_OTHR_ID,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_FIRST_NAME,
                report_field=NCAReport.BUYR_ACCTOWNR_ID_PRSN_FRSTNM,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_LAST_NAME,
                report_field=NCAReport.BUYR_ACCTOWNR_ID_PRSN_NM,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_DOB,
                report_field=NCAReport.BUYR_ACCTOWNR_ID_PRSN_BIRTHDT,
            ),
        ],
        report_country_branch_field=NCAReport.BUYR_ACCTOWNR_CTRYOFBRNCH,
    ),
    # Buyer Decision Maker
    ReconcilationNestedPartyList(
        rts22_party_root_field=RTS22Transaction.PARTIES_BUYER_DECISION_MAKER,
        reconciliation_fields=[
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.FIRM_IDENTIFIERS_LEI,
                report_field=NCAReport.BUYR_DCSNMAKR_LEI,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.OFFICIAL_IDENTIFIERS_MIFIR_ID,
                report_field=NCAReport.BUYR_DCSNMAKR_PRSN_OTHR_ID,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_FIRST_NAME,
                report_field=NCAReport.BUYR_DCSNMAKR_PRSN_FRSTNM,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_LAST_NAME,
                report_field=NCAReport.BUYR_DCSNMAKR_PRSN_NM,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_DOB,
                report_field=NCAReport.SELLR_DCSNMAKR_PRSN_BIRTHDT,
            ),
        ],
        report_country_branch_field=None,
    ),
    # Seller
    ReconcilationNestedPartyList(
        rts22_party_root_field=RTS22Transaction.PARTIES_SELLER,
        reconciliation_fields=[
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.FIRM_IDENTIFIERS_LEI,
                report_field=NCAReport.SELLR_ACCTOWNR_ID_LEI,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.OFFICIAL_IDENTIFIERS_MIFIR_ID,
                report_field=NCAReport.SELLR_ACCTOWNR_ID_PRSN_OTHR_ID,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_FIRST_NAME,
                report_field=NCAReport.SELLR_ACCTOWNR_ID_PRSN_ID_FRSTNM,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_LAST_NAME,
                report_field=NCAReport.SELLR_ACCTOWNR_ID_PRSN_ID_NM,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_DOB,
                report_field=NCAReport.SELLR_DCSNMAKR_PRSN_BIRTHDT,
            ),
        ],
        report_country_branch_field=NCAReport.SELLR_ACCTOWNR_CTRYOFBRNCH,
    ),
    # Seller Decision Maker
    ReconcilationNestedPartyList(
        rts22_party_root_field=RTS22Transaction.PARTIES_SELLER_DECISION_MAKER,
        reconciliation_fields=[
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.FIRM_IDENTIFIERS_LEI,
                report_field=NCAReport.SELLR_DCSNMAKR_LEI,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.OFFICIAL_IDENTIFIERS_MIFIR_ID,
                report_field=NCAReport.SELLR_DCSNMAKR_PRSN_OTHR_ID,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_FIRST_NAME,
                report_field=NCAReport.SELLR_DCSNMAKR_PRSN_FRSTNM,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_LAST_NAME,
                report_field=NCAReport.SELLR_DCSNMAKR_PRSN_NM,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_DOB,
                report_field=NCAReport.SELLR_DCSNMAKR_PRSN_BIRTHDT,
            ),
        ],
        report_country_branch_field=None,
    ),
]

ARM_BUYER_SELLER_AND_DECISION_MAKER_MAP = [
    # Buyer
    ReconcilationNestedPartyList(
        rts22_party_root_field=RTS22Transaction.PARTIES_BUYER,
        reconciliation_fields=[
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.FIRM_IDENTIFIERS_LEI,
                report_field=ARMReport.BUYER_ID,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.OFFICIAL_IDENTIFIERS_MIFIR_ID,
                report_field=ARMReport.BUYER_ID,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_FIRST_NAME,
                report_field=ARMReport.BUYER_FIRST_NAME,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_LAST_NAME,
                report_field=ARMReport.BUYER_SURNAME,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_DOB,
                report_field=ARMReport.BUYER_DOB,
            ),
        ],
        report_country_branch_field=ARMReport.BUYER_COUNTRY_OF_BRANCH,
    ),
    # Buyer Decision Maker
    ReconcilationNestedPartyList(
        rts22_party_root_field=RTS22Transaction.PARTIES_BUYER_DECISION_MAKER,
        reconciliation_fields=[
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.FIRM_IDENTIFIERS_LEI,
                report_field=ARMReport.BUYER_DECISION_MAKER_ID,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.OFFICIAL_IDENTIFIERS_MIFIR_ID,
                report_field=ARMReport.BUYER_DECISION_MAKER_ID,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_FIRST_NAME,
                report_field=ARMReport.BUYER_DECISION_MAKER_FIRST_NAME,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_LAST_NAME,
                report_field=ARMReport.BUYER_DECISION_MAKER_SURNAME,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_DOB,
                report_field=ARMReport.BUYER_DECISION_MAKER_DOB,
            ),
        ],
        report_country_branch_field=None,
    ),
    # Seller
    ReconcilationNestedPartyList(
        rts22_party_root_field=RTS22Transaction.PARTIES_SELLER,
        reconciliation_fields=[
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.FIRM_IDENTIFIERS_LEI,
                report_field=ARMReport.SELLER_ID,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.OFFICIAL_IDENTIFIERS_MIFIR_ID,
                report_field=ARMReport.SELLER_ID,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_FIRST_NAME,
                report_field=ARMReport.SELLER_FIRST_NAME,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_LAST_NAME,
                report_field=ARMReport.SELLER_SURNAME,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_DOB,
                report_field=ARMReport.SELLER_DOB,
            ),
        ],
        report_country_branch_field=ARMReport.SELLER_COUNTRY_OF_BRANCH,
    ),
    # Seller Decision Maker
    ReconcilationNestedPartyList(
        rts22_party_root_field=RTS22Transaction.PARTIES_SELLER_DECISION_MAKER,
        reconciliation_fields=[
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.FIRM_IDENTIFIERS_LEI,
                report_field=ARMReport.SELLER_DECISION_MAKER_ID,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.OFFICIAL_IDENTIFIERS_MIFIR_ID,
                report_field=ARMReport.SELLER_DECISION_MAKER_ID,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_FIRST_NAME,
                report_field=ARMReport.SELLER_DECISION_MAKER_FIRST_NAME,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_LAST_NAME,
                report_field=ARMReport.SELLER_DECISION_MAKER_SURNAME,
            ),
            PartyReconciliationFields(
                rts22_party_nested_field=RTS22Transaction.PERSONAL_DETAILS_DOB,
                report_field=ARMReport.SELLER_DECISION_MAKER_DOB,
            ),
        ],
        report_country_branch_field=None,
    ),
]
