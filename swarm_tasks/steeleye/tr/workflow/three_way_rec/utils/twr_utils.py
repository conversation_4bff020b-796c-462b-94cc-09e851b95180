import pandas as pd

from swarm_tasks.steeleye.tr.static import RTS22Transaction


def clnt_nore_check(
    clnt_nore_column: str,
    data_to_reconcile: pd.DataFrame,
    steeleye_series: pd.Series,
    steeleye_series_field_names: pd.Series,
):
    """
    This function serves as an auxiliary to reconcile_compound_party_fields to add the logic
    to verify when the party executionWithinFirm is client:nore
    :param clnt_nore_column: column where to lookup if clnt:nore is the executionWithinFirm
    :param data_to_reconcile: dataframe to reconcile with
    :param steeleye_series: series of reconciliation result
    :param steeleye_series_field_names: series of the steeleye_series field names used to reconcile
    """
    not_populated_mask = steeleye_series.isnull()
    clnt_nore_mask = (
        data_to_reconcile.loc[:, clnt_nore_column].apply(
            lambda market_ids_list: any(
                [
                    (
                        dic.get("path")
                        == RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM
                        and dic.get("labelId") == "clnt:nore"
                    )
                    for dic in market_ids_list
                ]
            )
        )
        & not_populated_mask
    )
    steeleye_series.loc[clnt_nore_mask] = "NORE"
    steeleye_series_field_names.loc[
        clnt_nore_mask
    ] = RTS22Transaction.MARKET_IDENTIFIERS_PARTIES_EXECUTION_WITHIN_FIRM
