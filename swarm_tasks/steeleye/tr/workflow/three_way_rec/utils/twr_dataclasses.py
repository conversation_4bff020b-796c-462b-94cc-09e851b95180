from dataclasses import dataclass
from typing import List
from typing import Optional

import pandas as pd


@dataclass
class PartyReconciliationFields:
    report_field: str
    rts22_party_nested_field: str


@dataclass
class ReconcilationNestedPartyList:
    reconciliation_fields: List[PartyReconciliationFields]
    report_country_branch_field: Optional[str]
    rts22_party_root_field: str


@dataclass
class ThreeWayRecResultStats:
    complete_matches_count: int
    earliest_transaction_date: str
    ingested_trn_breaks_count: int
    latest_transaction_date: str
    matches_with_field_breaks_count: int
    records_count: int
    trn_breaks_count: int


@dataclass
class UnderlyingInstrumentComparison:
    report_series: pd.Series
    steeleye_series: pd.Series
    names_series: pd.Series = None
