from se_core_tasks.abstractions.transformations.transform_map import TransformMap

from swarm_tasks.steeleye.tr.feed.enfusion.v2.enfusion_v2_tr_transformations import (
    EnfusionV2Transformations,
)
from swarm_tasks.steeleye.tr.feed.enfusion.v2.thornbridge_enfusion_v2_tr_transformations import (
    ThornbridgeEnfusionV2Transformations,
)
from swarm_tasks.steeleye.tr.feed.julius_baer.julius_baer_transformations import (
    JuliusBaerTransformations,
)
from swarm_tasks.steeleye.tr.feed.lme.lme_tr_transformations import LmeTrTransformations
from swarm_tasks.steeleye.tr.feed.saxo_bank.saxo_bank_transformations import (
    SaxoBankTransformations,
)
from swarm_tasks.steeleye.tr.feed.saxo_bank.thornbridge.saxo_bank_thornbridge_transformations import (
    SaxoBankThornbridgeTransformations,
)
from swarm_tasks.steeleye.tr.feed.unavista.unavista_tr_transformations import (
    UnaVistaTransformations,
)

saxo_bank_transform_map = TransformMap(
    default=SaxoBankTransformations,
    map={"thornbridge": SaxoBankThornbridgeTransformations},
)

una_vista_transform_map = TransformMap(
    default=UnaVistaTransformations,
    map={},
)

julius_baer_transform_map = TransformMap(
    default=JuliusBaerTransformations,
    map={},
)
enfusion_v2_transform_map = TransformMap(
    default=EnfusionV2Transformations,
    map={
        "thornbridge": ThornbridgeEnfusionV2Transformations,
    },
)
lme_transform_map = TransformMap(
    default=LmeTrTransformations,
    map={},
)
