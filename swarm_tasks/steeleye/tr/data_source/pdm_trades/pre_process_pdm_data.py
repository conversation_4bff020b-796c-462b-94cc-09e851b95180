from typing import List
from typing import Optional

import pandas as pd
from prefect.engine.signals import SKIP
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.utilities.task_utils import BaseColumns


class PDMColumns(BaseColumns):
    ASSET_MATURITY_DATE = "Asset_MaturityDate"
    ASSET_NAME = "Asset_Name"
    ASSET_RATE1 = "Asset_Rate1"
    ASSET_SECURITY_ID = "Asset_SecurityID"
    COUNTER_BANK_ABBREV_NAME = "CounterBank_AbbrevName"
    COUNTER_BANK_ENTITY_ID = "CounterBank_EntityId"
    CURRENCY_TYPE_IDENTIFIER = "CurrencyType_Identifier"
    ISSUER_NAME = "Issuer_Name"
    POSITION_ID = "Position_ID"
    TOTAL_AMOUNT = "TotalAmount"
    TRADE_ID = "Trade_ID"
    TRADE_MIFIR_TIMESTAMP = "Trade_mifirtimestamp"
    TRADE_PARAMOUNT = "Trade_ParAmount"
    TRADE_PRICE = "Trade_Price"
    TRADE_SETTLE_DATE = "Trade_SettleDate"
    TRADE_TRADE_GROUP_ID = "Trade_TradeGroup_ID"
    TRADE_TYPE_DESCRIPTION = "TradeTypeDescription"
    TRADE_GROUP_MIFIR_TIMESTAMP = "TradeGroup_mifirtimestamp"
    TRADER_NOTES = "Trader_Notes"


class AssetSecurityID:
    LX = "LX"


class Params(BaseParams):
    group_by_aggregate_dict: dict = Field(
        ...,
        description="Dict with aggregate logic after pandas dataframe group-by.",
    )
    group_by_columns: List[str] = Field(
        ...,
        description="Column names for pandas dataframe group-by.",
    )


class PreProcessPDMData(TransformBaseTask):
    """
    This task reads the incoming source dataframe for PDM trades and groups the columns based on params group_by_columns
    and then performs aggregate functions on the columns based on param group_by_aggregate_dict.
    e.g. group_by_columns = [TRADE_TRADE_GROUP_ID] and
         group_by_aggregate_dict: {TRADE_TRADE_GROUP_ID: first, TotalAmount: sum, Trade_ID: first,
                                   Trade_Par_Amount: sum}
         groups rows on TRADE_TRADE_GROUP_ID and sums the value in TotalAmount and Trade_ParAmount and returns the first
         row on TRADE_TRADE_GROUP_ID and Trade_ID.
    Confluence Page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2010743278/RTS22+Permira+Debt
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: Optional[BaseResources] = None,
        **kwargs,
    ) -> pd.DataFrame:
        data = source_frame.copy()
        cols_used = PDMColumns().get_columns()
        cols_mask = data.columns.isin(cols_used)
        data = data.loc[:, cols_mask]

        # check for required columns
        for col in [
            PDMColumns.TRADE_TRADE_GROUP_ID,
            PDMColumns.TOTAL_AMOUNT,
            PDMColumns.TRADE_PARAMOUNT,
        ]:
            if col not in data.columns:
                msg = f"Missing required column:{col}"
                raise SKIP(msg)

        for col in cols_used:
            if col not in data.columns:
                data.loc[:, col] = pd.NA

        for col in [PDMColumns.TOTAL_AMOUNT, PDMColumns.TRADE_PARAMOUNT]:
            col_not_null_mask = data[col].notnull()
            data.loc[col_not_null_mask, col] = (
                data.loc[col_not_null_mask, col].astype(float).abs()
            )

        # ignore rows where ASSET_SECURITY_ID=LX
        asset_security_lx_mask = (
            data[PDMColumns.ASSET_SECURITY_ID]
            .str.upper()
            .str.startswith(AssetSecurityID.LX, na=False)
        )
        data = data.loc[~asset_security_lx_mask]

        # Applies group-by and then aggregate function based on the group-by columns and aggregate dict provided
        grouped_df = data.groupby(
            params.group_by_columns,
            as_index=False,
        ).aggregate(params.group_by_aggregate_dict)
        return grouped_df
