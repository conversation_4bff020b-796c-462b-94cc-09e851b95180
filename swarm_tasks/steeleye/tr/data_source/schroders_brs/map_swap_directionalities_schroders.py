from typing import List
from typing import Optional
from typing import Union

import pandas as pd
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class SwapDirectionalities:
    SWAP_IN = "Swap In"
    SWAP_OUT = "Swap Out"
    OTHER = "Other"


class Params(BaseParams):

    source_index_classification_attribute: str = Field(
        ..., description="Column name of Source Index Classification"
    )
    source_instrument_classification_attribute: str = Field(
        ..., description="Column name of Source Instrument Classification"
    )
    swapin_pattern: str = Field(
        ...,
        description="Pattern to find in `source_index_classification_attribute` or "
        "`source_instrument_classification_attribute` to determine if a trade is a Swap In",
    )
    swapout_pattern: str = Field(
        ...,
        description="Pattern to find in `source_index_classification_attribute` or "
        "`source_instrument_classification_attribute` to determine if a trade is a Swap Out",
    )
    target_attribute: str = Field(..., description="Target column's name")


class MapSwapDirectionalitiesSchroders(TransformBaseTask):

    params_class = Params

    def execute(
        self,
        source_frame: Optional[pd.DataFrame] = None,
        params: Optional[Params] = None,
        **kwargs,
    ) -> pd.DataFrame:
        """
        A generic version of this task exists on `se-core-tasks`, this generic version is essentially
        a fork of the current task and will be maintained separately.

        Logic as requested in https://steeleye.atlassian.net/browse/ON-1893:
        "if [UV Index Classification].isPopulated() then:
            [UV Index Classification] (comma separated, will map to the array schema field)
        elif [UV Instrument Classification].isPopulated() then:
            [UV Instrument Classification] (comma separated, will map to the array schema field)

        whatever the result of the client value, this should be passed through the below logic
        if the field contains ""swapin"" (e.g., SwapInBasket), map this to ""Swap In"" in the schema)
        if the field contains ""swapout"" (e.g., SwapOutBasket), map this to ""Swap Out"" in the schema)
        else, parse as ""Other"" (i.e., only show other when either [UV Instrument Classification]
        or [UV Index Classification] is populated)"

        Populates "transactionDetails.swapDirectionalities" (array of enums)

        """

        target = pd.DataFrame(index=source_frame.index)
        target[params.target_attribute] = pd.NA

        if (
            params.source_index_classification_attribute not in source_frame.columns
            or params.source_instrument_classification_attribute
            not in source_frame.columns
        ):
            return target

        necessary_columns = [
            params.source_index_classification_attribute,
            params.source_instrument_classification_attribute,
        ]
        col_mask = source_frame.columns.isin(necessary_columns)
        classification_columns = source_frame.loc[:, col_mask]

        swap_mask = classification_columns.notnull().any(axis=1)
        if not swap_mask.any():
            return target

        target[params.target_attribute] = classification_columns.loc[
            swap_mask, :
        ].apply(
            lambda x: self._get_swap_directionalities(
                row=x,
                source_index_classification_attribute=params.source_index_classification_attribute,
                source_instrument_classification_attribute=params.source_instrument_classification_attribute,
                swapin_pattern=params.swapin_pattern,
                swapout_pattern=params.swapout_pattern,
            ),
            axis=1,
        )

        target = target.fillna(pd.NA)

        return target

    @staticmethod
    def _get_swap_directionalities(
        row: pd.Series,
        source_index_classification_attribute: str,
        source_instrument_classification_attribute: str,
        swapin_pattern: str,
        swapout_pattern: str,
    ) -> Optional[List[str]]:
        """
         Populates the "transactionDetails.swapDirectionalities" field
        :param row: row with source index and instrument classification data
        :param source_index_classification_attribute: source index classification
        :param source_instrument_classification_attribute: source instrument classification
        :param swapin_pattern: pattern to determine if a trade is a Swap In
        :param swapout_pattern: pattern to determine if a trade is a Swap Out
        :return: List of SwapDirectionalities enum
        """

        result = []

        index_classification: Union[List[str], pd.NA] = row.loc[
            source_index_classification_attribute
        ]

        index_classification_is_populated: bool = isinstance(index_classification, list)

        instrument_classification: Union[List[str], pd.NA] = row.loc[
            source_instrument_classification_attribute
        ]
        instrument_classification_is_populated: bool = isinstance(
            instrument_classification, list
        )

        classification_list = (
            index_classification
            if index_classification_is_populated
            and instrument_classification_is_populated
            and instrument_classification[0][0].lower() == "s"
            else instrument_classification
            if instrument_classification_is_populated
            and instrument_classification[0][0].lower() == "s"
            else []
        )

        for classification_value in classification_list:

            classification_value = classification_value.lower()

            if swapin_pattern in classification_value:
                result.append(SwapDirectionalities.SWAP_IN)

            elif swapout_pattern in classification_value:
                result.append(SwapDirectionalities.SWAP_OUT)

            else:
                result.append(SwapDirectionalities.OTHER)

        return result if result else None
