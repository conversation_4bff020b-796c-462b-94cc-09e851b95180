from enum import Enum
from typing import Optional

import pandas as pd
from se_elastic_schema.validators.steeleye.venues.venue_validator import VenueValidator
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

TRX_DTL_VENUE = "transactionDetails.venue"
TRX_REF_NO = "reportDetails.transactionRefNo"
TARGET_ATTRIBUTE = "reportDetails.tradingVenueTransactionIdCode"


class VenueTypeValidator(Enum):

    EEA_VENUES = "is_eea_venue"
    NON_EEA_VENUES = "is_non_eea_venue"
    SI_VENUES = "is_systematic_internaliser_venue"
    UK_VENUES = "is_uk_venue"


class Params(BaseParams):

    venue_type: str


class TradingVenueTransactionIdCode(TransformBaseTask):
    """
    This task populates the reportDetails.tradingVenueTransactionIdCode field.
    params.venue_type is mapped to a validator through the VenueTypeValidator enum.
    If it exists, the result is the validator from
    se_elastic_schema.validators.steeleye.venues.venue_validator that will be used

    Logic:
        if "transactionDetails.venue" passes the venue_type validation (ex: venue is in UK_venues.txt list):
        Populate "reportDetails.tradingVenueTransactionIdCode" with respective "reportDetails.transactionRefNo" value
    """

    params_class = Params

    def execute(
        self,
        source_frame: Optional[pd.DataFrame] = None,
        params: Optional[Params] = None,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index)
        target[TARGET_ATTRIBUTE] = pd.NA
        venue_type_param = params.venue_type
        venue_validator = VenueTypeValidator[venue_type_param]

        not_null_venue_mask = source_frame.loc[:, TRX_DTL_VENUE].notnull()

        valid_venue_mask = not_null_venue_mask & source_frame.loc[
            not_null_venue_mask, TRX_DTL_VENUE
        ].apply(lambda x: getattr(VenueValidator, venue_validator.value)(x))

        if not valid_venue_mask.any():
            return target

        target.loc[valid_venue_mask, TARGET_ATTRIBUTE] = source_frame.loc[
            valid_venue_mask, TRX_REF_NO
        ]

        target = target.fillna(pd.NA)

        return target
