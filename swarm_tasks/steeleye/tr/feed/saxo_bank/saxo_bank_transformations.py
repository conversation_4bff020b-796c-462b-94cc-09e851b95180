import os
from typing import Union

import pandas as pd
from prefect.engine import signals
from se_core_tasks.currency.convert_minor_to_major import (
    CastTo as ConvertMinorToMajorCastTo,
)
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_attribute import CastTo as MapAttributeCastTo
from se_core_tasks.map.map_conditional import Case
from se_core_tasks.map.map_value import RegexReplaceMap
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import ReportStatus
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_trades_tasks.abstractions.abstract_rts22_transaction_transformations import (
    AbstractRTS22TransactionsTransformations,
)
from se_trades_tasks.order_and_tr.party.utils import add_id_or_lei
from se_trades_tasks.order_and_tr.static import AssetClass
from swarm.task.auditor import Auditor

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ConvertMinorToMajorParams,
)
from swarm_tasks.steeleye.tr.static import RTS22Transaction
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ConvertDatetimeParams,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as MapAttributeParams
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as MapConditionalParams
from swarm_tasks.transform.map.map_static import MapStatic
from swarm_tasks.transform.map.map_static import Params as MapStaticParams
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as MapValueParams
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as MergeMarketIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.transaction_ref_no import (
    Params as TransactionRefNoParams,
)
from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.transaction_ref_no import (
    TransactionRefNo,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as InstrumentIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.identifiers.party_identifiers import (
    Params as PartyIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.identifiers.party_identifiers import (
    PartyIdentifiers,
)


class SaxoBankColumns:
    ACCOUNT_CURRENCY = "AccountCurrency"
    BUY_SELL = "BuySell"
    CALL_PUT = "CallPut"
    COMPLEX_TRADE_COMPONENT_ID = "ComplexTradeComponentId"
    COUNTERPARTY_ID = "CounterpartID"
    DIRTY_PRICE = "DirtyPrice"
    EXCHANGE_ISO_CODE = "ExchangeISOCode"
    EXECUTING_ENTITY_IDENTIFICATION_CODE = "ExecutingEntityIdentificationCode"
    EXPIRY_DATE = "ExpiryDate"
    FXTYPE = "FXType"
    ISIN = "ISIN"
    ISIN_CODE = "ISINCode"
    ISOMic = "ISOMic"
    INSTRUMENT_CODE = "InstrumentCode"
    INSTRUMENT_CURRENCY = "InstrumentCurrency"
    INSTRUMENT_ISIN_CODE = "InstrumentISINCode"
    NET_AMOUNT = "NetAmount"
    PRICE = "Price"
    SHORT_SELLING_INDICATOR = "ShortSellingIndicator"
    STRIKE = "Strike"
    TERM_OF_UNDERLYING_INDEX = "TermOfUnderlyingIndex"
    TRADE_EXECUTION_TIME = "TradeExecutionTime"
    TRADE_NUMBER = "TradeNumber"
    TRADE_TIME = "TradeTime"
    TRADED_AMOUNT = "TradedAmount"
    TRANSMISSION_OF_ORDER_INDICATOR = "TransmissionOfOrderIndicator"
    UNDERLYING_INSTRUMENT_CODE = "UnderlyingInstrumentCode"
    UNDERLYING_INSTRUMENT_ISIN_CODE = "UnderlyingInstrumentISINCode"
    USER_ID_ON_TRADE = "UserIdOnTrade"
    VENUE = "Venue"


class TempColumns:
    EXPIRY_DATE = "__expiry_date__"
    ISIN = "__isin__"
    VENUE = "__venue__"


class InstrumentIDsColumns:
    ASSET_CLASS = "__asset_class__"
    CURRENCY = "__currency__"
    EXPIRY_DATE = "__expiry_date__"
    ISIN = "__isin__"
    NOTIONAL_CURRENCY_2 = "__notional_currency_2__"
    OPTION_STRIKE_PRICE = "__option_strike_price__"
    OPTION_STRIKE_TYPE = "__option_strike_type__"
    UNDERLYING_INDEX_NAME = "__underlying_index_name__"
    UNDERLYING_INDEX_TERM = "__underlying_index_term__"
    UNDERLYING_ISIN = "__underlying_isin__"
    UNDERLYING_SYMBOL = "__underlying_symbol__"
    VENUE = "__venue__"


class PartyIDsColumns:
    BUYER = "__buyer__"
    BUY_SELL_INDICATOR = "__buy_sell_indicator__"
    COUNTERPARTY = "__counterparty__"
    EXECUTION_WITHIN_FIRM = "__execution_within_firm__"
    EXEC_ENTITY = "__exec_entity__"
    INVESTMENT_DECISION_WITHIN_FIRM = "__investment_decision_within_firm__"
    SELLER = "__seller__"
    TRADER = "__trader__"


class FileTypes:
    BONDS = "BondsTrades"
    CASH_TRANACTIONS = "CashTransactions"
    CFD = "CFDOptionTrades"
    CFD_EXECUTED = "CFDOptionTradesExecuted"
    FUTURES = "FuturesTrades"
    FX = "FXTrades"
    FX_OPTION = "FXOptionTrades"
    MUTUAL_FUNDS = "MutualFundsTrades"
    ORDER_ACTIVITY = "OrderActivity"
    SHARE = "ShareTrades"
    SP = "SPTrades"


class SaxoBankTransformations(AbstractRTS22TransactionsTransformations):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.file_url = os.getenv("SWARM_FILE_URL")
        self.file_name = os.path.basename(self.file_url)
        self.file_type = self.get_file_type(file_name=self.file_name)

        if not self.file_type:
            raise signals.FAIL(
                message=f"File type not recognised. File name: {self.file_name} "
                f"should start with one of: [BondsTrades, CFDTrades, "
                "FuturesTrades, FXTrades, FXOptionsTrades, "
                "MutualFundsTrades, ShareTrades, SPTrades]"
            )

        if self.file_type in [
            FileTypes.CASH_TRANACTIONS,
            FileTypes.CFD_EXECUTED,
            FileTypes.ORDER_ACTIVITY,
        ]:
            raise signals.SKIP("File Type is not supported for RTS22")

    def _pre_process(self):
        """
        Creates temporary column expiry date, used later in InstrumentIdentifiers
        and downstream in InstrumentOverrideExpiryDate
        """
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._instrument_details_instrument_derivative_expiry_date(),
                self._instr_ids_isin(),
            ],
            axis=1,
        )

        self.pre_process_df = pd.concat(
            [self.pre_process_df, self._temp_venue()],
            axis=1,  # needs ISIN
        )

    def _post_process(self):
        """
        Adds the temporary column '__expiry_date__' to be used downstream in
        InstrumentOverrideExpiryDate
        """
        self.target_df.loc[:, TempColumns.EXPIRY_DATE] = self.pre_process_df.loc[
            :, TempColumns.EXPIRY_DATE
        ]

    def _data_source_name(self) -> pd.DataFrame:
        """
        Static value: Saxobank
        """
        return map_static(
            source_frame=self.source_frame,
            target_attribute=RTS22Transaction.DATA_SOURCE_NAME,
            target_value="Saxobank",
        )

    def _date(self) -> pd.DataFrame:
        """
        Populates from TradeTime for Mutual and TradeExecutionTime for the rest
        """
        if self.file_type in [FileTypes.MUTUAL_FUNDS]:
            return convert_date_time(
                source_frame=self.source_frame,
                source_attribute=SaxoBankColumns.TRADE_TIME,
                target_attribute=RTS22Transaction.DATE,
                convert_to=ConvertTo.DATE.value,
            )
        else:
            return convert_date_time(
                source_frame=self.source_frame,
                source_attribute=SaxoBankColumns.TRADE_EXECUTION_TIME,
                target_attribute=RTS22Transaction.DATE,
                convert_to=ConvertTo.DATE.value,
            )

    def _meta_model(self) -> pd.DataFrame:
        """
        Static value: RTS22Transaction
        """
        return map_static(
            source_frame=self.source_frame,
            target_attribute=RTS22Transaction.META_MODEL,
            target_value="RTS22Transaction",
        )

    def _report_details_investment_firm_covered_directive(self) -> pd.DataFrame:
        """
        Static value: NEWT
        """
        return map_static(
            source_frame=self.source_frame,
            target_attribute=RTS22Transaction.REPORT_DETAILS_INVESTMENT_FIRM_COVERED_DIRECTIVE,
            target_value=True,
        )

    def _report_details_report_status(self) -> pd.DataFrame:
        """
        Static value: NEWT
        """
        return map_static(
            source_frame=self.source_frame,
            target_attribute=RTS22Transaction.REPORT_DETAILS_REPORT_STATUS,
            target_value=ReportStatus.NEWT.value,
        )

    def _report_details_trading_venue_transaction_id_code(self):
        """Not Implemented"""

    def _source_index(self) -> pd.DataFrame:
        """
        Static value: index from the source_frame
        """
        return map_static(
            source_frame=self.source_frame,
            target_attribute=RTS22Transaction.SOURCE_INDEX,
            from_index=True,
        )

    def _source_key(self) -> pd.DataFrame:
        """
        Static value: File URL
        """
        return map_static(
            source_frame=self.source_frame,
            target_attribute=RTS22Transaction.SOURCE_KEY,
            target_value=self.file_url,
        )

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(self):
        """
        Static mapping to Blank for tradersAlgosWaiversIndicators.commodityDerivativeIndicator
        JIRA: https://steeleye.atlassian.net/browse/ON-2871
        """

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(self):
        """Not Implemented"""

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(self):
        """
        Static value: False
        """
        return map_static(
            source_frame=self.source_frame,
            target_attribute=RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_SECRT_FNC_TXN_IND,
            target_value=False,
        )

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """
        Populates from ShortSellingIndicator for Share
        """
        if self.file_type in [FileTypes.SHARE]:
            return map_attribute(
                source_frame=self.source_frame,
                source_attribute=SaxoBankColumns.SHORT_SELLING_INDICATOR,
                target_attribute=RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_SHORT_SELLING_INDICATOR,
                auditor=self.auditor,
            )

    def _traders_algos_waivers_indicators_waiver_indicator(self):
        """Not Implemented"""

    def _transaction_details_branch_membership_country(self):
        """Not Implemented"""

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Populates from BuySell
        """
        return map_value(
            source_frame=self.source_frame,
            source_attribute=SaxoBankColumns.BUY_SELL,
            target_attribute=RTS22Transaction.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
            case_insensitive=True,
            value_map={
                "Buy": BuySellIndicator.BUYI.value,
                "Sell": BuySellIndicator.SELL.value,
            },
            auditor=self.auditor,
        )

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """
        Populates from ComplexTradeComponentId for Futures
        """
        if self.file_type in [FileTypes.FUTURES]:
            return map_attribute(
                source_frame=self.source_frame,
                source_attribute=SaxoBankColumns.COMPLEX_TRADE_COMPONENT_ID,
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID,
                auditor=self.auditor,
            )

    def _transaction_details_cross_indicator(self):
        """Not Implemented"""

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """
        Populates from NetAmount for Bonds
        """
        if self.file_type in [FileTypes.BONDS]:
            return map_attribute(
                source_frame=self.source_frame,
                source_attribute=SaxoBankColumns.NET_AMOUNT,
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_NET_AMOUNT,
                cast_to="numeric.absolute",
                auditor=self.auditor,
            )

    def _transaction_details_outgoing_order_addl_info(self):
        """Not Implemented"""

    def _transaction_details_position_effect(self):
        """Not Implemented"""

    def _transaction_details_price(self) -> pd.DataFrame:
        """
        Populates from DirtyPrice for Bonds and Price for the rest
        """
        if self.file_type in [FileTypes.BONDS]:
            return convert_minor_to_major_price(
                source_frame=self.source_frame,
                source_price_attr=SaxoBankColumns.DIRTY_PRICE,
                source_ccy_attr=SaxoBankColumns.INSTRUMENT_CURRENCY,
                target_price_attr=RTS22Transaction.TRANSACTION_DETAILS_PRICE,
            )
        else:
            return convert_minor_to_major_price(
                source_frame=self.source_frame,
                source_price_attr=SaxoBankColumns.PRICE,
                source_ccy_attr=SaxoBankColumns.INSTRUMENT_CURRENCY,
                target_price_attr=RTS22Transaction.TRANSACTION_DETAILS_PRICE,
            )

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """
        Populates from InstrumentCurrency
        """
        return convert_minor_to_major_ccy(
            source_frame=self.source_frame,
            source_ccy_attr=SaxoBankColumns.INSTRUMENT_CURRENCY,
            target_ccy_attr=RTS22Transaction.TRANSACTION_DETAILS_PRICE_CURRENCY,
        )

    def _transaction_details_price_not_applicable(self):
        """Not Implemented"""

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """
        PERC if bond else static value MONE
        """
        if self.file_type in [FileTypes.BONDS]:
            price_notation = PriceNotation.PERC.value
        else:
            price_notation = PriceNotation.MONE.value

        return map_static(
            source_frame=self.source_frame,
            target_attribute=RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOTATION,
            target_value=price_notation,
        )

    def _transaction_details_price_pending(self):
        """Not Implemented"""

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """
        Populates from TradedAmount
        """
        return map_attribute(
            source_frame=self.source_frame,
            source_attribute=SaxoBankColumns.TRADED_AMOUNT,
            target_attribute=RTS22Transaction.TRANSACTION_DETAILS_QUANTITY,
            cast_to=MapAttributeCastTo.NUMERIC_ABSOLUTE.value,
            auditor=self.auditor,
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """
        Populates from InstrumentCurrency for FXOptions and FXTrades
        """
        if self.file_type in [FileTypes.FX, FileTypes.FX_OPTION, FileTypes.BONDS]:
            return convert_minor_to_major_ccy(
                source_frame=self.source_frame,
                source_ccy_attr=SaxoBankColumns.ACCOUNT_CURRENCY,
                target_ccy_attr=RTS22Transaction.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
            )
        else:
            return pd.DataFrame(
                data=pd.NA,
                index=self.source_frame.index,
                columns=[RTS22Transaction.TRANSACTION_DETAILS_QUANTITY_CURRENCY],
            )

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """
        MONE when file_type is bond else Static value: UNIT
        """
        if self.file_type in [FileTypes.BONDS]:
            quantity_notation = QuantityNotation.MONE.value
        else:
            quantity_notation = QuantityNotation.UNIT.value

        return map_static(
            source_frame=self.source_frame,
            target_attribute=RTS22Transaction.TRANSACTION_DETAILS_QUANTITY_NOTATION,
            target_value=quantity_notation,
        )

    def _transaction_details_record_type(self):
        """Not Implemented"""

    def _transaction_details_settlement_amount(self):
        """Not Implemented"""

    def _transaction_details_settlement_amount_currency(self):
        """Not Implemented"""

    def _transaction_details_settlement_date(self):
        """Not Implemented"""

    def _transaction_details_swap_directionalities(self):
        """Not Implemented"""

    def _transaction_details_traded_quantity(self):
        """Not Implemented"""

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """
        Static value: AOTC
        """
        return map_static(
            source_frame=self.source_frame,
            target_attribute=RTS22Transaction.TRANSACTION_DETAILS_TRADING_CAPACITY,
            target_value=TradingCapacity.AOTC.value,
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """
        Populates from TradeTime for Mutual Trades and TradeExecutionTime for the rest
        """
        if self.file_type in [FileTypes.MUTUAL_FUNDS]:
            return convert_date_time(
                source_frame=self.source_frame,
                source_attribute=SaxoBankColumns.TRADE_TIME,
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_TRADING_DATE_TIME,
                convert_to=ConvertTo.DATETIME.value,
            )
        else:
            return convert_date_time(
                source_frame=self.source_frame,
                source_attribute=SaxoBankColumns.TRADE_EXECUTION_TIME,
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_TRADING_DATE_TIME,
                convert_to=ConvertTo.DATETIME.value,
            )

    def _transaction_details_trail_id(self):
        """Not Implemented"""

    def _transaction_details_ultimate_venue(self):
        """
        Static value: XOFF is ISIN is populated, XXXX if not
        Populated from TempColumns.VENUE
        """
        return map_attribute(
            source_frame=self.pre_process_df,
            source_attribute=TempColumns.VENUE,
            target_attribute=RTS22Transaction.TRANSACTION_DETAILS_ULTIMATE_VENUE,
            auditor=self.auditor,
        )

    def _transaction_details_venue(self) -> pd.DataFrame:
        """
        Static value: XOFF is ISIN is populated, XXXX if not
        Populated from TempColumns.VENUE
        """
        return map_attribute(
            source_frame=self.pre_process_df,
            source_attribute=TempColumns.VENUE,
            target_attribute=RTS22Transaction.TRANSACTION_DETAILS_VENUE,
            auditor=self.auditor,
        )

    def _transmission_details_order_transmission_indicator(self) -> pd.DataFrame:
        """
        Populates from TransmissionOfOrderIndicator for Bonds Trades,
        fills any missing values with True
        """
        if self.file_type in [FileTypes.BONDS]:
            return map_conditional(
                source_frame=self.source_frame,
                target_attribute=RTS22Transaction.TRANSMISSION_DETAILS_ORDER_TRANSMISSION_INDICATOR,
                cases=[
                    Case(
                        query=f"`{SaxoBankColumns.TRANSMISSION_OF_ORDER_INDICATOR}`.notnull()",
                        attribute=SaxoBankColumns.TRANSMISSION_OF_ORDER_INDICATOR,
                    ),
                    Case(
                        query=f"`{SaxoBankColumns.TRANSMISSION_OF_ORDER_INDICATOR}`.isnull()",
                        value=True,
                    ),
                ],
            )
        else:
            return pd.DataFrame(
                data=True,
                index=self.source_frame.index,
                columns=[
                    RTS22Transaction.TRANSMISSION_DETAILS_ORDER_TRANSMISSION_INDICATOR
                ],
            )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """
        Populates from 'transactionDetails.tradingDateTime' and 'transactionDetails.buySellIndicator'

        Assumes _transaction_details_buy_sell_indicator()  and
        _transaction_details_trading_date_time() have run successfully
        """
        return TransactionRefNo.process(
            pd.concat(
                [
                    self.source_frame,
                    self.target_df.loc[
                        :,
                        [
                            RTS22Transaction.TRANSACTION_DETAILS_TRADING_DATE_TIME,
                            RTS22Transaction.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                        ],
                    ],
                ],
                axis=1,
            ),
            params=TransactionRefNoParams(
                trade_id_attribute=SaxoBankColumns.TRADE_NUMBER,
                trade_time_attribute=SaxoBankColumns.TRADE_TIME,
            ),
        )

    def _market_identifiers_instrument(self):
        """
        Creates InstrumentIdentifiers results

        Assumes _transaction_details_price_currency()
        and _instrument_details_instrument_derivative_expiry_date() have run successfully
        """

        instrument_identifier_columns = pd.DataFrame(index=self.source_frame.index)

        self.instr_ids_asset_class(instr_df=instrument_identifier_columns)
        instrument_identifier_columns.loc[
            :, InstrumentIDsColumns.CURRENCY
        ] = self.target_df.loc[:, RTS22Transaction.TRANSACTION_DETAILS_PRICE_CURRENCY]
        instrument_identifier_columns.loc[
            :, InstrumentIDsColumns.EXPIRY_DATE
        ] = self.pre_process_df.loc[:, TempColumns.EXPIRY_DATE]
        instrument_identifier_columns.loc[
            :, InstrumentIDsColumns.ISIN
        ] = self.pre_process_df.loc[:, TempColumns.ISIN]
        self.instr_ids_notional_ccy_2(instr_df=instrument_identifier_columns)
        self.instr_ids_option_strike_price(instr_df=instrument_identifier_columns)
        self.instr_ids_option_strike_type(instr_df=instrument_identifier_columns)
        self.instr_ids_underlying_index_name(instr_df=instrument_identifier_columns)
        self.instr_ids_underlying_index_term(instr_df=instrument_identifier_columns)
        self.instr_ids_underlying_isin(instr_df=instrument_identifier_columns)
        self.instr_ids_underlying_symbol(instr_df=instrument_identifier_columns)
        self.instr_ids_venue(instr_df=instrument_identifier_columns)

        self.target_df.loc[
            :, InstrumentIDsColumns.ASSET_CLASS
        ] = instrument_identifier_columns.loc[:, InstrumentIDsColumns.ASSET_CLASS]

        self.target_df.loc[
            :, InstrumentIDsColumns.CURRENCY
        ] = instrument_identifier_columns.loc[:, InstrumentIDsColumns.CURRENCY]

        self.target_df.loc[
            :, InstrumentIDsColumns.VENUE
        ] = instrument_identifier_columns.loc[:, InstrumentIDsColumns.VENUE]

        return InstrumentIdentifiers.process(
            instrument_identifier_columns,
            params=InstrumentIdentifiersParams(
                asset_class_attribute=InstrumentIDsColumns.ASSET_CLASS,
                currency_attribute=InstrumentIDsColumns.CURRENCY,
                expiry_date_attribute=InstrumentIDsColumns.EXPIRY_DATE,
                isin_attribute=InstrumentIDsColumns.ISIN,
                notional_currency_2_attribute=InstrumentIDsColumns.NOTIONAL_CURRENCY_2,
                option_strike_price_attribute=InstrumentIDsColumns.OPTION_STRIKE_PRICE,
                option_type_attribute=InstrumentIDsColumns.OPTION_STRIKE_TYPE,
                underlying_index_name_attribute=InstrumentIDsColumns.UNDERLYING_INDEX_NAME,
                underlying_index_term_attribute=InstrumentIDsColumns.UNDERLYING_INDEX_TERM,
                underlying_isin_attribute=InstrumentIDsColumns.UNDERLYING_ISIN,
                underlying_symbol_attribute=InstrumentIDsColumns.UNDERLYING_SYMBOL,
                venue_attribute=InstrumentIDsColumns.VENUE,
            ),
        )

    def _market_identifiers_parties(self):
        """
        Creates PartyIdentifiers results

        Assumes _transaction_details_buy_sell_indicator() has run successfully
        """
        party_identifier_columns = pd.DataFrame(index=self.source_frame.index)

        party_identifier_columns.loc[
            :, PartyIDsColumns.BUY_SELL_INDICATOR
        ] = self.target_df.loc[
            :, RTS22Transaction.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
        ]
        self.party_id_counterparty(parties_df=party_identifier_columns)
        self.party_id_trader(parties_df=party_identifier_columns)
        self.party_id_exec_entity(parties_df=party_identifier_columns)

        return PartyIdentifiers.process(
            party_identifier_columns,
            params=PartyIdentifiersParams(
                target_attribute=RTS22Transaction.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=PartyIDsColumns.EXEC_ENTITY,
                counterparty_identifier=PartyIDsColumns.COUNTERPARTY,
                buy_sell_side_attribute=PartyIDsColumns.BUY_SELL_INDICATOR,
                buyer_identifier=PartyIDsColumns.EXEC_ENTITY,
                seller_identifier=PartyIDsColumns.COUNTERPARTY,
                trader_identifier=PartyIDsColumns.TRADER,
                use_buy_mask_for_buyer_seller=True,
            ),
        )

    def _market_identifiers(self) -> pd.DataFrame:
        """
        Merges InstrumentIdentifiers and PartyIdentifiers results

        Assumes _market_identifiers_instrument() and _market_identifiers_parties()
        have run successfully
        """

        market_identifier_columns = pd.DataFrame(index=self.source_frame.index)

        market_identifier_columns.loc[
            :, RTS22Transaction.MARKET_IDENTIFIERS_INSTRUMENT
        ] = self.target_df.loc[:, RTS22Transaction.MARKET_IDENTIFIERS_INSTRUMENT]
        market_identifier_columns.loc[
            :, RTS22Transaction.MARKET_IDENTIFIERS_PARTIES
        ] = self.target_df.loc[:, RTS22Transaction.MARKET_IDENTIFIERS_PARTIES]

        return MergeMarketIdentifiers.process(
            market_identifier_columns,
            params=MergeMarketIdentifiersParams(
                identifiers_path=RTS22Transaction.MARKET_IDENTIFIERS,
                instrument_path=RTS22Transaction.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=RTS22Transaction.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _traders_algos_waivers_indicators_investment_decision_within_firm_file_identifier(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_execution_within_firm_file_identifier(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_id(self) -> pd.DataFrame:
        """Not Implemented"""

    # Pre-Process Functions
    def _instrument_details_instrument_derivative_expiry_date(self) -> pd.DataFrame:
        """
        Populated from ExpiryDate only for Futures and FXOptions
        """
        if self.file_type in [FileTypes.FX_OPTION, FileTypes.FUTURES]:
            return convert_date_time(
                source_frame=self.source_frame,
                source_attribute=SaxoBankColumns.EXPIRY_DATE,
                target_attribute=TempColumns.EXPIRY_DATE,
                convert_to=ConvertTo.DATE.value,
                source_attribute_format="%Y%m%d",
            )
        else:
            # column needed downstream in InstrumentOverridesExpiryDate
            return pd.DataFrame(
                data=pd.NA,
                index=self.source_frame.index,
                columns=[TempColumns.EXPIRY_DATE],
            )

    def _instr_ids_isin(self):
        """
        Assigns ISIN from  InstrumentISINCode, ISIN or ISINCode according to file type
        """
        if self.file_type in [FileTypes.FX, FileTypes.FX_OPTION, FileTypes.FUTURES]:
            return map_attribute(
                source_frame=self.source_frame,
                source_attribute=SaxoBankColumns.INSTRUMENT_ISIN_CODE,
                target_attribute=TempColumns.ISIN,
                auditor=self.auditor,
            )
        elif self.file_type in [FileTypes.BONDS]:
            return map_attribute(
                source_frame=self.source_frame,
                source_attribute=SaxoBankColumns.ISIN,
                target_attribute=TempColumns.ISIN,
                auditor=self.auditor,
            )
        elif self.file_type in [FileTypes.CFD, FileTypes.MUTUAL_FUNDS, FileTypes.SHARE]:
            return map_attribute(
                source_frame=self.source_frame,
                source_attribute=SaxoBankColumns.ISIN_CODE,
                target_attribute=TempColumns.ISIN,
                auditor=self.auditor,
            )
        elif self.file_type in [FileTypes.SP]:
            return map_conditional(
                source_frame=self.source_frame,
                cases=[
                    Case(
                        query=f"`{SaxoBankColumns.ISIN_CODE}`.isnull()",
                        attribute=SaxoBankColumns.ISIN_CODE,
                    ),
                    Case(
                        query=f"`{SaxoBankColumns.ISIN_CODE}`.notnull()",
                        attribute=SaxoBankColumns.INSTRUMENT_ISIN_CODE,
                    ),
                ],
                target_attribute=TempColumns.ISIN,
            )
        else:
            return map_attribute(
                source_frame=self.source_frame,
                source_attribute=SaxoBankColumns.INSTRUMENT_CODE,
                target_attribute=TempColumns.ISIN,
                auditor=self.auditor,
            )

    def _temp_venue(self) -> pd.DataFrame:
        """
        Populates TempColumns.VENUE to use for:
            RTS22Transaction.TRANSACTION_DETAILS_VENUE
            RTS22Transaction.TRANSACTION_DETAILS_ULTIMATE_VENUE
        """
        return map_conditional(
            source_frame=self.pre_process_df,
            cases=[
                Case(query=f"{TempColumns.ISIN}.notnull()", value="XOFF"),
                Case(query=f"{TempColumns.ISIN}.isnull()", value="XXXX"),
            ],
            target_attribute=TempColumns.VENUE,
        )

    # Auxiliary Functions
    def instr_ids_asset_class(self, instr_df: pd.DataFrame):
        """
        Asset class, assigned according to the type fo file
        :param instr_df: dataframe of instrument identifier fields
        """
        if self.file_type in [FileTypes.BONDS]:
            instr_df.loc[:, InstrumentIDsColumns.ASSET_CLASS] = pd.Series(
                data=AssetClass.BOND, index=self.source_frame.index
            )
        elif self.file_type in [FileTypes.CFD]:
            instr_df.loc[:, InstrumentIDsColumns.ASSET_CLASS] = pd.Series(
                data=AssetClass.CFD, index=self.source_frame.index
            )
        elif self.file_type in [FileTypes.FUTURES]:
            instr_df.loc[:, InstrumentIDsColumns.ASSET_CLASS] = pd.Series(
                data=AssetClass.FUTURE, index=self.source_frame.index
            )
        elif self.file_type in [FileTypes.FX_OPTION]:
            instr_df.loc[:, InstrumentIDsColumns.ASSET_CLASS] = pd.Series(
                data=AssetClass.FX_OPTION, index=self.source_frame.index
            )
        elif self.file_type in [FileTypes.FX]:
            instr_df.loc[:, InstrumentIDsColumns.ASSET_CLASS] = map_conditional(
                source_frame=self.source_frame,
                cases=[
                    Case(
                        query=f"`{SaxoBankColumns.FXTYPE}`.isin(['Spot'])",
                        value=AssetClass.FX_SPOT,
                    ),
                    Case(
                        query=f"`{SaxoBankColumns.FXTYPE}`.isin(['Forward'])",
                        value=AssetClass.FX_FORWARD,
                    ),
                ],
                target_attribute=InstrumentIDsColumns.ASSET_CLASS,
            )[InstrumentIDsColumns.ASSET_CLASS]
        else:
            instr_df.loc[:, InstrumentIDsColumns.ASSET_CLASS] = pd.Series(
                data=pd.NA, index=self.source_frame.index
            )

    def instr_ids_notional_ccy_2(self, instr_df: pd.DataFrame):
        """
        Populated from InstrumentCurrency
        :param instr_df: dataframe of instrument identifier fields
        """
        instr_df.loc[:, InstrumentIDsColumns.NOTIONAL_CURRENCY_2] = map_attribute(
            source_frame=self.target_df,
            source_attribute=RTS22Transaction.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
            target_attribute=InstrumentIDsColumns.NOTIONAL_CURRENCY_2,
            end_index=3,
            auditor=self.auditor,
        )[InstrumentIDsColumns.NOTIONAL_CURRENCY_2]

    def instr_ids_option_strike_price(self, instr_df: pd.DataFrame):
        """
        Populated from Strike only for FXOptions
        :param instr_df: dataframe of instrument identifier fields
        """
        if self.file_type in [FileTypes.FX_OPTION]:
            instr_df.loc[
                :, InstrumentIDsColumns.OPTION_STRIKE_PRICE
            ] = convert_minor_to_major_price(
                source_frame=self.source_frame,
                source_price_attr=SaxoBankColumns.STRIKE,
                source_ccy_attr=SaxoBankColumns.INSTRUMENT_CURRENCY,
                target_price_attr=InstrumentIDsColumns.OPTION_STRIKE_PRICE,
            )[
                InstrumentIDsColumns.OPTION_STRIKE_PRICE
            ]
        else:
            instr_df.loc[:, InstrumentIDsColumns.OPTION_STRIKE_PRICE] = pd.Series(
                data=pd.NA, index=self.source_frame.index
            )

    def instr_ids_option_strike_type(self, instr_df: pd.DataFrame):
        """
        Populated from CallPut only for FXOptions
        :param instr_df: dataframe of instrument identifier fields
        """
        if self.file_type in [FileTypes.FX_OPTION]:
            instr_df.loc[:, InstrumentIDsColumns.OPTION_STRIKE_TYPE] = map_attribute(
                source_frame=self.source_frame,
                source_attribute=SaxoBankColumns.CALL_PUT,
                target_attribute=InstrumentIDsColumns.OPTION_STRIKE_TYPE,
                auditor=self.auditor,
            )[InstrumentIDsColumns.OPTION_STRIKE_TYPE]
        else:
            instr_df.loc[:, InstrumentIDsColumns.OPTION_STRIKE_TYPE] = pd.Series(
                data=pd.NA, index=self.source_frame.index
            )

    def instr_ids_underlying_index_name(self, instr_df: pd.DataFrame):
        """
        Populated from UnderlyingInstrumentCode for SP, Mutual and Futures Trades
        :param instr_df: dataframe of instrument identifier fields
        """
        if self.file_type in [FileTypes.FUTURES, FileTypes.MUTUAL_FUNDS, FileTypes.SP]:
            instr_df.loc[:, InstrumentIDsColumns.UNDERLYING_INDEX_NAME] = map_attribute(
                source_frame=self.source_frame,
                source_attribute=SaxoBankColumns.UNDERLYING_INSTRUMENT_CODE,
                target_attribute=InstrumentIDsColumns.UNDERLYING_INDEX_NAME,
                auditor=self.auditor,
            )[InstrumentIDsColumns.UNDERLYING_INDEX_NAME]
        else:
            instr_df.loc[:, InstrumentIDsColumns.UNDERLYING_INDEX_NAME] = pd.Series(
                data=pd.NA, index=self.source_frame.index
            )

    def instr_ids_underlying_index_term(self, instr_df: pd.DataFrame):
        """
        Populated from TermOfUnderlyingIndex only for Futures
        :param instr_df: dataframe of instrument identifier fields
        """
        if self.file_type in [FileTypes.FUTURES]:
            instr_df.loc[:, InstrumentIDsColumns.UNDERLYING_INDEX_TERM] = map_attribute(
                source_frame=self.source_frame,
                source_attribute=SaxoBankColumns.TERM_OF_UNDERLYING_INDEX,
                target_attribute=InstrumentIDsColumns.UNDERLYING_INDEX_TERM,
                auditor=self.auditor,
            )[InstrumentIDsColumns.UNDERLYING_INDEX_TERM]
        else:
            instr_df.loc[:, InstrumentIDsColumns.UNDERLYING_INDEX_TERM] = pd.Series(
                data=pd.NA, index=self.source_frame.index
            )

    def instr_ids_underlying_isin(self, instr_df: pd.DataFrame):
        """
        Populated from UnderlyingInstrumentISINCode only for SP Trades
        :param instr_df: dataframe of instrument identifier fields
        """
        if self.file_type in [FileTypes.SP]:
            instr_df.loc[:, InstrumentIDsColumns.UNDERLYING_ISIN] = map_attribute(
                source_frame=self.source_frame,
                source_attribute=SaxoBankColumns.UNDERLYING_INSTRUMENT_ISIN_CODE,
                target_attribute=InstrumentIDsColumns.UNDERLYING_ISIN,
                auditor=self.auditor,
            )[InstrumentIDsColumns.UNDERLYING_ISIN]
        else:
            instr_df.loc[:, InstrumentIDsColumns.UNDERLYING_ISIN] = pd.Series(
                data=pd.NA, index=self.source_frame.index
            )

    def instr_ids_underlying_symbol(self, instr_df: pd.DataFrame):
        """
        Populated from InstrumentCode for Futures and CFDs with the respective logic
        :param instr_df: dataframe of instrument identifier fields
        """
        if self.file_type in [FileTypes.FUTURES]:
            instr_df.loc[:, InstrumentIDsColumns.UNDERLYING_SYMBOL] = map_attribute(
                source_frame=self.source_frame,
                source_attribute=SaxoBankColumns.INSTRUMENT_CODE,
                target_attribute=InstrumentIDsColumns.UNDERLYING_SYMBOL,
                end_index=-2,
                auditor=self.auditor,
            )[InstrumentIDsColumns.UNDERLYING_SYMBOL]
        elif self.file_type in [FileTypes.CFD]:
            instr_df.loc[:, InstrumentIDsColumns.UNDERLYING_SYMBOL] = map_value(
                source_frame=self.source_frame,
                source_attribute=SaxoBankColumns.INSTRUMENT_CODE,
                target_attribute=InstrumentIDsColumns.UNDERLYING_SYMBOL,
                regex_replace_map=[RegexReplaceMap(regex=r"\..*$", drop=True)],
                regex_replace_only=True,
                auditor=self.auditor,
            )[InstrumentIDsColumns.UNDERLYING_SYMBOL]
        else:
            instr_df.loc[:, InstrumentIDsColumns.UNDERLYING_SYMBOL] = pd.Series(
                data=pd.NA, index=self.source_frame.index
            )

    def instr_ids_venue(self, instr_df: pd.DataFrame):
        """
        Populated from ISOMic for Futures and CFDs
        :param instr_df: dataframe of instrument identifier fields
        """
        if self.file_type in [FileTypes.FUTURES, FileTypes.CFD]:
            instr_df.loc[:, InstrumentIDsColumns.VENUE] = map_attribute(
                source_frame=self.source_frame,
                source_attribute=SaxoBankColumns.ISOMic,
                target_attribute=InstrumentIDsColumns.VENUE,
                auditor=self.auditor,
            )[InstrumentIDsColumns.VENUE]
        else:
            instr_df.loc[:, InstrumentIDsColumns.VENUE] = pd.Series(
                data=pd.NA, index=self.source_frame.index
            )

    def party_id_counterparty(self, parties_df: pd.DataFrame):
        """
        Populated from CounterpartID
        :param parties_df: dataframe of party identifiers fields
        """

        counterparty = map_attribute(
            source_frame=self.source_frame,
            source_attribute=SaxoBankColumns.COUNTERPARTY_ID,
            target_attribute=PartyIDsColumns.COUNTERPARTY,
            auditor=self.auditor,
        )

        parties_df.loc[:, PartyIDsColumns.COUNTERPARTY] = add_id_or_lei(
            counterparty, PartyIDsColumns.COUNTERPARTY
        )

    def party_id_trader(self, parties_df: pd.DataFrame):
        """
        Populated from UserIdOnTrade
        :param parties_df: dataframe of party identifiers fields
        """
        trader = map_attribute(
            source_frame=self.source_frame,
            source_attribute=SaxoBankColumns.USER_ID_ON_TRADE,
            target_attribute=PartyIDsColumns.TRADER,
            auditor=self.auditor,
        )

        parties_df.loc[:, PartyIDsColumns.TRADER] = add_id_or_lei(
            trader, PartyIDsColumns.TRADER
        )

    def party_id_exec_entity(self, parties_df: pd.DataFrame):
        """
        Populated from ExecutingEntityIdentificationCode
        :param parties_df: dataframe of party identifiers fields
        """
        exec_entity = map_attribute(
            source_frame=self.source_frame,
            source_attribute=SaxoBankColumns.EXECUTING_ENTITY_IDENTIFICATION_CODE,
            target_attribute=PartyIDsColumns.EXEC_ENTITY,
            auditor=self.auditor,
        )

        parties_df.loc[:, PartyIDsColumns.COUNTERPARTY] = add_id_or_lei(
            exec_entity, PartyIDsColumns.EXEC_ENTITY
        )

    @staticmethod
    def get_file_type(file_name: str) -> str:
        """
        Returns the adequate file_type
        :param file_name: name of the file
        :return: file_type
        """
        for file_type in [
            v for k, v in FileTypes.__dict__.items() if isinstance(v, str)
        ]:
            if file_name.startswith(file_type):
                return file_type


# Task Methods
def convert_date_time(
    source_frame: pd.DataFrame,
    source_attribute: str,
    target_attribute: str,
    convert_to: str = None,
    source_attribute_format: str = None,
) -> pd.DataFrame:
    """
    Aux function to call ConvertDateTime
    """
    return ConvertDatetime.process(
        source_frame=source_frame,
        params=ConvertDatetimeParams(
            source_attribute=source_attribute,
            target_attribute=target_attribute,
            convert_to=convert_to,
            source_attribute_format=source_attribute_format,
        ),
    )


def convert_minor_to_major_ccy(
    source_frame: pd.DataFrame,
    source_ccy_attr: str,
    target_ccy_attr: str,
) -> pd.DataFrame:
    """
    Aux function to call ConvertMinorToMajor for a currency
    """
    return ConvertMinorToMajor.process(
        source_frame=source_frame,
        params=ConvertMinorToMajorParams(
            source_ccy_attribute=source_ccy_attr,
            target_ccy_attribute=target_ccy_attr,
        ),
    )


def convert_minor_to_major_price(
    source_frame: pd.DataFrame,
    source_price_attr: str,
    source_ccy_attr: str,
    target_price_attr: str,
    cast_to=ConvertMinorToMajorCastTo.ABS.value,
) -> pd.DataFrame:
    """
    Aux function to call ConvertMinorToMajor for a price
    """
    return ConvertMinorToMajor.process(
        source_frame=source_frame,
        params=ConvertMinorToMajorParams(
            source_price_attribute=source_price_attr,
            source_ccy_attribute=source_ccy_attr,
            target_price_attribute=target_price_attr,
            cast_to=cast_to,
        ),
    )


def map_attribute(
    source_frame,
    source_attribute,
    target_attribute,
    auditor: Auditor,
    prefix=None,
    end_index=None,
    cast_to=None,
) -> pd.DataFrame:
    """
    Aux function to call MapAttribute
    """
    return MapAttribute.process(
        source_frame=source_frame,
        params=MapAttributeParams(
            source_attribute=source_attribute,
            target_attribute=target_attribute,
            prefix=prefix,
            end_index=end_index,
            cast_to=cast_to,
        ),
        auditor=auditor,
    )


def map_conditional(
    source_frame: pd.DataFrame, cases: list, target_attribute: str
) -> pd.DataFrame:
    """
    Aux function to call MapConditional
    """
    return MapConditional.process(
        source_frame=source_frame,
        params=MapConditionalParams(target_attribute=target_attribute, cases=cases),
    )


def map_static(
    source_frame: pd.DataFrame,
    target_attribute: str,
    target_value: Union[str, bool] = None,
    from_index: bool = False,
) -> pd.DataFrame:
    """
    Aux function to call MapStatic
    """
    return MapStatic.process(
        source_frame=source_frame,
        params=MapStaticParams(
            target_attribute=target_attribute,
            target_value=target_value,
            from_index=from_index,
        ),
    )


def map_value(
    source_frame: pd.DataFrame,
    source_attribute: str,
    target_attribute: str,
    auditor: Auditor,
    value_map: dict = None,
    default_value=None,
    case_insensitive=False,
    regex_replace_map=None,
    regex_replace_only=False,
) -> pd.DataFrame:
    """
    Aux function to call MapValue
    """
    return MapValue.process(
        source_frame=source_frame,
        params=MapValueParams(
            source_attribute=source_attribute,
            target_attribute=target_attribute,
            value_map=value_map,
            default_value=default_value,
            case_insensitive=case_insensitive,
            regex_replace_map=regex_replace_map,
            regex_replace_only=regex_replace_only,
        ),
        auditor=auditor,
    )
