import pandas as pd
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.steeleye.tr.feed.saxo_bank.saxo_bank_transformations import (
    PartyIDsColumns,
)
from swarm_tasks.steeleye.tr.feed.saxo_bank.saxo_bank_transformations import (
    SaxoBankTransformations,
)
from swarm_tasks.steeleye.tr.static import RTS22Transaction
from swarm_tasks.transform.steeleye.tr.identifiers.party_identifiers import (
    Params as PartyIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.identifiers.party_identifiers import (
    PartyIdentifiers,
)


class ThornbridgePartyIDs:
    THORNBRIDGE = "2138008NEORY2DQV4R24"
    SAXO = "549300TL5406IC1XKD09"
    CROSSROADS = "254900LFCDLGD2NHGM50"
    LARS_WIND = "Lars Wind"


class SaxoBankThornbridgeTransformations(SaxoBankTransformations):
    """
    Thornbridge overrides for SaxoBank transformations
    Changes PartyIdentifiers
    """

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """
        Creates PartyIdentifiers results

        Assumes _transaction_details_buy_sell_indicator() has run successfully
        """
        party_identifier_columns = pd.DataFrame(index=self.source_frame.index)

        party_identifier_columns.loc[
            :, PartyIDsColumns.BUY_SELL_INDICATOR
        ] = self.target_df.loc[
            :, RTS22Transaction.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
        ]

        party_identifier_columns.loc[:, PartyIDsColumns.EXEC_ENTITY] = (
            PartyPrefix.LEI + ThornbridgePartyIDs.THORNBRIDGE
        )

        party_identifier_columns.loc[:, PartyIDsColumns.COUNTERPARTY] = (
            PartyPrefix.LEI + ThornbridgePartyIDs.SAXO
        )

        party_identifier_columns.loc[
            :, PartyIDsColumns.INVESTMENT_DECISION_WITHIN_FIRM
        ] = (PartyPrefix.ID + ThornbridgePartyIDs.LARS_WIND)

        party_identifier_columns.loc[:, PartyIDsColumns.EXECUTION_WITHIN_FIRM] = (
            PartyPrefix.ID + ThornbridgePartyIDs.LARS_WIND
        )

        party_identifier_columns.loc[:, PartyIDsColumns.BUYER] = (
            PartyPrefix.LEI + ThornbridgePartyIDs.CROSSROADS
        )

        party_identifier_columns.loc[:, PartyIDsColumns.SELLER] = (
            PartyPrefix.LEI + ThornbridgePartyIDs.SAXO
        )

        self.party_id_trader(parties_df=party_identifier_columns)

        return PartyIdentifiers.process(
            party_identifier_columns,
            params=PartyIdentifiersParams(
                target_attribute=RTS22Transaction.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=PartyIDsColumns.EXEC_ENTITY,
                counterparty_identifier=PartyIDsColumns.COUNTERPARTY,
                investment_decision_within_firm_identifier=PartyIDsColumns.INVESTMENT_DECISION_WITHIN_FIRM,
                execution_within_firm_identifier=PartyIDsColumns.EXECUTION_WITHIN_FIRM,
                buy_sell_side_attribute=PartyIDsColumns.BUY_SELL_INDICATOR,
                buyer_identifier=PartyIDsColumns.BUYER,
                seller_identifier=PartyIDsColumns.SELLER,
                use_buy_mask_for_buyer_seller=True,
                buyer_decision_maker_identifier=PartyIDsColumns.EXEC_ENTITY,
                use_buy_mask_for_buyer_seller_decision_maker=True,
            ),
        )
