from enum import Enum


class ExecutionSourceColumns:
    BUYER_IDENTIFICATION_CODE = "BUYERIDENTIFICATIONCODE"
    CURRENCY = "CURRENCY"
    DESCRIPTION = "DESCRIPTION"
    EVENT_TYPE = "EVENTTYPE"
    EXCHANGE_MIC_CODE = "EXCHANGEMICCODE"
    EXEC_ID = "EXECID"
    EXECUTION_DATE = "EXECUTIONDATE"
    EXECUTION_ENTITY_IDENTIFICATION_CODE = "EXECUTIONENTITYIDENTIFICATIONCODE"
    EXECUTION_TIME = "EXECUTIONTIME"
    FUTURE_BLOOMBERG_ROOT = "FUTUREBLOOMBERGROOT"
    FUTURE_EXPIRATION_DATE = "FUTUREEXPIRATIONDATE"
    INSTRUMENT_CLASSIFICATION = "INSTRUMENTCLASSIFICATION"
    INSTRUMENT_IDENTIFICATION_CODE = "INSTRUMENTIDENTIFICATIONCODE"
    ISIN = "ISIN"
    LAST_PX = "LASTPX"
    LAST_QTY = "LASTQTY"
    NOTIONAL_CURRENCY_2 = "NOTIONALCURRENCY2"
    OPTION_CONTRACT_BBG_ROOT_CODE = "OPTIONCONTRACTBLOOMBERGROOTCODE"
    OPTION_EXPIRATION_DATE = "OPTIONEXPIRATIONDATE"
    OPTION_STRIKE = "OPTIONSTRIKE"
    OPTION_TYPE = "OPTIONTYPE"
    ORDER_DATE = "ORDERDATE"
    ORDER_EXECUTION_DESTINATION = "ORDEREXECUTIONDESTINATION"
    ORDER_ID = "ORDERID"
    ORDER_SIDE = "ORDERSIDE"
    ORDER_TOTAL_QUANTITY = "ORDERTOTALQUANTITY"
    PRICE = "PRICE"
    QUANTITY = "QUANTITY"
    REPORT_STATUS = "REPORTSTATUS"
    SELLER_IDENTIFICATION_CODE = "SELLERIDENTIFICATIONCODE"
    SHORT_SELLING_INDICATOR = "SHORTSELLINGINDICATOR"
    STRIKE_PRICE_TYPE = "STRIKEPRICETYPE"
    TERM_OF_UNDERLYING_INDEX = "TERMOFUNDERLYINGINDEX-VALUE"
    TICKER = "TICKER"
    TRADER = "TRADER"
    TRADING_DATE_TIME = "TRADINGDATETIME"
    TRANSACTION_REFERENCE_NUMBER = "TRANSACTIONREFERENCENUMBER"
    TRANSACTION_TYPE = "TRANSACTIONTYPE"
    UNDERLYING_INDEX_NAME = "UNDERLYINGINDEXNAME"
    UNDERLYING_INSTRUMENT_CODE = "UNDERLYINGINSTRUMENTCODE"
    VENUE = "VENUE"


class AllocationSourceColumns:
    BUYER_DECISION_MAKER_CODE = "BUYERDECISIONMAKERCODE"
    COMMODITY_DERIVATIVE_INDICATOR = "COMMODITYDERIVATIVEINDICATOR"
    COMPLEX_TRADE_COMPONENT_ID = "COMPLEXTRADECOMPONENTID"
    COUNTERPARTY = "COUNTERPARTY"
    CURRENCY_2 = "CURRENCY2"
    EXCHANGE_MIC_CODE = "EXCHANGEMICCODE_ALLOCATIONS"
    EXECUTION_WITHIN_FIRM = "EXECUTIONWITHINFIRM"
    EXPIRY_DATE = "EXPIRYDATE"
    FUTURE_EXPIRATION_DATE = "FUTUREEXPIRATIONDATE_ALLOCATIONS"
    INSTRUMENT_CLASSIFICATION = "INSTRUMENTCLASSIFICATION_ALLOCATIONS"
    INSTRUMENT_FULL_NAME = "INSTRUMENTFULLNAME"
    NET_AMOUNT = "NETAMOUNT"
    OPTION_TYPE = "OPTIONTYPE_ALLOCATIONS"
    ORDER_SIDE = "ORDERSIDE_ALLOCATIONS"
    OTC_POST_TRADE_INDICATOR = "OTCPOST-TRADEINDICATOR"
    PORTFOLIO_MANAGER = "PORTFOLIOMANAGER"
    PRICE_CURRENCY = "PRICECURRENCY"
    PRICE_TYPE = "PRICE-TYPE"
    QUANTITY_CURRENCY = "QUANTITYCURRENCY"
    QUANTITY_TYPE = "QUANTITYTYPE_ALLOCATIONS"
    SECURITIES_FINANCING_TRANSACTION_INDICATOR = (
        "SECURITIESFINANCINGTRANSACTIONINDICATOR"
    )
    SELLER_DECISION_MAKER_CODE = "SELLERDECISIONMAKERCODE"
    TRADING_CAPACITY = "TRADINGCAPACITY"
    TRADING_VENUE_TRANSACTION_IDENTIFICATION_CODE = (
        "TRADINGVENUETRANSACTIONIDENTIFICATIONCODE"
    )
    TRANSMISSION_OF_ORDER_INDICATOR = "TRANSMISSIONOFORDERINDICATOR"
    VENUE = "VENUE"


class TempColumns:
    ASSET_CLASS = "__asset_class__"
    BUY_SELL_INDICATOR = "__buy_sell_indicator__"
    BUYER = "__buyer__"
    BUYER_DECISION_MAKER = "__buyer_decision_maker__"
    COUNTERPARTY = "__counterparty__"
    DESCRIPTION_CURRENCY_1 = "__description_currency_1__"
    DESCRIPTION_CURRENCY_2 = "__description_currency_2__"
    DESCRIPTION_DATE = "__description_date__"
    EXECUTING_ENTITY = "__temp_executing_entity__"
    EXECUTING_ENTITY_WITHOUT_PREFIX = "__temp_executing_entity_without_prefix__"
    EXECUTION_WITHIN_FIRM = "__execution_within_firm__"
    EXPIRY_DATE = "__expiry_date__"
    INVESTMENT_DECISION_MAKER = "__investment_decision_maker__"
    INVESTMENT_DECISION_MAKER_IF_NOT_DEAL = "__investment_decision_maker_if_not_deal__"
    ISIN = "__isin__"
    NOTIONAL_CURRENCY_1 = "__notional_currency_1"
    NOTIONAL_CURRENCY_2 = "__notional_currency_2"
    OPTION_STRIKE_PRICE = "__option_strike_price__"
    OPTION_STRIKE_PRICE_TYPE = "__option_strike_price_type__"
    SELLER = "__seller__"
    SELLER_DECISION_MAKER = "__seller_decision_maker__"
    TEMP_COL_1 = "__temp_col_1__"
    TEMP_COL_2 = "__temp_col_2__"
    TRADER = "__trader__"
    TRADING_DATE_TIME = "__trading_date_time__"
    TRIGGER_FILE_NAME = "__TRIGGER_FILE_NAME__"
    UNDERLYING_SYMBOL = "__underlying_symbol__"


class FileTypes(Enum):
    ALLOCATIONS: str = "Allocations"
    EXECUTIONS: str = "Executions"


class DateFormats:
    EXECUTION_DATE_TIME = "%Y-%m-%dT%H:%M:%S.%f"
    EXPIRY_DATE = "%Y-%m-%d"
