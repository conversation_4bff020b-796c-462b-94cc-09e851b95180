import pandas as pd
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_attribute import CastTo
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import ReportStatus
from se_elastic_schema.static.mifid2 import ShortSellingIndicator
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_elastic_schema.static.reference import StrikePriceType
from se_trades_tasks.abstractions.abstract_rts22_transaction_transformations import (
    AbstractRTS22TransactionsTransformations,
)
from se_trades_tasks.order_and_tr.party.utils import add_id_or_lei
from se_trades_tasks.order_and_tr.static import AssetClass
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ConvertMinorToMajorParams,
)
from swarm_tasks.steeleye.tr.feed.enfusion.v2.static import AllocationSourceColumns
from swarm_tasks.steeleye.tr.feed.enfusion.v2.static import DateFormats
from swarm_tasks.steeleye.tr.feed.enfusion.v2.static import ExecutionSourceColumns
from swarm_tasks.steeleye.tr.feed.enfusion.v2.static import TempColumns
from swarm_tasks.steeleye.tr.static import RTS22Transaction
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ConvertDatetimeParams,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as MapAttributeParams
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as MapConditionalParams
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as MapValueParams
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as MergeMarketIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as InstrumentIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.identifiers.party_identifiers import (
    Params as PartyIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.identifiers.party_identifiers import (
    PartyIdentifiers,
)
from swarm_tasks.utilities.data_util import check_currency
from swarm_tasks.utilities.static import Delimiters


class EnfusionV2Transformations(AbstractRTS22TransactionsTransformations):
    """
    Primary Transformations for Enfusion V2 TR
    """

    def process(self) -> pd.DataFrame:
        self.pre_process()

        self.data_source_name()
        self.date()
        self.meta_model()
        self.report_details_report_status()
        self.source_index()
        self.source_key()
        self.traders_algos_waivers_indicators_commodity_derivative_indicator()
        self.traders_algos_waivers_indicators_otc_post_trade_indicator()
        self.traders_algos_waivers_indicators_securities_financing_txn_indicator()
        self.traders_algos_waivers_indicators_short_selling_indicator()
        self.transaction_details_buy_sell_indicator()
        self.transaction_details_complex_trade_component_id()
        self.transaction_details_net_amount()
        self.transaction_details_price()
        self.transaction_details_price_currency()
        self.transaction_details_price_notation()
        self.transaction_details_quantity()
        self.transaction_details_quantity_currency()
        self.transaction_details_quantity_notation()
        self.transaction_details_trading_capacity()
        self.transaction_details_trading_date_time()
        self.transaction_details_ultimate_venue()
        self.transaction_details_venue()
        self.transmission_details_order_transmission_indicator()

        self.report_details_transaction_ref_no()
        self.report_details_trading_venue_transaction_id_code()

        self.market_identifiers_instrument()
        self.market_identifiers_parties()
        self.market_identifiers()

        self.post_process()
        return self.target_df

    def _pre_process(self):
        """
        Creates temporary column expiry date, used later in InstrumentIdentifiers
        and downstream in InstrumentOverrideExpiryDate
        """
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_trading_date_time(),
                self._temp_executing_entity(),
                self._temp_party_ids_trader(),
                self._temp_buy_sell_indicator(),
                self._temp_instr_ids_asset_class(),
                self._temp_instr_ids_option_strike_price(),
            ],
            axis=1,
        )

        # these columns need access to others defined above
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_description_currencies(),  # Needs asset class
                self._temp_instr_ids_expiry_date(),  # Needs asset class
                self._temp_instr_ids_underlying_symbol(),  # Needs asset class
                self._temp_party_ids_investment_decision_maker(),  # needs trader for thornbridge
                self._temp_party_ids_buyer(),  # Needs buy_sell_indicator and executing entity
                self._temp_party_ids_seller(),  # Needs buy_sell_indicator and executing entity
            ],
            axis=1,
        )

        # these columns need access to others defined above
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_instr_ids_currency(),  # Needs asset class and description currencies
                self._temp_party_ids_counterparty(),  # Needs buy_sell_indicator
                self._temp_party_ids_buyer_decision_maker(),  # Needs buyer; executing entity and buysell for thornbridge
                self._temp_party_ids_seller_decision_maker(),  # Needs seller; executing entity and buysell for thornbridge
            ],
            axis=1,
        )

    def _post_process(self):
        """
        Adds temp columns required downstream in the bundle to target_df
        """

        self.target_df = pd.concat(
            [
                self.target_df,
                pd.DataFrame(
                    data=StrikePriceType.MNTRYVAL.value,
                    index=self.source_frame.index,
                    columns=[TempColumns.OPTION_STRIKE_PRICE_TYPE],
                ),
                self.pre_process_df.loc[:, TempColumns.ASSET_CLASS],
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        """
        Static value: Enfusion
        """
        return pd.DataFrame(
            data="Enfusion",
            index=self.source_frame.index,
            columns=[RTS22Transaction.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.EXECUTION_DATE
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=ExecutionSourceColumns.EXECUTION_DATE,
                source_attribute_format=DateFormats.EXECUTION_DATE_TIME,
                target_attribute=RTS22Transaction.DATE,
                convert_to=ConvertTo.DATE,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """
        Static value: RTS22Transaction
        """
        return pd.DataFrame(
            data="RTS22Transaction",
            index=self.source_frame.index,
            columns=[RTS22Transaction.META_MODEL],
        )

    def _report_details_investment_firm_covered_directive(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _report_details_report_status(self) -> pd.DataFrame:
        """
        Maps from ExecutionSourceColumns.EVENT_TYPE or assigns Static value 'NEWT'
        if ExecutionSourceColumns.EVENT_TYPE is empty.
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=ExecutionSourceColumns.EVENT_TYPE,
                target_attribute=RTS22Transaction.REPORT_DETAILS_REPORT_STATUS,
                case_insensitive=True,
                value_map={
                    "New": ReportStatus.NEWT.value,
                    "Cancel": ReportStatus.CANC.value,
                },
            ),
            auditor=self.auditor,
        ).fillna(ReportStatus.NEWT.value)

    def _report_details_trading_venue_transaction_id_code(self):
        """
        Populates from AllocationSourceColumns.TRADING_VENUE_TRANSACTION_IDENTIFICATION_CODE
        or RTS22Transaction.REPORT_DETAILS_TRANSACTION_REF_NO.
        """
        return MapConditional.process(
            source_frame=pd.concat(
                [
                    self.source_frame.loc[
                        :,
                        [
                            ExecutionSourceColumns.EXEC_ID,
                            AllocationSourceColumns.TRADING_VENUE_TRANSACTION_IDENTIFICATION_CODE,
                        ],
                    ],
                    self.target_df.loc[
                        :,
                        [
                            RTS22Transaction.REPORT_DETAILS_TRANSACTION_REF_NO,
                            RTS22Transaction.TRANSACTION_DETAILS_VENUE,
                        ],
                    ],
                ],
                axis=1,
            ),
            params=MapConditionalParams(
                target_attribute=RTS22Transaction.REPORT_DETAILS_TRADING_VENUE_TRANSACTION_ID_CODE,
                cases=[
                    Case(
                        query=f"`{ExecutionSourceColumns.EXEC_ID}`.notnull()",
                        attribute=RTS22Transaction.REPORT_DETAILS_TRANSACTION_REF_NO,
                    ),
                    Case(
                        query=f"`{ExecutionSourceColumns.EXEC_ID}`.isnull()",
                        attribute=AllocationSourceColumns.TRADING_VENUE_TRANSACTION_IDENTIFICATION_CODE,
                    ),
                    Case(
                        query=f"`{RTS22Transaction.TRANSACTION_DETAILS_VENUE}`.str.fullmatch('XOFF|XXXX', case=False, na=False)",
                        as_empty=True,
                    ),
                ],
            ),
        )

    def _source_index(self) -> pd.DataFrame:
        """
        Static value: index from the source_frame
        """
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[RTS22Transaction.SOURCE_INDEX],
        )

    def _source_key(self) -> pd.DataFrame:
        """
        Static value: File URL
        """
        return pd.DataFrame(
            data=self.source_frame[TempColumns.TRIGGER_FILE_NAME].values,
            index=self.source_frame.index,
            columns=[RTS22Transaction.SOURCE_KEY],
        )

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(self):
        """
        Not Implemented
        """

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(self):
        """
        Populates from ExecutionSourceColumns.OTC_POST_TRADE_INDICATOR
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=AllocationSourceColumns.OTC_POST_TRADE_INDICATOR,
                target_attribute=RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_OTC_POST_TRADE_IND,
                cast_to=CastTo.STRING_LIST.value,
                list_delimiter=Delimiters.SEMI_COLON,
            ),
            auditor=self.auditor,
        )

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(self):
        """
        Populates from AllocationSourceColumns.SECURITIES_FINANCING_TRANSACTION_INDICATOR
        """
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, AllocationSourceColumns.SECURITIES_FINANCING_TRANSACTION_INDICATOR
            ].values,
            index=self.source_frame.index,
            columns=[
                RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_SECRT_FNC_TXN_IND
            ],
        )

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """
        Populates from ExecutionSourceColumns.ORDER_SIDE. Sets the column to null
        if instrument classification does NOT follow the pattern "^[CDE][A-Z]{5}$
        (same pattern as SE_DV_300)
        """
        c_d_e_instrument_classification_pattern = "^[CDE][A-Z]{5}$"
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_SHORT_SELLING_INDICATOR,
                cases=[
                    Case(
                        query=f"~(`{ExecutionSourceColumns.ORDER_SIDE}`.str.fullmatch('short', case=False, na=False))",
                        as_empty=True,
                    ),
                    Case(
                        query=f"`{ExecutionSourceColumns.ORDER_SIDE}`.str.fullmatch('short', case=False, na=False)",
                        value=ShortSellingIndicator.SESH.value,
                    ),
                    # The foll. case is to prevent SE_DV_300 and uses the same pattern.
                    # But unlike the data validation, we are checking an input column here
                    # and not a value from the SRP instrument.
                    Case(
                        query=f"~`{ExecutionSourceColumns.INSTRUMENT_CLASSIFICATION}`.str.fullmatch('{c_d_e_instrument_classification_pattern}', case=False, na=False)",  # noqa E501
                        as_empty=True,
                    ),
                ],
            ),
        )

    def _traders_algos_waivers_indicators_waiver_indicator(self):
        """
        Not Implemented
        """

    def _transaction_details_branch_membership_country(self):
        """
        Not Implemented
        """

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Populates from TempColumns.BUY_SELL_INDICATOR
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.BUY_SELL_INDICATOR].values,
            index=self.pre_process_df.index,
            columns=[RTS22Transaction.TRANSACTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """
        Populates from AllocationSourceColumns.COMPLEX_TRADE_COMPONENT_ID
        """
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, AllocationSourceColumns.COMPLEX_TRADE_COMPONENT_ID
            ].values,
            index=self.source_frame.index,
            columns=[RTS22Transaction.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID],
        )

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cross_indicator(self):
        """
        Not Implemented
        """

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """
        Populates from AllocationSourceColumns.NET_AMOUNT.
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=AllocationSourceColumns.NET_AMOUNT,
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_NET_AMOUNT,
                cast_to=CastTo.NUMERIC_ABSOLUTE,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_outgoing_order_addl_info(self):
        """
        Not Implemented
        """

    def _transaction_details_position_effect(self):
        """
        Not Implemented
        """

    def _transaction_details_price(self) -> pd.DataFrame:
        """
        Populates from ExecutionSourceColumns.LAST_PX
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ConvertMinorToMajorParams(
                source_price_attribute=ExecutionSourceColumns.LAST_PX,
                source_ccy_attribute=ExecutionSourceColumns.CURRENCY,
                target_price_attribute=RTS22Transaction.TRANSACTION_DETAILS_PRICE,
            ),
        )

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """
        Populates from ExecutionSourceColumns.CURRENCY
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ConvertMinorToMajorParams(
                source_ccy_attribute=ExecutionSourceColumns.CURRENCY,
                target_ccy_attribute=RTS22Transaction.TRANSACTION_DETAILS_PRICE_CURRENCY,
            ),
        )

    def _transaction_details_price_not_applicable(self):
        """
        Not Implemented
        """

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """
        Populates from AllocationSourceColumns.PRICE_TYPE
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, AllocationSourceColumns.PRICE_TYPE].values,
            index=self.source_frame.index,
            columns=[RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOTATION],
        )

    def _transaction_details_price_pending(self):
        """
        Not Implemented
        """

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """
        Populates from ExecutionSourceColumns.QUANTITY
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, ExecutionSourceColumns.LAST_QTY].values,
            index=self.source_frame.index,
            columns=[RTS22Transaction.TRANSACTION_DETAILS_QUANTITY],
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """
        Populates from AllocationSourceColumns.QUANTITY_CURRENCY
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ConvertMinorToMajorParams(
                source_ccy_attribute=AllocationSourceColumns.QUANTITY_CURRENCY,
                target_ccy_attribute=RTS22Transaction.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
            ),
        )

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """
        Populates from AllocationSourceColumns.QUANTITY_TYPE
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=AllocationSourceColumns.QUANTITY_TYPE,
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_QUANTITY_NOTATION,
                case_insensitive=True,
                preserve_original=True,
                value_map={
                    "NOMI": QuantityNotation.NOML.value,
                },
            ),
            auditor=self.auditor,
        )

    def _transaction_details_record_type(self):
        """
        Not Implemented
        """

    def _transaction_details_settlement_amount(self):
        """
        Not Implemented
        """

    def _transaction_details_settlement_amount_currency(self):
        """
        Not Implemented
        """

    def _transaction_details_settlement_date(self):
        """
        Not Implemented
        """

    def _transaction_details_swap_directionalities(self):
        """
        Not Implemented
        """

    def _transaction_details_traded_quantity(self):
        """
        Not Implemented
        """

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populates from AllocationSourceColumns.TRADING_CAPACITY. This
        makes sure that upper/lower case values are translated to the
        correct schema case.
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=AllocationSourceColumns.TRADING_CAPACITY,
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_TRADING_CAPACITY,
                case_insensitive=True,
                value_map={
                    "AOTC": TradingCapacity.AOTC.value,
                    "DEAL": TradingCapacity.DEAL.value,
                    "MTCH": TradingCapacity.MTCH.value,
                },
            ),
            auditor=self.auditor,
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """
        Populates from TempColumns.TRADING_DATE_TIME
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.TRADING_DATE_TIME].values,
            index=self.pre_process_df.index,
            columns=[RTS22Transaction.TRANSACTION_DETAILS_TRADING_DATE_TIME],
        )

    def _transaction_details_trail_id(self):
        """
        Not Implemented
        """

    def _transaction_details_ultimate_venue(self):
        """
        Populates from ExecutionSourceColumns.EXCHANGE_MIC_CODE
        """
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, ExecutionSourceColumns.EXCHANGE_MIC_CODE
            ].values,
            index=self.source_frame.index,
            columns=[RTS22Transaction.TRANSACTION_DETAILS_ULTIMATE_VENUE],
        )

    def _transaction_details_venue(self) -> pd.DataFrame:
        """
        Populates from AllocationSourceColumns.EXCHANGE_MIC_CODE and ExecutionSourceColumns.EXCHANGE_MIC_CODE.
        In case we do not have value post mapping, static value XOFF is mapped.
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_VENUE,
                cases=[
                    Case(
                        query="index == index",
                        value="XOFF",
                    ),
                    Case(
                        query=f"`{ExecutionSourceColumns.EXCHANGE_MIC_CODE}`.notnull()",
                        attribute=ExecutionSourceColumns.EXCHANGE_MIC_CODE,
                    ),
                    Case(
                        query=f"`{AllocationSourceColumns.VENUE}`.notnull()",
                        attribute=AllocationSourceColumns.VENUE,
                    ),
                    Case(
                        query=f"`{ExecutionSourceColumns.OPTION_TYPE}`.notnull()",
                        value="XXXX",
                    ),
                ],
            ),
        )

    def _transmission_details_order_transmission_indicator(self) -> pd.DataFrame:
        """
        Populates from AllocationSourceColumns.TRANSMISSION_OF_ORDER_INDICATOR
        """
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, AllocationSourceColumns.TRANSMISSION_OF_ORDER_INDICATOR
            ].values,
            index=self.source_frame.index,
            columns=[
                RTS22Transaction.TRANSMISSION_DETAILS_ORDER_TRANSMISSION_INDICATOR
            ],
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """
        Populates from the following:
            ExecutionSourceColumns.ORDER_ID
            ExecutionSourceColumns.EXEC_ID
        """
        # This is done as we have a validation error DV-29 and DV-35
        # for Transaction Reference Number which prohibits
        # non-alphanumeric characters as well as the length of the field be less than 52 chars.
        stripped_exec_series = (
            self.source_frame.loc[:, ExecutionSourceColumns.EXEC_ID]
            .str.replace("[^a-zA-Z0-9]", "", regex=True)
            .str[:52]
        )
        return ConcatAttributes.process(
            source_frame=pd.concat(
                [
                    self.source_frame.loc[:, ExecutionSourceColumns.ORDER_ID],
                    stripped_exec_series,
                ],
                axis=1,
            ),
            params=ParamsConcatAttributes(
                source_attributes=[
                    ExecutionSourceColumns.ORDER_ID,
                    ExecutionSourceColumns.EXEC_ID,
                ],
                target_attribute=RTS22Transaction.REPORT_DETAILS_TRANSACTION_REF_NO,
            ),
        )

    def _market_identifiers_instrument(self):
        """
        Generates InstrumentIdentifiers to be used in linking instruments downstream
        """
        return InstrumentIdentifiers.process(
            source_frame=pd.concat(
                [
                    self.source_frame.loc[
                        :,
                        [
                            ExecutionSourceColumns.INSTRUMENT_IDENTIFICATION_CODE,
                            ExecutionSourceColumns.OPTION_TYPE,
                            ExecutionSourceColumns.UNDERLYING_INSTRUMENT_CODE,
                            ExecutionSourceColumns.EXCHANGE_MIC_CODE,
                            ExecutionSourceColumns.TICKER,
                        ],
                    ],
                    self.pre_process_df.loc[
                        :,
                        [
                            TempColumns.ASSET_CLASS,
                            TempColumns.EXPIRY_DATE,
                            TempColumns.OPTION_STRIKE_PRICE,
                            TempColumns.NOTIONAL_CURRENCY_1,
                            TempColumns.NOTIONAL_CURRENCY_2,
                            TempColumns.UNDERLYING_SYMBOL,
                        ],
                    ],
                ],
                axis=1,
            ),
            params=InstrumentIdentifiersParams(
                asset_class_attribute=TempColumns.ASSET_CLASS,
                bbg_figi_id_attribute=ExecutionSourceColumns.TICKER,
                currency_attribute=TempColumns.NOTIONAL_CURRENCY_1,
                expiry_date_attribute=TempColumns.EXPIRY_DATE,
                isin_attribute=ExecutionSourceColumns.INSTRUMENT_IDENTIFICATION_CODE,
                notional_currency_2_attribute=TempColumns.NOTIONAL_CURRENCY_2,
                option_strike_price_attribute=TempColumns.OPTION_STRIKE_PRICE,
                option_type_attribute=ExecutionSourceColumns.OPTION_TYPE,
                underlying_isin_attribute=ExecutionSourceColumns.UNDERLYING_INSTRUMENT_CODE,
                underlying_symbol_attribute=TempColumns.UNDERLYING_SYMBOL,
                venue_attribute=ExecutionSourceColumns.EXCHANGE_MIC_CODE,
            ),
        )

    def _market_identifiers_parties(self):
        """
        Generates PartyIdentifiers to be used in linking parties downstream
        """
        # Populate investment decision maker ONLY if trading capacity = DEAL

        investment_dec_maker_df = MapConditional.process(
            source_frame=pd.concat(
                [
                    self.target_df.loc[
                        :, [RTS22Transaction.TRANSACTION_DETAILS_TRADING_CAPACITY]
                    ],
                    self.pre_process_df.loc[:, TempColumns.INVESTMENT_DECISION_MAKER],
                ],
                axis=1,
            ),
            params=MapConditionalParams(
                target_attribute=TempColumns.INVESTMENT_DECISION_MAKER_IF_NOT_DEAL,
                cases=[
                    Case(
                        query=f"`{RTS22Transaction.TRANSACTION_DETAILS_TRADING_CAPACITY}`.str.fullmatch('{TradingCapacity.DEAL.value}', case=False, na=False)",
                        attribute=TempColumns.INVESTMENT_DECISION_MAKER,
                    ),
                    Case(
                        query=f"~`{RTS22Transaction.TRANSACTION_DETAILS_TRADING_CAPACITY}`.str.fullmatch('{TradingCapacity.DEAL.value}', case=False, na=False)",
                        as_empty=True,
                    ),
                ],
            ),
        )
        return PartyIdentifiers.process(
            source_frame=pd.concat(
                [self.pre_process_df, investment_dec_maker_df], axis=1
            ),
            params=PartyIdentifiersParams(
                target_attribute=RTS22Transaction.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=TempColumns.EXECUTING_ENTITY,
                investment_decision_within_firm_identifier=TempColumns.INVESTMENT_DECISION_MAKER_IF_NOT_DEAL,
                execution_within_firm_identifier=TempColumns.TRADER,
                counterparty_identifier=TempColumns.COUNTERPARTY,
                buy_sell_side_attribute=TempColumns.BUY_SELL_INDICATOR,
                buyer_identifier=TempColumns.BUYER,
                seller_identifier=TempColumns.SELLER,
                buyer_decision_maker_identifier=TempColumns.BUYER_DECISION_MAKER,
                seller_decision_maker_identifier=TempColumns.SELLER_DECISION_MAKER,
                trader_identifier=TempColumns.TRADER,
            ),
        )

    def _market_identifiers(self) -> pd.DataFrame:
        """
        Merges InstrumentIdentifiers and PartyIdentifiers results

        Assumes _market_identifiers_instrument() and _market_identifiers_parties()
        have run successfully
        """

        market_identifier_columns = pd.DataFrame(index=self.source_frame.index)

        market_identifier_columns.loc[
            :, RTS22Transaction.MARKET_IDENTIFIERS_INSTRUMENT
        ] = self.target_df.loc[:, RTS22Transaction.MARKET_IDENTIFIERS_INSTRUMENT]
        market_identifier_columns.loc[
            :, RTS22Transaction.MARKET_IDENTIFIERS_PARTIES
        ] = self.target_df.loc[:, RTS22Transaction.MARKET_IDENTIFIERS_PARTIES]

        return MergeMarketIdentifiers.process(
            market_identifier_columns,
            params=MergeMarketIdentifiersParams(
                identifiers_path=RTS22Transaction.MARKET_IDENTIFIERS,
                instrument_path=RTS22Transaction.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=RTS22Transaction.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_position_id(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    # Pre-Process Functions
    def _temp_trading_date_time(self) -> pd.DataFrame:
        """
        Temporary column used for:
            RTS22Transaction.TRANSACTION_DETAILS_TRADING_DATE_TIME
        Populates from ExecutionSourceColumns.EXECUTION_DATE
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=ExecutionSourceColumns.EXECUTION_DATE,
                source_attribute_format=DateFormats.EXECUTION_DATE_TIME,
                target_attribute=TempColumns.TRADING_DATE_TIME,
                convert_to=ConvertTo.DATETIME,
            ),
        )

    def _temp_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Creates temporary columns to be used in:
            RTS22Transaction.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
            TempColumns.COUNTERPARTY
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=ExecutionSourceColumns.ORDER_SIDE,
                target_attribute=TempColumns.BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "buy": BuySellIndicator.BUYI.value,
                    "cover": BuySellIndicator.BUYI.value,
                    "sell": BuySellIndicator.SELL.value,
                    "short": BuySellIndicator.SELL.value,
                },
            ),
            auditor=self.auditor,
        )

    def _temp_description_currencies(self) -> pd.DataFrame:
        """
        Temp columns for:
            TempColumns.INSTR_IDS_CURRENCY
        Populated from ExecutionSourceColumns.DESCRIPTION or AllocationSourceColumns.INSTRUMENT_FULL_NAME
        """

        currencies = pd.DataFrame(index=self.pre_process_df.index)

        # Get the currency before / for ExecutionSourceColumns.DESCRIPTION
        currencies[TempColumns.TEMP_COL_1] = (
            self.source_frame[ExecutionSourceColumns.DESCRIPTION]
            .str.split("/")
            .str.get(0)
            .fillna(pd.NA)
            .str.split(" ")
            .str[-1]
            .fillna(pd.NA)
        )

        # Get the currency before / for AllocationSourceColumns.INSTRUMENT_FULL_NAME
        currencies[TempColumns.TEMP_COL_2] = (
            self.source_frame[AllocationSourceColumns.INSTRUMENT_FULL_NAME]
            .str.split("/")
            .str.get(0)
            .fillna(pd.NA)
            .str.split(" ")
            .str[-1]
            .fillna(pd.NA)
        )

        # Giving preference to Executions data before Allocations data
        currencies[TempColumns.DESCRIPTION_CURRENCY_1] = currencies[
            TempColumns.TEMP_COL_1
        ].fillna(currencies[TempColumns.TEMP_COL_2])

        # Get the currency after / for ExecutionSourceColumns.DESCRIPTION
        currencies[TempColumns.TEMP_COL_1] = (
            self.source_frame[ExecutionSourceColumns.DESCRIPTION]
            .str.split("/")
            .str.get(1)
            .fillna(pd.NA)
            .str.split(" ")
            .str[0]
            .fillna(pd.NA)
        )

        # Get the currency after / for AllocationSourceColumns.INSTRUMENT_FULL_NAME
        currencies[TempColumns.TEMP_COL_2] = (
            self.source_frame[AllocationSourceColumns.INSTRUMENT_FULL_NAME]
            .str.split("/")
            .str.get(1)
            .fillna(pd.NA)
            .str.split(" ")
            .str[0]
            .fillna(pd.NA)
        )

        # Giving preference to Executions data before Allocations data
        currencies[TempColumns.DESCRIPTION_CURRENCY_2] = currencies[
            TempColumns.TEMP_COL_1
        ].fillna(currencies[TempColumns.TEMP_COL_2])

        currencies_converted = pd.concat(
            [
                ConvertMinorToMajor.process(
                    source_frame=currencies,
                    params=ConvertMinorToMajorParams(
                        source_ccy_attribute=TempColumns.DESCRIPTION_CURRENCY_1,
                        target_ccy_attribute=TempColumns.DESCRIPTION_CURRENCY_1,
                    ),
                ),
                ConvertMinorToMajor.process(
                    source_frame=currencies,
                    params=ConvertMinorToMajorParams(
                        source_ccy_attribute=TempColumns.DESCRIPTION_CURRENCY_2,
                        target_ccy_attribute=TempColumns.DESCRIPTION_CURRENCY_2,
                    ),
                ),
            ],
            axis=1,
        ).fillna("")

        unique_desc_ccy = (
            currencies_converted[TempColumns.DESCRIPTION_CURRENCY_1]
            .append(currencies_converted[TempColumns.DESCRIPTION_CURRENCY_2])
            .unique()
            .tolist()
        )

        result_map_ccy = {}
        for ccy in unique_desc_ccy:
            result_map_ccy.update({ccy: check_currency(ccy)})

        currencies_converted[TempColumns.DESCRIPTION_CURRENCY_1] = currencies_converted[
            TempColumns.DESCRIPTION_CURRENCY_1
        ].map(result_map_ccy)

        currencies_converted[TempColumns.DESCRIPTION_CURRENCY_2] = currencies_converted[
            TempColumns.DESCRIPTION_CURRENCY_2
        ].map(result_map_ccy)

        return currencies_converted

    # Instrument Identifiers Fields
    def _temp_instr_ids_asset_class(self):
        """
        Asset class, assigned according to the type fo file
        """
        instr_class_starts_with_jf = (
            f"(`{ExecutionSourceColumns.INSTRUMENT_CLASSIFICATION}`.fillna('').str.upper().str.startswith('JF')) | "
            f"(`{AllocationSourceColumns.INSTRUMENT_CLASSIFICATION}`.fillna('').str.upper().str.startswith('JF'))"
        )
        option_type_populated = (
            f"(`{ExecutionSourceColumns.OPTION_TYPE}`.notnull()) | "
            f"(`{AllocationSourceColumns.OPTION_TYPE}`.notnull())"
        )
        future_exp_date_populated = (
            f"(`{ExecutionSourceColumns.FUTURE_EXPIRATION_DATE}`.notnull()) | "
            f"(`{AllocationSourceColumns.FUTURE_EXPIRATION_DATE}`.notnull())"
        )
        fx_forward_condition = (
            f"(`{ExecutionSourceColumns.DESCRIPTION}`.str.contains('{AssetClass.FX_FORWARD}', case=False, na=False)) |"
            f"(`{AllocationSourceColumns.INSTRUMENT_FULL_NAME}`.str.contains("
            f"'{AssetClass.FX_FORWARD}', case=False, na=False))"
        )
        fx_spot_condition = (
            f"(`{ExecutionSourceColumns.DESCRIPTION}`.str.contains('{AssetClass.FX_SPOT}', case=False, na=False)) |"
            f"(`{AllocationSourceColumns.INSTRUMENT_FULL_NAME}`.str.contains("
            f"'{AssetClass.FX_SPOT}', case=False, na=False))"
        )

        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=TempColumns.ASSET_CLASS,
                cases=[
                    Case(
                        query=f"{future_exp_date_populated} & ~{instr_class_starts_with_jf}",
                        value=AssetClass.FUTURE,
                    ),
                    Case(
                        query=option_type_populated,
                        value=AssetClass.OPTION,
                    ),
                    Case(
                        query=fx_forward_condition,
                        value=AssetClass.FX_FORWARD,
                    ),
                    Case(
                        query=fx_spot_condition,
                        value=AssetClass.FX_SPOT,
                    ),
                ],
            ),
        )

    def _temp_instr_ids_currency(self) -> pd.DataFrame:
        """
        Populates TempColumns.NOTIONAL_CURRENCY_1 and TempColumns.NOTIONAL_CURRENCY_2
        which are used in InstrumentIdentifiers
        """
        return pd.concat(
            [
                MapConditional.process(
                    source_frame=pd.concat(
                        [
                            self.source_frame.loc[
                                :,
                                [
                                    ExecutionSourceColumns.CURRENCY,
                                    AllocationSourceColumns.PRICE_CURRENCY,
                                ],
                            ],
                            self.pre_process_df.loc[
                                :,
                                [
                                    TempColumns.ASSET_CLASS,
                                    TempColumns.DESCRIPTION_CURRENCY_1,
                                ],
                            ],
                        ],
                        axis=1,
                    ),
                    params=MapConditionalParams(
                        target_attribute=TempColumns.NOTIONAL_CURRENCY_1,
                        cases=[
                            Case(
                                query=f"`{TempColumns.ASSET_CLASS}`.str.fullmatch("
                                f"'{AssetClass.FX_FORWARD}|{AssetClass.FX_SPOT}', case=False, na=False)",
                                attribute=TempColumns.DESCRIPTION_CURRENCY_1,
                            ),
                            Case(
                                query=f"~(`{TempColumns.ASSET_CLASS}`.str.fullmatch("
                                f"'{AssetClass.FX_FORWARD}|{AssetClass.FX_SPOT}', case=False, na=False))",
                                attribute=AllocationSourceColumns.PRICE_CURRENCY,
                            ),
                            Case(
                                query=f"~(`{TempColumns.ASSET_CLASS}`.str.fullmatch("
                                f"'{AssetClass.FX_FORWARD}|{AssetClass.FX_SPOT}', case=False, na=False)) & "
                                f"`{ExecutionSourceColumns.CURRENCY}`.notnull()",
                                attribute=ExecutionSourceColumns.CURRENCY,
                            ),
                        ],
                    ),
                ),
                MapConditional.process(
                    source_frame=pd.concat(
                        [
                            self.source_frame.loc[
                                :,
                                [
                                    ExecutionSourceColumns.NOTIONAL_CURRENCY_2,
                                    AllocationSourceColumns.CURRENCY_2,
                                ],
                            ],
                            self.pre_process_df.loc[
                                :,
                                [
                                    TempColumns.ASSET_CLASS,
                                    TempColumns.DESCRIPTION_CURRENCY_2,
                                ],
                            ],
                        ],
                        axis=1,
                    ),
                    params=MapConditionalParams(
                        target_attribute=TempColumns.NOTIONAL_CURRENCY_2,
                        cases=[
                            Case(
                                query=f"`{TempColumns.ASSET_CLASS}`.str.fullmatch("
                                f"'{AssetClass.FX_FORWARD}|{AssetClass.FX_SPOT}', case=False, na=False)",
                                attribute=TempColumns.DESCRIPTION_CURRENCY_2,
                            ),
                            Case(
                                query=f"~(`{TempColumns.ASSET_CLASS}`.str.fullmatch("
                                f"'{AssetClass.FX_FORWARD}|{AssetClass.FX_SPOT}', case=False, na=False))",
                                attribute=AllocationSourceColumns.CURRENCY_2,
                            ),
                            Case(
                                query=f"~(`{TempColumns.ASSET_CLASS}`.str.fullmatch("
                                f"'{AssetClass.FX_FORWARD}|{AssetClass.FX_SPOT}', case=False, na=False)) & "
                                f"`{ExecutionSourceColumns.NOTIONAL_CURRENCY_2}`.notnull()",
                                attribute=ExecutionSourceColumns.NOTIONAL_CURRENCY_2,
                            ),
                        ],
                    ),
                ),
            ],
            axis=1,
        )

    def _temp_instr_ids_expiry_date(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates from ExecutionSourceColumns.EXPIRY_DATE
        """
        required_columns = [
            ExecutionSourceColumns.DESCRIPTION,
            AllocationSourceColumns.INSTRUMENT_FULL_NAME,
            ExecutionSourceColumns.FUTURE_EXPIRATION_DATE,
            ExecutionSourceColumns.OPTION_EXPIRATION_DATE,
            AllocationSourceColumns.EXPIRY_DATE,
        ]
        temp_df = self.source_frame.loc[:, required_columns]

        # Get last 10 characters from executions and allocations data
        temp_df[TempColumns.TEMP_COL_1] = (
            temp_df[ExecutionSourceColumns.DESCRIPTION].str[-10:].fillna(pd.NA)
        )
        temp_df[TempColumns.TEMP_COL_2] = (
            temp_df[AllocationSourceColumns.INSTRUMENT_FULL_NAME]
            .str[-10:]
            .fillna(pd.NA)
        )

        # Giving preference to Executions data before Allocations data
        temp_df[TempColumns.DESCRIPTION_DATE] = temp_df[TempColumns.TEMP_COL_1].fillna(
            temp_df[TempColumns.TEMP_COL_2]
        )
        temp_df[TempColumns.DESCRIPTION_DATE] = ConvertDatetime.process(
            source_frame=temp_df,
            params=ConvertDatetimeParams(
                source_attribute=TempColumns.DESCRIPTION_DATE,
                source_attribute_format=DateFormats.EXPIRY_DATE,
                target_attribute=TempColumns.DESCRIPTION_DATE,
                convert_to=ConvertTo.DATE,
            ),
        )
        temp_df[
            ExecutionSourceColumns.FUTURE_EXPIRATION_DATE
        ] = ConvertDatetime.process(
            source_frame=temp_df,
            params=ConvertDatetimeParams(
                source_attribute=ExecutionSourceColumns.FUTURE_EXPIRATION_DATE,
                source_attribute_format=DateFormats.EXPIRY_DATE,
                target_attribute=ExecutionSourceColumns.FUTURE_EXPIRATION_DATE,
                convert_to=ConvertTo.DATE,
            ),
        )
        temp_df[
            ExecutionSourceColumns.OPTION_EXPIRATION_DATE
        ] = ConvertDatetime.process(
            source_frame=temp_df,
            params=ConvertDatetimeParams(
                source_attribute=ExecutionSourceColumns.OPTION_EXPIRATION_DATE,
                source_attribute_format=DateFormats.EXPIRY_DATE,
                target_attribute=ExecutionSourceColumns.OPTION_EXPIRATION_DATE,
                convert_to=ConvertTo.DATE,
            ),
        )
        temp_df[AllocationSourceColumns.EXPIRY_DATE] = ConvertDatetime.process(
            source_frame=temp_df,
            params=ConvertDatetimeParams(
                source_attribute=AllocationSourceColumns.EXPIRY_DATE,
                source_attribute_format=DateFormats.EXPIRY_DATE,
                target_attribute=AllocationSourceColumns.EXPIRY_DATE,
                convert_to=ConvertTo.DATE,
            ),
        )
        return MapConditional.process(
            source_frame=pd.concat(
                [
                    temp_df,
                    self.pre_process_df.loc[:, [TempColumns.ASSET_CLASS]],
                ],
                axis=1,
            ),
            params=MapConditionalParams(
                target_attribute=TempColumns.EXPIRY_DATE,
                cases=[
                    Case(
                        query=f"`{TempColumns.ASSET_CLASS}`.str.fullmatch("
                        f"'{AssetClass.FX_FORWARD}', case=False, na=False)",
                        attribute=TempColumns.DESCRIPTION_DATE,
                    ),
                    Case(
                        query=f"~(`{TempColumns.ASSET_CLASS}`.str.fullmatch("
                        f"'{AssetClass.FX_FORWARD}', case=False, na=False)) & "
                        f"`{ExecutionSourceColumns.OPTION_EXPIRATION_DATE}`.notnull()",
                        attribute=ExecutionSourceColumns.OPTION_EXPIRATION_DATE,
                    ),
                    Case(
                        query=f"~(`{TempColumns.ASSET_CLASS}`.str.fullmatch("
                        f"'{AssetClass.FX_FORWARD}', case=False, na=False)) & "
                        f"`{ExecutionSourceColumns.FUTURE_EXPIRATION_DATE}`.notnull()",
                        attribute=ExecutionSourceColumns.FUTURE_EXPIRATION_DATE,
                    ),
                    Case(
                        query=f"~(`{TempColumns.ASSET_CLASS}`.str.fullmatch("
                        f"'{AssetClass.FX_FORWARD}', case=False, na=False)) & "
                        f"`{AllocationSourceColumns.EXPIRY_DATE}`.notnull()",
                        attribute=AllocationSourceColumns.EXPIRY_DATE,
                    ),
                ],
            ),
        )

    def _temp_instr_ids_option_strike_price(self):
        """
        Temporary Column for InstrumentIdentifiers
        Populates from ExecutionSourceColumns.STRIKE_PRICE
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ConvertMinorToMajorParams(
                source_price_attribute=ExecutionSourceColumns.OPTION_STRIKE,
                source_ccy_attribute=ExecutionSourceColumns.CURRENCY,
                target_price_attribute=TempColumns.OPTION_STRIKE_PRICE,
            ),
        )

    def _temp_instr_ids_underlying_symbol(self):
        """
        Temporary Column for InstrumentIdentifiers
        Populates from ExecutionSourceColumns.OPTION_CONTRACT_BBG_ROOT_CODE in case of options or
        ExecutionSourceColumns.FUTURE_BLOOMBERG_ROOT in case of future
        """
        return MapConditional.process(
            source_frame=pd.concat(
                [
                    self.source_frame.loc[
                        :,
                        [
                            ExecutionSourceColumns.OPTION_CONTRACT_BBG_ROOT_CODE,
                            ExecutionSourceColumns.FUTURE_BLOOMBERG_ROOT,
                        ],
                    ],
                    self.pre_process_df.loc[:, [TempColumns.ASSET_CLASS]],
                ],
                axis=1,
            ),
            params=MapConditionalParams(
                target_attribute=TempColumns.UNDERLYING_SYMBOL,
                cases=[
                    Case(
                        query=f"`{TempColumns.ASSET_CLASS}`.str.fullmatch('{AssetClass.OPTION}', case=False, na=False)",
                        attribute=ExecutionSourceColumns.OPTION_CONTRACT_BBG_ROOT_CODE,
                    ),
                    Case(
                        query=f"`{TempColumns.ASSET_CLASS}`.str.fullmatch('{AssetClass.FUTURE}', case=False, na=False)",
                        attribute=ExecutionSourceColumns.FUTURE_BLOOMBERG_ROOT,
                    ),
                ],
            ),
        )

    # Party Identifiers Fields
    def _temp_executing_entity(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from ExecutionSourceColumns.EXECUTION_ENTITY_IDENTIFICATION_CODE
        """
        return pd.concat(
            [
                pd.DataFrame(
                    data=add_id_or_lei(
                        self.source_frame.loc[
                            :,
                            [
                                ExecutionSourceColumns.EXECUTION_ENTITY_IDENTIFICATION_CODE
                            ],
                        ],
                        ExecutionSourceColumns.EXECUTION_ENTITY_IDENTIFICATION_CODE,
                    ).values,
                    index=self.source_frame.index,
                    columns=[TempColumns.EXECUTING_ENTITY],
                ),
                pd.DataFrame(
                    data=self.source_frame.loc[
                        :, [ExecutionSourceColumns.EXECUTION_ENTITY_IDENTIFICATION_CODE]
                    ].values,
                    index=self.source_frame.index,
                    columns=[TempColumns.EXECUTING_ENTITY_WITHOUT_PREFIX],
                ),
            ],
            axis=1,
        )

    def _temp_party_ids_trader(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from ExecutionSourceColumns.TRADER
        Adds id prefix to the column
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=ExecutionSourceColumns.TRADER,
                target_attribute=TempColumns.TRADER,
                prefix=PartyPrefix.ID,
            ),
            auditor=self.auditor,
        )

    def _temp_party_ids_buyer(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        If BUY populated from:
            TempColumns.EXECUTING_ENTITY
        If SELL populates in following order:
            AllocationSourceColumns.COUNTERPARTY
            ExecutionSourceColumns.SELLER_IDENTIFICATION_CODE
            AllocationSourceColumns.SELLER_DECISION_MAKER_CODE

        Adds id/lei prefix to the column
        """
        temp_df = pd.concat(
            [
                self.source_frame.loc[
                    :,
                    [
                        AllocationSourceColumns.COUNTERPARTY,
                        ExecutionSourceColumns.SELLER_IDENTIFICATION_CODE,
                        AllocationSourceColumns.SELLER_DECISION_MAKER_CODE,
                        ExecutionSourceColumns.EXECUTION_ENTITY_IDENTIFICATION_CODE,
                    ],
                ],
                self.pre_process_df.loc[
                    :,
                    [
                        TempColumns.BUY_SELL_INDICATOR,
                        TempColumns.EXECUTING_ENTITY_WITHOUT_PREFIX,
                    ],
                ],
            ],
            axis=1,
        )
        temp_df[AllocationSourceColumns.COUNTERPARTY] = temp_df[
            AllocationSourceColumns.COUNTERPARTY
        ].replace("Unknown", pd.NA)

        first_case_column = self.get_buyer_seller_first_case_column()

        buyer = MapConditional.process(
            source_frame=temp_df,
            params=MapConditionalParams(
                target_attribute=TempColumns.BUYER,
                cases=[
                    Case(
                        query=f"(`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.BUYI.value}')",
                        attribute=first_case_column,
                    ),
                    Case(
                        query=f"(`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.SELL.value}') & "
                        f"(`{AllocationSourceColumns.SELLER_DECISION_MAKER_CODE}`.notnull())",
                        attribute=AllocationSourceColumns.SELLER_DECISION_MAKER_CODE,
                    ),
                    Case(
                        query=f"(`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.SELL.value}') & "
                        f"(`{ExecutionSourceColumns.SELLER_IDENTIFICATION_CODE}`.notnull())",
                        attribute=ExecutionSourceColumns.SELLER_IDENTIFICATION_CODE,
                    ),
                    Case(
                        query=f"(`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.SELL.value}') & "
                        f"(`{AllocationSourceColumns.COUNTERPARTY}`.notnull())",
                        attribute=AllocationSourceColumns.COUNTERPARTY,
                    ),
                ],
            ),
        )
        return add_id_or_lei(buyer, TempColumns.BUYER)

    def _temp_party_ids_seller(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        If SELL populated from:
            TempColumns.EXECUTING_ENTITY
        If BUY populates in following order:
            AllocationSourceColumns.COUNTERPARTY
            ExecutionSourceColumns.BUYER_IDENTIFICATION_CODE
            AllocationSourceColumns.BUYER_DECISION_MAKER_CODE

        Adds id/lei prefix to the column
        """
        temp_df = pd.concat(
            [
                self.source_frame.loc[
                    :,
                    [
                        AllocationSourceColumns.COUNTERPARTY,
                        ExecutionSourceColumns.BUYER_IDENTIFICATION_CODE,
                        AllocationSourceColumns.BUYER_DECISION_MAKER_CODE,
                        ExecutionSourceColumns.EXECUTION_ENTITY_IDENTIFICATION_CODE,
                    ],
                ],
                self.pre_process_df.loc[
                    :,
                    [
                        TempColumns.BUY_SELL_INDICATOR,
                        TempColumns.EXECUTING_ENTITY_WITHOUT_PREFIX,
                    ],
                ],
            ],
            axis=1,
        )
        temp_df[AllocationSourceColumns.COUNTERPARTY] = temp_df[
            AllocationSourceColumns.COUNTERPARTY
        ].replace("Unknown", pd.NA)

        first_case_column = self.get_buyer_seller_first_case_column()

        seller = MapConditional.process(
            source_frame=temp_df,
            params=MapConditionalParams(
                target_attribute=TempColumns.SELLER,
                cases=[
                    Case(
                        query=f"(`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.SELL.value}')",
                        attribute=first_case_column,
                    ),
                    Case(
                        query=f"(`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.BUYI.value}') & "
                        f"(`{AllocationSourceColumns.BUYER_DECISION_MAKER_CODE}`.notnull())",
                        attribute=AllocationSourceColumns.BUYER_DECISION_MAKER_CODE,
                    ),
                    Case(
                        query=f"(`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.BUYI.value}') & "
                        f"(`{ExecutionSourceColumns.BUYER_IDENTIFICATION_CODE}`.notnull())",
                        attribute=ExecutionSourceColumns.BUYER_IDENTIFICATION_CODE,
                    ),
                    Case(
                        query=f"(`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.BUYI.value}') & "
                        f"(`{AllocationSourceColumns.COUNTERPARTY}`.notnull())",
                        attribute=AllocationSourceColumns.COUNTERPARTY,
                    ),
                ],
            ),
        )
        return add_id_or_lei(seller, TempColumns.SELLER)

    @staticmethod
    def get_buyer_seller_first_case_column():
        """
        method used to facilitate thornbridge override
        """
        return TempColumns.EXECUTING_ENTITY_WITHOUT_PREFIX

    def _temp_party_ids_counterparty(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers

        If BUY populates in following order:
            TempColumns.SELLER
        If SELL populates in following order:
            TempColumns.BUYER
        """
        return MapConditional.process(
            source_frame=self.pre_process_df.loc[
                :,
                [TempColumns.BUY_SELL_INDICATOR, TempColumns.BUYER, TempColumns.SELLER],
            ],
            params=MapConditionalParams(
                target_attribute=TempColumns.COUNTERPARTY,
                cases=[
                    Case(
                        query=f"(`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.BUYI.value}')",
                        attribute=TempColumns.SELLER,
                    ),
                    Case(
                        query=f"(`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.SELL.value}')",
                        attribute=TempColumns.BUYER,
                    ),
                ],
            ),
        )

    def _temp_party_ids_investment_decision_maker(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from AllocationSourceColumns.PORTFOLIO_MANAGER
        Adds id prefix to the column
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=AllocationSourceColumns.PORTFOLIO_MANAGER,
                target_attribute=TempColumns.INVESTMENT_DECISION_MAKER,
                prefix=PartyPrefix.ID,
            ),
            auditor=self.auditor,
        )

    def _temp_party_ids_buyer_decision_maker(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from AllocationSourceColumns.BUYER_DECISION_MAKER_CODE
        Adds id/lei prefix to the column
        """
        temp_df = pd.concat(
            [
                self.source_frame.loc[
                    :, AllocationSourceColumns.BUYER_DECISION_MAKER_CODE
                ],
                self.pre_process_df.loc[:, TempColumns.BUYER],
            ],
            axis=1,
        )
        temp_df[AllocationSourceColumns.BUYER_DECISION_MAKER_CODE] = add_id_or_lei(
            temp_df, AllocationSourceColumns.BUYER_DECISION_MAKER_CODE
        )
        return MapConditional.process(
            source_frame=temp_df,
            params=MapConditionalParams(
                target_attribute=TempColumns.BUYER_DECISION_MAKER,
                cases=[
                    Case(
                        query=f"(`{TempColumns.BUYER}` != `{AllocationSourceColumns.BUYER_DECISION_MAKER_CODE}`)",
                        attribute=AllocationSourceColumns.BUYER_DECISION_MAKER_CODE,
                    ),
                ],
            ),
        )

    def _temp_party_ids_seller_decision_maker(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populates from AllocationSourceColumns.SELLER_DECISION_MAKER_CODE
        Adds id/lei prefix to the column
        """
        temp_df = pd.concat(
            [
                self.source_frame.loc[
                    :, AllocationSourceColumns.SELLER_DECISION_MAKER_CODE
                ],
                self.pre_process_df.loc[:, TempColumns.SELLER],
            ],
            axis=1,
        )
        temp_df[AllocationSourceColumns.SELLER_DECISION_MAKER_CODE] = add_id_or_lei(
            temp_df, AllocationSourceColumns.SELLER_DECISION_MAKER_CODE
        )
        return MapConditional.process(
            source_frame=temp_df,
            params=MapConditionalParams(
                target_attribute=TempColumns.SELLER_DECISION_MAKER,
                cases=[
                    Case(
                        query=f"(`{TempColumns.SELLER}` != `{AllocationSourceColumns.SELLER_DECISION_MAKER_CODE}`)",
                        attribute=AllocationSourceColumns.SELLER_DECISION_MAKER_CODE,
                    ),
                ],
            ),
        )
