from pathlib import Path
from typing import Any

import pandas as pd
from prefect import context
from prefect.engine.signals import SKIP
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.conf import Settings
from swarm.task.transform.base import BaseTask

from swarm_tasks.steeleye.tr.feed.enfusion.v2.static import ExecutionSourceColumns
from swarm_tasks.steeleye.tr.feed.enfusion.v2.static import TempColumns

logger = context.get("logger")


class EnfusionTransactionFrameTransformer(BaseTask):
    """
    This task is used to create new frame based on the information
    contained in the two types of files used by this flow.

    Specs: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2063139091/RTS22+Enfusion+V2
    """

    def execute(
        self,
        pre_process_result: dict = None,
        s3_file_url_df: pd.DataFrame = pd.DataFrame(),
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            pre_process_result=pre_process_result,
            s3_file_url_df=s3_file_url_df,
        )

    @classmethod
    def process(
        cls,
        pre_process_result: dict = None,
        s3_file_url_df: pd.DataFrame = pd.DataFrame(),
    ) -> Any:
        if not pre_process_result:
            raise SKIP("Upstream pre process dict is empty")

        transformed_df = cls.transform_file(pre_process_result)

        if s3_file_url_df.empty:
            logger.warning(
                "S3 file urls not provided. SourceKey would not be populated correctly."
            )

        transformed_df.loc[:, TempColumns.TRIGGER_FILE_NAME] = s3_file_url_df[
            "s3_executions_file_url"
        ].iloc[0]

        # Sorting and resetting index based on orderid so that batches are created in such a
        # way which reduces probable missed in AssignMetaParent task
        transformed_df = transformed_df.sort_values(
            by=ExecutionSourceColumns.ORDER_ID, ascending=True
        )
        transformed_df = transformed_df.reset_index(drop=True)

        logger.info(f"Transformed record view's shape is {transformed_df.shape}")

        source_dir = cls._get_source_dir()
        csv_file_path = source_dir.joinpath("data_transformed.csv")
        transformed_df.to_csv(csv_file_path, index=False, encoding="utf-8", sep=",")
        result = ExtractPathResult(path=csv_file_path)

        return result

    @classmethod
    def transform_file(cls, df_dict: dict) -> pd.DataFrame:
        """
        Takes in the reference dataframe and then processes it according
        to order flow merge specs
        :param df_dict: Input DataFrame dicts
        :returns: Transformed df
        """
        allocation_df = df_dict.get("allocations", pd.DataFrame())
        execution_df = df_dict.get("executions", pd.DataFrame())

        if execution_df.empty or allocation_df.empty:
            raise SKIP(
                "Could not transform as executions/allocations df is empty. "
                f"Counts:\n executions: {execution_df.shape}, "
                f"allocation: {allocation_df.shape}"
            )
        # Normalizing column names by making them upper case and removing spaces
        allocation_df.columns = allocation_df.columns.str.upper().str.replace(r"\s", "")
        execution_df.columns = execution_df.columns.str.upper().str.replace(r"\s", "")

        # Sort and take first record of place_df to preserve 1:1 mapping
        allocation_df = allocation_df.sort_values(
            by=[ExecutionSourceColumns.ORDER_ID, ExecutionSourceColumns.ORDER_DATE],
            ascending=[1, 1],
        )
        allocation_df = allocation_df.groupby(
            ExecutionSourceColumns.ORDER_ID, as_index=False
        ).nth(0)

        # Merging allocation_df on execution_df df to get the necessary context for downstream processing
        # in primary transformations
        transformed_df = execution_df.merge(
            allocation_df,
            on=ExecutionSourceColumns.ORDER_ID,
            how="left",
            suffixes=("", "_allocations"),
        )

        return transformed_df

    @staticmethod
    def _get_source_dir() -> Path:
        return Path(Settings.context.sources_dir)
