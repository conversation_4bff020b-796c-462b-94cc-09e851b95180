from pathlib import Path
from typing import Any

import pandas as pd
from prefect import context
from prefect.engine.signals import SKIP
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.conf import Settings
from swarm.task.transform.base import BaseTask

from swarm_tasks.steeleye.tr.feed.lme.static import SourceColumns
from swarm_tasks.steeleye.tr.feed.lme.static import TempColumns

logger = context.get("logger")


class LmeTransactionFrameTransformer(BaseTask):
    """
    This task is used to create new frame based on the information
    contained in the two types of files used by this flow.

    Specs: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2709159950/RTS22+LME#Sigma--Controller-Function
    """

    def execute(
        self,
        pre_process_result: dict = None,
        s3_file_url_df: pd.DataFrame = pd.DataFrame(),
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            pre_process_result=pre_process_result,
            s3_file_url_df=s3_file_url_df,
        )

    @classmethod
    def process(
        cls,
        pre_process_result: dict = None,
        s3_file_url_df: pd.DataFrame = pd.DataFrame(),
    ) -> Any:
        if not pre_process_result:
            raise SKIP("Upstream pre process dict is empty")

        transformed_df = cls.transform_file(pre_process_result)

        if s3_file_url_df.empty:
            logger.warning(
                "S3 file urls not provided. SourceKey would not be populated correctly."
            )

        transformed_df.loc[:, TempColumns.TRIGGER_FILE_NAME] = s3_file_url_df[
            "s3_eodm_file_url"
        ].iloc[0]

        transformed_df = transformed_df.reset_index(drop=True)

        logger.info(f"Transformed record view's shape is {transformed_df.shape}")

        source_dir = cls._get_source_dir()
        csv_file_path = source_dir.joinpath("data_transformed.csv")
        transformed_df.to_csv(csv_file_path, index=False, encoding="utf-8", sep=",")
        result = ExtractPathResult(path=csv_file_path)

        return result

    @classmethod
    def transform_file(cls, df_dict: dict) -> pd.DataFrame:
        """
        Takes in the reference dataframe and then processes it according
        to order flow merge specs
        :param df_dict: Input DataFrame dicts
        :returns: Transformed df
        """
        eodm_df = df_dict.get("eodm", pd.DataFrame())
        additional_info_df = df_dict.get("additional_details", pd.DataFrame())

        if additional_info_df.empty or eodm_df.empty:
            raise SKIP(
                "Could not transform as EODM / Additional Info df is empty. "
                f"Counts:\n additional_info: {additional_info_df.shape}, "
                f"eodm: {eodm_df.shape}"
            )

        # Sort and take first record of place_df to preserve 1:1 mapping
        additional_info_df = additional_info_df.sort_values(
            by=[SourceColumns.MATCH_SLIP_ID, SourceColumns.TRADE_DATE_TIME],
            ascending=[1, 1],
        )
        additional_info_df = additional_info_df.groupby(
            SourceColumns.MATCH_SLIP_ID, as_index=False
        ).nth(0)

        # Merging allocation_df on execution_df df to get the necessary context for downstream processing
        # in primary transformations
        transformed_df = eodm_df.merge(
            additional_info_df,
            on=SourceColumns.MATCH_SLIP_ID,
            how="left",
            suffixes=("", "_addl_info"),
        )

        # Linking Match Slip IDs to retrieve Client Code
        for col in [SourceColumns.CLIENT_CODE, SourceColumns.TRADER_ID]:
            if col not in transformed_df.columns:
                transformed_df[col] = pd.NA

        mask = transformed_df[SourceColumns.CLIENT_CODE].isnull()
        transformed_df.loc[mask, SourceColumns.CLIENT_CODE] = transformed_df.loc[
            mask, SourceColumns.TRADER_ID
        ]

        transformed_df[SourceColumns.PRICE] = transformed_df[
            SourceColumns.PRICE
        ].str.replace(",", "")

        return transformed_df

    @staticmethod
    def _get_source_dir() -> Path:
        return Path(Settings.context.sources_dir)
