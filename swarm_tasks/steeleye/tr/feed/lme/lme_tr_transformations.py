import os

import pandas as pd
from se_core_tasks.currency.convert_minor_to_major import CastTo
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import OptionType
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import ReportStatus
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_trades_tasks.abstractions.abstract_rts22_transaction_transformations import (
    AbstractRTS22TransactionsTransformations,
)
from se_trades_tasks.order.static import Venue
from se_trades_tasks.order_and_tr.party.utils import add_id_or_lei
from se_trades_tasks.order_and_tr.static import AssetClass
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ConvertMinorToMajorParams,
)
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.generic.get_tenant_lei import Params as GetTenantLEIParams
from swarm_tasks.steeleye.tr.feed.lme.static import AddlInfoColumns
from swarm_tasks.steeleye.tr.feed.lme.static import DateFormats
from swarm_tasks.steeleye.tr.feed.lme.static import SourceColumns
from swarm_tasks.steeleye.tr.feed.lme.static import TempColumns
from swarm_tasks.steeleye.tr.static import RTS22Transaction
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.concat.concat_attributes import Prefix
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ConvertDatetimeParams,
)
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as MapConditionalParams
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as MapValueParams
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as MergeMarketIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as InstrumentIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.identifiers.party_identifiers import (
    Params as PartyIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.identifiers.party_identifiers import (
    PartyIdentifiers,
)
from swarm_tasks.utilities.static import Delimiters
from swarm_tasks.utilities.static import SWARM_FILE_URL


class LmeTrTransformations(AbstractRTS22TransactionsTransformations):
    """
    Primary Transformations for LME TR
    """

    def process(self) -> pd.DataFrame:
        self.pre_process()

        self.data_source_name()
        self.date()
        self.meta_model()
        self.report_details_report_status()
        self.source_index()
        self.source_key()
        self.traders_algos_waivers_indicators_securities_financing_txn_indicator()
        self.transaction_details_buy_sell_indicator()
        self.transaction_details_price()
        self.transaction_details_price_currency()
        self.transaction_details_price_notation()
        self.transaction_details_quantity()
        self.transaction_details_quantity_notation()
        self.transaction_details_trading_capacity()
        self.transaction_details_trading_date_time()
        self.transaction_details_venue()
        self.transaction_details_ultimate_venue()
        self.transaction_details_outgoing_order_addl_info()
        self.transmission_details_order_transmission_indicator()

        self.report_details_transaction_ref_no()
        self.report_details_trading_venue_transaction_id_code()

        self.market_identifiers_instrument()
        self.market_identifiers_parties()
        self.market_identifiers()

        self.post_process()
        return self.target_df

    def _pre_process(self):
        """
        Creates temporary column expiry date, used later in InstrumentIdentifiers
        and downstream in InstrumentOverrideExpiryDate
        """
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_trading_date_time(),
                self._temp_trading_date_time_just_date(),
                self._temp_executing_entity(),
                self._temp_buy_sell_indicator(),
                self._temp_instr_ids_asset_class(),
                self._temp_instr_ids_option_type(),
                self._temp_instr_ids_expiry_date(),
                self._temp_party_ids_investment_decision_within_firm(),
            ],
            axis=1,
        )

        # these columns need access to others defined above
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_party_ids_buyer(),  # Needs buy_sell_indicator
                self._temp_party_ids_seller(),  # Needs buy_sell_indicator
                self._temp_party_ids_execution_within_firm(),  # Needs investment decision within firm
            ],
            axis=1,
        )

    def _post_process(self):
        """
        Not Implemented
        """

    def _data_source_name(self) -> pd.DataFrame:
        """
        Static value: LME
        """
        return pd.DataFrame(
            data="LME",
            index=self.source_frame.index,
            columns=[RTS22Transaction.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """
        Populates from TempColumns.TRADING_DATE_TIME_JUST_DATE
        """
        return pd.DataFrame(
            data=self.pre_process_df[TempColumns.TRADING_DATE_TIME_JUST_DATE].values,
            index=self.pre_process_df.index,
            columns=[RTS22Transaction.DATE],
        )

    def _meta_model(self) -> pd.DataFrame:
        """
        Static value: RTS22Transaction
        """
        return pd.DataFrame(
            data="RTS22Transaction",
            index=self.source_frame.index,
            columns=[RTS22Transaction.META_MODEL],
        )

    def _report_details_investment_firm_covered_directive(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _report_details_report_status(self) -> pd.DataFrame:
        """
        Maps static value NEWT
        """
        return pd.DataFrame(
            data=ReportStatus.NEWT.value,
            index=self.source_frame.index,
            columns=[RTS22Transaction.REPORT_DETAILS_REPORT_STATUS],
        )

    def _report_details_trading_venue_transaction_id_code(self):
        """
        Populates RTS22Transaction.REPORT_DETAILS_TRADING_VENUE_TRANSACTION_ID_CODE
        from SourceColumns.SELECT_MATCH_NO or
        SourceColumns.LCH_MATCH_NUMBER + SourceColumns.CLEARING_SLIP_ID
        """
        temp_df = self.source_frame.loc[
            :,
            [
                SourceColumns.SELECT_MATCH_NO,
                SourceColumns.LCH_MATCH_NUMBER,
                SourceColumns.CLEARING_SLIP_ID,
            ],
        ]
        temp_df[TempColumns.TEMP_COL_1] = (
            temp_df[SourceColumns.LCH_MATCH_NUMBER]
            + temp_df[SourceColumns.CLEARING_SLIP_ID]
        )

        return MapConditional.process(
            source_frame=temp_df,
            params=MapConditionalParams(
                target_attribute=RTS22Transaction.REPORT_DETAILS_TRADING_VENUE_TRANSACTION_ID_CODE,
                cases=[
                    Case(
                        query=f"`{SourceColumns.SELECT_MATCH_NO}`.notnull()",
                        attribute=SourceColumns.SELECT_MATCH_NO,
                    ),
                    Case(
                        query=f"`{SourceColumns.SELECT_MATCH_NO}`.isnull()",
                        attribute=TempColumns.TEMP_COL_1,
                    ),
                ],
            ),
        )[RTS22Transaction.REPORT_DETAILS_TRADING_VENUE_TRANSACTION_ID_CODE].str[:52]

    def _source_index(self) -> pd.DataFrame:
        """
        Static value: index from the source_frame
        """
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[RTS22Transaction.SOURCE_INDEX],
        )

    def _source_key(self) -> pd.DataFrame:
        """
        Maps form either Static value: File URL of from TempColumns.TRIGGER_FILE_NAME
        """
        source_key_data = (
            self.source_frame[TempColumns.TRIGGER_FILE_NAME].values
            if all(self.source_frame[TempColumns.TRIGGER_FILE_NAME].notnull())
            else os.getenv(SWARM_FILE_URL)
        )
        return pd.DataFrame(
            data=source_key_data,
            index=self.source_frame.index,
            columns=[RTS22Transaction.SOURCE_KEY],
        )

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(self):
        """
        Not Implemented
        """

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(self):
        """
        Not Implemented
        """

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(self):
        """
        Static value: False
        """
        return pd.DataFrame(
            data=False,
            index=self.source_frame.index,
            columns=[
                RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_SECRT_FNC_TXN_IND
            ],
        )

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _traders_algos_waivers_indicators_waiver_indicator(self):
        """
        Not Implemented
        """

    def _transaction_details_branch_membership_country(self):
        """
        Not Implemented
        """

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Populates from TempColumns.BUY_SELL_INDICATOR
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.BUY_SELL_INDICATOR].values,
            index=self.pre_process_df.index,
            columns=[RTS22Transaction.TRANSACTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cross_indicator(self):
        """
        Not Implemented
        """

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_outgoing_order_addl_info(self):
        """
        Populated from SourceColumns.TRADE_CATEGORY and SourceColumns.VENUE
        """
        filter_cols = [
            SourceColumns.TRADE_CATEGORY,
            SourceColumns.VENUE,
        ]
        replace_col_names = [
            AddlInfoColumns.TRADE_CATEGORY,
            AddlInfoColumns.VENUE,
        ]
        temp_df = self.source_frame.loc[:, filter_cols]

        temp_df.columns = replace_col_names

        return ConcatAttributes.process(
            source_frame=temp_df,
            params=ParamsConcatAttributes(
                source_attributes=[
                    AddlInfoColumns.TRADE_CATEGORY,
                    AddlInfoColumns.VENUE,
                ],
                prefix=Prefix.attribute_name.value,
                delimiter=Delimiters.COMMA_SPACE,
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
            ),
        )

    def _transaction_details_position_effect(self):
        """
        Not Implemented
        """

    def _transaction_details_price(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.PRICE
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ConvertMinorToMajorParams(
                source_price_attribute=SourceColumns.PRICE,
                source_ccy_attribute=SourceColumns.CURRENCY,
                target_price_attribute=RTS22Transaction.TRANSACTION_DETAILS_PRICE,
                cast_to=CastTo.FLOAT,
            ),
        )

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.CURRENCY
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ConvertMinorToMajorParams(
                source_ccy_attribute=SourceColumns.CURRENCY,
                target_ccy_attribute=RTS22Transaction.TRANSACTION_DETAILS_PRICE_CURRENCY,
            ),
        )

    def _transaction_details_price_not_applicable(self):
        """
        Not Implemented
        """

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """
        Populates static value MONE
        """
        return pd.DataFrame(
            data=PriceNotation.MONE.value,
            index=self.source_frame.index,
            columns=[RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOTATION],
        )

    def _transaction_details_price_pending(self):
        """
        Not Implemented
        """

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """
        Populates from SourceColumns.VOLUME
        """
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.VOLUME].values,
            index=self.source_frame.index,
            columns=[RTS22Transaction.TRANSACTION_DETAILS_QUANTITY],
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """
        Populates static value UNIT
        """
        return pd.DataFrame(
            data=QuantityNotation.UNIT.value,
            index=self.source_frame.index,
            columns=[RTS22Transaction.TRANSACTION_DETAILS_QUANTITY_NOTATION],
        )

    def _transaction_details_record_type(self):
        """
        Not Implemented
        """

    def _transaction_details_settlement_amount(self):
        """
        Not Implemented
        """

    def _transaction_details_settlement_amount_currency(self):
        """
        Not Implemented
        """

    def _transaction_details_settlement_date(self):
        """
        Not Implemented
        """

    def _transaction_details_swap_directionalities(self):
        """
        Not Implemented
        """

    def _transaction_details_traded_quantity(self):
        """
        Not Implemented
        """

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """
        Populates static value AOTC
        """
        return pd.DataFrame(
            data=TradingCapacity.DEAL.value,
            index=self.source_frame.index,
            columns=[RTS22Transaction.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """
        Populates from TempColumns.TRADING_DATE_TIME
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.TRADING_DATE_TIME].values,
            index=self.pre_process_df.index,
            columns=[RTS22Transaction.TRANSACTION_DETAILS_TRADING_DATE_TIME],
        )

    def _transaction_details_trail_id(self):
        """
        Not Implemented
        """

    def _transaction_details_ultimate_venue(self):
        """
        Populates static value XLME
        """
        return pd.DataFrame(
            data=Venue.XLME,
            index=self.source_frame.index,
            columns=[RTS22Transaction.TRANSACTION_DETAILS_ULTIMATE_VENUE],
        )

    def _transaction_details_venue(self) -> pd.DataFrame:
        """
        Populates static value XLME
        """
        return pd.DataFrame(
            data=Venue.XLME,
            index=self.source_frame.index,
            columns=[RTS22Transaction.TRANSACTION_DETAILS_VENUE],
        )

    def _transmission_details_order_transmission_indicator(self) -> pd.DataFrame:
        """
        Static value: False
        """
        return pd.DataFrame(
            data=False,
            index=self.source_frame.index,
            columns=[
                RTS22Transaction.TRANSMISSION_DETAILS_ORDER_TRANSMISSION_INDICATOR
            ],
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """
        Populates from the following:
            SourceColumns.MATCH_SLIP_ID
            SourceColumns.MATCH_NO
            SourceColumns.TRADE_DATE
        """
        temp_df = self.source_frame.loc[
            :,
            [
                SourceColumns.MATCH_SLIP_ID,
                SourceColumns.MATCH_NO,
                SourceColumns.TRADE_DATE,
            ],
        ]
        temp_df[SourceColumns.TRADE_DATE] = temp_df[
            SourceColumns.TRADE_DATE
        ].str.replace("/", "")
        return ConcatAttributes.process(
            source_frame=temp_df,
            params=ParamsConcatAttributes(
                source_attributes=[
                    SourceColumns.MATCH_SLIP_ID,
                    SourceColumns.MATCH_NO,
                    SourceColumns.TRADE_DATE,
                ],
                target_attribute=RTS22Transaction.REPORT_DETAILS_TRANSACTION_REF_NO,
            ),
        )

    def _market_identifiers_instrument(self):
        """
        Generates InstrumentIdentifiers to be used in linking instruments downstream
        """
        market_identifiers_df = pd.concat(
            [
                self.source_frame.loc[
                    :,
                    [
                        SourceColumns.STRIKE_PRICE,
                        SourceColumns.COMMODITY,
                        SourceColumns.CURRENCY,
                    ],
                ],
                self.pre_process_df.loc[
                    :,
                    [
                        TempColumns.ASSET_CLASS,
                        TempColumns.EXPIRY_DATE,
                        TempColumns.OPTION_TYPE,
                    ],
                ],
                self.target_df.loc[
                    :,
                    [
                        RTS22Transaction.TRANSACTION_DETAILS_PRICE_CURRENCY,
                        RTS22Transaction.TRANSACTION_DETAILS_VENUE,
                    ],
                ],
            ],
            axis=1,
        )
        market_identifiers_df[TempColumns.STRIKE_PRICE] = ConvertMinorToMajor.process(
            source_frame=market_identifiers_df,
            params=ConvertMinorToMajorParams(
                source_price_attribute=SourceColumns.STRIKE_PRICE,
                source_ccy_attribute=SourceColumns.CURRENCY,
                target_price_attribute=TempColumns.STRIKE_PRICE,
                cast_to=CastTo.FLOAT,
            ),
        )
        return InstrumentIdentifiers.process(
            source_frame=market_identifiers_df,
            params=InstrumentIdentifiersParams(
                asset_class_attribute=TempColumns.ASSET_CLASS,
                expiry_date_attribute=TempColumns.EXPIRY_DATE,
                currency_attribute=RTS22Transaction.TRANSACTION_DETAILS_PRICE_CURRENCY,
                option_strike_price_attribute=TempColumns.STRIKE_PRICE,
                option_type_attribute=TempColumns.OPTION_TYPE,
                underlying_symbol_attribute=SourceColumns.COMMODITY,
                venue_attribute=RTS22Transaction.TRANSACTION_DETAILS_VENUE,
            ),
        )

    def _market_identifiers_parties(self):
        """
        Generates PartyIdentifiers to be used in linking parties downstream
        """
        return PartyIdentifiers.process(
            source_frame=self.pre_process_df,
            params=PartyIdentifiersParams(
                target_attribute=RTS22Transaction.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=TempColumns.EXECUTING_ENTITY,
                execution_within_firm_identifier=TempColumns.EXECUTION_WITHIN_FIRM,
                investment_decision_within_firm_identifier=TempColumns.INVESTMENT_DECISION_WITHIN_FIRM,
                counterparty_identifier=SourceColumns.COUNTER_PARTY,
                buy_sell_side_attribute=TempColumns.BUY_SELL_INDICATOR,
                buyer_identifier=TempColumns.BUYER,
                seller_identifier=TempColumns.SELLER,
            ),
        )

    def _market_identifiers(self) -> pd.DataFrame:
        """
        Merges InstrumentIdentifiers and PartyIdentifiers results

        Assumes _market_identifiers_instrument() and _market_identifiers_parties()
        have run successfully
        """

        market_identifier_columns = pd.DataFrame(index=self.source_frame.index)

        market_identifier_columns.loc[
            :, RTS22Transaction.MARKET_IDENTIFIERS_INSTRUMENT
        ] = self.target_df.loc[:, RTS22Transaction.MARKET_IDENTIFIERS_INSTRUMENT]
        market_identifier_columns.loc[
            :, RTS22Transaction.MARKET_IDENTIFIERS_PARTIES
        ] = self.target_df.loc[:, RTS22Transaction.MARKET_IDENTIFIERS_PARTIES]

        return MergeMarketIdentifiers.process(
            market_identifier_columns,
            params=MergeMarketIdentifiersParams(
                identifiers_path=RTS22Transaction.MARKET_IDENTIFIERS,
                instrument_path=RTS22Transaction.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=RTS22Transaction.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    def _transaction_details_position_id(self) -> pd.DataFrame:
        """
        Not Implemented
        """

    # Pre-Process Functions
    def _temp_trading_date_time(self) -> pd.DataFrame:
        """
        Temporary column used for:
            RTS22Transaction.TRANSACTION_DETAILS_TRADING_DATE_TIME
        Populates from Additional info file's SourceColumns.TRADE_DATE_TIME
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.TRADE_DATE_TIME,
                source_attribute_format=DateFormats.TRADE_DATE_TIME,
                target_attribute=TempColumns.TRADING_DATE_TIME,
                convert_to=ConvertTo.DATETIME,
            ),
        )

    def _temp_trading_date_time_just_date(self) -> pd.DataFrame:
        """
        Temporary column used for:
            RTS22Transaction.DATE
        Populates from Additional info file's SourceColumns.TRADE_DATE_TIME
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.TRADE_DATE_TIME,
                source_attribute_format=DateFormats.TRADE_DATE_TIME,
                target_attribute=TempColumns.TRADING_DATE_TIME_JUST_DATE,
                convert_to=ConvertTo.DATE,
            ),
        )

    def _temp_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Creates temporary columns to be used in:
            RTS22Transaction.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.BS,
                target_attribute=TempColumns.BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "b": BuySellIndicator.BUYI.value,
                    "s": BuySellIndicator.SELL.value,
                },
            ),
            auditor=self.auditor,
        )

    # Instrument Identifiers Fields
    def _temp_instr_ids_asset_class(self):
        """
        Asset class, assigned according to the type of traded Instrument
        """
        asset_class_future_condition = (
            f"(`{SourceColumns.TYPE}`.fillna('').str.upper().str.fullmatch('F'))"
        )
        asset_class_option_condition = (
            f"(`{SourceColumns.TYPE}`.fillna('').str.upper().str.fullmatch('TC')) | "
            f"(`{SourceColumns.TYPE}`.fillna('').str.upper().str.fullmatch('TP'))"
        )

        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=TempColumns.ASSET_CLASS,
                cases=[
                    Case(
                        query=asset_class_future_condition,
                        value=AssetClass.FUTURE,
                    ),
                    Case(
                        query=asset_class_option_condition,
                        value=AssetClass.OPTION,
                    ),
                ],
            ),
        )

    def _temp_instr_ids_expiry_date(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates from SourceColumns.EXPIRY_DATE
        """
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.PROMPT_DATE,
                source_attribute_format=DateFormats.EXPIRY_DATE,
                target_attribute=TempColumns.EXPIRY_DATE,
                convert_to=ConvertTo.DATE,
            ),
        )

    def _temp_instr_ids_option_type(self) -> pd.DataFrame:
        """
        Temporary Column for InstrumentIdentifiers
        Populates from SourceColumns.TYPE
        """
        option_type_call_condition = (
            f"(`{SourceColumns.TYPE}`.fillna('').str.upper().str.fullmatch('TC'))"
        )
        option_type_put_condition = (
            f"(`{SourceColumns.TYPE}`.fillna('').str.upper().str.fullmatch('TP'))"
        )

        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=TempColumns.OPTION_TYPE,
                cases=[
                    Case(
                        query=option_type_call_condition,
                        value=OptionType.CALL.value,
                    ),
                    Case(
                        query=option_type_put_condition,
                        value=OptionType.PUTO.value,
                    ),
                ],
            ),
        )

    # Party Identifiers Fields
    def _temp_executing_entity(self) -> pd.DataFrame:
        """
        Gets the executing entity value to be used in PartyIdentifiers from the
        AccountFirm record
        """
        tenant_lei_df = GetTenantLEI.process(
            source_frame=self.source_frame,
            params=GetTenantLEIParams(
                target_lei_column=TempColumns.EXECUTING_ENTITY_WITHOUT_PREFIX,
            ),
        )

        tenant_lei_df[TempColumns.EXECUTING_ENTITY] = (
            PartyPrefix.LEI + tenant_lei_df[TempColumns.EXECUTING_ENTITY_WITHOUT_PREFIX]
        )
        return tenant_lei_df

    def _temp_party_ids_buyer(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populated from SourceColumns.COUNTER_PARTY or maps to static XLME
        Adds id/lei prefix to the column
        """
        buyer = MapConditional.process(
            source_frame=pd.concat(
                [
                    self.pre_process_df.loc[:, TempColumns.BUY_SELL_INDICATOR],
                    self.pre_process_df.loc[
                        :, TempColumns.EXECUTING_ENTITY_WITHOUT_PREFIX
                    ],
                    self.source_frame.loc[:, SourceColumns.TRADE_CATEGORY],
                    self.source_frame.loc[:, SourceColumns.PUBLIC_REFERENCE_NUMBER],
                ],
                axis=1,
            ),
            params=MapConditionalParams(
                target_attribute=TempColumns.BUYER,
                cases=[
                    Case(
                        query=f"(`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.BUYI.value}')",
                        attribute=TempColumns.EXECUTING_ENTITY_WITHOUT_PREFIX,
                    ),
                    Case(
                        query=f"(`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.SELL.value}') & (`{SourceColumns.TRADE_CATEGORY}`.str.fullmatch('GIVE-UP EXECUTOR', case=False, na=False))",
                        attribute=SourceColumns.PUBLIC_REFERENCE_NUMBER,
                    ),
                    Case(
                        query=f"(`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.SELL.value}') & ~(`{SourceColumns.TRADE_CATEGORY}`.str.fullmatch('GIVE-UP EXECUTOR', case=False, na=False))",
                        value=Venue.XLME,
                    ),
                ],
            ),
        )
        return add_id_or_lei(buyer, TempColumns.BUYER)

    def _temp_party_ids_seller(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Populated from SourceColumns.COUNTER_PARTY or maps to static XLME
        Adds id/lei prefix to the column
        """
        seller = MapConditional.process(
            source_frame=pd.concat(
                [
                    self.pre_process_df.loc[:, TempColumns.BUY_SELL_INDICATOR],
                    self.pre_process_df.loc[
                        :, TempColumns.EXECUTING_ENTITY_WITHOUT_PREFIX
                    ],
                    self.source_frame.loc[:, SourceColumns.TRADE_CATEGORY],
                    self.source_frame.loc[:, SourceColumns.PUBLIC_REFERENCE_NUMBER],
                ],
                axis=1,
            ),
            params=MapConditionalParams(
                target_attribute=TempColumns.SELLER,
                cases=[
                    Case(
                        query=f"`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.SELL.value}'",
                        attribute=TempColumns.EXECUTING_ENTITY_WITHOUT_PREFIX,
                    ),
                    Case(
                        query=f"`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.BUYI.value}' & `{SourceColumns.TRADE_CATEGORY}`.str.fullmatch('GIVE-UP EXECUTOR', case=False, na=False)",
                        attribute=SourceColumns.PUBLIC_REFERENCE_NUMBER,
                    ),
                    Case(
                        query=f"`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.BUYI.value}' & ~(`{SourceColumns.TRADE_CATEGORY}`.str.fullmatch('GIVE-UP EXECUTOR', case=False, na=False))",
                        value=Venue.XLME,
                    ),
                ],
            ),
        )
        return add_id_or_lei(seller, TempColumns.SELLER)

    def _temp_party_ids_investment_decision_within_firm(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Adds id prefix to the column
        """
        return pd.DataFrame(
            data=(
                PartyPrefix.ID
                + self.source_frame.loc[:, SourceColumns.CLIENT_CODE].fillna(
                    self.source_frame[SourceColumns.TRADER_ID]
                )
            ).values,
            index=self.source_frame.index,
            columns=[TempColumns.INVESTMENT_DECISION_WITHIN_FIRM],
        )

    def _temp_party_ids_execution_within_firm(self) -> pd.DataFrame:
        """
        Temporary Column for PartyIdentifiers
        Adds id prefix to the column
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[
                :, TempColumns.INVESTMENT_DECISION_WITHIN_FIRM
            ].values,
            index=self.pre_process_df.index,
            columns=[TempColumns.EXECUTION_WITHIN_FIRM],
        )
