class SourceColumns:
    BS = "B/S"
    CLIENT_CODE = "Client Code"
    CLIENT_CODE_ADDL_INFO = "Client Code_addl_info"
    COMMODITY = "Commodity"
    COUNTER_PARTY = "Counteryparty"
    CURRENCY = "Currency"
    EXEC_ID_CODE = "Exec ID Code"
    LCH_MATCH_NUMBER = "LCH Match Number"
    CLEARING_SLIP_ID = "Clearing Slip ID"
    MATCH_NO = "Match No"
    MATCH_SLIP_ID = "Match Slip ID"
    PUBLIC_REFERENCE_NUMBER = "Public Reference (Long)"
    PRICE = "Price"
    PROMPT_DATE = "Prompt Date"
    SELECT_MATCH_NO = "Select Match No"
    STRIKE_PRICE = "Strike Price"
    TRADE_CATEGORY = "Trade Category"
    TRADE_DATE = "Trade Date"
    TRADE_TIME = "Trade Time"
    TRADE_DATE_TIME = "Trade Date Time"
    TRADER_ID = "Trader ID"
    TYPE = "Type"
    VENUE = "Venue"
    VOLUME = "Volume"


class TempColumns:
    ASSET_CLASS = "__asset_class__"
    BUY_SELL_INDICATOR = "__buy_sell_indicator__"
    BUYER = "__buyer__"
    BUYER_DECISION_MAKER = "__buyer_decision_maker__"
    COUNTERPARTY = "__counterparty__"
    DESCRIPTION_CURRENCY_1 = "__description_currency_1__"
    DESCRIPTION_CURRENCY_2 = "__description_currency_2__"
    DESCRIPTION_DATE = "__description_date__"
    EXECUTING_ENTITY = "__temp_executing_entity__"
    EXECUTING_ENTITY_WITHOUT_PREFIX = "__temp_executing_entity_without_prefix__"
    EXECUTION_WITHIN_FIRM = "__execution_within_firm__"
    EXPIRY_DATE = "__expiry_date__"
    GIVE_UP_EX_TC = "__give_up_ex_tc__"
    INVESTMENT_DECISION_WITHIN_FIRM = "__investment_decision_within_firm__"
    ISIN = "__isin__"
    NOTIONAL_CURRENCY_1 = "__notional_currency_1"
    NOTIONAL_CURRENCY_2 = "__notional_currency_2"
    OPTION_STRIKE_PRICE = "__option_strike_price__"
    OPTION_TYPE = "__option_type__"
    SELLER = "__seller__"
    SELLER_DECISION_MAKER = "__seller_decision_maker__"
    STRIKE_PRICE = "__strike_price__"
    TEMP_COL_1 = "__temp_col_1__"
    TEMP_COL_2 = "__temp_col_2__"
    TRADER = "__trader__"
    TRADING_DATE_TIME = "__trading_date_time__"
    TRADING_DATE_TIME_JUST_DATE = "__trading_date_time_just_date__"
    TRIGGER_FILE_NAME = "__TRIGGER_FILE_NAME__"
    UNDERLYING_SYMBOL = "__underlying_symbol__"


class AddlInfoColumns:
    TRADE_CATEGORY = "Trade Category"
    VENUE = "Venue"


class DateFormats:
    TRADE_DATE = "%d/%m/%Y"
    TRADE_TIME = "%H:%M"
    TRADE_DATE_TIME = "%d/%m/%Y %H:%M:%S:%f"
    EXPIRY_DATE = "%d/%m/%Y"
