import os

import pandas as pd
from se_core_tasks.currency.convert_minor_to_major import (
    CastTo as ConvertMinorToMajorCastTo,
)
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_attribute import CastTo
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.models import RTS22Transaction
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import OtcPostTradeIndicator
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import ReportStatus
from se_elastic_schema.static.mifid2 import ShortSellingIndicator
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_trades_tasks.abstractions.abstract_rts22_transaction_transformations import (
    AbstractRTS22TransactionsTransformations,
)
from se_trades_tasks.order_and_tr.static import AssetClass
from se_trades_tasks.order_and_tr.static import PartyPrefix
from se_trades_tasks.tr.static import RTS22TransactionColumns

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ConvertMinorToMajorParams,
)
from swarm_tasks.steeleye.tr.feed.julius_baer.static import SourceColumns
from swarm_tasks.steeleye.tr.feed.julius_baer.static import TempColumns
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ConcatAttributesParams,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ConvertDatetimeParams,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as MapAttributeParams
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as MapConditionalParams
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as MapValueParams
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as MergeMarketIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as InstrumentIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.identifiers.party_identifiers import (
    Params as PartyIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.identifiers.party_identifiers import (
    PartyIdentifiers,
)


class JuliusBaerTransformations(AbstractRTS22TransactionsTransformations):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.file_url = os.getenv("SWARM_FILE_URL")
        self.file_name = os.path.basename(self.file_url)

    def pre_process(self):

        self.pre_process_df = pd.concat(
            objs=[
                self.pre_process_df,
                self.get_expiry_date(),
                self.get_strike_price(),
                self.get_asset_class(),
                self.get_buysells(),
                self.get_trade_id(),
                self.get_dates(),
                self.get_price_currency(),
                self.get_quantity_currency(),
            ],
            axis=1,
        )

    def process(self) -> pd.DataFrame:

        self.pre_process()

        self.data_source_name()

        self.date()

        self.meta_model()

        self.report_details_report_status()
        self.report_details_transaction_ref_no()

        self.source_index()
        self.source_key()

        self.traders_algos_waivers_indicators_otc_post_trade_indicator()
        self.traders_algos_waivers_indicators_securities_financing_txn_indicator()

        self.transaction_details_buy_sell_indicator()
        self.transaction_details_complex_trade_component_id()
        self.transaction_details_price()
        self.transaction_details_price_pending()
        self.transaction_details_quantity()
        self.transaction_details_trading_capacity()
        self.transaction_details_trading_date_time()
        self.transaction_details_ultimate_venue()
        self.transaction_details_upfront_payment()
        self.transaction_details_upfront_payment_currency()
        self.transaction_details_venue()

        self.transmission_details_order_transmission_indicator()

        self.market_identifiers_instrument()
        self.market_identifiers_parties()
        self.market_identifiers()

        self.post_process()

        return self.target_df

    def post_process(self):
        self.target_df = pd.concat(
            objs=[
                self.target_df,
                self.pre_process_df.loc[
                    :,
                    [
                        TempColumns.STRIKE_PRICE,
                        TempColumns.EXPIRY_DATE,
                        TempColumns.SHORT_SELLING_INDICATOR,
                        TempColumns.PRICE_CURRENCY,
                        TempColumns.QUANTITY_CURRENCY,
                    ],
                ],
                self.source_frame.loc[
                    :,
                    [
                        SourceColumns.BUYER_ID,
                        SourceColumns.BUYER_DECISION_MAKER_ID,
                        SourceColumns.SELLER_ID,
                        SourceColumns.SELLER_DECISION_MAKER_ID,
                        SourceColumns.UNDERLYING_INDEX_NAME,
                    ],
                ],
                self.get_net_amount(),
                self.get_price_multiplier(),
                self.get_price_notation(),
                self.get_quantity_notation(),
                pd.Series(
                    data=[True] * self.source_frame.shape[0],
                    index=self.source_frame.index,
                    name=TempColumns.INSTR_FB_IS_CREATED_THROUGH_FALLBACK,
                ),
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=["Thornbridge Julius Baer"] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.DATE].values,
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.DATE],
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """
        Creates InstrumentIdentifiers results
        """
        return InstrumentIdentifiers.process(
            source_frame=pd.concat(
                objs=[
                    self.source_frame.loc[
                        :,
                        [
                            SourceColumns.INSTRUMENT_ID,
                            SourceColumns.OPTION_TYPE,
                            SourceColumns.UNDERLYING_INDEX_ID,
                            SourceColumns.UNDERLYING_INDEX_TERM,
                            SourceColumns.UNDERLYING_INSTRUMENT_ID,
                        ],
                    ],
                    self.pre_process_df.loc[
                        :,
                        [
                            TempColumns.EXPIRY_DATE,
                            TempColumns.STRIKE_PRICE,
                            TempColumns.ASSET_CLASS,
                            TempColumns.PRICE_CURRENCY,
                            TempColumns.QUANTITY_CURRENCY,
                        ],
                    ],
                    self.target_df.loc[
                        :,
                        [
                            RTS22TransactionColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                        ],
                    ],
                ],
                axis=1,
            ).fillna(pd.NA),
            params=InstrumentIdentifiersParams(
                asset_class_attribute=TempColumns.ASSET_CLASS,
                currency_attribute=TempColumns.PRICE_CURRENCY,
                notional_currency_2_attribute=TempColumns.QUANTITY_CURRENCY,
                venue_attribute=RTS22TransactionColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                expiry_date_attribute=TempColumns.EXPIRY_DATE,
                isin_attribute=SourceColumns.INSTRUMENT_ID,
                option_strike_price_attribute=TempColumns.STRIKE_PRICE,
                option_type_attribute=SourceColumns.OPTION_TYPE,
                underlying_index_name_attribute=SourceColumns.UNDERLYING_INDEX_ID,
                underlying_index_term_attribute=SourceColumns.UNDERLYING_INDEX_TERM,
                underlying_isin_attribute=SourceColumns.UNDERLYING_INSTRUMENT_ID,
                retain_task_inputs=True,
            ),
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """
        Creates PartyIdentifiers results
        """
        parties_df = pd.DataFrame(
            index=self.source_frame.index,
        )

        parties_df.loc[:, TempColumns.PARTY_EXECUTING_ENTITY] = (
            PartyPrefix.LEI
            + self.source_frame.loc[:, SourceColumns.EXECUTING_ENTITY_ID]
        )

        parties_df.loc[:, TempColumns.PARTY_TRADER] = self.get_labeled_id(
            id_column=SourceColumns.INVESTMENT_DECISION_ID,
            label_column=SourceColumns.INVESTMENT_DECISION_ID_TYPE,
        )

        parties_df.loc[
            :, TempColumns.PARTY_EXECUTION_WITHIN_FIRM
        ] = self.get_labeled_id(
            id_column=SourceColumns.FIRM_EXECUTION_ID,
            label_column=SourceColumns.FIRM_EXECUTION_ID_TYPE,
        )

        parties_df.loc[:, TempColumns.PARTY_BUYER] = self.get_labeled_id(
            id_column=SourceColumns.BUYER_ID, label_column=SourceColumns.BUYER_ID_TYPE
        )

        parties_df.loc[:, TempColumns.PARTY_BUYER_DECISION_MAKER] = self.get_labeled_id(
            id_column=SourceColumns.BUYER_DECISION_MAKER_ID,
            label_column=SourceColumns.BUYER_DECISION_MAKER_ID_TYPE,
        )

        parties_df.loc[:, TempColumns.PARTY_SELLER] = self.get_labeled_id(
            id_column=SourceColumns.SELLER_ID, label_column=SourceColumns.SELLER_ID_TYPE
        )

        parties_df.loc[
            :, TempColumns.PARTY_SELLER_DECISION_MAKER
        ] = self.get_labeled_id(
            id_column=SourceColumns.SELLER_DECISION_MAKER_ID,
            label_column=SourceColumns.SELLER_DECISION_MAKER_ID_TYPE,
        )

        parties_df.loc[:, TempColumns.PARTY_COUNTERPARTY] = MapConditional.process(
            source_frame=pd.concat(objs=[parties_df, self.pre_process_df], axis=1),
            params=MapConditionalParams(
                target_attribute=TempColumns.PARTY_COUNTERPARTY,
                cases=[
                    Case(
                        query=f"`{TempColumns.BUYSELL_INDICATOR}`.astype('str').str.fullmatch('{BuySellIndicator.BUYI.value}',case=False,na=False)",
                        attribute=TempColumns.PARTY_BUYER,
                    ),
                    Case(
                        query=f"`{TempColumns.BUYSELL_INDICATOR}`.astype('str').str.fullmatch('{BuySellIndicator.SELL.value}',case=False,na=False)",
                        attribute=TempColumns.PARTY_SELLER,
                    ),
                ],
            ),
        )

        return PartyIdentifiers.process(
            source_frame=pd.concat(
                objs=[
                    parties_df,
                    self.target_df.loc[
                        :,
                        RTS22TransactionColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                    ],
                ],
                axis=1,
            ),
            params=PartyIdentifiersParams(
                target_attribute=RTS22TransactionColumns.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=TempColumns.PARTY_EXECUTING_ENTITY,
                trader_identifier=TempColumns.PARTY_TRADER,
                counterparty_identifier=TempColumns.PARTY_COUNTERPARTY,
                investment_decision_within_firm_identifier=TempColumns.PARTY_TRADER,
                execution_within_firm_identifier=TempColumns.PARTY_EXECUTION_WITHIN_FIRM,
                buyer_identifier=TempColumns.PARTY_BUYER,
                buyer_decision_maker_identifier=TempColumns.PARTY_BUYER_DECISION_MAKER,
                seller_identifier=TempColumns.PARTY_SELLER,
                use_buy_mask_for_buyer_seller=False,
                use_buy_mask_for_buyer_seller_decision_maker=False,
                seller_decision_maker_identifier=TempColumns.PARTY_SELLER_DECISION_MAKER,
                buy_sell_side_attribute=RTS22TransactionColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
            ),
        )

    def _market_identifiers(self) -> pd.DataFrame:
        """
        Merges InstrumentIdentifiers and PartyIdentifiers results

        Assumes _market_identifiers_instrument() and _market_identifiers_parties()
        have run successfully
        """

        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=MergeMarketIdentifiersParams(
                identifiers_path=RTS22TransactionColumns.MARKET_IDENTIFIERS,
                instrument_path=RTS22TransactionColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=RTS22TransactionColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """
        Static value: RTS22TransactionColumns
        """
        return pd.DataFrame(
            data=[RTS22Transaction.__name__] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.META_MODEL],
        )

    def _report_details_investment_firm_covered_directive(self) -> pd.DataFrame:
        """
        Populated in LinkParties
        """

    def _report_details_report_status(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=[ReportStatus.NEWT.value] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.REPORT_DETAILS_REPORT_STATUS],
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """
        date + trade_id + buySell
        If over 52 characters:
            trade_id + date + buySell (first 52 characters)

        remove non-alphanumeric characters
        """
        # Date first, Trade ID second
        data_id = ConcatAttributes.process(
            source_frame=self.pre_process_df,
            params=ConcatAttributesParams(
                source_attributes=[
                    TempColumns.DATE,
                    TempColumns.TRADE_ID,
                    TempColumns.BUYSELL,
                ],
                target_attribute=TempColumns.REF_NO_ONE,
            ),
        )

        # Trade ID first, Date second
        id_data = ConcatAttributes.process(
            source_frame=self.pre_process_df,
            params=ConcatAttributesParams(
                source_attributes=[
                    TempColumns.TRADE_ID,
                    TempColumns.DATE,
                    TempColumns.BUYSELL,
                ],
                target_attribute=TempColumns.REF_NO_TWO,
            ),
        )

        # cap Trade ID first to 52 characters
        id_data_not_null = id_data.loc[:, TempColumns.REF_NO_TWO].notnull()

        id_data.loc[id_data_not_null, TempColumns.REF_NO_TWO] = (
            id_data.loc[id_data_not_null, TempColumns.REF_NO_TWO].astype(str).str[:52]
        )

        # check which Date first are over 52 characters
        data_id_over_fifty_two = (
            data_id.loc[:, TempColumns.REF_NO_ONE].astype(str).str.len() > 52
        )

        # Map values to target
        trans_ref_no = pd.DataFrame(
            data=[pd.NA] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.REPORT_DETAILS_TRANSACTION_REF_NO],
        )

        trans_ref_no.loc[
            ~data_id_over_fifty_two,
            RTS22TransactionColumns.REPORT_DETAILS_TRANSACTION_REF_NO,
        ] = data_id.loc[~data_id_over_fifty_two, TempColumns.REF_NO_ONE]
        trans_ref_no.loc[
            data_id_over_fifty_two,
            RTS22TransactionColumns.REPORT_DETAILS_TRANSACTION_REF_NO,
        ] = id_data.loc[data_id_over_fifty_two, TempColumns.REF_NO_TWO]

        # remove non-alphanumeric characters
        not_null_ref_no = trans_ref_no.loc[
            :, RTS22TransactionColumns.REPORT_DETAILS_TRANSACTION_REF_NO
        ].notnull()

        trans_ref_no.loc[
            not_null_ref_no, RTS22TransactionColumns.REPORT_DETAILS_TRANSACTION_REF_NO
        ] = (
            trans_ref_no.loc[
                not_null_ref_no,
                RTS22TransactionColumns.REPORT_DETAILS_TRANSACTION_REF_NO,
            ]
            .astype(str)
            .str.replace("[^a-zA-Z0-9]", "", regex=True)
        )

        return trans_ref_no

    def _source_index(self) -> pd.DataFrame:
        """
        Static value: index from the source_frame
        """
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.SOURCE_INDEX],
        )

    def _source_key(self) -> pd.DataFrame:
        """
        Static value: File URL
        """
        return pd.DataFrame(
            data=[self.file_url] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.SOURCE_KEY],
        )

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(self):
        """Implemented downstream in the bundle"""

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.OTC_POST_TRADE_INDICATOR,
                target_attribute=RTS22TransactionColumns.TRADERS_ALGOS_WAIVERS_INDICATORS_OTC_POST_TRADE_IND,
                case_insensitive=True,
                default_value=pd.NA,
                value_map={
                    "ACTX": [OtcPostTradeIndicator.ACTX.value],
                    "ALGO": [OtcPostTradeIndicator.ALGO.value],
                    "BENC": [OtcPostTradeIndicator.BENC.value],
                    "CANC": [OtcPostTradeIndicator.CANC.value],
                    "DUPL": [OtcPostTradeIndicator.DUPL.value],
                    "ILQD": [OtcPostTradeIndicator.ILQD.value],
                    "LRGS": [OtcPostTradeIndicator.LRGS.value],
                    "NLIQ": [OtcPostTradeIndicator.NLIQ.value],
                    "NPFT": [OtcPostTradeIndicator.NPFT.value],
                    "OILQ": [OtcPostTradeIndicator.OILQ.value],
                    "PRIC": [OtcPostTradeIndicator.PRIC.value],
                    "RFPT": [OtcPostTradeIndicator.RFPT.value],
                    "RPRI": [OtcPostTradeIndicator.RPRI.value],
                    "SDIV": [OtcPostTradeIndicator.SDIV.value],
                    "SIZE": [OtcPostTradeIndicator.SIZE.value],
                    "TNCP": [OtcPostTradeIndicator.TNCP.value],
                    "TPAC": [OtcPostTradeIndicator.TPAC.value],
                    "XFPH": [OtcPostTradeIndicator.XFPH.value],
                },
            ),
        )

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        """
        Static value: False
        """
        return pd.DataFrame(
            data=[False] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[
                RTS22TransactionColumns.TRADERS_ALGOS_WAIVERS_INDICATORS_SECRT_FNC_TXN_IND
            ],
        )

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """
        populated as temp column, additional logic downstream
        """

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Populates from Short Selling Indicator
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.BUYSELL_INDICATOR].values,
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _transaction_details_complex_trade_component_id(self) -> pd.Series:
        """Populates from SourceColumns.COMPLEX_TRADE_COMPONENT_ID"""

        not_null = self.source_frame.loc[
            :, SourceColumns.COMPLEX_TRADE_COMPONENT_ID
        ].notnull()

        complex_trade_id = pd.Series(
            data=[pd.NA] * self.source_frame.shape[0],
            index=self.source_frame.index,
            name=RTS22TransactionColumns.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID,
        )

        complex_trade_id[not_null] = (
            self.source_frame.loc[not_null, SourceColumns.COMPLEX_TRADE_COMPONENT_ID]
            .str.replace("[^a-zA-Z]", "")
            .str.upper()
            .replace("", pd.NA)
        )

        return complex_trade_id

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        """
        Populated downstream from linked party info
        """

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """
        Populated as a temp column, additional logic downstream
        """

    def _transaction_details_price(self) -> pd.DataFrame:
        """Populates with SourceColumns.PRICE"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ConvertMinorToMajorParams(
                source_price_attribute=SourceColumns.PRICE,
                source_ccy_attribute=SourceColumns.PRICE_CURRENCY,
                target_price_attribute=RTS22TransactionColumns.TRANSACTION_DETAILS_PRICE,
                cast_to=ConvertMinorToMajorCastTo.ABS,
            ),
        )

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """
        Populated as temp column to go through logic downstream
        """

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """
        Populated as temp column to go through logic downstream
        """

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        """Populates from RTS22TransactionColumns.TRANSACTION_DETAILS_PRICE"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, RTS22TransactionColumns.TRANSACTION_DETAILS_PRICE
            ]
            .isnull()
            .values,
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.TRANSACTION_DETAILS_PRICE_PENDING],
        )

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """Populates with SourceColumns.QUANTITY"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.QUANTITY,
                target_attribute=RTS22TransactionColumns.TRANSACTION_DETAILS_QUANTITY,
                cast_to=CastTo.NUMERIC_ABSOLUTE,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """
        Populated as temp column to go through logic downstream
        """

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """
        Populated as temp column to go through logic downstream
        """

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """Populates from SourceColumns.TRADING_CAPACITY"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.TRADING_CAPACITY,
                target_attribute=RTS22TransactionColumns.TRANSACTION_DETAILS_TRADING_CAPACITY,
                case_insensitive=True,
                default_value=pd.NA,
                value_map={
                    "MTCH": TradingCapacity.MTCH.value,
                    "A": TradingCapacity.MTCH.value,
                    "AOTC": TradingCapacity.AOTC.value,
                    "M": TradingCapacity.AOTC.value,
                    "W": TradingCapacity.AOTC.value,
                    "DEAL": TradingCapacity.DEAL.value,
                    "G": TradingCapacity.DEAL.value,
                    "I": TradingCapacity.DEAL.value,
                    "P": TradingCapacity.DEAL.value,
                    "R": TradingCapacity.DEAL.value,
                },
            ),
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """Populates with SourceColumns.TRADING_DATE_TIME"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.DATETIME].values,
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME],
        )

    def _transaction_details_ultimate_venue(self):
        """Populates with SourceColumns.VENUE"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.VENUE,
                target_attribute=RTS22TransactionColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """Populates with SourceColumns.UPFRONT_PAYMENT"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ConvertMinorToMajorParams(
                source_price_attribute=SourceColumns.UP_FRONT_PAYMENT,
                source_ccy_attribute=SourceColumns.UP_FRONT_PAYMENT_CURRENCY,
                target_price_attribute=RTS22TransactionColumns.TRANSACTION_DETAILS_UPFRONT_PAYMENT,
            ),
        )

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """Populates with SourceColumns.UPFRONT_PAYMENT_CURRENCY"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ConvertMinorToMajorParams(
                source_ccy_attribute=SourceColumns.UP_FRONT_PAYMENT_CURRENCY,
                target_ccy_attribute=RTS22TransactionColumns.TRANSACTION_DETAILS_UPFRONT_PAYMENT_CURRENCY,
            ),
        )

    def _transaction_details_venue(self) -> pd.DataFrame:
        """Populates with SourceColumns.VENUE"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.VENUE,
                target_attribute=RTS22TransactionColumns.TRANSACTION_DETAILS_VENUE,
            ),
            auditor=self.auditor,
        )

    def _transmission_details_order_transmission_indicator(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=[True] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[
                RTS22TransactionColumns.TRANSMISSION_DETAILS_ORDER_TRANSMISSION_INDICATOR
            ],
        )

    # Temp Methods
    def get_expiry_date(self) -> pd.DataFrame:
        return ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.EXPIRY_DATE,
                target_attribute=TempColumns.EXPIRY_DATE,
                convert_to=ConvertTo.DATE,
            ),
        )

    def get_strike_price(self) -> pd.DataFrame:
        """
        Used for instrument identifiers
        and linked instrument override
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ConvertMinorToMajorParams(
                source_price_attribute=SourceColumns.STRIKE_PRICE,
                source_ccy_attribute=SourceColumns.STRIKE_PRICE_CURRENCY,
                target_price_attribute=TempColumns.STRIKE_PRICE,
            ),
        )

    def get_asset_class(self) -> pd.DataFrame:
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.INSTRUMENT_CLASSIFICATION,
                target_attribute=TempColumns.ASSET_CLASS,
                default_value=pd.NA,
                case_insensitive=True,
                value_map={
                    "D": AssetClass.BOND,
                    "F": AssetClass.BOND,
                    "O": AssetClass.BOND,
                },
            ),
        )

    def get_buysells(self) -> pd.DataFrame:
        """
        Map Short Selling Indicator for buySell/buySellIndicator/ShortSelling:
            SESH: 5 / SELL / SESH
            SELL: 2 / SELL / SELL
            else: 1 / BUYI / pd.NA
        """

        buysell = MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.SHORT_SELLING_INDICATOR,
                target_attribute=TempColumns.BUYSELL,
                case_insensitive=True,
                default_value="1",
                value_map={"SESH": "5", "SELL": "2"},
            ),
        )

        buysell_indicator = MapValue.process(
            source_frame=buysell,
            params=MapValueParams(
                source_attribute=TempColumns.BUYSELL,
                target_attribute=TempColumns.BUYSELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "5": BuySellIndicator.SELL.value,
                    "2": BuySellIndicator.SELL.value,
                    "1": BuySellIndicator.BUYI.value,
                },
            ),
        )

        short_selling_indicator = MapValue.process(
            source_frame=buysell,
            params=MapValueParams(
                source_attribute=TempColumns.BUYSELL,
                target_attribute=TempColumns.SHORT_SELLING_INDICATOR,
                case_insensitive=True,
                value_map={
                    "5": ShortSellingIndicator.SESH.value,
                    "2": ShortSellingIndicator.SELL.value,
                    "1": pd.NA,
                },
            ),
        )

        return pd.concat(
            objs=[
                buysell,
                buysell_indicator,
                short_selling_indicator,
            ],
            axis=1,
        )

    def get_trade_id(self) -> pd.DataFrame:
        """
        Upper case Transaction Reference Number
        """
        not_null_trf = self.source_frame.loc[
            :, SourceColumns.TRANSACTION_REFERENCE_NUMBER
        ].notnull()

        trade_id = pd.DataFrame(
            data=[pd.NA] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[TempColumns.TRADE_ID],
        )

        trade_id.loc[not_null_trf, TempColumns.TRADE_ID] = self.source_frame.loc[
            not_null_trf, SourceColumns.TRANSACTION_REFERENCE_NUMBER
        ].str.upper()

        return trade_id

    def get_dates(self) -> pd.DataFrame:
        """
        convert Trading Date Time to expected formats
        """
        datetime = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.TRADING_DATE_TIME,
                target_attribute=TempColumns.DATETIME,
                convert_to=ConvertTo.DATETIME,
                source_attribute_format="%Y-%m-%d %H:%M:%S.%f",
            ),
        )

        date = ConvertDatetime.process(
            source_frame=datetime,
            params=ConvertDatetimeParams(
                source_attribute=TempColumns.DATETIME,
                target_attribute=TempColumns.DATE,
                convert_to=ConvertTo.DATE,
            ),
        )

        return pd.concat(objs=[datetime, date], axis=1)

    def get_price_multiplier(self) -> pd.DataFrame:
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.PRICE_MULTIPLIER,
                target_attribute=TempColumns.PRICE_MULTIPLIER,
                cast_to=CastTo.NUMERIC_ABSOLUTE,
            ),
            auditor=self.auditor,
        )

    def get_net_amount(self) -> pd.DataFrame:
        """Populates from SourceColumns.INSTRUMENT_CLASSIFICATION"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.NET_AMOUNT,
                target_attribute=TempColumns.NET_AMOUNT,
                cast_to=CastTo.NUMERIC_ABSOLUTE,
            ),
            auditor=self.auditor,
        )

    def get_price_currency(self) -> pd.DataFrame:
        """Populates from SourceColumns.PRICE_CURRENCY"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ConvertMinorToMajorParams(
                source_ccy_attribute=SourceColumns.PRICE_CURRENCY,
                target_ccy_attribute=TempColumns.PRICE_CURRENCY,
            ),
        )

    def get_quantity_currency(self) -> pd.DataFrame:
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ConvertMinorToMajorParams(
                source_ccy_attribute=SourceColumns.QUANTITY_CURRENCY,
                target_ccy_attribute=TempColumns.QUANTITY_CURRENCY,
            ),
        )

    def get_price_notation(self) -> pd.DataFrame:
        """Populates from SourceColumns.PRICE_TYPE"""
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=TempColumns.PRICE_NOTATION,
                cases=[
                    Case(
                        query=f"`{SourceColumns.PRICE_TYPE}`.astype('str').str"
                        f".fullmatch('MntryValAmt',case=False,na=False)",
                        value=PriceNotation.MONE.value,
                    ),
                ],
            ),
        )

    def get_quantity_notation(self) -> pd.DataFrame:
        """Populates from SourceColumns.QUANTITY_TYPE"""
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=TempColumns.QUANTITY_NOTATION,
                cases=[
                    Case(
                        query=f"`{SourceColumns.QUANTITY_TYPE}`.str.fullmatch('MntryValAmt', case=False, na=False)",
                        value=QuantityNotation.MONE.value,
                    ),
                    Case(
                        query=f"`{SourceColumns.QUANTITY_TYPE}`.str.fullmatch('Yld', case=False, na=False)",
                        value=QuantityNotation.NOML.value,
                    ),
                    Case(
                        query=f"`{SourceColumns.QUANTITY_TYPE}`.str.fullmatch('Unit', case=False, na=False)",
                        value=QuantityNotation.UNIT.value,
                    ),
                ],
            ),
        )

    # aux methods
    def get_labeled_id(self, id_column: str, label_column: str) -> pd.Series:
        """
        maps the label column against L: lei, T: id
        returns a series with the id column prefixed with correct label
        """
        label = (
            self.source_frame.loc[:, label_column]
            .map({"L": PartyPrefix.LEI, "T": PartyPrefix.ID})
            .fillna(pd.NA)
        )

        return label + self.source_frame.loc[:, id_column]

    # unused methods
    def _report_details_trading_venue_transaction_id_code(self):
        """Not implemented"""

    def _traders_algos_waivers_indicators_waiver_indicator(self):
        """Not implemented"""

    def _transaction_details_cross_indicator(self):
        """Not Implemented"""

    def _transaction_details_derivative_notional_change(self):
        """Not Implemented"""

    def _transaction_details_outgoing_order_addl_info(self):
        """Not Implemented"""

    def _transaction_details_position_effect(self):
        """Not Implemented"""

    def _transaction_details_position_id(self):
        """Not Implemented"""

    def _transaction_details_price_not_applicable(self):
        """Not Implemented"""

    def _transaction_details_record_type(self):
        """Not Implemented"""

    def _transaction_details_settlement_amount(self):
        """Not Implemented"""

    def _transaction_details_settlement_amount_currency(self):
        """Not Implemented"""

    def _transaction_details_settlement_date(self):
        """Not Implemented"""

    def _transaction_details_swap_directionalities(self):
        """Not Implemented"""

    def _transaction_details_traded_quantity(self):
        """Not Implemented"""

    def _transaction_details_trail_id(self):
        """Not Implemented"""
