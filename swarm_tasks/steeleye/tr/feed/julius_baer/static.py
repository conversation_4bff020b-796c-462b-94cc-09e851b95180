class SourceColumns:
    BUYER_COUNTRY_OF_BRANCH = "Buyer Country of Branch"
    BUYER_DECISION_MAKER_DOB = "Buyer Decision Maker DOB"
    BUYER_DECISION_MAKER_FIRST_NAME = "Buyer Decision Maker First Name"
    BUYER_DECISION_MAKER_ID_SUB_TYPE = "Buyer Decision Maker ID Sub Type"
    BUYER_DECISION_MAKER_ID_TYPE = "Buyer Decision Maker ID Type"
    BUYER_DECISION_MAKER_ID = "Buyer Decision Maker ID"
    BUYER_DECISION_MAKER_SURNAME = "Buyer Decision Maker Surname"
    BUYER_DOB = "Buyer DOB"
    BUYER_FIRST_NAME = "Buyer First Name"
    BUYER_ID_SUB_TYPE = "Buyer ID Sub Type"
    BUYER_ID_TYPE = "Buyer ID Type"
    BUYER_ID = "Buyer ID"
    BUYER_SURNAME = "Buyer Surname"
    BUYER_TRANSMITTER_ID = "Buyer Transmitter ID"
    COMMODITY_DERIVATIVE_INDICATOR = "Commodity Derivative Indicator"
    COMPLEX_TRADE_COMPONENT_ID = "Complex Trade Component ID"
    COUNTRY_OF_BRANCH = "Country of Branch"
    DATA_CATEGORY = "Data Category"
    DELIVERY_TYPE = "Delivery Type"
    DERIVATIVE_NOTIONAL_CHANGE = "Derivative Notional Change"
    EXECUTING_ENTITY_ID = "Executing Entity ID"
    EXPIRY_DATE = "Expiry Date"
    FIRM_EXECUTION_COUNTRY_OF_BRANCH = "Firm Execution Country of Branch"
    FIRM_EXECUTION_ID_SUB_TYPE = "Firm Execution ID Sub Type"
    FIRM_EXECUTION_ID_TYPE = "Firm Execution ID Type"
    FIRM_EXECUTION_ID = "Firm Execution ID"
    INSTRUMENT_CLASSIFICATION = "Instrument Classification"
    INSTRUMENT_ID_TYPE = "Instrument ID Type"
    INSTRUMENT_ID = "Instrument ID"
    INSTRUMENT_NAME = "Instrument Name"
    INTERNAL_CLIENT_IDENTIFICATION = "Internal Client Identification"
    INVESTMENT_DECISION_COUNTRY_OF_BRANCH = "Investment Decision Country of Branch"
    INVESTMENT_DECISION_ID_SUB_TYPE = "Investment Decision ID Sub Type"
    INVESTMENT_DECISION_ID_TYPE = "Investment Decision ID Type"
    INVESTMENT_DECISION_ID = "Investment Decision ID"
    INVESTMENT_FIRM_INDICATOR = "Investment Firm Indicator"
    MATURITY_DATE = "Maturity Date"
    NET_AMOUNT = "Net Amount"
    NOTIONAL_CURRENCY_1 = "Notional Currency 1"
    NOTIONAL_CURRENCY_2_TYPE = "Notional Currency 2 Type"
    NOTIONAL_CURRENCY_2 = "Notional Currency 2"
    OPTION_STYLE = "Option Style"
    OPTION_TYPE = "Option Type"
    ORDER_TRANSMISSION_INDICATOR = "Order Transmission Indicator"
    OTC_POST_TRADE_INDICATOR = "OTC Post Trade Indicator"
    PRICE_CURRENCY = "Price Currency"
    PRICE_MULTIPLIER = "Price Multiplier"
    PRICE_TYPE = "Price Type"
    PRICE = "Price"
    QUANTITY_CURRENCY = "Quantity Currency"
    QUANTITY_TYPE = "Quantity Type"
    QUANTITY = "Quantity"
    REPORT_STATUS = "Report Status"
    SELLER_COUNTRY_OF_BRANCH = "Seller Country of Branch"
    SELLER_DECISION_MAKER_DOB = "Seller Decision Maker DOB"
    SELLER_DECISION_MAKER_FIRST_NAME = "Seller Decision Maker First Name"
    SELLER_DECISION_MAKER_ID_SUB_TYPE = "Seller Decision Maker ID Sub Type"
    SELLER_DECISION_MAKER_ID_TYPE = "Seller Decision Maker ID Type"
    SELLER_DECISION_MAKER_ID = "Seller Decision Maker ID"
    SELLER_DECISION_MAKER_SURNAME = "Seller Decision Maker Surname"
    SELLER_DOB = "Seller DOB"
    SELLER_FIRST_NAME = "Seller First Name"
    SELLER_ID_SUB_TYPE = "Seller ID Sub Type"
    SELLER_ID_TYPE = "Seller ID Type"
    SELLER_ID = "Seller ID"
    SELLER_SURNAME = "Seller Surname"
    SELLER_TRANSMITTER_ID = "Seller Transmitter ID"
    SFT_INDICATOR = "SFT Indicator"
    SHORT_SELLING_INDICATOR = "Short Selling Indicator"
    STRIKE_PRICE_CURRENCY = "Strike Price Currency"
    STRIKE_PRICE_TYPE = "Strike Price Type"
    STRIKE_PRICE = "Strike Price"
    SUBMITTING_ENTITY_ID = "Submitting Entity ID"
    TRADING_CAPACITY = "Trading Capacity"
    TRADING_DATE_TIME = "Trading Date Time"
    TRANSACTION_REFERENCE_NUMBER = "Transaction Reference Number"
    UNDERLYING_INDEX_ID = "Underlying Index ID"
    UNDERLYING_INDEX_NAME = "Underlying Index Name"
    UNDERLYING_INDEX_TERM = "Underlying Index Term"
    UNDERLYING_INSTRUMENT_ID = "Underlying Instrument ID"
    UP_FRONT_PAYMENT_CURRENCY = "Up-Front Payment Currency"
    UP_FRONT_PAYMENT = "Up-Front Payment"
    UV_INDEX_CLASSIFICATION = "UV Index Classification"
    UV_INSTRUMENT_CLASSIFICATION = "UV Instrument Classification"
    VENUE_TRANSACTION_ID = "Venue Transaction ID"
    VENUE = "Venue"
    WAIVER_INDICATOR = "Waiver Indicator"


class TempColumns:
    ASSET_CLASS = "__asset_class__"
    BUYSELL = "__buysell__"
    BUYSELL_INDICATOR = "__buysell_indicator__"
    DATE = "__date__"
    DATETIME = "__datetime__"
    EXPIRY_DATE = "__expiry_date__"
    NET_AMOUNT = "__net_amount__"
    PRICE_CURRENCY = "__price_currency__"
    PRICE_MULTIPLIER = "__price_multiplier__"
    PRICE_NOTATION = "__price_notation__"
    QUANTITY_CURRENCY = "__quantity_currency__"
    QUANTITY_NOTATION = "__quantity_notation__"
    REF_NO_ONE = "__ref_no_one__"
    REF_NO_TWO = "__ref_no_two__"
    SHORT_SELLING_INDICATOR = "__short_selling_indicator__"
    STRIKE_PRICE = "__strike_price__"
    TRADE_ID = "__trade_id__"

    INSTR_FB_IS_CREATED_THROUGH_FALLBACK = "__instr_fb_is_created_through_fb__"

    PARTY_BUYER = "__party_buyer__"
    PARTY_BUYER_DECISION_MAKER = "__party_buyer_decision_maker__"
    PARTY_COUNTERPARTY = "__party_counterparty__"
    PARTY_SELLER = "__party_seller__"
    PARTY_SELLER_DECISION_MAKER = "__party_seller_decision_maker__"
    PARTY_EXECUTING_ENTITY = "__party_executing_entity__"
    PARTY_EXECUTION_WITHIN_FIRM = "__party_execution_within_firm__"
    PARTY_TRADER = "__party_trader__"
