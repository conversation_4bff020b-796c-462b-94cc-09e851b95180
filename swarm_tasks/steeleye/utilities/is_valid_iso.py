from enum import Enum
from typing import Callable
from typing import Optional

import pandas as pd
from pydantic import Field
from se_elastic_schema.validators.iso.isin import ISIN
from se_elastic_schema.validators.iso.iso_country import ISOCountry
from se_elastic_schema.validators.iso.iso_currency import ISOCurrency
from se_elastic_schema.validators.iso.lei import LEI
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class ISOType(str, Enum):
    COUNTRY = "COUNTRY"
    CURRENCY = "CURRENCY"
    ISIN = "ISIN"
    LEI = "LEI"


ISO_TO_FUNC_MAP = {
    ISOType.COUNTRY: ISOCountry.validate_country_code,
    ISOType.CURRENCY: ISOCurrency.validate_currency_code,
    ISOType.ISIN: ISIN.validate_isin_code,
    ISOType.LEI: LEI.validate_lei_code,
}


class Params(BaseParams):
    source_attribute: str = Field(..., description="Source column")
    target_attribute: str = Field(..., description="Target column")
    iso_type: ISOType = Field(..., description="ISO Type to validate")


class IsValidISO(TransformBaseTask):
    """
    Returns a dataframe with the target column as a boolean series where
    the values are valid according to param.iso_type.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[Params] = None,
        **kwargs
    ) -> pd.DataFrame:
        target = pd.DataFrame(index=source_frame.index)

        data = source_frame[params.source_attribute].astype(str).fillna("")

        iso_class: Callable = ISO_TO_FUNC_MAP[params.iso_type]

        target[params.target_attribute] = data.map(lambda x: iso_class(x).is_valid)

        return target
