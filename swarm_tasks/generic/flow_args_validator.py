import importlib
import json
from typing import Any
from typing import Optional

from pydantic import BaseModel
from pydantic import Field
from pydantic import ValidationError
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams):
    flow_args_model: str = Field(
        ...,
        description="Reference to the pydantic model class representing the flow args.",
    )


class AbstractFlowArgs(BaseModel):
    """
    This is the abstract class all FLowArgs for each flow should inherit from.
    """

    class Config:
        extra = "forbid"  # by default these classes should not allow extra fields

    def get(self, name: str, default=None) -> Any:
        return self.__getattribute__(name) or default


class FlowArgsValidator(BaseTask):
    """
    Validates the FlowArgs for any flow
    Imports the flow_args_model dynamically
    """

    params_class = Params

    def execute(
        self, params: Optional[Params] = None, flow_args: Optional[str] = None, **kwargs
    ) -> AbstractFlowArgs:

        try:
            cls = dynamic_import(params.flow_args_model)
            flow_args_obj: AbstractFlowArgs = cls(**json.loads(flow_args))
        except ValidationError as e:
            self.logger.error(
                f"Failed to unpack {params.flow_args_model} from {flow_args}"
            )
            raise e

        return flow_args_obj


def dynamic_import(name: str):
    components = name.split(".")
    mod = importlib.import_module(".".join(components[:-1]))
    return getattr(mod, components[-1])
