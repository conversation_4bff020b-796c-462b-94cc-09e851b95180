import pandas as pd
from prefect import context
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):
    source_attribute: str = Field(
        ..., description="Source column which contains the delimited text"
    )
    target_attribute: str = Field(
        ...,
        description="Name of the target attribute which will contain the result of extracting"
        "a part from the delimited text",
    )
    delimiter: str = Field(
        ..., description="Delimiter which is used to split the string"
    )
    index_of_part_to_extract: int = Field(
        defalult=0,
        description="Index of part of delimited text to extract after splitting it."
        "By default, the first part is returned (0-based index)",
    )
    null_if_no_delimiter: bool = Field(
        False,
        description="Set target attribute to null if there is no delimiter if True"
        "If False (default), returns the whole string if no delimiter",
    )


class ExtractPartFromDelimitedText(TransformBaseTask):
    """
    This task takes a source frame containing a column with delimited text, and
    extracts a part of the delimited text based on the index_of_part_to_extract
    and delimiter params. It returns a dataframe with just the target_attribute
    which contains the extracted text.

    Examples:
    source_frame = pd.DataFrame({params.source_attribute:["VFF-17SEP21P-12", "F1"]})

    params.delimiter='-', params.index_of_part_to_extract=0

    In this case, if null_if_no_delimiter is False,
    target = pd.DataFrame({params.target_attribute: "VFF", "F1"})

    If null_if_no_delimiter is true,
    target = pd.DataFrame({params.target_attribute: "VFF", pd.NA})
    """

    params_class = Params

    def execute(
        self, source_frame: pd.DataFrame = None, params: Params = None, **kwargs
    ) -> pd.DataFrame:
        return self.process(
            source_frame=source_frame, params=params, logger=self.logger
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        logger=context.get("logger"),
    ):

        target = pd.DataFrame(index=source_frame.index)
        target[params.target_attribute] = pd.NA
        if source_frame.empty or params.source_attribute not in source_frame.columns:
            logger.warning(
                "Source Frame empty/Source attribute not in source frame,"
                " returning empty data frame"
            )
            return target
        source_attribute_not_null_mask = source_frame.loc[
            :, params.source_attribute
        ].notnull()

        if params.null_if_no_delimiter:
            target.loc[source_attribute_not_null_mask, params.target_attribute] = (
                source_frame.loc[
                    source_attribute_not_null_mask, params.source_attribute
                ]
                .str.split(params.delimiter)
                .apply(
                    lambda x: x[params.index_of_part_to_extract]
                    if len(x) > 1
                    else pd.NA
                )
            )
        else:
            target.loc[source_attribute_not_null_mask, params.target_attribute] = (
                source_frame.loc[
                    source_attribute_not_null_mask, params.source_attribute
                ]
                .str.split(params.delimiter)
                .apply(
                    lambda x: x[params.index_of_part_to_extract] if len(x) > 1 else x[0]
                )
            )
        return target
