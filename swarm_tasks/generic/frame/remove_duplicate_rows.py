from typing import List
from typing import Optional

import pandas as pd
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):
    subset: List[str] = Field(
        ...,
        description="List of columns based on which duplicate rows are identified",
    )
    query: Optional[str] = Field(
        None,
        description="Query to be applied before removing duplicates",
    )
    sort_by_column: Optional[str] = Field(
        None,
        description="Sort based on a certain column before removing duplicates. This can be required as "
        "there can be use cases wherein it's required to keep the first record, or the last record that arrives "
        "based on time etc.",
    )
    reset_index: bool = Field(
        default=False,
        description="If True, it resets the index of the target df. It is false by default",
    )


class RemoveDuplicateRows(TransformBaseTask):
    """This Task removes duplicate rows based on input subset of columns.
    If query is provided, applies the duplicated check to the rows that qualifies the
    query condition only."""

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        **kwargs,
    ) -> pd.DataFrame:
        if source_frame.empty:
            return source_frame

        df = source_frame.copy()
        if params.sort_by_column:
            df = df.sort_values(params.sort_by_column)
        for col in params.subset:
            if col not in df.columns:
                self.logger.warn(f"source_frame is missing required col:{col}")
                return df

        # filter rows if params.query is passed
        filter_mask = pd.Series(True, index=df.index)
        if params.query:
            filter_mask = df.eval(params.query, engine="python")

        # default duplicated mask
        duplicated_mask = pd.Series(False, index=df.index)

        # identify duplicates and return non-duplicated rows
        duplicated_mask.loc[filter_mask] = df.loc[filter_mask].duplicated(
            subset=params.subset
        )
        target = df.loc[~duplicated_mask]
        if params.reset_index:
            target = target.reset_index(drop=True)
        return target
