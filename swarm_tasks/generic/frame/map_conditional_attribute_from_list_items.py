import pandas as pd
from se_core_tasks.map.map_conditional_attribute_from_list_items import (
    Params as GenericParams,
)
from se_core_tasks.map.map_conditional_attribute_from_list_items import (
    run_map_conditional_attribute_from_list_items,
)
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class MapConditionalAttributeFromListItems(TransformBaseTask):
    """
    This task creates the `target_attribute` column based on the relationship between
    `source_attribute_for_pattern_search` and `source_attribute_for_mapping`
    - two columns with 'object' datatype (lists)
    Logic:
    For each row of `source_attribute_for_pattern_search` column:
        Search for the `regex_pattern` expression in the list of values
        If a match is successful, access the same list index in the `source_attribute_for_mapping` column
        and store that value in the `target_attribute` column, pd.NA if no match occurs

    Example:

    source_frame:
    df = pd.DataFrame(
        {
            "SecurityAltID": [
                ["isin1", "not_isin", "not_isin"],
                ["not_isin", "isin2", "not_isin"],
                ["not_isin", "not_isin", "isin3"],
            ],
            "SecurityAltIDSource": [["4", "1", "1"], ["1", "4", "1"], ["1", "1", "4"]],
        }
    )

    params:
    source_attribute_for_pattern_search="SecurityAltIDSource"
    source_attribute_for_mapping="SecurityAltID"
    regex_pattern="^4$"
    target_attribute="__isin__"

    Output:
    pd.DataFrame({"__isin__": ["isin1", "isin2", "isin3"]})
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            params=params,
            auditor=self.auditor,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame,
        params: GenericParams,
        auditor=None,
    ) -> pd.DataFrame:

        return run_map_conditional_attribute_from_list_items(
            source_frame=source_frame,
            params=params,
            auditor=auditor,
        )
