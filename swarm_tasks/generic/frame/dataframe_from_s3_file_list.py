import logging
from typing import List
from typing import Optional

import pandas as pd
from prefect.engine import signals
from pydantic import Field
from se_core_tasks.core.core_dataclasses import S3File
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask
from swarm.task.transform.result import TransformResult


class Params(BaseParams):
    output_file_url_column: str = Field(
        default="file_url",
        description="Name of the column containing the file url in the output dataframe",
    )
    output_local_filepath_column: str = Field(
        default="local_filepath",
        description="Name of the column containing the local filepath (file which is to be/has been uploaded)"
        " in the output dataframe",
    )


class DataFrameFromS3FileList(BaseTask):
    """Class which takes as input a list of S3 File objects and returns a
    Transform result whose data frame has 2 columns:
    Column 1 contains the extract s3 URL (s3://{s3_file.bucket_name}/{s3_file.key_name})
    Column 2 contains the local (temp) file path
    """

    params_class = Params

    def execute(
        self,
        s3_file_list: List[S3File] = None,
        params: Optional[Params] = None,
        **kwargs,
    ) -> TransformResult:

        return self.process(
            s3_file_list=s3_file_list, params=params, logger=self.logger
        )

    @classmethod
    def process(
        cls,
        s3_file_list: List[S3File] = None,
        params: Optional[Params] = None,
        logger: logging.Logger = logging.getLogger(__name__),
    ) -> TransformResult:
        if not s3_file_list:
            raise signals.FAIL("s3_file_list empty")
        file_list = [
            [
                f"s3://{s3_file.bucket_name}/{s3_file.key_name}",
                s3_file.file_path.as_posix(),
            ]
            for s3_file in s3_file_list
        ]

        logger.info(
            f"S3 file list containing {len(file_list)} items converted to a data frame"
        )

        df = pd.DataFrame(
            data=file_list,
            columns=[
                params.output_file_url_column,
                params.output_local_filepath_column,
            ],
        )
        return TransformResult(target=df, batch_index=0)
