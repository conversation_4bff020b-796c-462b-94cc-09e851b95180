import pandas as pd
from se_core_tasks.currency.convert_minor_to_major import Params as GenericParams
from se_core_tasks.currency.convert_minor_to_major import run_convert_minor_to_major
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class ConvertMinorToMajor(TransformBaseTask):

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            params=params,
            auditor=self.auditor,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame,
        params: GenericParams,
        auditor=None,
    ) -> pd.DataFrame:

        return run_convert_minor_to_major(
            source_frame=source_frame,
            params=params,
            auditor=auditor,
        )
