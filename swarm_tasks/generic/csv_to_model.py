from typing import Optional

import pandas as pd
from prefect.engine.signals import SKIP
from se_core_tasks.transform.csv2model.record_validator import Params as GenericParams
from se_core_tasks.transform.csv2model.record_validator import (
    run_csv_to_model_record_validator,
)
from se_core_tasks.transform.csv2model.record_validator import (
    SkipIfModelFieldNotPopulated,
)
from se_core_tasks.transform.csv2model.record_validator import SkipIfModelNotResolved
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class CsvToModel(TransformBaseTask):
    """
    Task which processes a CSV file, resolves the model and then run validations on it.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[Params] = None,
        **kwargs,
    ) -> pd.DataFrame:

        try:

            result = run_csv_to_model_record_validator(
                source_frame=source_frame,
                params=params,
                auditor=self.auditor,
                logger=self.logger,
            )

        except (SkipIfModelFieldNotPopulated, SkipIfModelNotResolved) as ex:
            raise SKIP(ex.message)

        return result
