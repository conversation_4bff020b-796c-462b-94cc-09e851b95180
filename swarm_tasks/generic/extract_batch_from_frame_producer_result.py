from prefect.engine.signals import FAIL
from se_core_tasks.io.read.extract_batch_from_frame_producer_result import (
    FailIfFrameProducerResultNone,
)
from se_core_tasks.io.read.extract_batch_from_frame_producer_result import (
    run_extract_batch_from_frame_producer_result,
)
from swarm.task.base import BaseTask
from swarm.task.io.read.result import FrameProducerResult


class ExtractBatchFromFrameProducerResult(BaseTask):
    """
    Resolves the batch number from FrameProducerResult so that we do not store the
    Batch Producer results in memory till the end of flow execution.
    """

    def execute(
        self,
        frame_producer_result: FrameProducerResult = None,
        **kwargs,
    ) -> int:

        try:
            result = run_extract_batch_from_frame_producer_result(
                frame_producer_result=frame_producer_result,
                auditor=self.auditor,
                logger=self.logger,
            )
        except FailIfFrameProducerResultNone:
            raise FAIL("Could not resolve batch number from frame producer result.")

        return result
