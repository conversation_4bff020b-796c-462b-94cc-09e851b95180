from typing import Any
from typing import Dict
from typing import Optional

from prefect.engine.signals import SKIP
from pydantic import Field
from se_core_tasks.generic.get_key_value_from_dict import FailMissingKey
from se_core_tasks.generic.get_key_value_from_dict import Params as GenericParams
from se_core_tasks.generic.get_key_value_from_dict import run_get_key_value_from_dict
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams, GenericParams):
    skip_if_missing = Field(
        default=False,
        description=(
            "Where a skip or fail exception should be raised when `key` is missing."
            "`True` will raise a skip exception, `False` will raise a fail exception"
        ),
    )


class GetKeyValueFromDict(BaseTask):

    params_class = Params

    def execute(
        self,
        value: Optional[Dict] = None,
        params: Optional[Params] = None,
        **kwargs,
    ) -> Any:
        try:
            return run_get_key_value_from_dict(
                params=params,
                value=value,
            )
        except FailMissingKey as e:
            if params.skip_if_missing:
                raise SKIP(e.message)

            raise
