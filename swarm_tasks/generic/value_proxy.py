from typing import Optional
from typing import Union

from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams):
    value: Union[str, int, float, bool]


class ValueProxy(BaseTask):
    """
    This Task implements a basic value proxy.
    params.value will be used in a flow to define which tasks to execute.
    This is necessary to avoid duplicating flows, based solely on the need of complex mapping instructions.
    To be used as exemplified in flow id: tr-universal-steeleye-trade-blotter
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        **kwargs,
    ) -> Union[str, int, float, bool]:

        return params.value
