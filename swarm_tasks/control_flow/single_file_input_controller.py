from typing import Optional

import pandas as pd
from prefect.engine.signals import FAIL
from se_core_tasks.controllers.single_file_input_controller import FailNoPatternMatched
from se_core_tasks.controllers.single_file_input_controller import (
    Params as SingleFileInputControllerParams,
)
from se_core_tasks.controllers.single_file_input_controller import (
    run_single_file_input_controller,
)
from swarm.task.base import BaseParams
from swarm.task.transform.base import BaseTask


class Params(BaseParams, SingleFileInputControllerParams):
    pass


class SingleFileInputController(BaseTask):
    """
    This task checks for the file pointed to by the file_url matches the pattern(s) in
    params.pattern. If it does:
        if copy_file = False (default)
            - it returns the file url as a value of a single-column data frame,
              and writes it to S3. This file triggers another flow.
              NOTE:  that the output file's name will be the <current timestamp>.csv

        if copy_file = True:
            - it copies the input file with the same file name to the provided
              target_s3_key_prefix in the same bucket.

    If the file pointed to by the file_url does not match the patterns(s) in params.pattern,
    a FAIL signal is raised, unless the file follows the patterns in
    list_of_files_regex_for_which_empty_frame_returned (in this case, an empty data frame is
    returned).

    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        file_url: str = None,
        **kwargs,
    ) -> Optional[pd.DataFrame]:
        try:
            return run_single_file_input_controller(
                file_uri=file_url,
                params=params,
            )
        except FailNoPatternMatched:
            raise FAIL(
                f"File name: {file_url} does not match any of the expected input patterns: {params.list_of_files_regex}"
            )
