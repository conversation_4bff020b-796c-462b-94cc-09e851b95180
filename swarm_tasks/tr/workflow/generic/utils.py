import logging
from typing import List
from typing import Optional

import boto3
from se_elastic_schema.models import RTS22TransactionReport
from se_elastic_schema.models import TenantConfiguration
from swarm.client.record_handler_helper import init_record_handler
from swarm.conf import Settings

from swarm_tasks.tr.workflow.generic.static import ScheduleFields


def get_tenant_configuration(es, logger: logging.Logger) -> dict:
    """
    Fetch tenant configuration information from ES

    :param es: ElasticSearch client
    :param logger: Contextual Logger
    :return: TenantConfiguration dictionary
    """
    logger.info("fetching tenant configuration")
    tenant = Settings.tenant
    record_handler = init_record_handler(client=es.client, tenant=tenant)
    tenant_config = record_handler.get(
        index=TenantConfiguration.get_elastic_index_alias(tenant=tenant),
        model="TenantConfiguration",
        doc_id=Settings.realm,
    )
    return tenant_config


def get_rts22_transaction_report(es, report_id: str, logger: logging.Logger) -> dict:
    """
    Query ES and fetch RTS22 transaction report details for a given report ID

    :param es: ElasticSearch client
    :param report_id: RTS22 Transaction Report identifier (matches ES doc_id)
    :param logger: Contextual Logger
    :return: Dictionary containing a RTS22TransactionReport record
    """

    logger.info(f"fetching rts22 transaction report details for report id: {report_id}")
    tenant = Settings.tenant
    record_handler = init_record_handler(client=es.client, tenant=tenant)
    index = RTS22TransactionReport.get_elastic_index_alias(tenant=tenant)
    report_details = record_handler.get(
        index=index,
        model="RTS22TransactionReport",
        doc_id=report_id.lower(),
    )
    return report_details


def amazon_ses_send_email(
    source: str,
    source_arn: str,
    destinations: List[str],
    raw_message: dict,
    logger: logging.Logger,
):
    """
    Amazon SES - Amazon Simple Email Service to send emails via Boto3 SES Client

    :param source: Email author
    :param source_arn: Email author Amazon Resource Name
    :param destinations: List of email recipients
    :param raw_message: Email content
    :param logger: Contextual Logger
    """

    ses = boto3.client(
        "ses",
        aws_access_key_id=Settings.secrets_store.get_secret("SES_ACCESS_KEY_ID"),
        aws_secret_access_key=Settings.secrets_store.get_secret(
            "SES_SECRET_ACCESS_KEY"
        ),
    )

    logger.info("sending email")
    response = ses.send_raw_email(
        Source=source,
        SourceArn=source_arn,
        Destinations=destinations,
        RawMessage=raw_message,
    )
    logger.info(f"response after email has been sent: {response}")


def get_schedule_config(
    tenant_config: dict,
    realm: str,
    logger: logging.Logger,
    strict: bool = True,
) -> Optional[dict]:
    """
    Perform preliminary validations to assess that TenantConfiguration has the necessary parameters

    :param tenant_config: Bespoke TenantConfiguration
    :param realm: Bespoke tenant realm
    :param logger: Contextual Logger
    :param strict: By default, `trSchedule.scheduleDaily` must be True;
    however, this constraint can be relaxed - NoResponseFromArm flows
    may be triggered without automating the TR Scheduler
    :return: TrScheduler dict if it passes validation, None otherwise
    """

    if not tenant_config:
        logger.warning(f"no tenant configuration for {realm}")
        return

    schedule = tenant_config.get(ScheduleFields.TR_SCHEDULE)

    if not schedule:
        logger.warning(f"{realm} does not have TR Schedule activated")
        return

    if strict and not schedule.get(ScheduleFields.SCHEDULE_DAILY):
        logger.warning(f"{realm} does not have TR daily schedule activated")
        return

    return schedule
