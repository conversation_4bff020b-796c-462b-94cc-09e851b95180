class DatetimeFormats:

    READABLE_UTC = "%H:%M:%S %a %d %b %Y"
    UTC_ZULU = "%Y-%m-%dT%H:%M:%S.%fZ"


class ElasticSearchQueryTerms:
    MODEL = "&model"
    REPORTABLE_STATUS = "REPORTABLE_STATUS"
    RTS22_TRANSACTION = "RTS22Transaction"
    RTS22_TRANSACTION_REPORT = "RTS22TransactionReport"
    SUBMITTED = "submitted"
    TRADING_DATE_TIME = "transactionDetails.tradingDateTime"
    VALIDATION_STATUS = "VALIDATION_STATUS"
    WORKFLOW_IS_REPORTED = "workflow.isReported"
    WORKFLOW_STATUS = "workflow.status"
    WORKFLOW_VALIDATION_STATUS = "workflow.validationStatus"


class RTS22TransactionReportFields:
    ARM = "arm"
    NUMBER_TRADES_REPORTED = "reported"
    REPORT_ID = "reportId"
    RESPONSE_RECEIVED_TIMESTAMP = "responseReceived"
    SUBMITTED_TIMESTAMP = "submitted"


class ScheduleFields:
    NOTIFY_IF_NO_RESPONSE = "notifyIfNoARMResponseWithin"
    NOTIFY_IF_NO_RESPONSE_HOURS = "notifyIfNoARMResponseWithinHours"
    NOTIFY_WITH_SUMMARY_BEFORE_SUBMISSION = "notifyWithSummaryBeforeSubmission"
    NOTIFY_WITH_SUMMARY_BEFORE_SUBMISSION_HOURS = (
        "notifyWithSummaryBeforeSubmissionHoursBefore"
    )
    SCHEDULE_DAILY = "scheduleDaily"
    SCHEDULE_DAILY_HOUR = "scheduleDailyHour"
    SCHEDULE_DAILY_MINUTE = "scheduleDailyMinute"
    SCHEDULE_RECIPIENTS = "recipients"
    TR_SCHEDULE = "trSchedule"
