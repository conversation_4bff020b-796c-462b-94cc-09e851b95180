import copy
import itertools
import logging
from typing import List
from typing import Optional

import pandas as pd
import pendulum
from addict import Dict
from prefect.engine import signals
from pydantic import Field
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_elastic_schema.static.reference import DataSourceModel
from se_trades_tasks.order_and_tr.fix.dataclasses.fix_parser_dataclasses import (
    FixParserResult,
)
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask
from swarm.task.io.read import FrameProducerResult

from swarm_tasks.utilities.data_util import abs_or_none
from swarm_tasks.utilities.data_util import formatted_date_time_string
from swarm_tasks.utilities.data_util import is_empty
from swarm_tasks.utilities.dict_util import stripper
from swarm_tasks.utilities.fix.fix_util import fix_getter
from swarm_tasks.utilities.fix.multi_leg import MultilegHandler


class StaticFields:
    FILE_URL = "S3FileURL"
    FUT = "future"
    INSTRUMENT = "Instrument"
    MARKET = "Market"
    NEWO = "0"
    OPT = "option"
    ORDER_QTY_DATA = "OrderQtyData"
    ORDER_STATE = "OrderState"
    ORD_STATUS = "OrdStatus"
    PARTY_ROLE_SEPARATOR = "PartyRole_"
    REGULATORY_TRADE_ID_GRP = "RegulatoryTradeIDGrp"
    RTS22_TRANSACTION = "RTS22Transaction"
    STANDARD_HEADER = "StandardHeader"
    TRD_CAP_RPT_SIDE_GRP = "TrdCapRptSideGrp"


class OrderDictFields:

    NEWO_BLANK_FIELDS = dict(
        LastPx=0.0,
        LastQty=0.0,
        CumQty=0.0,
        AvgPx=0.0,
        TradeID=None,
        LastMkt=None,
        StandardHeader=dict(MsgSeqNum=None),
        model=DataSourceModel.ORDER.value,
    )

    FILL_BLANK_FIELDS = dict(LeavesQty=0.0, model=StaticFields.ORDER_STATE)


class AuxiliaryData:

    BUY = {"1", "3", "9", "A", "D", "F", "B", "BUY", "Buy", "buy", "COVER", "BY"}

    MONTH_SHIFT_MAP = {"BRN": -2, "W": -1, "B": -2, "T": -1, "SB": -1, "M": -1}

    YEAR_FIRST = [
        "%Y-%m-%dT%H:%M:%S%z",
        "%Y-%m-%dT%H:%M:%S.%f",
        "%Y-%m-%d %H:%M:%S.%f",
        "%Y-%m-%d%H:%M:%S.%f",
        "%Y/%m/%d %H:%M:%S.%f",
        "%Y-%m-%dT%H:%M:%S.%fZ",
        "%Y-%m-%dT%H:%M:%SZ",
        "%Y%m%d-%H:%M:%S.%f",
        "%Y%m%d %H:%M:%S.%f",
        "%Y%m%d%H:%M:%S.%f",
        "%Y-%m-%d",
        "%Y/%m/%d",
        "%Y%m%d",
        "%Y%m",
        "%Y%m%d %H:%M:%S.%f %z",
        "%Y%m%d %H:%M:%S.%f %zs",
        "%Y%m%d",
        "%Y%m%d-%H:%M:%S.%f",
        "%Y-%m-%d %H:%M:%SZ",
        "%Y%m%d;%H:%M:%S",
        "%y%m%d%H%M%S.%f",
        "%y%m%d%H%M%S",
    ]


class Params(BaseParams):

    # if True, the output of this task is a DataFrame containing Order and OrderState trades only
    # if False, the output of this task is a DataFrame containing RTS22Transaction trades only
    transactions_only: bool

    skip_non_fill: bool = Field(
        True,
        description="Param activated by default to trigger auxiliary method that skips flow for"
        "FIX messages where tag 150 (ExecType) is not F (Trade partial fill or fill)",
    )

    create_cancels: bool = Field(
        default=False,
        description="Param set to False by default to keep rows where tag 150 (ExecType) is H, which will"
        "be used to generate Cancel orderStates",
    )


class IceFixGenerateTrades(BaseTask):
    """
    This task expects a FixParserResult dataclass with ICE fix data from the FixParser class,
    processes the data and outputs a Pandas DataFrame with Orders and OrderStates or
    RTS22Transactions
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        fix_parser_data: List[FixParserResult] = None,
        **kwargs,
    ) -> FrameProducerResult:

        if not fix_parser_data:
            raise signals.SKIP("Fix Parser result is empty")
        results = []
        for fix_parser_result in fix_parser_data:
            results.append(
                self._process_fix_parser_result(
                    fix_parser_result=fix_parser_result, params=params
                )
            )
        # results contains a list of lists and needs to be flattened
        flattened_results = list(itertools.chain(*results))
        reports_df = pd.DataFrame(flattened_results)
        # Drop rows with all NA values: these rows are empty because of an
        # exception and need to be skipped
        reports_df = reports_df.dropna(how="all")
        target = self.normalize_trades_df(target=reports_df)

        return FrameProducerResult(frame=target)

    def _process_fix_parser_result(
        self, fix_parser_result: FixParserResult, params: Params
    ) -> List[dict]:
        """This function processes a fix_parser_result and returns a list of dicts
        containing transaction/order reports. Multiple dicts are possible in the
        case of Multiple legs being present in the ICE fix file.
        NOTE: if params.transactions_only, the list of dicts contains a transaction
        report and if not params.transactions_only, it contains an Order report.
        """
        order_reports = []
        transaction_reports = []
        fix_field_get = fix_getter(fix=fix_parser_result)

        if params.skip_non_fill:
            if not self.non_fill_exec_type(
                fget=fix_field_get, params=params, logger=self.logger
            ):
                # Skip the record: Return a list with an empty dict
                return [{}]

        # 555 tag is the Number of InstrumentLeg repeating group instances
        ff_555 = fix_field_get(555, typ="int", default=1)
        strike_price_index = 0

        for leg_index in range(ff_555):

            try:

                leg = None

                if ff_555 > 1:

                    leg = MultilegHandler(
                        fix_field_get, 602, 608, 612, 616, 624, 637, 654, 687, 9021
                    )
                    leg.set_index(leg_index)

                    asset_class = self.__asset_class(val=leg(608))
                    if asset_class == StaticFields.OPT:
                        strike_price_index += 1

                transaction_data = self.__tc_report(
                    fget=fix_field_get,
                    ord_status=fix_field_get(39),
                    leg=leg,
                    strike_price_index=strike_price_index,
                )
                # Add the file url
                if transaction_data != {}:
                    transaction_data[
                        StaticFields.FILE_URL
                    ] = fix_parser_result.s3_file_url

                if params.transactions_only:
                    transaction_reports.append(transaction_data)
                    continue

                new_order_data = self.__ex_report(
                    fix=Dict(transaction_data),
                    fget=fix_field_get,
                    leg=leg,
                    leg_index=leg_index,
                )
                # Add the file url
                new_order_data[StaticFields.FILE_URL] = fix_parser_result.s3_file_url

                order_state_data = copy.deepcopy(new_order_data)

                new_order_data[StaticFields.ORD_STATUS] = StaticFields.NEWO
                self.__update_for_order_status(
                    new_order_data, new_order_data[StaticFields.ORD_STATUS]
                )
                order_reports.append(new_order_data)

                order_state_data[StaticFields.ORD_STATUS] = fix_field_get(39)
                self.__update_for_order_status(
                    order_state_data, order_state_data[StaticFields.ORD_STATUS]
                )

                cancels_check = (
                    params.create_cancels
                    and fix_field_get(150).upper() == "H"
                    and order_state_data[StaticFields.ORD_STATUS] == "4"
                )

                if (
                    order_state_data[StaticFields.ORD_STATUS] in ["0", "2"]
                    or cancels_check
                ):
                    order_reports.append(order_state_data)

            except Exception as e:

                self.logger.warning(
                    f"Multi-leg Error: Leg number:{leg_index + 1}. error:{e}"
                )
                continue
        if params.transactions_only:
            return transaction_reports
        else:
            return order_reports

    def __ex_report(
        self,
        fix: Dict,
        fget,
        leg: Optional[MultilegHandler],
        leg_index: int,
    ) -> dict:
        """
        Create Order or OrderState report depending on ord_status
        :param fget: all fix fields and data
        :param leg: optional multileg "entry"
        :param leg_index: optional multileg "index"
        :return: fix data necessary for Order and OrderState
        """
        fix.StandardHeader.MsgSeqNum = fget(34, typ="int")
        fix.RegulatoryTradeIDGrp = dict(RegulatoryTradeID=fget(571))
        fix.ClOrdID = fget(11)
        fix.TradeRequestID = fget(568)
        fix.ExecID = fget(571)
        fix.OrdType = StaticFields.MARKET
        fix.TrdType = fget(828)
        fix.TrdCapRptSideGrp.TradeReportOrderDetail.OrderCapacity = (
            TradingCapacity.DEAL.value
        )
        parsed_date = self.__format_date(fget(60))

        if leg:
            fix.OrderID = f"MULTI|{fget(17)}|{fget(37)}|{leg_index + 1}|{parsed_date}"
            fix.OrderQtyData.OrderQty = abs_or_none(leg(687))
        else:
            fix.OrderID = f"{fget(17)}|{fget(37)}|{parsed_date}"
            fix.OrderQtyData.OrderQty = fget(32, typ="abs", default=0.0)

        return stripper(fix)

    def __tc_report(
        self,
        fget,
        ord_status: str,
        leg: Optional[MultilegHandler],
        strike_price_index: int,
    ) -> dict:
        """
        Create RTS22Transaction report
        :param fget: all fix fields and data
        :param ord_status: Order Status
        :param leg: optional multileg "entry"
        :param strike_price_index: index to fetch strike price
        :return: fix data necessary for RTS22Transaction
        """
        fix = Dict()
        fix.SecurityID = fget(48)
        fix.UnderlyingSymbol = self.__underlying_symbol(fget, leg)
        fix.Symbol = fget(55)
        fix.TrdTimestamps = fget(60)  # raw date from TransactTime tag of .fix file
        fix.PositionEffect = fget(77)
        fix.PriceType = QuantityNotation.MONE.value
        fix.QtyType = QuantityNotation.UNIT.value

        if leg:
            try:
                fix = self.__multileg_values(fix, fget, leg, strike_price_index)
            except Exception as e:
                self.logger.error(f"Error processing Multi-leg: {e}. Record skipped")
                return {}
        else:
            fix.TradeID = f"{fget(17)}|{fget(571)}"
            fix.TrdCapRptSideGrp = self.__side_group(fget, fget(54))
            fix.LastPx = fget(31, typ="abs", default=0.0)
            fix.AvgPx = fix.LastPx
            fix.LastQty = fget(32, typ="abs", default=0.0)
            fix.Instrument.SecurityExchange = fget(207)
            fix.LastMkt = fix.Instrument.SecurityExchange
            fix.Instrument.Venue = fix.Instrument.SecurityExchange
            fix.Instrument.StrikePrice = fget(202, typ="abs", default=0.0)
            fix.Instrument.OptionType = fget(461)[1:]
            fix.Instrument.AssetClass = self.__asset_class(fget(461))

        fix.Instrument.ExpiryDate = self.__expiry_date(fget, leg)

        __parties__ = fget(448, all_vals=True)
        __party_roles__ = fget(452, all_vals=True)
        if len(__parties__) != len(__party_roles__):
            self.logger.warning(
                "Skipping record as FIX message has inconsistent Parties data"
            )
            return {}
        self.split_parties_by_role(fix, __parties__, __party_roles__)

        fix.TargetSubId = fget(57)
        self.__update_for_order_status(fix, ord_status)
        fix.model = StaticFields.RTS22_TRANSACTION

        return stripper(fix)

    def __multileg_values(
        self, fix: Dict, fget, leg: MultilegHandler, strike_price_index: int
    ) -> Dict:
        """
        Set values of multileg fields
        :param fix: fix data
        :param fget: all fix fields and data
        :param leg: optional multileg "entry"
        :param strike_price_index: index to fetch strike price
        :return: fix data with multilegs data
        """
        fix.TradeID = f"{fget(17)}|{leg(654)}"
        fix.TrdCapRptSideGrp = self.__side_group(fget, leg(624))
        fix.LastPx = abs_or_none(leg(637))
        fix.AvgPx = abs_or_none(leg(637))
        fix.LastQty = abs_or_none(leg(687))
        fix.LastMkt = leg(616)
        fix.Instrument.SecurityExchange = leg(616)
        fix.Instrument.Venue = leg(616)
        fix.Instrument.StrikePrice = None
        fix.Instrument.OptionType = leg(608)[1:]
        fix.Instrument.AssetClass = self.__asset_class(leg(608))

        if fix.Instrument.AssetClass == StaticFields.OPT:
            fix.Instrument.StrikePrice = self.strike_price(fget, strike_price_index)

        return fix

    def __side_group(self, fget, side: Optional[str]) -> dict:
        """
        Side Group
        :param fget: all fix fields and data
        :param side: side
        :return: side group
        """
        ff_9701 = fget(9701, typ="str", default="2")
        group = Dict(Side=self.__get_side(side))
        group.TradeReportOrderDetail.OrderCapacity = {
            "0": TradingCapacity.DEAL.value,
            "1": TradingCapacity.MTCH.value,
            "2": TradingCapacity.AOTC.value,
        }.get(ff_9701)
        return group

    def __asset_class(self, val: Optional[str]) -> Optional[str]:
        """
        Asset Class
        :param val:
        :return: asset class
        """
        try:
            asset_cls = val[0]
            return {"F": StaticFields.FUT, "O": StaticFields.OPT}.get(asset_cls.upper())
        except Exception as e:
            self.logger.warning(
                f"error getting asset class from field:{val}, error:{e}"
            )
            return None

    def __expiry_date(self, fget, leg: MultilegHandler) -> Optional[str]:
        """
        Expiry Date
        :param fget: all fix fields and data
        :param leg: optional multileg "entry"
        :return: expiry date
        """
        if leg:
            expiry_date = leg(9021)
        else:
            expiry_date = fget(917)
        symbol = self.__underlying_symbol(fget, leg)
        if symbol:
            months_to_shift = AuxiliaryData.MONTH_SHIFT_MAP.get(symbol)
            if months_to_shift:
                expiry_date = self.reduce_month(dt=expiry_date, mnths=months_to_shift)
        return expiry_date

    def __underlying_symbol(self, fget, leg) -> Optional[str]:
        """
        Underlying Symbol
        :param fget: all fix fields and data
        :param leg: optional multileg "entry"
        :return: underlying symbol
        """
        symb = leg(602) if leg else fget(48)
        try:
            underlying_symbol = symb.split()[0]
        except Exception as e:
            underlying_symbol = None
            self.logger.warning(f"error getting underlying symbol. error:{e}")
        return underlying_symbol

    def __get_side(self, side: str, reverse: bool = False) -> str:
        """
        Side (Buy/Sell)
        :param side:
        :param reverse:
        :return: side
        """
        if is_empty(side):
            self.logger.warning(f"Unable to parse Side. Value: {side}")

        if reverse:
            return "2" if side.upper() in AuxiliaryData.BUY else "1"
        return "1" if side.upper() in AuxiliaryData.BUY else "2"

    @staticmethod
    def __update_for_order_status(rpt: dict, ord_status: str) -> None:
        """
        Update report fields based on Order Status
        :param rpt: Trade Report
        :param ord_status:
        """
        if ord_status == "0":
            rpt.update(OrderDictFields.NEWO_BLANK_FIELDS)
        elif ord_status == "2":
            rpt.update(OrderDictFields.FILL_BLANK_FIELDS)
        elif ord_status == "4":
            rpt.update(OrderDictFields.FILL_BLANK_FIELDS)

    @staticmethod
    def __format_date(val: Optional[str]) -> str:
        """
        Auxiliary method to format date
        :param val:
        :return: formatted date
        """
        dt = formatted_date_time_string(
            dt_str=val, dt_fmt=AuxiliaryData.YEAR_FIRST, target_fmt="%M:%S.%f"
        )
        return dt.replace("-", "") if dt else ""

    @staticmethod
    def non_fill_exec_type(fget, params: Params, logger: logging.Logger) -> bool:
        """
        This function returns False if FF150 != F (signifies that the record
        should be skipped), True otherwise.
        If the param `create_cancels` is True it also keeps FF150 == H
        :param fget: all fix fields and data
        :param params: task params
        """
        ff_150 = fget(150) or ""
        if ff_150.upper() != "F":
            if params.create_cancels and ff_150.upper() == "H":
                return True
            logger.warning(f"Record skipped as [FF150]!='F'. FF150:{ff_150}")
            return False
        return True

    @staticmethod
    def strike_price(fget, strike_price_index) -> Optional[float]:
        """
        Set Strike Price
        :param fget: all fix fields and data
        :param strike_price_index:
        :return: strike price
        """
        sp_leg = MultilegHandler(fget, 612)
        sp_leg.set_index(strike_price_index - 1)
        strike_price = abs_or_none(sp_leg(612))
        return strike_price

    @staticmethod
    def reduce_month(dt: Optional[str], mnths: int) -> Optional[str]:
        """
        Auxiliary date formatting
        :param dt:
        :param mnths:
        :return: formatted date
        """
        if isinstance(dt, str) and dt != "" and not dt.isspace():
            val_dt = pendulum.from_format(dt, "YYYYMMDD")
            val_dt = val_dt.add(months=mnths)
            return val_dt.format("YYYY-MM-DD")
        return dt

    @staticmethod
    def split_parties_by_role(fix_dict: Dict, parties: list, party_roles: list) -> dict:
        """
        Create fields for each Party Role and the associated Party ID
        :param fix_dict:
        :param parties:
        :param party_roles:
        :return: fix data with party ids separated by role
        """

        for index in range(len(parties)):
            fix_dict[
                f"{StaticFields.PARTY_ROLE_SEPARATOR}{party_roles[index]}"
            ] = parties[index]

        return fix_dict

    @staticmethod
    def normalize_trades_df(target: pd.DataFrame) -> pd.DataFrame:
        """
        Transform columns with .json entries into separate columns in the dataframe
        :param target: trades dataframe
        :return: trades dataframe with normalized .json rows
        """
        used_colums = [
            StaticFields.TRD_CAP_RPT_SIDE_GRP,
            StaticFields.INSTRUMENT,
            StaticFields.REGULATORY_TRADE_ID_GRP,
            StaticFields.ORDER_QTY_DATA,
            StaticFields.STANDARD_HEADER,
        ]
        res = target

        for col_to_normalize in used_colums:

            if col_to_normalize in target.columns:

                col_not_null_mask = target.loc[:, col_to_normalize].notnull()
                normalized_col_as_df = pd.json_normalize(
                    target.loc[col_not_null_mask, col_to_normalize]
                )

                normalized_col_as_df.index = target[col_not_null_mask].index

                res = pd.concat([res, normalized_col_as_df], axis=1).drop(
                    col_to_normalize, axis=1
                )

        return res
