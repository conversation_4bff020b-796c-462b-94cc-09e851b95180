from typing import Optional

import pandas as pd
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import Params

INSTRUMENT_IDENTIFIERS = "INSTRUMENT_IDENTIFIERS"


class VenueId:
    IFEU = "IFEU"
    NDEX = "NDEX"


class DFColumns:
    ID_FOR_IFEU = "ID_FOR_IFEU"


class InstrumentIdentifier(TransformBaseTask):
    """Generates Instrument Identifiers for ICE FIX Feed.
    Note: This task makes use of the generic instrument identifier task.
    The Params used here is defined in the generic identifer task."""

    params_class = Params

    def execute(
        self,
        source_frame: Optional[pd.DataFrame] = None,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        **kwargs,
    ) -> pd.DataFrame:

        cols_used = [
            params.asset_class_attribute,
            params.expiry_date_attribute,
            params.option_strike_price_attribute,
            params.option_type_attribute,
            params.swap_near_leg_date_attribute,
            params.underlying_symbol_attribute,
            params.venue_attribute,
            params.target_attribute,
        ]
        target = pd.DataFrame(
            data=pd.NA, index=source_frame.index, columns=[params.target_attribute]
        )
        if source_frame.empty:
            return target
        for col in cols_used:
            if col not in source_frame.columns:
                source_frame.loc[:, col] = pd.NA

        cols_mask = source_frame.columns.isin(cols_used)

        df = source_frame.loc[:, cols_mask]

        df[params.target_attribute] = InstrumentIdentifiers(
            name=INSTRUMENT_IDENTIFIERS
        ).execute(source_frame=df, params=params)

        venue_ndex_mask = df[params.venue_attribute].str.upper() == VenueId.NDEX
        if venue_ndex_mask.any():
            df.loc[venue_ndex_mask, DFColumns.ID_FOR_IFEU] = self._get_id_for_ifeu(
                df.loc[venue_ndex_mask], params=params, mask=venue_ndex_mask
            )
            mask = (
                df[params.target_attribute].notnull()
                & df[DFColumns.ID_FOR_IFEU].notnull()
            )

            df.loc[mask, params.target_attribute] = (
                df.loc[mask, params.target_attribute]
                + df.loc[mask, DFColumns.ID_FOR_IFEU]
            )

        return df[params.target_attribute].to_frame()

    @staticmethod
    def _get_id_for_ifeu(
        df: pd.DataFrame, params: Params, mask: pd.Series
    ) -> pd.Series:
        """Generates identifiers for IFEU instruments"""
        result = pd.Series(data=pd.NA, index=df.index)
        df.loc[mask, params.venue_attribute] = VenueId.IFEU
        result.loc[mask] = InstrumentIdentifiers(INSTRUMENT_IDENTIFIERS).execute(
            source_frame=df.loc[mask], params=params
        )[params.target_attribute]
        return result
