import string

import pandas as pd
from pydantic import Field
from se_elastic_schema.components.market.identifier import Identifier
from se_elastic_schema.static.market import IdentifierType
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.cloud.aws.s3.utils import read_csv_from_s3_download


class CsvColumnNames:
    SYMBOL = "symbol"
    INSTRUMENT_IDENTIFIER = "instrument_identifier"


INSTRUMENT_PATH = "instrumentDetails.instrument"


class Params(BaseParams):
    instrument_file_s3_key: str = Field(
        ..., description="S3 key for the instrument mapping file"
    )
    source_symbol_attribute: str = Field(
        ...,
        description="Source column which contains the symbols which can be mapped to the"
        " identifiers by looking up the s3 file",
    )
    target_attribute: str = Field(
        default="marketIdentifiers.instrument",
        description="Target attribute for all instrument identifiers",
    )


class InstrumentIdentifiersFromS3File(TransformBaseTask):
    """
    This task creates instrument identifiers based on mappings from an s3 file.
    This file, with the s3 path (s3 key: path+filename) is taken as a param.
    The file has 2 columns (and no header). The params.source_symbol_attribute
    field in the source file is used to do a lookup on the first column
    in the file, and the corresponding instrument identifiers are fetched
    from the file's second column. A target data frame is returned with one
    column (params.target_attribute) which contains the instrument identifiers.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources=None,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index)
        target[params.target_attribute] = pd.NA
        if source_frame.empty:
            self.logger.warning("Returning empty data frame as source frame is empty!")
            return target

        # Read instrument csv file from S3 into a dataframe
        s3_bucket = Settings.realm
        instrument_map_df = read_csv_from_s3_download(
            bucket=s3_bucket,
            key=params.instrument_file_s3_key,
            logger=self.logger,
            column_names=[CsvColumnNames.SYMBOL, CsvColumnNames.INSTRUMENT_IDENTIFIER],
        )
        if instrument_map_df.empty:
            self.logger.warning(
                "There was a problem while fetching and reading the"
                " s3 instruments file. Empty dataframe returned!"
            )
            return target

        # Drop duplicates as we want to use instrument_map as a lookup table
        instrument_map_df = instrument_map_df.drop_duplicates()
        # Set index=CsvColumnNames.SYMBOL so we can do a lookup easily
        instrument_map_df = instrument_map_df.set_index(
            CsvColumnNames.SYMBOL, drop=True
        )
        instrument_map = instrument_map_df.squeeze()
        security_ids = self._get_security_ids(
            df=source_frame, instrument_map=instrument_map, params=params
        )
        with_security_ids_mask = security_ids.notnull()

        if not with_security_ids_mask.any():
            return target
        # Assign instrument identifier for each security id
        target.loc[with_security_ids_mask, params.target_attribute] = security_ids.loc[
            with_security_ids_mask
        ].apply(
            lambda x: list(
                map(
                    lambda y: Identifier(
                        labelId=y,
                        path=INSTRUMENT_PATH,
                        type=IdentifierType.OBJECT,
                    ).dict(),
                    x,
                )
            )
        )
        return target

    @staticmethod
    def _get_security_ids(
        df: pd.DataFrame, instrument_map: pd.Series, params: Params
    ) -> pd.Series:
        """Maps the source frame column params.source_symbol_attribute to the instrument
        identifier in the s3 file by doing a lookup on CsvColumnNames.SYMBOL.
        Returns a Series with the instrument identifier in a list for each row. This is
        the format used by the other InstrumentIdentifiers tasks
        :param: df: data frame derived from the source_frame
        :type: df: pd.DataFrame
        :param: instrument_map: Series with index=symbol and data=Instrument identifier
        :type: instrument_map: pd.Series
        :param: params: Params instance
        :type: params: Params
        :returns: a Series containing a list of (1) instrument identifier in each row
        :rtype: pd.Series
        """
        # Remove punctuation symbols from df[params.source_symbol_attribute] elements
        remove_punctuation_mapping_table = str.maketrans("", "", string.punctuation)
        ids = (
            df.loc[:, params.source_symbol_attribute]
            .apply(lambda symbol: symbol.translate(remove_punctuation_mapping_table))
            .map(instrument_map)
            .fillna(pd.NA)
        )
        ids = ids.apply(lambda x: [x] if type(x) == str else x)
        return ids
