import logging
import os
from typing import Optional

from prefect.engine.signals import <PERSON><PERSON>
from pydantic import Field
from pydantic import root_validator
from pydantic import validator
from pysftp import ConnectionException
from pysftp import CredentialException
from se_core_tasks.core.core_dataclasses import Extract<PERSON>ath<PERSON><PERSON>ult
from se_core_tasks.core.result_category import ResultCategory
from swarm.client.sftp import SftpClient
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask
from swarm.task.io.write.sftp.result import SftpAction
from swarm.task.io.write.sftp.result import SftpFile
from swarm.task.io.write.sftp.result import SftpTargetResult

logger = logging.getLogger(__name__)


class Params(BaseParams):
    sftp_file_path: Optional[str] = Field(
        None, description="SFTP remote directory (LEGACY)"
    )
    remote_directory: Optional[str] = Field(None, description="SFTP remote directory")

    @root_validator
    def check_sftp_remote_directory_params(cls, values):
        sftp_file_path = values.get("sftp_remote_directory")
        remote_directory = values.get("remote_directory")

        if sftp_file_path and remote_directory:
            raise ValueError(
                "`sftp_file_path` and `remote_directory` are mutually exclusive."
            )

        return values

    @validator("remote_directory")
    def set_remote_directory(cls, value, values):
        sftp_file_path = values.get("sftp_file_path")

        if sftp_file_path and not value:
            logger.warning(
                "SftpUploader Params: `sftp_file_path` will be deprecated. Please use remote_directory."
            )
            value = sftp_file_path

        return value


class Resources(BaseResources):
    sftp_client_key: str


class SftpUploader(BaseTask):
    """
    This task uploads a local file into a SFTP remote server.
    """

    params_class = Params
    resources_class = Resources
    result_category = ResultCategory.SFTP

    def execute(
        self,
        result: ExtractPathResult = None,
        params: Params = None,
        resources: Resources = None,
        remote_directory: str = None,
        **kwargs,
    ) -> SftpTargetResult:

        if remote_directory and params.remote_directory:
            raise FAIL(
                "`params.remote_directory` and upstream `remote_directory` are mutually exclusive."
            )

        if remote_directory is None:
            remote_directory = params.remote_directory

        local_file = result.path

        try:
            sftp_client: SftpClient = self.clients.get(resources.sftp_client_key)
            # TODO this field should come from the registry in ES but there it is camelCase
            # and our ResourceConfig Pydantic model expects it to be snake_case
            # The proper solution is to add the camelCase alias to the model in se_elasticsearch repo
            # but we need this quick and dirty fix to accelerate Emir Prod issues resolution
            sftp_client._config.private_key_param = "/all/ssh/unavista/private"

            with sftp_client.connect() as sftp:
                try:
                    self.logger.info(
                        f"Change SFTP directory to {params.sftp_file_path}"
                    )
                    sftp.chdir(params.sftp_file_path)
                    self.logger.info(
                        f"Current working directory in remote sftp is: {sftp.getcwd()}"
                    )

                    # If the SFTP client is from the proxy, we need to pass the root directory in the put
                    # method too, IT CAN BE `None` when the client is PySFTP, it will construct the path
                    # as the remote directory, which is being done below.
                    # See: https://github.com/ryhsiao/pysftp/blob/685231499b2aea897b01cab370d847907647e40b/pysftp.py#L438
                    if remote_directory is None:
                        remote_directory = os.path.split(local_file.as_posix())[1]

                    self.logger.info(
                        f"Attempting to write file in SFTP, remote path: {remote_directory}"
                    )
                    self.logger.info(f"local file path: {local_file.as_posix()}")
                    sftp.put(
                        localpath=local_file.as_posix(), remotepath=remote_directory
                    )
                    self.logger.info(
                        f"Uploaded {local_file} to {params.sftp_file_path}"
                    )

                    sftp_file = SftpFile(
                        file_name=local_file.name,
                        remote_dir=remote_directory
                        or os.path.join("/", params.sftp_file_path, local_file.name),
                        action=SftpAction.PUT,
                        bytes=local_file.stat().st_size,
                    )
                    return SftpTargetResult(targets=[sftp_file])
                except IOError as e:
                    self.logger.exception("SftpUploader - Failed to write remote file")
                    raise e
                except OSError as e:
                    self.logger.exception("SftpUploader - Local file does not exist")
                    raise e

        except ConnectionException as e:
            self.logger.exception("SftpUploader - Connection Error")
            raise e

        except CredentialException as e:
            self.logger.exception("SftpUploader - Credentials Error")
            raise e

        except PermissionError as e:
            self.logger.exception("SftpUploader - Permission Error")
            raise e
