from typing import Optional

from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask
from swarm.task.io.write.elastic.result import ElasticBulkWriterResult
from swarm.task.io.write.elastic.static import BulkWriterColumns
from swarm.task.io.write.elastic.static import WriteStatus


class QuarantineCondition(BaseTask):
    def execute(
        self,
        params: Optional[BaseParams] = None,
        resources: Optional[BaseResources] = None,
        bulk_writer_result: ElasticBulkWriterResult = None,
    ) -> bool:

        needs_quarantine = False

        if not bulk_writer_result.frame.empty:
            conflict_mask = (
                bulk_writer_result.frame[BulkWriterColumns.STATUS]
                == WriteStatus.VERSION_CONFLICT
            )
            needs_quarantine = not bulk_writer_result.frame.loc[conflict_mask].empty

        return needs_quarantine
