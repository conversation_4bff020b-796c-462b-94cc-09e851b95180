import os
from pathlib import Path
from typing import Any
from typing import Dict
from typing import List

import humanize
import pandas as pd
from pydantic import Field
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.core.result_category import ResultCategory
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask
from swarm.task.io.write.elastic.result import ElasticBulkWriterResult
from swarm.task.io.write.elastic.static import BulkWriterColumns
from swarm.task.io.write.elastic.static import RECORD_INDEX_DELIMITER
from swarm.task.io.write.elastic.static import WriteStatus


class Params(BaseParams):
    payload_size: int = Field(..., description="Size of payload")


class Resources(BaseResources):
    es_client_key: str


class ElasticBulkDeleter(BaseTask):
    """
    This task takes an NDJSON input in which each line corresponds to one record
    to be deleted.
    These records are in the format expected by the Bulk API. E.g.
    {"delete":{"_id":"fd4d441c-532c-4ac8-bec8-1311b7ee4f52","_index":"ashwath:account_person","_type":"AccountPerson"}}
    will delete _id fd4d441c-532c-4ac8-bec8-1311b7ee4f52 from the AccountPerson model.

    This task must always have an ElasticBulkTransformer task upstream, with action_type='delete'. The
    ElasticBulkTransformer should receive (at a minimum) the &id, &hash, &model fields in a data frame.
    """

    params_class = Params
    resources_class = Resources
    result_category = ResultCategory.ELASTIC

    def execute(
        self,
        result: ExtractPathResult = None,
        params: Params = None,
        resources: Resources = None,
    ) -> ElasticBulkWriterResult:

        if not result.path:
            return ElasticBulkWriterResult(
                frame=pd.DataFrame(), quarantined=False, total_bytes=0
            )

        es = Settings.connections.get(resources.es_client_key)
        output_path = result.path.as_posix()
        output_files = []

        for root, directory_names, file_names in os.walk(output_path):
            for f in file_names:
                if not f.endswith(".ndjson"):
                    self.logger.info(f"skipping {f} (not .ndjson file).")
                    continue
                output_file = Path(root).joinpath(f)
                _, batch_index, _ = f.split(".")
                output_files.append((int(batch_index), output_file))

        output_files = [of[1] for of in sorted(output_files)]
        content = ""
        record_indices = list()
        actions = 0
        status_frames = list()
        total_bytes = 0
        for idx, output_file in enumerate(output_files):
            if idx > 0:
                content += "\n"
            with output_file.open("r") as f:
                self.logger.info(f"processing file {output_file}")
                for action_line in f:
                    raw_index, model, record_hash, action_line = action_line.split(
                        RECORD_INDEX_DELIMITER, 3
                    )
                    record_indices.append(
                        dict(
                            file_index=idx,
                            raw_index=int(raw_index),
                            model=model,
                            hash=record_hash,
                        )
                    )
                    content += action_line
                    actions += 1
                    if len(content) >= params.payload_size:
                        total_bytes += len(content)
                        status_frame = self._flush_content(
                            es=es,
                            content=content,
                            record_indices=record_indices,
                            actions=actions,
                        )
                        status_frames.append(status_frame)
                        content = ""
                        actions = 0
                        record_indices.clear()

        if content:
            total_bytes += len(content)
            status_frame = self._flush_content(
                es=es,
                content=content,
                record_indices=record_indices,
                actions=actions,
            )
            status_frames.append(status_frame)

        status_result = pd.concat(status_frames) if status_frames else pd.DataFrame()

        counts = dict()
        if not status_result.empty:
            for model, df in status_result.groupby(by=[BulkWriterColumns.MODEL]):
                counts[model] = df.status.value_counts().to_dict()

        self.logger.info(
            f"bulk writer wired {humanize.naturalsize(total_bytes)} "
            f"with results: {counts}"
        )

        return ElasticBulkWriterResult(
            frame=status_result, quarantined=False, total_bytes=total_bytes
        )

    def _flush_content(
        self,
        es,
        content: str,
        record_indices: List[Dict[str, Any]],
        actions: int,
    ) -> pd.DataFrame:
        """Deletes the records specified in 'content' using the Bulk API. It also creates
        as user-friendly status_df from the Bulk API response to be used to count the
        no. of deleted/version conflict/errored records.
        Note that content is of the following form:
        '{"delete":{"_id":"1bb7fb7d-c17f-4470-9367-524c2092","_index":"ashwath:account_person","_type":"AccountPerson"}}
        {"delete":{"_id":"2f732a6f-459f-4339-bac4-6f5debdf","_index":"ashwath:account_person","_type":"AccountPerson"}}'

        and record_indices is of the foll. form:
        [{'file_index': 0, 'raw_index': 0, 'model': 'AccountPerson',
         'hash': 'a91b0eb92cd4ec2c7aeb0c5a9ab7bad9386949544e730ec8ce3ba293560db142'}...]

        :param es: Elasticsearch client
        :param content: String content in the required format expected by the bulk api (see e.g. above)
        :param record_indices: index details of the record which is being deleted
        :param actions: No. of deletions which need to be done
        :returns: A status frame with useful details from the bulk api response
        """
        try:
            response = self._bulk_api(content=content, es=es)
            statuses = list(
                map(
                    lambda x: self._make_status(
                        record_indices=record_indices,
                        response_index=x[0],
                        response=x[1],
                        action_type="delete",
                    ),
                    enumerate(response["items"]),
                )
            )
            status_df = pd.DataFrame(statuses)

            errored_mask = ~status_df.status.isin([200, 409])
            status_df.loc[errored_mask, BulkWriterColumns.STATUS] = WriteStatus.ERRORED

            deleted_mask = status_df.status == 200
            # TODO Change to WriteStatus.DELETED after https://steeleye.atlassian.net/browse/ON-2934 is done
            # and a new swarm sdk version is created
            status_df.loc[deleted_mask, BulkWriterColumns.STATUS] = WriteStatus.UPDATED

            conflict_mask = status_df.status == 409
            status_df.loc[
                conflict_mask, BulkWriterColumns.STATUS
            ] = WriteStatus.VERSION_CONFLICT

            counts = dict()
            for model, df in status_df.groupby(by=["model"]):
                counts[model] = df.status.value_counts().to_dict()

            self.logger.info(
                f"sent {actions} actions over "
                f"{humanize.naturalsize(len(content))}: {counts}"
            )
            return status_df
        except Exception as e:
            self.logger.error(e)
            raise e

    @staticmethod
    def _bulk_api(content: str, es) -> dict:
        """
        Calls the bulk API using the content string in the expected format

        :param content: String content in the required format expected by the bulk api
        :param es: Elasticsearch client
        :return: Bulk API response (dict)
        """
        response = es.client.bulk(
            body=content, refresh="true", timeout="15m", request_timeout=900
        )
        return response

    @staticmethod
    def _make_status(
        record_indices: List[Dict[str, Any]],
        response_index: int,
        response: dict,
        action_type: str,
    ):
        """
        Creates a status dictionary from the Bulk API response
        record_indices is of the foll. form:
        [{'file_index': 0, 'raw_index': 0, 'model': 'AccountPerson',
         'hash': 'a91b0eb92cd4ec2c7aeb0c5a9ab7bad9386949544e730ec8ce3ba293560db142'}...]
        :param record_indices: index details of the record which has been deleted
        :param response_index: index of the response (there are multiple dictionaries in
        the response's items list.
        :param response: bulk api response
        :param action_type: type of action sent to the bulk api, 'delete' in this case
        :return: dictionary with different fields required to create the status data frame
        """
        result = response[action_type]
        error = result.get("error", dict())
        caused_by = error.get("caused_by", dict())

        status = {
            BulkWriterColumns.FILE_INDEX: record_indices[response_index].get(
                "file_index"
            ),
            BulkWriterColumns.RAW_INDEX: record_indices[response_index].get(
                "raw_index"
            ),
            BulkWriterColumns.MODEL: record_indices[response_index].get("model"),
            BulkWriterColumns.HASH: record_indices[response_index].get("hash"),
            BulkWriterColumns.ID: result["_id"],
            BulkWriterColumns.ES_INDEX: result["_index"],
            BulkWriterColumns.STATUS: result["status"],
            BulkWriterColumns.ERROR_TYPE: error.get("type"),
            BulkWriterColumns.ERROR_REASON: error.get("reason"),
            BulkWriterColumns.ERROR_CAUSED_BY_TYPE: caused_by.get("type"),
            BulkWriterColumns.ERROR_CAUSED_BY_REASON: caused_by.get("reason"),
        }

        return status
