import itertools
import os
from enum import Enum
from pathlib import Path
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Tuple

import humanize
import pandas as pd
from pydantic import Field
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.core.result_category import ResultCategory
from se_elastic_schema.models import find_model
from se_elastic_schema.models import SinkR<PERSON>ord<PERSON>udit
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask
from swarm.task.io.write.elastic.result import ElasticBulkWriterResult
from swarm.task.io.write.elastic.static import BulkWriterColumns
from swarm.task.io.write.elastic.static import RECORD_INDEX_DELIMITER
from swarm.task.io.write.elastic.static import WriteStatus

from swarm_tasks.transform.steeleye.link import util


class ElasticRefreshType(str, Enum):
    TRUE = "true"
    WAIT_FOR = "wait_for"
    FALSE = "false"


class Params(BaseParams):
    payload_size: int
    action_type: str = "create"
    quarantined: bool = False
    # Valid refresh operations are:
    # 1. "true" - Refresh the affected shards to make this operation visible to search
    # 2. "wait_for" - Wait for a refresh to make this operation visible to search
    # 3. "false" - Does nothing with refreshes
    # Please see doc for more info here - https://elasticsearch-py.readthedocs.io/en/master/api.html#elasticsearch.Elasticsearch.bulk
    refresh: ElasticRefreshType = Field(
        default=ElasticRefreshType.TRUE,
        description="Refresh operation to perform when performing I/O in the ElasticSearch. Defaults to 'true'",
    )
    # https://elasticsearch-py.readthedocs.io/en/7.x/api.html#elasticsearch.client.IndicesClient.flush
    # WARNING: This shouldn't be done until really needed. Please discuss with Data Management and
    # Data Integration Owners before you use this param in your flow.
    # It takes the alias of an index from the model name in the input and then maps it with tenant
    # For example: If you pass `RTS22Transaction` in input and flow is running for `iris`
    # then it will become `iris-rts22_transaction-alias`
    model_index_to_flush: Optional[str] = Field(
        default=None,
        description="Index of the input model to be flushed after the task is done "
        "with the write/update operation on an index. It only supports "
        "models from se-elastic-schema",
    )


class Resources(BaseResources):
    es_client_key: str


class ElasticBulkWriter(BaseTask):
    params_class = Params
    resources_class = Resources
    result_category = ResultCategory.ELASTIC

    def execute(  # noqa: C901
        self,
        result: ExtractPathResult = None,
        params: Params = None,
        resources: Resources = None,
    ) -> ElasticBulkWriterResult:

        if not result.path:
            return ElasticBulkWriterResult(
                frame=pd.DataFrame(), quarantined=params.quarantined, total_bytes=0
            )

        # This can be an ES5 SRP or ES8 tenant-data cluster
        # if `es_client_key` is `reference-data` or `tenant-data`.
        # this field is is passed from the bundle.yaml
        es = self.clients.get(resources.es_client_key)
        output_path = result.path.as_posix()
        output_files = []

        for root, directory_names, file_names in os.walk(output_path):
            for f in file_names:
                if not f.endswith(".ndjson"):
                    self.logger.info(f"skipping {f} (not .ndjson file).")
                    continue
                output_file = Path(root).joinpath(f)
                _, batch_index, _ = f.split(".")
                output_files.append((int(batch_index), output_file))

        output_files = [of[1] for of in sorted(output_files)]
        content = ""
        record_indices = list()
        actions = 0
        status_frames = list()
        total_bytes = 0
        quarantine_status_frame = pd.DataFrame()
        previous_model = None

        for idx, output_file in enumerate(output_files):
            if idx > 0:
                content += "\n"
            with output_file.open("r") as f:
                self.logger.info(f"processing file {output_file}")
                for line1, line2 in itertools.zip_longest(*[f] * 2):
                    raw_index, model, record_hash, line1 = line1.split(
                        RECORD_INDEX_DELIMITER, 3
                    )

                    if params.quarantined:
                        if (
                            not previous_model
                            and model == SinkRecordAudit.get_reference().name
                        ):
                            # SinkRecordAudit records starts here now for all the
                            # Quarantines so flush any content if we have
                            if content:
                                total_bytes += len(content)
                                status_frame = self._flush_content(
                                    es=es,
                                    params=params,
                                    content=content,
                                    record_indices=record_indices,
                                    actions=actions,
                                    action_type=params.action_type,
                                )
                                content = ""
                                actions = 0
                                status_frames.append(status_frame)
                                record_indices.clear()

                            previous_model = model
                            quarantine_status_frame = (
                                pd.concat(status_frames)
                                if status_frames
                                else pd.DataFrame()
                            )
                            status_frames = [quarantine_status_frame]

                        if model == SinkRecordAudit.get_reference().name:
                            (
                                skip_sink_audit,
                                reason,
                            ) = self.should_skip_sink_record_audit(
                                quarantine_status_frame=quarantine_status_frame,
                                sink_raw_index=int(raw_index),
                            )
                            if skip_sink_audit:
                                self.logger.warning(
                                    f"Skipping SinkRecordAudit record because failed to create quarantine with reason: {reason}"
                                )
                                continue

                    _, _, _, line2 = line2.split(RECORD_INDEX_DELIMITER, 3)
                    record_indices.append(
                        dict(
                            file_index=idx,
                            raw_index=int(raw_index),
                            model=model,
                            hash=record_hash,
                        )
                    )
                    content += line1
                    content += line2
                    actions += 1
                    if len(content) >= params.payload_size:
                        total_bytes += len(content)
                        status_frame = self._flush_content(
                            es=es,
                            params=params,
                            content=content,
                            record_indices=record_indices,
                            actions=actions,
                            action_type=params.action_type,
                        )
                        status_frames.append(status_frame)
                        content = ""
                        actions = 0
                        record_indices.clear()

        if content:
            total_bytes += len(content)
            status_frame = self._flush_content(
                es=es,
                params=params,
                content=content,
                record_indices=record_indices,
                actions=actions,
                action_type=params.action_type,
            )
            status_frames.append(status_frame)

        if params.model_index_to_flush:
            elastic_index = find_model(
                params.model_index_to_flush
            ).get_elastic_index_alias(tenant=Settings.tenant)
            self.logger.info(f"Flushing index: {elastic_index}")
            try:
                es.client.indices.flush(index=elastic_index)
                self.logger.info(f"Successfully flushed {elastic_index}")
            except Exception as e:
                self.logger.error(
                    f"Exception occured while flushing an index. Exception: {e}"
                )

        status_result = pd.concat(status_frames) if status_frames else pd.DataFrame()

        counts = dict()
        if not status_result.empty:
            for model, df in status_result.groupby(by=[BulkWriterColumns.MODEL]):
                counts[model] = df.status.value_counts().to_dict()

        self.logger.info(
            f"bulk writer wired {humanize.naturalsize(total_bytes)} "
            f"with results: {counts}"
        )

        return ElasticBulkWriterResult(
            frame=status_result, quarantined=params.quarantined, total_bytes=total_bytes
        )

    @staticmethod
    def should_skip_sink_record_audit(
        quarantine_status_frame: pd.DataFrame,
        sink_raw_index: int,
    ) -> Tuple[bool, Optional[str]]:
        """
        Method to check if we should be creating a SinkRecordAudit record
        or not based on the status of a Quarantine Record creation.
        If Quarantine is created Successfully, it returns 'True' else False
        :param quarantine_status_frame: Quarantine records status frame
        :param sink_raw_index: Raw index of SinkRecordAudit record
        :return: Boolean value and reason if the Audit should be skipped
        """
        # we will always have the Quarantine record for SinkRecordAudit
        quarantined_record = quarantine_status_frame[
            quarantine_status_frame["raw_index"] == sink_raw_index
        ].to_dict(orient="records")[0]

        if quarantined_record["status"] != "created":
            return True, quarantined_record["error_reason"]
        return False, None

    def _flush_content(
        self,
        es,
        params: Params,
        content: str,
        record_indices: List[Dict[str, Any]],
        actions: int,
        action_type: str,
    ):
        try:
            response = es.client.bulk(
                body=content,
                refresh=params.refresh.value,
                timeout="15m",
                request_timeout=900,
            )
            statuses = list(
                map(
                    lambda x: self._make_status(
                        record_indices=record_indices,
                        response_index=x[0],
                        response=x[1],
                        action_type=action_type,
                    ),
                    enumerate(response["items"]),
                )
            )
            status_df = pd.DataFrame(statuses)

            errored_mask = ~status_df.status.isin([200, 201, 409])
            status_df.loc[errored_mask, BulkWriterColumns.STATUS] = WriteStatus.ERRORED

            updated_mask = status_df.status == 200
            status_df.loc[updated_mask, BulkWriterColumns.STATUS] = WriteStatus.UPDATED

            created_mask = status_df.status == 201
            status_df.loc[created_mask, BulkWriterColumns.STATUS] = WriteStatus.CREATED

            conflict_mask = status_df.status == 409
            status_df.loc[
                conflict_mask, BulkWriterColumns.STATUS
            ] = WriteStatus.VERSION_CONFLICT

            version_conflicts = status_df.loc[
                (status_df.status == WriteStatus.VERSION_CONFLICT)
            ]

            if len(version_conflicts.index) > 0:
                # todo group by es index
                result_df = self._compare_hash(
                    es=es,
                    status_frame=version_conflicts,
                    alias=version_conflicts.iloc[0][BulkWriterColumns.ES_INDEX],
                )
                status_df = status_df.merge(
                    result_df[
                        [
                            BulkWriterColumns.RAW_INDEX,
                            BulkWriterColumns.MODEL,
                            BulkWriterColumns.STATUS,
                        ]
                    ],
                    left_on=[BulkWriterColumns.RAW_INDEX, BulkWriterColumns.MODEL],
                    right_on=[BulkWriterColumns.RAW_INDEX, BulkWriterColumns.MODEL],
                    how="left",
                )
                status_df["status_y"] = status_df["status_y"].fillna(
                    status_df["status_x"]
                )
                status_df.drop("status_x", inplace=True, axis=1)
                status_df.rename(
                    columns={"status_y": BulkWriterColumns.STATUS}, inplace=True
                )

            counts = dict()
            for model, df in status_df.groupby(by=["model"]):
                counts[model] = df.status.value_counts().to_dict()

            self.logger.info(
                f"sent {actions} actions over "
                f"{humanize.naturalsize(len(content))}: {counts}"
            )
            return status_df
        except Exception as e:
            self.logger.error(e)
            raise e

    @staticmethod
    def _make_status(
        record_indices: List[Dict[str, Any]],
        response_index: int,
        response: dict,
        action_type: str,
    ):
        result = response[action_type]
        error = result.get("error", dict())
        caused_by = error.get("caused_by", dict())

        status = {
            BulkWriterColumns.FILE_INDEX: record_indices[response_index].get(
                "file_index"
            ),
            BulkWriterColumns.RAW_INDEX: record_indices[response_index].get(
                "raw_index"
            ),
            BulkWriterColumns.MODEL: record_indices[response_index].get("model"),
            BulkWriterColumns.HASH: record_indices[response_index].get("hash"),
            BulkWriterColumns.ID: result["_id"],
            BulkWriterColumns.ES_INDEX: result["_index"],
            BulkWriterColumns.STATUS: result["status"],
            BulkWriterColumns.ERROR_TYPE: error.get("type"),
            BulkWriterColumns.ERROR_REASON: error.get("reason"),
            BulkWriterColumns.ERROR_CAUSED_BY_TYPE: caused_by.get("type"),
            BulkWriterColumns.ERROR_CAUSED_BY_REASON: caused_by.get("reason"),
        }

        return status

    def _compare_hash(
        self,
        es,
        status_frame: pd.DataFrame,
        alias: str,
    ) -> pd.DataFrame:
        self.logger.info(f"checking hash equality for {len(status_frame.index)} 409s")

        ids_to_inspect = status_frame[BulkWriterColumns.ID].unique().tolist()

        hits_cache = list()

        for items in util.batch(ids_to_inspect, 1024):
            self.logger.info(f"scrolling for {len(items)} ids")
            body = {
                "_source": [es.meta.hash, es.meta.id, es.meta.version],
                "version": True,
                "size": len(items),
                "query": {
                    "bool": {
                        "must_not": {"exists": {"field": es.meta.expiry}},
                        "must": [{"terms": {es.meta.id: items}}],
                    }
                },
            }
            hits = es.scroll(query=body, index=alias, as_dataframe=True)
            hits_cache.append(hits)

        cache = pd.concat(hits_cache)
        cache = cache.reset_index(drop=True)
        self.logger.info(f"cache columns: {list(cache.columns)}")

        for column in [es.meta.id, es.meta.hash]:
            if column not in cache.columns:
                self.logger.warning(f"cache missing {column}")
                cache[column] = pd.NA

        if cache.empty:
            self.logger.warning("unexpected empty cache")

        cache.rename(columns={es.meta.hash: "stored_hash"}, inplace=True)

        df = status_frame.merge(
            cache[[es.meta.id, "stored_hash"]],
            left_on=BulkWriterColumns.ID,
            right_on=es.meta.id,
            how="left",
        )

        duplicates_mask = df[BulkWriterColumns.HASH] == df["stored_hash"]
        df.loc[duplicates_mask, BulkWriterColumns.STATUS] = WriteStatus.DUPLICATE

        df = df.drop([es.meta.id, "stored_hash"], axis=1)
        return df
