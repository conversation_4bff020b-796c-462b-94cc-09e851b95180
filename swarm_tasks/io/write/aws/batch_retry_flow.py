import json
from typing import Any
from typing import Optional

import boto3
from pydantic import Field
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask

from swarm_tasks.utilities.s3 import s3_data_from_file_url

EVENT_SOURCE = "swarm:tasks:BatchRetryFlow"


class Params(BaseParams):
    delay_seconds: int = Field(900, ge=0, le=900)  # default the max for AWS Batch


class BatchRetryFlow(BaseTask):
    """
    This task is AWS-specific. It is supposed to be added on to any flow and allow for
    re-posting the task to the SQS queue with a delay.
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        file_url: Optional[str] = None,
        flow_args: Optional[str] = None,
        **kwargs,
    ) -> Any:

        file_is_from_s3 = str(file_url).startswith("s3://")
        if not file_is_from_s3 or Settings.DEV:
            raise Exception(
                "Cannot requeue task as its' running locally (with DEV=True) or file_url is not from S3"
            )

        s3_bucket, s3_key = s3_data_from_file_url(file_url)
        # the SQS queue name is the current realm/bucket with _ instead of .
        # and with the _flows suffix. Related to FlowRouter.
        queue = s3_bucket.replace(".", "_") + "_flows"
        # Get the service resource
        sqs = boto3.client("sqs")
        # Get the queue
        response = sqs.get_queue_url(QueueName=queue)
        queue_url = response["QueueUrl"]
        # Create a new message
        body = {
            "Records": [
                {
                    "eventSource": EVENT_SOURCE,
                    "realm": s3_bucket,
                    "bundle": {"id": s3_key},
                    "args": flow_args and json.loads(flow_args) or None,
                }
            ]
        }
        # Send the message
        sqs.send_message(
            DelaySeconds=params.delay_seconds,
            QueueUrl=queue_url,
            MessageBody=json.dumps(body),
        )
        self.auditor.add(f"Requeued file {file_url} to {queue} queue.")
