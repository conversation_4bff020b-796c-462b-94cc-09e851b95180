from pathlib import Path
from typing import List

from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


class BatchTargetDirectory(BaseTask):
    """
    Finds the parent directory of a list of unique ExtractPathResult target files. If the parent directory of the files
    is the same returns ExtractPathResult(parent dir) downstream.

    **NOTE** This task should have `mapped=false` It is to increase ElasticBulkWriters performance by avoiding
    timeouts incurred by parallel operations.
    """

    def execute(
        self,
        result: List[ExtractPathResult] = None,
        params: BaseParams = None,
        resources: BaseResources = None,
    ) -> ExtractPathResult:
        """
        Takes list of ExtractPathResult target `ndjson` files from ElasticBulkTransformer, finds the parent
        and passes it downstream to `ElasticBulkWriter`
        :param result:
        :param params:
        :param resources:
        :return:
        """
        if not result:
            return ExtractPathResult(path=None)

        paths: List[Path] = [
            parent_folder.path.parent
            for parent_folder in result
            if parent_folder and parent_folder.path
        ]

        if not paths:
            return ExtractPathResult(path=None)

        if len(set(paths)) == 1:
            return ExtractPathResult(paths[0])

        raise ValueError("Number of batches parent folder: `targets` > 1")
