from typing import Any
from typing import Optional

from prefect.engine.signals import FAIL
from se_core_tasks.io.read.poll_elastic_records import FailIfCountsNotMatched
from se_core_tasks.io.read.poll_elastic_records import (
    Params as ParamsPollElasticRecords,
)
from se_core_tasks.io.read.poll_elastic_records import run_poll_elastic_records
from swarm.conf import Settings
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask
from swarm.task.transform.result import TransformResult


class Resources(BaseResources):
    es_client_key: str


class PollElasticRecords(BaseTask):
    resources_class = Resources
    params_class = ParamsPollElasticRecords

    def execute(
        self,
        result: Optional[TransformResult] = None,
        params: ParamsPollElasticRecords = None,
        resources: Resources = None,
        **kwargs,
    ) -> Any:
        try:
            return run_poll_elastic_records(
                params=params,
                source_frame=result.target,
                es_client=self.clients.get(resources.es_client_key),
                tenant=Settings.tenant,
                logger=self.logger,
            )
        except FailIfCountsNotMatched as e:
            raise FAIL(e.message)
