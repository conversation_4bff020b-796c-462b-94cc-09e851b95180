from typing import Union

from se_core_tasks.core.core_dataclasses import File<PERSON><PERSON>litterR<PERSON>ult
from se_core_tasks.io.read.rename_csv_header import Params as GenericParams
from se_core_tasks.io.read.rename_csv_header import run_rename_csv_header
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask
from swarm.task.io.read.result import ExtractPathResult


class Params(BaseParams, GenericParams):
    pass


class RenameCsvHeader(BaseTask):

    params_class = Params

    def execute(
        self,
        params: Params = None,
        file_path: Union[FileSplitterResult, ExtractPathResult, str] = None,
        **kwargs,
    ) -> Union[FileSplitterResult, ExtractPathResult, str]:

        if isinstance(file_path, str):
            return run_rename_csv_header(
                params=params,
                file_path=file_path,
                auditor=self.auditor,
                logger=self.logger,
            )

        new_file_path = run_rename_csv_header(
            params=params,
            file_path=str(file_path.path),
            auditor=self.auditor,
            logger=self.logger,
        )
        file_path.path = new_file_path
        return file_path
