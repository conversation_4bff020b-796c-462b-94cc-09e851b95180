from pathlib import Path

from prefect.engine import signals
from se_core_tasks.io.read.gpg_decryptor import FailIfDecryptError
from se_core_tasks.io.read.gpg_decryptor import FailIfFilePathEmpty
from se_core_tasks.io.read.gpg_decryptor import FailIfPassphraseAbsent
from se_core_tasks.io.read.gpg_decryptor import FailIfPrivateKeyAbsent
from se_core_tasks.io.read.gpg_decryptor import Params as GenericParams
from se_core_tasks.io.read.gpg_decryptor import run_gpg_decryptor
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask
from swarm.task.io.read.result import ExtractPathResult


class Params(BaseParams, GenericParams):
    pass


class GpgDecryptor(BaseTask):

    params_class = Params

    def execute(
        self,
        params: Params = None,
        extractor_result: ExtractPathResult = None,
        **kwargs,
    ) -> ExtractPathResult:

        try:
            result = run_gpg_decryptor(
                file_path=extractor_result.path.as_posix(),
                params=params,
                tenant=Settings.tenant,
                logger=self.logger,
            )
            return ExtractPathResult(path=Path(result))
        except (
            FailIfFilePathEmpty,
            FailIfDecryptError,
            FailIfPassphraseAbsent,
            FailIfPrivateKeyAbsent,
        ) as e:
            raise signals.FAIL(f"Task failed due to: {e}")
