from typing import Optional
from typing import Union

import pandas as pd
from prefect.engine import signals
from se_core_tasks.core.core_dataclasses import (
    FrameProducerResult as FrameProducerResultCore,
)
from se_core_tasks.core.core_dataclasses import TransformResult as TransformResultCore
from se_trades_tasks.order_and_tr.fix.inline_fix_parser import Params as GenericParams
from se_trades_tasks.order_and_tr.fix.inline_fix_parser import run_inline_fix_parser
from se_trades_tasks.order_and_tr.fix.inline_fix_parser import SkipIfFixContentIsEmpty
from se_trades_tasks.order_and_tr.fix.inline_fix_parser import (
    SkipIfFixContentNotInSourceFrame,
)
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask
from swarm.task.io.read import FrameProducerResult
from swarm.task.transform.result import TransformResult


class Params(BaseParams, GenericParams):
    pass


class InlineFixParser(BaseTask):

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        source_frame: Union[pd.DataFrame, FrameProducerResult, TransformResult] = None,
        **kwargs,
    ) -> pd.DataFrame:

        if isinstance(source_frame, FrameProducerResult):
            source_frame = FrameProducerResultCore(
                frame=source_frame.frame, batch_index=source_frame.batch_index
            )

        elif isinstance(source_frame, TransformResult):
            source_frame = TransformResultCore(
                target=source_frame.target, batch_index=source_frame.batch_index
            )

        try:
            result = run_inline_fix_parser(
                source_frame=source_frame,
                params=params,
            )
        except (SkipIfFixContentNotInSourceFrame, SkipIfFixContentIsEmpty) as e:
            raise signals.SKIP(f"Flow will be skipped: {e.message}")

        return result
