from typing import List

import pandas as pd
from se_trades_tasks.order_and_tr.fix.dataclasses.fix_parser_dataclasses import (
    FixParserResult,
)
from se_trades_tasks.order_and_tr.fix.fix_parser import run_fix_parser
from swarm.task.base import BaseTask


class FixParser(BaseTask):
    def execute(
        self,
        fix_dataframe: pd.DataFrame = None,
        **kwargs,
    ) -> List[FixParserResult]:

        # FixParser returns a tuple, where the first element is a list of FixParserResult objects,
        # and the second element is a dictionary of audit data, that is only relevant in se-mono, not Swarm.
        result, _ = run_fix_parser(fix_dataframe=fix_dataframe, auditor=self.auditor)

        return result
