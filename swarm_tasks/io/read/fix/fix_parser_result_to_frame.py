from typing import List
from typing import Optional

from se_trades_tasks.order_and_tr.fix.dataclasses.fix_parser_dataclasses import (
    FixParserResult,
)
from se_trades_tasks.order_and_tr.fix.fix_parser_result_to_frame import (
    Params as GenericParams,
)
from se_trades_tasks.order_and_tr.fix.fix_parser_result_to_frame import (
    run_fix_parser_result_to_frame,
)
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask
from swarm.task.io.read import FrameProducerResult


class Params(BaseParams, GenericParams):
    pass


class FixParserResultToFrame(BaseTask):

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        fix_parsed_data: List[FixParserResult] = None,
        **kwargs,
    ) -> FrameProducerResult:

        result = run_fix_parser_result_to_frame(
            fix_parsed_data=fix_parsed_data,
            params=params,
        )

        return FrameProducerResult(
            frame=result.frame,
            batch_index=result.batch_index,
        )
