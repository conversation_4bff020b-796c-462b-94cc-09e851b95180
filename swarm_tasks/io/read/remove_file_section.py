import re
from typing import List
from typing import Optional

from pydantic import Field
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams):
    target_regex: str = Field(
        ..., description="Regex to find line from where to delete lines"
    )
    encoding: str = Field(None, description="Encoding to be passed into open()")
    remove_upwards: bool = Field(
        False, description="Defines in which direction to remove the file section."
    )
    include_target_line: bool = Field(
        False,
        description="Defines if the line where the target regex is found is\
         included or removed",
    )


class RemoveFileSection(BaseTask):
    """
    This tasks removes lines from a file from a given regex onwards
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        extractor_result: ExtractPathResult = None,
        **kwargs,
    ) -> ExtractPathResult:

        with open(file=extractor_result.path, mode="r", encoding=params.encoding) as f:
            file_lines = f.readlines()

        if not file_lines:
            return extractor_result

        index = self._get_target_index(lines=file_lines, params=params)

        if not index:
            self.logger.info(f"Did not find a match for: {params.target_regex}")
            return extractor_result

        sliced_lines = self._slice_file_lines(
            lines=file_lines, index=index, params=params
        )

        with open(file=extractor_result.path, mode="w", encoding=params.encoding) as f:
            for line in sliced_lines:
                f.write(line)

        return extractor_result

    @staticmethod
    def _get_target_index(lines: List[str], params: Params) -> int:
        """
        This function takes a list of lines from a file
        and returns the index where the regex is found.

        :param lines: lines from a file
        :param params: Params object with target_regex
        :return: index where regex is found
        """
        for index, line in enumerate(lines):
            if re.search(params.target_regex, line):
                return index

    @staticmethod
    def _slice_file_lines(lines: List[str], index: int, params: Params) -> List[str]:
        """
        This function takes a list of lines, an index where to split them and
        slices the list accordingly.

        :param lines: lines from a file
        :param index: index where to split from
        :param params: Params object with remove_upwards and include_target_line
        :return: None
        """

        if params.include_target_line:
            if params.remove_upwards:
                index -= 1
            else:
                index += 1

        sliced_lines = lines[index + 1 :] if params.remove_upwards else lines[:index]

        return sliced_lines
