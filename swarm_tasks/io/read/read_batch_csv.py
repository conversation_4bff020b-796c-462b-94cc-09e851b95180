from typing import Optional

from prefect.engine.signals import FAIL
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.io.read.read_batch_csv import FailIfFileNotFound
from se_core_tasks.io.read.read_batch_csv import Params as GenericParams
from se_core_tasks.io.read.read_batch_csv import run_read_batch_csv
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask
from swarm.task.io.read.result import FrameProducerResult


class Params(BaseParams, GenericParams):
    pass


class ReadBatchCSV(BaseTask):
    """This task is used to read a CSV file that has already been batched.
    It does not read the CSV file in chunks, so only use this when you already
    have a reasonably-sized batch CSV file. For large CSV files, please
    use the CsvFileSplitter task along with the BatchProducer to produce
    batches.

    The task returns a Pandas data frame containing the data in the CSV file.
    Note: no processing is done in this task. The returned result is the
    DataFrame created by pd.read_csv.
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        extractor_result: ExtractPathResult = None,
        **kwargs,
    ) -> FrameProducerResult:
        try:
            result = run_read_batch_csv(
                params=params,
                file_path=extractor_result.path,
                auditor=self.auditor,
                logger=self.logger,
            )

            return FrameProducerResult(frame=result, batch_index=0)
        except FailIfFileNotFound:
            raise FAIL("Could not load csv into dataframe ")
