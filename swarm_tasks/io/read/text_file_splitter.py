from typing import List
from typing import Optional

from prefect.engine.signals import SKIP
from se_core_tasks.io.read.text_file_splitter import Params as GenericParams
from se_core_tasks.io.read.text_file_splitter import run_text_file_splitter
from se_core_tasks.io.read.text_file_splitter import SkipIfInputFileIsEmpty
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask
from swarm.task.io.read.result import ExtractPathResult


class Params(BaseParams, GenericParams):
    pass


class TextFileSplitter(BaseTask):

    params_class = Params

    def execute(
        self,
        params: Optional[BaseParams] = None,
        resources: Optional[BaseResources] = None,
        extract_result: ExtractPathResult = None,
        **kwargs,
    ) -> List[ExtractPathResult]:

        return self.process(
            params=params,
            extract_result=extract_result,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        params: Optional[Params] = None,
        extract_result: ExtractPathResult = None,
        auditor=None,
        logger=None,
    ) -> List[ExtractPathResult]:
        try:
            return run_text_file_splitter(
                extract_result=extract_result,
                params=params,
                auditor=auditor,
                logger=logger,
            )
        except SkipIfInputFileIsEmpty as e:
            raise SKIP(f"Input file is empty: {e}")
