import re
import tempfile
from pathlib import Path
from typing import Optional

import fsspec
from prefect.engine.signals import FAIL
from prefect.engine.signals import SKIP
from pydantic import Field
from pydantic import root_validator
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams):

    line_regex: str = Field(
        default=...,
        description="Regex which to filter the input file's lines by "
        "(i.e: '<message_type>CONVERSATION</message_type>')",
    )
    target_extension: Optional[str] = Field(
        default=None,
        description="Extension of the new file which will be written in a "
        "temporary directory after filtering the input file",
    )
    xml_root_tag: Optional[str] = Field(
        default=None,
        description="For use cases where we're extracting repeated XML tags "
        "from a text file and we need to store them under"
        "a root element",
    )

    @root_validator
    def xml_root_tag_must_match_xml_target_extension(cls, values):
        # Note that it is ok to specify 'target_extension = `xml`' without specifying the root tag
        # Meaning that the XML being parsed already contains a root tag
        target_extension = values.get("target_extension")
        xml_root_tag = values.get("xml_root_tag")

        if xml_root_tag and (
            target_extension is not None and target_extension.lower() != ".xml"
        ):
            raise ValueError(
                "`xml_root_tag` parameter can only be specified if `target_extension` is `.xml`"
            )
        return values


class FilterTextFileLines(BaseTask):
    """
    This task reads an input text file from local file system or from a cloud provider, filters every line that matches
    a given regex pattern and writes a new file for downstream processing.

    Example of use case:
    The `comms-chat-refinitiv-fxt` flow uses this task because the tenant RMB send us their data on a .log text file
    which essentially contains a complete XML document per line (with many other lines that are non-relevant logs).
    With this task, we can filter this .log file, extract only the XML tags that we need,
    incorporate a new XML root element and write the data into a valid XML file ready for downstream processing.
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        file_url: str = None,
        **kwargs,
    ) -> ExtractPathResult:

        self.logger.info(
            f"Filtering {file_url} for lines that match {params.line_regex}"
        )

        result = []

        try:
            file_content = fsspec.open(file_url, "r").open()
        except Exception as e:
            raise FAIL(
                f"It was not possible to read {file_url} due to the following error: {e}"
            )

        for line in file_content:
            if re.search(params.line_regex, line):
                result.append(line)
        file_content.close()

        if not result:
            raise SKIP(
                f"The pattern '{params.line_regex}' was not found in the file {file_url}"
            )

        if params.xml_root_tag:
            result = (
                [f"<{params.xml_root_tag}>"] + result + [f"</{params.xml_root_tag}>"]
            )

        temp_dir = Path(tempfile.gettempdir())
        file_url_path = Path(file_url)

        if params.target_extension:
            target_file = (
                temp_dir.joinpath(file_url_path.stem + params.target_extension)
            ).as_posix()
        else:
            target_file = temp_dir.joinpath(file_url_path.name).as_posix()

        with open(target_file, "w") as output:
            output.writelines(result)
        self.logger.info(f"Wrote temporary file {target_file} with filtered results")

        return ExtractPathResult(path=Path(target_file))
