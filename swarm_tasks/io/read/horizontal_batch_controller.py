from typing import List

from se_core_tasks.core.core_dataclasses import File<PERSON><PERSON><PERSON>terR<PERSON>ult
from se_core_tasks.io.read.horizontal_batch_controller import Params as GenericParams
from se_core_tasks.io.read.horizontal_batch_controller import (
    run_horizontal_batch_controller,
)
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams, GenericParams):
    pass


class HorizontalBatchController(BaseTask):
    """
    Returns a bool based on whether the input file needs to be split up into
    multiple batches and re-uploaded to S3 based on number of chunks created
    by CsvFileSplitter
    """

    params_class = Params

    def execute(
        self,
        params: Params = None,
        list_of_batches: List[FileSplitterResult] = None,
        **kwargs,
    ) -> bool:

        result = run_horizontal_batch_controller(
            list_of_batches=list_of_batches,
            params=params,
            auditor=self.auditor,
            logger=self.logger,
        )

        return result
