from prefect import context
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_core_tasks.io.read.batch_producer import Params as GenericParams
from se_core_tasks.io.read.batch_producer import run_batch_producer
from swarm.schema.static import SwarmColumns
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask
from swarm.task.io.read.result import FrameProducerResult


class Params(BaseParams, GenericParams):
    pass


class BatchProducer(BaseTask):

    params_class = Params

    def execute(
        self,
        params: Params = None,
        file_splitter_result: FileSplitterResult = None,
        **kwargs,
    ) -> FrameProducerResult:

        result = run_batch_producer(
            params=params,
            file_splitter_result=file_splitter_result,
            raw_index_name=SwarmColumns.SWARM_RAW_INDEX,
            source_schema=context.swarm.source_schema,
            batch_index=file_splitter_result.batch_index,
            auditor=self.auditor,
            logger=self.logger,
        )

        return FrameProducerResult(frame=result.frame, batch_index=result.batch_index)
