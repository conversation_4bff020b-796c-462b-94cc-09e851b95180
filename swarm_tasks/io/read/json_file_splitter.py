import json
import os
from pathlib import Path
from typing import List
from typing import Optional
from typing import Union

import pandas as pd
from prefect import context
from pydantic import Field
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_core_tasks.utils.create_batches import create_batches
from swarm.schema.static import SwarmColumns
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


class Params(BaseParams):
    chunksize: int = 1000
    target_delimiter: Optional[str] = ","
    audit_input_rows: bool = Field(
        default=False,
        description="If True, audits the no of rows which were present in the input file",
    )


class JsonFileSplitter(BaseTask):
    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        extractor_result: Union[ExtractPathResult, List[ExtractPathResult]] = None,
    ) -> List[FileSplitterResult]:
        if isinstance(extractor_result, list):
            if len(extractor_result) == 0:
                raise ValueError(f"empty input file: {extractor_result.path}")
        elif os.stat(extractor_result.path).st_size == 0:
            raise ValueError(f"empty input file: {extractor_result.path}")

        if not isinstance(extractor_result, list):
            extractor_result = [extractor_result]

        content = []
        for result in extractor_result:
            with open(result.path) as f:
                load_result = json.load(f)
                if isinstance(load_result, dict):
                    content.append(load_result)
                else:
                    for row in load_result:
                        if isinstance(row, dict):
                            content.append(row)

        df = pd.json_normalize(content)
        source_dir = Path(context.swarm.sources_dir)

        batches = create_batches(
            df=df,
            chunksize=params.chunksize,
            target_delimiter=params.target_delimiter,
            source_dir=source_dir,
            index_name=SwarmColumns.SWARM_RAW_INDEX,
        )

        if params.audit_input_rows:
            # Auditing the number of records which were present in the input file
            audit_ctx = {"input_total_count": df.shape[0]}
            self.auditor.add(message="", ctx=audit_ctx)

        return batches
