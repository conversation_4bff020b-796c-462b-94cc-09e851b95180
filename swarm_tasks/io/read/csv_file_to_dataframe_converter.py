from pathlib import Path
from typing import Optional
from typing import Union

import pandas as pd
from pydantic import Field
from se_core_tasks.io.read.csv_file_to_dataframe_converter import (
    Params as GenericParams,
)
from se_core_tasks.io.read.csv_file_to_dataframe_converter import (
    run_csv_file_to_dataframe_converter,
)
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask
from swarm.task.transform.result import TransformResult


class Params(BaseParams, GenericParams):
    return_transform_result: bool = Field(
        default=True,
        description=("Whether a dataframe or TransformResult should be returned."),
    )


class CsvFileToDataframeConverter(BaseTask):
    """
    - This task can either return a dataframe or a TransformResult,
    this approach was required due to `PrimaryTransformations`
    expects at least one `TransformResult` as argument.
    """

    def execute(
        self,
        params: Optional[Params] = None,
        path: Path = None,
        **kwargs,
    ) -> Union[TransformResult, pd.DataFrame]:
        if params.return_transform_result:
            return TransformResult(
                target=CsvFileToDataframeConverter.process(
                    params=params,
                    path=path,
                )
            )

        return CsvFileToDataframeConverter.process(
            params=params,
            path=path,
        )

    params_class = Params

    @staticmethod
    def process(
        path: Path,
        params: Params,
    ) -> pd.DataFrame:
        return run_csv_file_to_dataframe_converter(
            params=params,
            path=path,
        )
