from pathlib import Path
from typing import Dict
from typing import Optional
from typing import Union

from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.io.read.csv_file_filter import Params as GenericParams
from se_core_tasks.io.read.csv_file_filter import run_csv_file_filter
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams, GenericParams):
    pass


class CsvFileFilter(BaseTask):
    def execute(
        self,
        params: Optional[Params] = None,
        local_file_path: Union[ExtractPathResult, str] = None,
        **kwargs,
    ) -> Dict[str, Path]:
        if isinstance(local_file_path, ExtractPathResult):
            local_file_path = local_file_path.path.as_posix()

        return CsvFileFilter.process(
            params=params,
            local_file_path=local_file_path,
        )

    params_class = Params

    @staticmethod
    def process(
        local_file_path: str,
        params: Params,
    ) -> Dict[str, Path]:
        return run_csv_file_filter(
            params=params,
            local_file_path=local_file_path,
        )
