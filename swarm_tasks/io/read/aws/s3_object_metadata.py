from typing import Optional
from urllib.parse import unquote_plus

import boto3
from prefect.engine.signals import SKIP
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


class Params(BaseParams):
    unquote: Optional[bool] = True
    skip_on_s3_exception: Optional[bool] = False


class S3ObjectMetadata(BaseTask):
    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        file_url: str = None,
    ) -> dict:
        if not file_url.lower().startswith("s3://"):
            self.logger.error("Param `file_url` must start with `s3://`")
            raise ValueError("Please prepend `s3://` to `file_url`")

        s3_bucket, s3_key = file_url[5:].split("/", 1)

        if params.unquote:
            s3_key = unquote_plus(s3_key)

        metadata = self._get_metadata(
            bucket=s3_bucket, key=s3_key, skip=params.skip_on_s3_exception
        )
        return metadata

    @staticmethod
    def _get_metadata(bucket: str, key: str, skip: bool) -> dict:
        try:
            s3 = boto3.client("s3")
            return s3.head_object(Bucket=bucket, Key=key)
        except Exception as e:
            if skip:
                raise SKIP(f"{e}")
            else:
                raise
