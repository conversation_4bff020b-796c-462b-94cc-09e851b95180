from typing import List
from typing import Optional

import boto3
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


class S3Keys:
    CONTINUATION_TOKEN = "ContinuationToken"
    CONTENTS = "Contents"
    IS_TRUNCATED = "IsTruncated"
    NEXT_CONTINUATION_TOKEN = "NextContinuationToken"


class Params(BaseParams):
    folder_url: Optional[str] = Field(
        None, description="The URL of the folder in s3 you want to query"
    )


class S3ListObjects(BaseTask):
    """
    Lists objects and their parameters inside a S3 Folder

    NOTE: The client parameter is only to passed when calling it directly from another task and not in a bundle.
    We cannot pass the `client` parameter in a bundle.

    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        client: Optional[boto3.client] = None,
        folder_url: Optional[str] = None,
    ) -> List:
        """
        List the contents of folder in s3 given a folder URL using boto3 ListObjectsV2

        :param params: For use in bundle to pass the folder_url of the bucket we wish to list objects
        :param resources: N/A
        :param client: Optionally pass the boto3 Client directly (useful to avoid invoking the client multiple times)
        *NOTE* Do not pass the client from the bundle, only directly from another task.
        :param folder_url: URL of the S3 Folder, must be in S3 URL format
        :return: List of dictionaries with contents of specified S3 Folder if items present in folder, otherwise empty
        list
        """

        if client is None:
            client = boto3.client("s3")
        # This is pre_prefix because the prefix in this task is the prefix of the folder we wish to list
        s3_pre_prefix = "s3://"
        s3_folder = folder_url or params.folder_url
        if not folder_url:
            raise ValueError("`folder_url` must be provided")
        if not folder_url.lower().startswith(s3_pre_prefix):
            error = f"Param `folder_url` must start with `{s3_pre_prefix}`"
            self.logger.error(error)
            raise ValueError(error)

        results = list()
        s3_bucket, s3_prefix = s3_folder.lstrip(s3_pre_prefix).split("/", 1)
        for file in self._get_all_s3_objects(
            client, Bucket=s3_bucket, Prefix=s3_prefix
        ):
            results.append(file)

        return results

    @staticmethod
    def _get_all_s3_objects(s3: boto3.client, **kwargs) -> dict:
        """
        Iterates through Contents of S3's list_objects_v2 returning what is in s3_folder and if Next_Continuation_Token
        is present in response will use it in case of folders items exceeding 1000 (Max items for one response).
        :param s3: s3 client
        :param kwargs: s3 bucket and s3 prefix
        :return:
        """
        continuation_token = None
        while True:
            list_objects_kwargs = dict(MaxKeys=1000, **kwargs)
            if continuation_token:
                list_objects_kwargs[S3Keys.CONTINUATION_TOKEN] = continuation_token
            response = s3.list_objects_v2(**list_objects_kwargs)
            yield from response.get(S3Keys.CONTENTS, [])

            if not response.get(S3Keys.IS_TRUNCATED):
                break
            continuation_token = response.get(S3Keys.NEXT_CONTINUATION_TOKEN)
