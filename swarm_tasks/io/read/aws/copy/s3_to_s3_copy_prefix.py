from datetime import datetime
from typing import Dict
from typing import List
from typing import NoReturn
from typing import Optional

import boto3
from prefect.engine import signals
from pydantic import Field
from pydantic import root_validator
from swarm.conf import Settings
from swarm.schema.static import Environment
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask

from swarm_tasks.cloud.aws.s3.utils import copy_file
from swarm_tasks.io.read.aws.copy.s3_to_s3_copy import STEELEYE


class Params(BaseParams):
    s3_source_folder: Optional[str] = Field(
        None, description="Name of the source folder to get the files to copy."
    )
    s3_target_folder: Optional[str] = Field(
        None, description="Name of the target folder to copy the files to"
    )
    s3_target_tenant: Optional[str] = Field(
        None, description="Name of the target tenant to copy the files to"
    )
    file_prefix: Optional[str] = Field(
        None, description="Prefix of the file that will be copied"
    )
    override_if_exists: bool = Field(
        False, description="If True, overrides the file in target if it exists."
    )
    skip_on_error: bool = Field(
        False, description="If True, continues to upload the remaining files."
    )

    @root_validator
    def _check_target(cls, values):
        if (
            (not values.get("s3_target_tenant"))
            or (not values.get("s3_source_folder"))
            or (not values.get("file_prefix"))
        ):
            raise ValueError(
                "s3_target_tenant, s3_source_folder and the file_prefix params should be populated"
            )
        return values


class S3ToS3CopyPrefix(BaseTask):
    """
    Task to copy today's files from a bucket to another given a prefix.
    Checks if the file exists in target bucket, if yes, by default don't override.
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        **kwargs,
    ) -> NoReturn:
        s3 = boto3.client("s3")
        environment = Settings.env
        s3_source_bucket = Settings.realm

        s3_target_bucket = (
            f"{params.s3_target_tenant}.{environment}{STEELEYE}"
            if environment != Environment.PROD
            else f"{params.s3_target_tenant}{STEELEYE}"
        )

        files_to_copy = self._get_files_to_copy(
            s3_client=s3,
            s3_source_bucket=s3_source_bucket,
            s3_source_folder=params.s3_source_folder,
            s3_target_bucket=s3_target_bucket,
            prefix_file=params.file_prefix,
            params=params,
        )

        if not files_to_copy:
            raise signals.SKIP(f"No files to copy to {s3_target_bucket}")

        self.logger.info(
            f"Copying files from s3://{s3_source_bucket} to: {s3_target_bucket} ..."
        )

        for source_key in files_to_copy:
            target_key = self._get_target_key(source_key, params)

            copy_file(
                source_bucket=s3_source_bucket,
                source_key=source_key,
                target_bucket=s3_target_bucket,
                target_key=target_key,
                s3_client=s3,
                skip_on_error=params.skip_on_error,
                logger=self.logger,
            )

    def _get_files_to_copy(
        self,
        s3_client: boto3.client,
        s3_source_bucket: str,
        s3_source_folder: str,
        s3_target_bucket: str,
        prefix_file: str,
        params: Params,
    ) -> Optional[List[str]]:
        """
        Get the name of the file that should be copied
        :param s3_client: s3 botocore client
        :param s3_source_bucket: name of bucket from which to get the files to be copied
        :param s3_source_folder: name of folder to search in
        :param prefix_file: prefix file to search by
        :param params: List of parameters
        :return: list of file names that should be copied
        """

        flows_files = []
        paginator = s3_client.get_paginator("list_objects")
        pages = paginator.paginate(
            Bucket=s3_source_bucket, Prefix=f"{s3_source_folder}{prefix_file}"
        )

        for page in pages:
            try:
                flows_files.extend(page["Contents"])
            except KeyError:
                return

        if self._validate_files(list_of_files=flows_files):
            files = [
                file["Key"]
                for file in flows_files
                if (
                    self._check_if_file_is_to_copy(
                        s3_client=s3_client,
                        s3_target_bucket=s3_target_bucket,
                        file=file["Key"],
                        params=params,
                    )
                    and file["LastModified"].date() == datetime.today().date()
                )
            ]

            return files

    @staticmethod
    def _check_if_file_is_to_copy(
        s3_client: boto3.client, s3_target_bucket: str, file: str, params: Params
    ) -> bool:
        """
        Check if file exists in the target bucket and if it is to override or not in case it exists.
        NOTE: Since we will use the paginator, we need to check if there is more than one file with the same prefix
        :param s3_client: s3 botocore client
        :param s3_target_bucket: name of bucket the file is to be copied to
        :param file: file to check
        :param params: List of parameters
        :return: list of file names that should be copied
        """

        try:
            flows_files = s3_client.list_objects(Bucket=s3_target_bucket, Prefix=file)

            if "Contents" in flows_files.keys():
                return params.override_if_exists

            return True

        except s3_client.exceptions.NoSuchBucket as exc:
            raise Exception(f"The target bucket doesn't exist. {exc}")

    @staticmethod
    def _get_target_key(source_key: str, params: Params) -> str:
        """
        Transforms key if params.s3_target_folder is provided
        :param source_key: key of source file
        :param params: task parameters
        :return: key to copy the file to
        """
        return (
            params.s3_target_folder + source_key.split("/")[-1]
            if params.s3_target_folder is not None
            else source_key
        )

    @staticmethod
    def _validate_files(list_of_files: List[Dict]) -> bool:
        """
        Check if mandatory keys exist in the flow file
        """
        files = [
            "Key" in file.keys() and "LastModified" in file.keys()
            for file in list_of_files
        ]

        return all(files)
