import json
from typing import Optional

from pydantic import ValidationError
from swarm.task.base import BaseTask

from swarm_tasks.io.read.aws.copy.models import FlowArgsModel


class ValidateFlowArgs(BaseTask):
    """
    Validates the FlowArgs for S3ToS3Copy flow
    """

    def execute(
        self,
        flow_args: Optional[str] = None,
        **kwargs,
    ) -> FlowArgsModel:
        self.logger.warning("This task can be replaced by FlowArgsValidator")
        try:
            flow_args_obj = FlowArgsModel(**json.loads(flow_args))
        except ValidationError as e:
            raise ValidationError(f"Error on parsing Flow Args: {e}", model=e.model)

        return flow_args_obj
