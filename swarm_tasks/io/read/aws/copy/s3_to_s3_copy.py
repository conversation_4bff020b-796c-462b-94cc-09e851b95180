import re
from typing import NoReturn
from typing import Optional
from urllib.parse import urlparse

import addict
import boto3
from prefect.engine import signals
from pydantic import Field
from swarm.conf import Settings
from swarm.schema.static import Environment
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask

from swarm_tasks.cloud.aws.s3.utils import copy_file

STEELEYE = ".steeleye.co"


class Params(BaseParams):
    s3_source_tenant: Optional[str] = Field(
        None, description="S3 source tenant to copy the file from"
    )
    s3_source_key: Optional[str] = Field(
        None, description="S3 key to copy the file from"
    )
    s3_target_tenant: Optional[str] = Field(
        None, description="S3 target tenant to copy the file to"
    )
    s3_target_folder: Optional[str] = Field(
        None, description="Name of the target folder to copy the files to"
    )
    skip_on_error: bool = Field(
        False, description="If True, continues to upload the remaining files."
    )
    regex_for_file_match: Optional[str] = Field(
        default=None,
        description="Regex pattern that the input file url should match for"
        "it to be copied. Defaults to None, i.e., there is no"
        "match before copying the file",
    )


class S3ToS3Copy(BaseTask):
    """
    Task to copy a file from an s3 bucket location to another.
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        file_url: Optional[str] = None,
        **kwargs,
    ) -> NoReturn:
        s3 = boto3.client("s3")

        self.process(s3_client=s3, params=params, file_url=file_url)

    def process(
        self,
        s3_client: boto3.client,
        params: Optional[Params] = None,
        file_url: Optional[str] = None,
    ) -> NoReturn:
        """
        Processes the inputs from the task into the bucket and keys for the files to be copied
        :param s3_client: s3 client instance
        :param params: parameters
        :param file_url: file url string
        """

        data_dict = self.get_parameters_to_process(params=params, file_url=file_url)

        self.logger.info(
            f"Copying file from s3://{data_dict.source_bucket}/{data_dict.source_key} to: {data_dict.target_bucket}/{data_dict.target_key}..."
        )

        copy_file(
            source_bucket=data_dict.source_bucket,
            source_key=data_dict.source_key,
            target_bucket=data_dict.target_bucket,
            target_key=data_dict.target_key,
            s3_client=s3_client,
            skip_on_error=params.skip_on_error,
            logger=self.logger,
        )

    @staticmethod
    def get_parameters_to_process(
        params: Optional[Params] = None, file_url: Optional[str] = None
    ) -> addict.Dict:
        """
        Gets the inputs from the task into the bucket and keys for the files to be copied
        :param params: parameters
        :param file_url: file url string
        :return: Dict
        """
        if (not params.s3_target_folder) and (not params.s3_target_tenant):
            raise ValueError(
                "At least one of s3_target_tenant and s3_target_folder needs to be populated"
            )

        environment = Settings.env

        if file_url:
            file_parsing = urlparse(file_url, allow_fragments=False)
            s3_source_bucket = file_parsing.netloc
            s3_source_key = file_parsing.path.lstrip("/")
        else:
            if not params.s3_source_key:
                raise ValueError(
                    "s3_source_key needs to be populated when not using file_url"
                )
            s3_source_key = params.s3_source_key
            s3_source_bucket = (
                (
                    f"{params.s3_source_tenant}.{environment}{STEELEYE}"
                    if environment != Environment.PROD
                    else f"{params.s3_source_tenant}{STEELEYE}"
                )
                if params.s3_source_tenant is not None
                else Settings.realm
            )

        if params.regex_for_file_match and not re.search(
            params.regex_for_file_match, s3_source_key
        ):
            raise signals.SKIP(
                f"Regex {params.regex_for_file_match} does not match"
                f" the filename {s3_source_key}. Skipping s3 to s3 copy"
            )

        s3_target_bucket = (
            (
                f"{params.s3_target_tenant}.{environment}{STEELEYE}"
                if environment != Environment.PROD
                else f"{params.s3_target_tenant}{STEELEYE}"
            )
            if params.s3_target_tenant is not None
            else s3_source_bucket
        )

        s3_target_key = (
            params.s3_target_folder + s3_source_key.split("/")[-1]
            if params.s3_target_folder is not None
            else s3_source_key
        )

        return addict.Dict(
            source_bucket=s3_source_bucket,
            source_key=s3_source_key,
            target_bucket=s3_target_bucket,
            target_key=s3_target_key,
        )
