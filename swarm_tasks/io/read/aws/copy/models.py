from pydantic import Field

from swarm_tasks.generic.flow_args_validator import AbstractFlowArgs


class FlowArgsModel(AbstractFlowArgs):
    """
    Model to validate flow args inputs of S3ToS3Copy flow
    """

    environment: str = Field(..., description="Name of the source environment")
    sourceTenant: str = Field(
        ..., description="Name of the source tenant to get the files to copy."
    )
