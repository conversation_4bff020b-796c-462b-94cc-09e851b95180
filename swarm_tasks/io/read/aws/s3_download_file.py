from pathlib import Path
from typing import Optional

from prefect import context
from prefect.engine.signals import SKIP
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.io.read.aws.s3_download_file import Params as GenericParams
from se_core_tasks.io.read.aws.s3_download_file import run_s3_download_file
from se_core_tasks.io.read.aws.s3_download_file import SkipIfFilePartFile
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams, GenericParams):
    pass


class S3DownloadFile(BaseTask):
    params_class = Params

    def execute(
        self,
        file_url: str = None,
        params: Params = None,
        **kwargs,
    ) -> ExtractPathResult:

        return self.process(
            params=params,
            file_url=file_url,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        self,
        file_url: str,
        params: Optional[Params] = None,
        auditor=None,
        logger=context.get("logger"),
    ) -> ExtractPathResult:

        try:
            return ExtractPathResult(
                path=Path(
                    run_s3_download_file(
                        params=params,
                        file_url=file_url,
                        realm=Settings.realm,
                        auditor=auditor,
                        logger=logger,
                    )
                )
            )
        except SkipIfFilePartFile as e:
            raise SKIP(f"Flow will be skipped: {e.message}")
