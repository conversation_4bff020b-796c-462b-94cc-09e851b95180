import tempfile
from pathlib import Path
from typing import Optional
from urllib.parse import unquote_plus

import boto3
import pandas as pd
from prefect import context
from prefect.engine import signals
from pydantic import Field
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class FileFormatFields:
    CSV = ".csv"
    TXT = ".txt"


class Params(BaseParams):
    s3_bucket: Optional[str] = Field(
        None, description="S3 bucket where the target file is"
    )
    s3_key: str = Field(
        ..., description="S3 path of the target file (excluding the bucket identifier)"
    )
    delimiter: str = Field(default=",", description="Delimiter for the file")


class DfFromS3Csv(BaseTask):

    """
    This task access an S3 bucket from context.swarm.identifier.realm, downloads a CSV file
    passed as params.s3_key and converts it into a Pandas DataFrame
    """

    params_class = Params

    def execute(self, params: Optional[Params] = None, **kwargs) -> pd.DataFrame:
        return self.process(params=params, logger=self.logger)

    @classmethod
    def process(
        cls,
        params: Params = None,
        logger=context.get("logger"),
    ):
        s3 = boto3.client("s3")
        s3_bucket = params.s3_bucket if params.s3_bucket else Settings.realm
        s3_key = params.s3_key
        filename = Path(s3_key).name
        temp_dir = tempfile.gettempdir()
        temp_file_path = Path(temp_dir).joinpath(filename)

        # stream file object, rather than download in one go
        logger.info(f"Extracting s3://{s3_bucket}/{s3_key} to: {temp_file_path} ...")

        try:

            with open(temp_file_path, "wb") as fh:
                s3.download_fileobj(s3_bucket, unquote_plus(s3_key), fh)

        except Exception as e:
            raise signals.FAIL(
                f"{e}: it was not possible to retrieve {s3_key} from {s3_bucket}"
            )

        if temp_file_path.suffix.lower() in (
            FileFormatFields.CSV,
            FileFormatFields.TXT,
        ):
            df = pd.read_csv(temp_file_path, sep=params.delimiter)
        else:
            raise signals.FAIL("S3 Key does not represent a valid .csv")

        return df
