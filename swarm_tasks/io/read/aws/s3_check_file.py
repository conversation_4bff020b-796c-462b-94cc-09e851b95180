import re
from typing import Dict
from typing import Optional

import boto3
from prefect.engine import signals
from pydantic import Field
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


class StaticS3ResponseValues:
    CONTENTS = "Contents"
    KEY = "Key"
    S3_PREFIX = "s3://"


class Params(BaseParams):
    folder: str = Field(..., description="S3 folder in bucket where to look for file")
    find: str = Field("", description="regex pattern to replace in filename")
    replace: str = Field("", description="string to replace by in filename")
    slice_filename_start: int = Field(
        None,
        description="integer value to slice filename from, e.g. [slice_filename_start:]",
    )
    slice_filename_end: int = Field(
        None,
        description="integer value to slice filename to, e.g. [:slice_filename_end]",
    )


class S3CheckFile(BaseTask):
    """
    Checks if file is present in s3 folder.
    Raises FAILED status if there isn't or there is multiple with same name.
    Returns key to the file if present.
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        extract_result: ExtractPathResult = None,
    ) -> str:

        source_file_name = extract_result.path.name
        target_file_name = re.sub(
            pattern=params.find, repl=params.replace, string=source_file_name
        )

        bucket = Settings.realm
        prefix = (
            params.folder
            + target_file_name[params.slice_filename_start : params.slice_filename_end]
        )

        s3_key = self._get_target_s3key(
            bucket=bucket, prefix=prefix, file_name=target_file_name
        )

        s3_file_url = StaticS3ResponseValues.S3_PREFIX + bucket + "/" + s3_key

        return s3_file_url

    @staticmethod
    def _get_target_s3key(bucket: str, prefix: str, file_name: str) -> str:
        """

        :param bucket: bucket where to search for
        :param prefix: prefix to search for in bucket
        :param file_name: name of targeted file
        :return: s3_key as str
        """
        list_objects = _get_objects(bucket=bucket, prefix=prefix)
        contents = list_objects.get(StaticS3ResponseValues.CONTENTS)
        if not contents:
            raise signals.FAIL(f"File {file_name} not available in s3")
        elif len(contents) > 1:
            raise signals.FAIL(f"Multiple files found with name: {file_name}")
        return contents[0][StaticS3ResponseValues.KEY]


def _get_objects(bucket: str, prefix: str) -> Dict:
    s3 = boto3.client("s3")
    return s3.list_objects_v2(Bucket=bucket, Prefix=prefix)
