from pathlib import Path

import pandas as pd
from prefect.engine import signals
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.task.base import BaseTask


class FileFormatFields:
    CSV = ".csv"
    XLS = ".xls"
    XLSX = ".xlsx"


class DataframeColumns:
    EXEC_PREV_SEC_NUM = "Exec Prev Seq Number"
    EXEC_SEC_NUM = "Exec Seq Number"
    EXEC_TYPE = "Exec Type"
    IS_CANCEL = "IS CANCEL"
    ORDER = "ORDER"
    ORDER_NUM = "Order Number"
    ROUTE = "ROUTE"
    ROUTE_STATUS = "Route Status"
    STATUS = "Status"
    TEMP_ORDER_EXEC = "_temp_order_num_exec_num_"
    TYPE = "TYPE"


class DataframeValues:
    CANCEL = "CANCEL"
    CANCEL_FIRST_UPPER = "Cancel"
    FILL = "FILL"


class EmsxPreProcessor(BaseTask):

    """
    Takes a Path for a csv file as input, reads it, loads it into memory
    after processing, and writes the file as CSV. Processing according to:
    https://steeleye.atlassian.net/wiki/spaces/IN/pages/2068152493/Order+BBG+-+EMSX

    This task was created for the order-bbg-emsx flow which is expecting two differently
    formatted xls files:
        **Orders/Placements file:** This file has a 3-row header. The first column contains
        one of `ORDER`, `ROUTE` or `FILL`, which indicate which header to use for that
        respective row. The different headers have some different columns.
        Additionally any FILL row is to be ignored.

        **Fills file:** This file contains only information regarding FILL, which are all to
        be processed. This file can be loaded normally. A column is added to reflect
        the first column present on the other file, which is needed for downstream
        processing.
    """

    def execute(
        self, extractor_result: ExtractPathResult = None, **kwargs
    ) -> ExtractPathResult:

        file_path = extractor_result.path
        if not file_path.exists():
            raise signals.SKIP(f"File does not exist at location: {extractor_result}")

        self.logger.info(f"Using file_url `{extractor_result}`")

        if file_path.suffix != FileFormatFields.CSV:
            raise signals.SKIP(f"File is not in CSV format: {extractor_result}")

        try:
            # ORDER and ROUTE dataframe
            data = pd.read_csv(
                file_path, dtype="object", header=None, low_memory=False
            ).fillna(pd.NA)
            data_order = self.get_partial_dataframe(data, DataframeColumns.ORDER)
            data_route = self.get_partial_dataframe(data, DataframeColumns.ROUTE)

            data = (
                pd.concat([data_order, data_route]).reset_index(drop=True).fillna(pd.NA)
            )

            # create cancels as duplicate rows
            data = self.create_cancel_duplicates(dataframe=data)

        except IndexError:
            # FILL dataframe
            del data
            data = pd.read_csv(file_path, dtype="object").fillna(pd.NA)
            data[DataframeColumns.TYPE] = DataframeValues.FILL

            # remove cancels and associated fills
            data = self.remove_cancels(dataframe=data)

        except Exception as error_msg:
            raise signals.FAIL(f"File format not as expected: {error_msg}")

        csv_path = (
            file_path.parent.joinpath(f"{file_path.stem}_transformed")
            .with_suffix(FileFormatFields.CSV)
            .as_posix()
        )
        data.to_csv(csv_path, index=False)

        response = ExtractPathResult(path=Path(csv_path))
        return response

    @staticmethod
    def get_partial_dataframe(source_frame: pd.DataFrame, target: str) -> pd.DataFrame:
        """
        With the target order type: ORDER, ROUTE, FILL get the partial dataframe
        corresponding to the required type
        :param source_frame: original dataframe
        :param target: order type
        :return: partial dataframe
        """

        # fetching only target rows
        partial_df = source_frame[source_frame.loc[:, 0] == target].dropna(
            axis=1, how="all"
        )

        # recreate header
        headers = partial_df.iloc[0]
        partial_df.columns = headers
        partial_df = partial_df[1:]

        # rename order type column
        partial_df = partial_df.rename(columns={target: DataframeColumns.TYPE})

        return partial_df

    @staticmethod
    def create_cancel_duplicates(dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        This task creates a duplicate line for every Order with `Status` == "Cancel"
        or Route with `Route Status` == "Cancel". It also adds the `IS CANCEL` column
        with value True to the duplicates
        :param dataframe: Order/Route dataframe with `Status` and `Route Status` columns
        :return: dataframe with added duplicates for Cancel rows
        """

        order_cancel_mask = (
            dataframe[DataframeColumns.STATUS] == DataframeValues.CANCEL_FIRST_UPPER
        )
        route_cancel_mask = (
            dataframe[DataframeColumns.ROUTE_STATUS]
            == DataframeValues.CANCEL_FIRST_UPPER
        )
        dataframe.loc[:, DataframeColumns.IS_CANCEL] = False
        cancel_mask = order_cancel_mask | route_cancel_mask

        to_cancel = dataframe.loc[cancel_mask, :]
        to_cancel.loc[:, DataframeColumns.IS_CANCEL] = True

        dataframe = (
            pd.concat([dataframe, to_cancel]).reset_index(drop=True).fillna(pd.NA)
        )

        return dataframe

    @staticmethod
    def remove_cancels(dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        Removes `Exec Type` == CANCEL rows and `Exec Type` == FILL rows where
        "FILL+Order Number+Exec Seq Number" matches any "FILL+Order Number+Exec Prev Seq Number"
        from `Exec Type` == CANCEL rows
        :param dataframe: source FILL dataframe
        :return: dataframe without Cancel related rows
        """
        cancels_mask = dataframe[DataframeColumns.EXEC_TYPE] == DataframeValues.CANCEL
        fills_mask = dataframe[DataframeColumns.EXEC_TYPE] == DataframeValues.FILL
        cancel_strings = []
        dataframe.loc[cancels_mask, :].apply(
            lambda x: cancel_strings.append(
                f"{DataframeValues.FILL}"
                f"{x[DataframeColumns.ORDER_NUM]}"
                f"{x[DataframeColumns.EXEC_PREV_SEC_NUM]}"
            ),
            axis=1,
        )
        dataframe.loc[:, DataframeColumns.TEMP_ORDER_EXEC] = dataframe.loc[
            fills_mask,
        ].apply(
            lambda x: f"{x[DataframeColumns.EXEC_TYPE]}"
            f"{x[DataframeColumns.ORDER_NUM]}"
            f"{x[DataframeColumns.EXEC_SEC_NUM]}",
            axis=1,
        )
        same_string_mask = dataframe[DataframeColumns.TEMP_ORDER_EXEC].isin(
            cancel_strings
        )

        temp_column_mask = dataframe.columns.isin([DataframeColumns.TEMP_ORDER_EXEC])

        no_cancels_mask = ~(cancels_mask | (fills_mask & same_string_mask))

        df = dataframe.loc[no_cancels_mask, ~temp_column_mask].reset_index(drop=True)

        return df
