from typing import List
from typing import Optional

import pandas as pd
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.cloud.aws.s3.utils import s3_download_file


class Params(BaseParams):
    metadata_column: str = Field(
        ...,
        description="Column with S3 urls on the batch csv",
    )
    dataframe_columns: Optional[list] = Field(
        default=None,
        description="List of expected columns in the output dataframe."
        " This is used to add missing columns as null. Note that"
        "the task may have extra columns which are not in this list",
    )
    delimiter: str = Field(default=",", description="Source file delimiter")
    encoding: Optional[str] = Field(
        default="utf-8", description="Encoding to be passed into pandas.read_csv"
    )
    column_names: Optional[List] = Field(
        default=None,
        description="Optional parameter for pandas.read_csv. If a list is provided, "
        "it will be used as the column names for the created pandas dataframe",
    )


class CSVBatchCSVDownloader(TransformBaseTask):
    """
    This task creates Dataframe from CSVs in S3.
    For each S3 url present it will download the csv
    which will then be converted to a dataframe.

    Expects a Dataframe with a column with S3 urls pointing to csv files, named in params.

    Outputs a concatenation of the dataframe created from csvs and the input dataframe.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[Params] = None,
        **kwargs,
    ) -> pd.DataFrame:

        content = []

        read_csv_config = dict(
            delimiter=params.delimiter,
            encoding=params.encoding,
            names=params.column_names,
            dtype=str,
        )

        source_frame[params.metadata_column].apply(
            lambda line: content.append(
                self._get_content_from_csv_line(
                    line, config=read_csv_config, params=params
                )
            )
        )

        target: pd.DataFrame = pd.concat(objs=content, ignore_index=True).fillna(pd.NA)

        # Add missing columns
        target = self._add_missing_columns(dataframe=target, params=params)

        return target

    def _get_content_from_csv_line(
        self, line: str, config: dict, params: Params
    ) -> pd.DataFrame:
        """
        downloads json content from s3 url and loads it into a dict
        :param line: s3 url
        :return: json dict
        """
        if pd.isna(line) or not line:
            return pd.DataFrame()

        # get bucket and key
        s3_bucket, s3_key = line[5:].split("/", 1)

        # download from s3
        try:
            csv_path = s3_download_file(
                bucket=s3_bucket, key=s3_key, logger=self.logger
            )
        except Exception as e:
            self.logger.warning(f"Failed to access S3 to download {line}: {e}")
            return pd.DataFrame()

        config["filepath_or_buffer"] = csv_path

        df = pd.read_csv(**config)
        df = df.dropna(how="all")

        df[params.metadata_column] = line

        return df

    @staticmethod
    def _add_missing_columns(dataframe: pd.DataFrame, params: Params) -> pd.DataFrame:
        """
        Each XML Call bundle which uses this task should have a param 'dataframe_columns'
        containing expected XML tags.
        This param should include only the Call Tags which will be mapped in the flow.
        This method adds the columns from params.dataframe_columns into the dataframe
        in the params with null (pd.NA) values if they are not already present
        :param: dataframe: source frame parsed from list of Call metadata files
        :param: params: Params instance
        :returns: source frame with the missing columns populated with null values
        :rtype: pd.DataFrame
        """
        if params.dataframe_columns:
            frame_cols = dataframe.columns.tolist()
            missing_cols = set(params.dataframe_columns).difference(frame_cols)

            if missing_cols:
                empty_frame = pd.DataFrame(
                    index=dataframe.index, columns=missing_cols
                ).fillna(pd.NA)
                dataframe = pd.concat([dataframe, empty_frame], axis=1)

        return dataframe
