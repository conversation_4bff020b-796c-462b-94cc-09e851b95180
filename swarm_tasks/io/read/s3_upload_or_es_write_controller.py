from prefect.engine.signals import FAIL
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.io.read.s3_upload_or_es_write_controller import (
    FailIfNdjsonDataIsEmpty,
)
from se_core_tasks.io.read.s3_upload_or_es_write_controller import (
    FailIfNdjsonPathIsEmpty,
)
from se_core_tasks.io.read.s3_upload_or_es_write_controller import (
    run_s3_or_es_write_controller,
)
from swarm.task.base import BaseTask


class S3OrEsWriteController(BaseTask):
    """
    Tasks which check the upstream ndjson file and check whether the ndjson needs to be
    uploaded to S3 or written to ES (based on the ElasticBulkWriter task)
    """

    def execute(
        self,
        ndjson_dir: ExtractPathResult = None,
        **kwargs,
    ) -> bool:

        try:
            result = run_s3_or_es_write_controller(
                ndjson_dir=ndjson_dir,
                auditor=self.auditor,
                logger=self.logger,
            )
            return result
        except (FailIfNdjsonPathIsEmpty, FailIfNdjsonDataIsEmpty):
            raise FAIL("Task failed due to internal error")
