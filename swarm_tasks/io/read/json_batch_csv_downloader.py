import pandas as pd
from se_core_tasks.io.read.json_batch_csv_downloader import Params as GenericParams
from se_core_tasks.io.read.json_batch_csv_downloader import (
    run_json_batch_csv_downloader,
)
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class JsonBatchCsvDownloader(TransformBaseTask):

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        return run_json_batch_csv_downloader(
            params=params,
            source_frame=source_frame,
            auditor=self.auditor,
            logger=self.logger,
        )
