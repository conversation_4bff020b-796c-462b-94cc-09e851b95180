import json
import os
from enum import Enum
from math import ceil
from pathlib import Path
from typing import Iterator
from typing import List
from typing import Optional

import pandas as pd
from prefect import context
from prefect.engine.signals import SKIP
from pydantic import Field
from se_core_tasks.core.core_dataclasses import Extract<PERSON><PERSON><PERSON><PERSON>ult
from se_core_tasks.core.core_dataclasses import <PERSON><PERSON><PERSON><PERSON><PERSON>R<PERSON>ult
from swarm.schema.static import SwarmColumns
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


class UnstackedColumnNameFormat(str, Enum):
    FULLY_QUALIFIED = "fully_qualified"
    FULLY_QUALIFIED_WITHOUT_SYMBOLS = "fully_qualified_no_symbols"
    INNERMOST_HIERARCHY_LEVEL = "innermost_hierarchy_level"


class Params(BaseParams):
    chunksize: int = Field(default=1000, description="Chunk size of each batch")
    target_delimiter: Optional[str] = Field(
        default=",", description="Delimiter for the target csv files"
    )
    columns_to_unstack: List[str] = Field(
        ...,
        description="List of columns to unstack (explode lists into rows, dicts into columns)",
    )
    unstacked_columns_name_format: UnstackedColumnNameFormat = Field(
        default=UnstackedColumnNameFormat.FULLY_QUALIFIED,
        description="Target dataframe column name format. For hierarchies h1, h2, h3, fully_qualified: h1.h2.h3,"
        "fully_qualified_no_symbols: h1h2h3, innermost_hierarchy_level: h3",
    )
    source_index_field_name: Optional[str] = Field(
        default="__source_index__",
        description="Name for the index column of the df obtained from the json file before unstacking,"
        "this is returned in the target csv if drop_source_index is True",
    )
    drop_source_index: bool = Field(
        default=False,
        description="Drop index column (of the df obtained from the json file before unstacking) if True",
    )
    dataframe_columns: Optional[List[str]] = Field(
        default=None,
        description="List of expected columns in the output dataframe."
        " This is used to add missing columns as null. Note that"
        "the task may have extra columns which are not in this list",
    )
    normalise_columns: bool = Field(
        default=False,
        description="if true then all the columns in the source file will be "
        "converted to upper case and spaces in column names will be "
        "removed.",
    )
    audit_input_rows: bool = Field(
        default=False,
        description="If True, audits the no of rows which were present in the input file",
    )


class NestedJsonFileSplitter(BaseTask):
    """
    This is a nested Json file splitter task which takes as input a json file with one or more records, and
    returns a FileSplitterResult with the number of batches based on the 'chunksize' parameter.
    It is to be used when each JSON record has a field which is nested to many levels.

    For example, the Bondia json files have a field called 'legs', which contain an array (list) of dictionaries.
    First, the json file is converted into a pandas Dataframe, with each row corresponding to a row in the df.
    Then, the 'column_to_unstack' (called legs in this example) is exploded into as many rows as there are dicts
    inside the legs list. Finally, these columns dicts are split such that each key gets its own column.
    The resulting column names are controlled by the 'nested_colnames_type' param (fully-qualified or
    innermost-hierarchy).

    Note: If you have a simple (not nested) json file, please use the normal JsonFileSplitter task.
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        extractor_result: ExtractPathResult = None,
    ) -> List[FileSplitterResult]:
        if os.stat(extractor_result.path).st_size == 0:
            raise SKIP(f"Empty input file: {extractor_result.path}")

        with open(extractor_result.path) as f:
            try:
                content = json.load(f)
            except Exception as e:
                raise SKIP(f"Not a valid JSON: {extractor_result.path}") from e

            if not content:
                raise SKIP(f"Empty file {extractor_result.path}")

            if isinstance(content, dict):
                # single row
                content = [content]

        # Create a data frame (without unstacking the nested column).
        df = pd.json_normalize(content)

        # handle all cases column names from client side
        if params.normalise_columns:
            df.columns = self.normalize_columns(df)

        unstacked_df = self.unstack(
            df, params.columns_to_unstack, params.source_index_field_name
        )
        unstacked_df = self.rename_unstacked_columns(
            unstacked_df, params.unstacked_columns_name_format
        )

        if params.drop_source_index:
            unstacked_df = unstacked_df.drop(columns=[params.source_index_field_name])

        source_dir = Path(context.swarm.sources_dir)

        # Split into batches
        batches = []
        total_count = 0
        for idx, df_chunk in enumerate(
            self._split_dataframe(unstacked_df, params.chunksize)
        ):
            if df_chunk.empty:
                self.auditor.add("Source data empty")
                return batches

            if params.normalise_columns:
                df_chunk.columns = self.normalize_columns(df_chunk)

            df_chunk.index.name = SwarmColumns.SWARM_RAW_INDEX
            df_chunk = df_chunk.reset_index()
            df_chunk = self._add_missing_columns(dataframe=df_chunk, params=params)
            total_count += df_chunk.shape[0]
            batch_csv = self._produce_csv_batch_file(
                batch_dir=source_dir, df=df_chunk, batch_num=idx, params=params
            )
            batch_extract_result = FileSplitterResult(path=batch_csv, batch_index=idx)
            batches.append(batch_extract_result)

        if params.audit_input_rows:
            # Auditing the number of records which were present in the input file
            audit_ctx = {"input_total_count": total_count}
            self.auditor.add(message="", ctx=audit_ctx)

        return batches

    def _produce_csv_batch_file(
        self, batch_dir: Path, df: pd.DataFrame, batch_num: int, params: Params
    ) -> Path:
        """Produces a batch CSV file from a dataframe object"""
        batch_file_path = batch_dir.joinpath(f"batch_{batch_num}.csv")

        self.logger.info(f"Generating batch file: {batch_file_path}")

        df.to_csv(
            batch_file_path.as_posix(),
            index=False,
            encoding="utf-8",
            sep=params.target_delimiter,
        )

        return batch_file_path

    @staticmethod
    def _split_dataframe(df: pd.DataFrame, chunksize: int) -> Iterator[pd.DataFrame]:
        """split dataframe into N chunks of at max chunksize rows"""
        num_chunks = ceil(len(df) / chunksize)
        for n in range(num_chunks):
            yield df[n * chunksize : (n + 1) * chunksize]

    @staticmethod
    def rename_unstacked_columns(
        df: pd.DataFrame, unstacked_columns_name_format: str
    ) -> pd.DataFrame:
        """Renames columns based on the value in unstacked_columns_name_format"""
        if (
            unstacked_columns_name_format
            == UnstackedColumnNameFormat.INNERMOST_HIERARCHY_LEVEL
        ):
            df = df.rename(columns=lambda x: x.split(".")[-1] if x.find(".") else x)
            return df
        if (
            unstacked_columns_name_format
            == UnstackedColumnNameFormat.FULLY_QUALIFIED_WITHOUT_SYMBOLS
        ):
            df = df.rename(columns=lambda x: x.replace(".", ""))
            return df
        # Default column names: fully qualified names with dots between hierarchies.
        return df

    @staticmethod
    def unstack(
        df: pd.DataFrame, columns_to_unstack: List[str], source_index_name: str
    ) -> pd.DataFrame:
        """Unstacks all columns in columns_to_unstack by exploding lists into rows
        and dicts into columns. Retains the original index in a column specified by
        the source_index_name column"""

        present_columns_to_unstack = [
            col for col in columns_to_unstack if col in df.columns
        ]

        for col in present_columns_to_unstack:
            unstack = df.pop(col)
            unstacked_df = pd.concat(
                [
                    pd.json_normalize(data).assign(_index=i)
                    if isinstance(data, list)
                    else pd.DataFrame(data=[i], columns=["_index"])
                    for i, data in enumerate(unstack)
                ],
                ignore_index=True,
            )
            df = pd.merge(
                df, unstacked_df, how="left", left_index=True, right_on="_index"
            )
            df = df.rename(columns={"_index": source_index_name})

        return df

    @staticmethod
    def _add_missing_columns(dataframe: pd.DataFrame, params: Params) -> pd.DataFrame:
        """
        This method adds the columns from params.dataframe_columns into the dataframe
        in the params with null (pd.NA) values if they are not already present
        :param: dataframe: source frame parsed from list of Call metadata files
        :param: params: Params instance
        :returns: source frame with the missing columns populated with null values
        :rtype: pd.DataFrame
        """
        if params.dataframe_columns:
            frame_cols = dataframe.columns.tolist()
            missing_cols = set(params.dataframe_columns).difference(frame_cols)

            if missing_cols:
                empty_frame = pd.DataFrame(
                    index=dataframe.index, columns=missing_cols
                ).fillna(pd.NA)
                dataframe = pd.concat([dataframe, empty_frame], axis=1)

        return dataframe

    @staticmethod
    def normalize_columns(dataframe: pd.DataFrame) -> pd.Index:
        """
        This method converts the column names of a dataframe to small case
        and also converts the blank space to underscore
        For ex: ColumnName -> columnname, Column   Name -> column_name
        :param dataframe: The dataframe to normalize columns for
        :return: pd.Index
        """
        dataframe.columns = dataframe.columns.str.lower().str.replace(r"\s+", "_")
        return dataframe.columns
