from pathlib import Path
from typing import Optional

from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


class LocalFile(BaseTask):
    def execute(
        self,
        params: Optional[BaseParams] = None,
        resources: Optional[BaseResources] = None,
        file_url: str = None,
    ) -> ExtractPathResult:
        file_path = Path(file_url)
        if not file_path.exists():
            raise ValueError(f"File does not exist at location: {file_url}")

        self.logger.info(f"Using file_url `{file_url}`")
        response = ExtractPathResult(path=file_path)
        return response
