from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.boolean.base import BooleanBaseTask


class Params(BaseParams):
    query: str = Field(
        ...,
        description="This should follow the pandas syntax for pd.DataFrame.query.",
    )


class FrameQuery(BooleanBaseTask):
    """
    This task queries rows based on query parameter.
    Returns boolean for given query

    WARNING: the columns in query must exist in the source frame or will raise
    pd.core.computation.ops.UndefinedVariableError

    Params:
        query: should follow pandas query syntax

    Raises:
        ValueError: if query is empty
    """

    params_class = Params

    def execute(
        self, params: Params = None, resources: BaseResources = None, data_input=None
    ) -> bool:
        source_frame = data_input["source_frame"][0].frame
        return not source_frame.query(params.query, engine="python").empty
