from swarm.schema.base import AbstractComponent
from swarm.task.boolean.base import BooleanBaseTask


class Params(AbstractComponent):
    flag: bool


class FlagController(BooleanBaseTask):
    """
    This Task implements a basic control flag.
    Useful for controlFlows.conditionalTask in bundles.
    To be used as exemplified in flow id: tr-universal-ice-fix-trades
    """

    params_class = Params

    def execute(self, params: Params = None, data_input=None) -> bool:

        return params.flag
