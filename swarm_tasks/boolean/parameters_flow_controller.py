from enum import Enum
from typing import Optional

from swarm.task.base import BaseParams
from swarm.task.boolean.base import BooleanBaseTask


class ConditionEnum(str, Enum):
    AND = "AND"
    OR = "OR"


class Params(BaseParams):
    arg_name: str
    condition: ConditionEnum = ConditionEnum.AND
    ignore_case: bool = False
    starts_with: Optional[str] = None
    ends_with: Optional[str] = None
    contains: Optional[str] = None


class ParametersFlowController(BooleanBaseTask):
    """
    This Task implements control test on values from upstream tasks
    """

    params_class = Params

    def execute(self, params: Params = None, data_input=None) -> bool:
        arg_value: str = data_input.get(params.arg_name)

        if not arg_value:
            # self.auditor.add(f"missing required argument {params.arg_name}")
            raise ValueError(f"missing required argument {params.arg_name}")

        if params.ignore_case:
            arg_value = arg_value.lower()

        result_list = []

        if params.starts_with:
            result_list.append(arg_value.startswith(params.starts_with))

        if params.ends_with:
            result_list.append(arg_value.endswith(params.ends_with))

        if params.contains:
            result_list.append(params.contains in arg_value)

        if params.condition == ConditionEnum.OR:
            return any(result_list)
        else:
            return all(result_list)
