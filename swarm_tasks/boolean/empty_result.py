import pandas as pd
from swarm.task.base import BaseResources
from swarm.task.boolean.base import BooleanBaseTask
from swarm.task.transform.result import TransformResult


class EmptyResult(BooleanBaseTask):
    """
    This task checks if the upstream data_input source_frame is empty
    checks first for instance of pd.DataFrame, then TransformResult then generic

    INPORTANT: won't raise exception but will return True
    """

    def execute(
        self, resources: BaseResources = None, data_input=None, **kwargs
    ) -> bool:

        # need the try since the data_input can be anything
        try:
            if not data_input:
                return True

            if isinstance(data_input["source_frame"], pd.DataFrame):
                return data_input["source_frame"].empty

            if isinstance(data_input["source_frame"], TransformResult):
                return data_input["source_frame"].target.empty

            if not data_input["source_frame"]:
                return True

            return False

        except Exception:
            return True
