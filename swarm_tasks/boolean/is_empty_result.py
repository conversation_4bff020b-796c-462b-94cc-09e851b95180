from typing import Optional
from typing import Union

from swarm.task.base import BaseTask
from swarm.task.io.read import FrameProducerResult
from swarm.task.transform.result import TransformResult


class IsEmptyResult(BaseTask):
    """
    This task checks if the upstream result source_frame is empty, returning a boolean
    """

    def execute(
        self,
        result: Optional[Union[FrameProducerResult, TransformResult]] = None,
        **kwargs
    ) -> bool:

        # todo rationalise result types (e.g., align path to frame)
        frame = (
            result.frame if isinstance(result, FrameProducerResult) else result.target
        )

        return frame.empty
