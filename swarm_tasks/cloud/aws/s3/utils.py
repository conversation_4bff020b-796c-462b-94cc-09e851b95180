import asyncio
import csv
import logging
import tempfile
import traceback
import warnings
from contextlib import redirect_stderr
from io import Bytes<PERSON>
from io import String<PERSON>
from pathlib import Path
from typing import Any
from typing import List
from typing import NoReturn
from typing import Optional
from typing import Tuple
from urllib.parse import unquote_plus

import backoff
import boto3
import chardet
import pandas as pd
import requests
from aiobotocore import session as aio_session
from botocore.exceptions import BotoCoreError
from botocore.exceptions import ClientError
from prefect.engine import signals
from se_core_tasks.io.utils import download_raw_file_from_s3
from se_core_tasks.utils.cloud import get_s3_client
from se_core_tasks.utils.data_manipulation import fetch_column_names_from_csv
from se_core_tasks.utils.data_manipulation import snif_csv_delimiter
from swarm.conf import Settings
from swarm.task.auditor import Auditor

DEFAULT_ENCODING = "utf-8"


def s3_download_file(
    bucket: Optional[str],
    key: str,
    logger: logging.Logger = logging.getLogger(__name__),
) -> Path:
    """
    This function downloads a file from S3 and store it in a temp directory. It
    returns the Path to the file in the temp directory
    :param bucket: S3 bucket where the file is, defaults to Settings.realm
    :param key: S3 filepath
    :param logger: Python logger
    :return: temporary file path
    """

    s3 = get_s3_client()
    s3_bucket = bucket if bucket else Settings.realm
    filename = Path(key).name
    temp_dir = tempfile.gettempdir()
    temp_file_path = Path(temp_dir).joinpath(filename)
    logger.info(f"Downloading s3://{bucket}/{key} ...")

    try:
        with open(temp_file_path, "wb") as fh:
            try:
                s3.download_fileobj(s3_bucket, key, fh)
            except ClientError as ce:
                logger.info(
                    f"File could not be downloaded. Exception: {ce}."
                    f"Retrying after unquoting the key"
                )
                s3.download_fileobj(s3_bucket, unquote_plus(key), fh)
    except Exception as e:
        raise e

    return temp_file_path


def read_csv_from_s3_download(
    bucket: str,
    key: str,
    logger: logging.Logger,
    column_names: Optional[List[str]] = None,
) -> pd.DataFrame:
    """
    Download tabular file from S3 and load it to a Pandas DataFrame
    :param bucket: S3 Bucket where .csv file is located e.g.: pinafore.dev.steeleye.co
    :param key: S3 filepath e.g.: onboarding/svg_lookup.csv
    :param logger: Prefect logging.Logger
    :param column_names: list of column names for pd.read_csv's names option;
                         default value: None (first row inferred as column names)
    :return: Pandas DataFrame with S3 tabular file data
    """

    try:
        csv_file_from_s3 = s3_download_file(bucket=bucket, key=key, logger=logger)
    except Exception as e:
        logger.error(f"{e}: it was not possible to retrieve {key} from {bucket}")
        return pd.DataFrame()

    try:
        s3_frame = pd.read_csv(csv_file_from_s3, names=column_names)
    except Exception as e:
        logger.error(f"{e}: it was not possible to read the file to a Pandas DataFrame")
        return pd.DataFrame()

    return s3_frame


async def download_file_and_convert_to_frame(
    client: boto3.client,
    bucket: str,
    file_name: str,
    index: str,
    sep: str,
    detect_encoding: bool,
    column_names,
    logger,
    auditor: Auditor,
    detect_file_sep: bool = False,
    audit_skipped_rows: bool = False,
) -> dict:
    """
    Helper function which reads s3 object as a byte file and then loads it as a pandas df

    :param auditor:
    :param audit_skipped_rows: If True, audits the no of row which were skipped post the filter condition.
    :param client: S3 aio client
    :param bucket: Bucket where the S3 file is present
    :param file_name: Key of the s3 object
    :param index: Index to which the async result is to be appended
    :param detect_encoding: Flag to detect encoding of the bytes data before loading it in dataframe.
                            If False utf-8 is used
    :param column_names: list of column names for pd.read_csv's names option;
                         default value: None (first row inferred as column names)
    :param logger: Prefect logging.Logger:
    :param sep: file separator
    :param detect_file_sep: flag to indicate whether file separator needs to be detected before read. If True
                            overrides the param 'sep'.
    :param logger: Prefect logging.Logger

    :return: dict containing the dataframe referenced with index key.
            eg: {index: Dataframe}
    """
    data_dict = await download_raw_file_from_s3(
        client=client, bucket=bucket, file_name=file_name, index=index
    )
    bytes_data = next(iter(data_dict.values()))

    try:
        encoding = DEFAULT_ENCODING
        if detect_encoding:
            encoding = chardet.detect(bytes_data).get("encoding", DEFAULT_ENCODING)

        if detect_file_sep:
            logger.info("Attempting to detect CSV file delimiter")
            csv_path = f"s3://{Settings.realm}/{file_name}"
            try:
                sep = sniff_delimiter(path=csv_path, encoding=encoding)
                logger.info(f"Detected file separator: {sep}")
            except csv.Error as ce:
                skip_message = (
                    f"CSV file {csv_path} could not be read, as the file was either"
                    f" empty or contained only newline characters/no delimiters. Exception: {ce}."
                )
                logger.error(skip_message)
                return dict()

        warnings.filterwarnings("always", category=pd.errors.ParserWarning)
        stderr_buffer = StringIO()
        with redirect_stderr(stderr_buffer):
            df = pd.read_csv(
                BytesIO(bytes_data),
                sep=sep,
                names=column_names,
                encoding=encoding,
                error_bad_lines=False,
                warn_bad_lines=True,  # this param was updated in Pandas to on_bad_lines{‘error’, ‘warn’, ‘skip’} or
                # callable, default ‘error’ in version 1.3.0.
            )

        stderr_output = stderr_buffer.getvalue()

        # Process warning messages
        warning_messages = 0
        # warning_messages = []
        for line in stderr_output.split("\n"):
            if line.startswith("b'Skipping line"):
                warning_messages += 1

        if audit_skipped_rows and warning_messages > 0:
            audit_ctx = {"skip_count": warning_messages}
            auditor.add(message="", ctx=audit_ctx)
    #
    except Exception as e:
        logger.error(f"{e}: it was not possible to read the file to a Pandas DataFrame")
        return dict()

    return {index: df}


async def async_read_csv_from_s3(
    download_config: list,
    logger: logging.Logger,
    auditor: Auditor,
    column_names: Optional[List[str]] = None,
    detect_encoding: bool = False,
    file_sep: Optional[str] = ",",
    detect_file_separator: bool = False,
    audit_skipped_rows: bool = False,
) -> Any:
    """
    Download tabular file asynchronously from S3 and load it to a Pandas DataFrame
    :param audit_skipped_rows:
    :param auditor:
    :param download_config: List of dict config while contains the detail of the
    file to be downloaded. Follows following format:
        {
            "bucket": <s3 bucket name>,
            "key": <s3 file key>,
            "index": <unique key with acts as index for the data>
        }
    :param logger: Prefect logging.Logger
    :param column_names: list of column names for pd.read_csv's names option;
                         default value: None (first row inferred as column names)
    :param detect_encoding: Flag to detect encoding of the bytes data before loading it in dataframe.
                            If False utf-8 is used
    :param file_sep: Seperator for the file. Defaults to CSV.
    :param detect_file_separator: Flag to indicate if file seperator needs to be detected before read.
    :return: dict containing the dataframe referenced with index key.
            eg: {index: Dataframe}
    """

    try:
        session = aio_session.get_session()
        tasks = list()
        async with session.create_client("s3") as s3:
            for config in download_config:
                tasks.append(
                    asyncio.ensure_future(
                        download_file_and_convert_to_frame(
                            client=s3,
                            bucket=config["bucket"],
                            file_name=config["key"],
                            index=config["index"],
                            sep=file_sep,
                            detect_encoding=detect_encoding,
                            detect_file_sep=detect_file_separator,
                            column_names=column_names,
                            logger=logger,
                            auditor=auditor,
                            audit_skipped_rows=audit_skipped_rows,
                        )
                    )
                )
            df_map = await asyncio.gather(*tasks)
    except Exception:
        logger.error(f"Error reading s3 file with exception: {traceback.format_exc()}")
        return dict()

    return df_map


def s3_list_files(
    bucket: str, folder_path: str, logger: logging.Logger
) -> Optional[List[str]]:
    """Gets a list of files from an s3 folder, and returns them in a
    list. If no files are found, it returns None.

    :param bucket: s3 bucket name
    :param folder_path: s3 folder path
    :param logger: Logger instance
    :return: List of files in bucket/folder_path or None
    """
    s3 = get_s3_client()
    file_list = []
    try:
        paginator = s3.get_paginator("list_objects_v2")
        # Use paginator so we can get results for > 1000 files
        pages = paginator.paginate(Bucket=bucket, Prefix=folder_path)
        for page in pages:
            for obj in page.get("Contents", []):
                if obj.get("Size", 0) > 0:
                    file_list.append(Path(obj.get("Key", "")).name)
    except BotoCoreError as e:
        logger.error(f"Error while getting list of files: {e}")
        return
    # Path(obj.get("Key", "")).name returns '.' if obj.get("Key", "") is None
    return [file_ for file_ in file_list if file_ != "."]


@backoff.on_exception(
    backoff.expo,
    exception=(signals.FAIL, signals.SKIP),
    max_tries=3,
)
def copy_file(
    source_bucket: str,
    source_key: str,
    target_bucket: str,
    target_key: str,
    s3_client: boto3,
    skip_on_error: bool,
    logger: logging.Logger,
) -> NoReturn:
    """
    Method to copy a file from one bucket to another

    :param source_bucket: str, name of source bucket to get the files to copy
    :param source_key: str, name of source key to be copied
    :param target_bucket: str, name of target bucket to copy the files to
    :param target_key: str, name of target key to copy
    :param s3_client: s3 botocore client
    :param skip_on_error: boolean to check if it is suppose to continue to upload the remaining files
    :param logger: logger
    """

    message = f"Failed to copy file {source_bucket}/{source_key} to {target_bucket}/{target_key}"
    try:
        try:
            logger.info(f"Copying {source_key} to {target_key}")
            s3_client.copy(
                CopySource={"Bucket": source_bucket, "Key": source_key},
                Bucket=target_bucket,
                Key=target_key,
            )
        except ClientError as ce:
            logger.info(
                f"File couldn't be read. Exception: {ce}. Retrying after unquoting key"
            )
            s3_client.copy(
                CopySource={"Bucket": source_bucket, "Key": unquote_plus(source_key)},
                Bucket=target_bucket,
                Key=unquote_plus(target_key),
            )
    except (BotoCoreError, ClientError) as exc:
        if skip_on_error:
            logger.warning(exc)
            raise signals.SKIP(message)
        else:
            logger.error(exc)
            raise signals.FAIL(message)


def read_s3_content_as_bytes(
    bucket: str, key: str, logger: logging.Logger, s3_client: boto3.client
) -> Tuple[bytes, requests.Response]:
    """
     Returns the base64 decoded bytes for an s3 object

    :param bucket: the name of the s3 bucket
    :param key: the key representing the object to fetch
    :param logger: logger
    :param s3_client: boto3 s3 client
    :return: decrypted content and s3.get_object response
    """
    try:
        try:
            response = s3_client.get_object(Bucket=bucket, Key=key)
        except ClientError as ce:
            logger.info(
                f"File couldn't be read. Exception: {ce}. Retrying after unquoting key"
            )
            response = s3_client.get_object(Bucket=bucket, Key=unquote_plus(key))
    except Exception as e:
        # If there is still an exception, raise it back to the calling program
        raise e

    content = response.get("Body").read()

    return content, response


def write_file_to_s3(
    bucket: str,
    key: str,
    content: bytes,
    logger: logging.Logger,
    s3_client: boto3.client,
) -> requests.Response:
    """
     Writes a file to s3
    :param bucket: the name of the s3 bucket
    :param key: the key to which the object should be written
    :param logger: logger
    :param content: the content which should be written to s3
    :param s3_client: boto3 s3 client
    :return: HTTP response
    """
    try:
        response = s3_client.put_object(Body=content, Bucket=bucket, Key=key)
    except Exception as e:
        logger.error(f"Error while writing to s3: {e}")
        raise Exception(f"There was a problem while uploading the file to s3: {e}")
    return response


def get_csv_header_from_s3(
    s3_bucket: str,
    s3_key: str,
    logger: logging.Logger,
) -> Optional[List[str]]:
    """
    Gets the header file from an S3 key and returns said header columns as a list.
    If headers could not be fetched, it returns None.
    :param s3_bucket: s3 bucket where header file is located
    :param s3_key: s3 key where header file is located
    :param logger: logger
    :return: list of columns names
    """

    try:
        columns_file_path = s3_download_file(bucket=s3_bucket, key=s3_key)
    except ClientError as e:
        logger.error(
            f"Could not read {s3_bucket}/{s3_key}. Exception: {e}. Returning None"
        )
        return

    names = fetch_column_names_from_csv(file_path=columns_file_path)
    return names


def sniff_delimiter(path: str, encoding: str) -> str:
    """
    :param path: str
    :param encoding: str
    :return: str
    Calls snif_csv_delimiter to detect the delimiter of the file
    """
    return snif_csv_delimiter(csv_file_path=path, encoding=encoding)
