import pandas as pd
from prefect import context
from prefect.engine.signals import FAIL
from se_core_tasks.map.map_attribute import FailIfMoreThanOneDelimiter
from se_core_tasks.map.map_attribute import Params as GenericParams
from se_core_tasks.map.map_attribute import run_map_attribute
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class MapAttribute(TransformBaseTask):

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            params=params,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame,
        params: GenericParams,
        auditor=None,
        logger=context.get("logger"),
    ) -> pd.DataFrame:
        try:
            return run_map_attribute(
                source_frame=source_frame,
                params=params,
                auditor=auditor,
                logger=logger,
            )
        except FailIfMoreThanOneDelimiter as e:
            raise FAIL(f"Flow has failed: {e}")
