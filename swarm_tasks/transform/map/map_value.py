import pandas as pd
from prefect import context
from se_core_tasks.map.map_value import Params as GenericParams
from se_core_tasks.map.map_value import run_map_value
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class MapValue(TransformBaseTask):

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        value_map: dict = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            params=params,
            value_map=value_map,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame,
        params: GenericParams,
        value_map: dict = None,
        auditor=None,
        logger=context.get("logger"),
    ) -> pd.DataFrame:

        return run_map_value(
            source_frame=source_frame,
            params=params,
            value_map=value_map,
            auditor=auditor,
            logger=logger,
        )
