import pandas as pd
from se_core_tasks.map.map_from_nested import Params as MapFromNestedParams
from se_core_tasks.map.map_from_nested import run_map_from_nested
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(MapFromNestedParams, BaseParams):
    pass


class MapFromNested(TransformBaseTask):

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources=None,
        **kwargs
    ) -> pd.DataFrame:

        return run_map_from_nested(source_frame=source_frame, params=params, **kwargs)
