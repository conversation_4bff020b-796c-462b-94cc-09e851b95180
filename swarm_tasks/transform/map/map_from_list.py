from typing import List
from typing import Optional

import pandas as pd
from pydantic import Field
from pydantic import root_validator
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):
    source_attribute: str = Field(..., description="Source column name.")
    target_attribute: str = Field(..., description="Target column name.")
    mask: str = Field(
        None, description="This should follow the pandas syntax for pd.DataFrame.query."
    )
    item_index: Optional[int] = Field(
        None, description="Index of the item to extract.", ge=0
    )
    column_to_exclude_list_elements_by: Optional[str] = Field(
        None,
        description="For when we want to retrieve all elements of the list in `source_attribute`'s "
        "rows except for the value in `column_to_exclude_list_elements_by`. Note that this should refer to the "
        "name of a DataFrame column, not the actual value to exclude by",
    )

    @root_validator
    def check_mandatory_params(cls, values):
        item_index = values.get("item_index")
        column_to_exclude_list_elements_by = values.get(
            "column_to_exclude_list_elements_by"
        )

        if not isinstance(item_index, int) and not column_to_exclude_list_elements_by:
            raise ValueError(
                "Either `item_index` or `column_to_exclude_list_elements_by` must be populated"
            )

        if isinstance(item_index, int) and column_to_exclude_list_elements_by:
            raise ValueError(
                "Only one of `item_index` or `column_to_exclude_list_elements_by` must be populated"
            )

        return values


class MapFromList(TransformBaseTask):
    """
    This task extracts data from the list in each row
    value on `params.source_attribute` in source_frame
    according to the `params.item_index`.

    If `params.column_to_exclude_list_elements_by` is specified instead, this task will take all list elements
    from `params.source_attribute` except for the associated value in the
    `params.column_to_exclude_list_elements_by` column
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:
        return self.process(source_frame=source_frame, params=params)

    @classmethod
    def process(cls, source_frame: pd.DataFrame, params: Params):
        target = pd.DataFrame(index=source_frame.index)
        target[params.target_attribute] = pd.NA

        if source_frame.empty:
            return target

        if params.mask is not None:
            mask = source_frame.eval(params.mask, engine="python")
        else:
            mask = pd.Series(True, index=source_frame.index)

        data = source_frame.loc[mask, params.source_attribute]

        if data.empty:
            return target

        not_null_mask = data.notnull()

        # Item index logic - note that item_index can be 0, thus we're asserting that it is an int, therefore not a None
        if isinstance(params.item_index, int):
            not_null_mask = not_null_mask & data.loc[not_null_mask].map(
                lambda x: len(x) >= params.item_index + 1
                if isinstance(x, list)
                else False
            )

            if not not_null_mask.any():
                return target

            target.loc[mask & not_null_mask, params.target_attribute] = data.loc[
                not_null_mask
            ].map(lambda x: x[params.item_index])

        # Exclude list elements by column logic
        elif params.column_to_exclude_list_elements_by:
            if params.column_to_exclude_list_elements_by in source_frame.columns:
                column_to_exclude_by_df = pd.concat(
                    [
                        data,
                        source_frame.loc[
                            mask, params.column_to_exclude_list_elements_by
                        ],
                    ],
                    axis=1,
                )

                target.loc[
                    mask, params.target_attribute
                ] = column_to_exclude_by_df.apply(
                    lambda row: cls._map_from_list_with_exclusion(row=row),
                    axis=1,
                )
            else:
                target.loc[mask, params.target_attribute] = data

        return target

    @staticmethod
    def _map_from_list_with_exclusion(row: pd.Series) -> Optional[List]:
        """
        `row` is a Pandas Series with two elements: the first is the list value we want to map from;
        the second is the actual value what we want to exclude from the list.

        If the list value is not populated, return pd.NA
        If the value to exclude from the list is not populated, return the list
        Otherwise, return the list without the excluded value

        :param row: Pandas Series with two elements
        :return: List elements with exclusion or pd.NA
        """

        row_list_value = row[0]
        row_column_to_exclude_by_value = row[1]

        if not isinstance(row_list_value, list) and pd.isna(row_list_value):
            return pd.NA

        if pd.isna(row_column_to_exclude_by_value):
            return row_list_value

        return [
            item for item in row_list_value if item != row_column_to_exclude_by_value
        ]
