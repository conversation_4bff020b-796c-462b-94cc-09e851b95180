import pandas as pd
from se_core_tasks.map.map_conditional import Params as GenericPara<PERSON>
from se_core_tasks.map.map_conditional import run_map_conditional
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class MapConditional(TransformBaseTask):

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame, params=params, auditor=self.auditor, **kwargs
        )

    @classmethod
    def process(
        cls, source_frame: pd.DataFrame, params: GenericParams, auditor=None, **kwargs
    ) -> pd.DataFrame:

        return run_map_conditional(
            source_frame=source_frame, params=params, auditor=auditor, **kwargs
        )
