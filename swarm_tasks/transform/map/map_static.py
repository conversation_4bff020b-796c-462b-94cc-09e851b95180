import pandas as pd
from prefect import context
from se_core_tasks.map.map_static import Params as GenericParams
from se_core_tasks.map.map_static import run_map_static
from swarm.schema.static import SwarmColumns
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class MapStatic(TransformBaseTask):

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            params=params,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame,
        params: GenericParams,
        auditor=None,
        logger=context.get("logger"),
    ) -> pd.DataFrame:
        return run_map_static(
            source_frame=source_frame,
            params=params,
            auditor=auditor,
            logger=logger,
            raw_index_name=SwarmColumns.SWARM_RAW_INDEX,
        )
