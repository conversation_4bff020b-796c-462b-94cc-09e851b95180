import pandas as pd
from prefect import context
from se_trades_tasks.order.best_execution.modify_best_execution_volume import (
    Params as GenericParams,
)
from se_trades_tasks.order.best_execution.modify_best_execution_volume import (
    run_modify_best_execution_volume_results,
)
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class ModifyBestExecutionResults(TransformBaseTask):
    """
    Does a basic operation on the values of the BestExecutionData Volume
    fields based on the params provided.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            params=params,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame,
        params: Params,
        auditor=None,
        logger=context.get("logger"),
    ) -> pd.DataFrame:

        return run_modify_best_execution_volume_results(
            source_frame=source_frame,
            params=params,
            auditor=auditor,
            logger=logger,
        )
