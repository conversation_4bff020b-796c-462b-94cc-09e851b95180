import pandas as pd
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):
    group_by_attribute: str
    duplicate_attribute: str


class FilterGroupByDuplicates(TransformBaseTask):
    """

    This task groups a `source_dataframe` by the `group_by_attribute` column
    and applies the `get_first_index_of_non_null_occurrence` method to the subset.
    Note that:
    1- All columns of source_frame are returned
    2- Indexes of source_frame are NOT changed

    Algo example:

    source_frame
       a     b
    0  0     a
    1  1     b
    2  1  <NA>
    3  2  <NA>
    4  2     w
    5  3  <NA>

    source_frame.loc[indexes]
       a     b
    0  0     a
    1  1     b
    4  2     w
    5  3  <NA>
    """

    params_class = Params

    def execute(
        self, source_frame: pd.DataFrame = None, params: Params = None, **kwargs
    ) -> pd.DataFrame:

        if (
            source_frame.empty
            or params.group_by_attribute not in source_frame.columns
            or params.duplicate_attribute not in source_frame.columns
        ):
            return pd.DataFrame(index=source_frame.index)

        indexes = (
            source_frame.groupby(params.group_by_attribute)
            .apply(
                lambda x: self.get_first_index_of_non_null_occurrence(
                    subset_df=x, duplicate_attribute=params.duplicate_attribute
                )
            )
            .tolist()
        )
        return source_frame.loc[indexes]

    @staticmethod
    def get_first_index_of_non_null_occurrence(
        subset_df: pd.DataFrame, duplicate_attribute: str
    ) -> int:
        """
        Creates a mask of non null values for the `duplicate_attribute` column in the `subset_df`
        If the subset_df only has null values for the `duplicate_attribute` column:
            Returns first index of `subset_df`, regardless of there being multiple null duplicates or just a single null value
        If the subset_df has non-null values:
            Drops all null values (if they exist) and returns the index of the first non-null occurrence

        :param subset_df: Pandas DataFrame
        :param duplicate_attribute: Column with duplicate values
        :return: int - first index of non null occurrence
        """
        mask = subset_df.loc[:, duplicate_attribute].notnull()
        return (
            subset_df.index[0]
            if not mask.any()
            else subset_df.loc[mask, duplicate_attribute].dropna().index[0]
        )
