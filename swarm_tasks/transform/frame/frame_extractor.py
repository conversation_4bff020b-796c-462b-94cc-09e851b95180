from typing import Dict

import pandas as pd
from prefect import context
from prefect.engine import signals
from se_core_tasks.frame.frame_extractor import Params as GenericParams
from se_core_tasks.frame.frame_extractor import run_frame_extractor
from se_core_tasks.frame.frame_extractor import SkipIfEmptyFrame
from se_core_tasks.frame.frame_extractor import SkipIfMissingData
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams, GenericParams):
    pass


class FrameExtractor(BaseTask):

    params_class = Params

    def execute(
        self,
        dict_of_dfs: Dict[str, pd.DataFrame] = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            dict_of_dfs=dict_of_dfs,
            params=params,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        dict_of_dfs: Dict[str, pd.DataFrame],
        params: Params,
        auditor=None,
        logger=context.get("logger"),
    ) -> pd.DataFrame:

        try:
            return run_frame_extractor(
                dict_of_dfs=dict_of_dfs,
                params=params,
                auditor=auditor,
                logger=logger,
            )
        except (SkipIfMissingData, SkipIfEmptyFrame) as e:
            raise signals.SKIP(f"Flow will be skipped: {e.message}")
