from enum import Enum
from typing import List
from typing import Optional

import pandas as pd
from pydantic import Field
from pydantic import root_validator
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Action(Enum):
    add = "add"
    strip = "strip"
    rename = "rename"


class Params(BaseParams):
    prefix: Optional[str] = Field(
        None,
        description="Prefix in the source column",
    )
    suffix: Optional[str] = Field(
        None,
        description="Suffix in the source column",
    )
    action: Action = Field(
        description="Action the needs to be performed on the "
        "source attribute column."
    )
    rename_dict: Optional[dict] = Field(
        None,
        description="This is a dict containing the original column name and the "
        "desired name to rename.{'original_name1':'desired_name1', "
        "'original_name2':'desired_name2',}",
    )

    rename_columns: Optional[List[dict]] = Field(
        None,
        description="This is a yaml dict with `source_attribute` and `target_attribute` that supports dot "
        "column source names",
    )

    @root_validator
    def validate_param_rename_uniqueness(cls, values):
        if values.get("rename_columns") and values.get("rename_dict"):
            raise ValueError(
                "Only one can be asigned at the same `rename_dict` or `rename_columns`"
            )
        return values

    @root_validator
    def validate_param(cls, values):
        params_given = [x for x, y in values.items() if y is not None]
        expected_params = ["prefix", "suffix", "rename_dict", "rename_columns"]
        if not any(y in expected_params for y in params_given):
            raise ValueError("At least one param must be defined.")
        return values


class FrameColumnManipulator(TransformBaseTask):
    """This task manipulates the column names of the dataframe based on the params
    provided.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        **kwargs,
    ) -> pd.DataFrame:

        if source_frame.empty:
            return pd.DataFrame()

        target = source_frame

        if params.action == Action.add:
            if params.prefix:
                target = target.add_prefix(params.prefix)
            elif params.suffix:
                target = target.add_suffix(params.suffix)

        elif params.action == Action.strip:
            if params.prefix:
                prefix_str = rf"^{params.prefix}"
                target.columns = target.columns.str.replace(prefix_str, "")
            elif params.suffix:
                suffix_str = rf"{params.suffix}$"
                target.columns = target.columns.str.replace(suffix_str, "")

        elif params.action == Action.rename:
            if params.rename_dict:
                target = target.rename(columns=params.rename_dict)

            elif params.rename_columns:
                target_dict = {
                    pair.get("source_attribute"): pair.get("target_attribute")
                    for pair in params.rename_columns
                }
                target = target.rename(columns=target_dict)

        return target
