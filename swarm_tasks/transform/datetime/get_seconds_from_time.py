import pandas as pd
from prefect import context
from prefect.engine.signals import SKIP
from se_core_tasks.datetime.get_seconds_from_time import Params as GenericParams
from se_core_tasks.datetime.get_seconds_from_time import run_get_seconds_from_time
from se_core_tasks.datetime.get_seconds_from_time import SkipIfSourceFrameEmpty
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class GetSecondsFromTime(TransformBaseTask):

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ):

        return self.process(
            source_frame=source_frame,
            params=params,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        auditor=None,
        logger=context.get("logger"),
    ):

        try:
            return run_get_seconds_from_time(
                source_frame=source_frame,
                params=params,
                auditor=auditor,
                logger=logger,
            )
        except SkipIfSourceFrameEmpty as e:
            raise SKIP(f"Flow will be skipped: {e.message}")
