import pandas as pd
from se_core_tasks.utils.datetime import DatetimeFormat


def add_delta_to_epoch_time(
    time_data: pd.Series, time_delta: pd.Series, time_unit: str, delta_unit: str
) -> pd.Series:
    """
    :param time_data: pd.Series
    :param time_delta: pd.Series
    :param time_unit: str
    :param delta_unit: str
    :return: pd.Series
    Adds time_delta in epoch to time_data and returns pandas datetime series formatted
    as "%Y-%m-%dT%H:%M:%S.%fZ"
    Use case: if we have timestamp_start in epoch and duration of a call then we can calculate
    timestamp_end using this function.
    """
    result = pd.to_datetime(time_data.dropna(), unit=time_unit) + pd.to_timedelta(
        time_delta.dropna(), unit=delta_unit
    )
    return result.dt.strftime(DatetimeFormat.DATETIME)


def format_call_duration(time_delta_series: pd.Series) -> pd.Series:
    """
    :param time_delta_series: pd.Series (Timedelta object)
    :return: pd.Series
    Returns time component of timedelta object
    e.g. 0 days 00:00:07.047000 -> 00:00:07
    """
    return time_delta_series.astype(str).str.split(" ").str[-1].str.split(".").str[0]
