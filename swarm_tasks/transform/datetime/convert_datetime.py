import pandas as pd
from prefect import context
from se_core_tasks.datetime.convert_datetime import Params as GenericParams
from se_core_tasks.datetime.convert_datetime import run_convert_datetime
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class ConvertDatetime(TransformBaseTask):

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            params=params,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        self,
        source_frame: pd.DataFrame,
        params: Params,
        auditor=None,
        logger=context.get("logger"),
    ) -> pd.DataFrame:

        return run_convert_datetime(
            source_frame=source_frame,
            params=params,
            auditor=auditor,
            logger=logger,
        )
