from enum import Enum

import pandas as pd
from pydantic import Field
from se_core_tasks.utils.datetime import strft<PERSON>el<PERSON>
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Unit(str, Enum):
    MILLISECONDS = "ms"
    SECONDS = "seconds"


class Params(BaseParams):
    source_attribute: str = Field(
        ..., description="input column name containing time delta values"
    )
    target_attribute: str = Field(..., description="output column name")
    source_unit: Unit


class ConvertTimeDelta(TransformBaseTask):
    """
    This task takes a duration column and returns a column with a time-only string of the format HH:MM:SS
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: BaseResources = None,
        **kwargs,
    ) -> pd.DataFrame:
        return self.process(source_frame=source_frame, params=params)

    @classmethod
    def process(cls, source_frame: pd.DataFrame, params: Params):
        target = pd.DataFrame(index=source_frame.index)
        target_attr_not_null_mask = source_frame[params.source_attribute].notnull()
        target.loc[
            target_attr_not_null_mask, params.target_attribute
        ] = pd.to_timedelta(
            source_frame.loc[target_attr_not_null_mask, params.source_attribute].astype(
                float
            ),
            unit=params.source_unit.value,
        ).apply(
            strftimedelta
        )
        return target.fillna(pd.NA)
