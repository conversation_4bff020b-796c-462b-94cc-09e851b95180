import datetime
from typing import Optional
from typing import Union

import pandas as pd
from pydantic import Field
from pydantic import validator
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):
    source_datetime_attribute: str = Field(
        ..., description="name of column with the datetime that will be incremented"
    )
    source_datetime_format: str = Field(
        ...,
        description="datetime format of params.source_datetime_attribute (incremented datetime will use the same format)",
    )
    source_incrementor_attribute: str = Field(
        ..., description="name of column with the value which will be used to increment"
    )
    source_incrementor_unit: str = Field(
        ...,
        description="unit to convert the params.source_incrementor_attribute value to a Pandas Timedelta object"
        "see https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Timedelta.html"
        "for more detail on valid units",
    )
    target_attribute: str = Field(
        ..., description="name of target column with incremented datetime"
    )
    decrement: Optional[bool] = Field(
        False, description="will decrement instead of increment if True"
    )

    @validator("source_incrementor_unit")
    def validate_timedelta(cls, value):

        if value is not None:
            try:
                # arbitrary 1 to test converting the params.unit to a valid timedelta
                pd.Timedelta(f"1 {value}")
            except ValueError:
                raise ValueError(
                    "'source_incrementor_unit' param must be convertible to a Pandas Timedelta object "
                    "(ex: 'hours' or 'milliseconds')"
                )
        return value


class IncrementDatetime(TransformBaseTask):
    """
    This task takes a datetime column params.source_datetime_attribute
    and returns a column params.target_attribute with the
    datetime incremented by params.timedelta
    """

    params_class = Params

    def execute(
        self, source_frame: pd.DataFrame = None, params: Params = None, **kwargs
    ) -> pd.DataFrame:
        return self.process(
            source_frame=source_frame,
            params=params,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Params = None,
    ) -> pd.DataFrame:
        target = pd.DataFrame(index=source_frame.index)
        target.loc[:, params.target_attribute] = pd.NA
        used_columns = [
            params.source_datetime_attribute,
            params.source_incrementor_attribute,
        ]
        columns_mask = source_frame.columns.isin(used_columns)
        not_null_source_attributes = source_frame.loc[:, columns_mask].notnull().all(1)

        target.loc[
            not_null_source_attributes, params.target_attribute
        ] = source_frame.loc[not_null_source_attributes, columns_mask].apply(
            lambda x: cls._increment_datetime(
                datetime_as_str=x[params.source_datetime_attribute],
                datetime_format=params.source_datetime_format,
                incrementor=x.loc[params.source_incrementor_attribute],
                unit=params.source_incrementor_unit,
                decrement=params.decrement,
            ),
            axis=1,
        )

        return target

    @staticmethod
    def _increment_datetime(
        datetime_as_str: str,
        datetime_format: str,
        incrementor: Union[str, float, int],
        unit: str,
        decrement: bool,
    ) -> str:
        """
        Increment a datetime string
        :param datetime_as_str: datetime (string) to be incremented
        :param datetime_format: format to convert the `datetime_as_str` to datetime object
        :param incrementor: value to increment to datetime
        :param unit: incrementor value unit
        :param decrement: decrement instead of increment if True
        :return: incremented datetime as string with the same `datetime_format` format
        """

        datetime_dt = datetime.datetime.strptime(datetime_as_str, datetime_format)
        timedelta = pd.Timedelta(f"{incrementor} {unit}")
        result_datetime = (
            datetime_dt + timedelta if not decrement else datetime_dt - timedelta
        )
        return result_datetime.strftime(datetime_format)
