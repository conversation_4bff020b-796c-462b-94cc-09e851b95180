""" This task offers similar functionality as the increment_datetime.py task, but uses the arrow library
to do date arithmetic. This task can be used for adding/subtracting years, months, days, weeks, quarters
from datetimes/dates. The value to be added/subtracted can be passed in as a source frame attribute or
a static value.

The primary reason the task exists is that it allows months to be subtracted from a date/datetime
 -- this cannot be done in increment_datetime. There are a few ambiguities when doing month
arithmetic which this task takes care of. Note that for cases where subtracting a month gives an invalid date,
arrow uses the closest valid date.
E.g. Subtract 1 month from '20210331': '20210228' is the last valid date in February.
Similarly, subtracting 1 month from '20210330' also returns '20210228'

So this is used when you need a datetime/date result, but you care about
the month and year values more than the day (when instruments are linked, the month and year are used rather
than the day).
The above example also holds for the other units of time.
"""
from typing import Optional

import pandas as pd
import pendulum
from pydantic import Field
from pydantic import root_validator
from se_elastic_schema.elastic_schema.core.base import BaseStrEnum
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

# TODO: Add time arithmetic too?
static_manipulator_column = "static_manipulator"


class ManipulatorUnit(BaseStrEnum):
    MONTHS = "months"
    QUARTERS = "quarters"
    SECONDS = "seconds"
    WEEKS = "weeks"
    YEARS = "years"


class Params(BaseParams):
    source_attribute: str = Field(
        ...,
        description="name of datetime/date column (dtype:str) to which months needed to be added/subtracted",
    )
    source_attribute_format: str = Field(
        "YYYY-MM-DD",
        description="arrow datetime format of params.source_attribute. This should be"
        "specified in the 'YYYY MM DD HH mm ss' format with the required special characters in between.",
    )
    source_manipulator_attribute: Optional[str] = Field(
        None,
        description="name of column with the value which will be used to increment/decrement source_attribute",
    )
    static_manipulator: Optional[int] = Field(
        None,
        description="static value which will be used to increment/decrement source_attribute",
    )
    source_manipulator_unit: ManipulatorUnit = Field(
        ...,
        description="unit of time which should be added/subtracted. These are units defined within arrow",
    )
    target_attribute: str = Field(
        ..., description="name of target column with added/subtracted months"
    )
    target_attribute_format: str = Field(
        "YYYY-MM-DD",
        description="arrow datetime format of params.target_attribute. This should be specified"
        "in the 'YYYY MM DD HH mm ss' format with the required special characters in between.",
    )
    decrement: Optional[bool] = Field(
        False, description="will decrement instead of increment if True"
    )

    @root_validator
    def validate_source_manipulator_attribute_and_static_manipulator(cls, values):
        if not (
            bool(values.get("source_manipulator_attribute"))
            ^ bool(values.get("static_manipulator"))
        ):
            raise ValueError(
                "Please specify either source_manipulator_attribute or static_manipulator (but not both)"
            )

        return values


class ArrowDatetimeArithmetic(TransformBaseTask):
    """
    This task takes a sourrce date/datetime column params.source_attribute and either a
    params.source_manipulator_attribute or a params.static_manipulator value. It
    adds/subtracts source_manipulator_attribute/static_manipulator to the source date
    in params.source_manipulator_unit units, and returns a column params.target_attribute.
    """

    params_class = Params

    def execute(
        self, source_frame: pd.DataFrame = None, params: Params = None, **kwargs
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index)
        target.loc[:, params.target_attribute] = pd.NA
        if not any(source_frame.loc[:, params.source_attribute].notnull()):
            return target
        if params.source_manipulator_attribute:
            not_null_source_manipulator_attribute_mask = source_frame.loc[
                :, params.source_manipulator_attribute
            ].notnull()
            if params.decrement:
                source_frame.loc[
                    not_null_source_manipulator_attribute_mask,
                    params.source_manipulator_attribute,
                ] = source_frame.loc[
                    not_null_source_manipulator_attribute_mask,
                    params.source_manipulator_attribute,
                ].apply(
                    lambda x: -x
                )
            source_frame.loc[:, params.source_manipulator_attribute] = source_frame.loc[
                :, params.source_manipulator_attribute
            ].fillna(0)
            # Do all the processing and return the target data frame
            return self._process(
                source_frame, params.source_manipulator_attribute, target, params
            )

        elif params.static_manipulator:
            val_to_add = (
                -params.static_manipulator
                if params.decrement
                else params.static_manipulator
            )
            # Add new column with static increment value
            source_frame[static_manipulator_column] = val_to_add
            return self._process(
                source_frame, static_manipulator_column, target, params
            )

    def _process(
        self,
        source_frame: pd.DataFrame,
        manipulator_attribute: str,
        target: pd.DataFrame,
        params: Params = None,
    ) -> pd.DataFrame:
        """Takes a source data frame with expected columns (columns present in params) and an empty target df,
        along with a column in the source_df defined by the manipulator_attribute param. Returns the target
        data frame with the required arrows arithmetic done
        """
        used_columns = [params.source_attribute, manipulator_attribute]
        columns_mask = source_frame.columns.isin(used_columns)
        not_null_source_attributes = (
            source_frame.loc[:, columns_mask].notnull().all(axis=1)
        )
        target.loc[
            not_null_source_attributes, params.target_attribute
        ] = source_frame.loc[not_null_source_attributes, columns_mask].apply(
            lambda x: self.datetime_adder_subtractor(
                source_datetime=x[params.source_attribute],
                source_datetime_format=params.source_attribute_format,
                manipulator=x[manipulator_attribute],
                target_attribute_format=params.target_attribute_format,
                unit=params.source_manipulator_unit.value,
            ),
            axis=1,
        )
        return target

    @staticmethod
    def datetime_adder_subtractor(
        source_datetime: str,
        source_datetime_format: str,
        manipulator: int,
        target_attribute_format: str,
        unit: str,
    ) -> str:
        """Increments/decrements a datetime string based on the unit and manipulator arguments.
        When using unit='months', the bevahiour of this function might be slightly different than
        you might expect, as seen in the below example with source_datetime = 20210331 and manipulator=-1.

        Examples:
        date:
        date_adder_subtractor('20210325', 'YYYYMMDD', 2, 'YYYY-MM-DD', 'months')
        '2021-05-25'
        date_adder_subtractor('20210325', 'YYYYMMDD', -1, 'YYYY-MM-DD', 'months')
        '2021-02-25'
        date_adder_subtractor('20210331', 'YYYYMMDD', -1, 'YYYY-MM-DD', 'months')
        '2021-02-28'
        date_adder_subtractor('20210331', 'YYYYMMDD', -1, 'YYYY-MM-DD', 'years')
        '2020-03-31'
        date_adder_subtractor('20210331', 'YYYYMMDD', -1, 'YYYY-MM-DD', 'quarters')
        '2020-12-31'
        date_adder_subtractor('20210325', 'YYYYMMDD', 2, 'YYYY-MM-DD', 'weeks')
        '2021-04-08'

        datetime:
        date_adder_subtractor('20210325 19:14:12', 'YYYYMMDD HH:mm:ss', 2, 'YYYY-MM-DD HH:mm:ss', 'days')
        '2021-03-27 19:14:12'
        date_adder_subtractor('20210325 19:14:12', 'YYYYMMDD HH:mm:ss', -4, 'YYYY-MM-DD HH:mm:ss', 'months')
        '2020-11-25 19:14:12'

        """
        # Explicitly convert manipulator to int as datetime.add takes in only int values
        manipulator = int(manipulator)

        output_date = pendulum.from_format(source_datetime, source_datetime_format)
        dateunit_kwargs = {}
        # variable kwargs for the arrow shift function
        if unit == "quarters":
            dateunit_kwargs["months"] = manipulator * 3
        else:
            dateunit_kwargs[unit] = manipulator
        output_date = output_date.add(**dateunit_kwargs)
        return output_date.format(target_attribute_format)
