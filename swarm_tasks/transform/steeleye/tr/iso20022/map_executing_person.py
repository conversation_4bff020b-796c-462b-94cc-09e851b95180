from typing import Dict
from typing import List
from typing import Set

import numpy as np
import pandas as pd
import prefect
from se_elastic_schema.models import TenantConfiguration
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class In:
    MARKET_IDENTIFIERS = "marketIdentifiers"
    REPORT_STATUS = "reportDetails.reportStatus"
    MIFIR_ID = "parties.executionWithinFirm.officialIdentifiers.mifirId"
    DEC_MKR_ID_TP = "parties.executionWithinFirm.structure.decisionMaker.idType"
    DEC_MKR_ID = "parties.executionWithinFirm.structure.decisionMaker.id"
    BRANCH_COUNTRY = "parties.executionWithinFirm.officialIdentifiers.branchCountry"
    MIFIR_ID_TP = "parties.executionWithinFirm.officialIdentifiers.mifirIdType"
    MIFIR_ID_SUB_TP = "parties.executionWithinFirm.officialIdentifiers.mifirIdSubType"


class Out:
    ALGO = "ExctgPrsn.Algo"
    CLNT = "ExctgPrsn.Clnt"
    PRSN_BRANCH_COUNTRY = "ExctgPrsn.Prsn.CtryOfBrnch"
    PRSN_MIFIR_ID = "ExctgPrsn.Prsn.Othr.Id"
    PRSN_SCHEME_CD = "ExctgPrsn.Prsn.Othr.SchmeNm.Cd"
    PRSN_SCHEME_PRTRY = "ExctgPrsn.Prsn.Othr.SchmeNm.Prtry"


class MapExecutingPerson(TransformBaseTask):
    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: BaseParams = None,
        resources: BaseResources = None,
        **kwargs
    ) -> pd.DataFrame:
        # mask source to new transactions only
        source_frame = source_frame.loc[source_frame[In.REPORT_STATUS] == "NEWT"]

        target = pd.DataFrame(index=source_frame.index)

        missing_cols = self.required_columns().difference(set(source_frame.columns))
        if missing_cols:
            source_frame = source_frame.assign(**{col: None for col in missing_cols})

        # mask algos by decision maker id type
        algo_mask = source_frame[In.DEC_MKR_ID_TP] == "A"

        # assign algo
        if algo_mask.any():
            target[Out.ALGO] = source_frame.loc[algo_mask, In.DEC_MKR_ID]

        client_nore_mask = ~algo_mask & (
            source_frame[In.MARKET_IDENTIFIERS].apply(
                self.extract_market_identifier, path="parties.executionWithinFirm"
            )
            == "NORE"
        )

        # assign client NORE
        if client_nore_mask.any():
            target.loc[client_nore_mask, Out.CLNT] = "NORE"

        tenant_configuration: TenantConfiguration = (
            prefect.context.swarm.tenant_configuration
        )

        # extract pi enrichment from tenant config via context
        pi_enrichment = tenant_configuration.trPIEnrichmentEnabled or False

        if pi_enrichment:
            # assign person mifir id
            target.loc[~client_nore_mask, Out.PRSN_MIFIR_ID] = source_frame.loc[
                ~client_nore_mask, In.MARKET_IDENTIFIERS
            ].apply(
                func=self.extract_market_identifier,
                path="parties.executionWithinFirm",
            )

            enrichment_mask = ~client_nore_mask & target[Out.PRSN_MIFIR_ID].notnull()

            # assign person prtry scheme
            target.loc[enrichment_mask, Out.PRSN_SCHEME_PRTRY] = "T"

            # assign person branch country
            target.loc[enrichment_mask, Out.PRSN_BRANCH_COUNTRY] = "XX"

        else:
            # mask by mifir id type
            mifir_n_mask = ~algo_mask & (source_frame[In.MIFIR_ID_TP] == "N")

            # assign person branch country
            target[Out.PRSN_BRANCH_COUNTRY] = source_frame.loc[
                mifir_n_mask, In.BRANCH_COUNTRY
            ]

            # assign person mifir id
            target[Out.PRSN_MIFIR_ID] = source_frame.loc[mifir_n_mask, In.MIFIR_ID]

            # assign person cd scheme
            target[Out.PRSN_SCHEME_CD] = source_frame.loc[
                mifir_n_mask
                & (source_frame[In.MIFIR_ID_SUB_TP].isin(["NIDN", "CCPT"])),
                In.MIFIR_ID_SUB_TP,
            ]

            # assign person prtry scheme
            target[Out.PRSN_SCHEME_PRTRY] = source_frame.loc[
                mifir_n_mask
                & ~(source_frame[In.MIFIR_ID_SUB_TP].isin(["NIDN", "CCPT"])),
                In.MIFIR_ID_SUB_TP,
            ]

        return target

    @staticmethod
    def extract_market_identifier(market_identifiers: List[Dict[str, str]], path: str):
        filtered = [mi for mi in market_identifiers if mi["path"] == path]
        if not filtered:
            return np.nan

        _, parsed_id = filtered[0].get("labelId").split(":", 1)
        return parsed_id.upper()

    @staticmethod
    def required_columns() -> Set[str]:
        cols = {
            In.MARKET_IDENTIFIERS,
            In.REPORT_STATUS,
            In.MIFIR_ID,
            In.DEC_MKR_ID_TP,
            In.DEC_MKR_ID,
            In.BRANCH_COUNTRY,
            In.MIFIR_ID_TP,
            In.MIFIR_ID_SUB_TP,
        }
        return cols
