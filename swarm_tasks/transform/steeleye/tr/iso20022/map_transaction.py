from typing import Set

import numpy as np
import pandas as pd
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.utilities.decimal import enforce_significant_figures


class In:
    REPORT_STATUS = "reportDetails.reportStatus"
    TRADING_DT = "transactionDetails.tradingDateTime"
    TRADING_CAP = "transactionDetails.tradingCapacity"
    DERIV_NTNL_CH = "transactionDetails.derivativeNotionalChange"
    VENUE = "transactionDetails.venue"
    CTRY_OF_BRANCH = "transactionDetails.branchMembershipCountry"
    TRADE_PLC_MATCH_ID = "reportDetails.tradingVenueTransactionIdCode"
    COMPLEX_TRADE_CMPT_ID = "transactionDetails.complexTradeComponentId"
    QTY = "transactionDetails.quantity"
    QTY_NOTATION = "transactionDetails.quantityNotation"
    QTY_CCY = "transactionDetails.quantityCurrency"
    NET_AMOUNT = "transactionDetails.netAmount"
    UP_FRONT_PMT = "transactionDetails.upFrontPayment"
    UP_FRONT_PMT_CCY = "transactionDetails.upFrontPaymentCurrency"
    PRICE_PENDING = "transactionDetails.pricePending"
    PRICE_NOTATION = "transactionDetails.priceNotation"
    PRICE = "transactionDetails.price"
    PRICE_CCY = "transactionDetails.priceCurrency"
    NOTIONAL_CCY = "instrumentDetails.instrument.notionalCurrency1"


class Out:
    TRADING_DT = "Tx.TradDt"
    TRADING_CAP = "Tx.TradgCpcty"
    DERIV_NTNL_CH = "Tx.DerivNtnlChng"
    VENUE = "Tx.TradVn"
    CTRY_OF_BRANCH = "Tx.CtryOfBrnch"
    TRADE_PLC_MATCH_ID = "Tx.TradPlcMtchgId"
    COMPLEX_TRADE_CMPT_ID = "Tx.CmplxTradCmpntId"
    QTY_UNIT = "Tx.Qty.Unit"
    QTY_NOMINAL_VAL = "Tx.Qty.NmnlVal.#text"
    QTY_NOMINAL_VAL_CCY = "Tx.Qty.NmnlVal.@Ccy"
    QTY_MONETARY_VAL = "Tx.Qty.MntryVal.#text"
    QTY_MONETARY_VAL_CCY = "Tx.Qty.MntryVal.@Ccy"
    NET_AMOUNT = "Tx.NetAmt"
    UP_FRONT_PMT_AMOUNT = "Tx.UpFrntPmt.Amt.#text"
    UP_FRONT_PMT_AMOUNT_CCY = "Tx.UpFrntPmt.Amt.@Ccy"
    PRICE_PENDING = "Tx.Pric.NoPric.Pdg"
    PRICE_MONETARY_VAL = "Tx.Pric.Pric.MntryVal.Amt.#text"
    PRICE_MONETARY_VAL_CCY = "Tx.Pric.Pric.MntryVal.Amt.@Ccy"
    PRICE_PCTG_VAL = "Tx.Pric.Pric.Pctg"
    PRICE_YLD_VAL = "Tx.Pric.Pric.Yld"
    PRICE_BASIS_PTS_VAL = "Tx.Pric.Pric.BsisPts"


class MapTransaction(TransformBaseTask):
    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: BaseParams = None,
        resources: BaseResources = None,
        **kwargs
    ) -> pd.DataFrame:
        # mask source to new transactions only
        source_frame = source_frame.loc[source_frame[In.REPORT_STATUS] == "NEWT"]

        target = pd.DataFrame(index=source_frame.index)

        missing_cols = self.required_columns().difference(set(source_frame.columns))
        if missing_cols:
            source_frame = source_frame.assign(**{col: None for col in missing_cols})

        target[Out.TRADING_DT] = source_frame.loc[:, In.TRADING_DT]
        target[Out.TRADING_CAP] = source_frame.loc[:, In.TRADING_CAP]
        target[Out.DERIV_NTNL_CH] = source_frame.loc[:, In.DERIV_NTNL_CH]
        target[Out.VENUE] = source_frame.loc[:, In.VENUE]
        target[Out.CTRY_OF_BRANCH] = source_frame.loc[:, In.CTRY_OF_BRANCH]
        target[Out.TRADE_PLC_MATCH_ID] = source_frame.loc[:, In.TRADE_PLC_MATCH_ID]
        target[Out.COMPLEX_TRADE_CMPT_ID] = source_frame.loc[
            :, In.COMPLEX_TRADE_CMPT_ID
        ]

        # map unit quantity
        target[Out.QTY_UNIT] = source_frame.loc[
            (source_frame[In.QTY_NOTATION] == "UNIT"),
            In.QTY,
        ]

        # map nominal quantity and currency
        nominal_mask = source_frame[In.QTY_NOTATION].isin(["NOML", "NOME"])
        target[Out.QTY_NOMINAL_VAL] = source_frame.loc[nominal_mask, In.QTY].astype(str)
        target[Out.QTY_NOMINAL_VAL_CCY] = source_frame.loc[
            nominal_mask,
            In.QTY_CCY,
        ]

        # map monetary quantity and currency
        monetary_mask = source_frame[In.QTY_NOTATION] == "MONE"
        target[Out.QTY_MONETARY_VAL] = source_frame.loc[monetary_mask, In.QTY].astype(
            str
        )
        target[Out.QTY_MONETARY_VAL_CCY] = source_frame.loc[
            monetary_mask,
            In.QTY_CCY,
        ]

        # map net amount
        target[Out.NET_AMOUNT] = (
            source_frame.loc[:, In.NET_AMOUNT]
            .dropna()
            .apply(lambda x: np.format_float_positional(x))
            .apply(
                lambda x: enforce_significant_figures(value=x, precision=18, scale=5)
            )
        )

        # map up front payment amount
        target[Out.UP_FRONT_PMT_AMOUNT] = (
            source_frame.loc[:, In.UP_FRONT_PMT].dropna().astype(str)
        )

        # map up front payment amount currency
        target[Out.UP_FRONT_PMT_AMOUNT_CCY] = source_frame.loc[:, In.UP_FRONT_PMT_CCY]

        # map price pending
        price_pending_mask = source_frame[In.PRICE_PENDING].notnull() & (
            source_frame[In.PRICE_PENDING] is True
        )
        if price_pending_mask.any():
            target.loc[price_pending_mask, Out.PRICE_PENDING] = "PNDG"

        # map monetary price
        mone_mask = (source_frame[In.PRICE_NOTATION] == "MONE") & ~price_pending_mask
        target[Out.PRICE_MONETARY_VAL] = (
            source_frame.loc[mone_mask, In.PRICE]
            .dropna()
            .apply(lambda x: np.format_float_positional(x))
            .apply(
                lambda x: enforce_significant_figures(value=x, precision=18, scale=13)
            )
        )
        target[Out.PRICE_MONETARY_VAL_CCY] = source_frame.loc[
            mone_mask, In.PRICE_CCY
        ].fillna(source_frame[In.NOTIONAL_CCY])

        # map percentage price
        target[Out.PRICE_PCTG_VAL] = (
            source_frame.loc[
                (source_frame[In.PRICE_NOTATION] == "PERC") & ~price_pending_mask,
                In.PRICE,
            ]
            .dropna()
            .apply(lambda x: np.format_float_positional(x))
            .apply(
                lambda x: enforce_significant_figures(value=x, precision=11, scale=10)
            )
        )

        # map yield price
        target[Out.PRICE_YLD_VAL] = (
            source_frame.loc[
                (source_frame[In.PRICE_NOTATION] == "YIEL") & ~price_pending_mask,
                In.PRICE,
            ]
            .dropna()
            .apply(lambda x: np.format_float_positional(x))
            .apply(
                lambda x: enforce_significant_figures(value=x, precision=11, scale=10)
            )
        )

        # map basis points price
        target[Out.PRICE_BASIS_PTS_VAL] = (
            source_frame.loc[
                (source_frame[In.PRICE_NOTATION] == "BAPO") & ~price_pending_mask,
                In.PRICE,
            ]
            .dropna()
            .apply(lambda x: np.format_float_positional(x))
            .apply(
                lambda x: enforce_significant_figures(value=x, precision=18, scale=17)
            )
        )

        return target

    @staticmethod
    def required_columns() -> Set[str]:
        cols = {
            In.REPORT_STATUS,
            In.TRADING_DT,
            In.TRADING_CAP,
            In.DERIV_NTNL_CH,
            In.VENUE,
            In.CTRY_OF_BRANCH,
            In.TRADE_PLC_MATCH_ID,
            In.COMPLEX_TRADE_CMPT_ID,
            In.QTY,
            In.QTY_CCY,
            In.QTY_NOTATION,
            In.NET_AMOUNT,
            In.UP_FRONT_PMT,
            In.UP_FRONT_PMT_CCY,
            In.PRICE,
            In.PRICE_CCY,
            In.PRICE_NOTATION,
            In.PRICE_PENDING,
            In.NOTIONAL_CCY,
        }
        return cols
