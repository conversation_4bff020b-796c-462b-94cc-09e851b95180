from typing import Set

import pandas as pd
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class In:
    REPORT_STATUS = "reportDetails.reportStatus"
    PARTIES_BUYER = "parties.buyer"
    PARTIES_BUYER_DECISION_MAKER = "parties.buyerDecisionMaker"
    PARTIES_SELLER = "parties.seller"
    PARTIES_SELLER_DECISION_MAKER = "parties.sellerDecisionMaker"
    FIRM_LEI = "firmIdentifiers.lei"
    FIRM_BRANCH_COUNTRY = "firmIdentifiers.branchCountry"
    OFFICIAL_MIFIR_ID = "officialIdentifiers.mifirId"
    OFFICIAL_MIFIR_ID_SUB = "officialIdentifiers.mifirIdSubType"
    OFFICIAL_BRANCH_COUNTRY = "officialIdentifiers.branchCountry"
    PERSONAL_FIRST_NAME = "personalDetails.firstName"
    PERSONAL_LAST_NAME = "personalDetails.lastName"
    PERSONAL_DOB = "personalDetails.dob"


class Out:
    BUYER = "Buyr"
    SELLER = "Sellr"
    ACCT_OWNR = "AcctOwnr"
    DEC_MKR = "DcsnMakr"
    ACCT_OWNR_ID_INTL = "Id.Intl"
    ACCT_OWNR_ID_LEI = "Id.LEI"
    ACCT_OWNR_CTRY_BRANCH = "CtryOfBrnch"
    ACCT_OWNR_PRSN_FIRST_NM = "Id.Prsn.FrstNm"
    ACCT_OWNR_PRSN_NM = "Id.Prsn.Nm"
    ACCT_OWNR_PRSN_BIRTH_DT = "Id.Prsn.BirthDt"
    ACCT_OWNR_PRNS_OTHER_ID = "Id.Prsn.Othr.Id"
    ACCT_OWNR_PRSN_SCHEME_CD = "Id.Prsn.Othr.SchmeNm.Cd"
    ACCT_OWNR_PRSN_SCHEME_PRTRY = "Id.Prsn.Othr.SchmeNm.Prtry"
    DEC_MKR_LEI = "LEI"
    DEC_MKR_PRSN_FIRST_NM = "Prsn.FrstNm"
    DEC_MKR_PRSN_NM = "Prsn.Nm"
    DEC_MKR_PRSN_BIRTH_DT = "Prsn.BirthDt"
    DEC_MKR_PRNS_OTHER_ID = "Prsn.Othr.Id"
    DEC_MKR_PRSN_SCHEME_CD = "Prsn.Othr.SchmeNm.Cd"
    DEC_MKR_PRSN_SCHEME_PRTRY = "Prsn.Othr.SchmeNm.Prtry"


class MapBuyerSeller(TransformBaseTask):
    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: BaseParams = None,
        resources: BaseResources = None,
    ) -> pd.DataFrame:
        # mask source to new transactions only
        source_frame = source_frame.loc[source_frame[In.REPORT_STATUS] == "NEWT"]

        target = pd.DataFrame(index=source_frame.index)

        missing_cols = self.required_columns().difference(set(source_frame.columns))
        if missing_cols:
            source_frame = source_frame.assign(**{col: None for col in missing_cols})

        self.logger.info("explode and shape buyer")
        buyers = self.explode_and_shape(parties=source_frame.loc[:, In.PARTIES_BUYER])

        self.logger.info("explode and shape buyer decision maker")
        buyer_decision_makers = self.explode_and_shape(
            parties=source_frame.loc[:, In.PARTIES_BUYER_DECISION_MAKER]
        )

        self.logger.info("map buyer")
        target[Out.BUYER] = self.map_parties(
            account_owners=buyers,
            decision_makers=buyer_decision_makers,
            source_index=source_frame.index,
        )

        self.logger.info("explode and shape seller")
        sellers = self.explode_and_shape(parties=source_frame.loc[:, In.PARTIES_SELLER])

        self.logger.info("explode and shape seller decision maker")
        seller_decision_makers = self.explode_and_shape(
            parties=source_frame.loc[:, In.PARTIES_SELLER_DECISION_MAKER]
        )

        self.logger.info("map seller")
        target[Out.SELLER] = self.map_parties(
            account_owners=sellers,
            decision_makers=seller_decision_makers,
            source_index=source_frame.index,
        )

        return target

    def map_parties(
        self,
        account_owners: pd.DataFrame,
        decision_makers: pd.DataFrame,
        source_index: pd.Index,
    ):
        ao = self.map_account_owners(parties=account_owners, source_index=source_index)
        dm = self.map_decision_makers(
            parties=decision_makers, source_index=source_index
        )

        # merge mapped account owners and decision makers
        target = pd.merge(left=ao, right=dm, left_index=True, right_index=True)

        # return single dict with account owner and decision maker
        result = target.apply(lambda x: x.to_dict(), axis=1)

        return result

    @staticmethod
    def map_decision_makers(parties: pd.DataFrame, source_index: pd.Index):
        decision_makers = pd.DataFrame(index=parties.index)

        firm_mask = (
            parties[In.FIRM_LEI].notnull() | parties[In.FIRM_BRANCH_COUNTRY].notnull()
        )

        # decision maker as firm
        decision_makers.loc[firm_mask, Out.DEC_MKR_LEI] = parties.loc[
            firm_mask, In.FIRM_LEI
        ]

        # decision maker as person
        decision_makers.loc[~firm_mask, Out.DEC_MKR_PRSN_FIRST_NM] = parties.loc[
            ~firm_mask, In.PERSONAL_FIRST_NAME
        ].str.upper()
        decision_makers.loc[~firm_mask, Out.DEC_MKR_PRSN_NM] = parties.loc[
            ~firm_mask, In.PERSONAL_LAST_NAME
        ].str.upper()
        decision_makers.loc[~firm_mask, Out.DEC_MKR_PRSN_BIRTH_DT] = parties.loc[
            ~firm_mask, In.PERSONAL_DOB
        ]
        decision_makers.loc[~firm_mask, Out.DEC_MKR_PRNS_OTHER_ID] = parties.loc[
            ~firm_mask, In.OFFICIAL_MIFIR_ID
        ]

        decision_makers.loc[~firm_mask, Out.ACCT_OWNR_PRSN_SCHEME_CD] = parties.loc[
            ~firm_mask, In.OFFICIAL_MIFIR_ID_SUB
        ]
        decision_makers.loc[~firm_mask, Out.ACCT_OWNR_CTRY_BRANCH] = parties.loc[
            ~firm_mask, In.OFFICIAL_BRANCH_COUNTRY
        ]

        target = pd.DataFrame(index=source_index)

        # set decision maker to list of dicts
        target[Out.DEC_MKR] = decision_makers.apply(lambda x: x.to_dict(), axis=1)

        return target

    @staticmethod
    def map_account_owners(parties: pd.DataFrame, source_index: pd.Index):
        account_owners = pd.DataFrame(index=parties.index)

        firm_mask = (
            parties[In.FIRM_LEI].notnull() | parties[In.FIRM_BRANCH_COUNTRY].notnull()
        )
        intc_mask = parties[In.FIRM_LEI].str.upper() == "INTC"

        # account owner as firm
        account_owners.loc[firm_mask & intc_mask, Out.ACCT_OWNR_ID_INTL] = parties.loc[
            firm_mask & intc_mask, In.FIRM_LEI
        ].str.upper()
        account_owners.loc[firm_mask & ~intc_mask, Out.ACCT_OWNR_ID_LEI] = parties.loc[
            firm_mask & ~intc_mask, In.FIRM_LEI
        ]
        account_owners.loc[
            firm_mask & ~intc_mask, Out.ACCT_OWNR_CTRY_BRANCH
        ] = parties.loc[firm_mask & ~intc_mask, In.FIRM_BRANCH_COUNTRY]

        # account owner as person
        account_owners.loc[~firm_mask, Out.ACCT_OWNR_PRSN_FIRST_NM] = parties.loc[
            ~firm_mask, In.PERSONAL_FIRST_NAME
        ].str.upper()
        account_owners.loc[~firm_mask, Out.ACCT_OWNR_PRSN_NM] = parties.loc[
            ~firm_mask, In.PERSONAL_LAST_NAME
        ].str.upper()
        account_owners.loc[~firm_mask, Out.ACCT_OWNR_PRSN_BIRTH_DT] = parties.loc[
            ~firm_mask, In.PERSONAL_DOB
        ]
        account_owners.loc[~firm_mask, Out.ACCT_OWNR_PRNS_OTHER_ID] = parties.loc[
            ~firm_mask, In.OFFICIAL_MIFIR_ID
        ]

        account_owners.loc[~firm_mask, Out.ACCT_OWNR_PRSN_SCHEME_CD] = parties.loc[
            ~firm_mask, In.OFFICIAL_MIFIR_ID_SUB
        ]
        account_owners.loc[~firm_mask, Out.ACCT_OWNR_CTRY_BRANCH] = parties.loc[
            ~firm_mask, In.OFFICIAL_BRANCH_COUNTRY
        ]

        target = pd.DataFrame(index=source_index)

        # set account owner to list of dicts
        target[Out.ACCT_OWNR] = account_owners.apply(lambda x: x.to_dict(), axis=1)

        return target

    def explode_and_shape(self, parties: pd.Series):
        exploded = parties.explode().apply(pd.Series)

        frames = list()

        if "firmIdentifiers" in exploded.columns:
            firm_identifiers = (
                exploded["firmIdentifiers"]
                .apply(pd.Series)
                .add_prefix("firmIdentifiers.")
            )
            frames.append(firm_identifiers)

        if "officialIdentifiers" in exploded.columns:
            official_identifiers = (
                exploded["officialIdentifiers"]
                .apply(pd.Series)
                .add_prefix("officialIdentifiers.")
            )
            frames.append(official_identifiers)

        if "personalDetails" in exploded.columns:
            personal_details = (
                exploded["personalDetails"]
                .apply(pd.Series)
                .add_prefix("personalDetails.")
            )
            frames.append(personal_details)

        if not frames:
            self.logger.warning("missing required party details")

        merged = (
            pd.concat(frames, axis=1) if frames else pd.DataFrame(index=parties.index)
        )

        missing_cols = self.required_party_columns().difference(set(merged.columns))
        if missing_cols:
            merged = merged.assign(**{col: None for col in missing_cols})

        merged = merged.loc[:, self.required_party_columns()]

        return merged

    @staticmethod
    def required_columns() -> Set[str]:
        cols = {
            In.REPORT_STATUS,
            In.PARTIES_BUYER,
            In.PARTIES_SELLER,
            In.PARTIES_BUYER_DECISION_MAKER,
            In.PARTIES_SELLER_DECISION_MAKER,
        }
        return cols

    @staticmethod
    def required_party_columns() -> Set[str]:
        cols = {
            In.FIRM_BRANCH_COUNTRY,
            In.FIRM_LEI,
            In.OFFICIAL_BRANCH_COUNTRY,
            In.OFFICIAL_MIFIR_ID,
            In.OFFICIAL_MIFIR_ID_SUB,
            In.PERSONAL_FIRST_NAME,
            In.PERSONAL_LAST_NAME,
            In.PERSONAL_DOB,
        }
        return cols
