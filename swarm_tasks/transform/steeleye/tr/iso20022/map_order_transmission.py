from typing import Set

import pandas as pd
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class In:
    REPORT_STATUS = "reportDetails.reportStatus"
    TRANSMISSION_IND = "transmissionDetails.orderTransmissionIndicator"
    BUYER_TRANS_FIRM_LEI = "parties.buyerTransmittingFirm.firmIdentifiers.lei"
    SELLER_TRANS_FIRM_LEI = "parties.sellerTransmittingFirm.firmIdentifiers.lei"


class Out:
    TRANSMISSION_IND = "OrdrTrnsmssn.TrnsmssnInd"
    TRANSMITTING_BUYER = "OrdrTrnsmssn.TrnsmttgBuyr"
    TRANSMITTING_SELLER = "OrdrTrnsmssn.TrnsmttgSellr"


class MapOrderTransmission(TransformBaseTask):
    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: BaseParams = None,
        resources: BaseResources = None,
    ) -> pd.DataFrame:
        # mask source to new transactions only
        source_frame = source_frame.loc[source_frame[In.REPORT_STATUS] == "NEWT"]

        target = pd.DataFrame(index=source_frame.index)

        missing_cols = self.required_columns().difference(set(source_frame.columns))
        if missing_cols:
            source_frame = source_frame.assign(**{col: None for col in missing_cols})

        target[Out.TRANSMISSION_IND] = source_frame.loc[:, In.TRANSMISSION_IND]
        target[Out.TRANSMITTING_BUYER] = source_frame.loc[:, In.BUYER_TRANS_FIRM_LEI]
        target[Out.TRANSMITTING_SELLER] = source_frame.loc[:, In.SELLER_TRANS_FIRM_LEI]

        return target

    @staticmethod
    def required_columns() -> Set[str]:
        cols = {
            In.REPORT_STATUS,
            In.TRANSMISSION_IND,
            In.BUYER_TRANS_FIRM_LEI,
            In.SELLER_TRANS_FIRM_LEI,
        }
        return cols
