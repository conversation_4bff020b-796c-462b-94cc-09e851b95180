import re
from dataclasses import dataclass
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Set
from typing import Union

import numpy as np
import pandas as pd
from se_elastic_schema.static.mifid2 import RTS22TransactionStatus
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.utilities.decimal import enforce_significant_figures


class In:
    BUYER = "parties.buyer"
    REPORT_STATUS = "reportDetails.reportStatus"
    MIFIR_ELIGIBLE = "workflow.eligibility.eligible"
    ELIGIBILITY_TOTV = "workflow.eligibility.totv"
    WORKFLOW_STATUS = "workflow.status"
    EXECUTING_ENTITY_LEI = "parties.executingEntity.firmIdentifiers.lei"
    INSTRUMENT_ID_CODE = "instrumentDetails.instrument.instrumentIdCode"
    INSTRUMENT_FULL_NAME = "instrumentDetails.instrument.instrumentFullName"
    INSTRUMENT_CLASSIFICATION = "instrumentDetails.instrument.instrumentClassification"
    NOTIONAL_CCY = "instrumentDetails.instrument.notionalCurrency1"
    NOTIONAL_CCY_2 = "instrumentDetails.instrument.fxDerivatives.notionalCurrency2"
    BOND_MATURITY_DATE = "instrumentDetails.instrument.bond.maturityDate"
    ON_FIRDS = "instrumentDetails.instrument.ext.onFIRDS"
    ELIGIBILITY_ON_FIRDS = "workflow.eligibility.onFirds"
    VENUE_IN_EEA = "instrumentDetails.instrument.ext.venueInEEA"
    TXN_VENUE = "transactionDetails.venue"
    DELIVERY_TYPE = "instrumentDetails.instrument.derivative.deliveryType"
    DERIV_EXPIRY_DT = "instrumentDetails.instrument.derivative.expiryDate"
    DERIV_PRICE_MULT = "instrumentDetails.instrument.derivative.priceMultiplier"
    DERIV_OPTION_TP = "instrumentDetails.instrument.derivative.optionType"
    DERIV_OPTION_EX_STYLE = (
        "instrumentDetails.instrument.derivative.optionExerciseStyle"
    )
    DERIV_STRIKE_PRICE = "instrumentDetails.instrument.derivative.strikePrice"
    DERIV_STRIKE_PRICE_CCY = (
        "instrumentDetails.instrument.derivative.strikePriceCurrency"
    )
    DERIV_STRIKE_PRICE_TP = "instrumentDetails.instrument.ext.strikePriceType"
    # new fields
    DERIV_UNDERLYING_INDEX_TERM = (
        "instrumentDetails.instrument.derivative.underlyingIndexTerm"
    )
    DERIV_UNDERLYING_INDEX_TERM_VALUE = (
        "instrumentDetails.instrument.derivative.underlyingIndexTermValue"
    )
    DERIV_UNDERLYING_INDEX_NAME = (
        "instrumentDetails.instrument.derivative.underlyingIndexName"
    )
    DERIV_UNDERLYING_INSTRUMENTS = (
        "instrumentDetails.instrument.derivative.underlyingInstruments"
    )
    EXT_UNDERLYING_INSTRUMENTS = (
        "instrumentDetails.instrument.ext.underlyingInstruments"
    )
    SWAP_DIRECTIONALITIES = "transactionDetails.swapDirectionalities"


class Out:
    # isin
    ID = "FinInstrm.Id"
    # general attributes
    # Disabled as per https://steeleye.atlassian.net/browse/IAPS-37
    # OTHR_GEN_ID = "FinInstrm.Othr.FinInstrmGnlAttrbts.Id"
    OTHR_GEN_FULL_NM = "FinInstrm.Othr.FinInstrmGnlAttrbts.FullNm"
    OTHR_GEN_CLS_TP = "FinInstrm.Othr.FinInstrmGnlAttrbts.ClssfctnTp"
    OTHR_GEN_NTNL_CCY = "FinInstrm.Othr.FinInstrmGnlAttrbts.NtnlCcy"
    # debt attributes
    OTHR_DEBT_MAT_DT = "FinInstrm.Othr.DebtInstrmAttrbts.MtrtyDt"
    # derivative attributes
    OTHR_DERIV_DLVRY_TP = "FinInstrm.Othr.DerivInstrmAttrbts.DlvryTp"
    OTHR_DERIV_XPRY_DT = "FinInstrm.Othr.DerivInstrmAttrbts.XpryDt"
    OTHR_DERIV_PRICE_MULT = "FinInstrm.Othr.DerivInstrmAttrbts.PricMltplr"
    OTHR_DERIV_OPTION_TP = "FinInstrm.Othr.DerivInstrmAttrbts.OptnTp"
    OTHR_DERIV_OPTION_EX_STYLE = "FinInstrm.Othr.DerivInstrmAttrbts.OptnExrcStyle"
    OTHR_DERIV_STRIKE_PRICE_PCTG = (
        "FinInstrm.Othr.DerivInstrmAttrbts.StrkPric.Pric.Pctg"
    )
    OTHR_DERIV_STRIKE_PRICE_YLD = "FinInstrm.Othr.DerivInstrmAttrbts.StrkPric.Pric.Yld"
    OTHR_DERIV_STRIKE_PRICE_MONE_AMT = (
        "FinInstrm.Othr.DerivInstrmAttrbts.StrkPric.Pric.MntryVal.Amt.#text"
    )
    OTHR_DERIV_STRIKE_PRICE_MONE_CCY = (
        "FinInstrm.Othr.DerivInstrmAttrbts.StrkPric.Pric.MntryVal.Amt.@Ccy"
    )
    OTHR_DERIV_STRIKE_PRICE_BSIS = (
        "FinInstrm.Othr.DerivInstrmAttrbts.StrkPric.Pric.BsisPts"
    )
    OTHR_DERIV_STRIKE_PRICE_PNDG_PDG = (
        "FinInstrm.Othr.DerivInstrmAttrbts.StrkPric.NoPric.Pdg"
    )
    OTHR_DERIV_STRIKE_PRICE_PNDG_CCY = (
        "FinInstrm.Othr.DerivInstrmAttrbts.StrkPric.NoPric.Ccy"
    )
    OTHR_DERIV_UNDRLYGINSTRM_SNGL = (
        "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Sngl"
    )
    OTHR_DERIV_UNDRLYGINSTRM_OTHR_SNGL_INDX_NM_REFRATE_NM = (
        "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.Indx.Nm.RefRate.Nm"
    )
    OTHR_DERIV_UNDRLYGINSTRM_OTHR_SNGL = (
        "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl"
    )
    OTHR_DERIV_UNDRLYGINSTRM_OTHR_SNGL_ISIN = (
        "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.ISIN"
    )
    OTHR_DERIV_UNDRLYGINSTRM_OTHR_SNGL_INDX_TERM_UNIT = (
        "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.Indx.Term.Unit"
    )
    OTHR_DERIV_UNDRLYGINSTRM_OTHR_SNGL_INDX_TERM_VALUE = (
        "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.Indx.Term.Value"
    )
    OTHR_DERIV_UNDRLYGINSTRM_OTHR_BSKT = (
        "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Bskt"
    )
    OTHR_DERIV_UNDRLYGINSTRM_OTHR_BSKT_INDX = (
        "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Bskt.Indx"
    )
    OTHR_DERIV_UNDRLYGINSTRM_OTHR_SNGL_BSKT = (
        "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.Indx"
    )
    OTHR_DERIV_UNDRLYGINSTRM_SWP_SNGL_ISIN = (
        "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.%(swap)s.Sngl.ISIN"
    )
    OTHR_DERIV_UNDRLYGINSTRM_SWP_SNGL = (
        "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.%(swap)s.Sngl"
    )
    OTHR_DERIV_UNDRLYGINSTRM_SWP_BSKT = (
        "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.%(swap)s.Bskt"
    )
    OTHER_DERIV_ASSETCLASS_FX_NTNLCCY = (
        "FinInstrm.Othr.DerivInstrmAttrbts.AsstClssSpcfcAttrbts.FX.OthrNtnlCcy"
    )
    OTHR_DERIV_UNDRLYGINSTRM_SWP_BSKT_INDX = (
        "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.%(swap)s.Bskt.Indx"
    )
    OTHR_DERIV_UNDRLYGINSTRM_SWP_SNGL_INDX = (
        "FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.%(swap)s.Sngl.Indx"
    )


class UnderlyingInstrument:
    ID_CODE = "underlyingInstrumentCode"
    UNDERLYING_INDEX_NAME = "underlyingIndexName"


class TempCols:
    INDEX_NAME = "__index_name__"
    INDEX_TERM = "__index_term__"
    INDEX_VALUES = "__index_values__"
    UNDERLYING_INDEX_IDS = "__underlying_index_ids__"
    PARENT_INDEX_VALUES = "__parent_index_values__"
    SWAP_IN_COUNT = "__swap_in_count__"
    SWAP_OUT_COUNT = "__swap_out_count__"
    OTHER_COUNT = "__other_count__"
    SWAP_IN_VALUES = "__swap_in_values__"
    SWAP_OUT_VALUES = "__swap_out_values__"
    OTHER_VALUES = "__outher_values__"


class TempKeys:
    UNDERLYING_ISIN = "ul_isin"
    UNDERLYING_INDEX_NAME = "ul_index_name"
    UNDERLYING_INDEX_ID = "ul_index_id"
    UNDERLYING_INDEX_TERM = "ul_index_term"
    UNDERLYING_INDEX_TERM_VALUE = "ul_index_term_value"


class IndexPaths:
    INDEX = "Indx"
    ISIN = "ISIN"
    NAME_REF_RATE = "Nm.RefRate.Nm"
    NAME_REF_RATE_INDEX = "Nm.RefRate.Indx"
    NAME_TERM_UNIT = "Nm.Term.Unit"
    NAME_TERM_VALUE = "Nm.Term.Val"
    TERM_VALUE = "Term.Value"
    TERM_UNIT = "Term.Unit"


class SwapDirectionalities:
    SWAP_IN = "Swap In"
    SWAP_OUT = "Swap Out"
    OTHER = "Other"


@dataclass
class Swap:
    mask: pd.Series
    path: Dict[str, str]


class MapInstrument(TransformBaseTask):
    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: BaseParams = None,
        resources: BaseResources = None,
        **kwargs,
    ) -> pd.DataFrame:
        # mask source to new transactions only
        source_frame = source_frame.loc[source_frame[In.REPORT_STATUS] == "NEWT"]

        target = pd.DataFrame(index=source_frame.index)

        missing_cols = self.required_columns().difference(set(source_frame.columns))
        if missing_cols:
            source_frame = source_frame.assign(**{col: None for col in missing_cols})

        # compose the report isin mask
        # can be refined with https://steeleye.atlassian.net/browse/PR-1501
        report_isin_mask = source_frame[In.INSTRUMENT_ID_CODE].notnull() & (
            (
                # Changed in https://steeleye.atlassian.net/browse/EU-7373
                source_frame[In.ELIGIBILITY_ON_FIRDS].dropna().astype(bool)
                & source_frame[In.MIFIR_ELIGIBLE].dropna().astype(bool)
            )
            | (
                (
                    source_frame[In.WORKFLOW_STATUS].dropna()
                    == RTS22TransactionStatus.REPORTABLE_USER_OVERRIDE.value
                )
                & (
                    source_frame[In.EXT_UNDERLYING_INSTRUMENTS].isnull()
                    & source_frame[In.DERIV_UNDERLYING_INSTRUMENTS].isnull()
                    & source_frame[In.DERIV_UNDERLYING_INDEX_NAME].isnull()
                )
            )
        )

        # if isin, populate id
        target[Out.ID] = source_frame.loc[report_isin_mask, In.INSTRUMENT_ID_CODE]

        # filter source frame to exclude has isin
        source_frame = source_frame.loc[~report_isin_mask]

        # map other attributes if non-isin instruments present
        if not source_frame.empty:
            other_target = self.map_other(source_frame=source_frame)
            target = pd.concat([target, other_target], axis=1)

        return target

    def map_other(self, source_frame: pd.DataFrame):
        target = pd.DataFrame(index=source_frame.index)

        # populate other general attributes
        # Disabled as per https://steeleye.atlassian.net/browse/IAPS-37
        # target[Out.OTHR_GEN_ID] = source_frame.loc[:, In.INSTRUMENT_ID_CODE]
        target[Out.OTHR_GEN_FULL_NM] = source_frame.loc[:, In.INSTRUMENT_FULL_NAME]
        target[Out.OTHR_GEN_CLS_TP] = source_frame.loc[:, In.INSTRUMENT_CLASSIFICATION]
        target[Out.OTHR_GEN_NTNL_CCY] = source_frame.loc[:, In.NOTIONAL_CCY]

        # populate other derivative attributes
        target[Out.OTHR_DERIV_DLVRY_TP] = source_frame.loc[:, In.DELIVERY_TYPE]
        target[Out.OTHR_DERIV_XPRY_DT] = source_frame.loc[:, In.DERIV_EXPIRY_DT]
        target[Out.OTHR_DERIV_PRICE_MULT] = source_frame.loc[:, In.DERIV_PRICE_MULT]
        target[Out.OTHR_DERIV_OPTION_TP] = source_frame.loc[:, In.DERIV_OPTION_TP]
        target[Out.OTHR_DERIV_OPTION_EX_STYLE] = source_frame.loc[
            :, In.DERIV_OPTION_EX_STYLE
        ]

        # if not isin, populate other debt attributes
        target[Out.OTHR_DEBT_MAT_DT] = source_frame.loc[:, In.BOND_MATURITY_DATE]

        # Other feeds still need to be updated to populate the swap directionalities
        # value, so check for it, if the value is there or not.
        if In.SWAP_DIRECTIONALITIES in source_frame.columns:
            swap_directionalities_mask = source_frame[
                In.SWAP_DIRECTIONALITIES
            ].notnull()
        else:
            swap_directionalities_mask = pd.Series(
                [False] * source_frame.shape[0],
                index=source_frame.index,
            )

        # map underlying when the swap directionalities isn't populated
        not_swap_target_result = self.map_underlying_not_swap_values(
            source_frame=source_frame[~swap_directionalities_mask],
            target=target[~swap_directionalities_mask],
        )

        # Fill all the required columns with pd.NA, if they aren't populated then
        # pandas dynamic setting on dataframe will not show any changes.
        # *not_swap_target_result.columns is basically all the columns to be populated
        # from old method and other than that all are new
        required_cols = [
            *not_swap_target_result.columns,
            Out.OTHR_DERIV_UNDRLYGINSTRM_OTHR_BSKT,
            Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_BSKT % dict(swap="SwpIn"),
            Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_BSKT_INDX % dict(swap="SwpIn"),
            Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_BSKT % dict(swap="SwpOut"),
            Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_BSKT_INDX % dict(swap="SwpOut"),
            Out.OTHR_DERIV_UNDRLYGINSTRM_OTHR_BSKT,
            Out.OTHR_DERIV_UNDRLYGINSTRM_OTHR_BSKT_INDX,
            # Columns required when swap is single
            Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_SNGL_ISIN % dict(swap="SwpIn"),
            Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_SNGL_INDX % dict(swap="SwpIn"),
            Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_SNGL_ISIN % dict(swap="SwpOut"),
            Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_SNGL_INDX % dict(swap="SwpOut"),
            Out.OTHR_DERIV_UNDRLYGINSTRM_OTHR_SNGL_ISIN,
            Out.OTHR_DERIV_UNDRLYGINSTRM_OTHR_SNGL_BSKT,
        ]

        for col in required_cols:
            if col not in target.columns:
                target[col] = pd.NA

        target[~swap_directionalities_mask] = not_swap_target_result

        for col in required_cols:
            if col not in target.columns:
                target[col] = pd.NA

        # New mapping logic for swaps happen here, this new logic is separated
        # from the old code. Let the old code do what it's doing at the moment and
        # then just update the target dataframe with new colume values because all
        # columns are already populated.
        target.loc[
            swap_directionalities_mask
        ] = MapInstrument.map_underlying_instruments_for_swaps(
            source_frame[swap_directionalities_mask], target[swap_directionalities_mask]
        )

        # Derivative instrument asset class specific attributes
        target[Out.OTHER_DERIV_ASSETCLASS_FX_NTNLCCY] = source_frame.loc[
            (
                source_frame[In.INSTRUMENT_CLASSIFICATION].str.match("FFC...")
                | source_frame[In.INSTRUMENT_CLASSIFICATION].str.match("SF....")
            ),
            In.NOTIONAL_CCY_2,
        ]

        # default strike price type to monetary
        source_frame.loc[:, In.DERIV_STRIKE_PRICE_TP] = source_frame.loc[
            :, In.DERIV_STRIKE_PRICE_TP
        ].fillna("MntryVal")

        pndg_and_not_strike_price_mask = (
            source_frame[In.DERIV_STRIKE_PRICE_TP] == "PNDG"
        ) & (source_frame[In.DERIV_STRIKE_PRICE].isnull())

        # fields when strike price is null
        pndg_and_not_strike_price = source_frame.loc[
            pndg_and_not_strike_price_mask,
            [In.DERIV_STRIKE_PRICE_TP, In.DERIV_STRIKE_PRICE_CCY],
        ]
        # exclude null strike price
        source_frame = source_frame.loc[source_frame[In.DERIV_STRIKE_PRICE].notnull()]

        if not source_frame.empty:
            # filter percentage strike price type
            pctg_data = source_frame.loc[
                source_frame[In.DERIV_STRIKE_PRICE_TP] == "Pctg",
                In.DERIV_STRIKE_PRICE,
            ].dropna()

            if not pctg_data.empty:
                # map percentage strike price
                target[Out.OTHR_DERIV_STRIKE_PRICE_PCTG] = (
                    pctg_data.dropna()
                    .astype(str)
                    .apply(
                        lambda x: enforce_significant_figures(
                            value=x, precision=18, scale=17
                        )
                    )
                )

            # filter yield strike price type
            yld_data = source_frame.loc[
                source_frame[In.DERIV_STRIKE_PRICE_TP] == "Yld",
                In.DERIV_STRIKE_PRICE,
            ].dropna()

            if not yld_data.empty:
                # map yield strike price
                target[Out.OTHR_DERIV_STRIKE_PRICE_YLD] = (
                    yld_data.dropna()
                    .astype(str)
                    .apply(
                        lambda x: enforce_significant_figures(
                            value=x, precision=11, scale=10
                        )
                    )
                )

            # filter basis points strike price type
            basis_data = source_frame.loc[
                source_frame[In.DERIV_STRIKE_PRICE_TP] == "BsisPts",
                In.DERIV_STRIKE_PRICE,
            ].dropna()

            if not basis_data.empty:
                # map basis points strike price
                target[Out.OTHR_DERIV_STRIKE_PRICE_BSIS] = (
                    basis_data.dropna()
                    .astype(str)
                    .apply(
                        lambda x: enforce_significant_figures(
                            value=x, precision=18, scale=17
                        )
                    )
                )

            # filter pending strike price type
            pndg_data = source_frame.loc[
                source_frame[In.DERIV_STRIKE_PRICE_TP] == "PNDG",
                In.DERIV_STRIKE_PRICE,
            ].dropna()

            if not pndg_data.empty:
                # map pending strike price
                target[
                    Out.OTHR_DERIV_STRIKE_PRICE_PNDG_PDG
                ] = pndg_data.dropna().astype(str)

            # filter monetary strike price type
            mone_data = source_frame.loc[
                source_frame[In.DERIV_STRIKE_PRICE_TP] == "MntryVal",
                In.DERIV_STRIKE_PRICE,
            ].dropna()

            if not mone_data.empty:
                # map monetary strike price
                target[Out.OTHR_DERIV_STRIKE_PRICE_MONE_AMT] = (
                    mone_data.dropna()
                    .astype(str)
                    .apply(
                        lambda x: enforce_significant_figures(
                            value=x, precision=18, scale=13
                        )
                    )
                )

        # exclude null strike price currency
        source_frame = source_frame.loc[
            source_frame[In.DERIV_STRIKE_PRICE_CCY].notnull()
        ]

        if not source_frame.empty:
            # filter pending strike price type
            pndg_data = source_frame.loc[
                source_frame[In.DERIV_STRIKE_PRICE_TP] == "PNDG",
                In.DERIV_STRIKE_PRICE_CCY,
            ].dropna()
            if not pndg_data.empty:
                # map pending strike price
                target[Out.OTHR_DERIV_STRIKE_PRICE_PNDG_CCY] = pndg_data

            # filter monetary strike price type
            mone_data = source_frame.loc[
                source_frame[In.DERIV_STRIKE_PRICE_TP] == "MntryVal",
                In.DERIV_STRIKE_PRICE_CCY,
            ].dropna()

            if not mone_data.empty:
                # map monetary strike price
                target[Out.OTHR_DERIV_STRIKE_PRICE_MONE_CCY] = mone_data

        if not pndg_and_not_strike_price.empty:
            target.loc[
                pndg_and_not_strike_price_mask, Out.OTHR_DERIV_STRIKE_PRICE_PNDG_PDG
            ] = pndg_and_not_strike_price.loc[
                pndg_and_not_strike_price_mask, In.DERIV_STRIKE_PRICE_TP
            ].astype(
                str
            )

            target.loc[
                pndg_and_not_strike_price_mask, Out.OTHR_DERIV_STRIKE_PRICE_PNDG_CCY
            ] = (
                pndg_and_not_strike_price.loc[
                    pndg_and_not_strike_price_mask,
                    In.DERIV_STRIKE_PRICE_CCY,
                ]
                .dropna()
                .astype(str)
            )

        return target

    def map_underlying_not_swap_values(
        self, source_frame: pd.DataFrame, target: pd.DataFrame
    ):
        if target.empty:
            return target

        # reference series
        underlying_instrument_ids = (
            source_frame[In.DERIV_UNDERLYING_INSTRUMENTS]
            .apply(instrument_ids, via_ext=False)
            .fillna(
                source_frame[In.EXT_UNDERLYING_INSTRUMENTS].apply(
                    instrument_ids, via_ext=True
                )
            )
        )
        buyer_lei = source_frame[In.BUYER].apply(get_buyer_lei)
        swap_in = Swap(
            mask=source_frame.loc[
                In.EXECUTING_ENTITY_LEI != buyer_lei, In.EXECUTING_ENTITY_LEI
            ].notnull(),
            path=dict(swap="SwpIn"),
        )
        swap_out = Swap(
            mask=source_frame.loc[
                In.EXECUTING_ENTITY_LEI == buyer_lei, In.EXECUTING_ENTITY_LEI
            ].notnull(),
            path=dict(swap="SwpOut"),
        )
        # reference series
        index_names = (
            source_frame[In.DERIV_UNDERLYING_INDEX_NAME]
            .apply(index_name)
            .fillna(source_frame[In.EXT_UNDERLYING_INSTRUMENTS].apply(index_name))
        )

        # matches regex instrument classifications
        instrument_classification_matcher = regex_matcher(
            source_frame[In.INSTRUMENT_CLASSIFICATION]
        )

        source_frame[In.DERIV_UNDERLYING_INDEX_TERM_VALUE] = source_frame[
            [In.DERIV_UNDERLYING_INDEX_TERM_VALUE, In.DERIV_UNDERLYING_INDEX_TERM]
        ].apply(
            lambda x: calculate_underlying_index_term_value(
                x[In.DERIV_UNDERLYING_INDEX_TERM_VALUE],
                x[In.DERIV_UNDERLYING_INDEX_TERM],
            ),
            axis=1,
        )

        source_frame[In.DERIV_UNDERLYING_INDEX_TERM] = source_frame[
            In.DERIV_UNDERLYING_INDEX_TERM
        ].apply(lambda x: format_underlying_index_term(x))

        # index is always the same shape so appends to the end of the instrument path
        add_index = index_builder(
            target_frame=target,
            undrlying_instrument_ids=underlying_instrument_ids,
            index_names=index_names,
            index_terms=source_frame[In.DERIV_UNDERLYING_INDEX_TERM],
            index_values=source_frame[In.DERIV_UNDERLYING_INDEX_TERM_VALUE],
        )

        underlying_instruments = (
            source_frame[In.DERIV_UNDERLYING_INSTRUMENTS].notnull()
            | source_frame[In.EXT_UNDERLYING_INSTRUMENTS].notnull()
        )
        swap_or_forward = instrument_classification_matcher(
            "S.....", "J.I...", "JT..C."
        )

        # conditions
        commodities_contract_mask = instrument_classification_matcher("JT...C")
        swap_mask = instrument_classification_matcher("S.....")
        not_swap_and_no_underlying_instrument = ~(
            underlying_instruments | swap_or_forward | commodities_contract_mask
        )
        underlying_single_instrument = instrument_classification_matcher(
            "HC....",
            "HEI...",
            "HTI...",
            "O..N..",
            "FFN...",
            "O..I..",
            "FFI...",
            "J.I...",
            "JT..C.",
        )
        underlying_single_instrument_not_swap = instrument_classification_matcher(
            "S....."
        ) & ~(swap_in.mask | swap_out.mask)
        unaccounted_single_instrument = underlying_instrument_ids.apply(
            lambda x: True
            if isinstance(x, str)
            else isinstance(x, list) and len(x) == 1 or False
        )
        unaccounted_multi_instrument = underlying_instrument_ids.apply(
            lambda x: isinstance(x, list) and len(x) > 1
        )

        # paths
        has_index_mask = cumulative_mask = (
            ~not_swap_and_no_underlying_instrument
            & underlying_single_instrument
            & ~commodities_contract_mask
            & ~swap_mask
        )
        add_index(cumulative_mask, Out.OTHR_DERIV_UNDRLYGINSTRM_OTHR_SNGL)

        self.swaps(
            target=target,
            swap=swap_in,
            underlying_instrument_ids=underlying_instrument_ids,
            index_names=index_names,
            instrument_classification_matcher=instrument_classification_matcher,
            add_index=add_index,
        )

        self.swaps(
            target=target,
            swap=swap_out,
            underlying_instrument_ids=underlying_instrument_ids,
            index_names=index_names,
            instrument_classification_matcher=instrument_classification_matcher,
            add_index=add_index,
        )

        self.commodities_contracts_builder(
            target_frame=target,
            undrlyg_instr_ids=underlying_instrument_ids,
            index_names=index_names,
            index_terms=source_frame[In.DERIV_UNDERLYING_INDEX_TERM],
            index_values=source_frame[In.DERIV_UNDERLYING_INDEX_TERM_VALUE],
            mask=commodities_contract_mask,
        )

        cumulative_mask = (
            underlying_single_instrument_not_swap
            & ~cumulative_mask
            & ~commodities_contract_mask
        )
        add_index(cumulative_mask, Out.OTHR_DERIV_UNDRLYGINSTRM_SNGL)

        cumulative_mask = (
            unaccounted_single_instrument
            & ~cumulative_mask
            & ~has_index_mask
            & ~commodities_contract_mask
        )
        target.loc[
            ~(commodities_contract_mask | swap_mask),
            Out.OTHR_DERIV_UNDRLYGINSTRM_OTHR_SNGL_ISIN,
        ] = underlying_instrument_ids.loc[cumulative_mask]

        cumulative_mask = (
            unaccounted_multi_instrument & ~cumulative_mask & ~commodities_contract_mask
        )
        target[
            Out.OTHR_DERIV_UNDRLYGINSTRM_OTHR_BSKT
        ] = MapInstrument.map_deriv_underlying_isin(
            underlying_instrument_ids.loc[cumulative_mask]
        )

        target.loc[
            not_swap_and_no_underlying_instrument,
            Out.OTHR_DERIV_UNDRLYGINSTRM_OTHR_SNGL_INDX_NM_REFRATE_NM,
        ] = index_names.loc[not_swap_and_no_underlying_instrument]

        return target

    @staticmethod
    def swaps(
        target: pd.DataFrame,
        swap: Swap,
        underlying_instrument_ids: pd.Series,
        index_names: pd.Series,
        instrument_classification_matcher,
        add_index,
    ):

        # conditions
        underlying_swap_single_isin = instrument_classification_matcher(
            "SCU...", "SES..."
        )
        underlying_swap_single_index = instrument_classification_matcher(
            "SCI...", "SCV...", "SEI...", "SR....", "SCM...", "STI..."
        )
        underlying_swap_basket_index = instrument_classification_matcher(
            "SEB...", "SCB..."
        ) & ~(index_names.isnull())
        underlying_swap_basket = (
            instrument_classification_matcher("SEB...", "SCB...") & index_names.isnull()
        )

        # paths
        target[
            f"{Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_SNGL_ISIN % swap.path}"
        ] = underlying_instrument_ids.loc[underlying_swap_single_isin & swap.mask]

        path_to_index = f"{Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_SNGL % swap.path}"
        add_index(underlying_swap_single_index & swap.mask, path_to_index)

        path_to_index = f"{Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_BSKT % swap.path}"
        add_index(underlying_swap_basket_index & swap.mask, path_to_index)

        target[
            f"{Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_BSKT % swap.path}"
        ] = MapInstrument.map_deriv_underlying_isin(
            underlying_instrument_ids.loc[underlying_swap_basket & swap.mask]
        )

    @staticmethod
    def map_deriv_underlying_isin(data: pd.Series) -> pd.Series:
        """
        Maps the Derivative Instrument Underlying ISIN's.

        :data: Pandas series of Underlying ISIN's to map.
        :return: Pandas series of Mapped ISIN's
        """
        if data.empty:
            return data

        # Here data is recived in 2 formats -
        # 1. 'ONLY_STRING'
        # 2. List with dicts of ISIN like - [{'ISIN': ''}, {'ISIN': ''}]
        # Flatten the data in a list of ISIN's, as it's required for the acceptable format
        # of the data
        return data.apply(
            lambda x: [{"ISIN": [item["ISIN"] for item in x]}]
            if isinstance(x, list)
            else [{"ISIN": [x]}]
        )

    @staticmethod
    def required_columns() -> Set[str]:
        return {
            In.BUYER,
            In.MIFIR_ELIGIBLE,
            In.ELIGIBILITY_TOTV,
            In.WORKFLOW_STATUS,
            In.EXECUTING_ENTITY_LEI,
            In.REPORT_STATUS,
            In.INSTRUMENT_ID_CODE,
            In.INSTRUMENT_FULL_NAME,
            In.INSTRUMENT_CLASSIFICATION,
            In.NOTIONAL_CCY,
            In.BOND_MATURITY_DATE,
            In.ON_FIRDS,
            In.VENUE_IN_EEA,
            In.TXN_VENUE,
            In.DELIVERY_TYPE,
            In.DERIV_EXPIRY_DT,
            In.DERIV_PRICE_MULT,
            In.DERIV_OPTION_TP,
            In.DERIV_OPTION_EX_STYLE,
            In.DERIV_STRIKE_PRICE,
            In.DERIV_STRIKE_PRICE_TP,
            In.DERIV_STRIKE_PRICE_CCY,
            In.DERIV_UNDERLYING_INSTRUMENTS,
            In.DERIV_UNDERLYING_INDEX_NAME,
            In.DERIV_UNDERLYING_INDEX_TERM,
            In.DERIV_UNDERLYING_INDEX_TERM_VALUE,
            In.EXT_UNDERLYING_INSTRUMENTS,
            In.NOTIONAL_CCY_2,
            In.ELIGIBILITY_ON_FIRDS,
        }

    @staticmethod
    def commodities_contracts_builder(
        target_frame: pd.DataFrame,
        undrlyg_instr_ids: pd.Series,
        index_names: pd.Series,
        index_terms: pd.Series,
        index_values: pd.Series,
        mask: pd.Series,
    ) -> None:
        target_frame.loc[
            mask, Out.OTHR_DERIV_UNDRLYGINSTRM_OTHR_SNGL_ISIN
        ] = undrlyg_instr_ids.loc[mask]
        target_frame.loc[
            mask, Out.OTHR_DERIV_UNDRLYGINSTRM_OTHR_SNGL_INDX_NM_REFRATE_NM
        ] = index_names.loc[mask]
        target_frame.loc[
            mask, Out.OTHR_DERIV_UNDRLYGINSTRM_OTHR_SNGL_INDX_TERM_UNIT
        ] = index_terms.loc[mask]
        target_frame.loc[
            mask, Out.OTHR_DERIV_UNDRLYGINSTRM_OTHR_SNGL_INDX_TERM_VALUE
        ] = index_values.loc[mask]

    @staticmethod
    def map_underlying_instruments_for_swaps(
        source_frame: pd.DataFrame, target: pd.DataFrame
    ):
        if target.empty:
            return target

        temp_df = pd.DataFrame(index=target.index)

        (
            temp_df[TempCols.SWAP_IN_COUNT],
            temp_df[TempCols.SWAP_OUT_COUNT],
            temp_df[TempCols.OTHER_COUNT],
            temp_df[TempCols.SWAP_IN_VALUES],
            temp_df[TempCols.SWAP_OUT_VALUES],
            temp_df[TempCols.OTHER_VALUES],
        ) = zip(
            *source_frame[
                [In.EXT_UNDERLYING_INSTRUMENTS, In.SWAP_DIRECTIONALITIES]
            ].apply(
                lambda x: MapInstrument.flatten_swap_values_and_counts(
                    x[In.EXT_UNDERLYING_INSTRUMENTS], x[In.SWAP_DIRECTIONALITIES]
                ),
                axis=1,
            )
        )

        # If Swap count is greater than 1, logic starts here
        swap_in_count = temp_df[TempCols.SWAP_IN_COUNT] > 1
        swap_out_count = temp_df[TempCols.SWAP_OUT_COUNT] > 1
        other_count = temp_df[TempCols.OTHER_COUNT] > 1

        if swap_in_count.any():
            target[
                [
                    Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_BSKT % dict(swap="SwpIn"),
                    Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_BSKT_INDX % dict(swap="SwpIn"),
                ]
            ] = temp_df.loc[swap_in_count].apply(
                lambda x: MapInstrument.map_swap_values(
                    values=x[TempCols.SWAP_IN_VALUES], is_single=False
                ),
                axis=1,
                result_type="expand",
            )
        if swap_out_count.any():
            target[
                [
                    Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_BSKT % dict(swap="SwpOut"),
                    Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_BSKT_INDX % dict(swap="SwpOut"),
                ]
            ] = temp_df.loc[swap_out_count].apply(
                lambda x: MapInstrument.map_swap_values(
                    values=x[TempCols.SWAP_OUT_VALUES], is_single=False
                ),
                result_type="expand",
                axis=1,
            )

        if other_count.any():
            target[
                [
                    Out.OTHR_DERIV_UNDRLYGINSTRM_OTHR_BSKT,
                    Out.OTHR_DERIV_UNDRLYGINSTRM_OTHR_BSKT_INDX,
                ]
            ] = temp_df.loc[other_count].apply(
                lambda x: MapInstrument.map_swap_values(
                    values=x[TempCols.OTHER_VALUES], is_single=False
                ),
                result_type="expand",
                axis=1,
            )

        # If Any Swap Count is single, the logic starts here
        swap_in_count_single = temp_df[TempCols.SWAP_IN_COUNT] == 1
        swap_out_count_single = temp_df[TempCols.SWAP_OUT_COUNT] == 1
        other_count_single = temp_df[TempCols.OTHER_COUNT] == 1

        if swap_in_count_single.any():
            target[
                [
                    Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_SNGL_ISIN % dict(swap="SwpIn"),
                    Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_SNGL_INDX % dict(swap="SwpIn"),
                ]
            ] = temp_df.loc[swap_in_count_single].apply(
                lambda x: MapInstrument.map_swap_values(
                    values=x[TempCols.SWAP_IN_VALUES], is_single=False
                ),
                result_type="expand",
                axis=1,
            )

        if swap_out_count_single.any():
            target[
                [
                    Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_SNGL_ISIN % dict(swap="SwpOut"),
                    Out.OTHR_DERIV_UNDRLYGINSTRM_SWP_SNGL_INDX % dict(swap="SwpOut"),
                ]
            ] = temp_df.loc[swap_out_count_single].apply(
                lambda x: MapInstrument.map_swap_values(
                    values=x[TempCols.SWAP_OUT_VALUES], is_single=False
                ),
                result_type="expand",
                axis=1,
            )

        if other_count_single.any():
            target[
                [
                    Out.OTHR_DERIV_UNDRLYGINSTRM_OTHR_SNGL_ISIN,
                    Out.OTHR_DERIV_UNDRLYGINSTRM_OTHR_SNGL_BSKT,
                ]
            ] = temp_df.loc[other_count_single].apply(
                lambda x: MapInstrument.map_swap_values(
                    values=x[TempCols.OTHER_VALUES], is_single=False
                ),
                result_type="expand",
                axis=1,
            )

        return target

    @staticmethod
    def map_swap_values(values: List[Dict[str, str]], is_single: bool):
        if not values:
            return pd.NA, pd.NA

        without_underlying_index_name, with_underlying_index_name = [], []
        for value in values:
            isin = value.get(TempKeys.UNDERLYING_ISIN)
            underlying_index_name = value.get(TempKeys.UNDERLYING_INDEX_NAME)

            if not underlying_index_name:
                without_underlying_index_name.append(isin)
            else:
                with_underlying_index_name.append(
                    {
                        IndexPaths.ISIN: isin,
                        IndexPaths.NAME_REF_RATE: underlying_index_name,
                        IndexPaths.NAME_REF_RATE_INDEX: value.get(
                            TempKeys.UNDERLYING_INDEX_ID
                        ),
                        IndexPaths.NAME_TERM_UNIT: value.get(
                            TempKeys.UNDERLYING_INDEX_TERM
                        ),
                        IndexPaths.NAME_TERM_VALUE: value.get(
                            TempKeys.UNDERLYING_INDEX_TERM_VALUE
                        ),
                    }
                )

        if is_single:
            return (
                without_underlying_index_name[0]
                if without_underlying_index_name
                else pd.NA,
                with_underlying_index_name[0] if with_underlying_index_name else pd.NA,
            )

        return {
            "ISIN": without_underlying_index_name or pd.NA
        }, with_underlying_index_name or pd.NA

    @staticmethod
    def flatten_swap_values_and_counts(
        underlying_instruments: List[Dict[Any, Any]], swaps: List[str]
    ):
        underlying_instruments = (
            []
            if not isinstance(underlying_instruments, list)
            else underlying_instruments
        )
        # Just in case if the underlying instruments size isn't same as of swaps
        underlying_instruments = underlying_instruments + [{}] * (
            len(swaps) - len(underlying_instruments)
        )
        in_count, out_count, other_count = 0, 0, 0
        in_values, out_values, other_values = [], [], []

        for index, swap in enumerate(swaps):
            value = underlying_instruments[index]
            derivative_values = value.get("derivative", {})

            underlying_index_name = derivative_values.get("underlyingIndexName")
            underlying_index_term = derivative_values.get("underlyingIndexTerm")
            underlying_index_term_value = derivative_values.get(
                "underlyingIndexTermValue"
            )

            value_normalize = {
                TempKeys.UNDERLYING_ISIN: value.get("instrumentIdCode"),
                TempKeys.UNDERLYING_INDEX_NAME: underlying_index_name,
                TempKeys.UNDERLYING_INDEX_ID: value.get("ext", {}).get(
                    "underlyingIndexId"
                ),
                TempKeys.UNDERLYING_INDEX_TERM: format_underlying_index_term(
                    underlying_index_term
                ),
                TempKeys.UNDERLYING_INDEX_TERM_VALUE: calculate_underlying_index_term_value(
                    underlying_index_term_value, underlying_index_term
                ),
            }

            if swap == SwapDirectionalities.SWAP_IN:
                in_count += 1
                in_values.append(value_normalize)
            elif swap == SwapDirectionalities.SWAP_OUT:
                out_count += 1
                out_values.append(value_normalize)
            else:
                other_count += 1
                other_values.append(value_normalize)

        return in_count, out_count, other_count, in_values, out_values, other_values


def regex_matcher(source_column: pd.Series):
    def matcher(*regex_patterns):
        return source_column.str.match("|".join(regex_patterns), na=False)

    return matcher


def inject_index_parent_data(
    underlying_instrument_ids: Union[List[Dict[str, str]], str],
    parent_data: Dict[str, str],
) -> List[Dict[str, str]]:
    """
    Injects the parent node data in the first `Indx` column

    :param underlying_instrument_ids: Underlying instrument ID's for the record, can be dict or str
    :param parent_data: The dict of fields to be added in Parent Index at position 1
    :returns: Data with all the changes in a list of dictionaries
    """
    result = (
        underlying_instrument_ids
        if isinstance(underlying_instrument_ids, list)
        else [{"ISIN": underlying_instrument_ids}]
    )

    # The parent data SHOULD only be there in the first Indx tag
    result[0] = {**result[0], **parent_data}
    return result


def index_builder(
    target_frame,
    undrlying_instrument_ids: pd.Series,
    index_names: pd.Series,
    index_terms: pd.Series,
    index_values: pd.Series,
):
    def builder(mask: pd.Series, path_to_index: str):
        temp_df = pd.DataFrame(
            {
                TempCols.INDEX_NAME: index_names,
                TempCols.INDEX_TERM: index_terms,
                TempCols.INDEX_VALUES: index_values,
                TempCols.UNDERLYING_INDEX_IDS: undrlying_instrument_ids,
            }
        )

        # Calculate all the 1st Indx value. It's not supposed to go in
        # for the other <Indx>
        # Please see https://steeleye.atlassian.net/browse/EU-5093?focusedCommentId=102536
        data = temp_df.apply(
            lambda x: inject_index_parent_data(
                x[TempCols.UNDERLYING_INDEX_IDS],
                {
                    IndexPaths.NAME_REF_RATE: x[TempCols.INDEX_NAME],
                    IndexPaths.TERM_UNIT: x[TempCols.INDEX_TERM],
                    IndexPaths.TERM_VALUE: x[TempCols.INDEX_VALUES],
                },
            ),
            axis=1,
        )

        target_frame[f"{path_to_index}.{IndexPaths.INDEX}"] = data.loc[mask]

    return builder


def instrument_ids(instruments: list, via_ext: bool = False) -> Union[str, List[dict]]:
    if not isinstance(instruments, list):
        return np.nan

    id_code = "instrumentIdCode" if via_ext else UnderlyingInstrument.ID_CODE

    if len(instruments) == 1:
        return instruments[0].get(id_code)

    return [dict(ISIN=x.get(id_code)) for x in instruments]


def index_name(content: Union[str, List[dict]]) -> Union[str, List[dict]]:
    if isinstance(content, list):
        derivatives = [x.get("derivative") for x in content if "derivative" in x]
        if not derivatives:
            return np.nan

        names = [
            x.get(UnderlyingInstrument.UNDERLYING_INDEX_NAME, "")[:25]
            for x in derivatives
        ]
        return names[0] if names else np.nan

    return content[:25] if isinstance(content, str) else np.nan


def get_buyer_lei(buyer: List[dict]) -> str:
    if not isinstance(buyer, list) or not buyer:
        return np.nan

    return buyer[0].get("firmIdentifiers", {}).get("lei")


def calculate_underlying_index_term_value(
    underlying_index_term_value: Optional[str],
    underlying_index_term: Optional[str],
) -> Optional[str]:
    """
    Calculates the UnderlyingIndexTermValue based on two conditions, else it returns
    'pd.NA'
    1. If UnderlyingIndexTermValue is populated then take that
    2. If condition '1' isn't satisfied then take any leading numeric value
       from the UnderlyingIndexTerm if it's populated.
       For ex: '1ABC2' -> '1'
               '22ABC2' -> '22'
    Please check "EU-5338" for more details

    :param underlying_index_term_value: string
    :param underlying_index_term: string
    :returns: The calculated value or 'pd.NA'
    """
    if isinstance(underlying_index_term_value, str):
        return underlying_index_term_value

    if isinstance(underlying_index_term, str) and underlying_index_term:
        value = re.search(r"\d+", underlying_index_term)
        return value.group().upper() if value else pd.NA

    return pd.NA


def format_underlying_index_term(
    underlying_index_term: Optional[str],
) -> Optional[str]:
    """
    Formats the UnderlyingIndexTerm on the basis of value provided.
    We want to take only alpha characters from the value provided.
    For ex: '2abc2' -> 'ABC'
    Please check "EU-5338" for more details

    :param underlying_index_term: str
    :returns: The calculated value or pd.NA if no value
    """
    return (
        "".join(re.findall(r"[a-zA-Z]+", underlying_index_term)).upper() or pd.NA
        if isinstance(underlying_index_term, str)
        else pd.NA
    )
