import pandas as pd
from se_trades_tasks.order_and_tr.short_selling_indicator.short_selling_indicator import (
    Params as GenericParams,
)
from se_trades_tasks.order_and_tr.short_selling_indicator.short_selling_indicator import (
    run_short_selling_indicator,
)
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class ShortSellingIndicator(TransformBaseTask):
    """
    This task creates the short selling indicator based on the following logic:

    if [instrument.instrumentDetails.instrumentClassification].startswith(“E”,”D”,”C”)
        AND [transactionDetails.buySellIndicator] = “SELL”
        AND [Client ID].upper() != “INTC”

        then:
            if “SHORT” in [Buy / Sell].upper()
                “SESH”
            else:
                “SELL”

    Params:
        In Steeleye Trade Blotter, the source data params should be:
            - buy_sell_attribute = Buy / Sell
            - client_id_attribute = Client ID

    NOTE: buy_sell_attribute rather than be an input from source data, it should come from
    a specific field with more detailed buy sell indicator.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: BaseResources = None,
        **kwargs,
    ) -> pd.DataFrame:

        return run_short_selling_indicator(
            source_frame=source_frame, params=params, **kwargs
        )
