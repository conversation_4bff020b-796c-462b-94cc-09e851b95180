from typing import Optional

import pandas as pd
from se_elastic_schema.static.mifid2 import RTS22ExternalStatus
from swarm.schema.static import SwarmColumns
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask
from swarm.task.io.read import FrameProducerResult
from swarm.task.transform.result import TransformResult


class Resources(BaseResources):
    es_client_key: str


class LinkTransaction(BaseTask):
    resources_class = Resources

    def execute(
        self,
        result: Optional[FrameProducerResult] = None,
        params: Optional[BaseParams] = None,
        resources: Resources = None,
        **kwargs
    ) -> TransformResult:

        if result.frame.empty:
            return TransformResult(
                target=pd.DataFrame(), batch_index=result.batch_index
            )

        es = self.clients.get(resources.es_client_key)

        id_fields = [
            SwarmColumns.SWARM_RAW_INDEX,
            es.meta.id,
            es.meta.model,
            es.meta.hash,
            es.meta.version,
        ]

        # constrain target to id fields
        target = result.frame[id_fields]

        # update workflow arm status
        target.loc[:, "workflow.arm.status"] = RTS22ExternalStatus.PENDING

        # update workflow is reported
        target.loc[:, "workflow.isReported"] = True

        # increment version
        target.loc[:, es.meta.version] = target[es.meta.version] + 1

        return TransformResult(target=target, batch_index=result.batch_index)
