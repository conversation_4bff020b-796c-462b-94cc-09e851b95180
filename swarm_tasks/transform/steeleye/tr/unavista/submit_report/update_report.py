import os
from datetime import datetime
from typing import Any
from typing import List
from typing import Optional

import boto3
from generator.submission_email_generator import ARMSubmissionEmailGenerator
from se_elastic_schema.models import RTS22TransactionReport
from se_elastic_schema.models import UserAudit
from se_trades_tasks.tr.workflow.static import ScheduleTypeEnum
from swarm.client.record_handler_helper import init_record_handler
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask
from swarm.task.io.read import FrameProducerResult

from swarm_tasks.transform.steeleye.tr.static import TransactionReportColumns
from swarm_tasks.transform.steeleye.tr.unavista.utils import (
    fetch_tr_config_from_platform_config,
)


class Resources(BaseResources):
    es_client_key: str


class Params(BaseParams):
    email_from: str


class UpdateReport(BaseTask):
    SOURCE_ARN = "arn:aws:ses:eu-west-1:510739586076:identity/steel-eye.com"
    resources_class = Resources
    params_class = Params

    def execute(
        self,
        result: List[Optional[FrameProducerResult]] = None,
        params: Params = None,
        resources: Resources = None,
        **kwargs,
    ) -> Any:

        # extract tenant from context
        tenant = Settings.tenant

        es = self.clients.get(resources.es_client_key)

        record_handler = init_record_handler(client=es.client, tenant=tenant)

        # assign report id using first record in meta
        report_id = result[0].frame.iloc[0][TransactionReportColumns.RTS22_REPORT_ID]
        report_meta_id = report_id.lower()
        self.logger.info(f"assign report meta id to {report_meta_id}")

        utc_now = datetime.utcnow()
        now = utc_now.isoformat() + "Z"

        # number of transactions updated as REPORTED
        pending_count = sum([len(res.frame.index) for res in result])

        result = {"submitted": now, "arm": {"counts": {"pending": pending_count}}}

        self.logger.info(f"update report using {result}")

        txn_report_index = RTS22TransactionReport.get_elastic_index_alias(tenant=tenant)

        txn_update_response = es.client.update(
            index=txn_report_index,
            id=report_meta_id,
            body={"doc": result},
            refresh=True,
        )

        self.logger.info(f"update report response\n{txn_update_response}")

        self.logger.info("fetch latest full report version")
        report = record_handler.get(
            index=txn_report_index,
            model="RTS22TransactionReport",
            doc_id=report_meta_id,
        )

        # extract realm from context
        realm = Settings.realm

        # create user audit
        user = report.get("submittedBy", "system")
        user_audit = dict(
            realm=realm,
            user=user,
            uri=f"{realm}/tr/reports",
            timestamp=now,
            recordId=report_meta_id,
            eventDetails=dict(
                module="Transaction Reporting",
                category="Workflow",
                event="RTS22 Transaction Report Submitted",
                description=f"{'System' if user == 'system' else 'User'} submitted RTS 22 Transaction Report",
            ),
        )

        self.logger.info("create user audit")
        user_audit_alias = UserAudit.get_elastic_index_alias(tenant=tenant)
        record_handler.create(doc=user_audit, alias=user_audit_alias, model="UserAudit")

        # extract notify on submission setting from schedule (default false)
        notify_on_submission_config = fetch_tr_config_from_platform_config(
            tenant=tenant,
            stack=Settings.STACK,
            env=Settings.env,
            schedule_type=ScheduleTypeEnum.notify_on_submission_to_arm,
        )
        if notify_on_submission_config:
            recipients = notify_on_submission_config.get("recipients", list())

            # construct email generator for arm submission
            email_generator = ARMSubmissionEmailGenerator(realm=realm)

            self.logger.info("generate email message")
            msg = email_generator.generate_email(
                subject="RTS22 Transaction Report Submitted",
                report_id=report_meta_id,
                execution_date=utc_now.date(),
                submission_time=utc_now.time().isoformat().split(".")[0],
                transaction_count=report.get("reported", 0),
                # todo calculate failures (e.g,. failed checks presumably)
                # failures_count=0,
            )

            # TODO: ideally this should be abstracted in a utility function/class
            # assign boto3 ses client
            ses = boto3.client(
                "ses",
                aws_access_key_id=os.environ.get("SES_ACCESS_KEY_ID"),
                aws_secret_access_key=os.environ.get("SES_SECRET_ACCESS_KEY"),
            )

            self.logger.info("send email")
            response = ses.send_raw_email(
                Source=params.email_from,
                SourceArn=UpdateReport.SOURCE_ARN,
                Destinations=recipients,
                RawMessage=dict(Data=msg.as_string()),
            )
            self.logger.info(f"email send response: {response}")

        return txn_update_response
