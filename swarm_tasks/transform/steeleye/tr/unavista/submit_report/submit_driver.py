import time
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from urllib.parse import urlparse

import fsspec
import numpy as np
import pandas as pd
import xmltodict
from prefect.engine.signals import FAIL
from se_elastic_schema.models import RTS22Transaction
from swarm.conf import Settings
from swarm.schema.static import SwarmColumns
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask
from swarm.task.io.read.result import FrameProducerResult

from swarm_tasks.transform.steeleye.tr.static import TransactionReportColumns


class Params(BaseParams):
    chunksize: int = 100000


class Resources(BaseResources):
    es_client_key: str


class SubmitDriver(BaseTask):
    params_class = Params
    resources_class = Resources

    def execute(
        self,
        file_url: str = None,
        params: Optional[Params] = None,
        resources: Optional[Resources] = None,
        **kwargs,
    ) -> List[FrameProducerResult]:

        es = self.clients.get(resources.es_client_key)

        # extract tenant from context
        tenant = Settings.tenant

        # extract report id from file url
        report_id = self.parse_report_id(file_url=file_url)

        # extract total count of transaction reference numbers from report
        total_transaction_reference_numbers_count = (
            self.parse_xml_and_extract_transaction_ref_no_count(file_url=file_url)
        )

        # define rts22 transaction alias
        txn_alias = RTS22Transaction.get_elastic_index_alias(tenant=tenant)

        # for each report, scroll and add to batches
        batches = list()

        # fields to restrict in source
        id_fields = [
            es.meta.id,
            es.meta.model,
            es.meta.hash,
            es.meta.version,
        ]

        # define query to retrieve elastic meta for txns in report
        body = {
            "_source": {"includes": id_fields},
            "query": {"bool": {"filter": [{"term": {"workflow.reportId": report_id}}]}},
        }

        df = self.get_transaction_record_from_es_and_reconcile(
            es=es,
            query=body,
            index=txn_alias,
            total_transaction_ref_numbers_count=total_transaction_reference_numbers_count,
        )

        for idx, (_, df_chunk) in enumerate(
            df.groupby(np.arange(len(df)) // params.chunksize)
        ):
            if idx > 0:
                report_id += str(idx)
            self.logger.info(
                f"produce frame for report `{report_id}` [{len(df_chunk.index)}]"
            )
            df_chunk.index.name = SwarmColumns.SWARM_RAW_INDEX
            df_chunk[TransactionReportColumns.RTS22_REPORT_ID] = report_id
            df_chunk = df_chunk.reset_index()
            result = FrameProducerResult(frame=df_chunk, batch_index=idx)
            batches.append(result)

        return batches

    def get_transaction_record_from_es_and_reconcile(
        self,
        es,
        query: Dict[str, Any],
        index: str,
        total_transaction_ref_numbers_count: int,
    ) -> pd.DataFrame:
        """
        Gets the transaction to be reported from Elasticsearch and matches the total
        count against what we have in the report file
        Tries to match it for max 10 minutes if not matched, else raises FAIL signal

        :param es: Elasticsearch client
        :param query: Query to be executed against Elasticsearch
        :param index: Index against which the search to be executed
        :param total_transaction_ref_numbers_count: Total count of Transaction Ref Numbers
        :return: Elasticsearch result as pandas dataframe
        """
        for try_count in range(10):
            df = self.get_records_from_elastic(es, query, index)
            if df.shape[0] != total_transaction_ref_numbers_count:
                self.logger.warn(
                    f"Count not matched in Elasticsearch for total transaction reference numbers. ES: {df.shape[0]} "
                    f"vs Total TRN Ref No: {total_transaction_ref_numbers_count}. Try count: {try_count + 1}"
                )
                self.logger.info("Sleeping for 1 minute...")
                # Since we are now waiting for more 60 seconds and again a new
                # result will be fetched, no profit in keep the result stored in
                # memory, release the memory
                df = None
                time.sleep(60)
            else:
                return df

        # We still don't have a match after 10 minutes~, no option other than failure
        raise FAIL("Failed to find total Transactions match in Elastic vs In File")

    @staticmethod
    def get_records_from_elastic(es, query: Dict[str, Any], index: str):
        """
        Returns records result dataframe from Elastic. In a separate method to
        make testing easy.

        :param es: Elasticsearch client
        :param query: Query to be executed against Elasticsearch
        :param index: Index against which the search to be executed
        :return: Elasticsearch result as pandas dataframe
        :return: Results dataframe
        """
        return es.scroll(query=query, index=index, include_elastic_meta=True)

    @staticmethod
    def parse_report_id(file_url: str) -> str:
        o = urlparse(file_url, allow_fragments=False)
        path_items = o.path.split("/")

        # report id excludes .xml file suffix
        report_id, _ = path_items[-1].rsplit(".", 1)

        return report_id

    @staticmethod
    def parse_xml_and_extract_transaction_ref_no_count(file_url: str) -> int:
        """
        Parses the Transaction Report and then extracts the Transaction Reference
        numbers from it and returns the total count

        :param file_url: Transaction Report
        :return: List of Transaction Ref Numbers
        """
        total_transactions = 0

        def handle_report_xml(path: Any, item: Any):
            nonlocal total_transactions
            for value in item.values():
                total_transactions += 1 if value.get("TxId") else 0
            return True

        with fsspec.open(file_url, "rb") as report_content:
            xmltodict.parse(
                xml_input=report_content, item_depth=5, item_callback=handle_report_xml
            )

        # The Tx value is at depth "5" and should always be there in any case
        # because that's the static value while generating the report.
        if not total_transactions:
            raise FAIL("No transaction record found in the report")

        # NOTE: Here we can have same Transaction Ref No twice because client can
        # submit CANC and NEW at once for a Transaction
        return total_transactions
