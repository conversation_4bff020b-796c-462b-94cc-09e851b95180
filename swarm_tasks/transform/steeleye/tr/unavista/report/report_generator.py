from pathlib import Path
from typing import List
from typing import Optional

import pandas as pd
import xmltodict
from prefect import context
from se_core_tasks.core.core_dataclasses import S3Action
from se_core_tasks.core.core_dataclasses import S3File
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask
from swarm.task.transform.result import TransformResult
from swarm.utilities.indict import Indict

from swarm_tasks.transform.steeleye.tr.static import TransactionReportColumns


class ReportGenerator(BaseTask):
    def execute(
        self,
        result: Optional[TransformResult] = None,
        flow_args: Optional[str] = None,
        params: Optional[BaseParams] = None,
        resources: Optional[BaseResources] = None,
    ) -> S3File:

        # define meta columns outside report content
        meta_columns = [
            TransactionReportColumns.RTS22_REPORT_ID,
            TransactionReportColumns.RTS22_REPORT_USER,
            TransactionReportColumns.RTS22_REPORT_STATUS,
            TransactionReportColumns.RTS22_REPORT_S3_KEY,
            "&key",
        ]
        self.logger.info("store meta columns")
        report_meta = result.target.loc[:, meta_columns]

        # prefix new transaction columns
        new_txns = self.shape_frame_to_status(
            target=result.target,
            report_meta=report_meta,
            status="NEWT",
            column_prefix="New",
        )

        # prefix cancel transaction columns
        cxl_txns = self.shape_frame_to_status(
            target=result.target,
            report_meta=report_meta,
            status="CANC",
            column_prefix="Cxl",
        )

        self.logger.info("unflatten cancel transactions")
        records = self.frame_to_json(frame=cxl_txns)

        self.logger.info("unflatten new transactions")
        new_records = self.frame_to_json(frame=new_txns)

        # merge new and cancel transactions with cancel transactions first
        records.extend(new_records)

        # assign report id and s3 key name using first record in meta
        report_id = report_meta.iloc[0][TransactionReportColumns.RTS22_REPORT_ID]
        key_name = report_meta.iloc[0][TransactionReportColumns.RTS22_REPORT_S3_KEY]
        self.logger.info(f"assign report id to {report_id} and s3 key to {key_name}")

        # generate xml report content
        self.logger.info("package xml report content")
        content = self.package(report_id=report_id, tx_reports=records)

        self.logger.info("write local xml file")
        batch_folder = f"batch-{result.batch_index}"
        output_path: Path = context.swarm.get("targets_dir").joinpath(batch_folder)
        output_path.mkdir(parents=True, exist_ok=True)
        output_path = output_path.joinpath(f"target.{result.batch_index}.xml")
        output_path.open("w+").write(content)

        realm = Settings.realm

        result = S3File(
            file_path=output_path,
            bucket_name=realm,
            key_name=key_name,
            action=S3Action.UPLOAD,
            bytes=output_path.stat().st_size,
        )

        return result

    def package(self, report_id, tx_reports):
        doc_ns = "urn:iso:std:iso:20022:tech:xsd:DRAFT15auth.016.001.01"

        report = {
            "UVMiFIRDocument": {
                "@xmlns": "urn:uv:xsd:unavista.mifir.iso20022.001.001.001",
                "UVHeader": {
                    "UVHeader": {
                        "@xmlns": "unavista.header.001.001.001",
                        "FileID": f"{report_id}.xml",
                    }
                },
                "Document": {
                    "Document": {
                        "@xmlns": doc_ns,
                        "FinInstrmRptgTxRpt": {"Tx": tx_reports},
                    }
                },
            }
        }
        self.logger.info("unparse report dict as xml")
        xml = xmltodict.unparse(input_dict=report)
        return xml.split("\n")[1]

    @staticmethod
    def frame_to_json(frame: pd.DataFrame) -> List[dict]:
        if frame.empty:
            return list()

        return (
            Indict(
                obj=frame,
                from_dataframe_params={"date_format": "iso", "orient": "records"},
            )
            .remove_empty()
            .unflatten()
            .to_dict()
            .get("data")
        )

    @staticmethod
    def shape_frame_to_status(
        target: pd.DataFrame,
        report_meta: pd.DataFrame,
        status: str,
        column_prefix: str,
    ):
        mask = target[TransactionReportColumns.RTS22_REPORT_STATUS] == status
        shaped = target.loc[mask]
        shaped = shaped.drop(columns=report_meta)
        shaped_columns = dict(
            [(col, f"{column_prefix}.{col}") for col in shaped.columns]
        )
        shaped = shaped.rename(columns=shaped_columns)
        return shaped
