import json
from datetime import date
from datetime import datetime
from typing import List
from typing import Optional
from typing import Union

import numpy as np
import pandas as pd
import pendulum
import prefect
from pydantic.main import BaseModel
from se_elastic_schema.models import AccountFirm
from se_elastic_schema.models import RTS22Transaction
from se_elastic_schema.models import RTS22TransactionReport
from se_elastic_schema.models import User<PERSON>udit
from se_schema_meta import EXPIRY
from se_schema_meta import MODEL
from se_schema_meta import TIMESTAMP
from swarm.client.record_handler.abstract_record_handler import AbstractRecordHandler
from swarm.client.record_handler_helper import init_record_handler
from swarm.conf import Settings
from swarm.schema.static import SwarmColumns
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask
from swarm.task.io.read.result import FrameProducerResult

from swarm_tasks.transform.steeleye.tr.static import TransactionReportColumns


class ActionType:
    NEXT = "next"
    CURRENT = "current"
    OVERDUE = "overdue"


class DateRange(BaseModel):
    from_date: Optional[Union[datetime, date]] = None
    to_date: Optional[Union[datetime, date]] = None


class Params(BaseParams):
    submit_flow_prefix: str = "flows/tr-workflow-unavista-submit-report"
    chunksize: int = 50000


class Resources(BaseResources):
    es_client_key: str


class ReportDriver(BaseTask):
    params_class = Params
    resources_class = Resources

    router_report_tpl = "SteelEye_TRMiFIR_{lei}.{key}"

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[Resources] = None,
        flow_args: Optional[str] = None,
        **kwargs,
    ) -> List[FrameProducerResult]:

        es = self.clients.get(resources.es_client_key)

        tenant = Settings.tenant

        # assign param args if present
        args = json.loads(flow_args) if flow_args else dict()

        # assign user
        user = args.get("user", "system")

        # fetch account firm lei
        account_firm_alias = AccountFirm.get_elastic_index_alias(tenant=tenant)

        query = {
            "size": 1,
            "query": {
                "bool": {
                    "must_not": [{"exists": {"field": EXPIRY}}],
                    "filter": [{"terms": {MODEL: [AccountFirm.__name__]}}],
                }
            },
            "sort": [{TIMESTAMP: {"order": "desc"}}],
        }

        try:
            record = es.search(index=account_firm_alias, query=query)["hits"]["hits"][0]
            account_firm_lei = record["_source"]["firmIdentifiers"]["lei"]
        except (KeyError, IndexError):
            raise prefect.engine.signals.FAIL(
                f"No AccountFirm record found in the ES index {account_firm_alias}, with"
                "the `firmIdentifiers.lei` field. It is mandatory for TRv2."
            )

        self.logger.info(f"assigned account firm lei as {account_firm_lei}")

        # define rts22 transaction alias
        txn_alias = RTS22Transaction.get_elastic_index_alias(tenant=tenant)

        # assign date range
        date_range = self.assign_date_range(args=args)

        # establish the query to drive the remainder of the workflow
        roadmap_query = self.driver_query(
            date_range=date_range,
            aggs=self.driver_aggs(),
            es_client=es,
            is_roadmap_query=True,
        )

        # override query
        override_query = args.get("overrideQuery")
        if override_query:
            roadmap_query.update(override_query)

        # execute roadmap search
        roadmap = es.search(query=roadmap_query, index=txn_alias)
        hits_total = roadmap["hits"]["total"]
        self.logger.info(f"roadmap query identified {hits_total} eligible transactions")

        # extract executing entities from aggs
        executing_entities = dict(
            [
                (bucket.key, bucket.doc_count)
                for bucket in roadmap.aggregations.get("EXEC-ENTITY-LEI").buckets
            ]
        )

        realm = Settings.realm

        # create record handler
        record_handler = init_record_handler(client=es.client, tenant=tenant)

        # utc now str
        now = datetime.utcnow().isoformat() + "Z"

        # iterate over unique lei's
        reports = list()
        for lei, count in executing_entities.items():
            unique_key = pendulum.now(tz=pendulum.UTC).format("YYYYMMDDHHmmss")
            report_id = self.router_report_tpl.format(lei=lei, key=unique_key)
            reports.append(
                dict(
                    report_id=report_id,
                    count=count,
                    filters=[
                        dict(term={"parties.executingEntity.firmIdentifiers.lei": lei})
                    ],
                )
            )

        # for each report, scroll and add to batches
        batches = list()
        batch_idx = 0
        for report_idx, report in enumerate(reports):
            report_id = report.get("report_id")
            self.logger.info(
                f"scroll {report.get('count')} records for report {report_id}"
            )
            if not override_query:
                report_query = self.driver_query(
                    date_range=date_range,
                    additional_filters=report.get("filters", list()),
                    es_client=es,
                    is_roadmap_query=False,
                )
            else:
                report_query = self.update_override_query(override_query, report)
            df = es.scroll(
                query=report_query, index=txn_alias, include_elastic_meta=True
            )

            for idx, (_, df_chunk) in enumerate(
                df.groupby(np.arange(len(df)) // params.chunksize)
            ):
                if idx > 0:
                    report_id += str(idx)

                self.logger.info(
                    f"produce frame for report `{report_id}` [{len(df_chunk.index)}]"
                )

                # compose s3 report key
                key_name = f"{params.submit_flow_prefix}/{report_id}.xml"

                df_chunk.index.name = SwarmColumns.SWARM_RAW_INDEX
                df_chunk = df_chunk.reset_index()
                df_chunk[TransactionReportColumns.RTS22_REPORT_S3_KEY] = key_name
                df_chunk[TransactionReportColumns.RTS22_REPORT_ID] = report_id
                df_chunk[TransactionReportColumns.RTS22_REPORT_USER] = user
                batch_idx += 1
                result = FrameProducerResult(frame=df_chunk, batch_index=batch_idx)

                # [EU-8582] - These changes were necessary keeping in mind that the report should
                # get generated whenever all the trades are CANC's or whenever the override_query
                # is not populated.
                # Report should not be generated whenever API/FE team would be testing the report
                # generation process by passing an override_query param as sys_args
                cancelled_trades_count = len(
                    result.frame["reportDetails.reportStatus"] == "CANC"
                )
                are_all_cancelled_trades = cancelled_trades_count == len(result.frame)

                if not override_query or are_all_cancelled_trades:
                    # create transaction report
                    self.create_report(
                        record_handler=record_handler,
                        report_id=report_id,
                        tenant=tenant,
                        realm=realm,
                        key_name=key_name,
                        user=user,
                        reported=len(df_chunk.index),
                        now=now,
                    )

                batches.append(result)

        return batches

    def create_report(
        self,
        record_handler: AbstractRecordHandler,
        report_id: str,
        tenant: str,
        realm: str,
        key_name: str,
        user: str,
        reported: int,
        now: str,
    ):
        # txn report alias
        txn_report_alias = RTS22TransactionReport.get_elastic_index_alias(tenant=tenant)

        report_url = f"s3://{realm}/{key_name}"

        # compose transaction report
        txn_report = {
            "reportId": report_id,
            "generated": now,
            "submittedBy": user,
            "reported": reported,
            "reportUrl": report_url,
        }

        self.logger.info("create transaction report")
        record_handler.create(
            doc=txn_report,
            alias=txn_report_alias,
            model="RTS22TransactionReport",
            user=user,
        )

        # create user audit
        user_audit = dict(
            realm=realm,
            user=user,
            uri=f"{realm}/tr/reports",
            timestamp=now,
            recordId=report_id,
            eventDetails=dict(
                module="Transaction Reporting",
                category="Workflow",
                event="RTS22 Transaction Report Generated",
                description=f"{'System' if user == 'system' else 'User'} generated RTS 22 Transaction Report",
            ),
        )

        self.logger.info(f"create user audit for report id {report_id}")
        user_audit_alias = UserAudit.get_elastic_index_alias(tenant=tenant)
        record_handler.create(doc=user_audit, alias=user_audit_alias, model="UserAudit")

    @staticmethod
    def driver_aggs() -> dict:
        exec_entity_lei = {
            "EXEC-ENTITY-LEI": {
                "terms": {
                    "field": "parties.executingEntity.firmIdentifiers.lei",
                    "size": 1000,
                }
            }
        }
        return exec_entity_lei

    @staticmethod
    def update_override_query(
        query: dict,
        report: dict,
    ) -> dict:

        """
                We wil be receiving Query and Report Like this for this method
                Report:
                -------------------------Report Starts Here---------------------------------
                {
          "report_id": "SteelEye_TRMiFIR_<ExecEntyID>.20241021065839",
          "count": 5,
          "filters": [
            {
              "term": {
                "parties.executingEntity.firmIdentifiers.lei": "<ExecEntyID>"
                        }
            }
                    ]
             }
          -------------------------Report Ends Here---------------------------------
            Query:
            -------------------------Query Starts Here---------------------------------
        {
            "query": {
                "bool": {
                    "must_not": [
                        {
                            "exists": {
                                "field": "&expiry"
                            }
                        },
                        {
                            "term": {
                                "workflow.isReported": True
                            }
                        }
                    ],
                    "must": [
                        {
                            "term": {
                                "&model": "RTS22Transaction"
                            }
                        },
                        {
                            "term": {
                                "reportDetails.reportStatus": "CANC"
                            }
                        },
                        {
                            "terms": {
                                "workflow.status": [
                                    "REPORTABLE",
                                    "REPORTABLE_USER_OVERRIDE"
                                ]
                            }
                        },
                        {
                            "term": {
                                "parties.executingEntity.firmIdentifiers.lei": "<ExecEntyID>"
                            }
                        }
                    ]
                }
            },
        }
        -------------------------Query Ends Here---------------------------------

            This method modifies the must clause of an Elasticsearch query by replacing or adding a filter based on the execution_entity_id extracted from the report.
            Args :
            report: A dictionary that contains execution_entity_id from the first filter.
            query: A dictionary representing the Elasticsearch query.
        """
        firmIdentifier = "parties.executingEntity.firmIdentifiers.lei"
        try:
            for filter_item in report.get("filters", []):
                term = filter_item.get("term", {})
                if firmIdentifier in term:
                    execution_entity_id = {
                        "term": {firmIdentifier: term[firmIdentifier]}
                    }
                    break
        except KeyError:
            execution_entity_id = ""

        # Ensure the 'must' clause exists in the query structure
        must_clause = query["query"]["bool"].get("must", [])

        found = False
        for index, clause in enumerate(must_clause):
            if firmIdentifier in clause.get("term", {}):
                must_clause[index] = execution_entity_id
                found = True
                break

        if not found:
            # If not found, append a new term clause
            must_clause.append(execution_entity_id)

        query["query"]["bool"]["must"] = must_clause

        return query

    @staticmethod
    def driver_query(
        date_range: DateRange,
        is_roadmap_query: bool,
        additional_filters: Optional[List[dict]] = None,
        aggs: Optional[dict] = None,
        es_client=None,
    ) -> dict:
        filters = [
            {"term": {"workflow.isReported": False}},
            {"terms": {"workflow.status": ["REPORTABLE", "REPORTABLE_USER_OVERRIDE"]}},
            {
                "terms": {
                    "workflow.validationStatus": ["PASSED", "PASSED_USER_OVERRIDE"]
                }
            },
        ]

        expr = dict()
        if date_range.from_date:
            expr["gte"] = date_range.from_date.isoformat()
        if date_range.to_date:
            expr["lte"] = date_range.to_date.isoformat()
        date_filter = {"range": {"transactionDetails.tradingDateTime": expr}}
        canc_filter = {"term": {"reportDetails.reportStatus": "CANC"}}
        status_filter = {
            "bool": {
                "must_not": [
                    {"exists": {"field": es_client.meta.expiry}},
                ],
                "should": [
                    date_filter,
                ],
                "minimum_should_match": 1,
            }
        }
        if is_roadmap_query:
            """
            RoadMap Query Should Fetch All Eligible Txn's
            """
            status_filter["bool"]["should"].append(canc_filter)
        else:
            """This Filter Ensures Query Should Fetch Only NEWT's if we dont pass the override Query"""
            status_filter["bool"]["must_not"].append(canc_filter)

        filters.append(status_filter)

        if additional_filters:
            filters.extend(additional_filters)

        body = {
            "query": {"bool": {"filter": filters}},
        }
        if aggs:
            body.update({"size": 0, "aggs": aggs})

        return body

    def assign_date_range(self, args: dict) -> DateRange:
        if "fromDate" not in args and "toDate" not in args:
            to_date = self.action_to_date(action=ActionType.CURRENT)
            self.logger.info(f"set driver to date as {to_date}")
            return DateRange(to_date=to_date)

        from_date = args.get("fromDate")
        from_date = None if from_date == "None" else int(from_date)
        if from_date:
            from_date = pendulum.from_timestamp(from_date / 1000)
            self.logger.info(f"set driver from date as {from_date}")

        to_date = args.get("toDate")
        to_date = None if to_date == "None" else int(to_date)
        if to_date:
            to_date = pendulum.from_timestamp(to_date / 1000)
            self.logger.info(f"set driver to date as {to_date}")

        return DateRange(from_date=from_date, to_date=to_date)

    @staticmethod
    def action_to_date(action: str) -> datetime:
        now = pendulum.now(tz=pendulum.UTC)
        offset = (
            0
            if action == ActionType.NEXT
            else -1
            if action == ActionType.CURRENT
            else -2
        )
        max_transaction_date = (now + pd.offsets.BDay(offset)).date()
        return max_transaction_date
