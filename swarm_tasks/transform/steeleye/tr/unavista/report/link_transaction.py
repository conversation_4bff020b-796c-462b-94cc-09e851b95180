from typing import Optional

import pandas as pd
from swarm.schema.static import SwarmColumns
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask
from swarm.task.io.read import FrameProducerResult
from swarm.task.transform.result import TransformResult

from swarm_tasks.transform.steeleye.tr.static import TransactionReportColumns


class Resources(BaseResources):
    es_client_key: str


class LinkTransaction(BaseTask):
    resources_class = Resources

    def execute(
        self,
        result: Optional[FrameProducerResult] = None,
        params: Optional[BaseParams] = None,
        resources: Resources = None,
    ) -> TransformResult:

        if result.frame.empty:
            return TransformResult(
                target=pd.DataFrame(), batch_index=result.batch_index
            )

        # assign report id using first record in meta
        report_id = result.frame.iloc[0][TransactionReportColumns.RTS22_REPORT_ID]
        self.logger.info(f"assign report id to {report_id}")

        es = self.clients.get(resources.es_client_key)

        id_fields = [
            SwarmColumns.SWARM_RAW_INDEX,
            es.meta.id,
            es.meta.model,
            es.meta.hash,
            es.meta.version,
        ]

        # constrain target to id fields
        target = result.frame[id_fields]

        # create workflow report id col
        target.loc[:, "workflow.reportId"] = report_id

        # increment version
        target.loc[:, es.meta.version] = target[es.meta.version] + 1

        return TransformResult(target=target, batch_index=result.batch_index)
