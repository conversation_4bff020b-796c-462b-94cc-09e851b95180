import json
from datetime import datetime
from datetime import timezone
from enum import Enum
from pathlib import Path
from typing import List
from typing import Optional
from xml.etree import cElementTree
from xml.etree.ElementTree import Element

import addict
import pandas as pd
import pendulum
from generator.response_email_generator import ARMResponseEmailGenerator
from generator.response_email_generator import NCAResponseEmailGenerator
from prefect import context
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_elastic_schema.models import RTS22Transaction
from se_elastic_schema.models import RTS22TransactionReport
from se_elastic_schema.static.mifid2 import RTS22ExternalStatus
from se_elasticsearch.repository import AnyElasticsearchRepositoryType
from se_trades_tasks.tr.workflow.static import ScheduleTypeEnum
from swarm.conf import Settings
from swarm.schema.static import SwarmColumns
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask
from swarm.utilities.indict import Indict

from swarm_tasks.tr.workflow.generic.utils import amazon_ses_send_email
from swarm_tasks.tr.workflow.generic.utils import get_rts22_transaction_report
from swarm_tasks.transform.steeleye.tr.unavista.utils import (
    fetch_tr_config_from_platform_config,
)

UNAVISTA_ARM_NS = "{unavista.mifir.response.001.001.001}"
UNAVISTA_NCA_NS = "{unavista.mifir.nca.response.001.001.001}"


class ResponseType(str, Enum):
    ARM = "ARM"
    NCA = "NCA"


class Resources(BaseResources):
    es_client_key: str = "tenant-data"


class Params(BaseParams):
    chunksize: int
    response_type: ResponseType
    email_from: str = "<EMAIL>"
    source_arn: str = "arn:aws:ses:eu-west-1:510739586076:identity/steel-eye.com"


class UnavistaResponseHandler(BaseTask):
    params_class = Params
    resources_class = Resources

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[Resources] = None,
        extractor_result: ExtractPathResult = None,
    ) -> List[FileSplitterResult]:

        es: AnyElasticsearchRepositoryType = Settings.connections.get(
            resources.es_client_key
        )
        source_dir = Path(Settings.context.sources_dir)
        resp_type = params.response_type.lower()
        xmlns = UNAVISTA_NCA_NS if resp_type == "nca" else UNAVISTA_ARM_NS

        batches = []
        for idx, df_chunk in enumerate(
            self._process_response_file(
                es=es,
                file_path=extractor_result.path,
                resp_type=resp_type,
                xmlns=xmlns,
                chunk_size=params.chunksize,
                params=params,
            )
        ):
            df_chunk.index.name = SwarmColumns.SWARM_RAW_INDEX
            df_chunk = df_chunk.reset_index()
            batch_csv = self._produce_csv_batch_file(
                batch_dir=source_dir,
                df=df_chunk,
                batch_num=idx,
            )
            batch_extract_result = FileSplitterResult(path=batch_csv, batch_index=idx)
            batches.append(batch_extract_result)
            idx += 1

        return batches

    def _process_response_file(
        self,
        es: AnyElasticsearchRepositoryType,
        file_path: Path,
        resp_type: str,
        xmlns: str,
        chunk_size: int,
        params: Params,
    ):
        count: int = 0
        count_data_for_email = {}
        file_id = "n/a"
        report = {}
        first_tr = None
        datalist = []
        for event, elem in cElementTree.iterparse(file_path, events=("end",)):
            if elem.tag == (xmlns + "MessageInformation"):
                report = map_elem_to_report(
                    elem, resp_type, xmlns, context.parameters["file_url"]
                )
                file_id = report.pop("fileId")
                context.swarm.file_id = file_id
                count_data_for_email = (
                    _get_nca_tr_count_data(elem=elem, xmlns=xmlns)
                    if resp_type == "nca"
                    else _get_arm_tr_count_data(elem=elem, xmlns=xmlns)
                )
                elem.clear()

            elif elem.tag == (xmlns + "ErrorTransactionInformation") or elem.tag == (
                xmlns + "TransactionInformation"
            ):
                count += 1
                tr = map_elem_to_tr(elem, file_id, resp_type, xmlns)
                datalist.append(tr)
                if not first_tr:
                    first_tr = tr

                if count % chunk_size == 0:
                    yield pd.DataFrame.from_records(datalist)
                    datalist = []

                elem.clear()

        yield pd.DataFrame.from_records(datalist)

        report_id = self._update_report(
            es, file_id=file_id, report=report, first_tr=first_tr
        )
        report_details = get_rts22_transaction_report(
            es=es, report_id=report_id, logger=self.logger
        )

        self._notify_users(
            report_details=report_details,
            count_data=count_data_for_email,
            params=params,
        )
        self.logger.info(f"processed {count} items")

    def _produce_csv_batch_file(
        self, batch_dir: Path = None, df: pd.DataFrame = None, batch_num: int = None
    ) -> Path:
        """Produces a batch CSV file from a dataframe object"""

        batch_file_path = batch_dir.joinpath(f"batch_{batch_num}.csv")
        self.logger.info(f"Generating batch file: {batch_file_path}")
        df.to_csv(batch_file_path, index=False, encoding="utf-8", sep=",")
        return batch_file_path

    def _notify_users(
        self,
        report_details: dict,
        count_data: dict,
        params: Params,
    ):
        """
        send email notification to users if email notification is enabled in tenant config
        """
        realm = Settings.realm

        msg = None
        recipients = []

        # We don't want the flow to break when the datetime fields aren't
        # populated. So if not populated then just fallback to the
        # default field and then log and audit it.
        report_submission_datetime, arm_submission_datetime = self.get_datetime_fields(
            report_details
        )

        if params.response_type == ResponseType.ARM:
            notify_on_arm_response_config = fetch_tr_config_from_platform_config(
                tenant=Settings.tenant,
                stack=Settings.STACK,
                env=Settings.env,
                schedule_type=ScheduleTypeEnum.notify_on_response_from_arm,
            )

            if notify_on_arm_response_config:
                recipients = notify_on_arm_response_config.get("recipients", list())

                # construct email generator for arm response
                email_generator = ARMResponseEmailGenerator(realm=realm)

                self.logger.info("generate email message for arm response")
                msg = email_generator.generate_email(
                    subject="RTS22 Transaction Report: ARM Response Notification",
                    report_id=report_details["reportId"].lower(),
                    receipt_time=count_data["receipt_datetime"]
                    .time()
                    .isoformat()
                    .split(".")[0],
                    transaction_count=count_data.get("transaction_count", 0),
                    arm_accepted_count=count_data.get("arm_accepted_count", 0),
                    arm_rejected_count=count_data.get("arm_rejected_count", 0),
                    report_submission_datetime=report_submission_datetime,
                    arm_submission_datetime=arm_submission_datetime,
                )

        if params.response_type == ResponseType.NCA:
            notify_on_nca_response_config = fetch_tr_config_from_platform_config(
                tenant=Settings.tenant,
                stack=Settings.STACK,
                env=Settings.env,
                schedule_type=ScheduleTypeEnum.notify_on_response_from_nca,
            )

            if notify_on_nca_response_config:
                recipients = notify_on_nca_response_config.get("recipients", list())

                # construct email generator for nca response
                email_generator = NCAResponseEmailGenerator(realm=realm)

                nca_submission_datetime = datetime.strptime(
                    report_details["nca"]["responseReceived"], "%Y-%m-%dT%H:%M:%S.%fZ"
                )

                self.logger.info("generate email message for nca response")
                msg = email_generator.generate_email(
                    subject="RTS22 Transaction Report: NCA Response Notification",
                    report_id=report_details["reportId"].lower(),
                    receipt_time=count_data["receipt_datetime"]
                    .time()
                    .isoformat()
                    .split(".")[0],
                    transaction_count=count_data.get("transaction_count", 0),
                    arm_accepted_count=count_data.get("arm_accepted_count", 0),
                    arm_rejected_count=count_data.get("arm_rejected_count", 0),
                    nca_submitted_count=count_data.get("nca_submitted_count", 0),
                    nca_accepted_count=count_data.get("nca_accepted_count", 0),
                    nca_rejected_count=count_data.get("nca_rejected_count", 0),
                    report_submission_datetime=report_submission_datetime,
                    arm_submission_datetime=arm_submission_datetime,
                    nca_submission_datetime=nca_submission_datetime,
                )

        if msg:
            amazon_ses_send_email(
                source=params.email_from,
                source_arn=params.source_arn,
                destinations=recipients,
                raw_message=dict(Data=msg.as_string()),
                logger=self.logger,
            )

    def get_datetime_fields(self, report_details: dict) -> List[datetime]:
        message_index_values = ["Report submitted", "ARM submission"]
        result = []

        for index, datetime_field in enumerate(
            [
                report_details.get("submitted"),
                report_details.get("arm", {}).get("responseReceived"),
            ]
        ):
            if not datetime_field:
                datetime_field = (
                    datetime.now(timezone.utc).isoformat().replace("+00:00", "Z")
                )
                message = f"{[message_index_values[index]]} datetime is None. Falling back to {datetime_field}"
                self.logger.info(message)
                self.auditor.add(message)
            result.append(datetime.strptime(datetime_field, "%Y-%m-%dT%H:%M:%S.%fZ"))

        return result

    def _update_report(
        self, es: AnyElasticsearchRepositoryType, file_id, report, first_tr
    ) -> Optional[str]:
        try:
            tenant = Settings.tenant

            cur_report = self._get_tr_report(
                es=es, tenant=tenant, file_id=file_id, first_tr=first_tr
            )
            if not cur_report:
                self.logger.error(
                    f"unable to find report matching file_id {file_id} or tr {first_tr}]"
                )
                return

            alias = RTS22TransactionReport.get_elastic_index_alias(tenant=tenant)
            resp = es.client.update(
                index=alias,
                id=f"{cur_report.workflow.reportId.lower()}",
                body={"doc": report},
                refresh=True,
            )
            self.logger.info(json.dumps(resp.body))
            return cur_report.workflow.reportId
        except Exception as e:
            self.logger.error(e)
            raise e

    def _get_tr_report(
        self, es: AnyElasticsearchRepositoryType, tenant, file_id, first_tr
    ):
        first_trno = (
            first_tr.get("reportDetails.transactionRefNo") if first_tr else "n/a"
        )
        first_stat = first_tr.get("reportDetails.reportStatus") if first_tr else "n/a"
        query = {
            "size": 1,
            "query": {
                "bool": {
                    "must_not": [{"exists": {"field": es.meta.expiry}}],
                    "filter": [{"term": {es.meta.model: "RTS22Transaction"}}],
                    "should": [
                        {"term": {"workflow.arm.response": f"{file_id}"}},
                        {
                            "bool": {
                                "filter": [
                                    {
                                        "term": {
                                            "reportDetails.transactionRefNo": first_trno
                                        }
                                    },
                                    {
                                        "term": {
                                            "reportDetails.reportStatus": first_stat
                                        }
                                    },
                                ]
                            }
                        },
                    ],
                    "minimum_should_match": 1,
                }
            },
            "_source": ["workflow.reportId"],
        }
        alias = RTS22Transaction.get_elastic_index_alias(tenant=tenant)
        resp = es.search(query=query, index=alias)
        self.logger.info(json.dumps(resp))

        hits = resp["hits"]["hits"]
        return addict.Dict(hits[0]["_source"]) if hits else None


def map_elem_to_report(elem: Element, resp_type: str, xmlns: str, response_url: str):
    return {
        "fileId": __get_text(elem, "FileID", xmlns),
        resp_type: {
            "responseReceived": datetime.utcnow().isoformat() + "Z",
            "responseUrl": response_url,
            "counts": _get_nca_counts(elem, xmlns)
            if resp_type == "nca"
            else _get_arm_counts(elem, xmlns),
        },
    }


def _get_arm_counts(elem: Element, xmlns: str):
    return {
        "pending": 0,
        "accepted": int(__get_text(elem, "QueuedToNCAs", xmlns)),
        "rejected": int(__get_text(elem, "ARMFailed", xmlns)),
    }


def _get_nca_counts(elem: Element, xmlns: str):
    return {
        "pending": int(__get_text(elem, "NCAPending", xmlns)),
        "accepted": int(__get_text(elem, "NCAAccepted", xmlns)),
        "rejected": int(__get_text(elem, "NCARejected", xmlns)),
    }


def map_elem_to_tr(elem: Element, file_id: str, resp_type: str, xmlns: str):
    result = {
        "reportDetails": {
            "transactionRefNo": __get_text(elem, "TransactionReferenceNumber", xmlns),
            "reportStatus": __get_text(elem, "ReportType", xmlns),
        },
        "workflow": {},
    }

    errors = []
    for error_info in elem.findall(xmlns + "ErrorInformation"):
        item = {
            "ruleId": __get_text(error_info, "Code", xmlns),
            "errorCode": __get_text(error_info, "Value", xmlns),
            "errorText": __get_text(error_info, "Reason", xmlns),
            "transactionField": __get_text(error_info, "Field", xmlns),
        }
        errors.append(item)

    work_result = {"response": file_id}
    accepted = False
    if errors:
        work_result["status"] = RTS22ExternalStatus.REJECTED
        work_result["checks"] = errors
    else:
        work_result["status"] = RTS22ExternalStatus.ACCEPTED
        accepted = True

    result["workflow"][resp_type] = work_result
    if accepted and resp_type == "arm":
        result["workflow"]["nca"] = {"status": RTS22ExternalStatus.PENDING}

    return Indict(obj=result).flatten().remove_empty().to_dict()


def _get_nca_tr_count_data(elem: Element, xmlns: str) -> dict:
    """
    fetch transaction count data from xml element
    """
    return {
        "transaction_count": int(__get_text(elem, "Reports", xmlns)),
        "arm_accepted_count": int(__get_text(elem, "QueuedToNCAs", xmlns)),
        "arm_rejected_count": int(__get_text(elem, "ARMFailed", xmlns)),
        "nca_accepted_count": int(__get_text(elem, "NCAAccepted", xmlns)),
        "nca_rejected_count": int(__get_text(elem, "NCARejected", xmlns)),
        "nca_submitted_count": int(__get_text(elem, "SubmittedToNCAs", xmlns)),
        "receipt_datetime": __parse_datetime(
            __get_text(elem, "ReportDateTime", xmlns), resp_type="nca"
        ),
    }


def _get_arm_tr_count_data(elem: Element, xmlns: str) -> dict:
    """
    fetch transaction count data from xml element
    """
    return {
        "transaction_count": int(__get_text(elem, "Reports", xmlns)),
        "arm_accepted_count": int(__get_text(elem, "QueuedToNCAs", xmlns)),
        "arm_rejected_count": int(__get_text(elem, "ARMFailed", xmlns)),
        "receipt_datetime": __parse_datetime(
            __get_text(elem, "ReportDateTime", xmlns), resp_type="arm"
        ),
    }


def __get_text(elem: Element, attrib: str, xmlns: str):
    node = elem.find(xmlns + attrib)
    return node.text.strip() if (node is not None and node.text is not None) else None


def __parse_datetime(date_string: str, resp_type: str) -> datetime:
    """
    Tries to use datetime for the expected format, if it errors, tries to
    use pendulum to guess the format and convert to the appropriate datetime object
    :param date_string: string of the datetime
    :return: datetime object
    """
    try:
        parsed_datetime = (
            datetime.strptime(date_string, "%Y-%m-%dT%H:%M:%SZ")
            if resp_type == "nca"
            else datetime.strptime(date_string, "%Y-%m-%dT%H:%M:%S.%fZ")
        )
        return parsed_datetime
    except ValueError:
        parsed_datetime = pendulum.parse(
            date_string[:-1] if date_string.endswith("Z") else date_string
        )
        return parsed_datetime
