import json

import pandas as pd
from se_elastic_schema.models import RTS22Transaction
from se_elastic_schema.static.mifid2 import RTS22ExternalStatus
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):
    response_type: str


class Resources(BaseResources):
    es_client_key: str = "tenant-data"


class LinkTransactionResponse(TransformBaseTask):
    params_class = Params
    resources_class = Resources

    ID_FIELDS = ["reportDetails.transactionRefNo", "reportDetails.reportStatus"]

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: Resources = None,
        **kwargs,
    ) -> pd.DataFrame:

        if source_frame.empty:
            return pd.DataFrame()

        df = source_frame[self.ID_FIELDS].dropna(how="all")
        if df.empty:
            return pd.DataFrame()

        self.logger.info("Getting transaction reference details to fetch")
        tr_ref_terms = df.apply(self._build_tr_ref_terms, axis=1).tolist()

        transactions = self._get_transactions(
            resources, params.response_type.lower(), tr_ref_terms
        )
        if transactions.empty:
            return pd.DataFrame()

        source_frame = source_frame.reset_index()
        target = pd.merge(
            left=source_frame, right=transactions, on=self.ID_FIELDS, how="right"
        )
        return target

    def _build_tr_ref_terms(self, ser: pd.Series) -> dict:
        return {
            "bool": {
                "must": [
                    {"term": {self.ID_FIELDS[0]: ser.get(self.ID_FIELDS[0])}},
                    {"term": {self.ID_FIELDS[1]: ser.get(self.ID_FIELDS[1])}},
                ]
            }
        }

    def _get_transactions(self, resources, resp_type, tr_ref_terms: list):
        es = Settings.connections.get(resources.es_client_key)

        query = {
            "size": es.MAX_QUERY_SIZE,
            "query": {
                "bool": {
                    "must_not": [{"exists": {"field": es.meta.expiry}}],
                    "filter": [
                        {"term": {es.meta.model: "RTS22Transaction"}},
                        {
                            "terms": {
                                f"workflow.{resp_type}.status": [
                                    f"{RTS22ExternalStatus.PENDING}",
                                    f"{RTS22ExternalStatus.REJECTED}",
                                    f"{RTS22ExternalStatus.REJECTION_CLEARED}",
                                ],
                            }
                        },
                    ],
                    "should": tr_ref_terms,
                    "minimum_should_match": 1,
                }
            },
            "_source": [
                es.meta.id,
                es.meta.model,
                es.meta.hash,
                es.meta.version,
                self.ID_FIELDS[0],
                self.ID_FIELDS[1],
            ],
        }

        self.logger.info(f"scrolling transactions using {json.dumps(query)}")
        tenant = Settings.tenant
        alias = RTS22Transaction.get_elastic_index_alias(tenant=tenant)
        data = es.scroll(query=query, index=alias)

        if not data.empty:
            data[es.meta.version] = data[es.meta.version] + 1

        return data
