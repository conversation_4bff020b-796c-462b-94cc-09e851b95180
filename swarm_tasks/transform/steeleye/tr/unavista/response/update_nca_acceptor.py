import json
from typing import Any
from typing import Optional

from prefect import context
from se_elastic_schema.models import RTS22Transaction
from se_elastic_schema.static.mifid2 import RTS22ExternalStatus
from swarm.conf import Settings
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


class Resources(BaseResources):
    es_client_key: str


class UpdateNcaAcceptor(BaseTask):
    resources_class = Resources

    def execute(
        self,
        resources: Optional[Resources] = None,
        **kwargs,
    ) -> Any:

        file_id = context.swarm.file_id
        self.logger.info(f"update transactions => {file_id}")

        es = self.clients.get(resources.es_client_key)
        update_query = {
            "script": {
                "source": f"ctx._source.workflow.nca.response = '{file_id}'; "
                f"ctx._source.workflow.nca.status = '{RTS22ExternalStatus.ACCEPTED}'; "
                f"ctx._source.workflow.nca.remove('checks'); "
                f"ctx._source['{es.meta.version}'] += 1; "
            },
            "query": {
                "bool": {
                    "filter": [
                        {"term": {es.meta.model: "RTS22Transaction"}},
                        {"term": {"workflow.arm.response": f"{file_id}"}},
                        {
                            "term": {
                                "workflow.nca.status": f"{RTS22ExternalStatus.PENDING}"
                            }
                        },
                    ],
                    "must_not": {"term": {"workflow.nca.response": f"{file_id}"}},
                }
            },
        }

        self.logger.debug(f"update nca acceptor using {json.dumps(update_query)}")
        alias = RTS22Transaction.get_elastic_index_alias(tenant=Settings.tenant)
        response = es.update_by_query(query=update_query, index=alias)

        self.logger.info(f"update nca acceptor\n{response}")
        return response
