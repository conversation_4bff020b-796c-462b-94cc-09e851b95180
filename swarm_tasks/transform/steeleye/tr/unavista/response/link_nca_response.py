import numpy as np
import pandas as pd
from prefect import context
from se_elastic_schema.static.mifid2 import RTS22ExternalStatus
from swarm.task.transform.base import TransformBaseTask


class LinkNcaResponse(TransformBaseTask):
    def execute(self, source_frame: pd.DataFrame = None, **kwargs) -> pd.DataFrame:

        file_id = context.swarm.file_id
        self.logger.info(f"update transactions => {file_id}")

        if source_frame.empty:
            return pd.DataFrame()

        target_frame = source_frame
        target_frame["workflow.nca.response"] = file_id
        target_frame["workflow.nca.status"] = RTS22ExternalStatus.ACCEPTED
        target_frame["workflow.nca.checks"] = np.nan
        target_frame = target_frame.reset_index()

        return target_frame
