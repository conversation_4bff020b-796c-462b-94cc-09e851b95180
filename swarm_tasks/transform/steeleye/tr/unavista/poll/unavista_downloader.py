import re
from pathlib import Path
from tempfile import mkdtemp
from typing import List
from typing import Optional

from pysftp import <PERSON>Exception
from pysftp import CredentialException
from se_core_tasks.core.core_dataclasses import S3Action
from se_core_tasks.core.core_dataclasses import S3File
from se_elastic_schema.validators.iso.lei import LEI
from swarm.client.sftp import SftpClient
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


LEI_REGEX_PATTERN = re.compile(r"([A-Z\d]{18}[\d]{2})")


class Params(BaseParams):
    sftp_file_path: str
    parameter_name: str = "lei/sftp/unavista"
    decrypt: bool = True
    retry_count: int = 6


class Resources(BaseResources):
    sftp_client_key: str


class UnavistaDownloader(BaseTask):
    params_class = Params
    resources_class = Resources

    # noinspection PyBroadException
    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[Resources] = None,
        **kwargs,
    ) -> List[S3File]:

        downloaded_files = []
        sftp_client: SftpClient = self.clients.get(resources.sftp_client_key)
        local_path = Path(mkdtemp())

        param_name = f"/{Settings.STACK}/{params.parameter_name}"
        mapping = self._get_lei_mapping(param_name, params.decrypt)
        try:
            with sftp_client.connect(retry_count=params.retry_count) as sftp:
                self.logger.info(f"change sftp dir to {params.sftp_file_path}")

                sftp.chdir(params.sftp_file_path)
                self.logger.info("invoke listdir")

                target_mappings = {}
                for remote_file_attr in sftp.listdir_attr():
                    remote_file = remote_file_attr.filename
                    if (
                        remote_file_attr.st_size == 0
                        or remote_file_attr.longname.startswith("d")
                    ):
                        # directories have size 0 and the longmane starts with "drwx.."
                        self.logger.info(f"skipping dir {remote_file}")
                        continue

                    lei_from_filename = remote_file.split("_")[0].upper()
                    if not LEI.is_valid_value(lei_from_filename):
                        self.logger.info(
                            f"lei `{lei_from_filename}` is invalid."
                            f"Remote file {remote_file} doesnt follow naming convention. Parsing filename to get lei"
                        )
                        leis_by_regex = [
                            i
                            for i in LEI_REGEX_PATTERN.findall(remote_file)
                            if LEI.is_valid_value(i)
                        ]
                        if len(leis_by_regex) != 1:
                            self.logger.error(
                                f"Either zero or more than one leis found in file name. Skipping file `{remote_file}`"
                            )
                            continue
                        lei_from_filename = leis_by_regex[0]
                    bucket = mapping.get(lei_from_filename)
                    if not bucket:
                        self.logger.info(
                            f"skipping file `{remote_file}` as no mapping found for `{lei_from_filename}`"
                        )
                        continue
                    target_mappings[remote_file] = bucket

                for unavista_file_name, bucket in target_mappings.items():
                    local_file_path = local_path.joinpath(unavista_file_name)
                    try:
                        self.logger.info(f"get remote file {unavista_file_name}")
                        sftp.get(unavista_file_name, localpath=local_file_path)

                        agent = "nca" if "_NCARes_" in unavista_file_name else "arm"
                        key_name = (
                            f"flows/tr-workflow-unavista-process-{agent}-response"
                            f"/{unavista_file_name}"
                        )
                        self.logger.info(
                            f"add target result for s3://{bucket}/{key_name}"
                        )

                        s3_file = S3File(
                            file_path=local_file_path,
                            bucket_name=bucket,
                            key_name=key_name,
                            action=S3Action.UPLOAD,
                            bytes=local_file_path.stat().st_size,
                        )
                        downloaded_files.append(s3_file)

                    except Exception:
                        self.logger.exception(
                            f"SftpDownloader - failed to download {unavista_file_name}"
                        )

        except ConnectionException as e:
            self.logger.exception("SftpDownloader - connection error")
            raise e

        except CredentialException as e:
            self.logger.exception("SftpDownloader - credentials error")
            raise e

        except PermissionError:
            self.logger.exception("SftpDownloader - permission error")

        return downloaded_files

    @staticmethod
    def _get_lei_mapping(name: str, decrypt: bool):
        ssm = Settings.secrets_store
        values = ssm.get_secret(name, decrypt=decrypt).split("\n")

        mapping = {}

        for value in values:
            lei, client = value.split("=")
            mapping[lei] = client

        return mapping
