import pandas as pd
from se_trades_tasks.tr.eligibility_assessor.eligibility_assessor import (
    run_eligibility_assessor,
)
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):
    pass


class Resources(BaseResources):
    es_client_key: str
    srp_client_key: str


class EligibilityAssessor(TransformBaseTask):
    """
    Algo steps:
        - Fetch instruments from SRP
        - Map Firds Instruments
    """

    params_class = Params
    resources_class = Resources

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: Resources = None,
        **kwargs,
    ) -> pd.DataFrame:
        if source_frame.empty:
            return source_frame

        return run_eligibility_assessor(
            source_frame=source_frame,
            tenant=Settings.tenant,
            es_client=self.clients.get(resources.es_client_key),
            srp_client=self.clients.get(resources.srp_client_key),
        )
