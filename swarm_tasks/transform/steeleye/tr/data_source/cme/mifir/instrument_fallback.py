import itertools
import re
from typing import List
from typing import Optional

import pandas as pd
from pydantic import Field
from se_core_tasks.currency.convert_minor_to_major import ConvertMinorToMajor
from se_elastic_schema.static.mifid2 import RTS22TransactionStatus
from se_elastic_schema.static.reference import InstrumentIdCodeType
from se_elastic_schema.static.reference import StrikePriceType
from se_elastic_schema.validators.iso.isin import ISIN
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.orders.common_utils import static
from swarm_tasks.transform.steeleye.tr.data_source.cme.mifir.static import (
    CmeMifirColumns,
)

INSTRUMENT_PATH = "instrumentDetails.instrument"
WORKFLOW_ELIGIBILITY_PATH = "workflow.eligibility"
COMMODITIES_INSTRUMENT_PREFIX = "JT"


class InstrumentFields:
    # Bond
    BOND_MATURITY_DATE = "bond.maturityDate"

    # Derivative
    DERIVATIVE_DELIVERY_TYPE = "derivative.deliveryType"
    DERIVATIVE_EXPIRY_DATE = "derivative.expiryDate"
    DERIVATIVE_OPTION_EXERCISE_STYLE = "derivative.optionExerciseStyle"
    DERIVATIVE_OPTION_TYPE = "derivative.optionType"
    DERIVATIVE_PRICE_MULTIPLIER = "derivative.priceMultiplier"
    DERIVATIVE_STRIKE_PRICE = "derivative.strikePrice"
    DERIVATIVE_STRIKE_PRICE_CURRENCY = "derivative.strikePriceCurrency"
    DERIVATIVE_UNDERLYING_INDEX_NAME = "derivative.underlyingIndexName"
    DERIVATIVE_UNDERLYING_INDEX_TERM = "derivative.underlyingIndexTerm"
    DERIVATIVE_UNDERLYING_INSTRUMENTS = "derivative.underlyingInstruments"

    # Ext
    EXT_INSTRUMENT_ID_CODE_TYPE = "ext.instrumentIdCodeType"
    EXT_ON_FIRDS = "ext.onFIRDS"
    EXT_MIFIR_ELIGIBLE = "ext.mifirEligible"
    EXT_NOTIONAL_CURRENCY_2_TYPE = "ext.notionalCurrency2Type"
    EXT_PRICE_NOTATION = "ext.priceNotation"
    EXT_QUANTITY_NOTATION = "ext.quantityNotation"
    EXT_STRIKE_PRICE_TYPE = "ext.strikePriceType"
    EXT_VENUE_NAME = "ext.venueName"
    EXT_UNDERLYING_INSTRUMENTS = "ext.underlyingInstruments"

    # FX Derivatives
    FX_DERIVATIVES_NOTIONAL_CURRENCY_2 = "fxDerivatives.notionalCurrency2"

    # TODO: underlyings
    INSTRUMENT_CLASSIFICATION = "instrumentClassification"
    INSTRUMENT_FULL_NAME = "instrumentFullName"

    INSTRUMENT_ID_CODE = "instrumentIdCode"

    UNDERLYING_INSTRUMENT_ID = "underlyingInstrumentCode"

    NOTIONAL_CURRENCY_1 = "notionalCurrency1"

    VENUE_TRADING_VENUE = "venue.tradingVenue"

    COMMODITIES_OR_EMISSION_ALLOWANCE_DER_IND = (
        "commoditiesOrEmissionAllowanceDerivativeInd"
    )

    IS_CREATED_THROUGH_FALLBACK = "isCreatedThroughFallback"


class RTS22TransactionFields:
    TRX_DTL_PRICE_NOTATION = "transactionDetails.priceNotation"
    TRX_DTL_QUANTITY_NOTATION = "transactionDetails.quantityNotation"
    REPORT_DTL_REPORT_STATUS = "reportDetails.reportStatus"
    SWAP_DIRECTIONALITIES = "transactionDetails.swapDirectionalities"


class SwapDirectionalities:
    SWAP_IN = "Swap In"
    SWAP_OUT = "Swap Out"
    OTHER = "Other"


class WorkflowEligibilityFields:
    ELIGIBILITY_ELIGIBLE = "eligible"
    ELIGIBILITY_EXECUTION_VENUE = "executionVenue"
    ELIGIBILITY_ON_FIRDS = "onFirds"
    ELIGIBILITY_UNDERLYING_ON_FIRDS = "underlyingOnFirds"
    ELIGIBILITY_REASON = "executionVenue"
    ELIGIBILITY_TOTV = "totv"
    ELIGIBILITY_UTOTV = "utotv"


class WorkflowFields:
    STATUS = "workflow.status"


class Params(BaseParams):
    instrument_tags_eligible: Optional[List[str]] = Field(
        None,
        description="List of tags to check against"
        " source instrument and underlying index names"
        " to make the record mifir eligible",
    )
    instrument_tags_non_eligible: Optional[List[str]] = Field(
        None,
        description="List of tags to check against"
        " source instrument and underlying index names"
        " to make the record mifir NON eligible",
    )
    instrument_classification_regex: Optional[str] = Field(
        None,
        description="Instrument classification regex"
        " to check against source instrument"
        " to make the record mifir NON_REPORTABLE",
    )


class InstrumentFallback(TransformBaseTask):
    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: BaseResources = None,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index, columns=[INSTRUMENT_PATH])
        if source_frame.empty:
            return target
        cols_used = [
            CmeMifirColumns.NOTIONAL_CURRENCY_1,
            CmeMifirColumns.NOTIONAL_CURRENCY_2,
            CmeMifirColumns.STRIKE_PRICE_CURRENCY,
            CmeMifirColumns.INSTRUMENT_ID,
            CmeMifirColumns.INSTRUMENT_NAME,
            CmeMifirColumns.STRIKE_PRICE_TYPE,
            CmeMifirColumns.UNDERLYING_INDEX_NAME,
            CmeMifirColumns.UNDERLYING_INDEX_TERM,
            CmeMifirColumns.UNDERLYING_INDEX_ID,
            CmeMifirColumns.UNDERLYING_INSTRUMENT_ID,
            CmeMifirColumns.DELIVERY_TYPE,
            CmeMifirColumns.PRICE_TYPE,
            CmeMifirColumns.QUANTITY_TYPE,
            CmeMifirColumns.EXPIRY_DATE,
            CmeMifirColumns.PRICE_MULTIPLIER,
            CmeMifirColumns.STRIKE_PRICE,
            CmeMifirColumns.INSTRUMENT_CLASSIFICATION,
            CmeMifirColumns.VENUE,
            CmeMifirColumns.MATURITYDATE,
            CmeMifirColumns.OPTIONEXERCISESTYLE,
            CmeMifirColumns.OPTIONTYPE,
            INSTRUMENT_PATH,
            RTS22TransactionFields.REPORT_DTL_REPORT_STATUS,
            RTS22TransactionFields.TRX_DTL_PRICE_NOTATION,
            RTS22TransactionFields.TRX_DTL_QUANTITY_NOTATION,
        ]
        cols_mask = source_frame.columns.isin(cols_used)

        df = source_frame.loc[:, cols_mask]

        for col in cols_used:
            if col not in df.columns:
                df[col] = pd.NA

        target = source_frame.loc[
            :,
            (
                (source_frame.columns.str.startswith("workflow"))
                | (source_frame.columns == INSTRUMENT_PATH)
            ),
        ]

        target[
            RTS22TransactionFields.SWAP_DIRECTIONALITIES
        ] = self._get_transaction_details_swap_data(source_frame)

        # Run fallback where there is no instrument
        without_instrument = df[INSTRUMENT_PATH].isnull()

        if without_instrument.any():
            data = df.loc[without_instrument]
            data = self._process_data(df=data)
            # Synthetic instrument
            synthetic_instruments = self._get_synthetic_instruments(df=data)

            target.loc[without_instrument, INSTRUMENT_PATH] = synthetic_instruments

            # Workflow
            synthetic_workflow = self._get_synthetic_workflow(df=data)

            target.loc[
                without_instrument, WORKFLOW_ELIGIBILITY_PATH
            ] = synthetic_workflow
            # Workflow status
            target.loc[
                without_instrument, WorkflowFields.STATUS
            ] = RTS22TransactionStatus.REPORTABLE_USER_OVERRIDE

            if params.instrument_classification_regex:
                self._override_reportable_status(
                    df=target,
                    not_in_srp_mask=without_instrument,
                    instrument_classification_pattern=params.instrument_classification_regex,
                )

        return target

    @staticmethod
    def _process_data(df: pd.DataFrame) -> pd.DataFrame:

        # Currencies
        # read the static json file with minor currencies and conversion map
        minor_ccy_price_data = ConvertMinorToMajor.read_minor_ccy_and_price(
            static.MINOR_CURRENCIES_FILE_PATH
        )
        for col in [
            CmeMifirColumns.NOTIONAL_CURRENCY_1,
            CmeMifirColumns.NOTIONAL_CURRENCY_2,
            CmeMifirColumns.STRIKE_PRICE_CURRENCY,
        ]:
            col_not_null_mask = df[col].notnull()
            if col_not_null_mask.any():
                df.loc[col_not_null_mask, col] = df.loc[col_not_null_mask, col].apply(
                    (
                        lambda x: ConvertMinorToMajor.convert_currency(
                            source_ccy=x, conversion_map_list=minor_ccy_price_data
                        )
                    )
                )

        # ISIN
        isin_data = df[CmeMifirColumns.INSTRUMENT_ID].dropna()

        if not isin_data.empty:
            valid_isins_mask = (df[CmeMifirColumns.INSTRUMENT_ID].notnull()) & (
                isin_data.apply(lambda x: ISIN.validate_isin_code(x).is_valid)
            )

            df.loc[
                valid_isins_mask, InstrumentFields.EXT_INSTRUMENT_ID_CODE_TYPE
            ] = InstrumentIdCodeType.ID.value

            df.loc[
                ~valid_isins_mask, InstrumentFields.EXT_INSTRUMENT_ID_CODE_TYPE
            ] = pd.NA

        # Upper
        for col in [
            CmeMifirColumns.DELIVERY_TYPE,
            CmeMifirColumns.OPTIONTYPE,
            CmeMifirColumns.OPTIONEXERCISESTYLE,
            CmeMifirColumns.PRICE_TYPE,
            CmeMifirColumns.QUANTITY_TYPE,
        ]:
            data = df[col].dropna()

            if data.empty:
                continue

            df[col] = data.str.upper()

        # Strike Price Type
        with_strike_price_type_mask = df[CmeMifirColumns.STRIKE_PRICE_TYPE].notnull()

        df.loc[with_strike_price_type_mask, CmeMifirColumns.STRIKE_PRICE_TYPE] = (
            df.loc[with_strike_price_type_mask, CmeMifirColumns.STRIKE_PRICE_TYPE]
            .map(
                {
                    "BPNT": StrikePriceType.BSISPTS.value,
                    "MONE": StrikePriceType.MNTRYVAL.value,
                }
            )
            .fillna(
                df.loc[with_strike_price_type_mask, CmeMifirColumns.STRIKE_PRICE_TYPE]
            )
        )

        # Price Type
        with_price_type_mask = df[CmeMifirColumns.PRICE_TYPE].notnull()

        df.loc[with_price_type_mask, CmeMifirColumns.PRICE_TYPE] = (
            df.loc[with_price_type_mask, CmeMifirColumns.PRICE_TYPE]
            .map({"BPNT": "BAPO"})
            .fillna(df.loc[with_price_type_mask, CmeMifirColumns.PRICE_TYPE])
        )

        # Underlying Index Name
        #   use underlying index name
        use_underlying_index_name = df[CmeMifirColumns.UNDERLYING_INDEX_NAME].notnull()

        # split values by ',', ';' and trim is by 25 characters and then join again with the comma
        df.loc[use_underlying_index_name, CmeMifirColumns.UNDERLYING_INDEX_NAME] = (
            df.loc[use_underlying_index_name, CmeMifirColumns.UNDERLYING_INDEX_NAME]
            .str.split(r"[;,]")
            .explode()
            .str[:25]
            .groupby(level=0)
            .agg(",".join)
        )

        return df

    def _get_synthetic_instruments(self, df: pd.DataFrame) -> pd.Series:
        synthetic_instruments = pd.DataFrame(index=df.index)

        # Instrument
        straight_map = [
            (CmeMifirColumns.DELIVERY_TYPE, InstrumentFields.DERIVATIVE_DELIVERY_TYPE),
            (CmeMifirColumns.EXPIRY_DATE, InstrumentFields.DERIVATIVE_EXPIRY_DATE),
            (
                CmeMifirColumns.INSTRUMENT_CLASSIFICATION,
                InstrumentFields.INSTRUMENT_CLASSIFICATION,
            ),
            (CmeMifirColumns.INSTRUMENT_ID, InstrumentFields.INSTRUMENT_ID_CODE),
            (CmeMifirColumns.INSTRUMENT_NAME, InstrumentFields.INSTRUMENT_FULL_NAME),
            (CmeMifirColumns.MATURITYDATE, InstrumentFields.BOND_MATURITY_DATE),
            (CmeMifirColumns.NOTIONAL_CURRENCY_1, InstrumentFields.NOTIONAL_CURRENCY_1),
            (
                CmeMifirColumns.NOTIONAL_CURRENCY_2,
                InstrumentFields.FX_DERIVATIVES_NOTIONAL_CURRENCY_2,
            ),
            (
                CmeMifirColumns.OPTIONEXERCISESTYLE,
                InstrumentFields.DERIVATIVE_OPTION_EXERCISE_STYLE,
            ),
            (CmeMifirColumns.OPTIONTYPE, InstrumentFields.DERIVATIVE_OPTION_TYPE),
            (CmeMifirColumns.QUANTITY_TYPE, InstrumentFields.EXT_QUANTITY_NOTATION),
            (CmeMifirColumns.STRIKE_PRICE, InstrumentFields.DERIVATIVE_STRIKE_PRICE),
            (
                CmeMifirColumns.STRIKE_PRICE_CURRENCY,
                InstrumentFields.DERIVATIVE_STRIKE_PRICE_CURRENCY,
            ),
            (CmeMifirColumns.PRICE_TYPE, InstrumentFields.EXT_PRICE_NOTATION),
            (
                CmeMifirColumns.PRICE_MULTIPLIER,
                InstrumentFields.DERIVATIVE_PRICE_MULTIPLIER,
            ),
            (CmeMifirColumns.STRIKE_PRICE_TYPE, InstrumentFields.EXT_STRIKE_PRICE_TYPE),
        ]

        for entry in straight_map:
            synthetic_instruments[entry[1]] = df[entry[0]]

        synthetic_instruments[InstrumentFields.EXT_ON_FIRDS] = False
        synthetic_instruments[InstrumentFields.EXT_MIFIR_ELIGIBLE] = True
        synthetic_instruments[InstrumentFields.IS_CREATED_THROUGH_FALLBACK] = True

        underlying_instrument_data = self._get_underlying_instrument_data(df=df)

        # commoditiesOrEmissionAllowanceDerivativeInd
        synthetic_instruments[
            InstrumentFields.COMMODITIES_OR_EMISSION_ALLOWANCE_DER_IND
        ] = self._get_commodities_or_emission_ind(df=synthetic_instruments)

        if not underlying_instrument_data.empty:
            synthetic_instruments = pd.concat(
                [synthetic_instruments, underlying_instrument_data], axis=1
            )

        inst_dtl_inst = synthetic_instruments.loc[:, :].apply(
            lambda x: x.dropna().to_dict(), axis=1
        )

        return inst_dtl_inst

    @staticmethod
    def _get_underlying_instrument_data(df: pd.DataFrame) -> pd.DataFrame:

        data = pd.DataFrame(index=df.index)
        data[InstrumentFields.EXT_UNDERLYING_INSTRUMENTS] = [
            [] for _ in range(df.shape[0])
        ]
        data[InstrumentFields.DERIVATIVE_UNDERLYING_INSTRUMENTS] = [
            [] for _ in range(df.shape[0])
        ]

        def assign_underlying_field(
            source_field: str, target_field: str, stage_field: str
        ):
            def allocate_underlying_instrument(
                index: int, under_inst_index: int, field: str, value: str
            ):
                # allocate ext.* fields
                if field != InstrumentFields.UNDERLYING_INSTRUMENT_ID:
                    if (
                        len(
                            data.loc[index, InstrumentFields.EXT_UNDERLYING_INSTRUMENTS]
                        )
                        == under_inst_index
                    ):
                        data.loc[
                            index, InstrumentFields.EXT_UNDERLYING_INSTRUMENTS
                        ].append({})

                    data.loc[index, InstrumentFields.EXT_UNDERLYING_INSTRUMENTS][
                        under_inst_index
                    ][field] = value

                # allocate derivative.* field
                if field == InstrumentFields.UNDERLYING_INSTRUMENT_ID:
                    if (
                        len(
                            data.loc[
                                index,
                                InstrumentFields.DERIVATIVE_UNDERLYING_INSTRUMENTS,
                            ]
                        )
                        == under_inst_index
                    ):
                        data.loc[
                            index, InstrumentFields.DERIVATIVE_UNDERLYING_INSTRUMENTS
                        ].append({})

                    # Join values using PIPE and remove + or - from the value
                    data.loc[index, InstrumentFields.DERIVATIVE_UNDERLYING_INSTRUMENTS][
                        under_inst_index
                    ][field] = re.sub(r"[+-]", "", "|".join(re.split(r"[;,]", value)))

            stage_field_data = df[source_field].dropna()

            if stage_field_data.empty:
                return

            # Split source field by ';', ',' to determine if field has multiple values or not
            # and remove + or - from the value
            data[stage_field] = stage_field_data.apply(
                lambda x: [
                    re.sub(r"[+-]", "", x)
                    for x in list(map(str.strip, re.split(r"[;,]", x)))
                ]
            )

            # Source field doesn't have multiple entries
            one_mask = (data[stage_field].notnull()) & (
                data[stage_field].dropna().apply(len) == 1
            )

            only_one = data[stage_field].loc[one_mask].dropna()

            if not only_one.empty:
                data.loc[one_mask, :].apply(
                    lambda x: [
                        allocate_underlying_instrument(
                            index=x.name,
                            under_inst_index=i,
                            field=target_field,
                            value=y,
                        )
                        for i, y in enumerate(x[stage_field])
                    ],
                    axis=1,
                )
                if target_field not in [
                    InstrumentFields.INSTRUMENT_ID_CODE,
                    InstrumentFields.UNDERLYING_INSTRUMENT_ID,
                ]:
                    data.loc[one_mask, target_field] = only_one.apply(lambda x: x[0])

            # Source field is an array over here
            more_than_one_mask = (data[stage_field].notnull()) & (
                data[stage_field].dropna().apply(len) > 1
            )

            more_than_one = data[stage_field].loc[more_than_one_mask].dropna()

            if not more_than_one.empty:
                data.loc[more_than_one_mask, :].apply(
                    lambda x: [
                        allocate_underlying_instrument(
                            index=x.name,
                            under_inst_index=i,
                            field=target_field,
                            value=y,
                        )
                        for i, y in enumerate(x[stage_field])
                    ],
                    axis=1,
                )

        # Underlying Index Name
        assign_underlying_field(
            source_field=CmeMifirColumns.UNDERLYING_INDEX_NAME,
            target_field=InstrumentFields.DERIVATIVE_UNDERLYING_INDEX_NAME,
            stage_field="__underlying_index_name",
        )

        # Underlying Index Term
        assign_underlying_field(
            source_field=CmeMifirColumns.UNDERLYING_INDEX_TERM,
            target_field=InstrumentFields.DERIVATIVE_UNDERLYING_INDEX_TERM,
            stage_field="__underlying_index_term",
        )

        # Underlying Instrument Id
        assign_underlying_field(
            source_field=CmeMifirColumns.UNDERLYING_INSTRUMENT_ID,
            target_field=InstrumentFields.INSTRUMENT_ID_CODE,
            stage_field="__underlying_index_id",
        )

        # Underlying Instrument ISIN
        assign_underlying_field(
            source_field=CmeMifirColumns.UNDERLYING_INSTRUMENT_ID,
            target_field=InstrumentFields.UNDERLYING_INSTRUMENT_ID,
            stage_field="__underlying_instrument_id",
        )

        data = data.loc[:, ~data.columns.str.startswith("__")]

        return data

    @staticmethod
    def _get_transaction_details_swap_data(df: pd.DataFrame) -> pd.Series:
        def assign_swap_value(
            underlying_index_name: str,
            underlying_index_id: str,
            underlying_index_term: str,
        ):
            # Split values to check if there are multiple values or not
            underlying_index_name = re.split(r"[;,]", underlying_index_name)
            underlying_index_id = re.split(r"[;,]", underlying_index_id)
            underlying_index_term = re.split(r"[,;]", underlying_index_term)

            result = []
            for index_name, index_id, _ in itertools.zip_longest(
                underlying_index_name,
                underlying_index_id,
                underlying_index_term,
                fillvalue="",  # Fill value with empty string so `.startswith` work without any complain in loop
            ):
                if index_name.startswith(("+", "-")):
                    result.append(
                        SwapDirectionalities.SWAP_IN
                        if index_name.startswith("+")
                        else SwapDirectionalities.SWAP_OUT
                    )
                elif index_id.startswith(("+", "-")):
                    result.append(
                        SwapDirectionalities.SWAP_IN
                        if index_id.startswith("+")
                        else SwapDirectionalities.SWAP_OUT
                    )
                else:
                    result.append(SwapDirectionalities.OTHER)

            return result or pd.NA

        data = df.fillna("").apply(
            lambda x: assign_swap_value(
                x[CmeMifirColumns.UNDERLYING_INDEX_NAME],
                x[CmeMifirColumns.UNDERLYING_INDEX_ID],
                x[CmeMifirColumns.UNDERLYING_INDEX_TERM],
            ),
            axis=1,
        )
        return data

    @staticmethod
    def _get_synthetic_workflow(df: pd.DataFrame) -> pd.Series:
        synthetic_workflow = pd.DataFrame(index=df.index)

        synthetic_workflow.loc[:, WorkflowEligibilityFields.ELIGIBILITY_ELIGIBLE] = True

        # Eligibility on FIRDS
        synthetic_workflow[WorkflowEligibilityFields.ELIGIBILITY_ON_FIRDS] = False

        # Eligibility Underlying on FIRDS
        synthetic_workflow[
            WorkflowEligibilityFields.ELIGIBILITY_UNDERLYING_ON_FIRDS
        ] = False

        # Eligibility TOTV
        synthetic_workflow[WorkflowEligibilityFields.ELIGIBILITY_TOTV] = False

        # Eligibility UTOTV
        synthetic_workflow.loc[:, WorkflowEligibilityFields.ELIGIBILITY_UTOTV] = True

        # Eligibility Execution Venue
        synthetic_workflow.loc[
            :, WorkflowEligibilityFields.ELIGIBILITY_EXECUTION_VENUE
        ] = "XXXX"

        workflow_eligibility = synthetic_workflow.loc[:, :].apply(
            lambda x: x.dropna().to_dict(), axis=1
        )

        return workflow_eligibility

    @staticmethod
    def _get_commodities_or_emission_ind(df: pd.DataFrame) -> pd.Series:
        df[InstrumentFields.COMMODITIES_OR_EMISSION_ALLOWANCE_DER_IND] = False
        jt_mask = (
            df[InstrumentFields.INSTRUMENT_CLASSIFICATION]
            .str.upper()
            .str.startswith(COMMODITIES_INSTRUMENT_PREFIX, na=False)
        )
        if jt_mask.any():
            df.loc[
                jt_mask, InstrumentFields.COMMODITIES_OR_EMISSION_ALLOWANCE_DER_IND
            ] = True
        return df[InstrumentFields.COMMODITIES_OR_EMISSION_ALLOWANCE_DER_IND]

    @classmethod
    def _override_reportable_status(
        cls,
        df: pd.DataFrame,
        not_in_srp_mask: pd.Series.bool,
        instrument_classification_pattern: str,
    ) -> None:
        """
        Transformative method to override reportable status of mifir records
        if instrument classification matches a given pattern and instrument
        id code is not null
        https://steeleye.atlassian.net/browse/EU-1096: this has to be
        applied only to records where the instrument was not found in SRP, i.e.,
        it was linked using the fallback logic. This is indicated by not_in_srp_mask
        :param: df: Pandas dataframe containing mifir records
        :param: not_in_srp_mask: boolean mask indicating if the instrument was not
                found in srp
        :param: instrument_classification_pattern: regex to match against
                instrument classification
        """

        instrument_classification_matches_pattern_mask = df.loc[
            :, INSTRUMENT_PATH
        ].apply(
            lambda x: cls._regex_is_perfect_match(
                instrument_classification_pattern,
                x.get(InstrumentFields.INSTRUMENT_CLASSIFICATION),
            )
            if x.get(InstrumentFields.INSTRUMENT_CLASSIFICATION)
            else False
        )

        instrument_id_code_not_null_mask = df.loc[:, INSTRUMENT_PATH].apply(
            lambda x: InstrumentFields.INSTRUMENT_ID_CODE in x
        )

        non_reportable_mask = (
            instrument_classification_matches_pattern_mask
            & instrument_id_code_not_null_mask
            & not_in_srp_mask
        )

        df.loc[
            non_reportable_mask, WorkflowFields.STATUS
        ] = RTS22TransactionStatus.NON_REPORTABLE

    @staticmethod
    def _regex_is_perfect_match(pattern: str, string: str) -> bool:
        """
        Check if a regex pattern perfectly matches a given string and return
        a boolean
        :param pattern: regex pattern
        :param string: string to which the regex pattern will be matched against
        :return: True if regex pattern is perfect match for string,
        False otherwise
        """
        if re.match(pattern, string):
            return True
        return False
