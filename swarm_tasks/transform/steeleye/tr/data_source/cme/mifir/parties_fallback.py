import hashlib
from enum import Enum
from typing import Optional

import pandas as pd
from se_elastic_schema.static.reference import MifirIdSubtype
from se_elastic_schema.static.reference import MifirIdType
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.tr.data_source.cme.mifir.static import (
    CmeMifirColumns,
)


class PersonField:
    STRUCTURE_TYPE = "structure.type"
    NAME = "name"
    FIRST_NAME = "personalDetails.firstName"
    LAST_NAME = "personalDetails.lastName"
    MIFIR_ID = "officialIdentifiers.mifirId"
    MIFIR_ID_TYPE = "officialIdentifiers.mifirIdType"
    MIFIR_ID_SUB_TYPE = "officialIdentifiers.mifirIdSubType"
    DOB = "personalDetails.dob"
    BRANCH_COUNTRY = "officialIdentifiers.branchCountry"
    UNIQUE_IDS = "uniqueIds"


class PartyField(str, Enum):
    BUYER = "parties.buyer"
    BUYER_DECISION_MAKER = "parties.buyerDecisionMaker"
    BUYER_TRANSMITTING_FIRM = "parties.buyerTransmittingFirm"
    COUNTERPARTY = "parties.counterparty"
    EXECUTING_ENTITY = "parties.executingEntity"
    EXECUTION_WITHIN_FIRM = "parties.executionWithinFirm"
    INVESTMENT_DECISION_WITHIN_FIRM = "parties.investmentDecisionWithinFirm"
    SELLER = "parties.seller"
    SELLER_DECISION_MAKER = "parties.sellerDecisionMaker"
    SELLER_TRANSMITTING_FIRM = "parties.sellerTransmittingFirm"
    TRADER = "parties.trader"


class RTS22TransactionField:
    INVESTMENT_FIRM_COVERED_DIRECTIVE = "reportDetails.investmentFirmCoveredDirective"


class Resources(BaseResources):
    es_client_key: str


# columns from upstream tasks (e.g. LinkParties) that should be passed to the target frame
PASS_THROUGH_COLUMNS = list(map(lambda x: x.value, PartyField)) + [
    RTS22TransactionField.INVESTMENT_FIRM_COVERED_DIRECTIVE
]

COLS_USED = [
    CmeMifirColumns.BUYER_FIRST_NAMES,
    CmeMifirColumns.BUYER_SURNAMES,
    CmeMifirColumns.BUYER_ID,
    CmeMifirColumns.BUYER_IDENTIFICATION_CODE_TYPE,
    CmeMifirColumns.BUYER_DATE_OF_BIRTH,
    CmeMifirColumns.BUYER_COUNTRY_OF_THE_BRANCH,
    CmeMifirColumns.SELLER_FIRSTNAMES,
    CmeMifirColumns.SELLER_SURNAMES,
    CmeMifirColumns.SELLER_ID,
    CmeMifirColumns.SELLER_IDENTIFICATION_CODE_TYPE,
    CmeMifirColumns.SELLER_DATE_OF_BIRTH,
    CmeMifirColumns.SELLER_COUNTRY_OF_THE_BRANCH,
    CmeMifirColumns.BUYER_DECISION_MAKER_FIRST_NAMES,
    CmeMifirColumns.BUYER_DECISION_MAKER_SURNAMES,
    CmeMifirColumns.BUYER_DECISION_MAKER_ID,
    CmeMifirColumns.BUYER_DECISION_MAKER_CODE_TYPE,
    CmeMifirColumns.BUYER_DECISION_MAKER_DATE_OF_BIRTH,
    CmeMifirColumns.SELLER_DECISION_MAKER_FIRST_NAMES,
    CmeMifirColumns.SELLER_DECISION_MAKER_SURNAMES,
    CmeMifirColumns.SELLER_DECISION_MAKER_ID,
    CmeMifirColumns.SELLER_DECISION_MAKER_CODE_TYPE,
    CmeMifirColumns.SELLER_DECISION_MAKER_DATE_OF_BIRTH,
    CmeMifirColumns.INVESTMENT_DECISION_ID,
    CmeMifirColumns.INVESTMENT_DECISION_WITHIN_FIRM_TYPE,
    CmeMifirColumns.INVESTMENT_DECISION_COUNTRY_OF_BRANCH,
    CmeMifirColumns.FIRM_EXECUTION_ID,
    CmeMifirColumns.EXECUTION_WITHIN_FIRM_TYPE,
    CmeMifirColumns.EXECUTING_ENTITY_COUNTRY_OF_BRANCH,
    *PASS_THROUGH_COLUMNS,
]

# note - enums from client file
VALID_PARTY_ID_TYPES = ["CCPT", "CONCAT", "NIDN"]
EXTERNAL_PARTY_META_KEY = "MarketPerson:"
INTERNAL_PARTY_META_KEY = "AccountPerson:"
MIFIR_ID_TYPE_MAP = {
    MifirIdSubtype.CONCAT: MifirIdType._N,
    MifirIdSubtype.CCPT: MifirIdType._N,
    MifirIdSubtype.NIDN: MifirIdType._N,
    "LEI": "L",
}


class PartiesFallback(TransformBaseTask):
    """
    populate party fields using data directly from the input file
    if a row has an existing party e.g. parties.buyer, it will not be updated

    NOTE:
    This applies to all party fields when the type/label is “CONCAT”, “CCPT”, “NIDN”.
    (if the label/type is “ALGO” or “LEI” then complete the link up to myMarket in the usual way)

    NOTE DL:
    &key for AccountPerson and MarketPerson is being created here as an MD5 hash from a set of properties
    TODO move this logic to SDK and create hash from HASH_PROPERTIES
    """

    resources_class = Resources

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: BaseParams = None,
        resources: Resources = None,
        **kwargs
    ) -> pd.DataFrame:
        target = pd.DataFrame(index=source_frame.index, columns=PASS_THROUGH_COLUMNS)
        if source_frame.empty:
            return target

        es = self.clients.get(resources.es_client_key)
        df = source_frame.copy()

        # copy over any existing parties from source_frame
        for col in PASS_THROUGH_COLUMNS:
            if col in source_frame.columns:
                target[col] = source_frame[col]

        # make sure required cols exist
        for col in COLS_USED:
            if col not in df.columns:
                df[col] = pd.NA

        target[PartyField.BUYER] = self._fill_missing_buyer(df, es.meta.key)
        target[PartyField.SELLER] = self._fill_missing_seller(df, es.meta.key)
        target[
            PartyField.BUYER_DECISION_MAKER
        ] = self._fill_missing_buyer_decision_maker(df, es.meta.key)
        target[
            PartyField.SELLER_DECISION_MAKER
        ] = self._fill_missing_seller_decision_maker(df, es.meta.key)
        target[
            PartyField.INVESTMENT_DECISION_WITHIN_FIRM
        ] = self._fill_missing_firm_investment_decision_maker(df, es.meta.key)
        target[PartyField.EXECUTION_WITHIN_FIRM] = self._fill_missing_firm_executioner(
            df, es.meta.key
        )
        return target

    def _fill_missing_buyer(self, df: pd.DataFrame, meta_key: str) -> pd.Series:
        result = df[PartyField.BUYER]
        mask = df[PartyField.BUYER].isna() & df[
            CmeMifirColumns.BUYER_IDENTIFICATION_CODE_TYPE
        ].isin(VALID_PARTY_ID_TYPES)
        if mask.any():
            result.loc[mask] = self._format_party(
                df.loc[mask],
                first_name=CmeMifirColumns.BUYER_FIRST_NAMES,
                last_name=CmeMifirColumns.BUYER_SURNAMES,
                mifir_id=CmeMifirColumns.BUYER_ID,
                mifir_id_type=CmeMifirColumns.BUYER_IDENTIFICATION_CODE_TYPE,
                dob=CmeMifirColumns.BUYER_DATE_OF_BIRTH,
                nationality=CmeMifirColumns.BUYER_COUNTRY_OF_THE_BRANCH,
                meta_key=meta_key,
            ).apply(lambda x: [x.dropna().to_dict()], axis=1)
        return result

    def _fill_missing_seller(self, df: pd.DataFrame, meta_key: str) -> pd.Series:
        result = df[PartyField.SELLER]
        mask = df[PartyField.SELLER].isna() & df[
            CmeMifirColumns.SELLER_IDENTIFICATION_CODE_TYPE
        ].isin(VALID_PARTY_ID_TYPES)
        if mask.any():
            result.loc[mask] = self._format_party(
                df.loc[mask],
                first_name=CmeMifirColumns.SELLER_FIRSTNAMES,
                last_name=CmeMifirColumns.SELLER_SURNAMES,
                mifir_id=CmeMifirColumns.SELLER_ID,
                mifir_id_type=CmeMifirColumns.SELLER_IDENTIFICATION_CODE_TYPE,
                dob=CmeMifirColumns.SELLER_DATE_OF_BIRTH,
                nationality=CmeMifirColumns.SELLER_COUNTRY_OF_THE_BRANCH,
                meta_key=meta_key,
            ).apply(lambda x: [x.dropna().to_dict()], axis=1)
        return result

    def _fill_missing_buyer_decision_maker(
        self, df: pd.DataFrame, meta_key: str
    ) -> pd.Series:
        result = df[PartyField.BUYER_DECISION_MAKER]
        mask = df[PartyField.BUYER_DECISION_MAKER].isna() & df[
            CmeMifirColumns.BUYER_DECISION_MAKER_CODE_TYPE
        ].isin(VALID_PARTY_ID_TYPES)
        if mask.any():
            result.loc[mask] = self._format_party(
                df.loc[mask],
                first_name=CmeMifirColumns.BUYER_DECISION_MAKER_FIRST_NAMES,
                last_name=CmeMifirColumns.BUYER_DECISION_MAKER_SURNAMES,
                mifir_id=CmeMifirColumns.BUYER_DECISION_MAKER_ID,
                mifir_id_type=CmeMifirColumns.BUYER_DECISION_MAKER_CODE_TYPE,
                dob=CmeMifirColumns.BUYER_DECISION_MAKER_DATE_OF_BIRTH,
                meta_key=meta_key,
            ).apply(lambda x: [x.dropna().to_dict()], axis=1)
        return result

    def _fill_missing_seller_decision_maker(
        self, df: pd.DataFrame, meta_key: str
    ) -> pd.Series:
        result = df[PartyField.SELLER_DECISION_MAKER]
        mask = df[PartyField.SELLER_DECISION_MAKER].isna() & df[
            CmeMifirColumns.SELLER_DECISION_MAKER_CODE_TYPE
        ].isin(VALID_PARTY_ID_TYPES)
        if mask.any():
            result.loc[mask] = self._format_party(
                df.loc[mask],
                first_name=CmeMifirColumns.SELLER_DECISION_MAKER_FIRST_NAMES,
                last_name=CmeMifirColumns.SELLER_DECISION_MAKER_SURNAMES,
                mifir_id=CmeMifirColumns.SELLER_DECISION_MAKER_ID,
                mifir_id_type=CmeMifirColumns.SELLER_DECISION_MAKER_CODE_TYPE,
                dob=CmeMifirColumns.SELLER_DECISION_MAKER_DATE_OF_BIRTH,
                meta_key=meta_key,
            ).apply(lambda x: [x.dropna().to_dict()], axis=1)
        return result

    def _fill_missing_firm_investment_decision_maker(
        self, df: pd.DataFrame, meta_key: str
    ) -> pd.Series:
        result = df[PartyField.INVESTMENT_DECISION_WITHIN_FIRM]
        mask = df[PartyField.INVESTMENT_DECISION_WITHIN_FIRM].isna() & df[
            CmeMifirColumns.INVESTMENT_DECISION_WITHIN_FIRM_TYPE
        ].isin(VALID_PARTY_ID_TYPES)
        if mask.any():
            result.loc[mask] = self._format_party_within_firm(
                df.loc[mask],
                name=CmeMifirColumns.INVESTMENT_DECISION_ID,
                mifir_id=CmeMifirColumns.INVESTMENT_DECISION_ID,
                mifir_id_type=CmeMifirColumns.INVESTMENT_DECISION_WITHIN_FIRM_TYPE,
                nationality=CmeMifirColumns.INVESTMENT_DECISION_COUNTRY_OF_BRANCH,
                meta_key=meta_key,
            ).apply(lambda x: x.dropna().to_dict(), axis=1)
        return result

    def _fill_missing_firm_executioner(
        self, df: pd.DataFrame, meta_key: str
    ) -> pd.Series:
        result = df[PartyField.EXECUTION_WITHIN_FIRM]
        mask = df[PartyField.EXECUTION_WITHIN_FIRM].isna() & df[
            CmeMifirColumns.EXECUTION_WITHIN_FIRM_TYPE
        ].isin(VALID_PARTY_ID_TYPES)
        if mask.any():
            result.loc[mask] = self._format_party_within_firm(
                df.loc[mask],
                name=CmeMifirColumns.FIRM_EXECUTION_ID,
                mifir_id=CmeMifirColumns.FIRM_EXECUTION_ID,
                mifir_id_type=CmeMifirColumns.EXECUTION_WITHIN_FIRM_TYPE,
                nationality=CmeMifirColumns.EXECUTING_ENTITY_COUNTRY_OF_BRANCH,
                meta_key=meta_key,
            ).apply(lambda x: x.dropna().to_dict(), axis=1)

        return result

    @staticmethod
    def _format_party(
        source: pd.DataFrame,
        first_name: str,
        last_name: str,
        mifir_id: str,
        mifir_id_type: str,
        dob: str,
        nationality: Optional[str] = None,
        meta_key: str = None,
    ) -> pd.DataFrame:
        """
        map columns from source df to fields for AccountPerson/ MarketPerson to be used in RTS22Transaction.party fields
        params after source are column names for the specific attribute
        """
        df = pd.DataFrame(index=source.index)
        df[PersonField.FIRST_NAME] = source[first_name]
        df[PersonField.LAST_NAME] = source[last_name]
        df[PersonField.NAME] = (
            source[first_name].fillna("") + " " + source[last_name].fillna("")
        )
        df[PersonField.NAME] = df[PersonField.NAME].replace(" ", pd.NA)

        df[PersonField.MIFIR_ID] = source[mifir_id]
        df[PersonField.MIFIR_ID_TYPE] = source[mifir_id_type].map(MIFIR_ID_TYPE_MAP)
        df[PersonField.MIFIR_ID_SUB_TYPE] = pd.NA
        df.loc[
            df[PersonField.MIFIR_ID_TYPE] == "N", PersonField.MIFIR_ID_SUB_TYPE
        ] = source[mifir_id_type]
        df[PersonField.DOB] = pd.to_datetime(source[dob].fillna(pd.NaT)).dt.strftime(
            "%Y-%m-%d"
        )
        if nationality:
            df[PersonField.BRANCH_COUNTRY] = source[nationality]

        df[meta_key] = df.apply(
            lambda x: PartiesFallback.build_hash_meta_key(
                row=x,
                name=PersonField.NAME,
                mifir_id=PersonField.MIFIR_ID,
                mifir_id_type=PersonField.MIFIR_ID_TYPE,
                mifir_id_sub_type=PersonField.MIFIR_ID_SUB_TYPE,
                branch_country=PersonField.BRANCH_COUNTRY,
                dob=PersonField.DOB,
                external_party=True,
            ),
            axis=1,
        )

        return df

    @staticmethod
    def _format_party_within_firm(
        source: pd.DataFrame,
        name: str,
        mifir_id: str,
        mifir_id_type: str,
        nationality: str,
        meta_key: str,
    ) -> pd.DataFrame:
        """
        map columns from source df to fields for AccountPerson/ MarketPerson to be used in RTS22Transaction.party fields
        params after source are column names for the specific attribute
        NOTE: this method returns a more spare representation of a person compared to _format_person()
        """
        df = pd.DataFrame(index=source.index, columns=[PersonField.UNIQUE_IDS])
        df[PersonField.MIFIR_ID] = source[mifir_id]
        df[PersonField.MIFIR_ID_TYPE] = source[mifir_id_type].map(MIFIR_ID_TYPE_MAP)
        df.loc[
            df[PersonField.MIFIR_ID_TYPE] == "N", PersonField.MIFIR_ID_SUB_TYPE
        ] = source[mifir_id_type]
        df[PersonField.BRANCH_COUNTRY] = source[nationality]
        df[PersonField.FIRST_NAME] = source[name]
        df[PersonField.NAME] = source[name]
        existing_mifir_id_mask = df[PersonField.MIFIR_ID].notna()
        df.loc[existing_mifir_id_mask, PersonField.UNIQUE_IDS] = (
            df.loc[existing_mifir_id_mask, PersonField.MIFIR_ID_TYPE].str.lower()
            + ":"
            + df.loc[existing_mifir_id_mask, PersonField.MIFIR_ID].str.lower()
        ).apply(lambda x: [x])

        df[meta_key] = df.apply(
            lambda x: PartiesFallback.build_hash_meta_key(
                row=x,
                name=PersonField.NAME,
                mifir_id=PersonField.MIFIR_ID,
                mifir_id_type=PersonField.MIFIR_ID_TYPE,
                mifir_id_sub_type=PersonField.MIFIR_ID_SUB_TYPE,
                branch_country=PersonField.BRANCH_COUNTRY,
                external_party=False,
            ),
            axis=1,
        )

        return df

    @classmethod
    def build_hash_meta_key(
        cls,
        row: pd.Series,
        name: str,
        mifir_id: str,
        mifir_id_type: str,
        mifir_id_sub_type: str,
        branch_country: str,
        dob: str = "",
        external_party: bool = True,
    ) -> str:
        """
        Create an MD5 hash key from a set of properties describing an external or internal party. This hash
        will be used to create a unique meta key to handle duplicate/quarantine trades.
        :param row: Pandas Series with fields for AccountPerson/MarketPerson to be used in RTS22Transaction.party fields
        :param name: PersonField.NAME
        :param mifir_id: PersonField.MIFIR_ID
        :param mifir_id_type: PersonField.MIFIR_ID_TYPE
        :param mifir_id_sub_type: PersonField.MIFIR_ID_SUB_TYPE
        :param branch_country: PersonField.BRANCH_COUNTRY
        :param dob: PersonField.DOB
        :param external_party: True for MarketPerson, False for AccountPerson
        :return: An MD5 hexdigested hash unique key from a set of properties obtained from input row
        """
        clean_row = row.fillna("")
        meta_key = "".join(
            [
                clean_row[name],
                clean_row[mifir_id],
                clean_row[mifir_id_type],
                clean_row[mifir_id_sub_type],
                clean_row[branch_country],
            ]
        )

        if external_party:
            party_meta_key = EXTERNAL_PARTY_META_KEY
            meta_key += clean_row[dob]
        else:
            party_meta_key = INTERNAL_PARTY_META_KEY

        return party_meta_key + hashlib.md5(meta_key.encode()).hexdigest()
