import pandas as pd
from se_elastic_schema.components.market.identifier import Identifier
from se_elastic_schema.static.market import IdentifierType
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.tr.data_source.cme.mifir.static import (
    CmeMifirColumns,
)


class RTS22TransactionModelField:
    TRX_DTL_BUY_SELL_INDICATOR = "transactionDetails.buySellIndicator"
    TRX_DTL_TRADING_CAPACITY = "transactionDetails.tradingCapacity"
    PARTIES_BUYER = "parties.buyer"
    PARTIES_BUYER_DECISION_MAKER = "parties.buyerDecisionMaker"
    PARTIES_COUNTERPARTY = "parties.counterparty"
    PARTIES_EXECUTING_ENTITY = "parties.executingEntity"
    PARTIES_EXECUTION_WITHIN_FIRM = "parties.executionWithinFirm"
    PARTIES_INVESTMENT_DECISION_WITHIN_FIRM = "parties.investmentDecisionWithinFirm"
    PARTIES_SELLER = "parties.seller"
    PARTIES_SELLER_DECISION_MAKER = "parties.sellerDecisionMaker"
    PARTIES_TRADER = "parties.trader"


TARGET_ATTRIBUTE = "marketIdentifiers.parties"


class PartyIdentifiers(TransformBaseTask):
    """
    This task should create all the party identifiers from GetBux data source
    by LinkParties to retrieve the documents to embed in the record.
    """

    def execute(
        self, source_frame: pd.DataFrame = None, params=None, resources=None, **kwargs
    ) -> pd.DataFrame:
        target = pd.DataFrame(index=source_frame.index, columns=[TARGET_ATTRIBUTE])
        if source_frame.empty:
            return target

        cols_used = [
            CmeMifirColumns.BUYER_ID,
            CmeMifirColumns.BUYER_DECISION_MAKER_ID,
            CmeMifirColumns.EXECUTING_ENTITY_ID,
            CmeMifirColumns.SELLER_ID,
            CmeMifirColumns.SELLER_DECISION_MAKER_ID,
            CmeMifirColumns.FIRM_EXEC,
            CmeMifirColumns.INV_DEC_ID,
            CmeMifirColumns.EXECUTION_WITHIN_FIRM_TYPE,
            CmeMifirColumns.INVESTMENT_DECISION_WITHIN_FIRM_TYPE,
            CmeMifirColumns.BUYER_IDENTIFICATION_CODE_TYPE,
            CmeMifirColumns.BUYER_DECISION_MAKER_CODE_TYPE,
            CmeMifirColumns.SELLER_IDENTIFICATION_CODE_TYPE,
            CmeMifirColumns.SELLER_DECISION_MAKER_CODE_TYPE,
        ]
        cols_mask = source_frame.columns.isin(cols_used)

        df = source_frame.loc[:, cols_mask]

        for col in cols_used:
            if col not in df.columns:
                df[col] = pd.NA

        identifiers_df: pd.DataFrame = pd.DataFrame(index=df.index)

        # buyer
        identifiers_df[
            RTS22TransactionModelField.PARTIES_BUYER
        ] = self._make_buyer_identifier(df=df)

        # buyerDecisionMaker
        identifiers_df[
            RTS22TransactionModelField.PARTIES_BUYER_DECISION_MAKER
        ] = self._make_buyer_decision_maker_identifier(df=df)

        # executingEntity
        identifiers_df[
            RTS22TransactionModelField.PARTIES_EXECUTING_ENTITY
        ] = self._make_exec_entity_identifier(df=df)

        # executionWithinFirm
        identifiers_df[
            RTS22TransactionModelField.PARTIES_EXECUTION_WITHIN_FIRM
        ] = self._make_exec_within_firm_identifier(df=df)

        # investmentDecisionWithinFirm
        identifiers_df[
            RTS22TransactionModelField.PARTIES_INVESTMENT_DECISION_WITHIN_FIRM
        ] = self._make_inv_dec_within_firm_identifier(df=df)

        # seller
        identifiers_df[
            RTS22TransactionModelField.PARTIES_SELLER
        ] = self._make_seller_identifier(df=df)

        # sellerDecisionMaker
        identifiers_df[
            RTS22TransactionModelField.PARTIES_SELLER_DECISION_MAKER
        ] = self._make_seller_decision_maker_identifier(df=df)

        cols_to_merge = [
            RTS22TransactionModelField.PARTIES_BUYER,
            RTS22TransactionModelField.PARTIES_BUYER_DECISION_MAKER,
            RTS22TransactionModelField.PARTIES_EXECUTING_ENTITY,
            RTS22TransactionModelField.PARTIES_EXECUTION_WITHIN_FIRM,
            RTS22TransactionModelField.PARTIES_INVESTMENT_DECISION_WITHIN_FIRM,
            RTS22TransactionModelField.PARTIES_SELLER,
            RTS22TransactionModelField.PARTIES_SELLER_DECISION_MAKER,
        ]
        target.loc[:, TARGET_ATTRIBUTE] = identifiers_df[cols_to_merge].apply(
            lambda x: x.dropna().tolist(), axis=1
        )

        return target

    @staticmethod
    def _make_buyer_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)
        buyer_data_mask = (
            df[CmeMifirColumns.BUYER_IDENTIFICATION_CODE_TYPE].notnull()
            & df[CmeMifirColumns.BUYER_ID].notnull()
        )
        if buyer_data_mask.any():
            # Add Prefix
            df.loc[buyer_data_mask, CmeMifirColumns.BUYER_ID] = (
                df[CmeMifirColumns.BUYER_IDENTIFICATION_CODE_TYPE]
                + ":"
                + df[CmeMifirColumns.BUYER_ID]
            )

            # Make identifier
            result.loc[buyer_data_mask] = df.loc[
                buyer_data_mask, CmeMifirColumns.BUYER_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=RTS22TransactionModelField.PARTIES_BUYER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )
        return result

    @staticmethod
    def _make_buyer_decision_maker_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)
        buyer_decision_maker_data_mask = (
            df[CmeMifirColumns.BUYER_DECISION_MAKER_CODE_TYPE].notnull()
            & df[CmeMifirColumns.BUYER_DECISION_MAKER_ID].notnull()
        )
        if buyer_decision_maker_data_mask.any():
            # Add Prefix
            df.loc[
                buyer_decision_maker_data_mask, CmeMifirColumns.BUYER_DECISION_MAKER_ID
            ] = (
                df[CmeMifirColumns.BUYER_DECISION_MAKER_CODE_TYPE]
                + ":"
                + df[CmeMifirColumns.BUYER_DECISION_MAKER_ID]
            )

            # Make identifier
            result.loc[buyer_decision_maker_data_mask] = df.loc[
                buyer_decision_maker_data_mask, CmeMifirColumns.BUYER_DECISION_MAKER_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=RTS22TransactionModelField.PARTIES_BUYER_DECISION_MAKER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )
        return result

    @staticmethod
    def _make_exec_entity_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)
        executing_entity_data_mask = df[CmeMifirColumns.EXECUTING_ENTITY_ID].notnull()
        if executing_entity_data_mask.any():
            # Add Prefix
            df.loc[executing_entity_data_mask, CmeMifirColumns.EXECUTING_ENTITY_ID] = (
                "lei:" + df[CmeMifirColumns.EXECUTING_ENTITY_ID]
            )

            # Make Identifier
            result.loc[executing_entity_data_mask] = df.loc[
                executing_entity_data_mask, CmeMifirColumns.EXECUTING_ENTITY_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=RTS22TransactionModelField.PARTIES_EXECUTING_ENTITY,
                    type=IdentifierType.OBJECT,
                ).dict()
            )
        return result

    @staticmethod
    def _make_exec_within_firm_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)
        firm_execution_data_mask = (
            df[CmeMifirColumns.EXECUTION_WITHIN_FIRM_TYPE].notnull()
            & df[CmeMifirColumns.FIRM_EXEC].notnull()
        )
        if firm_execution_data_mask.any():
            # Add Prefix
            df.loc[firm_execution_data_mask, CmeMifirColumns.FIRM_EXEC] = (
                df[CmeMifirColumns.EXECUTION_WITHIN_FIRM_TYPE]
                + ":"
                + df[CmeMifirColumns.FIRM_EXEC]
            )

            # Make identifier
            result.loc[firm_execution_data_mask] = df.loc[
                firm_execution_data_mask, CmeMifirColumns.FIRM_EXEC
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=RTS22TransactionModelField.PARTIES_EXECUTION_WITHIN_FIRM,
                    type=IdentifierType.OBJECT,
                ).dict()
            )
        return result

    @staticmethod
    def _make_inv_dec_within_firm_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)
        investment_decision_id_mask = (
            df[CmeMifirColumns.INVESTMENT_DECISION_WITHIN_FIRM_TYPE].notnull()
            & df[CmeMifirColumns.INV_DEC_ID].notnull()
        )
        if investment_decision_id_mask.any():
            # Add Prefix
            df.loc[investment_decision_id_mask, CmeMifirColumns.INV_DEC_ID] = (
                df[CmeMifirColumns.INVESTMENT_DECISION_WITHIN_FIRM_TYPE]
                + ":"
                + df[CmeMifirColumns.INV_DEC_ID]
            )

            # Make identifier
            result.loc[investment_decision_id_mask] = df.loc[
                investment_decision_id_mask, CmeMifirColumns.INV_DEC_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=RTS22TransactionModelField.PARTIES_INVESTMENT_DECISION_WITHIN_FIRM,
                    type=IdentifierType.OBJECT,
                ).dict()
            )
        return result

    @staticmethod
    def _make_seller_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)
        seller_data_mask = (
            df[CmeMifirColumns.SELLER_IDENTIFICATION_CODE_TYPE].notnull()
            & df[CmeMifirColumns.SELLER_ID].notnull()
        )
        if seller_data_mask.any():
            # Add Prefix
            df.loc[seller_data_mask, CmeMifirColumns.SELLER_ID] = (
                df[CmeMifirColumns.SELLER_IDENTIFICATION_CODE_TYPE]
                + ":"
                + df[CmeMifirColumns.SELLER_ID]
            )

            # Make identifier
            result.loc[seller_data_mask] = df.loc[
                seller_data_mask, CmeMifirColumns.SELLER_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=RTS22TransactionModelField.PARTIES_SELLER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )
        return result

    @staticmethod
    def _make_seller_decision_maker_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)
        # Add Prefix
        seller_decision_maker_data_mask = (
            df[CmeMifirColumns.SELLER_DECISION_MAKER_CODE_TYPE].notnull()
            & df[CmeMifirColumns.SELLER_DECISION_MAKER_ID].notnull()
        )
        if seller_decision_maker_data_mask.any():
            df.loc[
                seller_decision_maker_data_mask,
                CmeMifirColumns.SELLER_DECISION_MAKER_ID,
            ] = (
                df[CmeMifirColumns.SELLER_DECISION_MAKER_CODE_TYPE]
                + ":"
                + df[CmeMifirColumns.SELLER_DECISION_MAKER_ID]
            )

            # Make identifier
            result.loc[seller_decision_maker_data_mask] = df.loc[
                seller_decision_maker_data_mask,
                CmeMifirColumns.SELLER_DECISION_MAKER_ID,
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=RTS22TransactionModelField.PARTIES_SELLER_DECISION_MAKER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )
        return result
