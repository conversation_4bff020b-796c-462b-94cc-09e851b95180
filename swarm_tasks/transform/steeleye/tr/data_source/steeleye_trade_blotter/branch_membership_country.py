from typing import Optional

import pandas as pd
from pydantic import Field
from se_elastic_schema.validators.steeleye.venues.venue_validator import VenueValida<PERSON>
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.map.map_iso_3166_country_code import CountryCodeType
from swarm_tasks.transform.map.map_iso_3166_country_code import MapIso3166CountryCode
from swarm_tasks.transform.map.map_iso_3166_country_code import (
    Params as ParamsIsoCountryCode,
)


TRX_DTL_VENUE = "transactionDetails.venue"
TARGET_ATTRIBUTE = "transactionDetails.branchMembershipCountry"
EXECUTING_ENTITY_ATTRIBUTE = "parties.executingEntity"
BRANCH_COUNTRY_ATTRIBUTE = "firmIdentifiers.branchCountry"


class Params(BaseParams):
    branch_membership_country_column: Optional[str] = Field(
        default=None,
        description="If non null, looks for a column containing the branch membership"
        "country, and populates the value from there if the value is non-null.",
    )


class BranchMembershipCountry(TransformBaseTask):
    """
    This task gets the transactionDetails.branchMembershipCountry.

    Logic:
        if transactionDetails.venue .len() = 4
        AND [transactionDetails.venue] not in the SI-List (a list of MIC’s is saved below)
        AND [transactionDetails.venue] not in the NonEEA-List (a list of MIC’s is saved below)
        AND [transactionDetails.venue] NOT IN ("XXXX","XOFF") then:
            set the value of [transactionDetails.branchMembershipCountry] =
            [params.branch_membership_column] if present and not null
            [parties.executingEntity.firmIdentifiers.branchCountry] otherwise


    """

    params_class = Params

    def execute(
        self,
        source_frame: Optional[pd.DataFrame] = None,
        params: Optional[Params] = None,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index)
        target[TARGET_ATTRIBUTE] = pd.NA

        # Get the venue mask based on new conditions
        venue_mask = self._get_venue_mask(df=source_frame)

        # If params.branch_membership_country_column is present in the source frame,
        # convert it to an ISO Alpha 2 code and map it to the target. This needs
        # to be done only if venue_mask is True
        if (
            params
            and params.branch_membership_country_column
            and params.branch_membership_country_column in source_frame.columns
        ):
            target.loc[venue_mask, TARGET_ATTRIBUTE] = MapIso3166CountryCode.process(
                source_frame=source_frame.loc[venue_mask, :],
                params=ParamsIsoCountryCode(
                    source_country_column=params.branch_membership_country_column,
                    target_country_column=TARGET_ATTRIBUTE,
                    target_country_code_type=CountryCodeType.ALPHA_2,
                ),
                auditor=self.auditor,
            ).loc[venue_mask, TARGET_ATTRIBUTE]

        branch_membership_null_mask = target[TARGET_ATTRIBUTE].isnull()

        if not venue_mask.any() or not branch_membership_null_mask.any():
            return target

        # Retrieve branch membership country for filtered venues. This should only be
        # done if the branch membership country from params.branch_membership_column is null.
        target.loc[
            venue_mask & branch_membership_null_mask, TARGET_ATTRIBUTE
        ] = self._get_branch_membership_country(
            source_frame=source_frame.loc[venue_mask & branch_membership_null_mask]
        )

        # Replace None values with pd.NA
        target = target.fillna(value=pd.NA)

        return target

    @staticmethod
    def _get_venue_mask(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(False, index=df.index)

        populated_venue_mask = df[TRX_DTL_VENUE].notnull()

        if not populated_venue_mask.any():
            return result

        mask = (
            populated_venue_mask
            & (df.loc[populated_venue_mask, TRX_DTL_VENUE].str.len() == 4)
            & ~df.loc[populated_venue_mask, TRX_DTL_VENUE].map(
                VenueValidator.is_systematic_internaliser_venue
            )
            & ~df.loc[populated_venue_mask, TRX_DTL_VENUE].map(
                VenueValidator.is_non_eea_venue
            )
            & ~df.loc[populated_venue_mask, TRX_DTL_VENUE].isin(["XXXX", "XOFF"])
        )

        return mask

    def _get_branch_membership_country(
        self, source_frame: pd.DataFrame
    ) -> Optional[pd.Series]:

        if EXECUTING_ENTITY_ATTRIBUTE in source_frame:
            return source_frame[EXECUTING_ENTITY_ATTRIBUTE].str.get(
                BRANCH_COUNTRY_ATTRIBUTE
            )
        else:
            # If the attribute is not in the dataframe, return None
            return pd.Series([pd.NA] * len(source_frame), index=source_frame.index)
