from typing import Optional

import pandas as pd
import pendulum
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.steeleye.tr.static import RTS22Transaction
from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.static import (
    SteelEyeTradeBlotterColumns,
)

TARGET_ATTRIBUTE = "reportDetails.transactionRefNo"
TRANSACTION_ID = "__transaction_id__"


class Params(BaseParams):
    buy_sell_indicator_attribute: Optional[str] = Field(
        RTS22Transaction.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
        description="Field to look up buy sell indicator on on",
    )
    order_id_attribute: Optional[str] = Field(
        SteelEyeTradeBlotterColumns.ORDER_ID, description="Field to look up order id on"
    )
    trade_id_attribute: Optional[str] = Field(
        SteelEyeTradeBlotterColumns.TRADE_ID, description="Field to look up trade id on"
    )
    trade_time_attribute: Optional[str] = Field(
        SteelEyeTradeBlotterColumns.TRADE_DATE,
        description="Field to look up trade date on",
    )
    trading_date_time_attribute: Optional[str] = Field(
        RTS22Transaction.TRANSACTION_DETAILS_TRADING_DATE_TIME,
        description="Field to look up trading date time on",
    )


class TransactionRefNo(TransformBaseTask):
    """
    This task creates the transaction ref no.
    """

    params_class = Params

    def execute(
        self, source_frame: pd.DataFrame = None, params: Params = None, **kwargs
    ) -> pd.DataFrame:
        return self.process(source_frame=source_frame, params=params)

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Params = None,
    ):
        target = pd.DataFrame(index=source_frame.index)
        target[TARGET_ATTRIBUTE] = pd.NA

        data = source_frame[
            [params.trading_date_time_attribute, params.buy_sell_indicator_attribute]
        ]

        data[TRANSACTION_ID] = cls._make_transaction_id(df=source_frame, params=params)

        if not data[TRANSACTION_ID].any():
            return target

        data[params.trading_date_time_attribute] = cls._process_trading_date_time(
            df=source_frame, params=params
        )

        target[TARGET_ATTRIBUTE] = (
            data[
                [
                    TRANSACTION_ID,
                    params.trading_date_time_attribute,
                    params.buy_sell_indicator_attribute,
                ]
            ]
            .dropna(how="all")
            .apply(lambda x: "".join(x.dropna().tolist()), axis=1)
        )

        target[TARGET_ATTRIBUTE] = (
            target[TARGET_ATTRIBUTE]
            .dropna()
            .str.replace("[^a-zA-Z0-9]", "")
            .str[:52]
            .str.upper()
        )

        target[TARGET_ATTRIBUTE] = target[TARGET_ATTRIBUTE].fillna(pd.NA)

        return target

    @staticmethod
    def _make_transaction_id(df: pd.DataFrame, params: Params) -> pd.Series:

        result = pd.Series(pd.NA, index=df.index)

        # Create Order ID column with na values if non-existent
        if params.order_id_attribute not in df.columns:
            df[params.order_id_attribute] = pd.NA

        # With order id
        use_order_id_mask = df[params.order_id_attribute].notnull()

        if use_order_id_mask.any():
            result.loc[use_order_id_mask] = df.loc[
                use_order_id_mask,
                [params.order_id_attribute, params.trade_id_attribute],
            ].apply(lambda x: "|".join(x.dropna().tolist()), axis=1)

        # With trade id and without order id
        use_trade_id_mask = (
            df[params.order_id_attribute].isnull()
            & df[params.trade_id_attribute].notnull()
        )

        if not use_trade_id_mask.any():
            return result

        result.loc[use_trade_id_mask] = df.loc[
            use_trade_id_mask,
            [params.trade_id_attribute, params.trade_time_attribute],
        ].apply(lambda x: "|".join(x.dropna().tolist()), axis=1)

        # Slice to max length 52
        result = result.dropna().str[:52]

        return result

    @staticmethod
    def _process_trading_date_time(df: pd.DataFrame, params: Params) -> pd.Series:
        """
        Parse the timestamp into date and then converts it into
        string.

        NOTE: It uses `pendulum` to parse the datetime because
        `pd.to_datetime` yells when time is so far in future.

        :param df: Dataframe from which the column to process to be taken
        :param params: Task params
        :return: Series of converted dates
        """
        return (
            df[params.trading_date_time_attribute]
            .dropna()
            .apply(lambda x: pendulum.parse(x).date().isoformat())
        )
