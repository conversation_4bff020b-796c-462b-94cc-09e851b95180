import pandas as pd
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.static import (
    SteelEyeTradeBlotterColumns,
)

TARGET_ATTRIBUTE = "transactionDetails.outgoingOrderAddlInfo"


class OutgoingOrderAddlInfo(TransformBaseTask):
    """
    This task creates the outgoingOrderAddlInfo
    """

    # TODO: add to yaml
    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: BaseParams = None,
        resources: BaseResources = None,
    ) -> pd.DataFrame:

        target = pd.Series(index=source_frame.index, name=TARGET_ATTRIBUTE)

        df = source_frame[
            [
                SteelEyeTradeBlotterColumns.CLIENT_ID,
                SteelEyeTradeBlotterColumns.COUNTERPARTY_ID,
                SteelEyeTradeBlotterColumns.TRADER_ID,
                SteelEyeTradeBlotterColumns.SYMBOL,
            ]
        ]

        for col in df.columns:
            df[col] = df[col].dropna().apply(lambda x: f"{col} - {x}")

        target[TARGET_ATTRIBUTE] = df.dropna(how="all").apply(
            lambda x: ", ".join(x.dropna().tolist()), axis=1
        )

        return target[TARGET_ATTRIBUTE].to_frame()
