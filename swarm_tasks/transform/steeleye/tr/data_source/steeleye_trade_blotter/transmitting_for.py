import pandas as pd
from se_elastic_schema.static.mifid2 import BuySellIndicator
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.static import (
    SteelEyeTradeBlotterColumns,
)

TRX_DTL_BUY_SELL_INDICATOR = "transactionDetails.buySellIndicator"

BUY_TARGET_COLUMN = "parties.buyerTransmittingFirm.firmIdentifiers.lei"
SELL_TARGET_COLUMN = "parties.sellerTransmittingFirm.firmIdentifiers.lei"


class TransmittingFor(TransformBaseTask):
    """
    This task adds override for the firm lei when trade in on behalf of someone else
    """

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: BaseParams = None,
        resources: BaseResources = None,
        **kwargs
    ) -> pd.DataFrame:
        target = pd.DataFrame(index=source_frame.index)

        if SteelEyeTradeBlotterColumns.TRANSMITTING_FOR not in source_frame.columns:
            return target

        transmitting_for_mask = (
            source_frame[SteelEyeTradeBlotterColumns.TRANSMITTING_FOR].str.len() == 20
        )

        if not transmitting_for_mask.any():
            return target

        buy_override_mask = (
            source_frame[TRX_DTL_BUY_SELL_INDICATOR] == BuySellIndicator.BUYI
        ) & transmitting_for_mask

        sell_override_mask = (
            source_frame[TRX_DTL_BUY_SELL_INDICATOR] == BuySellIndicator.SELL
        ) & transmitting_for_mask

        if buy_override_mask.any():
            target[BUY_TARGET_COLUMN] = source_frame.loc[
                buy_override_mask, SteelEyeTradeBlotterColumns.TRANSMITTING_FOR
            ]

        if sell_override_mask.any():
            target[SELL_TARGET_COLUMN] = source_frame.loc[
                sell_override_mask, SteelEyeTradeBlotterColumns.TRANSMITTING_FOR
            ]

        return target
