from enum import Enum


class SteelEyeTradeBlotterColumns:
    """
    Columns present in the csv/excel SteelEye Trade Blotter template
    """

    AVERAGE_PRICE = "AVERAGEPRICE"
    BBG_FIGI = "BLOOMBERGFIGIID"
    BUY_SELL = "BUY/SELL"
    CLIENT_ID = "CLIENTID"
    COUNTERPARTY_ID = "COUNTERPARTYID"
    COUNTRY_OF_THE_BRANCH_MEMBERSHIP = "COUNTRYOFTHEBRANCHMEMBERSHIP"
    CUSIP = "CUSIP"
    EXCHANGE_MIC = "EXCHANGEMIC"
    EXECUTING_ENTITY_ID = "EXECUTINGENTITYID"
    EXECUTION_WITHIN_FIRM = "EXECUTIONWITHINFIRM"
    EXPIRY_DATE_MATURITY_DATE = "EXPIRYDATE/MATURITYDATE(YYYYMMDD)"
    INSTRUMENT_ASSET_CLASS = "INSTRUMENTASSETCLASS"
    INSTRUMENT_CLASSIFICATION = "INSTRUMENTCLASSIFICATION"
    INVESTMENT_DECISION_MAKER = "INVESTMENTDECISIONMAKER"
    INSTRUMENT_NAME = "INSTRUMENTNAME"
    ISIN = "ISIN"
    IS_CFD = "ISCFD?"
    IS_SPREAD_BET = "ISSPREADBET?"
    LIMIT_PRICE = "LIMITPRICE"
    NET_AMOUNT = "NETAMOUNT"
    NOTIONAL_CURRENCY_2 = "NOTIONALCURRENCY2"
    OPTION_STRIKE_PRICE = "OPTIONSTRIKEPRICE"
    OPTION_STYLE = "OPTIONSTYLE"
    OPTION_TYPE = "OPTIONTYPE"
    ORDER_ID = "ORDERID"
    ORDER_DATE = "ORDERDATE(YYYYMMDD)"
    ORDER_TIME = "ORDERTIME(HH:MM:SS)"
    POSITION_EFFECT = "POSITIONEFFECT"
    PRICE = "PRICE"
    PRICE_CURRENCY = "PRICECURRENCY"
    PRICE_MULTIPLIER = "PRICEMULTIPLIER"
    PRICE_NOTATION = "PRICENOTATION"
    QUANTITY_CURRENCY = "QUANTITYCURRENCY"
    QUANTITY_NOTATION = "QUANTITYNOTATION"
    SETTLE_AMOUNT = "SETTLEAMOUNT"
    SETTLE_DATE = "SETTLEDATE"
    STOP_PRICE = "STOPPRICE"
    SYMBOL = "SYMBOL"
    TIME_IN_FORCE = "TIMEINFORCEFORANORDER"
    TRADE_DATE = "TRADEDATE(YYYYMMDD)"
    TRADE_ID = "TRADEID"
    TRADE_TIME = "TRADETIME(HH:MM:SS)"
    TRADED_QUANTITY = "TRADEDQUANTITY"
    TRADER_ID = "TRADERID"
    TRADING_CAPACITY = "TRADINGCAPACITY"
    TRANSMITTING_FOR = "TRANSMITTINGFOR"
    ULTIMATE_VENUE = "ULTIMATEVENUE"
    UNDERLYING_INDEX_NAME = "UNDERLYINGINDEXNAME"
    UNDERLYING_INDEX_SERIES = "UNDERLYINGINDEXSERIES"
    UNDERLYING_INDEX_TERM = "UNDERLYINGINDEXTERM"
    UNDERLYING_INDEX_VERSION = "UNDERLYINGINDEXVERSION"
    UNDERLYING_INSTRUMENT_SYMBOL = "UNDERLYINGINSTRUMENTSYMBOL/S"
    UNDERLYING_ISIN = "UNDERLYINGINSTRUMENTISIN/S"
    UPFRONT_PAYMENT = "UP-FRONTPAYMENT"
    UPFRONT_PAYMENT_CURRENCY = "UP-FRONTPAYMENTCURRENCY"


class CreditDefaultSwaps(str, Enum):
    CREDIT_DEFAULT_SWAPS_SINGLE_STOCK = "CREDIT DEFAULT SWAPS - SINGLE STOCK"
    CDS_SINGLE_STOCK = "CDS - SINGLE STOCK"
    CREDIT_DEFAULT_SWAP = "CREDIT DEFAULT SWAP"
    CDS_INDEX_SWAP = "CDS INDEX SWAP"
    CDS_INDEX = "CDS INDEX"
    CDX = "CDX"


class FxOptions(str, Enum):
    FX_OPTION = "FX OPTION"
    FX_OPTION_BARRIER = "FX OPTION BARRIER"
    FX_OPTION_BARRIER_DIGITAL = "FX OPTION BARRIER DIGITAL"
    FX_OPTION_LOOKBACK = "FX OPTION LOOKBACK"
    FX_OPTION_OTHER = "FX OPTION OTHER"
