from typing import Optional

import pandas as pd
from pydantic import Field
from se_core_tasks.currency.convert_minor_to_major import ConvertMinorToMajor
from se_elastic_schema.static.mifid2 import RTS22TransactionStatus
from se_elastic_schema.static.reference import InstrumentIdCodeType
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.orders.common_utils import static
from swarm_tasks.transform.steeleye.tr.data_source.bain.brs.static import BainBRSColumns

INSTRUMENT_PATH = "instrumentDetails.instrument"
WORKFLOW_ELIGIBILITY_PATH = "workflow.eligibility"
IS_CREATED_THROUGH_FALLBACK = "isCreatedThroughFallback"


class BondRegex:
    BOND = "^(D[A-Z]{5})$"


class InstrumentFields:
    # Default Instrument Classification to use when this field is not populated in input data
    DEFAULT_INSTRUMENT_CLASSIFICATION = "DCXXXX"

    # Derivative
    DERIVATIVE_DELIVERY_TYPE = "derivative.deliveryType"
    DERIVATIVE_EXPIRY_DATE = "derivative.expiryDate"
    DERIVATIVE_STRIKE_PRICE = "derivative.strikePrice"
    DERIVATIVE_STRIKE_PRICE_CURRENCY = "derivative.strikePriceCurrency"
    DERIVATIVE_PRICE_MULTIPLIER = "derivative.priceMultiplier"
    DERIVATIVE_UNDERLYING_INSTRUMENTS = "derivative.underlyingInstruments"
    DERIVATIVE_UNDERLYING_INDEX_NAME = "derivative.underlyingIndexName"
    DERIVATIVE_UNDERLYING_INDEX_TERM = "derivative.underlyingIndexTerm"

    # Ext
    EXT_STRIKE_PRICE_TYPE = "ext.strikePriceType"
    EXT_ON_FIRDS = "ext.onFIRDS"
    EXT_PRICE_NOTATION = "ext.priceNotation"
    EXT_QUANTITY_NOTATION = "ext.quantityNotation"
    EXT_UNDERLYING_INSTRUMENTS = "ext.underlyingInstruments"
    EXT_INSTRUMENT_ID_TYPE = "ext.instrumentIdCodeType"

    # non-nested fields
    INSTRUMENT_CLASSIFICATION = "instrumentClassification"
    INSTRUMENT_FULL_NAME = "instrumentFullName"
    INSTRUMENT_ID_CODE = "instrumentIdCode"
    UNDERLYING_INSTRUMENT_ID = "underlyingInstrumentCode"
    NOTIONAL_CURRENCY_1 = "notionalCurrency1"

    # Bond Maturity Date
    MATURITY_DATE = "bond.maturityDate"

    # underlying fields
    UNDERLYING_INSTRUMENT_ID_CODE = "underlyingInstrumentCode"
    UNDERLYING_INSTRUMENT_CLASSIFICATION = "underlyingInstrumentClassification"


class WorkflowFields:
    STATUS = "workflow.status"


class WorkflowEligibilityFields:
    ELIGIBILITY_ELIGIBLE = "eligible"
    ELIGIBILITY_ON_FIRDS = "onFirds"
    ELIGIBILITY_UNDERLYING_ON_FIRDS = "underlyingOnFirds"
    ELIGIBILITY_TOTV = "totv"
    ELIGIBILITY_UTOTV = "utotv"


class Params(BaseParams):
    price_notation_map: dict = Field(
        ..., description="Used to map the incoming Price Notation data."
    )
    quantity_notation_map: dict = Field(
        ..., description="Used to map the incoming Quantity Notation data."
    )


class TempFields:
    TEMP_INSTRUMENT_ID_TYPE = "temp_instrument_id_type"


class InstrumentFallback(TransformBaseTask):
    """Instrument details and workflow details are populated here for the instruments
    that are not found/linked on SRP."""

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: Optional[BaseResources] = None,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index, columns=[INSTRUMENT_PATH])
        if source_frame.empty:
            return target

        cols_used = [
            BainBRSColumns.DELIVERY_TYPE,
            BainBRSColumns.EXPIRY_DATE,
            BainBRSColumns.INSTRUMENT_CLASSIFICATION,
            BainBRSColumns.INSTRUMENT_ID,
            BainBRSColumns.INSTRUMENT_NAME,
            BainBRSColumns.MATURITY_DATE,
            BainBRSColumns.NOTIONAL_CURRENCY_1,
            BainBRSColumns.PRICE_MULTIPLIER,
            BainBRSColumns.PRICE_TYPE,
            BainBRSColumns.QUANTITY_TYPE,
            BainBRSColumns.STRIKE_PRICE,
            BainBRSColumns.STRIKE_PRICE_CURRENCY,
            BainBRSColumns.STRIKE_PRICE_TYPE,
            BainBRSColumns.UNDERLYING_INDEX_ID,
            BainBRSColumns.UNDERLYING_INDEX_NAME,
            BainBRSColumns.UNDERLYING_INDEX_TERM,
            BainBRSColumns.UNDERLYING_INSTRUMENT_ID,
            INSTRUMENT_PATH,
        ]
        df = source_frame.loc[:, source_frame.columns.intersection(cols_used)]

        for col in cols_used:
            if col not in df.columns:
                df[col] = pd.NA

        target = source_frame.loc[
            :,
            (
                (source_frame.columns.str.startswith("workflow"))
                | (source_frame.columns == INSTRUMENT_PATH)
            ),
        ]

        # Run fallback where there is no instrument
        without_instrument_mask = df[INSTRUMENT_PATH].isnull()

        if not without_instrument_mask.any():
            return target

        data = df.loc[without_instrument_mask]
        data = self._process_data(df=data, params=params)

        # Synthetic instrument
        synthetic_instruments = self._get_synthetic_instruments(df=data)

        # Synthetic workflow
        synthetic_workflow = self._get_synthetic_workflow(df=data)

        target.loc[
            without_instrument_mask, WORKFLOW_ELIGIBILITY_PATH
        ] = synthetic_workflow

        # Workflow status
        target.loc[
            without_instrument_mask, WorkflowFields.STATUS
        ] = RTS22TransactionStatus.REPORTABLE_USER_OVERRIDE

        target.loc[without_instrument_mask, INSTRUMENT_PATH] = synthetic_instruments

        return target

    @staticmethod
    def _process_data(df: pd.DataFrame, params: Params) -> pd.DataFrame:

        # Currency Conversion
        # read the static json file with minor currencies and conversion map
        minor_ccy_price_data = ConvertMinorToMajor.read_minor_ccy_and_price(
            static.MINOR_CURRENCIES_FILE_PATH
        )
        ccy_not_null_mask = df[BainBRSColumns.NOTIONAL_CURRENCY_1].notnull()
        if ccy_not_null_mask.any():
            df.loc[ccy_not_null_mask, BainBRSColumns.NOTIONAL_CURRENCY_1] = df.loc[
                ccy_not_null_mask, BainBRSColumns.NOTIONAL_CURRENCY_1
            ].apply(
                (
                    lambda x: ConvertMinorToMajor.convert_currency(
                        source_ccy=x, conversion_map_list=minor_ccy_price_data
                    )
                )
            )

        # Upper
        for col in [BainBRSColumns.PRICE_TYPE, BainBRSColumns.QUANTITY_TYPE]:
            data = df[col].dropna()

            if data.empty:
                continue

            df.loc[:, col] = df[col].str.upper()

        # Price Notation
        with_price_notation_mask = df[BainBRSColumns.PRICE_TYPE].notnull()

        df.loc[with_price_notation_mask, BainBRSColumns.PRICE_TYPE] = df.loc[
            with_price_notation_mask, BainBRSColumns.PRICE_TYPE
        ].map(params.price_notation_map)

        # Quantity Notation
        with_qty_notation_mask = df[BainBRSColumns.QUANTITY_TYPE].notnull()

        df.loc[with_qty_notation_mask, BainBRSColumns.QUANTITY_TYPE] = df.loc[
            with_qty_notation_mask, BainBRSColumns.QUANTITY_TYPE
        ].map(params.quantity_notation_map)

        # InstrumentIdCodeType
        with_instrument_id_mask = df[BainBRSColumns.INSTRUMENT_ID].notnull()
        df.loc[
            with_instrument_id_mask, TempFields.TEMP_INSTRUMENT_ID_TYPE
        ] = InstrumentIdCodeType.ID.value

        # Instrument Classification as per ON-1934
        with_instrument_classification_mask = df[
            BainBRSColumns.INSTRUMENT_CLASSIFICATION
        ].notnull()
        df.loc[
            ~with_instrument_classification_mask,
            BainBRSColumns.INSTRUMENT_CLASSIFICATION,
        ] = InstrumentFields.DEFAULT_INSTRUMENT_CLASSIFICATION

        # Only bonds should have the maturity date populated
        is_bond_mask = df[BainBRSColumns.INSTRUMENT_CLASSIFICATION].str.match(
            BondRegex.BOND
        )
        df.loc[~is_bond_mask, BainBRSColumns.MATURITY_DATE] = pd.NA

        return df

    def _get_synthetic_instruments(self, df: pd.DataFrame) -> pd.Series:
        synthetic_instruments = pd.DataFrame(index=df.index)

        # straight mapping from source file fields
        straight_map = [
            (BainBRSColumns.DELIVERY_TYPE, InstrumentFields.DERIVATIVE_DELIVERY_TYPE),
            (BainBRSColumns.EXPIRY_DATE, InstrumentFields.DERIVATIVE_EXPIRY_DATE),
            (
                BainBRSColumns.INSTRUMENT_CLASSIFICATION,
                InstrumentFields.INSTRUMENT_CLASSIFICATION,
            ),
            (BainBRSColumns.INSTRUMENT_ID, InstrumentFields.INSTRUMENT_ID_CODE),
            (BainBRSColumns.INSTRUMENT_NAME, InstrumentFields.INSTRUMENT_FULL_NAME),
            (BainBRSColumns.NOTIONAL_CURRENCY_1, InstrumentFields.NOTIONAL_CURRENCY_1),
            (
                BainBRSColumns.PRICE_MULTIPLIER,
                InstrumentFields.DERIVATIVE_PRICE_MULTIPLIER,
            ),
            (BainBRSColumns.PRICE_TYPE, InstrumentFields.EXT_PRICE_NOTATION),
            (BainBRSColumns.QUANTITY_TYPE, InstrumentFields.EXT_QUANTITY_NOTATION),
            (BainBRSColumns.STRIKE_PRICE, InstrumentFields.DERIVATIVE_STRIKE_PRICE),
            (BainBRSColumns.MATURITY_DATE, InstrumentFields.MATURITY_DATE),
            (
                BainBRSColumns.NOTIONAL_CURRENCY_1,
                InstrumentFields.DERIVATIVE_STRIKE_PRICE_CURRENCY,
            ),
            (BainBRSColumns.STRIKE_PRICE_TYPE, InstrumentFields.EXT_STRIKE_PRICE_TYPE),
            (
                TempFields.TEMP_INSTRUMENT_ID_TYPE,
                InstrumentFields.EXT_INSTRUMENT_ID_TYPE,
            ),
        ]

        for entry in straight_map:
            synthetic_instruments[entry[1]] = df[entry[0]]

        # Set isCreatedThroughFallback = True
        synthetic_instruments[IS_CREATED_THROUGH_FALLBACK] = True

        # underlying instrument data
        underlying_instrument_data = self._get_underlying_instrument_data(df=df)

        # EXT_ON_FIRDS
        synthetic_instruments[InstrumentFields.EXT_ON_FIRDS] = False

        if not underlying_instrument_data.empty:
            synthetic_instruments = pd.concat(
                [synthetic_instruments, underlying_instrument_data], axis=1
            )

        inst_dtl_inst = synthetic_instruments.loc[:, :].apply(
            lambda x: x.dropna().to_dict(), axis=1
        )

        return inst_dtl_inst

    def _get_underlying_instrument_data(self, df: pd.DataFrame) -> pd.DataFrame:

        data = pd.DataFrame(index=df.index)
        data[InstrumentFields.EXT_UNDERLYING_INSTRUMENTS] = [
            [] for i in range(df.shape[0])
        ]
        data[InstrumentFields.DERIVATIVE_UNDERLYING_INSTRUMENTS] = [
            [] for j in range(df.shape[0])
        ]

        # Underlying Index Name
        self.assign_underlying_field(
            df=df,
            data=data,
            source_field=BainBRSColumns.UNDERLYING_INDEX_NAME,
            target_field=InstrumentFields.DERIVATIVE_UNDERLYING_INDEX_NAME,
            stage_field="__underlying_index_name",
        )

        # Underlying Index Term
        self.assign_underlying_field(
            df=df,
            data=data,
            source_field=BainBRSColumns.UNDERLYING_INDEX_TERM,
            target_field=InstrumentFields.DERIVATIVE_UNDERLYING_INDEX_TERM,
            stage_field="__underlying_index_term",
        )

        # Instrument ID
        self.assign_underlying_field(
            df=df,
            data=data,
            source_field=BainBRSColumns.UNDERLYING_INSTRUMENT_ID,
            target_field=InstrumentFields.INSTRUMENT_ID_CODE,
            stage_field="__instrument_id",
        )

        # Underlying Instrument ID
        self.assign_underlying_field(
            df=df,
            data=data,
            source_field=BainBRSColumns.UNDERLYING_INSTRUMENT_ID,
            target_field=InstrumentFields.UNDERLYING_INSTRUMENT_ID,
            stage_field="__underlying_instrument_id",
        )

        data = data.loc[:, ~data.columns.str.startswith("__")]

        return data

    @staticmethod
    def _get_synthetic_workflow(df: pd.DataFrame) -> pd.Series:

        synthetic_workflow = pd.DataFrame(index=df.index)

        # Eligibility Eligible
        synthetic_workflow[WorkflowEligibilityFields.ELIGIBILITY_ELIGIBLE] = True

        # Eligibility on FIRDS
        synthetic_workflow[WorkflowEligibilityFields.ELIGIBILITY_ON_FIRDS] = False

        # Eligibility TOTV
        # Set TOTV to True if an ISIN exists in the input file, False otherwise. See
        # https://steeleye.atlassian.net/browse/EU-1538?focusedCommentId=68605
        isin_not_null_mask = df.loc[:, BainBRSColumns.INSTRUMENT_ID].notnull()
        synthetic_workflow.loc[
            isin_not_null_mask, WorkflowEligibilityFields.ELIGIBILITY_TOTV
        ] = True
        synthetic_workflow.loc[
            ~isin_not_null_mask, WorkflowEligibilityFields.ELIGIBILITY_TOTV
        ] = False

        # Eligibility UTOTV
        synthetic_workflow[WorkflowEligibilityFields.ELIGIBILITY_UTOTV] = True

        # Eligibility underlying on FIRDS
        synthetic_workflow[
            WorkflowEligibilityFields.ELIGIBILITY_UNDERLYING_ON_FIRDS
        ] = True

        workflow_eligibility = synthetic_workflow.loc[:, :].apply(
            lambda x: x.dropna().to_dict(), axis=1
        )

        return workflow_eligibility

    def assign_underlying_field(
        self,
        df: pd.DataFrame,
        data: pd.DataFrame,
        source_field: str,
        target_field: str,
        stage_field: str,
    ):
        """the source field is to be assigned to two locations:
        1. target_field
        2. either instrument.ext.underlyingInstruments[0].<target_field> or
        instrument.derivative.underlyingInstruments[0].<target_field> depending on the
        source field."""

        stage_field_data = df[source_field]

        not_null_stage_field_mask = stage_field_data.notnull()

        if not not_null_stage_field_mask.any():
            return

        # Split source field by ;
        data[stage_field] = stage_field_data.loc[not_null_stage_field_mask].apply(
            lambda x: list(map(str.strip, x.split(";")))
        )

        # Only one value
        one_mask = not_null_stage_field_mask & data.loc[
            not_null_stage_field_mask, stage_field
        ].map(lambda x: len(x) == 1)

        if one_mask.any():
            data.loc[one_mask, :].apply(
                lambda x: [
                    self.allocate_underlying_instrument(
                        data=data,
                        index=x.name,
                        under_inst_index=i,
                        field=target_field,
                        value=y,
                    )
                    for i, y in enumerate(x[stage_field])
                ],
                axis=1,
            )
            if target_field not in [
                InstrumentFields.INSTRUMENT_ID_CODE,
                InstrumentFields.UNDERLYING_INSTRUMENT_ID,
            ]:
                data.loc[one_mask, target_field] = data.loc[
                    one_mask, stage_field
                ].apply(lambda x: x[0])

        # > 1 value
        more_than_one_mask = not_null_stage_field_mask & data.loc[
            not_null_stage_field_mask, stage_field
        ].map(lambda x: len(x) > 1)

        if more_than_one_mask.any():
            data.loc[more_than_one_mask, :].apply(
                lambda x: [
                    self.allocate_underlying_instrument(
                        data=data,
                        index=x.name,
                        under_inst_index=i,
                        field=target_field,
                        value=y,
                    )
                    for i, y in enumerate(x[stage_field])
                ],
                axis=1,
            )

    @staticmethod
    def allocate_underlying_instrument(
        data: pd.DataFrame, index: int, under_inst_index: int, field: str, value: str
    ):
        """allocates underlying instrument fields under either
        EXT_UNDERLYING_INSTRUMENTS or DERIVATIVE_UNDERLYING_INSTRUMENTS depending on
        the source field."""
        if field != InstrumentFields.UNDERLYING_INSTRUMENT_ID:
            if (
                len(data.loc[index, InstrumentFields.EXT_UNDERLYING_INSTRUMENTS])
                == under_inst_index
            ):
                data.loc[index, InstrumentFields.EXT_UNDERLYING_INSTRUMENTS].append({})

            data.loc[index, InstrumentFields.EXT_UNDERLYING_INSTRUMENTS][
                under_inst_index
            ][field] = value

        if field == InstrumentFields.UNDERLYING_INSTRUMENT_ID:
            if (
                len(data.loc[index, InstrumentFields.DERIVATIVE_UNDERLYING_INSTRUMENTS])
                == under_inst_index
            ):
                data.loc[
                    index, InstrumentFields.DERIVATIVE_UNDERLYING_INSTRUMENTS
                ].append({})

            data.loc[index, InstrumentFields.DERIVATIVE_UNDERLYING_INSTRUMENTS][
                under_inst_index
            ][field] = value
