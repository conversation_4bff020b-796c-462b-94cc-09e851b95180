import pandas as pd
from se_elastic_schema.static.reference import OrderRecordType
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.tr.data_source.schroders_brs.static import (
    SchrodersBRSColumns,
)

TARGET_ATTRIBUTE = "transactionDetails.recordType"


class RecordType(TransformBaseTask):
    """
    'If ([Buyer Id]='INTC' and [Seller Decision Maker Id].len()="20") or
        ([Seller Id]='INTC' and [Buyer Decision Maker Id].len()="20") then:
            "Client Side"
    elif ([Buyer Id]='INTC' or [Seller Id]='INTC') AND [Record Type] == "F" then:
        "Market Side"
    """

    def execute(
        self, source_frame: pd.DataFrame = None, params=None, resources=None, **kwargs
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index)

        target[TARGET_ATTRIBUTE] = self._get_record_type(df=source_frame)

        return target

    @staticmethod
    def _get_record_type(df: pd.DataFrame) -> pd.Series:
        target = pd.Series(pd.NA, index=df.index)

        data = df.loc[
            :,
            [
                SchrodersBRSColumns.BUYER_ID,
                SchrodersBRSColumns.BUYER_DECISION_MAKER_ID,
                SchrodersBRSColumns.SELLER_ID,
                SchrodersBRSColumns.SELLER_DECISION_MAKER_ID,
                SchrodersBRSColumns.RECORD_TYPE,
            ],
        ]

        for col in [SchrodersBRSColumns.BUYER_ID, SchrodersBRSColumns.SELLER_ID]:
            data[col] = data[col].dropna().str.upper()

        # Client side
        client_side_mask = (
            (data[SchrodersBRSColumns.BUYER_ID] == "INTC")
            & (data[SchrodersBRSColumns.SELLER_DECISION_MAKER_ID].str.len() == 20)
        ) | (
            (data[SchrodersBRSColumns.SELLER_ID] == "INTC")
            & (data[SchrodersBRSColumns.BUYER_DECISION_MAKER_ID].str.len() == 20)
        )

        target.loc[client_side_mask] = OrderRecordType.CLIENT_SIDE.value

        # Market side
        market_side_mask = (
            (data[SchrodersBRSColumns.BUYER_ID] == "INTC")
            | (data[SchrodersBRSColumns.SELLER_ID] == "INTC")
        ) & (data[SchrodersBRSColumns.RECORD_TYPE] == "F")

        target.loc[market_side_mask] = OrderRecordType.MARKET_SIDE.value

        return target
