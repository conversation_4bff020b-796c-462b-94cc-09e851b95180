from typing import Optional

import pandas as pd
from pydantic import Field
from se_elastic_schema.components.market.identifier import Identifier
from se_elastic_schema.static.market import IdentifierType
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.validators.iso.lei import LEI
from se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.blotter_party_identifiers import (
    Identifiers,
)
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.tr.data_source.schroders_brs.static import (
    SchrodersBRSColumns,
)


class RTS22TransactionModelField:
    TRX_DTL_BUY_SELL_INDICATOR = "transactionDetails.buySellIndicator"
    TRX_DTL_TRADING_CAPACITY = "transactionDetails.tradingCapacity"
    PARTIES_BUYER = "parties.buyer"
    PARTIES_BUYER_DECISION_MAKER = "parties.buyerDecisionMaker"
    PARTIES_COUNTERPARTY = "parties.counterparty"
    PARTIES_EXECUTING_ENTITY = "parties.executingEntity"
    PARTIES_EXECUTION_WITHIN_FIRM = "parties.executionWithinFirm"
    PARTIES_INVESTMENT_DECISION_WITHIN_FIRM = "parties.investmentDecisionWithinFirm"
    PARTIES_SELLER = "parties.seller"
    PARTIES_SELLER_DECISION_MAKER = "parties.sellerDecisionMaker"
    PARTIES_TRADER = "parties.trader"


TARGET_ATTRIBUTE = "marketIdentifiers.parties"
LEI_INTERNAL_PORTFOLIO = "GB96416"


class TempColumns:
    BUYER = "BUYER"
    BUYER_ID = "BUYER ID"
    BUYER_ID_SRC = "BUYER ID SRC"
    BUYER_LABEL = "BUYER LABEL"
    SELLER = "SELLER"
    SELLER_ID = "SELLER ID"
    SELLER_ID_SRC = "SELLER ID SRC"
    SELLER_LABEL = "SELLER LABEL"


class PartyLables:
    LEI = "lei:"
    ID = "id:"


class Params(BaseParams):
    lei_value: str = Field(..., description="Tenant LEI")


class PartyIdentifiers(TransformBaseTask):
    """
    This task should create all the party identifiers from Schroders BRS data source
    by LinkParties to retrieve the documents to embed in the record.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        **kwargs,
    ) -> pd.DataFrame:

        cols_used = [
            SchrodersBRSColumns.BUYER_ID,
            SchrodersBRSColumns.BUYER_DECISION_MAKER_ID,
            SchrodersBRSColumns.EXECUTING_ENTITY_ID,
            SchrodersBRSColumns.SELLER_ID,
            SchrodersBRSColumns.SELLER_DECISION_MAKER_ID,
            SchrodersBRSColumns.FIRM_EXECUTION_ID,
            SchrodersBRSColumns.INVESTMENT_DECISION_ID,
            RTS22TransactionModelField.TRX_DTL_BUY_SELL_INDICATOR,
            SchrodersBRSColumns.INTERNAL_PORTFOLIO_CODE,
        ]
        cols_mask = source_frame.columns.isin(cols_used)
        df = source_frame.loc[:, cols_mask]

        for col in cols_used:
            if col not in df.columns:
                df[col] = pd.NA

        df = self._process_data(df=df)

        identifiers_df: pd.DataFrame = pd.DataFrame(index=df.index)

        # buyer
        identifiers_df[
            RTS22TransactionModelField.PARTIES_BUYER
        ] = self._make_buyer_identifier(df=df, params=params)

        # buyer decision maker
        identifiers_df[
            RTS22TransactionModelField.PARTIES_BUYER_DECISION_MAKER
        ] = self._make_buyer_dec_maker_identifier(df=df)

        # counterparty
        identifiers_df[
            RTS22TransactionModelField.PARTIES_COUNTERPARTY
        ] = self._make_counterparty_identifier(df=df)

        # executing entity
        identifiers_df[
            RTS22TransactionModelField.PARTIES_EXECUTING_ENTITY
        ] = self._make_exec_entity_identifier(df=df)

        # execution within firm
        identifiers_df[
            RTS22TransactionModelField.PARTIES_EXECUTION_WITHIN_FIRM
        ] = self._make_exec_within_firm_identifier(df=df)

        # investment decision within firm
        identifiers_df[
            RTS22TransactionModelField.PARTIES_INVESTMENT_DECISION_WITHIN_FIRM
        ] = self._make_invest_dec_within_firm_identifier(df=df)

        # seller
        identifiers_df[
            RTS22TransactionModelField.PARTIES_SELLER
        ] = self._make_seller_identifier(df=df, params=params)

        # seller decision maker
        identifiers_df[
            RTS22TransactionModelField.PARTIES_SELLER_DECISION_MAKER
        ] = self._make_seller_dec_maker_identifier(df=df)

        # trader
        identifiers_df[
            RTS22TransactionModelField.PARTIES_TRADER
        ] = self._make_trader_identifier(df=df)

        identifiers_df.loc[:, TARGET_ATTRIBUTE] = identifiers_df.loc[:, :].apply(
            lambda x: x.dropna().tolist(), axis=1
        )

        empty_list_mask = identifiers_df[TARGET_ATTRIBUTE].apply(len) == 0

        if empty_list_mask.any():
            identifiers_df[TARGET_ATTRIBUTE].loc[empty_list_mask] = pd.NA

        return identifiers_df[TARGET_ATTRIBUTE].to_frame()

    def _process_data(self, df: pd.DataFrame) -> pd.DataFrame:
        df = self.__process_country_codes(df=df)
        df[TempColumns.BUYER_ID_SRC] = df[SchrodersBRSColumns.BUYER_ID]
        df[TempColumns.SELLER_ID_SRC] = df[SchrodersBRSColumns.SELLER_ID]
        # Set prefix
        lei_cols = [
            SchrodersBRSColumns.BUYER_ID,
            SchrodersBRSColumns.BUYER_DECISION_MAKER_ID,
            SchrodersBRSColumns.EXECUTING_ENTITY_ID,
            SchrodersBRSColumns.SELLER_ID,
            SchrodersBRSColumns.SELLER_DECISION_MAKER_ID,
        ]

        firm_cols = [
            SchrodersBRSColumns.FIRM_EXECUTION_ID,
            SchrodersBRSColumns.INVESTMENT_DECISION_ID,
        ]

        for col in lei_cols:
            data = df[col].dropna()
            if data.empty:
                continue

            df.loc[:, col] = data.apply(
                lambda x: f"lei:{x}"
                if LEI.validate_lei_code(x).is_valid and x.lower() != "intc"
                else f"id:{x}"
            )

        for col in firm_cols:
            data = df[col].dropna()
            if data.empty:
                continue
            df.loc[:, col] = data.apply(
                lambda x: f"clnt:{x}" if x.upper() == "NORE" else f"id:{x}"
            )
        return df

    @staticmethod
    def __process_country_codes(df: pd.DataFrame) -> pd.DataFrame:
        """
        Override Firm Execution Id and Investment Decision Id country codes to GB/LU

        :param df:
        :return:
        """
        # TODO: logic repeated. It should be encapsulated in one function
        # Firm Execution Id
        not_null_firm_execution_id_mask = df[
            SchrodersBRSColumns.FIRM_EXECUTION_ID
        ].notnull()

        if not_null_firm_execution_id_mask.any():
            nore_mask = (
                df.loc[
                    not_null_firm_execution_id_mask,
                    SchrodersBRSColumns.FIRM_EXECUTION_ID,
                ].str.upper()
                == "NORE"
            )

            gb_mask = (
                not_null_firm_execution_id_mask
                & ~nore_mask
                & df[SchrodersBRSColumns.FIRM_EXECUTION_ID].str.match("SC", case=False)
            )

            if gb_mask.any():
                df.loc[gb_mask, SchrodersBRSColumns.FIRM_EXECUTION_ID] = (
                    "GB"
                    + df.loc[gb_mask, SchrodersBRSColumns.FIRM_EXECUTION_ID].str[2:]
                )

            not_gb_mask = not_null_firm_execution_id_mask & ~nore_mask & ~gb_mask

            if not_gb_mask.any():
                df.loc[not_gb_mask, SchrodersBRSColumns.FIRM_EXECUTION_ID] = (
                    "LU"
                    + df.loc[not_gb_mask, SchrodersBRSColumns.FIRM_EXECUTION_ID].str[2:]
                )

        # Investment Decision Id
        not_null_investment_decision_id_mask = df[
            SchrodersBRSColumns.INVESTMENT_DECISION_ID
        ].notnull()

        if not_null_investment_decision_id_mask.any():
            gb_mask = not_null_investment_decision_id_mask & df[
                SchrodersBRSColumns.INVESTMENT_DECISION_ID
            ].str.match("SC", case=False)

            if gb_mask.any():
                df.loc[gb_mask, SchrodersBRSColumns.INVESTMENT_DECISION_ID] = (
                    "GB"
                    + df.loc[gb_mask, SchrodersBRSColumns.INVESTMENT_DECISION_ID].str[
                        2:
                    ]
                )

            not_gb_mask = not_null_investment_decision_id_mask & ~gb_mask

            if not_gb_mask.any():
                df.loc[not_gb_mask, SchrodersBRSColumns.INVESTMENT_DECISION_ID] = (
                    "LU"
                    + df.loc[
                        not_gb_mask, SchrodersBRSColumns.INVESTMENT_DECISION_ID
                    ].str[2:]
                )

        return df

    def _make_buyer_identifier(self, df: pd.DataFrame, params: Params) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)
        buyer_seller_id_lei_mask = self._get_buyer_seller_lei_mask(
            df=df, src_col=TempColumns.BUYER_ID_SRC
        )
        df[TempColumns.BUYER_LABEL] = self._get_label(
            df=df,
            lei_mask=buyer_seller_id_lei_mask,
            src_field=TempColumns.BUYER_ID_SRC,
            dest_field=TempColumns.BUYER_LABEL,
        )
        df[TempColumns.BUYER_ID] = self._get_id(
            df=df,
            lei_mask=buyer_seller_id_lei_mask,
            src_field=TempColumns.BUYER_ID_SRC,
            dest_field=TempColumns.BUYER_ID,
            params=params,
            buy_sell=BuySellIndicator.BUYI.value,
        )
        not_na_mask = (
            df[TempColumns.BUYER_LABEL].notnull() & df[TempColumns.BUYER_ID].notnull()
        )
        df.loc[not_na_mask, TempColumns.BUYER] = (
            df.loc[not_na_mask, TempColumns.BUYER_LABEL]
            + df.loc[not_na_mask, TempColumns.BUYER_ID]
        )

        buyer_not_null_mask = df[TempColumns.BUYER].notnull()

        if buyer_not_null_mask.any():
            result.loc[buyer_not_null_mask] = df.loc[
                buyer_not_null_mask, TempColumns.BUYER
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=RTS22TransactionModelField.PARTIES_BUYER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )
        return result

    @staticmethod
    def _make_buyer_dec_maker_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)
        mask = df[SchrodersBRSColumns.BUYER_DECISION_MAKER_ID].notnull()

        if mask.any():
            result.loc[mask] = df.loc[
                mask, SchrodersBRSColumns.BUYER_DECISION_MAKER_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=RTS22TransactionModelField.PARTIES_BUYER_DECISION_MAKER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )
        return result

    @staticmethod
    def _make_counterparty_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)
        buy_mask = df[SchrodersBRSColumns.BUYER_ID].notnull()
        sell_mask = df[SchrodersBRSColumns.SELLER_ID].notnull()
        use_buyer_id_mask = buy_mask & (
            (df[SchrodersBRSColumns.SELLER_DECISION_MAKER_ID].notnull())
            | (
                (
                    df[SchrodersBRSColumns.SELLER_ID].dropna().str.lower()
                    == Identifiers.INTC
                )
                & (df[SchrodersBRSColumns.BUYER_DECISION_MAKER_ID].isnull())
            )
        )

        use_seller_id_mask = sell_mask & (
            (df[SchrodersBRSColumns.BUYER_DECISION_MAKER_ID].notnull())
            | (
                (
                    df[SchrodersBRSColumns.BUYER_ID].dropna().str.lower()
                    == Identifiers.INTC
                )
                & (df[SchrodersBRSColumns.SELLER_DECISION_MAKER_ID].isnull())
            )
        )

        if use_buyer_id_mask.any():
            result.loc[use_buyer_id_mask] = df.loc[
                use_buyer_id_mask, SchrodersBRSColumns.BUYER_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=RTS22TransactionModelField.PARTIES_COUNTERPARTY,
                    type=IdentifierType.OBJECT,
                ).dict()
            )

        if use_seller_id_mask.any():
            result.loc[use_seller_id_mask] = df.loc[
                use_seller_id_mask, SchrodersBRSColumns.SELLER_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=RTS22TransactionModelField.PARTIES_COUNTERPARTY,
                    type=IdentifierType.OBJECT,
                ).dict()
            )
        return result

    @staticmethod
    def _make_exec_entity_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)
        mask = df[SchrodersBRSColumns.EXECUTING_ENTITY_ID].notnull()

        if mask.any():
            result.loc[mask] = df.loc[
                mask, SchrodersBRSColumns.EXECUTING_ENTITY_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=RTS22TransactionModelField.PARTIES_EXECUTING_ENTITY,
                    type=IdentifierType.OBJECT,
                ).dict()
            )
        return result

    @staticmethod
    def _make_exec_within_firm_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)
        mask = df[SchrodersBRSColumns.FIRM_EXECUTION_ID].notnull()

        if mask.any():
            result.loc[mask] = df.loc[
                mask, SchrodersBRSColumns.FIRM_EXECUTION_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=RTS22TransactionModelField.PARTIES_EXECUTION_WITHIN_FIRM,
                    type=IdentifierType.OBJECT,
                ).dict()
            )
        return result

    @staticmethod
    def _make_invest_dec_within_firm_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)
        mask = df[SchrodersBRSColumns.INVESTMENT_DECISION_ID].notnull()

        if mask.any():
            result.loc[mask] = df.loc[
                mask, SchrodersBRSColumns.INVESTMENT_DECISION_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=RTS22TransactionModelField.PARTIES_INVESTMENT_DECISION_WITHIN_FIRM,
                    type=IdentifierType.OBJECT,
                ).dict()
            )
        return result

    def _make_seller_identifier(self, df: pd.DataFrame, params: Params) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)
        buyer_seller_id_lei_mask = self._get_buyer_seller_lei_mask(
            df=df, src_col=TempColumns.SELLER_ID_SRC
        )
        df[TempColumns.SELLER_LABEL] = self._get_label(
            df=df,
            lei_mask=buyer_seller_id_lei_mask,
            src_field=TempColumns.SELLER_ID_SRC,
            dest_field=TempColumns.SELLER_LABEL,
        )
        df[TempColumns.SELLER_ID] = self._get_id(
            df=df,
            lei_mask=buyer_seller_id_lei_mask,
            src_field=TempColumns.SELLER_ID_SRC,
            dest_field=TempColumns.SELLER_ID,
            params=params,
            buy_sell=BuySellIndicator.SELL.value,
        )
        not_na_mask = (
            df[TempColumns.SELLER_LABEL].notnull() & df[TempColumns.SELLER_ID].notnull()
        )
        df.loc[not_na_mask, TempColumns.SELLER] = (
            df.loc[not_na_mask, TempColumns.SELLER_LABEL]
            + df.loc[not_na_mask, TempColumns.SELLER_ID]
        )

        seller_not_null_mask = df[TempColumns.SELLER].notnull()

        if seller_not_null_mask.any():
            result.loc[seller_not_null_mask] = df.loc[
                seller_not_null_mask, TempColumns.SELLER
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=RTS22TransactionModelField.PARTIES_SELLER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )
        return result

    @staticmethod
    def _make_seller_dec_maker_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)
        mask = df[SchrodersBRSColumns.SELLER_DECISION_MAKER_ID].notnull()

        if mask.any():
            result.loc[mask] = df.loc[
                mask, SchrodersBRSColumns.SELLER_DECISION_MAKER_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=RTS22TransactionModelField.PARTIES_SELLER_DECISION_MAKER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )
        return result

    @staticmethod
    def _make_trader_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)
        mask = df[SchrodersBRSColumns.INVESTMENT_DECISION_ID].notnull()

        if mask.any():
            result.loc[mask] = df.loc[
                mask, SchrodersBRSColumns.INVESTMENT_DECISION_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=RTS22TransactionModelField.PARTIES_TRADER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )
        return result

    @staticmethod
    def _get_label(
        df: pd.DataFrame, lei_mask: pd.Series, src_field: str, dest_field: str
    ) -> pd.Series:
        """
        :param df: source dataframe
        :param lei_mask: mask denoting src_field as lei or not.
        :param src_field: str Buyer Id/Seller Id
        :param dest_field: str transformed Buyer Id/Seller Id
        :return: pd.Series dest_field
        """
        # Note: here we are not using validate_lei_code method because here we are only
        # checking for the length of the field(==20) and do not want to do other checks(
        # which are part of validate_lei_code method) to decide if the value is an LEI.
        mask = lei_mask | (df[src_field].notnull() & (df[src_field].str.len() == 20))

        df.loc[mask, dest_field] = PartyLables.LEI
        df.loc[~mask, dest_field] = PartyLables.ID
        return df[dest_field]

    @staticmethod
    def _get_id(
        df: pd.DataFrame,
        lei_mask: pd.Series,
        src_field: str,
        dest_field: str,
        params: Params,
        buy_sell: str,
    ) -> pd.Series:
        """
        :param df: source DataFrame
        :param lei_mask: mask denoting src_field as lei or not.
        :param dest_field: destination field name
        :param params: input params
        :param buy_sell: Buy/Sell
        :return: dest_field
        """
        mask = lei_mask & (
            df[RTS22TransactionModelField.TRX_DTL_BUY_SELL_INDICATOR] == buy_sell
        )
        df.loc[mask, dest_field] = params.lei_value
        df.loc[~mask, dest_field] = df.loc[~lei_mask, src_field]
        return df[dest_field]

    @staticmethod
    def _get_buyer_seller_lei_mask(df: pd.DataFrame, src_col: str) -> pd.Series:
        """returns True mask if INTERNAL_PORTFOLIO_CODE==LEI_INTERNAL_PORTFOLIO and
        if either source_col(Buyer/Seller Id).len == 0(null) or 3."""
        buyer_seller_id_lei_mask = df[
            SchrodersBRSColumns.INTERNAL_PORTFOLIO_CODE
        ].str.upper().str.startswith(LEI_INTERNAL_PORTFOLIO, na=False) & (
            df[src_col].isnull()
            | (df[src_col].notnull() & (df[src_col].str.len() == 3))
        )
        return buyer_seller_id_lei_mask
