import re
from typing import Dict
from typing import List
from typing import Optional
from typing import Tuple

import pandas as pd
from pydantic import Field
from se_core_tasks.currency.convert_minor_to_major import ConvertMinorToMajor
from se_elastic_schema.static.mifid2 import ReportStatus
from se_elastic_schema.static.mifid2 import RTS22TransactionStatus
from se_elastic_schema.static.reference import InstrumentIdCodeType
from se_elastic_schema.static.reference import StrikePriceType
from se_elastic_schema.validators.iso.isin import ISIN
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.orders.common_utils import static
from swarm_tasks.transform.steeleye.tr.data_source.schroders_brs.static import (
    SchrodersBRSColumns,
)

INSTRUMENT_PATH = "instrumentDetails.instrument"
WORKFLOW_ELIGIBILITY_PATH = "workflow.eligibility"


class InstrumentFields:
    # Bond
    BOND_MATURITY_DATE = "bond.maturityDate"

    # Derivative
    DERIVATIVE_DELIVERY_TYPE = "derivative.deliveryType"
    DERIVATIVE_EXPIRY_DATE = "derivative.expiryDate"
    DERIVATIVE_OPTION_EXERCISE_STYLE = "derivative.optionExerciseStyle"
    DERIVATIVE_OPTION_TYPE = "derivative.optionType"
    DERIVATIVE_PRICE_MULTIPLIER = "derivative.priceMultiplier"
    DERIVATIVE_STRIKE_PRICE = "derivative.strikePrice"
    DERIVATIVE_STRIKE_PRICE_CURRENCY = "derivative.strikePriceCurrency"
    DERIVATIVE_UNDERLYING_INDEX_NAME = "derivative.underlyingIndexName"
    DERIVATIVE_UNDERLYING_INDEX_TERM = "derivative.underlyingIndexTerm"

    # Ext
    EXT_INSTRUMENT_ID_CODE_TYPE = "ext.instrumentIdCodeType"
    EXT_ON_FIRDS = "ext.onFIRDS"
    EXT_MIFIR_ELIGIBLE = "ext.mifirEligible"
    EXT_NOTIONAL_CURRENCY_2_TYPE = "ext.notionalCurrency2Type"
    EXT_PRICE_NOTATION = "ext.priceNotation"
    EXT_QUANTITY_NOTATION = "ext.quantityNotation"
    EXT_STRIKE_PRICE_TYPE = "ext.strikePriceType"
    EXT_VENUE_NAME = "ext.venueName"
    EXT_UNDERLYING_INSTRUMENTS = "ext.underlyingInstruments"

    # FX Derivatives
    FX_DERIVATIVES_NOTIONAL_CURRENCY_2 = "fxDerivatives.notionalCurrency2"

    # TODO: underlyings
    INSTRUMENT_CLASSIFICATION = "instrumentClassification"
    INSTRUMENT_FULL_NAME = "instrumentFullName"

    INSTRUMENT_ID_CODE = "instrumentIdCode"

    NOTIONAL_CURRENCY_1 = "notionalCurrency1"

    VENUE_TRADING_VENUE = "venue.tradingVenue"


class RTS22TransactionFields:
    TRX_DTL_PRICE_NOTATION = "transactionDetails.priceNotation"
    TRX_DTL_QUANTITY_NOTATION = "transactionDetails.quantityNotation"
    REPORT_DTL_REPORT_STATUS = "reportDetails.reportStatus"


class WorkflowEligibilityFields:
    ELIGIBILITY_ELIGIBLE = "eligible"
    ELIGIBILITY_EXECUTION_VENUE = "executionVenue"
    ELIGIBILITY_ON_FIRDS = "onFirds"
    ELIGIBILITY_REASON = "executionVenue"
    ELIGIBILITY_TOTV = "totv"
    ELIGIBILITY_UTOTV = "utotv"


class WorkflowFields:
    STATUS = "workflow.status"


class StageField:
    UNDERLYING_INDEX_NAME = "__underlying_index_name"
    UNDERLYING_INDEX_TERM = "__underlying_index_term"
    UNDERLYING_INDEX_ID = "__underlying_index_id"


class Params(BaseParams):
    override_underlying: Optional[Dict[str, List[str]]] = Field(
        None,
        description="Dictionary represents a list of source file values to be"
        "overridden by a hardcoded alternative",
    )
    instrument_tags_eligible: Optional[List[str]] = Field(
        None,
        description="List of tags to check against"
        " source instrument and underlying index names"
        " to make the record mifir eligible",
    )
    instrument_tags_non_eligible: Optional[List[str]] = Field(
        None,
        description="List of tags to check against"
        " source instrument and underlying index names"
        " to make the record mifir NON eligible",
    )


class InstrumentFallback(TransformBaseTask):
    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: BaseResources = None,
        **kwargs,
    ) -> pd.DataFrame:

        if INSTRUMENT_PATH not in source_frame.columns:
            source_frame.loc[:, INSTRUMENT_PATH] = pd.NA

        target = source_frame.loc[
            :,
            (
                (source_frame.columns.str.startswith("workflow"))
                | (source_frame.columns == INSTRUMENT_PATH)
            ),
        ]

        # Run fallback where there is no instrument and it is not a cancellation
        without_instrument = source_frame[INSTRUMENT_PATH].isnull() & (
            source_frame[RTS22TransactionFields.REPORT_DTL_REPORT_STATUS]
            != ReportStatus.CANC
        )

        if not without_instrument.any():
            return target

        data = source_frame.loc[without_instrument]

        data, valid_isins_mask = self._process_data(data)

        # Synthetic instrument
        synthetic_instruments = self._get_synthetic_instruments(
            df=data, params=params, valid_isins_mask=valid_isins_mask
        )

        target.loc[without_instrument, INSTRUMENT_PATH] = synthetic_instruments

        # Workflow

        eligible_mask: pd.Series = self._get_eligible_synthetic_mask(
            df=data, params=params
        )

        synthetic_workflow = self._get_synthetic_workflow(
            df=data, valid_isins_mask=valid_isins_mask, eligible_mask=eligible_mask
        )

        target.loc[without_instrument, WORKFLOW_ELIGIBILITY_PATH] = synthetic_workflow

        # Workflow status
        target.loc[
            without_instrument & ~eligible_mask, WorkflowFields.STATUS
        ] = RTS22TransactionStatus.NON_REPORTABLE

        target.loc[
            without_instrument & eligible_mask, WorkflowFields.STATUS
        ] = RTS22TransactionStatus.REPORTABLE

        return target

    @staticmethod
    def _process_data(df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series]:
        # read the static json file with minor currencies and conversion map
        minor_ccy_price_data = ConvertMinorToMajor.read_minor_ccy_and_price(
            static.MINOR_CURRENCIES_FILE_PATH
        )

        # Currencies
        for col in [
            SchrodersBRSColumns.NOTIONAL_CURRENCY_1,
            SchrodersBRSColumns.NOTIONAL_CURRENCY_2,
            SchrodersBRSColumns.STRIKE_PRICE_CURRENCY,
        ]:
            col_not_null_mask = df[col].notnull()
            if col_not_null_mask.any():
                df.loc[col_not_null_mask, col] = df.loc[col_not_null_mask, col].apply(
                    (
                        lambda x: ConvertMinorToMajor.convert_currency(
                            source_ccy=x, conversion_map_list=minor_ccy_price_data
                        )
                    )
                )

        # ISIN
        isin_data = df[SchrodersBRSColumns.INSTRUMENT_ID].dropna()

        if not isin_data.empty:

            valid_isins_mask = (df[SchrodersBRSColumns.INSTRUMENT_ID].notnull()) & (
                isin_data.apply(lambda x: ISIN.validate_isin_code(x).is_valid)
            )

            df.loc[
                valid_isins_mask, InstrumentFields.EXT_INSTRUMENT_ID_CODE_TYPE
            ] = InstrumentIdCodeType.ID.value

            df.loc[
                ~valid_isins_mask, InstrumentFields.EXT_INSTRUMENT_ID_CODE_TYPE
            ] = InstrumentIdCodeType.OTHR.value

            # Workflow
            df.loc[valid_isins_mask, WorkflowEligibilityFields.ELIGIBILITY_TOTV] = True
            df.loc[
                ~valid_isins_mask, WorkflowEligibilityFields.ELIGIBILITY_TOTV
            ] = False

            df.loc[valid_isins_mask, WorkflowEligibilityFields.ELIGIBILITY_UTOTV] = True
            df.loc[
                ~valid_isins_mask, WorkflowEligibilityFields.ELIGIBILITY_UTOTV
            ] = False

        else:
            valid_isins_mask = pd.Series(False, index=df.index)

            df.loc[
                :, InstrumentFields.EXT_INSTRUMENT_ID_CODE_TYPE
            ] = InstrumentIdCodeType.OTHR.value
            df.loc[:, WorkflowEligibilityFields.ELIGIBILITY_TOTV] = False
            df.loc[:, WorkflowEligibilityFields.ELIGIBILITY_UTOTV] = False

        # Instrument classification (equities)
        equity_mask = df[SchrodersBRSColumns.SECURITY_TYPE] == "EQUITY"

        df.loc[equity_mask, SchrodersBRSColumns.INSTRUMENT_CLASSIFICATION] = "EXXXXX"

        null_inst_class_and_security_type_notnull = (
            df[SchrodersBRSColumns.INSTRUMENT_CLASSIFICATION].isnull()
            & df[SchrodersBRSColumns.SECURITY_TYPE].notnull()
        )

        if null_inst_class_and_security_type_notnull.any():
            df.loc[
                null_inst_class_and_security_type_notnull,
                SchrodersBRSColumns.INSTRUMENT_CLASSIFICATION,
            ] = (
                df.loc[
                    null_inst_class_and_security_type_notnull,
                    SchrodersBRSColumns.SECURITY_TYPE,
                ]
                .str.upper()
                .str[0]
                + "XXXXX"
            )

        # Instrument Full Name
        with_instrument_name = df[SchrodersBRSColumns.INSTRUMENT_NAME].notnull()

        df.loc[with_instrument_name, InstrumentFields.INSTRUMENT_FULL_NAME] = df.loc[
            with_instrument_name, SchrodersBRSColumns.INSTRUMENT_NAME
        ]

        df.loc[~with_instrument_name, InstrumentFields.INSTRUMENT_FULL_NAME] = df.loc[
            ~with_instrument_name, SchrodersBRSColumns.SECURITY_DESCRIPTION
        ]

        # Strike Price Type
        with_strike_price_type_mask = df[
            SchrodersBRSColumns.STRIKE_PRICE_TYPE
        ].notnull()

        df.loc[with_strike_price_type_mask, SchrodersBRSColumns.STRIKE_PRICE_TYPE] = (
            df.loc[with_strike_price_type_mask, SchrodersBRSColumns.STRIKE_PRICE_TYPE]
            .map({"MntryValAmt": StrikePriceType.MNTRYVAL.value})
            .fillna(
                df.loc[
                    with_strike_price_type_mask, SchrodersBRSColumns.STRIKE_PRICE_TYPE
                ]
            )
        )

        # Underlying Index Name
        #    use instrument name
        use_instrument_name_mask = (
            (df[SchrodersBRSColumns.UNDERLYING_INDEX_NAME].isnull())
            & (
                df[SchrodersBRSColumns.SECURITY_SUB_TYPE_NAME].notnull()
                & df[SchrodersBRSColumns.SECURITY_SUB_TYPE_NAME].str.match(
                    "Future Equity", case=False
                )
            )
            & df[SchrodersBRSColumns.UNDERLYING_INDEX_ID].isnull()
            & (
                df[SchrodersBRSColumns.INSTRUMENT_CLASSIFICATION].notnull()
                & df[SchrodersBRSColumns.INSTRUMENT_CLASSIFICATION]
                .str.upper()
                .str.startswith("FFI")
            )
        )

        df.loc[
            use_instrument_name_mask, SchrodersBRSColumns.UNDERLYING_INDEX_NAME
        ] = df.loc[use_instrument_name_mask, SchrodersBRSColumns.INSTRUMENT_NAME].str[
            :25
        ]

        #    use underlying index id
        use_underlying_index_id_mask = (
            df[SchrodersBRSColumns.UNDERLYING_INDEX_NAME].isnull()
        ) & df[SchrodersBRSColumns.UNDERLYING_INDEX_ID].notnull()

        df.loc[
            use_underlying_index_id_mask,
            SchrodersBRSColumns.UNDERLYING_INDEX_NAME,
        ] = df.loc[
            use_underlying_index_id_mask, SchrodersBRSColumns.UNDERLYING_INDEX_ID
        ]

        #   use underlying index name
        use_underlying_index_name = df[
            SchrodersBRSColumns.UNDERLYING_INDEX_NAME
        ].notnull()

        df.loc[
            use_underlying_index_name, SchrodersBRSColumns.UNDERLYING_INDEX_NAME
        ] = df.loc[
            use_underlying_index_name, SchrodersBRSColumns.UNDERLYING_INDEX_NAME
        ].str[
            :25
        ]

        # Upper
        for col in [
            SchrodersBRSColumns.DELIVERY_TYPE,
            SchrodersBRSColumns.OPTION_STYLE,
            SchrodersBRSColumns.OPTION_TYPE,
            SchrodersBRSColumns.PRICE_TYPE,
            SchrodersBRSColumns.QUANTITY_TYPE,
        ]:
            data = df[col].dropna()

            if data.empty:
                continue

            df[col] = data.str.upper()

        return df, valid_isins_mask

    def _get_synthetic_instruments(
        self, df: pd.DataFrame, params: Params, valid_isins_mask: pd.Series
    ) -> pd.Series:
        synthetic_instruments = pd.DataFrame(index=df.index)

        # Instrument
        straight_map = [
            (
                SchrodersBRSColumns.DELIVERY_TYPE,
                InstrumentFields.DERIVATIVE_DELIVERY_TYPE,
            ),
            (SchrodersBRSColumns.EXPIRY_DATE, InstrumentFields.DERIVATIVE_EXPIRY_DATE),
            (
                SchrodersBRSColumns.INSTRUMENT_CLASSIFICATION,
                InstrumentFields.INSTRUMENT_CLASSIFICATION,
            ),
            (SchrodersBRSColumns.INSTRUMENT_ID, InstrumentFields.INSTRUMENT_ID_CODE),
            (
                InstrumentFields.EXT_INSTRUMENT_ID_CODE_TYPE,
                InstrumentFields.EXT_INSTRUMENT_ID_CODE_TYPE,
            ),
            (
                InstrumentFields.INSTRUMENT_FULL_NAME,
                InstrumentFields.INSTRUMENT_FULL_NAME,
            ),
            (SchrodersBRSColumns.MATURITY_DATE, InstrumentFields.BOND_MATURITY_DATE),
            (
                SchrodersBRSColumns.NOTIONAL_CURRENCY_1,
                InstrumentFields.NOTIONAL_CURRENCY_1,
            ),
            (
                SchrodersBRSColumns.NOTIONAL_CURRENCY_2,
                InstrumentFields.FX_DERIVATIVES_NOTIONAL_CURRENCY_2,
            ),
            (
                SchrodersBRSColumns.NOTIONAL_CURRENCY_2_TYPE,
                InstrumentFields.EXT_NOTIONAL_CURRENCY_2_TYPE,
            ),
            (
                SchrodersBRSColumns.OPTION_STYLE,
                InstrumentFields.DERIVATIVE_OPTION_EXERCISE_STYLE,
            ),
            (SchrodersBRSColumns.OPTION_TYPE, InstrumentFields.DERIVATIVE_OPTION_TYPE),
            (
                SchrodersBRSColumns.PRICE_MULTIPLIER,
                InstrumentFields.DERIVATIVE_PRICE_MULTIPLIER,
            ),
            (
                RTS22TransactionFields.TRX_DTL_PRICE_NOTATION,
                InstrumentFields.EXT_PRICE_NOTATION,
            ),
            (
                RTS22TransactionFields.TRX_DTL_QUANTITY_NOTATION,
                InstrumentFields.EXT_QUANTITY_NOTATION,
            ),
            (
                SchrodersBRSColumns.STRIKE_PRICE,
                InstrumentFields.DERIVATIVE_STRIKE_PRICE,
            ),
            (SchrodersBRSColumns.VENUE, InstrumentFields.VENUE_TRADING_VENUE),
            (SchrodersBRSColumns.VENUE_DESCRIPTION, InstrumentFields.EXT_VENUE_NAME),
        ]

        for entry in straight_map:
            synthetic_instruments[entry[1]] = df[entry[0]]

        security_code_mask = (
            df[SchrodersBRSColumns.SECURITY_SUB_TYPE_CODE]
            .str.contains("CPO|CCO", case=False)
            .fillna(False)
        )

        # Strike Price Currency
        strike_price_currency = df[SchrodersBRSColumns.STRIKE_PRICE_CURRENCY]
        strike_price_currency.loc[security_code_mask] = df[
            SchrodersBRSColumns.NOTIONAL_CURRENCY_1
        ]
        synthetic_instruments[
            InstrumentFields.DERIVATIVE_STRIKE_PRICE_CURRENCY
        ] = strike_price_currency

        # Strike Price Type
        strike_price_type = df[SchrodersBRSColumns.STRIKE_PRICE_TYPE]
        strike_price_type.loc[security_code_mask] = StrikePriceType.MNTRYVAL.value
        strike_price_type = strike_price_type.str.replace(
            "MntryValAmt", StrikePriceType.MNTRYVAL.value
        )
        synthetic_instruments[
            InstrumentFields.EXT_STRIKE_PRICE_TYPE
        ] = strike_price_type

        synthetic_instruments[InstrumentFields.EXT_ON_FIRDS] = False
        synthetic_instruments.loc[
            ~valid_isins_mask, InstrumentFields.EXT_MIFIR_ELIGIBLE
        ] = True
        synthetic_instruments.loc[
            valid_isins_mask, InstrumentFields.EXT_MIFIR_ELIGIBLE
        ] = False

        underlying_instrument_data = self._get_underlying_instrument_data(
            df=df, params=params
        )

        if not underlying_instrument_data.empty:
            synthetic_instruments = pd.concat(
                [synthetic_instruments, underlying_instrument_data], axis=1
            )

        inst_dtl_inst = synthetic_instruments.loc[:, :].apply(
            lambda x: x.dropna().to_dict(), axis=1
        )

        return inst_dtl_inst

    def _get_underlying_instrument_data(
        self, df: pd.DataFrame, params: Params
    ) -> pd.DataFrame:

        data = pd.DataFrame(index=df.index)
        data[InstrumentFields.EXT_UNDERLYING_INSTRUMENTS] = [
            [] for i in range(df.shape[0])
        ]

        # Underlying Index Name
        self.assign_underlying_field(
            df=df,
            data=data,
            params=params,
            source_field=SchrodersBRSColumns.UNDERLYING_INDEX_NAME,
            target_field=InstrumentFields.DERIVATIVE_UNDERLYING_INDEX_NAME,
            stage_field=StageField.UNDERLYING_INDEX_NAME,
        )

        # Underlying Index Term
        self.assign_underlying_field(
            df=df,
            data=data,
            params=params,
            source_field=SchrodersBRSColumns.UNDERLYING_INDEX_TERM,
            target_field=InstrumentFields.DERIVATIVE_UNDERLYING_INDEX_TERM,
            stage_field=StageField.UNDERLYING_INDEX_TERM,
        )

        # Underlying Instrument Id
        self.assign_underlying_field(
            df=df,
            data=data,
            params=params,
            source_field=SchrodersBRSColumns.UNDERLYING_INSTRUMENT_ID,
            target_field=InstrumentFields.INSTRUMENT_ID_CODE,
            stage_field=StageField.UNDERLYING_INDEX_ID,
        )

        data = data.loc[:, ~data.columns.str.startswith("__")]

        return data

    @staticmethod
    def _get_synthetic_workflow(
        df: pd.DataFrame, valid_isins_mask: pd.Series, eligible_mask: pd.Series
    ) -> pd.Series:

        synthetic_workflow = pd.DataFrame(index=df.index)

        straight_map = [
            (
                SchrodersBRSColumns.VENUE,
                WorkflowEligibilityFields.ELIGIBILITY_EXECUTION_VENUE,
            ),
        ]

        for entry in straight_map:
            synthetic_workflow.loc[: entry[1]] = df[entry[0]]

        synthetic_workflow.loc[
            eligible_mask, WorkflowEligibilityFields.ELIGIBILITY_ELIGIBLE
        ] = True
        synthetic_workflow.loc[
            ~eligible_mask, WorkflowEligibilityFields.ELIGIBILITY_ELIGIBLE
        ] = False

        # Eligibility on FIRDS
        synthetic_workflow[WorkflowEligibilityFields.ELIGIBILITY_ON_FIRDS] = False

        # Eligibility reason
        synthetic_workflow.loc[
            eligible_mask, WorkflowEligibilityFields.ELIGIBILITY_REASON
        ] = "Marked as reportable due to index list"

        synthetic_workflow.loc[
            ~eligible_mask, WorkflowEligibilityFields.ELIGIBILITY_REASON
        ] = "Instrument not connected with SteelEye Reference Data"

        # Eligibility TOTV
        synthetic_workflow[WorkflowEligibilityFields.ELIGIBILITY_TOTV] = False

        # Eligibility UTOTV
        synthetic_workflow.loc[
            ~valid_isins_mask, WorkflowEligibilityFields.ELIGIBILITY_UTOTV
        ] = True
        synthetic_workflow.loc[
            valid_isins_mask, WorkflowEligibilityFields.ELIGIBILITY_UTOTV
        ] = False

        workflow_eligibility = synthetic_workflow.loc[:, :].apply(
            lambda x: x.dropna().to_dict(), axis=1
        )

        return workflow_eligibility

    @staticmethod
    def _get_eligible_synthetic_mask(df: pd.DataFrame, params: Params) -> pd.Series:

        eligible_mask = pd.Series(False, index=df.index)

        if params.instrument_tags_eligible:
            eligible_regex = re.compile(
                rf"({'|'.join(params.instrument_tags_eligible)})", re.IGNORECASE
            )

            eligible_mask = eligible_mask | (
                df[SchrodersBRSColumns.INSTRUMENT_NAME]
                .str.contains(eligible_regex)
                .fillna(False)
                | df[SchrodersBRSColumns.UNDERLYING_INDEX_NAME]
                .str.contains(eligible_regex)
                .fillna(False)
            )

        if params.instrument_tags_non_eligible:
            non_eligible_regex = re.compile(
                rf"({'|'.join(params.instrument_tags_non_eligible)})", re.IGNORECASE
            )

            eligible_mask = eligible_mask | ~(
                df[SchrodersBRSColumns.INSTRUMENT_NAME]
                .str.contains(non_eligible_regex)
                .fillna(False)
                | df[SchrodersBRSColumns.UNDERLYING_INDEX_NAME]
                .str.contains(non_eligible_regex)
                .fillna(False)
                | (
                    df[SchrodersBRSColumns.INSTRUMENT_ID].notnull()
                    & df[SchrodersBRSColumns.INSTRUMENT_ID].apply(
                        lambda x: ISIN.validate_isin_code(x).is_valid
                    )
                )
            )

        return eligible_mask

    def assign_underlying_field(
        self,
        df: pd.DataFrame,
        data: pd.DataFrame,
        params: Params,
        source_field: str,
        target_field: str,
        stage_field: str,
    ):
        stage_field_data = df[source_field]

        if stage_field == StageField.UNDERLYING_INDEX_ID and params.override_underlying:
            for override, match_vals in params.override_underlying.items():
                match_regex = "|".join(match_vals)
                mask = df[SchrodersBRSColumns.UNDERLYING_INDEX_NAME].str.match(
                    match_regex, case=False
                )
                stage_field_data.loc[mask] = override

        not_null_stage_field_mask = stage_field_data.notnull()

        if not not_null_stage_field_mask.any():
            return

        # Split source field by ;
        data[stage_field] = stage_field_data.loc[not_null_stage_field_mask].apply(
            lambda x: list(map(str.strip, x.split(";")))
        )

        # Only one value
        one_mask = not_null_stage_field_mask & data.loc[
            not_null_stage_field_mask, stage_field
        ].map(lambda x: len(x) == 1)

        if one_mask.any():
            # NOTE: underlying index id, even if it is only one value,
            # it is always assigned under underlying instruments
            if stage_field == StageField.UNDERLYING_INDEX_ID:
                data.loc[one_mask, :].apply(
                    lambda x: [
                        self.allocate_underlying_instrument(
                            data=data,
                            index=x.name,
                            under_inst_index=i,
                            field=target_field,
                            value=y,
                        )
                        for i, y in enumerate(x[stage_field])
                    ],
                    axis=1,
                )
            else:
                data.loc[one_mask, target_field] = data.loc[
                    one_mask, stage_field
                ].apply(lambda x: x[0])

        # > 1 value
        more_than_one_mask = not_null_stage_field_mask & data.loc[
            not_null_stage_field_mask, stage_field
        ].map(lambda x: len(x) > 1)

        if more_than_one_mask.any():
            data.loc[more_than_one_mask, :].apply(
                lambda x: [
                    self.allocate_underlying_instrument(
                        data=data,
                        index=x.name,
                        under_inst_index=i,
                        field=target_field,
                        value=y,
                    )
                    for i, y in enumerate(x[stage_field])
                ],
                axis=1,
            )

    @staticmethod
    def allocate_underlying_instrument(
        data: pd.DataFrame, index: int, under_inst_index: int, field: str, value: str
    ):
        if (
            len(data.loc[index, InstrumentFields.EXT_UNDERLYING_INSTRUMENTS])
            == under_inst_index
        ):
            data.loc[index, InstrumentFields.EXT_UNDERLYING_INSTRUMENTS].append({})

        data.loc[index, InstrumentFields.EXT_UNDERLYING_INSTRUMENTS][under_inst_index][
            field
        ] = value
