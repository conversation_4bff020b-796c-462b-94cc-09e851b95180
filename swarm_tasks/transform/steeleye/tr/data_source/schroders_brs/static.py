class SchrodersBRSColumns:
    """
    Columns present in the csv/excel Schroders-BRS template
    """

    BUYER_ID = "Buyer Id"
    BUYER_DECISION_MAKER_ID = "Buyer Decision Maker Id"
    BUY_SELL_INDICATOR = "Buy Sell Indicator"
    DELIVERY_TYPE = "Delivery Type"
    EXECUTING_ENTITY_ID = "Executing Entity Id"
    EXPIRY_DATE = "Expiry Date"
    FIRM_EXECUTION_ID = "Firm Execution Id"
    INSTRUMENT_CLASSIFICATION = "Instrument Classification"
    INSTRUMENT_ID = "Instrument Id"
    INSTRUMENT_NAME = "Instrument Name"
    INTERNAL_PORTFOLIO_CODE = "Internal Portfolio Code"
    INVESTMENT_DECISION_ID = "Investment Decision Id"
    MATURITY_DATE = "Maturity Date"
    NOTIONAL_CURRENCY_1 = "Notional Currency 1"
    NOTIONAL_CURRENCY_2 = "Notional Currency 2"
    NOTIONAL_CURRENCY_2_TYPE = "Notional Currency 2 Type"
    OPTION_STYLE = "Option Style"
    OPTION_TYPE = "Option Type"
    PRICE_CURRENCY = "Price Currency"
    PRICE_MULTIPLIER = "Price Multiplier"
    PRICE_TYPE = "Price Type"
    QUANTITY_TYPE = "Quantity Type"
    SECURITY_DESCRIPTION = "Security Description"
    SECURITY_SUB_TYPE_CODE = "Security Sub Type Code"
    SECURITY_SUB_TYPE_NAME = "Security Sub Type Name"
    SECURITY_TYPE = "Security Type"
    SELLER_ID = "Seller Id"
    SELLER_DECISION_MAKER_ID = "Seller Decision Maker Id"
    STRIKE_PRICE = "Strike Price"
    STRIKE_PRICE_CURRENCY = "Strike Price Currency"
    STRIKE_PRICE_TYPE = "Strike Price Type"
    UNDERLYING_INDEX_ID = "Underlying Index Id"
    UNDERLYING_INDEX_NAME = "Underlying Index Name"
    UNDERLYING_INDEX_TERM = "Underlying Index Term"
    UNDERLYING_INSTRUMENT_ID = "Underlying Instrument Id"
    RECORD_TYPE = "Record Type"
    VENUE = "Venue"
    VENUE_DESCRIPTION = "Venue Description"
