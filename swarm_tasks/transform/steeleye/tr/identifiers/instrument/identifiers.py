import pandas as pd
from se_trades_tasks.order_and_tr.instrument.identifiers.identifiers import (
    Params as GenericParams,
)
from se_trades_tasks.order_and_tr.instrument.identifiers.identifiers import (
    run_instrument_identifiers,
)
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class InstrumentIdentifiers(TransformBaseTask):
    """
    As per in https://steeleye.atlassian.net/browse/PWG-554

    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:
        return self.process(
            source_frame=source_frame, params=params, auditor=self.auditor
        )

    @classmethod
    def process(
        cls, source_frame: pd.DataFrame = None, params: Params = None, **kwargs
    ) -> pd.DataFrame:

        return run_instrument_identifiers(
            source_frame=source_frame,
            params=params,
        )
