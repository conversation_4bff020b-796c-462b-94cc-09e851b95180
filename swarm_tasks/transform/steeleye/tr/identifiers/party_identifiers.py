import pandas as pd
from se_trades_tasks.tr.party.identifiers.generic_tr_party_identifiers import (
    Params as GenericParams,
)
from se_trades_tasks.tr.party.identifiers.generic_tr_party_identifiers import (
    run_generic_tr_party_identifiers,
)
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class PartyIdentifiers(TransformBaseTask):
    """
    This task should create all the columns with ids which will be used
    by LinkParties to retrieve the documents to embed in the record.
    This task uses exclusive attributes for each identifier.
    Please see the docstring
    """

    params_class = Params

    def execute(
        self, source_frame: pd.DataFrame = None, params: Params = None, **kwargs
    ) -> pd.DataFrame:
        return self.process(source_frame=source_frame, params=params)

    @classmethod
    def process(
        cls, source_frame: pd.DataFrame = None, params: Params = None, **kwargs
    ) -> pd.DataFrame:
        return run_generic_tr_party_identifiers(
            source_frame=source_frame,
            params=params,
        )
