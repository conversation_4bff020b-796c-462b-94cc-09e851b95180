from typing import Any
from typing import Optional

import pandas as pd
from se_trades_tasks.order.party.generic_order_party_identifiers import (
    Params as GenericParams,
)
from se_trades_tasks.order.party.generic_order_party_identifiers import (
    run_generic_order_party_identifiers,
)
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Params(GenericParams):
    pass


class GenericOrderPartyIdentifiers(TransformBaseTask):

    params_class = Params

    def execute(
        self,
        source_frame: Optional[pd.DataFrame] = None,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        **kwargs,
    ) -> pd.DataFrame:
        return GenericOrderPartyIdentifiers.process(
            source_frame=source_frame, params=params, auditor=self.auditor
        )

    @staticmethod
    def process(
        source_frame: pd.DataFrame,
        params: Params,
        auditor: Optional[Any] = None,
    ) -> pd.DataFrame:
        return run_generic_order_party_identifiers(
            source_frame=source_frame,
            params=params,
            auditor=auditor,
        )
