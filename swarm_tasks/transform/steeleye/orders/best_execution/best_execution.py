from typing import Optional

import pandas as pd
from prefect import context
from se_trades_tasks.order.best_execution.best_execution import run_best_execution
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Resources(BaseResources):
    es_client_key: str


logger = context.get("logger")


class BestExecution(TransformBaseTask):
    """
    This task calculates the best execution trade quality populating
    """

    resources_class = Resources

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[BaseParams] = None,
        resources: Optional[Resources] = None,
        **kwargs,
    ) -> pd.DataFrame:
        es_client = Settings.connections.get(resources.es_client_key)
        logger.info("Starting BestExecution tasks...")

        return run_best_execution(
            source_frame=source_frame, fetch_market_eod_data=True, es_client=es_client
        )
