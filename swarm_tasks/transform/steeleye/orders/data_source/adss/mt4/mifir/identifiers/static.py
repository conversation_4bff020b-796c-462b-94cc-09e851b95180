class ADSSMifirMT4Columns:
    ReportStatus = "ReportStatus"
    TransactionReferenceNumber = "TransactionReferenceNumber"
    TradingVenueTransactionIdentificationCode = (
        "TradingVenueTransactionIdentificationCode"
    )
    ExecutingEntityIdentificationCode = "ExecutingEntityIdentificationCode"
    InvestmentFirmCoveredBy201465EU = "InvestmentFirmCoveredBy201465EU"
    BuyerIdentificationCodeType = "BuyerIdentificationCodeType"
    BuyerNPcode = "BuyerNPcode"
    BuyerIdentificationCode = "BuyerIdentificationCode"
    BuyerCountryOfTheBranch = "BuyerCountryOfTheBranch"
    BuyerFirstNames = "BuyerFirstNames"
    BuyerSurnames = "BuyerSurnames"
    BuyerDateOfBirth = "BuyerDateOfBirth"
    BuyerDecisionMakerCodeType = "BuyerDecisionMakerCodeType"
    BuyerDecisionMakerNPcode = "BuyerDecisionMakerNPcode"
    BuyerDecisionMakerCode = "BuyerDecisionMakerCode"
    BuyerDecisionMakerFirstNames = "BuyerDecisionMakerFirstNames"
    BuyerDecisionSurnames = "BuyerDecisionSurnames"
    BuyerDecisionMakerDateOfBirth = "BuyerDecisionMakerDateOfBirth"
    SellerIdentificationCodeType = "SellerIdentificationCodeType"
    SellerNPcode = "SellerNPcode"
    SellerIdentificationCode = "SellerIdentificationCode"
    SellerCountryOfTheBranch = "SellerCountryOfTheBranch"
    SellerFirstNames = "SellerFirstNames"
    SellerSurnames = "SellerSurnames"
    SellerDateOfBirth = "SellerDateOfBirth"
    SellerDecisionMakerCodeType = "SellerDecisionMakerCodeType"
    SellerDecisionMakerNPcode = "SellerDecisionMakerNPcode"
    SellerDecisionMakerCode = "SellerDecisionMakerCode"
    SellerDecisionMakerSurnames = "SellerDecisionMakerSurnames"
    SellerDecisionMakerFirstNames = "SellerDecisionMakerFirstNames"
    SellerDecisionMakerDateOfBirth = "SellerDecisionMakerDateOfBirth"
    TransmissionOfOrderIndicator = "TransmissionOfOrderIndicator"
    TransmittingFirmIdentificationCodeForTheBuyer = (
        "TransmittingFirmIdentificationCodeForTheBuyer"
    )
    TransmittingFirmIdentificationCodeForTheSeller = (
        "TransmittingFirmIdentificationCodeForTheSeller"
    )
    TradingDateTime = "TradingDateTime"
    TradingCapacity = "TradingCapacity"
    QuantityType = "QuantityType"
    Quantity = "Quantity"
    QuantityCurrency = "QuantityCurrency"
    DerivativeNotionalIncreaseDecrease = "DerivativeNotionalIncreaseDecrease"
    PriceType = "PriceType"
    Price = "Price"
    PriceCurrency = "PriceCurrency"
    NetAmount = "NetAmount"
    Venue = "Venue"
    CountryOfTheBranchMembership = "CountryOfTheBranchMembership"
    UpfrontPayment = "UpfrontPayment"
    UpfrontPaymentCurrency = "UpfrontPaymentCurrency"
    ComplexTradeComponentId = "ComplexTradeComponentId"
    InstrumentIdentificationCode = "InstrumentIdentificationCode"
    InstrumentFullName = "InstrumentFullName"
    InstrumentClassification = "InstrumentClassification"
    NotionalCurrency1 = "NotionalCurrency1"
    NotionalCurrency2 = "NotionalCurrency2"
    PriceMultiplier = "PriceMultiplier"
    UnderlyingInstrumentCode = "UnderlyingInstrumentCode"
    UnderlyingIndexName = "UnderlyingIndexName"
    TermOfTheUnderlyingIndex = "TermOfTheUnderlyingIndex"
    OptionType = "OptionType"
    StrikePriceType = "StrikePriceType"
    StrikePrice = "StrikePrice"
    StrikePriceCurrency = "StrikePriceCurrency"
    OptionExerciseStyle = "OptionExerciseStyle"
    MaturityDate = "MaturityDate"
    ExpiryDate = "ExpiryDate"
    DeliveryType = "DeliveryType"
    InvestmentDecisionWithinFirmType = "InvestmentDecisionWithinFirmType"
    InvestmentDecisionWithinFirmNPCode = "InvestmentDecisionWithinFirmNPCode"
    InvestmentDecisionWithinFirm = "InvestmentDecisionWithinFirm"
    CountryOfTheBranchResponsibleForThePersonMakingTheInvestmentDecision = (
        "CountryOfTheBranchResponsibleForThePersonMakingTheInvestmentDecision"
    )
    ExecutionDecisionWithinFirmType = "ExecutionDecisionWithinFirmType"
    ExecutionDecisionWithinFirmNPcode = "ExecutionDecisionWithinFirmNPcode"
    ExecutionDecisionWithinFirm = "ExecutionDecisionWithinFirm"
    CountryOfTheBranchSupervisingThePersonResponsibleForTheExecution = (
        "CountryOfTheBranchSupervisingThePersonResponsibleForTheExecution"
    )
    WaiverIndicator = "WaiverIndicator"
    ShortSellingIndicator = "ShortSellingIndicator"
    OTCPostTradeIndicator = "OTCPostTradeIndicator"
    CommodityDerivativeIndicator = "CommodityDerivativeIndicator"
    SecuritiesFinancingTransactionIndicator = "SecuritiesFinancingTransactionIndicator"
    BranchLocation = "BranchLocation"
    BuySellIndicator = "transactionDetails.buySellIndicator"
