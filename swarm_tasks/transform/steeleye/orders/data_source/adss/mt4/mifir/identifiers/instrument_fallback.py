import json
from typing import Optional

import pandas as pd
from pydantic import Field
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):
    instrument_symbol_attribute: str = Field(
        ..., description="Source column using which the instrument data is fetched"
    )
    instrument_path: str = Field(
        "instrumentDetails.instrument", description="Target instrument path"
    )


class InstrumentFallBack(TransformBaseTask):
    """This task generates instrument data for the rows from the custom tenant
    instrument cache for the records where instruments could not be linked to SRP
    instrument"""

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        extractor_result: ExtractPathResult = None,
        params: Params = None,
        resources: Optional[BaseResources] = None,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(
            index=source_frame.index, columns=[params.instrument_path]
        )

        if source_frame.empty:
            return target

        if params.instrument_symbol_attribute not in source_frame.columns:
            self.auditor.add(
                f"Source field missing from"
                f"source_frame:{params.instrument_symbol_attribute}"
            )
            return source_frame[params.instrument_path].to_frame()

        cols_used = [
            params.instrument_symbol_attribute,
            params.instrument_path,
        ]

        cols_mask = source_frame.columns.isin(cols_used)

        df = source_frame.loc[:, cols_mask]

        for col in cols_used:
            if col not in df.columns:
                df[col] = pd.NA

        # read static instruments cache file from s3
        instrument_cache = self.__read_instrument_cache(extractor_result)

        # apply fallback to rows where instruments are not linked
        fallback_mask = (
            df[params.instrument_path].isnull()
            & df[params.instrument_symbol_attribute].notnull()
        )
        if fallback_mask.any():
            df.loc[fallback_mask, params.instrument_path] = (
                df.loc[fallback_mask, params.instrument_symbol_attribute]
                .dropna()
                .apply(lambda x: self.__get_instrument_data(x, instrument_cache))
            )

        target = df[params.instrument_path].to_frame()
        return target

    @staticmethod
    def __read_instrument_cache(extractor_result: ExtractPathResult) -> dict:
        """Reads instruments cache json file on s3"""
        instrument_cache_path = extractor_result.path.as_posix()
        with open(instrument_cache_path) as cache_data:
            data = json.load(cache_data)
        return data

    @staticmethod
    def __get_instrument_data(symbol: str, instrument_cache: dict) -> Optional[dict]:
        """Searches the instrument_cache dict for the input symbol under
        tag additionalIdentifiers and if the additionalIdentifier contains the string
        seblotter"""
        symbol = symbol.split(".")[0].lower()
        for rec in instrument_cache["records"]:
            add_ids = rec["ext"].get("additionalIdentifiers")
            if not add_ids:
                continue
            normalised_add_id = {k.lower(): v.lower() for k, v in add_ids.items()}
            for key, value in normalised_add_id.items():
                if "seblotter" in key and value == symbol:
                    return rec
        return pd.NA
