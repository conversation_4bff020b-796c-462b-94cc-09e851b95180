from typing import Optional

import pandas as pd
from se_elastic_schema.components.market.identifier import Identifier
from se_elastic_schema.static.market import IdentifierType
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.orders.data_source.adss.mt4.emir.identifiers.static import (
    StaticIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.data_source.adss.mt4.mifir.identifiers.static import (
    ADSSMifirMT4Columns,
)

INSTRUMENT_PATH = "instrumentDetails.instrument"
TARGET_COLUMN = "marketIdentifiers.instrument"


class InstrumentIdentifiers(TransformBaseTask):
    def execute(
        self,
        source_frame: Optional[pd.DataFrame] = None,
        params: Optional[BaseParams] = None,
        resources: Optional[BaseResources] = None,
        **kwargs,
    ) -> pd.DataFrame:

        cols_used = [
            ADSSMifirMT4Columns.InstrumentFullName,
            ADSSMifirMT4Columns.UnderlyingInstrumentCode,
            TARGET_COLUMN,
        ]
        target = pd.DataFrame(index=source_frame.index, columns=[TARGET_COLUMN])
        if source_frame.empty:
            return target
        for col in cols_used:
            if col not in source_frame.columns:
                source_frame[col] = pd.NA

        cols_mask = source_frame.columns.isin(cols_used)

        df = source_frame.loc[:, cols_mask]

        # convert InstrumentFullName to upper
        df[ADSSMifirMT4Columns.InstrumentFullName] = df[
            ADSSMifirMT4Columns.InstrumentFullName
        ].str.upper()

        # spread-bets
        sb_mask = df[ADSSMifirMT4Columns.InstrumentFullName].str.endswith(
            StaticIdentifiers.DOT_SB, na=False
        )
        if sb_mask.any():
            df.loc[sb_mask, TARGET_COLUMN] = df.loc[
                sb_mask, ADSSMifirMT4Columns.UnderlyingInstrumentCode
            ].apply(
                lambda x: [f"{StaticIdentifiers.VENUE_XXXX}{x}{StaticIdentifiers.SB}"]
                if not pd.isna(x)
                else pd.NA
            )

        # cfd
        if not sb_mask.all():
            df.loc[~sb_mask, TARGET_COLUMN] = df.loc[
                ~sb_mask, ADSSMifirMT4Columns.UnderlyingInstrumentCode
            ].apply(
                lambda x: [f"{StaticIdentifiers.VENUE_XXXX}{x}{StaticIdentifiers.CFD}"]
                if not pd.isna(x)
                else pd.NA
            )

        target[TARGET_COLUMN] = (
            df[TARGET_COLUMN]
            .dropna()
            .apply(
                lambda x: list(
                    map(
                        lambda y: Identifier(
                            labelId=y,
                            path=INSTRUMENT_PATH,
                            type=IdentifierType.OBJECT,
                        ).dict(),
                        x,
                    )
                )
            )
        )
        return target
