from typing import Optional

import pandas as pd
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.orders.data_source.adss.mt4.emir.identifiers.static import (
    ADSSEmirMT4Columns,
)


class SkipNonFXRows(TransformBaseTask):
    """This task skips non-FX rows from the source file."""

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[BaseParams] = None,
        resources: Optional[BaseResources] = None,
        **kwargs
    ) -> pd.DataFrame:

        if source_frame.empty:
            return source_frame

        cols_used = [
            ADSSEmirMT4Columns.INSTRUMENT_ID,
            ADSSEmirMT4Columns.ACTION,
            ADSSEmirMT4Columns.ACTION_TYPE,
        ]
        for col in cols_used:
            if col not in source_frame.columns:
                source_frame.loc[:, col] = pd.NA

        mask = (source_frame[ADSSEmirMT4Columns.INSTRUMENT_ID].str.upper() == "CU") & (
            (source_frame[ADSSEmirMT4Columns.ACTION].str.upper() == "N")
            | (source_frame[ADSSEmirMT4Columns.ACTION_TYPE].str.upper() == "N")
        )

        df = source_frame.loc[mask]

        return df
