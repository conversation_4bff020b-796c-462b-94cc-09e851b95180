import pandas as pd
from se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.convert_cusip_to_isin import (
    Params as GenericParams,
)
from se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.convert_cusip_to_isin import (
    run_convert_cusip_to_isin,
)
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class ConvertCusipToIsin(TransformBaseTask):

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            params=params,
            auditor=self.auditor,
            **kwargs,
        )

    @classmethod
    def process(
        cls, source_frame: pd.DataFrame, params: GenericParams, auditor=None, **kwargs
    ) -> pd.DataFrame:

        return run_convert_cusip_to_isin(
            source_frame=source_frame,
            params=params,
            auditor=auditor,
        )
