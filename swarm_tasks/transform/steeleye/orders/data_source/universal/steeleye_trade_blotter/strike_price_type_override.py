import pandas as pd
from se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.strike_price_type_override import (
    Params as GenericParams,
)
from se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.strike_price_type_override import (
    run_strike_price_type_override,
)
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class StrikePriceTypeOverride(TransformBaseTask):
    """ON-1776 - Override 'strikePriceType' for FX:
    if (instrumentDetails.instrument.ext.strikePriceType == ""
         and  instrumentDetails.instrument.instrumentClassification.startswith(HF)
         )
         then:
            instrumentDetails.instrument.ext.strikePriceType = MntryVal
    Note: The logic is applied only when override_strike_price_type param is True"""

    params_class = Params

    def execute(
        self, source_frame: pd.DataFrame = None, params: Params = None, **kwargs
    ) -> pd.DataFrame:
        return run_strike_price_type_override(
            source_frame=source_frame, params=params, **kwargs
        )
