import pandas as pd
from se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.blotter_party_identifiers import (
    Params as GenericParams,
)
from se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.blotter_party_identifiers import (
    run_blotter_party_identifiers,
)
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class Resources(BaseResources):
    es_client_key: str


class PartyIdentifiers(TransformBaseTask):

    params_class = Params
    resources_class = Resources

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: Resources = None,
        **kwargs,
    ) -> pd.DataFrame:

        es_client = self.clients.get(resources.es_client_key)

        return self.process(
            source_frame=source_frame,
            params=params,
            auditor=self.auditor,
            es_client=es_client,
            **kwargs,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame,
        params: GenericParams,
        es_client=None,
        auditor=None,
        **kwargs,
    ) -> pd.DataFrame:

        return run_blotter_party_identifiers(
            source_frame=source_frame,
            params=params,
            auditor=auditor,
            es_client=es_client,
            tenant=Settings.tenant,
        )
