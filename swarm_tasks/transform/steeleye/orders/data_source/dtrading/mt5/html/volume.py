import numpy as np
import pandas as pd
from pydantic import Field
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):
    source_attribute: str
    target_attribute: str
    delimiter: str = Field(
        "/",
        description="Volume field comes as '12 / 1' the delimiter is the character used"
        " to separate the numbers",
    )
    index: int = Field(
        0,
        description="Once Volume has been split based on the delimiter dictates the index"
        " we select from the resulting list",
    )


class Volume(TransformBaseTask):
    """
    Handles dtrading Volume field format and selects the required part. e.g. 12 / 1

    params:
        source_attribute: Volume
        target_attribute: priceFormingData.tradedQuantity
        index: 1

    Output: priceFormingData.tradedQuantity = 1
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources=None,
        extractor_result: ExtractPathResult = None,
        **kwargs
    ) -> pd.DataFrame:
        target = pd.DataFrame(index=source_frame.index)
        target[params.target_attribute] = np.nan
        mask = source_frame[params.source_attribute].notnull()
        if mask.any():
            target.loc[mask, params.target_attribute] = (
                source_frame.loc[mask, params.source_attribute]
                .str.replace("K", "000")
                .str.replace(" ", "")
                .str.split(params.delimiter)
                .apply(lambda x: x[params.index])
                .astype(float)
            )
        return target
