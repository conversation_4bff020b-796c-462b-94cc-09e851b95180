from typing import Optional

import pandas as pd
from se_elastic_schema.components.market.identifier import Identifier
from se_elastic_schema.static.market import IdentifierType
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class ColumnFields:

    INSTRUMENT_PATH = "instrumentDetails.instrument"
    TARGET_COLUMN = "marketIdentifiers.instrument"
    INPUT_ISIN = "ISIN"
    INPUT_VENUE = "transactionDetails.venue"
    INPUT_CURRENCY = "CCY"


class Placeholders:
    XOFF = "XOFF"
    XXXX = "XXXX"


class InstrumentIdentifiers(TransformBaseTask):

    """
    This tasks builds the instrument identifiers
    based on Master Icarus logic for NON_CFD Carrying ISIN
    """

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[BaseParams] = None,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(
            index=source_frame.index, columns=[ColumnFields.TARGET_COLUMN]
        )

        if source_frame.empty:
            return target

        cols_used = [
            ColumnFields.INPUT_ISIN,
            ColumnFields.INPUT_VENUE,
            ColumnFields.INPUT_CURRENCY,
        ]
        for col in cols_used:
            if col not in source_frame.columns:
                source_frame[col] = pd.NA

        cols_mask = source_frame.columns.isin(cols_used)

        df = source_frame.loc[:, cols_mask]

        xoff_mask = df[ColumnFields.INPUT_VENUE] == Placeholders.XOFF

        df.loc[xoff_mask, ColumnFields.TARGET_COLUMN] = Placeholders.XXXX

        target[ColumnFields.TARGET_COLUMN] = df.apply(
            lambda x: [
                # ISIN + Currency + Venue
                Identifier(
                    labelId=f"{x[ColumnFields.INPUT_ISIN]}{x[ColumnFields.INPUT_CURRENCY]}{x[ColumnFields.TARGET_COLUMN]}",
                    path=ColumnFields.INSTRUMENT_PATH,
                    type=IdentifierType.OBJECT,
                ).dict(by_alias=True),
                # ISIN + Currency
                Identifier(
                    labelId=f"{x[ColumnFields.INPUT_ISIN]}{x[ColumnFields.INPUT_CURRENCY]}",
                    path=ColumnFields.INSTRUMENT_PATH,
                    type=IdentifierType.OBJECT,
                ).dict(by_alias=True),
                # ISIN
                Identifier(
                    labelId=f"{x[ColumnFields.INPUT_ISIN]}",
                    path=ColumnFields.INSTRUMENT_PATH,
                    type=IdentifierType.OBJECT,
                ).dict(by_alias=True),
            ],
            axis=1,
        )
        return target
