import pandas as pd
from pydantic import Field
from se_elastic_schema.components.market.identifier import Identifier
from se_elastic_schema.static.market import IdentifierType
from se_elastic_schema.static.mifid2 import BuySellIndicator
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.orders.common_utils.static import PartiesFields
from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.static import (
    SteelEyeTradeBlotterColumns,
)


class Params(BaseParams):
    executing_entity_id: str = Field(
        None, description="Will be used in `Executing Entity ID` values"
    )


class DFColumns:
    MARKET_IDENTIFIERS_PARTIES = "marketIdentifiers.parties"
    COUNTERPARTY = "Counterparty"
    DEALER = "Dealer"


class Identifiers:
    ID = "id"
    LEI = "lei"


class PartyIdentifiers(TransformBaseTask):
    """
    This task should create all the columns with ids which will be used
    by LinkParties to retrieve the documents to embed in the record.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(
            index=source_frame.index, columns=[DFColumns.MARKET_IDENTIFIERS_PARTIES]
        )
        if source_frame.empty:
            return target

        cols_used = [
            SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID,
            DFColumns.DEALER,
            DFColumns.COUNTERPARTY,
            PartiesFields.TRX_DTL_BUY_SELL_INDICATOR,
        ]
        df = source_frame.loc[:, source_frame.columns.intersection(cols_used)]
        for col in cols_used:
            if col not in df.columns:
                df[col] = pd.NA

        if df.empty:
            return target

        df = self._add_prefix_to_ids(df=df)

        # override EXECUTING_ENTITY_ID
        if params.executing_entity_id is not None:
            df[
                SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID
            ] = f"{Identifiers.LEI}:{params.executing_entity_id}"

        # required masks
        buy_mask = df[PartiesFields.TRX_DTL_BUY_SELL_INDICATOR] == BuySellIndicator.BUYI

        sell_mask = (
            df[PartiesFields.TRX_DTL_BUY_SELL_INDICATOR] == BuySellIndicator.SELL
        )

        identifiers_df: pd.DataFrame = pd.DataFrame(index=df.index)

        # buyer
        identifiers_df[PartiesFields.PARTIES_BUYER] = self._make_buyer_identifier(
            df=df, buy_mask=buy_mask
        )

        # buyer file identifier
        identifiers_df[
            PartiesFields.PARTIES_BUYER_FILE_ID
        ] = self._make_buyer_file_identifier(df=df, buy_mask=buy_mask)

        # seller
        identifiers_df[PartiesFields.PARTIES_SELLER] = self._make_seller_identifier(
            df=df, sell_mask=sell_mask
        )

        # seller file identifier
        identifiers_df[
            PartiesFields.PARTIES_SELLER_FILE_ID
        ] = self._make_seller_file_identifier(df=df, sell_mask=sell_mask)

        # counterparty
        identifiers_df[
            PartiesFields.PARTIES_COUNTERPARTY
        ] = self._make_generic_identifier(
            df=df,
            col_for_id=DFColumns.COUNTERPARTY,
            path=PartiesFields.PARTIES_COUNTERPARTY,
            type=IdentifierType.OBJECT,
        )

        # counterparty file identifier
        identifiers_df[
            PartiesFields.PARTIES_CP_FILE_ID
        ] = self._make_generic_file_identifier(df=df, col_for_id=DFColumns.COUNTERPARTY)

        # executing entity
        identifiers_df[
            PartiesFields.PARTIES_EXECUTING_ENTITY
        ] = self._make_generic_identifier(
            df=df,
            col_for_id=SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID,
            path=PartiesFields.PARTIES_EXECUTING_ENTITY,
            type=IdentifierType.OBJECT,
        )

        # executing entity file identifier
        identifiers_df[
            PartiesFields.PARTIES_EXEC_ENTITY_FILE_ID
        ] = self._make_generic_file_identifier(
            df=df, col_for_id=SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID
        )

        # execution within firm
        identifiers_df[
            PartiesFields.PARTIES_EXECUTION_WITHIN_FIRM
        ] = self._make_generic_identifier(
            df=df,
            col_for_id=DFColumns.DEALER,
            path=PartiesFields.PARTIES_EXECUTION_WITHIN_FIRM,
            type=IdentifierType.OBJECT,
        )

        # execution within firm file identifier
        identifiers_df[
            PartiesFields.PARTIES_EXEC_WITHIN_FIRM_FILE_ID
        ] = self._make_generic_file_identifier(df=df, col_for_id=DFColumns.DEALER)

        # investment decision within firm
        identifiers_df[
            PartiesFields.PARTIES_INVEST_DEC_WITHIN_FIRM
        ] = self._make_generic_identifier(
            df=df,
            col_for_id=DFColumns.DEALER,
            path=PartiesFields.PARTIES_INVEST_DEC_WITHIN_FIRM,
            type=IdentifierType.OBJECT,
        )

        # investment decision within firm file identifier
        identifiers_df[
            PartiesFields.PARTIES_INVEST_DEC_WITHIN_FIRM_FILE_ID
        ] = self._make_generic_file_identifier(df=df, col_for_id=DFColumns.DEALER)

        # trader
        identifiers_df[PartiesFields.PARTIES_TRADER] = self._make_generic_identifier(
            df=df,
            col_for_id=DFColumns.DEALER,
            path=PartiesFields.PARTIES_TRADER,
            type=IdentifierType.ARRAY,
        )

        # trader file identifier
        identifiers_df[
            PartiesFields.PARTIES_TRADER_FILE_ID
        ] = self._make_generic_file_identifier(df=df, col_for_id=DFColumns.DEALER)

        cols_to_merge = [
            PartiesFields.PARTIES_BUYER,
            PartiesFields.PARTIES_SELLER,
            PartiesFields.PARTIES_EXECUTING_ENTITY,
            PartiesFields.PARTIES_TRADER,
            PartiesFields.PARTIES_COUNTERPARTY,
            PartiesFields.PARTIES_EXECUTION_WITHIN_FIRM,
            PartiesFields.PARTIES_INVEST_DEC_WITHIN_FIRM,
        ]

        # Merge columns to create PartiesFields.MARKET_IDENTIFIERS_PARTIES
        identifiers_df.loc[
            :, PartiesFields.MARKET_IDENTIFIERS_PARTIES
        ] = identifiers_df[cols_to_merge].apply(lambda x: x.dropna().tolist(), axis=1)

        target_col_list = [
            PartiesFields.MARKET_IDENTIFIERS_PARTIES,
            PartiesFields.PARTIES_BUYER_FILE_ID,
            PartiesFields.PARTIES_SELLER_FILE_ID,
            PartiesFields.PARTIES_EXEC_ENTITY_FILE_ID,
            PartiesFields.PARTIES_TRADER_FILE_ID,
            PartiesFields.PARTIES_CP_FILE_ID,
            PartiesFields.PARTIES_EXEC_WITHIN_FIRM_FILE_ID,
            PartiesFields.PARTIES_INVEST_DEC_WITHIN_FIRM_FILE_ID,
        ]

        return identifiers_df[target_col_list]

    @staticmethod
    def _add_prefix_to_ids(df: pd.DataFrame) -> pd.DataFrame:
        """
        Add prefix to columns with ids
        :param df: source_frame
        :return: dataframe with prefixed ids
        """
        columns = [DFColumns.DEALER, DFColumns.COUNTERPARTY]
        for col in columns:
            not_null_mask = df[col].notnull()
            if not_null_mask.any():
                df.loc[not_null_mask, col] = (
                    df.loc[not_null_mask, col]
                    .dropna()
                    .apply(lambda x: f"{Identifiers.ID}:{x}")
                )
        return df

    @staticmethod
    def _make_buyer_identifier(df: pd.DataFrame, buy_mask: pd.Series) -> pd.Series:
        """
        Create buyer identifier
        :param df: source_frame
        :param buy_mask: buyer rows
        :return: series with buyer identifiers
        """
        result = pd.Series(pd.NA, index=df.index)
        if buy_mask.any():
            result.loc[buy_mask] = df.loc[
                buy_mask, SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=PartiesFields.PARTIES_BUYER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )

        not_buy_mask = ~buy_mask & df[DFColumns.COUNTERPARTY].notnull()

        if not_buy_mask.any():
            result.loc[not_buy_mask] = df.loc[
                not_buy_mask, DFColumns.COUNTERPARTY
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=PartiesFields.PARTIES_BUYER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )

        return result

    @staticmethod
    def _make_buyer_file_identifier(df: pd.DataFrame, buy_mask: pd.Series) -> pd.Series:
        """
        create buyer file identifier
        :param df: source_frame
        :param buy_mask: buyer rows
        :return: series with buyer file identifier
        """
        result = pd.Series(pd.NA, index=df.index)

        if buy_mask.any():
            result.loc[buy_mask] = (
                df.loc[buy_mask, SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID]
                .dropna()
                .str.lower()
            )

        not_buy_mask = ~buy_mask & df[DFColumns.COUNTERPARTY].notnull()

        if not_buy_mask.any():
            result.loc[not_buy_mask] = (
                df.loc[not_buy_mask, DFColumns.COUNTERPARTY].dropna().str.lower()
            )

        return result

    @staticmethod
    def _make_seller_identifier(df: pd.DataFrame, sell_mask: pd.Series) -> pd.Series:
        """
        Create seller identifier
        :param df: source_frame
        :param sell_mask: seller rows
        :return: series with seller identifiers
        """
        result = pd.Series(pd.NA, index=df.index)

        if sell_mask.any():
            result.loc[sell_mask] = df.loc[
                sell_mask, SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=PartiesFields.PARTIES_SELLER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )

        not_sell_mask = ~sell_mask & df[DFColumns.COUNTERPARTY].notnull()

        if not_sell_mask.any():
            result.loc[not_sell_mask] = df.loc[
                not_sell_mask,
                DFColumns.COUNTERPARTY,
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=PartiesFields.PARTIES_SELLER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )

        return result

    @staticmethod
    def _make_seller_file_identifier(
        df: pd.DataFrame, sell_mask: pd.Series
    ) -> pd.Series:
        """
        Create seller file identifier
        :param df: source_frame
        :param sell_mask: seller rows
        :return: series with seller file identifiers
        """
        result = pd.Series(pd.NA, index=df.index)

        if sell_mask.any():
            result.loc[sell_mask] = (
                df.loc[sell_mask, SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID]
                .dropna()
                .str.lower()
            )

        not_sell_mask = ~sell_mask & df[DFColumns.COUNTERPARTY].notnull()

        if not_sell_mask.any():
            result.loc[not_sell_mask] = (
                df.loc[not_sell_mask, DFColumns.COUNTERPARTY].dropna().str.lower()
            )

        return result

    @staticmethod
    def _make_generic_identifier(
        df: pd.DataFrame, col_for_id: str, path: str, type: str
    ) -> pd.Series:
        """
        Create generic identifier which does not require specific logic
        :param df: source_frame
        :param col_for_id: column to create the identifier
        :param path: Identifier.path
        :param type: Identifier.type
        :return: series with a generic identifier
        """
        result = pd.Series(pd.NA, index=df.index)

        mask = df[col_for_id].notnull()

        if mask.any():
            result.loc[mask] = df.loc[mask, col_for_id].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=path,
                    type=type,
                ).dict()
            )

        return result

    @staticmethod
    def _make_generic_file_identifier(df: pd.DataFrame, col_for_id: str) -> pd.Series:
        """
        Create generic file identifier which does not require specific logic
        :param df: source_frame
        :param col_for_id: column to create the identifier
        :return: series with a generic file identifier
        """
        result = pd.Series(pd.NA, index=df.index)

        mask = df[col_for_id].notnull()

        if mask.any():
            result.loc[mask] = df.loc[mask, col_for_id].dropna().str.lower()

        return result
