from typing import Optional

import pandas as pd
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.orders.data_source.atfx.mt4.html.identifiers.static import (
    ATFXMT4Columns,
)

INSTRUMENT_PATH = "instrumentDetails.instrument"


class QuantityFields:
    initialQty = "priceFormingData.initialQuantity"
    tradedQty = "priceFormingData.tradedQuantity"


class LotSize(TransformBaseTask):
    """This task fetches lot sizes for the rows from the custom tenant
    lot size cache"""

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        extractor_result: ExtractPathResult = None,
        params: Optional[BaseParams] = None,
        resources: Optional[BaseResources] = None,
        **kwargs
    ) -> pd.DataFrame:
        target_columns = [QuantityFields.initialQty, QuantityFields.tradedQty]
        target = pd.DataFrame(index=source_frame.index, columns=target_columns)
        if source_frame.empty:
            return target

        cols_used = [
            ATFXMT4Columns.SYMBOL,
            ATFXMT4Columns.TempQuantity,
            ATFXMT4Columns.Lots,
        ]

        cols_mask = source_frame.columns.isin(cols_used)

        df = source_frame.loc[:, cols_mask]
        for col in cols_used:
            if col not in source_frame.columns:
                df[col] = pd.NA

        zero_mask = df[ATFXMT4Columns.TempQuantity] == 0
        mask = zero_mask & df[ATFXMT4Columns.SYMBOL].notnull()

        lot_cache = self.__read_lot_cache(extractor_result)
        if lot_cache and mask.any():
            df.loc[mask, ATFXMT4Columns.TempQuantity] = (
                df.loc[mask, ATFXMT4Columns.SYMBOL]
                .str.upper()
                .apply(lambda x: lot_cache.get(x, 0.0))
            )
        else:
            df.loc[mask, ATFXMT4Columns.TempQuantity] = 0.0

        df[QuantityFields.tradedQty] = df[ATFXMT4Columns.TempQuantity].fillna(
            0.0
        ).astype(float) * df[ATFXMT4Columns.Lots].fillna(0.0).astype(float)
        df[QuantityFields.initialQty] = df[QuantityFields.tradedQty]
        return df[target_columns]

    @staticmethod
    def __read_lot_cache(extractor_result: ExtractPathResult) -> dict:
        data = pd.read_excel(extractor_result.path, header=0)
        data = data.drop_duplicates(subset=[ATFXMT4Columns.Products], keep="first")
        data[ATFXMT4Columns.Products] = data[ATFXMT4Columns.Products].str.upper()
        return data.set_index(ATFXMT4Columns.Products)[
            ATFXMT4Columns.ContractSize
        ].to_dict()
