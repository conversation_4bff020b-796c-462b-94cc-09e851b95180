from typing import Optional

import pandas as pd
from pydantic import Field
from se_elastic_schema.components.market.identifier import Identifier
from se_elastic_schema.static.market import IdentifierType
from se_elastic_schema.static.mifid2 import BuySellIndicator
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.orders.common_utils.static import PartiesFields
from swarm_tasks.transform.steeleye.orders.data_source.atfx.mt4.html.identifiers.static import (
    ATFXMT4Columns,
)


class Params(BaseParams):
    lei_col: str = Field(
        ...,
        description="Column name containing client LEI number",
    )


class PartyIdentifiers(TransformBaseTask):
    """
    This task should create all the columns with ids which will be used
    by LinkParties to retrieve the documents to embed in the record.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: Optional[BaseResources] = None,
        **kwargs,
    ) -> pd.DataFrame:

        cols_used = [
            ATFXMT4Columns.Login,
            PartiesFields.TRX_DTL_BUY_SELL_INDICATOR,
            params.lei_col,
        ]

        cols_mask = source_frame.columns.isin(cols_used)

        df = source_frame.loc[:, cols_mask]
        for col in cols_used:
            if col not in source_frame.columns:
                df[col] = pd.NA

        df = self._add_prefix_to_ids(df=df)

        buy_mask = df[PartiesFields.TRX_DTL_BUY_SELL_INDICATOR] == BuySellIndicator.BUYI

        sell_mask = (
            df[PartiesFields.TRX_DTL_BUY_SELL_INDICATOR] == BuySellIndicator.SELL
        )

        identifiers_df: pd.DataFrame = pd.DataFrame(index=df.index)

        # buyer
        identifiers_df[PartiesFields.PARTIES_BUYER] = self._make_buyer_identifier(
            df=df, buy_mask=buy_mask, path=PartiesFields.PARTIES_BUYER, params=params
        )

        # buyerFileIdentifier
        identifiers_df[
            PartiesFields.PARTIES_BUYER_FILE_ID
        ] = self._make_buyer_file_identifier(df=df, buy_mask=buy_mask, params=params)

        # buyerDecisionMaker
        identifiers_df[
            PartiesFields.PARTIES_BUYER_DECISION_MAKER
        ] = self._make_buyer_identifier(
            df=df,
            buy_mask=buy_mask,
            path=PartiesFields.PARTIES_BUYER_DECISION_MAKER,
            params=params,
        )

        # buyerDecisionMakerFileIdentifier
        identifiers_df[
            PartiesFields.PARTIES_BUYER_DECISION_MAKER_FILE_ID
        ] = self._make_buyer_file_identifier(df=df, buy_mask=buy_mask, params=params)

        # counterparty
        identifiers_df[
            PartiesFields.PARTIES_COUNTERPARTY
        ] = self._make_counterparty_identifier(df=df)

        # counterpartyFileIdentifier
        identifiers_df[
            PartiesFields.PARTIES_CP_FILE_ID
        ] = self._make_counterparty_file_identifier(df=df)

        # executing entity
        identifiers_df[
            PartiesFields.PARTIES_EXECUTING_ENTITY
        ] = self._make_executing_entity_identifier(df=df, params=params)

        # executingEntityFileIdentifier
        identifiers_df[PartiesFields.PARTIES_EXEC_ENTITY_FILE_ID] = df[params.lei_col]

        # seller
        identifiers_df[PartiesFields.PARTIES_SELLER] = self._make_seller_identifier(
            df=df, sell_mask=sell_mask, path=PartiesFields.PARTIES_SELLER, params=params
        )

        # sellerFileIdentifier
        identifiers_df[
            PartiesFields.PARTIES_SELLER_FILE_ID
        ] = self._make_seller_file_identifier(df=df, sell_mask=sell_mask, params=params)

        # sellerDecisionMaker
        identifiers_df[
            PartiesFields.PARTIES_SELLER_DECISION_MAKER
        ] = self._make_seller_identifier(
            df=df,
            sell_mask=sell_mask,
            path=PartiesFields.PARTIES_SELLER_DECISION_MAKER,
            params=params,
        )

        # sellerDecisionMakerFileIdentifier
        identifiers_df[
            PartiesFields.PARTIES_SELLER_DECISION_MAKER_FILE_ID
        ] = self._make_seller_file_identifier(df=df, sell_mask=sell_mask, params=params)

        cols_to_merge = [
            PartiesFields.PARTIES_BUYER,
            PartiesFields.PARTIES_BUYER_DECISION_MAKER,
            PartiesFields.PARTIES_EXECUTING_ENTITY,
            PartiesFields.PARTIES_SELLER,
            PartiesFields.PARTIES_SELLER_DECISION_MAKER,
            PartiesFields.PARTIES_COUNTERPARTY,
        ]

        identifiers_df.loc[
            :, PartiesFields.MARKET_IDENTIFIERS_PARTIES
        ] = identifiers_df[cols_to_merge].apply(lambda x: x.dropna().tolist(), axis=1)

        target_col_list = [
            PartiesFields.MARKET_IDENTIFIERS_PARTIES,
            PartiesFields.PARTIES_EXEC_ENTITY_FILE_ID,
            PartiesFields.PARTIES_BUYER_FILE_ID,
            PartiesFields.PARTIES_SELLER_FILE_ID,
            PartiesFields.PARTIES_CP_FILE_ID,
            PartiesFields.PARTIES_SELLER_DECISION_MAKER_FILE_ID,
            PartiesFields.PARTIES_BUYER_DECISION_MAKER_FILE_ID,
        ]

        return identifiers_df[target_col_list]

    @staticmethod
    def _add_prefix_to_ids(df: pd.DataFrame) -> pd.DataFrame:
        id_mask = df[ATFXMT4Columns.Login].notnull()
        if id_mask.any():
            df.loc[id_mask, ATFXMT4Columns.Login] = (
                df.loc[id_mask, ATFXMT4Columns.Login]
                .dropna()
                .apply(lambda x: f"id:{x}")
            )

        return df

    @staticmethod
    def _make_buyer_identifier(
        df: pd.DataFrame, buy_mask: pd.Series, path: str, params: Params
    ) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)

        if buy_mask.any():
            result.loc[buy_mask] = df.loc[buy_mask, params.lei_col].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=path,
                    type=IdentifierType.ARRAY,
                ).dict(by_alias=True)
            )

        not_buy_mask = ~buy_mask & df[ATFXMT4Columns.Login].notnull()
        if (not_buy_mask).any():
            result.loc[not_buy_mask] = df.loc[not_buy_mask, ATFXMT4Columns.Login].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=path,
                    type=IdentifierType.ARRAY,
                ).dict(by_alias=True)
            )

        return result

    @staticmethod
    def _make_buyer_file_identifier(
        df: pd.DataFrame, buy_mask: pd.Series, params: Params
    ) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)

        if buy_mask.any():
            result.loc[buy_mask] = df.loc[buy_mask, params.lei_col].dropna().str.lower()

        not_buy_mask = ~buy_mask & df[ATFXMT4Columns.Login].notnull()
        if (not_buy_mask).any():
            result.loc[not_buy_mask] = (
                df.loc[not_buy_mask, ATFXMT4Columns.Login].dropna().str.lower()
            )

        return result

    @staticmethod
    def _make_counterparty_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)

        # Removed temporarily waiting on https://steeleye.atlassian.net/browse/DE-225
        # Must be able to add MarketPersons as Counterparty for this to work
        # mask = df[ATFXMT4Columns.Login].notnull()
        #
        # if mask.any():
        #     result.loc[mask] = df.loc[mask, ATFXMT4Columns.Login].apply(
        #         lambda x: Identifier(
        #             labelId=x.lower(),
        #             path=PartiesFields.PARTIES_COUNTERPARTY,
        #             type=IdentifierType.OBJECT,
        #         ).dict(by_alias=True)
        #     )

        return result

    @staticmethod
    def _make_counterparty_file_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)

        mask = df[ATFXMT4Columns.Login].notnull()

        if mask.any():
            result.loc[mask] = df.loc[mask, ATFXMT4Columns.Login].str.lower()

        return result

    @staticmethod
    def _make_executing_entity_identifier(
        df: pd.DataFrame, params: Params
    ) -> pd.Series:
        result = df[params.lei_col].apply(
            lambda x: Identifier(
                labelId=x.lower(),
                path=PartiesFields.PARTIES_EXECUTING_ENTITY,
                type=IdentifierType.OBJECT,
            ).dict(by_alias=True)
        )

        return result

    @staticmethod
    def _make_executing_entity_file_identifier(
        df: pd.DataFrame, params: Params
    ) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)

        result.loc[:] = df[params.lei_col]

        return result

    @staticmethod
    def _make_seller_identifier(
        df: pd.DataFrame, sell_mask: pd.Series, path: str, params: Params
    ) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)

        if sell_mask.any():
            result.loc[sell_mask] = df.loc[sell_mask, params.lei_col].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=path,
                    type=IdentifierType.ARRAY,
                ).dict(by_alias=True)
            )

        parties_seller_not_sell_mask = ~sell_mask & df[ATFXMT4Columns.Login].notnull()

        if parties_seller_not_sell_mask.any():
            result.loc[parties_seller_not_sell_mask] = df.loc[
                parties_seller_not_sell_mask, ATFXMT4Columns.Login
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=path,
                    type=IdentifierType.ARRAY,
                ).dict(by_alias=True)
            )

        return result

    @staticmethod
    def _make_seller_file_identifier(
        df: pd.DataFrame, sell_mask: pd.Series, params: Params
    ) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)

        if sell_mask.any():
            result.loc[sell_mask] = df.loc[sell_mask, params.lei_col].str.lower()

        parties_seller_not_sell_mask = ~sell_mask & df[ATFXMT4Columns.Login].notnull()

        if parties_seller_not_sell_mask.any():
            result.loc[parties_seller_not_sell_mask] = df.loc[
                parties_seller_not_sell_mask, ATFXMT4Columns.Login
            ].str.lower()

        return result
