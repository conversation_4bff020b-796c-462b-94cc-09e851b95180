from typing import Optional

import pandas as pd
from pydantic import Field
from se_elastic_schema.components.market.identifier import Identifier
from se_elastic_schema.static.market import IdentifierType
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.orders.data_source.atfx.mt4.html.identifiers.static import (
    ATFXMT4Columns,
)

INSTRUMENT_PATH = "instrumentDetails.instrument"
TARGET_COLUMN = "marketIdentifiers.instrument"


class Params(BaseParams):
    asset_class_identifier: str = Field(
        ...,
        description="Instrument asset class",
    )
    venue_identifier: str = Field(
        ...,
        description="Instrument venue",
    )


class InstrumentIdentifiers(TransformBaseTask):
    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        **kwargs,
    ) -> pd.DataFrame:

        cols_used = [ATFXMT4Columns.SYMBOL]
        target = pd.DataFrame(index=source_frame.index, columns=[TARGET_COLUMN])

        if source_frame.empty:
            return target

        cols_mask = source_frame.columns.isin(cols_used)

        df = source_frame.loc[:, cols_mask]

        for col in cols_used:
            if col not in source_frame.columns:
                df[col] = pd.NA

        df[ATFXMT4Columns.SYMBOL] = df[ATFXMT4Columns.SYMBOL].apply(self.clean)

        spot_mask = (df[ATFXMT4Columns.SYMBOL].str.len() == 6) & (
            df[ATFXMT4Columns.SYMBOL].str.isalpha()
        )

        if spot_mask.any():
            df.loc[spot_mask, TARGET_COLUMN] = (
                df.loc[spot_mask, ATFXMT4Columns.SYMBOL]
                .dropna()
                .apply(
                    lambda x: [
                        f"{params.venue_identifier}{x}{params.asset_class_identifier}".upper()
                    ]
                )
            )

            target = (
                df[TARGET_COLUMN]
                .dropna()
                .apply(
                    lambda x: list(
                        map(
                            lambda y: Identifier(
                                labelId=y,
                                path=INSTRUMENT_PATH,
                                type=IdentifierType.OBJECT,
                            ).dict(by_alias=True),
                            x,
                        )
                    )
                )
            )
        return target

    @staticmethod
    def clean(val: str) -> Optional[str]:
        if pd.isnull(val):
            return

        return "".join([c for c in val if c.isalnum()][:6])
