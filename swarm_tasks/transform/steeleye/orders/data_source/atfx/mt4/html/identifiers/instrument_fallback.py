import json
from typing import Optional
from typing import <PERSON>Var
from typing import Union

import pandas as pd
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.orders.data_source.atfx.mt4.html.identifiers.static import (
    ATFXMT4Columns,
)

INSTRUMENT_PATH = "instrumentDetails.instrument"
PDNA = TypeVar("pandas.pd.NA")


class InstrumentFallBack(TransformBaseTask):
    """This task generates instrument data for the rows from the custom tenant
    instrument cache"""

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        extractor_result: ExtractPathResult = None,
        params: Optional[BaseParams] = None,
        resources: Optional[BaseResources] = None,
        **kwargs
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index, columns=[INSTRUMENT_PATH])

        if source_frame.empty:
            return target

        cols_used = [
            ATFXMT4Columns.SYMBOL,
            INSTRUMENT_PATH,
        ]
        cols_mask = source_frame.columns.isin(cols_used)

        df = source_frame.loc[:, cols_mask]

        for col in cols_used:
            if col not in df.columns:
                df[col] = pd.NA

        df[ATFXMT4Columns.SYMBOL] = df[ATFXMT4Columns.SYMBOL].apply(self.clean)

        instrument_cache = self.__read_instrument_cache(extractor_result)
        fallback_mask = (
            df[INSTRUMENT_PATH].isnull() & df[ATFXMT4Columns.SYMBOL].notnull()
        )
        df.loc[fallback_mask, INSTRUMENT_PATH] = df.loc[
            fallback_mask, ATFXMT4Columns.SYMBOL
        ].apply(lambda x: self.__get_instrument_data(x, instrument_cache))

        target = df[INSTRUMENT_PATH].to_frame()
        return target

    @staticmethod
    def __read_instrument_cache(extractor_result: ExtractPathResult) -> dict:
        with open(extractor_result.path) as cache_data:
            data = json.load(cache_data)
        return data

    @staticmethod
    def __get_instrument_data(symbol: str, instrument_cache: dict) -> Union[dict, PDNA]:
        symbol = symbol.split(".")[0].lower()
        for rec in instrument_cache["records"]:
            add_ids = rec["ext"].get("additionalIdentifiers")
            if not add_ids:
                continue
            normalised_add_id = {k.lower(): v.lower() for k, v in add_ids.items()}
            for key, value in normalised_add_id.items():
                if "mt4" in key and value == symbol:
                    return rec
        return pd.NA

    @staticmethod
    def clean(val: str) -> Optional[str]:
        if pd.isnull(val):
            return
        val = val.replace("-", ".")
        val = val[1:].split(".")[0] if val.startswith(".") else val.split(".")[0]
        val = "".join([c for c in val if c.isalnum()])
        for replace_str in ("cash", "pro", "vip"):
            val = val.replace(replace_str, "")

        return val
