import pandas as pd
from se_trades_tasks.order_and_tr.instrument.identifiers.merge_market_identifiers import (
    Params as GenericParams,
)
from se_trades_tasks.order_and_tr.instrument.identifiers.merge_market_identifiers import (
    run_merge_market_identifiers,
)
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class MergeMarketIdentifiers(TransformBaseTask):

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            params=params,
            auditor=self.auditor,
            **kwargs,
        )

    @classmethod
    def process(
        cls, source_frame: pd.DataFrame, params: GenericParams, auditor=None, **kwargs
    ) -> pd.DataFrame:

        return run_merge_market_identifiers(
            source_frame=source_frame,
            params=params,
            auditor=auditor,
        )
