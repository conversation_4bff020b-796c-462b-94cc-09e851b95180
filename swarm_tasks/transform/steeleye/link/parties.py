import pandas as pd
from se_trades_tasks.order_and_tr.party.link_parties import Params as GenericParams
from se_trades_tasks.order_and_tr.party.link_parties import run_link_parties
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class Resources(BaseResources):
    es_client_key: str


class LinkParties(TransformBaseTask):
    params_class = Params
    resources_class = Resources

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: Resources = None,
        **kwargs,
    ) -> pd.DataFrame:
        es_client = self.clients.get(resources.es_client_key)
        tenant: str = Settings.tenant
        return run_link_parties(
            source_frame=source_frame,
            params=params,
            es_client=es_client,
            tenant=tenant,
            **kwargs,
        )
