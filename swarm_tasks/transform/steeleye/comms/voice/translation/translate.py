import textwrap

import boto3
import pandas as pd
from prefect import context
from pydantic import Field
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.comms.voice.transcription.static import (
    TranscriptionFields,
)
from swarm_tasks.transform.steeleye.comms.voice.transcription.utils import (
    get_voice_config,
)


class Params(BaseParams):
    config_path: str = Field(
        "config/transcription/voice.json",
        description="s3 path with transcription configuration",
    )

    # this is the separator between translations on the same text field
    # ex: ----------en.GB----------
    separator: str = Field(
        "-" * 10 + " {} " + "-" * 10, description="separator between texts "
    )

    width: int = 4500


logger = context.get("logger")


class AddTranslation(TransformBaseTask):
    """process the data from the transcription folder and updates the record on elastic"""

    params_class = Params

    def execute(
        self, source_frame: pd.DataFrame = None, params: Params = None, **kwargs
    ) -> pd.DataFrame:

        global logger

        logger = context.get("logger")

        config = get_voice_config(bucket=Settings.realm, config_path=params.config_path)

        if not config.translation:
            return source_frame

        client = boto3.client("translate")

        cols_used = [
            TranscriptionFields.RECORD_ID,
            TranscriptionFields.TRANSCRIPTION,
            TranscriptionFields.LANGUAGE,
        ]

        cols_mask = source_frame.columns.isin(cols_used)

        source_frame.loc[:, TranscriptionFields.TRANSCRIPTION] = source_frame.loc[
            :, cols_mask
        ].apply(
            self._translate,
            languages=config.translation,
            client=client,
            param=params,
            axis=1,
        )

        return source_frame

    @staticmethod
    def _translate(
        row: pd.Series, languages: list, client: boto3.client, param: Params
    ) -> str:
        """
        do the translation from the text
        :param row: pd.DataFrame input data
        :param languages: list languages to translate
        :param client: boto3.client translation client
        :param param: Params input parameters
        :return str: merged translated text
        """
        transcription = (
            row[TranscriptionFields.TRANSCRIPTION]
            if not pd.isna(row[TranscriptionFields.TRANSCRIPTION])
            else ""
        )

        if not transcription:
            return pd.NA

        text_lines = textwrap.wrap(transcription, width=param.width)

        final = [transcription]

        for language in languages:
            if language == row[TranscriptionFields.LANGUAGE]:
                continue

            translated = [param.separator.format(language), "\n"]

            for line in text_lines:
                try:
                    translated_text = client.translate_text(
                        Text=line,
                        SourceLanguageCode=AddTranslation._root_language(
                            row[TranscriptionFields.LANGUAGE]
                        ),
                        TargetLanguageCode=AddTranslation._root_language(language),
                    )
                    translated.append(
                        translated_text[TranscriptionFields.TRANSLATED_TEXT]
                    )
                except Exception as e:
                    translated_text = f"Translation unavailable for record {row[TranscriptionFields.RECORD_ID]}."
                    translated.append(translated_text)
                    logger.warning(f"{translated_text}, {e}")

            final.append("\n".join(translated))

        return "\n".join(final)

    @staticmethod
    def _root_language(language: str) -> str:
        """
        returns the root language from complex input
        :params language: str complex input language
        :return str: root language
        """

        return language.split("-")[0]
