from datetime import datetime
from datetime import timed<PERSON><PERSON>
from pathlib import Path
from typing import Any
from typing import Optional

import boto3
import pandas as pd
from boto3 import Session
from pydantic import Field
from se_elastic_schema.models import Call
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import BaseTask

from swarm_tasks.transform.steeleye.comms.voice.transcription.static import (
    AWSTranscribeFields,
)
from swarm_tasks.transform.steeleye.comms.voice.transcription.static import (
    TranscriptionFields,
)
from swarm_tasks.transform.steeleye.comms.voice.transcription.static import (
    TranscriptionRules,
)
from swarm_tasks.transform.steeleye.comms.voice.transcription.utils import (
    get_voice_config,
)
from swarm_tasks.transform.steeleye.comms.voice.transcription.utils import (
    TranscriptionJobNameEncoderDecoder,
)


class Params(BaseParams):
    aws_config_path: str = Field(
        "config/transcription/voice.json",
        description="s3 path with transcription configuration. Only used for AWS Transcribe",
    )

    aws_transcription_path: str = Field(
        "transcription/in",
        description="s3 path for transcription file. Only used for AWS Transcribe",
    )

    aws_region: str = Field(
        "https://s3-eu-west-1.amazonaws.com/",
        description="base region name for the s3 data. Only used for AWS Transcribe",
    )

    audio_format_from_call: Optional[bool] = Field(
        False,
        description="if true, transcription audio format will be taken from the call instead of params.audio_format",
    )


class Resources(BaseResources):
    es_client_key: str


class AwsTranscribeScheduler(BaseTask):

    params_class = Params
    resources_class = Resources

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        es_client: Any = None,
        realm: str = None,
        tenant: str = None,
        **kwargs,
    ):

        session = Session()
        transcribe = session.client("transcribe")

        config = get_voice_config(bucket=realm, config_path=params.aws_config_path)

        self.logger.info(config)

        if source_frame.empty or not config.enabled:
            self.logger.warning(
                f"Source frame has {len(source_frame)} "
                f"records and transcription enabled= {config.enabled} "
                f"Transcription SKIPPED. Task exited with no errors"
            )
            return pd.DataFrame()

        cols_used = [
            TranscriptionFields.ATTACHMENT_BUCKET,
            TranscriptionFields.FROM,
            TranscriptionFields.TO,
            TranscriptionFields.ATTACHMENT_KEY,
            TranscriptionFields.DURATION,
        ]
        if params.audio_format_from_call:
            if TranscriptionFields.ATTACHMENT_FILE_TYPE not in source_frame.columns:
                raise ValueError(
                    f"Cannot use param 'audio_format_from_call' when {TranscriptionFields.ATTACHMENT_FILE_TYPE} "
                    f"is missing from source_frame columns"
                )

            cols_used.append(TranscriptionFields.ATTACHMENT_FILE_TYPE)

        cols_mask = source_frame.columns.isin(cols_used)
        df = source_frame.loc[:, cols_mask]
        if (
            params.audio_format_from_call
            and TranscriptionFields.ATTACHMENT_FILE_TYPE in df.columns
        ):
            # strip "." from fileType. for calls where value is derived using pathlib.Path().suffix e.g. .mp3 -> mp3
            df[TranscriptionFields.ATTACHMENT_FILE_TYPE] = df[
                TranscriptionFields.ATTACHMENT_FILE_TYPE
            ].str.strip(".")

        self.logger.info(df)

        if config.allowance:
            allowance = timedelta(
                **{config.allowance.periodicity: config.allowance.quantity}
            )
            used_delta = self._get_transcription_usage(
                es_client=es_client, tenant=tenant
            )

            self.logger.info(used_delta)

            if used_delta >= allowance:
                self.logger.warning(
                    f"Max translation time reached. Used: {used_delta}, Allowance: {allowance}"
                )

                return pd.DataFrame()
            df = self._transcription_schedule_filter(
                df=df, current=used_delta, allowance=allowance
            )

        if config.whitelist:
            whitelist_mask = df[TranscriptionFields.FROM].isin(config.whitelist)
            df = df.loc[whitelist_mask]

        if config.blacklist:
            blacklist_mask = df[TranscriptionFields.TO].apply(
                lambda x: True
                if len(set(x).intersection(set(config.blacklist))) > 0
                else False
            )
            df = df.loc[~blacklist_mask]

        queued = df.apply(
            self._queue_calls,
            config=config,
            client=transcribe,
            params=params,
            axis=1,
        )

        self.logger.info(f"ranscription queue finished. All Success: {all(queued)}")

        return queued

    @staticmethod
    def fmt_call_duration(duration: str) -> str:
        """
        formats the duration fields to the correct time format
        :param duration: str time duration representation
        t"""
        duration = str(duration).split(".")[0]
        if len(duration) == 5:
            duration = duration + ":00"  # hh:mm -> hh:mm:ss
        if len(str(duration)) <= 4:
            return "00:00:00"
        return duration

    @staticmethod
    def _get_transcription_usage(es_client: Any, tenant: str) -> timedelta:
        """
        queries elasticsearch for all time spent in transcriptions for the period

        :param es_client: ElasticSearch client
        :return tuple(int, list): quantity of seconds used and a list of all timedeltas
        """
        # creates a datetime object from first time and day of currenty month and year
        date_from = datetime.now().replace(
            day=1, hour=0, minute=0, second=0, microsecond=0
        )

        body = {
            "_source": {"includes": [TranscriptionFields.DURATION]},
            "size": es_client.MAX_QUERY_SIZE,
            "query": {
                "bool": {
                    "filter": [
                        {"range": {"&timestamp": {"gte": date_from}}},
                        {"terms": {es_client.meta.model: [TranscriptionFields.MODEL]}},
                        {"exists": {"field": TranscriptionFields.TEXT}},
                        {"exists": {"field": TranscriptionFields.DURATION}},
                    ]
                }
            },
        }

        results = es_client.scroll(
            query=body, index=Call.get_elastic_index_alias(tenant=tenant)
        )

        if results.empty:
            return timedelta()

        results[TranscriptionFields.DURATION] = results[
            TranscriptionFields.DURATION
        ].apply(AwsTranscribeScheduler.fmt_call_duration)
        results[TranscriptionFields.DURATION] = pd.to_timedelta(
            results[TranscriptionFields.DURATION]
        )

        return results[TranscriptionFields.DURATION].sum()

    @staticmethod
    def _transcription_schedule_filter(
        df: pd.DataFrame, current: timedelta, allowance: timedelta
    ) -> pd.Series:
        """
        transcription allowance validation pre queuing process by record by cumulative sum of durations
        NOTE: this process may allow more audios being schedule due the paralel processing between batches
        :param df: pd.Series of duration of audio files
        :param current: timedelta, current used allowance
        :param allowance: timedelta, max time allowed
        :return pd.Series: containing the schedule flag
        """

        remaining = allowance - current

        df[TranscriptionFields.DURATION] = df[TranscriptionFields.DURATION].apply(
            AwsTranscribeScheduler.fmt_call_duration
        )
        df[TranscriptionFields.DURATION] = pd.to_timedelta(
            df[TranscriptionFields.DURATION]
        )

        df["tmp"] = df[TranscriptionFields.DURATION].cumsum()

        return df.loc[df["tmp"] < remaining]

    def _queue_calls(
        self,
        voice: pd.Series,
        config: TranscriptionRules,
        client: boto3.client,
        params,  # TranscriptionRouter.params,
    ) -> bool:
        """
        Format and queue the voice files to transcription

        :param voice: pd.Series voice transcription data
        :param config: TranscriptionRules rules for the transcription
        :return: bool
        """
        if (
            TranscriptionFields.ATTACHMENT_BUCKET not in voice.index
            or TranscriptionFields.ATTACHMENT_KEY not in voice.index
            or pd.isna(voice[TranscriptionFields.ATTACHMENT_BUCKET])
            or pd.isna(voice[TranscriptionFields.ATTACHMENT_KEY])
        ):
            self.logger.warning(f"No bucket/key found for {voice.to_list()}")
            return False
        try:
            self.logger.info(
                f"queuing voice file: {voice[TranscriptionFields.ATTACHMENT_BUCKET]}/"
                f"{voice[TranscriptionFields.ATTACHMENT_KEY]}"
            )

            audio_url = (
                f"{params.aws_region}{voice[TranscriptionFields.ATTACHMENT_BUCKET]}/"
                f"{voice[TranscriptionFields.ATTACHMENT_KEY]}"
            )
            encoded_job_name = TranscriptionJobNameEncoderDecoder.encode_job_name(
                amp_id=voice[TranscriptionFields.ATTACHMENT_KEY]
            )
            if config.languageDetection:
                # e.g. transcription/in/NzM4NjJlYTktN2U0NC00MDgxLWI4N2YtMDdkOTRhODIwYWE4.auto

                output_key: str = (
                    Path(params.aws_transcription_path, encoded_job_name)
                    .with_suffix(".auto")
                    .as_posix()
                )

                transcription_parameters = dict(
                    TranscriptionJobName=encoded_job_name,
                    Media={AWSTranscribeFields.MEDIAFILEURI: audio_url},
                    OutputBucketName=voice[TranscriptionFields.ATTACHMENT_BUCKET],
                    OutputKey=output_key,
                    IdentifyLanguage=True,
                )

                if config.languageOptions:
                    transcription_parameters[
                        TranscriptionFields.LANGUAGE_OPTIONS
                    ] = config.languageOptions

                transcription_job = client.start_transcription_job(
                    **transcription_parameters
                )

            else:
                # e.g. transcription/in/NzM4NjJlYTktN2U0NC00MDgxLWI4N2YtMDdkOTRhODIwYWE4.en-GB
                output_key = (
                    Path(params.aws_transcription_path, encoded_job_name)
                    .with_suffix(f".{config.baseLanguage}")
                    .as_posix()
                )

                transcription_job = client.start_transcription_job(
                    TranscriptionJobName=encoded_job_name,
                    Media={AWSTranscribeFields.MEDIAFILEURI: audio_url},
                    OutputBucketName=voice[TranscriptionFields.ATTACHMENT_BUCKET],
                    OutputKey=output_key,
                    LanguageCode=config.baseLanguage,
                )

            self.logger.info(transcription_job)

            return True

        except Exception as e:
            self.logger.warning(
                f"Transcription id {voice[TranscriptionFields.ATTACHMENT_KEY]} failed. traceback {e}"
            )
            return False
