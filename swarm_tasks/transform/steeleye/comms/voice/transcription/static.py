from typing import List
from typing import Optional

from pydantic import BaseModel
from pydantic import Field
from typing_extensions import Literal


class AWSDynamoFields:
    BUCKET = "bucket"
    ITEMS = "Items"
    PARAM = "dynamo"
    PATH = "path"


class AWSS3Fields:
    BUCKET = "Bucket"
    CONTENTS = "Contents"
    KEY = "Key"
    PARAM = "s3"


class AWSTranscribeFields:
    BODY = "body"
    LANGUAGECODE = "LanguageCode"
    MEDIAFILEURI = "MediaFileUri"
    RESULTS = "results"
    TRANSCRIPT = "transcript"
    TRANSCRIPTIONJOB = "TranscriptionJob"
    TRANSCRIPTS = "transcripts"
    RESULTLANGUAGE = "language_code"


class TranscriptionFields:
    ALTERNATIVES = "alternatives"
    ATTACHMENT_BUCKET = "voiceFile.fileInfo.location.bucket"
    ATTACHMENT_FILE_TYPE = "voiceFile.fileType"
    ATTACHMENT_KEY = "voiceFile.fileInfo.location.key"
    AUTO = "auto"
    BODY = "body"
    BODY_DISPLAY_TEXT = "body.displayText"
    BODY_TEXT = "body.text"
    BODY_TYPE = "body.type"
    CONFIDENCE = "confidence"
    DESCRIPTION = "description"
    DURATION = "callDuration"
    END = "end"
    FROM = "identifiers.fromId"
    IDENTIFIERS_FROM_ID = "identifiers.fromId"
    LANGUAGE = "language"
    LANGUAGE_OPTIONS = "LanguageOptions"
    METADATA_CREATED = "metadata.created"
    METADATA_REQUEST_ID = "metadata.request_id"
    MODEL = "Call"
    PARTICIPANTS = "participants"
    PUNCTUATED_WORD = "punctuated_word"
    RECORD_ID = "&id"
    RECORD_KEY = "&key"
    RESULTS_CHANNELS = "results.channels"
    SOURCE_AUDIO_LANGUAGE = "sourceAudioLanguage"
    SPEAKER = "speaker"
    START = "start"
    STATUS = "status"
    TEXT = "body.text"
    TIME = "time"
    TO = "identifiers.toIds"
    TRANSCRIBED = "transcribed"
    TRANSCRIPT = "transcript"
    TRANSCRIPTION = "transcription"
    TRANSCRIPTION_SCHEDULE = "transcription_schedule"
    TRANSCRIPTION_STATUS = "transcriptionStatus"
    TRANSCRIPTION_STATUS_DESCRIPTION = "transcriptionStatus.description"
    TRANSCRIPTS = "transcripts"
    TRANSLATED_TEXT = "TranslatedText"
    TRANSLATION = "translation"
    VENDOR = "vendor"
    WORD = "word"
    WORDS = "words"


Languages = Literal[
    "ar-AE",
    "ar-SA",
    "de-CH",
    "de-DE",
    "en-AB",
    "en-AU",
    "en-GB",
    "en-IE",
    "en-IN",
    "en-US",
    "en-WL",
    "es-ES",
    "es-US",
    "fa-IR",
    "fr-CA",
    "fr-FR",
    "he-IL",
    "hi-IN",
    "id-ID",
    "it-IT",
    "ja-JP",
    "ko-KR",
    "ms-MY",
    "nl-NL",
    "pt-BR",
    "pt-PT",
    "ru-RU",
    "ta-IN",
    "te-IN",
    "tr-TR",
    "zh-CN",
]

Periodicity = Literal["seconds", "minutes", "hours", "days"]


class Allowance(BaseModel):
    periodicity: Periodicity = Field(None)
    quantity: int = Field(
        ..., description="quantity of allowed seconds during the period"
    )


class TranscriptionRules(BaseModel):
    """
    Basic configuration model
    """

    enabled: bool = Field(False)
    languageDetection: bool = Field(False)
    whitelist: List[str] = Field(default_factory=list)
    blacklist: List[str] = Field(default_factory=list)
    allowance: Optional[Allowance] = Field(None)
    baseLanguage: Languages = Field("en-GB")
    translation: Optional[List[Languages]] = Field(None)
    languageOptions: Optional[List[Languages]] = Field(None)
