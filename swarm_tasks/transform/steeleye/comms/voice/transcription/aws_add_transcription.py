import json
from pathlib import Path
from typing import Dict
from typing import List

import pandas as pd
from boto3.dynamodb.conditions import Key
from boto3.session import Session
from prefect import context
from prefect.engine.signals import SKIP
from pydantic import Field
from pydantic import root_validator
from pydantic import validator
from se_core_tasks.core.core_dataclasses import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ult
from se_core_tasks.utils.create_batches import create_batches
from se_elastic_schema.models import Call
from smart_open import open
from swarm.conf import Settings
from swarm.schema.static import SwarmColumns
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import BaseTask
from typing_extensions import Literal

from swarm_tasks.transform.steeleye.comms.voice.transcription.static import (
    AWSDynamoFields,
)
from swarm_tasks.transform.steeleye.comms.voice.transcription.static import AWSS3Fields
from swarm_tasks.transform.steeleye.comms.voice.transcription.static import (
    AWSTranscribeFields,
)
from swarm_tasks.transform.steeleye.comms.voice.transcription.static import (
    TranscriptionFields,
)
from swarm_tasks.transform.steeleye.comms.voice.transcription.utils import (
    TranscriptionJobNameEncoderDecoder,
)

READFROM_OPTIONS = Literal["s3", "dynamo"]
EXTENSION_AUTO = "auto"
AWS_WRITE_ACCESS_TEST_FILE = ".write_access_check_file.temp"


class TempColumns:
    DECODED_JOB_NAME = "decoded_job_name"
    ID = "_id"


class Params(BaseParams):

    config_path: str = Field(
        "config/transcription/voice.json",
        description="s3 path with transcription configuration",
    )

    transcription_path: str = Field(
        "transcription/in", description="s3 path for transcription files"
    )

    processed_path: str = Field(
        "transcription/processed", description="s3 path for processed files"
    )

    read_from: READFROM_OPTIONS = Field(
        "s3", description="reads the file list from S3 or dynamodb"
    )

    dynamodb_table: str = Field(
        "voice_transcriptions", description="dynamodb table containing the file list"
    )

    skip_transcribed: bool = Field(
        True, description="skip all already transcribed records"
    )

    chunksize: int = 1000

    target_delimiter: str = ","

    @validator("transcription_path", always=True)
    def ensure_trailling_slash(cls, v):
        if not v.endswith("/"):
            return v + "/"
        return v

    @validator("processed_path", always=True)
    def ensure_trailling_slash2(cls, v):
        if not v.endswith("/"):
            return v + "/"
        return v

    @root_validator
    def dynamo_fields(cls, values):
        read_from = values.get("read_from")
        dynamodb_table = values.get("dynamodb_table")

        if read_from == "s3":
            return values

        if read_from == "dynamo" and not dynamodb_table:
            raise AttributeError(
                "`dynamodb_table` " "must be provided when `read_from` is `dynamo`"
            )

        return values


class Resources(BaseResources):
    es_client_key: str


logger = context.get("logger")


class AwsAddTranscription(BaseTask):
    """
    Adds valid transcription data to the flow. This task fetches the data from
    processed/in (default value and can be passed by bundle) decodes the file name
    using base64 and tries to match it with voice file location key. The trancriptions
    are then linked to the resp records. This is also backwards-compatible to match the
    decoded filename (job name) to the call &id as was being used to encode in swarm-tasks
    version prior to 3.21.1
    """

    params_class = Params
    resources_class = Resources

    def execute(
        self, params: Params = None, resources: Resources = None, **kwargs
    ) -> List[FileSplitterResult]:
        return self.process(params=params, resources=resources)

    @classmethod
    def process(
        cls,
        params: Params = None,
        resources: Resources = None,
    ) -> List[FileSplitterResult]:
        """
        This task searches for AWS Transcription Job files in "transcription/in" and
        Call records in elasticsearch with the same Ids and merges the data into a
        dataframe, which is turned into batches

        :param params: Params instance with required paths and flags
        :param resources: Resource instance with es client key as tenant-data
        :return:
        """

        global logger

        logger = context.get("logger")

        session = Session()
        transcribe = session.client("transcribe")
        s3 = session.client("s3")
        dynamo = session.resource("dynamodb")
        es = Settings.connections.get(resources.es_client_key)

        results = []

        if params.read_from == AWSS3Fields.PARAM:
            transcription_list = cls._get_s3_transcription_list(
                params=params, s3_session=s3
            )
        else:
            transcription_list = cls._get_dynamo_transcription_list(
                params=params, dynamo=dynamo
            )

        # list objects fom s3 api also returns the key from the folder in contents
        if not transcription_list or transcription_list == [params.transcription_path]:
            raise SKIP("No files to add transcription")

        for path_key in transcription_list:
            logger.info(f"processing transcript file {path_key}")
            try:
                voice_id = (
                    TranscriptionJobNameEncoderDecoder.call_meta_id_from_output_key(
                        key=path_key
                    )
                )

                full_body = cls.open_trancription_file(
                    path_key=path_key, session=session
                )

                body = full_body[AWSTranscribeFields.RESULTS][
                    AWSTranscribeFields.TRANSCRIPTS
                ][0][AWSTranscribeFields.TRANSCRIPT]

                results.append(
                    {
                        TempColumns.DECODED_JOB_NAME: voice_id,
                        TranscriptionFields.TRANSCRIPTION: body,
                        TranscriptionFields.TRANSCRIBED: True,
                        TranscriptionFields.LANGUAGE: cls._get_language(
                            path_key=path_key, full_body=full_body
                        ),
                    }
                )

                if params.read_from == AWSS3Fields.PARAM:
                    cls._move_s3_transcribe_file(params=params, s3=s3, path=path_key)
                else:
                    cls._delete_dynamo_path_record(
                        params=params, dynamo=dynamo, path=path_key
                    )

                # without deleting jobs expire after 89 days automatically
                # processed jobs not deleted clog up the jobs queue the legacy system looks at for processing
                cls._delete_aws_transcribe_job(transcribe=transcribe, path=path_key)

            except Exception as e:
                logger.warning(f"Transcription {path_key} failed. Traceback: {e}")

        df = pd.DataFrame(results)

        records = cls._get_records(
            es=es, ids=df[TempColumns.DECODED_JOB_NAME].to_list()
        )

        if records.empty:
            raise SKIP("no records to update")

        merged = cls._merge_records(es=es, df=df, records=records)

        if params.skip_transcribed:
            if TranscriptionFields.BODY_TEXT not in merged.columns:
                merged[TranscriptionFields.BODY_TEXT] = pd.NA

            not_transcribed_mask = merged[TranscriptionFields.BODY_TEXT].isnull()
            merged = merged.loc[not_transcribed_mask, :]

        # remove body columns
        merged = merged[
            [column for column in merged.columns if not column.startswith("body")]
        ]

        # remove any transcripts where a corresponding Call record cannot be found. See jira ON-2209
        call_record_not_found_mask = merged[TranscriptionFields.RECORD_KEY].isnull()
        if call_record_not_found_mask.any():
            logger.warning(
                f"{len(merged.loc[call_record_not_found_mask])}/{len(merged)} "
                f"transcripts cant be processed due to missing Call records"
            )
            merged = merged.loc[~call_record_not_found_mask, :]

        if merged.empty:
            raise SKIP("updating existing transcriptions is disabled.")

        source_dir = Path(Settings.context.sources_dir).resolve()

        return create_batches(
            df=merged,
            chunksize=params.chunksize,
            target_delimiter=params.target_delimiter,
            source_dir=source_dir,
            index_name=SwarmColumns.SWARM_RAW_INDEX,
        )

    @staticmethod
    def open_trancription_file(path_key: str, session: Session, **kwargs) -> Dict:
        """
        Opens the file from D3 given its path key and loads the result into a dictionary
        :param path_key: path key to S3 file
        :param session: botocore Session instance
        :return: dictionary with contents from file
        """
        with open(
            f"s3://{Settings.realm}/{path_key}",
            "rb",
            transport_params={"session": session},
        ) as content:
            return json.loads(content.read().decode())

    @staticmethod
    def _get_language(path_key: str, full_body: dict) -> str:
        """
        identifies the language from file and full_body identified language field

        :params path_key: str file name key
        :params full_body: dict from the transcribed data
        :return str: language detected
        """

        extension = path_key.split(".")[-1]
        if extension != EXTENSION_AUTO:
            return extension

        return full_body[AWSTranscribeFields.RESULTS][
            AWSTranscribeFields.RESULTLANGUAGE
        ]

    @staticmethod
    def _get_s3_transcription_list(params: Params, s3_session) -> List[str]:
        """
        return the list of paths for all new transcriptions
        :param params: Params input parameter object
        :param s3_session: boto3 s3 session
        :return list: list of dictionaries containing paths
        """

        texts_list = s3_session.list_objects_v2(
            Bucket=Settings.realm, Prefix=f"{params.transcription_path}"
        )

        return [
            x.get(AWSS3Fields.KEY)
            for x in texts_list.get(AWSS3Fields.CONTENTS, [])
            if not x.get(AWSS3Fields.KEY).endswith(AWS_WRITE_ACCESS_TEST_FILE)
        ]

    @staticmethod
    def _get_dynamo_transcription_list(params: Params, dynamo) -> List[str]:
        """
        return the list of paths for all new transcriptions ( can be moved to Kafka )
        :param params: Params input parameter object
        :param dynamo: boto3 dynamodb session
        :return list: list of dictionaries containing paths
        """

        table = dynamo.Table(params.dynamodb_table)

        text_list = table.query(
            KeyConditionExpression=Key(AWSDynamoFields.BUCKET).eq(Settings.realm)
        )

        return [
            x.get(AWSDynamoFields.PATH)
            for x in text_list.get(AWSDynamoFields.ITEMS, [])
        ]

    @staticmethod
    def _move_s3_transcribe_file(params: Params, s3, path: str):
        """
        moves processed files to processed folder
        :param params: Params input parameter object
        :param s3: boto3 s3 session
        :param path: path of the object to move
        """

        transcription_name = Path(path)

        # If the folder doesn't have the delete permission we should not error
        # but process them manually
        try:
            # copy the processed file
            s3.copy(
                {AWSS3Fields.BUCKET: Settings.realm, AWSS3Fields.KEY: path},
                Settings.realm,
                f"{params.processed_path}{transcription_name.name}",
            )
            # remove the processed file
            s3.delete_objects(
                Bucket=Settings.realm,
                Delete={"Objects": [{AWSS3Fields.KEY: path}]},
            )
        except Exception as e:
            logger.warning(f"Could not move file {e}")

    @staticmethod
    def _delete_aws_transcribe_job(transcribe: Session.client, path: str):
        """
        delete AWS transcribe job if it exists
        NOTE - deleting AWS transcribe job does NOT delete the S3 transcript file
        """

        job_name = Path(path).stem
        try:
            transcribe.delete_transcription_job(TranscriptionJobName=job_name)
        except Exception as e:
            if "The requested job couldn't be found" in str(e):
                # job has already been deleted and exception can be ignored
                pass
            else:
                raise e

    @staticmethod
    def _delete_dynamo_path_record(params: Params, dynamo, path: str):
        """
        deletes transcribed record form dynamodb table
        :param params: Params input parameter object
        :param dynamo: boto3 dynamodb session
        :param path: path of the object to delete
        """
        table = dynamo.Table(params.dynamodb_table)
        table.delete_item(
            Key={AWSDynamoFields.BUCKET: Settings.realm, AWSDynamoFields.PATH: path}
        )

    @staticmethod
    def _merge_records(es, df: pd.DataFrame, records: pd.DataFrame) -> pd.DataFrame:
        """
        Merge records from elastic and transcription service
        :param es: ElasticsearchClient instance
        :param df: pd.DataFrame from the transcription content
        :param records: pd.DataFrame with the voice record from elastic
        :return pd.DataFrame: merged frames
        """
        # If the call feeds is on swarm-tasks version older than 3.21.1 it'd still use encoding based on &id
        # But versions after 3.21.1 will use location of attachment for job name. Since, there's no way to find
        # if the transcription job name is based on &id or location key, merge operation will be performed
        # on both, The two results will always be mutually exclusive and hence concatenated at the end.

        # Step I: Find calls based on location key
        merge_on_location_key = pd.merge(
            df,
            records,
            how="left",
            left_on=[TempColumns.DECODED_JOB_NAME],
            right_on=[TranscriptionFields.ATTACHMENT_KEY],
        )

        # mask where location based match was not found
        location_key_match_not_found_mask = merge_on_location_key[
            TranscriptionFields.ATTACHMENT_KEY
        ].isnull()

        # Step: II
        # Try merge based on amp id for transcriptions where location key based match was not found
        # IMPORTANT: The index must be retained
        merge_on_amp_id = pd.merge(
            df[location_key_match_not_found_mask],
            records,
            how="left",
            left_on=[TempColumns.DECODED_JOB_NAME],
            right_on=[es.meta.id],
        ).set_index(df[location_key_match_not_found_mask].index)

        # Step III: Concat
        merged_df = pd.concat(
            [merge_on_location_key[~location_key_match_not_found_mask], merge_on_amp_id]
        )

        # Return merged df after dropping the decoded job name as it's no longer needed.
        decoded_job_name_col_mask = merged_df.columns.isin(
            [TempColumns.DECODED_JOB_NAME]
        )
        return merged_df.loc[:, ~decoded_job_name_col_mask].sort_index()

    @staticmethod
    def _get_records(es, ids: list) -> pd.DataFrame:
        """
        get elasticsearch call records from the transcription list
        :param es: ElasticsearchClient instance
        :param ids: list of ids to load
        :return pd.DataFrame: with the result query
        """

        fields = [TempColumns.ID, TranscriptionFields.ATTACHMENT_KEY]
        multi_match = [dict(multi_match=dict(query=i, fields=fields)) for i in ids]
        body = {
            "query": {"bool": {"should": multi_match}},
            "_source": {
                "includes": [
                    es.meta.id,
                    es.meta.hash,
                    es.meta.key,
                    es.meta.model,
                    TranscriptionFields.BODY,
                    TranscriptionFields.ATTACHMENT_KEY,
                ]
            },
        }

        tenant = Settings.tenant
        index = Call.get_elastic_index_alias(tenant=tenant)

        df = es.scroll(query=body, index=index, include_elastic_meta=False)

        return df
