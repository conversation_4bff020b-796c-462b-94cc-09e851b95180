import json
import os
from pathlib import Path

import numpy as np
import pandas as pd
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):
    source_attribute: str


class Reference:
    CODE = "instrumentClassification"
    SCHEMA = "schema"


class CFI:
    CATEGORY = "cfiCategory"
    GROUP = "cfiGroup"
    ATTR1 = "cfiAttribute1"
    ATTR2 = "cfiAttribute2"
    ATTR3 = "cfiAttribute3"
    ATTR4 = "cfiAttribute4"
    ALL = [CATEGORY, GROUP, ATTR1, ATTR2, ATTR3, ATTR4]


class Schema:
    CAT = "cat"
    GROUP = "group"
    ATTR1 = "attr1"
    ATTR2 = "attr2"
    ATTR3 = "attr3"
    ATTR4 = "attr4"


DIRECTORY = Path(os.path.dirname(__file__))
RESOURCE_PATH = DIRECTORY.joinpath("data/cfi-reference.json")


class CFIAttributesFromCFICode(TransformBaseTask):
    """
    Derives CFI attributes from the CFI code, based on the data found at
    ./data/cfireference.json
    """

    params_class = Params
    CFI_REFERENCE = json.load(RESOURCE_PATH.open())

    def execute(
        self, source_frame: pd.DataFrame = None, params: Params = None, resources=None
    ) -> pd.DataFrame:

        data = pd.DataFrame(columns=CFI.ALL, index=source_frame.index)
        data[CFI.ALL] = np.nan

        if params.source_attribute not in source_frame:
            return data

        # get valid cfi codes
        data[Reference.CODE] = source_frame.loc[
            self.valid_cfi_codes(source_frame[params.source_attribute]),
            params.source_attribute,
        ]

        if data[Reference.CODE].empty:
            return data

        data[Reference.SCHEMA] = (
            data[Reference.CODE].dropna().str[:2].map(self.CFI_REFERENCE)
        )
        data = data.join(pd.DataFrame(data[Reference.SCHEMA].dropna().to_list()))

        data[CFI.GROUP] = data[Schema.GROUP]
        data[CFI.CATEGORY] = data[Schema.CAT]

        data[CFI.ATTR1] = data[Reference.CODE].str[2]
        data[CFI.ATTR2] = data[Reference.CODE].str[3]
        data[CFI.ATTR3] = data[Reference.CODE].str[4]
        data[CFI.ATTR4] = data[Reference.CODE].str[5]

        data[CFI.ATTR1] = (
            data[[CFI.ATTR1, Schema.ATTR1]]
            .dropna()
            .apply(lambda x: x[Schema.ATTR1].get(x[CFI.ATTR1], np.nan), axis=1)
        )
        data[CFI.ATTR2] = (
            data[[CFI.ATTR2, Schema.ATTR2]]
            .dropna()
            .apply(lambda x: x[Schema.ATTR2].get(x[CFI.ATTR2], np.nan), axis=1)
        )
        data[CFI.ATTR3] = (
            data[[CFI.ATTR3, Schema.ATTR3]]
            .dropna()
            .apply(lambda x: x[Schema.ATTR3].get(x[CFI.ATTR3], np.nan), axis=1)
        )
        data[CFI.ATTR4] = (
            data[[CFI.ATTR4, Schema.ATTR4]]
            .dropna()
            .apply(lambda x: x[Schema.ATTR4].get(x[CFI.ATTR4], np.nan), axis=1)
        )

        return data[CFI.ALL]

    @staticmethod
    def valid_cfi_codes(codes: pd.Series) -> pd.Series:
        return (codes.str.len() == 6) & codes.str.isalpha()
