import numpy as np
import pandas as pd
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.reference.instrument.lme.tif import static


SOURCE_ATTRIBUTE = static.TIFColumns.UNDERLYING_ISIN
TARGET_ATTRIBUTE = "derivative.underlyingInstruments"
NESTED_KEY = "underlyingInstrumentCode"


class UnderlyingInstrumentIdCode(TransformBaseTask):
    """
    Maps underlying instrument for LME TIF,
    there is only ever one underlyingInstrumentCode
    """

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: BaseParams = None,
        resources: BaseResources = None,
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index)
        target[TARGET_ATTRIBUTE] = np.nan

        if SOURCE_ATTRIBUTE not in source_frame:
            return target

        data = source_frame[SOURCE_ATTRIBUTE]

        target[TARGET_ATTRIBUTE] = data.dropna().apply(lambda x: [{NESTED_KEY: x}])
        return target
