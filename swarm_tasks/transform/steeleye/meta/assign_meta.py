import asyncio
import concurrent.futures
import os
import time
from collections import namedtuple
from functools import partial
from functools import wraps
from traceback import format_exc
from typing import Dict
from typing import List
from typing import Optional
from typing import Type
from typing import Union

import addict
import numpy as np
import pandas as pd
import prefect
from indict.base import EMPTY_VALUES
from prefect.engine.signals import FAIL
from pydantic import Field
from pydantic.error_wrappers import ValidationError as PydanticValidationError
from se_elastic_schema.elastic_schema.core.steeleye_record_validation import (
    SteelEyeRecordValidationMixin,
)
from se_elastic_schema.elastic_schema.core.steeleye_record_validation import (
    SteelEyeRecordValidationRule,
)
from se_elastic_schema.elastic_schema.core.steeleye_record_validation import (
    SteelEyeValidationExecutionError,
)
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import (
    SteelEyeSchemaBaseModelES8,
)
from se_elastic_schema.models import find_model
from se_elastic_schema.models import TenantConfiguration
from se_elastic_schema.steeleye_record_validations.categories import flags
from se_schema_meta import HASH
from se_schema_meta import ID
from se_schema_meta import KEY
from se_schema_meta import MODEL
from se_schema_meta import PARENT
from se_schema_meta import TIMESTAMP
from se_schema_meta import UNIQUE_PROPS
from se_schema_meta import USER
from se_schema_meta import VALIDATION_ERRORS
from se_schema_meta import VERSION
from swarm.conf import Settings
from swarm.schema.static import SwarmColumns
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask
from swarm.utilities.indict import Indict

from swarm_tasks.utilities.audit import audit_pydantic_validation_errors

MAX_THREADS = os.getenv("MAX_THREADS", 2)

TEMP_NULL = "__TEMP_NULL__"

ExcludedValidationRule = namedtuple(
    "ExcludedValidationRule", ["tenant", "bundle", "rules"]
)
EXCLUDED_VALIDATION_RULE_CONFIG = [
    # There is no current use case for this feature, the current one is a placeholder
    ExcludedValidationRule("dummy", "tr-schroders-brs", [flags.SteeleyeValidation294]),
]


def catch_errors(fn):
    """
    Wraps function to catch and handle errors for audit

    :return: Decorator
    """

    @wraps(fn)
    def wrapper(self: TransformBaseTask, *args, **kwargs):
        try:
            return fn(self, *args, **kwargs)
        except Exception as e:
            function_name = " ".join(fn.__name__.split("_"))
            error_message = f"Error {function_name} - {e.__str__()}."
            self.logger.exception(error_message)
            ctx = dict(error=e.__str__(), traceback=format_exc())
            self.auditor.add(message=error_message, ctx=ctx)
            FAIL(error_message)

    return wrapper


class Resources(BaseResources):
    es_client_key: str


class Params(BaseParams):
    model_attribute: str = Field(..., description="Column with the meta model defined")
    parent_attribute: Optional[str] = Field(
        None, description="Column with the meta parent defined"
    )
    user_attribute: Optional[str] = Field(
        None, description="Column with the meta user defined"
    )


class AssignMeta(TransformBaseTask):
    params_class = Params
    resources_class = Resources
    tenant_configuration: Union[TenantConfiguration, addict.Dict] = None

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: Resources = None,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index)

        if source_frame.empty:
            self.logger.info("received empty source frame")
            return target

        # `run_steeleye_validations` expects an object with dot notation.
        # Ideally, it should be a `TenantConfiguration` object,
        # but it can be an Addict object as well.
        # To ensure that we don't block all ingestion feeds because of a potential update
        # to the TenantConfiguration doc in ES, we want to handle validation errors
        # and fallback to an Addict instead.
        try:
            self.tenant_configuration = TenantConfiguration.validate(
                prefect.context.swarm.tenant_configuration
            )
        except Exception:
            if prefect.context.swarm.tenant_configuration:
                self.tenant_configuration = addict.Dict(
                    prefect.context.swarm.tenant_configuration
                )

        with_model = source_frame[params.model_attribute].notnull()

        # retrieve records with model defined
        data = source_frame.loc[with_model]

        # Populate index if it is not present
        if SwarmColumns.SWARM_RAW_INDEX not in data.columns:
            data[SwarmColumns.SWARM_RAW_INDEX] = data.index

        if data.empty:
            self.logger.info(
                f"No record with model defined. Model column: {params.model_attribute}."
            )
            return target

        # ECM-365 hack to ensure that OrderState records are converted to the Order model
        # ES8 se-elastic-schema does not support OrderState records anymore
        is_order_state_mask = data.loc[:, params.model_attribute] == "OrderState"
        if is_order_state_mask.any():
            data.loc[is_order_state_mask, params.model_attribute] = "Order"

        # Create and instantiate all unique models present in the data
        instantiated_model_dict = dict()
        unique_models = (
            data[params.model_attribute]
            .fillna("")
            .replace("OrderState", "Order")
            .unique()
            if params.model_attribute in data.columns
            else list()
        )
        for model in unique_models:

            instantiated_model = None
            try:
                instantiated_model = find_model(model)
            except (KeyError, TypeError):
                self.logger.error(f"{model} Model not found / is not supported.")
            instantiated_model_dict.update({model: instantiated_model})

        # Async driver
        target = asyncio.run(
            self.apply_meta(
                df=data,
                params=params,
                instantiated_model_dict=instantiated_model_dict,
            )
        )

        target.index = data.index

        return target

    @catch_errors
    async def apply_meta(
        self,
        df: pd.DataFrame,
        params: Params,
        instantiated_model_dict: Dict[str, Type[SteelEyeSchemaBaseModelES8]],
    ) -> pd.DataFrame:
        """
        Applies model validations and generates the ES meta fields along running the steel-eye validations.

        :param df: Input Source frame
        :param params: Params of the task
        :param instantiated_model_dict: Dict containing the instantiated models which are present in source file
        :return: The model meta properties of the record
        """
        params_meta_fields = [
            params.model_attribute,
            params.parent_attribute,
            params.user_attribute,
            SwarmColumns.SWARM_RAW_INDEX,
        ]

        cols_mask = df.columns.isin(params_meta_fields)

        filtered_df, meta_field_df = df.loc[:, ~cols_mask], df.loc[:, cols_mask]

        meta_field_df = meta_field_df.replace({pd.NA: None, np.nan: None})

        records_dict = filtered_df.to_dict(orient="records")
        meta_field_record_list = meta_field_df.to_dict(orient="records")

        self.logger.info("Starting unflattening process of records")

        unflatten_record_list = await asyncio.gather(
            *[
                self.unflatten_records(
                    record=record,
                )
                for record in records_dict
            ]
        )

        self.logger.info("Starting assign meta model validation")
        # Check if validation rule exclusion is set for current tenant
        excluded_validation_rules = None
        for config in EXCLUDED_VALIDATION_RULE_CONFIG:
            if Settings.bundle == config.bundle and Settings.tenant == config.tenant:
                excluded_validation_rules = config.rules
                break

        # Generating a meta timestamp to populate all records, to keep the previous logic
        meta_timestamp = time.time_ns() // int(1e6)

        # Using threads to execute the following CPU intensive task,
        # pure async isn't giving the optimizations as the internal
        # task is non-co-operative as it does not yield control back to the event loop and hence
        # co-operative concurrency does not work over here.
        max_workers = int(MAX_THREADS) if MAX_THREADS != -1 else None
        self.logger.info(f"Creating thread pool with {MAX_THREADS} threads")
        executor_pool = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        try:
            loop = asyncio.get_running_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        meta_list = await asyncio.gather(
            *[
                loop.run_in_executor(
                    executor_pool,
                    partial(
                        self.add_steel_eye_meta,
                        **dict(
                            record=record,
                            meta_field_record=meta_field_record_list[record_index],
                            meta_timestamp=meta_timestamp,
                            params=params,
                            instantiated_model_dict=instantiated_model_dict,
                            excluded_validation_rules=excluded_validation_rules,
                        ),
                    ),
                )
                for record_index, record in enumerate(unflatten_record_list)
            ]
        )

        meta_df = pd.DataFrame(meta_list)
        executor_pool.shutdown()

        return meta_df

    async def unflatten_records(self, record: dict):
        """
        Unflatten the input record so that it can be used in model validations

        :param record: Record which is to be unflattened
        :returns: Unflattened record
        """
        try:
            # Record itself has deeply nested objects which have null values which Indict.remove_empty cannot handle
            # To fix this be first flatten the record and replace the null values with temp_null
            # which can be handled by Indict
            flattened_record = (
                pd.Series(Indict(obj=pd.Series(record)).flatten().to_dict())
                .fillna(TEMP_NULL)
                .to_dict()
            )

            # Converting pandas Timestamp object to datetime.datetime as pd.Timestamp.isoformat
            # does not inherit parameters from its datetime.datetime equivalent method.
            for key, value in flattened_record.items():
                if isinstance(value, pd.Timestamp):
                    flattened_record[key] = value.to_pydatetime()

            # Not flattening the record anymore as it is already flattened
            unflattened_record = (
                Indict(obj=flattened_record)
                .remove_empty(empty_values=EMPTY_VALUES + (TEMP_NULL,))
                .unflatten(perform_flatten=False)
                .to_dict()
            )
            return unflattened_record
        except Exception:
            self.logger.exception(f"Could not unflatten record: {record}")
            return dict()

    def add_steel_eye_meta(
        self,
        record: dict,
        meta_field_record: dict,
        meta_timestamp: int,
        params: Params,
        instantiated_model_dict: Dict[str, Type[SteelEyeSchemaBaseModelES8]],
        excluded_validation_rules: Optional[List[SteelEyeRecordValidationRule]] = None,
    ) -> dict:
        """
        Validates and enriches the record with its respective model.

        :param record: Record for which the hash is generated.
        :param meta_field_record: Source input meta fields
        :param meta_timestamp: Timestamp which is populated for the record which used by ES.
        :param params: Params of the task
        :param instantiated_model_dict: Dict containing the instantiated models which are present in source file
        :param excluded_validation_rules: List containing the excluded validation rules
        :return: The model meta properties of the record
        """

        model = instantiated_model_dict.get(
            meta_field_record.get(params.model_attribute)
        )
        record_meta = dict()
        if not model:
            return record_meta

        try:
            validated_record = model.validate(record)

        except PydanticValidationError as error:
            audit_pydantic_validation_errors(
                error=error,
                raw_index=meta_field_record.get(SwarmColumns.SWARM_RAW_INDEX),
                auditor=self.auditor,
                logger=self.logger,
                record=record,
            )
            return record_meta

        try:

            # &model and &parent are not populated by `apply_steeleye_meta`
            # &user uses a generic default value if the value is not present
            validated_record.parent__ = meta_field_record.get(params.parent_attribute)
            validated_record.user__ = meta_field_record.get(params.user_attribute)
            validated_record.model__ = meta_field_record.get(params.model_attribute)

            validated_record.apply_steeleye_meta(
                timestamp=meta_timestamp,
            )
        except Exception:
            return record_meta

        # A record where meta can't be assigned should be considered invalid.
        record_meta.update(
            {
                ID: validated_record.id__,
                KEY: validated_record.key__,
                HASH: validated_record.hash__,
                MODEL: validated_record.model__,
                PARENT: validated_record.parent__,
                TIMESTAMP: validated_record.timestamp__,
                UNIQUE_PROPS: validated_record.uniqueProps__,
                USER: validated_record.user__,
                VERSION: validated_record.version__,
            }
        )

        try:

            if isinstance(validated_record, SteelEyeRecordValidationMixin):
                validation_params = dict(tenant_configuration=self.tenant_configuration)
                if excluded_validation_rules:
                    validation_params[
                        "excluded_validation_rules"
                    ] = excluded_validation_rules
                validated_record.run_steeleye_validations(**validation_params)

        except SteelEyeValidationExecutionError as exc:
            auditor_ctx = {
                "error": f"Validation {exc.se_validation} failed: {exc.error_message} ",
                "raw_index": meta_field_record.get(SwarmColumns.SWARM_RAW_INDEX),
            }

            self.auditor.add(
                message="It was not possible to run SteelEye validations for this record",
                ctx=auditor_ctx,
            )

        validation_errors = validated_record.validationErrors__ or []

        record_meta[VALIDATION_ERRORS] = (
            [errors.dict() for errors in validation_errors]
            if validation_errors
            else validation_errors
        )

        return record_meta
