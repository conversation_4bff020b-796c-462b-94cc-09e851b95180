from enum import Enum
from typing import List
from typing import Optional

import pandas as pd
from pydantic import Field
from pydantic import validator
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class SortAttributes(str, Enum):
    ascending = "ascending"
    descending = "descending"


class Prefix(str, Enum):
    attribute_name = "attribute name"


class Params(BaseParams):
    delimiter: Optional[str] = None
    mask: str = Field(
        None, description="This should follow the pandas syntax for pd.DataFrame.query."
    )
    prefix: Optional[Prefix] = None
    sort_attributes: Optional[SortAttributes] = None
    source_attributes: List[str]
    target_attribute: str
    max_length: Optional[int] = Field(
        None,
        description="Optional. If provided, specifies the max length of the final "
        "concatenated string",
    )

    @validator("delimiter", always=True)
    def convert_delimiter_to_empty_string(cls, v):
        if v is None:
            return ""
        return v


class ConcatAttributes(TransformBaseTask):
    """
    This task concatenates a list of attributes into a single column
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(source_frame=source_frame, params=params)

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Params = None,
    ):
        target = pd.DataFrame(index=source_frame.index)

        target[params.target_attribute] = pd.NA

        # Sort attributes
        if params.sort_attributes:
            reverse = params.sort_attributes == SortAttributes.descending
            cols = sorted(params.source_attributes, reverse=reverse)
        else:
            cols = params.source_attributes

        cols_mask = source_frame.columns.isin(cols)

        mask = pd.Series(True, index=source_frame.index)

        if params.mask is not None:
            mask = source_frame.eval(params.mask, engine="python")

        if not mask.any():
            return target

        data = source_frame.loc[mask, cols_mask].reindex(
            cols, axis=1
        )  # to keep the expected order

        with_data_mask = mask & data.notnull().any(axis=1)

        if not with_data_mask.any():
            return target

        # Apply prefix
        if params.prefix and params.prefix == Prefix.attribute_name:
            for col in data.columns:
                not_null_column_mask = data[col].notnull()

                if not_null_column_mask.any():
                    data.loc[not_null_column_mask, col] = data.loc[
                        not_null_column_mask, col
                    ].apply(lambda x: f"{col}: {x}")

        target.loc[with_data_mask, params.target_attribute] = data.loc[
            with_data_mask, :
        ].apply(
            lambda x: f"{params.delimiter}".join(x.dropna().astype("str").tolist()),
            axis=1,
        )
        # if max_length param provided then cut the string to max_length
        if params.max_length:
            target.loc[with_data_mask, params.target_attribute] = target.loc[
                with_data_mask, params.target_attribute
            ].str[: params.max_length]
        return target
