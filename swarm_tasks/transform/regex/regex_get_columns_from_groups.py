import re
from enum import Enum
from pathlib import Path
from typing import Optional
from urllib.parse import unquote
from urllib.parse import unquote_plus

import pandas as pd
from prefect.engine.signals import SKIP
from pydantic import Field
from pydantic import validator
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

TEMP_GROUP_DICT_COL = "group_dict"


class StringParser(str, Enum):
    UNQUOTE = "unquote"
    UNQUOTE_PLUS = "unquote.plus"


class Params(BaseParams):
    file_url_column: str = Field(..., description="Column containing the s3 file URLs")
    pattern: str = Field(
        ..., description="Regex pattern with at least one named subgroup"
    )
    parse_string: Optional[StringParser] = Field(
        default=None, description="Function to use to unquote the s3 url"
    )
    target_source_key_column: str = Field(
        ...,
        description="Name of sourceKey column to which the file url should be written"
        "in the output dataframe",
    )
    filename_in_target_column: Optional[str] = Field(
        default=None,
        description="If not None, includes a column containing the file name in the output dataframe",
    )

    @validator("pattern")
    def ensure_pattern_contains_named_subgroup(cls, v: str):
        subgroup_pattern = r"\(\?P\<\w+\>[^)]+\)"
        if not re.search(subgroup_pattern, v):
            raise ValueError("No named subgroup in pattern")

        return v


class RegexGetColumnsFromGroups(TransformBaseTask):
    """
    This task takes a data frame containing a column of s3 file urls as input.
    It captures named subgroup in the s3 file urls based on the regex in
    params.pattern.
    These named subgroup are returned as columns in the output dataframe.
    """

    params_class = Params

    def execute(
        self, source_frame: pd.DataFrame = None, params: Params = None, **kwargs
    ) -> pd.DataFrame:

        if source_frame.empty:
            raise SKIP(message="Empty dataframe received, cannot extract call columns")

        list_of_call_dicts = []
        for file_url in source_frame[params.file_url_column].tolist():
            group_dict = self._match_group_dict(file_url=file_url, params=params)
            list_of_call_dicts.append(group_dict)

        result = pd.DataFrame(list_of_call_dicts).fillna(pd.NA)
        return result

    def _match_group_dict(self, file_url: str, params: Params) -> dict:
        """Matches the params.pattern pattern which contains named subgroup in the
        file_url string. Returns the named groups along with the matched values
        as keys of a dictionary. If there's no match, it returns an empty dict
        :param: file_url: s3 file url
        :param: params: Params instance
        :returns: Dictionary containing all the subgroups returned by match as keys and
                  the matched values, None if there is no match. Note that the file_url
                  is also returned as a dict sourceKey key.
        :rtype: dict
        """
        unquote_function_dict = {
            StringParser.UNQUOTE: unquote,
            StringParser.UNQUOTE_PLUS: unquote_plus,
        }
        unquote_function = unquote_function_dict.get(params.parse_string)
        if unquote_function:
            file_url = unquote_function(file_url)
        match = re.search(rf"{params.pattern}", file_url)
        if not match:
            self.logger.warning(
                f"{file_url} does not match the pattern '{params.pattern}'"
            )
            return {}
        group_dict = match.groupdict()
        # Add file_url before returning the dict
        group_dict[params.target_source_key_column] = file_url
        if params.filename_in_target_column:
            group_dict[params.filename_in_target_column] = Path(file_url).name
        return group_dict
