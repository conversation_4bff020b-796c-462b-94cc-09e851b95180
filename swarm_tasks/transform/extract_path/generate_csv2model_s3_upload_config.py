from typing import List
from typing import Optional

from prefect.engine.signals import FAIL
from prefect.engine.signals import SKIP
from pydantic import Field
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.core.core_dataclasses import S3File
from se_core_tasks.transform.csv2model.generate_s3_upload_config import (
    FailIfModelNotResolved,
)
from se_core_tasks.transform.csv2model.generate_s3_upload_config import (
    Params as GenericParams,
)
from se_core_tasks.transform.csv2model.generate_s3_upload_config import (
    run_generate_csv2model_s3_upload_config,
)
from se_core_tasks.transform.csv2model.generate_s3_upload_config import (
    SkipIfExtractPathResultEmpty,
)
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams, GenericParams):
    bucket_name: Optional[str] = Field(
        None, description="Bucket where the file will be uploaded to"
    )


class GenerateCsv2ModelS3UploadConfig(BaseTask):
    """
    Creates a list of S3 Files from a list of ExtractPathResult.
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        extract_path_result_list: List[ExtractPathResult] = None,
        file_url: str = None,
        **kwargs,
    ) -> Optional[List[S3File]]:

        try:
            params.bucket_name = (
                Settings.realm if not params.bucket_name else params.bucket_name
            )

            result = run_generate_csv2model_s3_upload_config(
                extract_path_result_list=extract_path_result_list,
                file_url=file_url,
                params=params,
                auditor=self.auditor,
                logger=self.logger,
            )
        except FailIfModelNotResolved:
            raise FAIL("Failing flow as model could not be resolved.")

        except SkipIfExtractPathResultEmpty:
            raise SKIP("Skipping as could not convert to S3File from ExtractPathResult")

        return result
