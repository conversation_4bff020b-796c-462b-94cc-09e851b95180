from pathlib import Path
from typing import List
from typing import Optional
from typing import Union

from prefect.engine import signals
from pydantic import Field
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.core.core_dataclasses import S3Action
from se_core_tasks.core.core_dataclasses import S3File
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams):
    file_suffix: Optional[str] = Field(
        None,
        description="File suffix used to extract a subset of the ExtractPathResult list. The resulting"
        "S3 file list will only contain this subset of files. ",
    )
    s3_key_prefix: str = Field(
        ..., description="S3 Prefix where the call recordings will be uploaded to"
    )


class S3FileListFromExtractPathResultList(BaseTask):
    """
    Creates a list of S3 Files from a list of ExtractPathResults. There is an optional file
    extension parameter, which filters the ExtractPathResults before creating the S3 file
    list.
    If the extract_result_list is a simple ExtractPathResult, it is converted to a list.
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        extract_result_list: Union[ExtractPathResult, List[ExtractPathResult]] = None,
        **kwargs,
    ) -> List[S3File]:
        return self.process(params=params, extract_result_list=extract_result_list)

    @classmethod
    def process(
        cls,
        params: Optional[Params] = None,
        extract_result_list: List[ExtractPathResult] = None,
    ):
        if not extract_result_list:
            raise signals.SKIP("No ExtractPathResult elements in extract_result_list")
        if not isinstance(extract_result_list, list):
            extract_result_list = [extract_result_list]
        if params.file_suffix:
            required_file_list = [
                extract_result
                for extract_result in extract_result_list
                if extract_result.path.suffix == params.file_suffix
            ]
        else:
            required_file_list = [
                extract_result for extract_result in extract_result_list
            ]

        if not required_file_list:
            raise signals.SKIP("No files exist to create S3Files from")

        s3_file_list = []
        for extract_path in required_file_list:
            s3_key = Path(params.s3_key_prefix, extract_path.path.name).as_posix()
            s3_file = S3File(
                file_path=extract_path.path,
                bucket_name=Settings.realm,
                key_name=s3_key,
                action=S3Action.UPLOAD,
            )
            s3_file_list.append(s3_file)

        return s3_file_list
