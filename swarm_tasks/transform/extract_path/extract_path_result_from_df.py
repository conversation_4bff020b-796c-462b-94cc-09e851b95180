from pathlib import Path
from typing import Optional
from typing import Union

import pandas as pd
from prefect.engine.signals import SKIP
from pydantic import Field
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.io.read.result import FrameProducerResult
from swarm.task.transform.base import BaseTask
from swarm.task.transform.result import TransformResult

from swarm_tasks.utilities.task_utils import extract_source_frame


class Params(BaseParams):
    output_csv_file_name: str = Field(
        ..., description="Name of the output csv file (without extension)"
    )


class ExtractPathResultFromDf(BaseTask):
    """
    This task is used to create an ExtractPathResult from a data frame. The extract path result
    points to a file containing the data that is present in the data frame
    """

    params_class = Params

    def execute(
        self,
        source_frame: Optional[
            Union[pd.DataFrame, FrameProducerResult, TransformResult]
        ] = None,
        original_file_url: Optional[str] = None,
        params: Optional[Params] = None,
        **kwargs,
    ) -> ExtractPathResult:

        return self.process(
            source_frame=source_frame,
            params=params,
            original_file_url=original_file_url,
        )

    @classmethod
    def process(
        cls,
        source_frame: Optional[
            Union[pd.DataFrame, FrameProducerResult, TransformResult]
        ] = None,
        original_file_url: Optional[str] = None,
        params: Optional[Params] = None,
    ) -> ExtractPathResult:

        df = extract_source_frame(source_frame=source_frame)
        if df.empty:
            raise SKIP("Source Frame empty")

        source_dir = cls._get_source_dir()
        if original_file_url:
            csv_file_path = source_dir.joinpath(
                f"{Path(original_file_url).stem}_{params.output_csv_file_name}.csv"
            )
        else:
            csv_file_path = source_dir.joinpath(f"{params.output_csv_file_name}.csv")
        df.to_csv(csv_file_path, index=False, encoding="utf-8", sep=",")
        return ExtractPathResult(path=csv_file_path)

    @staticmethod
    def _get_source_dir() -> Path:
        return Path(Settings.context.sources_dir)
