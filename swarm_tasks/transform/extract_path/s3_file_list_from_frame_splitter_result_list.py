from typing import List
from typing import Optional

from prefect.engine.signals import FAIL
from pydantic import Field
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_core_tasks.core.core_dataclasses import S3File
from se_core_tasks.transform.cloud_file_list_from_frame_splitter_result_list import (
    FailIfFileSplitterResultEmpty,
)
from se_core_tasks.transform.cloud_file_list_from_frame_splitter_result_list import (
    Params as GenericParams,
)
from se_core_tasks.transform.cloud_file_list_from_frame_splitter_result_list import (
    run_cloud_file_list_from_file_splitter_result_list,
)
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams, GenericParams):
    bucket_name: Optional[str] = Field(
        None, description="Bucket where the file will be uploaded to"
    )


class S3FileListFileSplitterResultList(BaseTask):
    """
    Creates a list of S3 Files from a list of FileSplitterResult.
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        file_splitter_result_list: List[FileSplitterResult] = None,
        file_url: str = None,
        **kwargs,
    ) -> Optional[List[S3File]]:

        try:
            params.bucket_name = (
                Settings.realm if not params.bucket_name else params.bucket_name
            )

            result = run_cloud_file_list_from_file_splitter_result_list(
                file_splitter_result_list=file_splitter_result_list,
                file_url=file_url,
                params=params,
                auditor=self.auditor,
                logger=self.logger,
            )
        except FailIfFileSplitterResultEmpty:
            raise FAIL("Could not convert to S3File from FileSplitterResult")

        return result
