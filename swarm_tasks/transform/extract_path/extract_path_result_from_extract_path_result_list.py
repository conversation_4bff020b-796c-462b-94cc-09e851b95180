from typing import List
from typing import Optional

from prefect.engine import signals
from pydantic import Field
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams):
    file_name: str = Field(
        ...,
        description="File name of the required ExtractPathResult file which needs to "
        "be extracted from the ExtractPathResult list",
    )


class ExtractPathResultFromExtractPathResultList(BaseTask):
    """
    Extracts an ExtractPathResult from a list of ExtractPathResult based on the file name.
    This is typically used when we need to get a file from a folder (for e.g. after all files
    were extracted from a ZIP/Tar archive and inserted into a list of ExtractPathResults).
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        extract_result_list: List[ExtractPathResult] = None,
        **kwargs,
    ) -> ExtractPathResult:

        if not extract_result_list:
            raise signals.SKIP(f"Empty {extract_result_list}")

        required_file_list = [
            extract_result
            for extract_result in extract_result_list
            if extract_result.path.name == params.file_name
        ]

        if not required_file_list:
            raise signals.SKIP(f"Required file {params.file_name} not found")

        result = required_file_list[0]
        return result
