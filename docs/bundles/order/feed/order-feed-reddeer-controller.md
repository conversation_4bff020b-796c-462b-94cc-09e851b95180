# Order Feed Red Deer Controller

Specs: [Order Feed Red Deer Controller Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2297069739/Order+-+Red+Deer)

The Order Feed Red Deer Processor Flow shall only execute when 2 specific files are available in S3.
One of the files name starts with `emso_orders` and the other with `emso_order_allocations`. The client
will drop these pairs of files every day, thus the filename also contains the associated date.
Therefore, for a given day, this task will search for the pair of order and order allocations files,
and build a new CSV file with the concatenated data of the pair files, which will then trigger the Processor Flow

## I/O

The input can be the `emso_orders` or the `emso_order_allocations` file. The file that arrives first will trigger this flow, 
however the flow will not produce any results as the other file is still missing. When the second file arrives,
this flow will concatenate the data of both files into a single CSV file and upload it to `flows/order-feed-reddeer-processor/`.
