# Order Feed Charles River Controller

Specs: [Order Feed Charles River Controller Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2450587649/Order+Charles+River#Controller-Task)

The Order Feed Charles River Processor Flow will only execute when 4 specific files are available in S3.
The file names will have the following format:
* `FSI_CRIMS_allocation_YYYYMMDD.txt`
* `FSI_CRIMS_fills_YYYYMMDD.txt`
* `FSI_CRIMS_order_YYYYMMDD.txt`
* `FSI_CRIMS_place_YYYYMMDD.txt`

We also have another file which is dropped called `FSI_CRIMS_orderhistory_YYYYMMDD.txt`. 
The controller flow skips this file and its data is not used in ingestion / downstream.

The client will drop these files every day, thus the filename also contains the associated date.
For a given day, this task will search for the files, download the data, combine the data based on flow type, 
perform record transformation, create batches and then upload the batches to the processor S3 paths to 
trigger the Processor flows.

## I/O

The input can be any of the aforementioned file. All the files will trigger the flow, but only the last 
file will run the flow end to end. This is done to ensure all the files are present in the downstream flow context.
This flow will perform the necessary record transformations and then create batches of the resultant file and upload 
it to `flows/order-feed-charles-river-order-processor/` and `flows/order-feed-charles-river-allocation-processor/` 
to trigger the order and allocation processor flows.

## Tasks
The following tasks are defined twice for the Allocation and Orders Flow respectively.

- ### MultipleFilesInputController
Checks the S3 location of the incoming file for required file patterns and then creates a 
CSV containing the S3 locations of the files mentioned in the task parameters. 
For allocations flow we need the orders, allocation and place files whereas for the order flow all 4 files are required.
If any of the required files are not present the flow will be skipped. Allocation flow is not triggered for 
the `Fill file`. This is done to ensure the flows are not redundantly triggered.

- ### S3DownloadMultipleFiles
Task which reads the files in S3 *(asynchronously)* and then collates it in a dict to be used in downstream tasks.
For Orders flow all 4 files are used. For allocations flow only order, allocation and place files are used.

- ### CharlesRiverFrameTransformer
Task specific to the charles river flow which creates the frame used by downstream tasks. 
The flow demarcation is also done by this task by a parameter called `trigger_file_type`. 
In `order-feed-charles-river-allocation-processor` flow it is set to `allocation` 
and for `order-feed-charles-river-order-processor` flow it is set to `order`.

- ### CsvFileSplitter
Split CSV file in Batches if the input file exceeds a parametrized threshold.

- ### S3FileListFileSplitterResultList
Auxiliary task which creates a list of S3Files Object from a list of FileSplitterResult 
object produced upstream in CsvFileSplitter. The result is used downstream by S3UploadFile task.

- ### S3UploadFile
Upload the batches to the specific S3 location, thereby triggering the Processor flow.
