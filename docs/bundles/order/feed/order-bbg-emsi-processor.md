# Order BBG EMSI Processor

Specs: [Order BBG EMSI Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2745696257/Order+BBG+EMSI+EOD+File+V2)

This is the processor flow downstream of `order-bbg-emsi-controller` controller flow. 
This flow is triggered when the controller task uploads, the CSV batches file to the processor S3 path.


## Input file
Batched CSV file containing the necessary information to process the data.

## Output
`Order`,  `OrderState` and `QuarantinedOrder` records in ElasticSearch.

## Tasks

- ### ParametersFlowController
This flow can be executed for both a local file or one on S3.

- ### S3DownloadFile
Download CSV file from S3.

- ### LocalFile
Read local CSV file.

- ### CsvFileSplitter
Split CSV file in Batches if the input file exceeds a parametrized threshold

- ### BatchProducer
Reads each CSV chunk and produces Batches of DataFrames to downstream tasks.

- ### PrimaryTransformations
Performs the initial mapping for the Order and OrderState fields that are sourced from the original file.

- ### LinkParties
Links the Party Identifiers to existing records.

- ### LinkInstrument
Links the Instrument Identifiers to existing records in SRP.

- ### InstrumentFallback
Adds manually-created instruments where instruments are not populated.

- ### PartiesFallback
Adds manually-created parties where parties are not populated.

- ### AssignMetaParent
Links each OrderState record to the appropriate parent Order record.

- ### RemoveNewoMetaParent
This task removes the \_\_meta_parent__ field for any NEWO record, as they are not necessary.

- ### AuxiliaryFrameConcatenator
Concatenates all columns from the `PrimaryTransformations`, `PartiesFallback`, `InstrumentFallback`
and `RemoveNewoMetaParent` tasks.

- ### BestExecution
Populates Best Execution fields.

- ### BestExecutionConcatenator
Concatenates the data frames from `VerticalConcatenator` and `BestExecution`.

- ### AssignMeta
Creates the meta fields of the records after record validation.

- ### PostMetaConcatenator
Concatenates the data frames from `BestExecutionConcatenator` and `AssignMeta`.

- ### ElasticBulkTransformer
Creates a file containing all the Order and OrderState records to be ingested into ElasticSearch.

- ### PutIfAbsent
Uses the file created by ElasticBulkTransformer to ingest Order and OrderState records into Elasticsearch.

- ### GenerateOrderKafkaMessages
Creates Order messages for Kafka producer.   

- ### KafkaMessageProducer
Kafka Producer which writes messages into the OrderAnalyticsTopic Kafka topic.

- ### QuarantineCondition
Assesses if any record should be quarantined.

- ### QuarantinedElasticBulkTransformer
Creates a file containing all the QuarantiedOrder records to be ingested into ElasticSearch.

- ### QuarantinedPutIfAbsent
Uses the file created by QuarantinedElasticBulkTransformer to ingest QuarantiedOrder records into Elasticsearch.

- ### Noop
Dummy task, used when there is no need to quarantine.