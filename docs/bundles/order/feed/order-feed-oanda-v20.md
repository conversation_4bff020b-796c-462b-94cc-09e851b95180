# Order Feed Oanda V20

Specs: [Order: Oanda V20](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2412183553/Order+Oanda+V20)

The flow is triggered by dropping a GZIP or CSV file into the following path in S3 `flows/order-feed-oanda-v20`

The flow has an horizontal scaling prevention logic in place. If the original file generates a large number of batches. These will be grouped into smaller chunks and re-uploaded as CSV to the above-mentioned path.

## Input file
 `.gz` files containing one `NDJSON` file or an equivalent `.csv` file

## Output
`Order`, `OrderState` and `QuarantinedOrder` records in ElasticSearch

## Tasks

1. ### ParametersFlowController
This flow can be executed for both a local file or one on S3

2. ### S3DownloadFile
Download CSV file from S3

3. ### LocalFile
Read local CSV file

4. ### Unzip
Unzips `.gz` file into a temporary path

4. ### TextFileSplitter
Reads the file in chunks and generates a list of smaller files, according to the defined chunksize

4. ### TextManipulation
Removes some unnecessary fields which stop the files from being correctly read as json

5. ### NDJSONFileSplitter
Split NDJSON file in Batches of CSV if the input file exceeds a parametrized threshold

6. ### BatchProducer
Reads each CSV chunk and produces Batches of DataFrames to downstream tasks

7. ### IsFileTypeCSVController
Decides which section of the flow to run, according to if the input file is a CSV or a `.gz`

8. ### HorizontalBatchController
Horizontal Scalling Logic, decides when to upload batches to S3 or continue with the flow

9. ### MergeAndChunkCsvFiles
Merges the CSV files into chunks to upload

10. ### S3FileListFileSplitterResultList
Creates S3File objects of the batches to upload

11. ### S3UploadOrderFile
Uploads bacthes

12. ### BatchProducer
Reads each CSV chunk and produces Batches of DataFrames to downstream tasks

13. ### DownloadInstrumentData
Downloads instrument CSV from S3

14. ### PrimaryTransformations
Maps all the required columns to the required target columns

15. ### LinkParties
Links the Party Identifiers to existing records

16. ### LinkInstrument
Links the Instrument Identifiers to existing records in SRP

17. ### InstrumentFallback
Adds manually-created instruments where instruments are not populated

18. ### MetaParent
Links each OrderState record to the appropriate parent Order record

19. ### PrimaryFrameConcatenator
Concatenates all columns from the `PrimaryTransformations`, `LinkParties`, `InstrumentFallback` and `MetaParent` tasks

20. ### OrderRecords
Filters only Order records into a data frame

21. ### OrderStateRecords
Filters only OrderState records into a data frame

22. ### StripPrefixOrder
Removes the prefix '_order' from all Order records

23. ### StripPrefixOrderState
Removes the prefix '_orderState' from all OrderState records

24. ### VerticalConcatenator
Concatenates Order and OrderState records into a single data frame

25. ### RemoveDupNEWO
Removes duplicate NEWO present in the dataframe

26. ### BestExecution
Populates Best Execution fields

27. ### BestExecutionConcatenator
Concatenates the data frames from `RemoveDupNEWO` and `BestExecution`

28. ### AssignMeta
Creates the meta fields of the records after record validation

29. ### PostMetaConcatenator
Concatenates the data frames from `BestExecutionConcatenator` and `AssignMeta`

30. ### ElasticBulkTransformer
Creates a file containing all the Order and OrderState records to be ingested into ElasticSearch

31. ### ElasticBulkWriter
Uses the file created by ElasticBulkTransformer to ingest Order and OrderState records into Elasticsearch 

32. ### GenerateOrderKafkaMessages
Creates Order messages for Kafka producer    

33. ### KafkaMessageProducer
Kafka Producer which writes messages into the OrderAnalyticsTopic Kafka topic

34. ### QuarantineCondition
Assesses if any record should be quarantined

35. ### QuarantinedElasticBulkTransformer
Creates a file containing all the QuarantinedOrder records to be ingested into ElasticSearch

36. ### QuarantinedElasticBulkWriter
Uses the file created by QuarantinedElasticBulkTransformer to ingest QuarantinedOrder records into Elasticsearch

37. ### Noop
Dummy task, used when there is no need to quarantine.