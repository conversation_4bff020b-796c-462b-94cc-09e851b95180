# Order Feed Fidessa Front Office Trades

Specs: [Order Fidessa Front Office Trades Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2173599812/Order+Fidessa+Front+Office+Trades)

The flow is triggered by dropping a CSV file into the following path in S3: `flows/order-feed-fidessa-front-office-trades`

## Input file
FxTrade or FxOption `.csv` files

## Output
`Order` and `QuarantinedOrder` records in ElasticSearch. 

## Skip logic
```
If there are two records which have the same value in [Trade_Id] and one record [State] = "I" with a [Version_Number] that is less than the [Version_Number] of the related [State] = "C" OR
if [State] == "C" OR
if [Trade_Type] == "AGTD" OR
if [Trade_Type] == "BLOCK" AND [Business_Transaction_Description] != "Client trade" OR
if [Business_Transaction_Description] == ["Booking" OR 'Order warehousing transfer'] OR
if [Book_Id] == "NETTNG"
```

## Tasks

- ### ParametersFlowController
This flow can be executed for both a local file or one on S3

- ### S3DownloadFile
Download CSV file from S3

- ### LocalFile
Read local CSV file

- ### CsvFileSplitter
Split CSV file in Batches if the input file exceeds a parametrized threshold

- ### BatchProducer
Reads each CSV chunk and produces Batches of DataFrames to downstream tasks

- ### ExtractBatchFromFrameProducerResult
Extracts the batch number from the BatchProducer's FrameProducerResult. This
batch number (integer) is later passed to the ElasticBulkTransformer

- ### FrontOfficeSkipLogic
Applies the required skip logic to the flow

- ### FidessaFrontOfficeTransformations
Maps the columns to the required target columns

- MetaParent 
Links each OrderState record to the appropriate parent Order record

- ### LinkParties
Links the Party Identifiers to existing records

- ### PartiesFallback
Adds skinny party info where parties are not populated

- ### LinkInstrument
Links the Instrument Identifiers to existing records in SRP

- ### OverrideInstrumentName
Overrides the instrument name fetched from SRP with a value from the file

- ### InstrumentFallback
Adds manually-created instruments where instruments are not populated

- ### PrimaryFrameConcatenator
Concatenates all columns from the previous tasks. Also, drops non-schema
columns

- ### OrderStateRecords
Filters only OrderState records into a data frame

- ### StripPrefixOrder
Removes the prefix '_order' from all Order records

- ### StripPrefixOrderState
Removes the prefix '_orderState' from all OrderState records

- ### VerticalConcatenator
Concatenates Order and OrderState records into a single data frame

- ### BestExecution
Populates Best Execution fields

- ### BestExecutionConcatenator
Concatenates the data frames from `VerticalConcatenator` and `BestExecution`

- ### AssignMeta
Creates the meta fields of the records after record validation

- ### PostMetaConcatenator
Concatenates the data frames from `BestExecutionConcatenator` and `AssignMeta`

- ### ElasticBulkTransformer
Creates a file containing all the Order and OrderState records to be ingested into ElasticSearch

- ### ElasticBulkWriter
Uses the file created by ElasticBulkTransformer to ingest Order and OrderState records into Elasticsearch 

- ### GenerateOrderKafkaMessages
Creates Order messages for Kafka producer    

- ### KafkaMessageProducer
Kafka Producer which writes messages into the OrderAnalyticsTopic Kafka topic

- ### QuarantineCondition
Assesses if any record should be quarantined

- ### QuarantinedElasticBulkTransformer
Creates a file containing all the QuarantiedOrder records to be ingested into ElasticSearch

- ### QuarantinedElasticBulkWriter
Uses the file created by QuarantinedElasticBulkTransformer to ingest QuarantiedOrder records into Elasticsearch

- ### Noop
Dummy task, used when there is no need to quarantine.
