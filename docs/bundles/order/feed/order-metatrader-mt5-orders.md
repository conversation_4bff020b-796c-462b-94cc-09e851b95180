# Order Metatrader MT5 Orders

Specs: [Metatrader MT5 Orders confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2269807113/Order+rts22Transaction+MetaTrader+5+MT5)

The flow is triggered by dropping a CSV file into the following path in S3: 
`flows/order-metatrader-mt5-orders`

This flow is expected to create `Order(NEWO)` and CAME, REMO, EXPI and CHME `OrderStates` only. 
`FILL/PARF` are created in a separate bundle 'order-metatrader-mt5-deals'

## Input file
"Orders" `.csv` file

InstrumentData - a static .csv` file in s3 path 
"resource/metatrader/mt5/instrument/InstrumentData.csv".

## Output
`Order`, `OrderState` ,`QuarantinedOrder` records in ElasticSearch.

## Tasks

1. ### ParametersFlowController
This flow can be executed for both a local file or one on S3

2. ### S3DownloadFile
Download CSV file from S3

3. ### LocalFile
Read local CSV file

4. ### CsvFileSplitter
Split CSV file in Batches if the input file exceeds a parametrized threshold

5. ### BatchProducer
Reads each CSV chunk and produces Batches of DataFrames to downstream tasks

6. ### PrimaryTransformations
Performs the initial mapping for the Order and OrderState fields that are sourced from the original file

7. ### LinkParties
Links the Party Identifiers to existing records

8. ### PartiesFallback
Adds skinny party info where parties are not populated

9. ### PopulateInstruments
Uses InstrumentFallback task to populate instrument data directly from a static s3 
file expected at s3 path "resource/metatrader/mt5/instrument/InstrumentData.csv".

10. ### PrimaryFrameConcatenator
Frame concatenator for the above tasks.

11. ### BestExecution
Populates Best Execution fields

12. ### BestExecutionConcatenator
Concatenates the data frames from `PrimaryFrameConcatenator` and `BestExecution`

13. ### ModifyBestExecutionResults
Modifies the `BestExecutionVolume` fields based on the Contract Size column.

14. ### AssignMeta
Creates the meta fields of the records after record validation

15. ### PostMetaConcatenator
Concatenates the data frames from `PrimaryFrameConcatenator` and `AssignMeta`

16. ### ElasticBulkTransformer
Creates a file containing all the Order and OrderState records to be ingested into ElasticSearch

17. ### PutIfAbsent
Uses the file created by ElasticBulkTransformer to ingest Order and OrderState records into Elasticsearch

18. ### QuarantineCondition
Assesses if any record should be quarantined

19. ### QuarantinedElasticBulkTransformer
Creates a file containing all the QuarantiedOrder records to be ingested into ElasticSearch

20. ### QuarantinedPutIfAbsent
Uses the file created by QuarantinedElasticBulkTransformer to ingest QuarantiedOrder records into Elasticsearch

21. ### Noop
Dummy task, used when there is no need to quarantine.