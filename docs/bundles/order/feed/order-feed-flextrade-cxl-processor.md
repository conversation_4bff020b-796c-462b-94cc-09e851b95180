# Order Feed Flextrade CXL Processor

Specs: [Order Flextrade Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2473590785/Order+FlexTrade)

The flow is triggered by the `order-feed-flextrade-controller-prim-rpl-processor` flow, which creates
a suitable input file for the EXE flow and uploads it to the `flows/order-feed-flextrade-cxl-processor` path.

## Input file
CSV file produced by `order-feed-flextrade-controller-prim-rpl-processor`. This CSV contains only
CXL (Cancel) records.

## Output
* `OrderState` and `QuarantinedOrder` records in ElasticSearch for CXL records

## Tasks

-  ### ParametersFlowController
This flow can be executed for both a local file or one on S3

- ### S3DownloadFile
Download CSV file from S3

- ### LocalFile
Read local CSV file

- ### CsvFileSplitter
Split CSV file in Batches if the input file exceeds a parametrized threshold

- ### BatchProducer
Reads each CSV chunk and produces Batches of DataFrames to downstream tasks

- ### ExtractBatchFromFrameProducerResult
Extracts the batch from the Batch Producer result

- ### InlineFixParserExeUserFixtag
Parses the fix fields present in the exe_userfixtag column. It also prefixes the parsed columns 
with exe_user_fix_tag_.

- ### InlineFixParserPrimRplFixtags
Parses the fix fields present in the prim_rpl_fixtags column. It also prefixes the parsed columns 
with prim_rpl_fix_tag_.

- ### SourceFrameConcatenator
Concatenates the fix columns with the regular columns

- ### FlextradeCxlTransformations
Maps all the required columns to the required target columns

- ### MetaParent
Links each OrderState record to the appropriate parent Order record

- ### LinkParties
Links the Party Identifiers to existing records

- ### LinkInstrument
Links the Instrument Identifiers to existing records in SRP

- ### InstrumentFallback
Adds manually-created instruments where instruments are not populated 
  
- ### PrimaryFrameConcatenator
Concatenates all columns from the `FlextradeCxlTransformations`, `LinkParties`, `LinkInstrument`,
and `AssignMetaParent` tasks

- ### OrderRecords
Filters only Order records into a data frame

- ### OrderStateRecords
Filters only OrderState records into a data frame

- ### StripPrefixOrder
Removes the prefix '_order' from all Order records

- ### StripPrefixOrderState
Removes the prefix '_orderState' from all OrderState records

- ### VerticalConcatenator
Concatenates Order and OrderState records into a single data frame

- ### RemoveSyntheticNEWOs
Removes all synthetic NEWOs created in the flow. NEWOs are expected to be created in the `order-feed-flextrade-controller-prim-rpl-processor` flow

- ### AssignMeta
Creates the meta fields of the records after record validation

- ### PostMetaConcatenator
Concatenates the data frames from `RemoveSyntheticNEWOs` and `AssignMeta`

- ### ElasticBulkTransformer
Creates a file containing all the Order and OrderState records to be ingested into ElasticSearch

- ### ElasticBulkWriter
Uses the file created by ElasticBulkTransformer to ingest Order and OrderState records into Elasticsearch 

- ### GenerateOrderKafkaMessages
Creates Order messages for Kafka producer    

- ### KafkaMessageProducer
Kafka Producer which writes messages into the OrderAnalyticsTopic Kafka topic

- ### QuarantineCondition
Assesses if any record should be quarantined

- ### QuarantinedElasticBulkTransformer
Creates a file containing all the QuarantiedOrder records to be ingested into ElasticSearch

- ### QuarantinedElasticBulkWriter
Uses the file created by QuarantinedElasticBulkTransformer to ingest QuarantiedOrder records into Elasticsearch

- ### Noop
Dummy task, used when there is no need to quarantine.

