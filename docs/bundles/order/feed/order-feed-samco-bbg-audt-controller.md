# Order Feed Shell Controller

Since the files provided by the client are humungous, this controller flow was 
created to split the file in batches and then trigger an individual AWS Batch job for the 
processor flow for each of the chunks. 

## I/O

The Order Feed Shell Controller will execute when the client drops a file in the 
`flows/order-feed-samco-bbg-audt-controller` S3 location. Post-processing the resultant chucked files 
will be uploaded to `flows/order-feed-samco-bbg-audt-processor`.

## Tasks

- ### ParametersFlowController
This flow can be executed for both a local file or one on S3.

- ### S3DownloadFile
Download CSV file from S3.

- ### LocalFile
Read local CSV file.

- ### CsvFileSplitter
Split CSV file in Batches if the input file exceeds a parametrized threshold.

- ### S3FileListFileSplitterResultList
Auxiliary task which creates a list of S3Files Object from a list of FileSplitterResult 
object produced upstream in CsvFileSplitter. The result is used downstream by S3UploadFile task.

- ### S3UploadFile
Upload the batches to the specific S3 location, thereby triggering the Processor flow.
