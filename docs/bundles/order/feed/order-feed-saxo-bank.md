# Order Feed Saxo Bank

Specs: [Order Saxo Bank Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/**********/Order+Saxo+Bank)

This flow is triggered by dropping an S3toS3Copy task in the corresponding TR flow, which copies the input file
from `flows/tr-feed-saxo-bank` to `flows/order-feed-saxo-bank`. 

It can also be triggered directly by dropping a CSV file into the following path in S3: `flows/order-feed-saxo-bank`

It can currently handle 8 types of files with different headers:

* BondsTradesExecuted
* SPTradesExecuted
* CFDTradesExecuted
* FuturesTradesExecuted
* FXOptionTradesExecuted
* FXTradesExecuted
* MutualFundsTradesExecuted
* ShareTradesExecuted

In addition, the client sends the following 3 files, which are skipped (not processed):
* OrderActivity
* CashTransactions
* CFDOptionTradesExecuted

## Input file
One of 11 types of `.csv` files (3 of which are skipped)

## Output
`Order`, `OrderState` and `QuarantinedOrder` records in ElasticSearch

## Skip logic
No records are skipped. But 3 types of files are skipped: OrderActivity, CashTransactions and
CFDOptionTradesExecuted

## Tasks

-  ### ParametersFlowController
This flow can be executed for both a local file or one on S3

- ### S3DownloadFile
Download CSV file from S3

- ### LocalFile
Read local CSV file

- ### CsvFileSplitter
Split CSV file in Batches if the input file exceeds a parametrized threshold

- ### BatchProducer
Reads each CSV chunk and produces Batches of DataFrames to downstream tasks

- ### FilterRowsInSourceFile
Filters the rows in the source CSV file by applying the required skip logic   

- ### SaxoBankOrderTransformations
Maps all the required columns to the required target columns

- ### LinkParties
Links the Party Identifiers to existing records

- ### LinkInstrument
Links the Instrument Identifiers to existing records in SRP

- ### AssignMetaParent
Links each OrderState record to the appropriate parent Order record

- ### PrimaryFrameConcatenator
Concatenates all columns from the `PrimaryTransformations`, `LinkParties`, `LinkInstrument`,
and `AssignMetaParent` tasks

- ### OrderRecords
Filters only Order records into a data frame

- ### OrderStateRecords
Filters only OrderState records into a data frame

- ### StripPrefixOrder
Removes the prefix '_order' from all Order records

- ### StripPrefixOrderState
Removes the prefix '_orderState' from all OrderState records

- ### VerticalConcatenator
Concatenates Order and OrderState records into a single data frame

- ### BestExecution
Populates Best Execution fields

- ### BestExecutionConcatenator
Concatenates the data frames from `VerticalConcatenator` and `BestExecution`

- ### AssignMeta
Creates the meta fields of the records after record validation

- ### PostMetaConcatenator
Concatenates the data frames from `BestExecutionConcatenator` and `AssignMeta`

- ### ElasticBulkTransformer
Creates a file containing all the Order and OrderState records to be ingested into ElasticSearch

- ### ElasticBulkWriter
Uses the file created by ElasticBulkTransformer to ingest Order and OrderState records into Elasticsearch 

- ### GenerateOrderKafkaMessages
Creates Order messages for Kafka producer    

- ### KafkaMessageProducer
Kafka Producer which writes messages into the OrderAnalyticsTopic Kafka topic

- ### QuarantineCondition
Assesses if any record should be quarantined

- ### QuarantinedElasticBulkTransformer
Creates a file containing all the QuarantiedOrder records to be ingested into ElasticSearch

- ### QuarantinedElasticBulkWriter
Uses the file created by QuarantinedElasticBulkTransformer to ingest QuarantiedOrder records into Elasticsearch

- ### Noop
Dummy task, used when there is no need to quarantine.
