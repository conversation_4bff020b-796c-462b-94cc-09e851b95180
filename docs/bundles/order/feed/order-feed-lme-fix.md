# Order Feed LME 

Specs: [Feed LME Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/571506968/Order+LME+FIX+Drop+Copy)

This is the order flow for Fix files given by LME Select and ICBC. 

This flow is triggered by an S3 event after the landing files are aggregated in a CSV by the Fix-Router (ECS Task). 
Which are as follows:
* `flow/order-feed-lme-fix`

## Input file
CSV file containing a reference to the Fix file in S3. 

## Output
`Order`,  `OrderState` and `QuarantinedOrder` records in ElasticSearch. 

## Tasks

- ### ParametersFlowController
This flow can be executed for both a local file or one on S3.

- ### S3DownloadFile
Download CSV file from S3.

- ### LocalFile
Read local CSV file.

- ### FixBatchCsvDownloader
Download the Fix file from S3

- ### FixParser
Parse the Fix file

- ### FixParserResultToFrame
Converts the Fix file to a DataFrame

- ### FilterRowsInSourceFile
Implements the skip logic

- ### PrimaryTransformations
Performs the initial mapping for the Order and OrderState fields that are sourced from the original file.

- ### LinkParties
Links the Party Identifiers to existing records.

- ### LinkInstrument
Links the Instrument Identifiers to existing records in SRP.

- ### InstrumentFallback
Adds manually-created instruments where instruments are not populated.

- ### AssignMetaParent
Links each OrderState record to the appropriate parent Order record.

- ### PrimaryFrameConcatenator
Concatenates all columns from the `PrimaryTransformations`, `LinkParties`, `LinkInstrument`
and `RemoveNewoMetaParent` tasks.

- ### BestExecution
Populates Best Execution fields.

- ### BestExecutionConcatenator
Concatenates the data frames from `VerticalConcatenator` and `BestExecution`.

- ### AssignMeta
Creates the meta fields of the records after record validation.

- ### FinalConcatenator
Concatenates the data frames from `BestExecutionConcatenator` and `AssignMeta`.

- ### ElasticBulkTransformer
Creates a file containing all the Order and OrderState records to be ingested into ElasticSearch.

- ### PutIfAbsent
Uses the file created by ElasticBulkTransformer to ingest Order and OrderState records into Elasticsearch.

- ### GenerateOrderKafkaMessages
Creates Order messages for Kafka producer.   

- ### KafkaMessageProducer
Kafka Producer which writes messages into the OrderAnalyticsTopic Kafka topic.

- ### QuarantineCondition
Assesses if any record should be quarantined.

- ### QuarantinedElasticBulkTransformer
Creates a file containing all the QuarantiedOrder records to be ingested into ElasticSearch.

- ### QuarantinedPutIfAbsent
Uses the file created by QuarantinedElasticBulkTransformer to ingest QuarantiedOrder records into Elasticsearch.

- ### Noop
Dummy task, used when there is no need to quarantine.