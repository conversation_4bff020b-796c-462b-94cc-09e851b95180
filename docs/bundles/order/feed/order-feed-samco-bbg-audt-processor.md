# Order Feed Shell Processor

Specs: [Order Feed Shell Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2642083893/DRAFT+2+Order+Bloomberg+AUDT+SHELL-SAMCo)

This is the processor flow downstream of `order-feed-samco-bbg-audt-controller` controller flow.

This flow is triggered when the controller task uploads, the CSV batches file to the processor S3 path:
`flows/order-feed-samco-bbg-audt-processor`.

NOTE: Synthetic NEWO is not created in this flow. Also, SRP instruments are not linked. 
Instruments created by fallback task. 

## Input file
Batched CSV file containing the necessary information to process the data. 

## Output
`Order`,  `OrderState` and `QuarantinedOrder` records in ElasticSearch.

## Tasks

- ### ParametersFlowController
This flow can be executed for both a local file or one on S3.

- ### S3DownloadFile
Download CSV file from S3.

- ### LocalFile
Read local CSV file.

- ### CsvFileSplitter
Split CSV file in Batches if the input file exceeds a parametrized threshold

- ### BatchProducer
Reads each CSV chunk and produces Batches of DataFrames to downstream tasks.

- ### GetRowsByCondition
Skips some input data based on condition *(usually extraneous lines and headers)*

- ### PrimaryTransformations
Performs the initial mapping for the Order and OrderState fields that are sourced from the original file.

- ### LinkParties
Links the Party Identifiers to existing records.

- ### InstrumentFallback
Adds manually-created instruments where instruments are not populated.

- ### PartiesFallback
Adds manually-created parties where parties are not populated.

- ### AssignMetaParent
Links each OrderState record to the appropriate parent Order record.

- ### AuxiliaryFrameConcatenator
Concatenates all columns from the `PrimaryTransformations`, `LinkParties`, `LinkInstrument`
and `RemoveNewoMetaParent` tasks.

- ### OrderRecords
Filters only Order records into a data frame

- ### OrderStateRecords
Filters only OrderState records into a data frame

- ### StripPrefixOrder
Removes the prefix '_order' from all Order records

- ### StripPrefixOrderState
Removes the prefix '_orderState' from all OrderState records

- ### VerticalConcatenator
Concatenates Order and OrderState records into a single data frame

- ### RemoveInvalidAndSyntheticOrderStates
Removes invalid records with no status and removes synthetic NEWO that were created upstream.

- ### BestExecution
Populates Best Execution fields.

- ### BestExecutionConcatenator
Concatenates the data frames from `VerticalConcatenator` and `BestExecution`.

- ### AssignMeta
Creates the meta fields of the records after record validation.

- ### PostMetaConcatenator
Concatenates the data frames from `BestExecutionConcatenator` and `AssignMeta`.

- ### ElasticBulkTransformer
Creates a file containing all the Order and OrderState records to be ingested into ElasticSearch.

- ### PutIfAbsent
Uses the file created by ElasticBulkTransformer to ingest Order and OrderState records into Elasticsearch.

- ### GenerateOrderKafkaMessages
Creates Order messages for Kafka producer.   

- ### KafkaMessageProducer
Kafka Producer which writes messages into the OrderAnalyticsTopic Kafka topic.

- ### QuarantineCondition
Assesses if any record should be quarantined.

- ### QuarantinedElasticBulkTransformer
Creates a file containing all the QuarantiedOrder records to be ingested into ElasticSearch.

- ### QuarantinedPutIfAbsent
Uses the file created by QuarantinedElasticBulkTransformer to ingest QuarantiedOrder records into Elasticsearch.

- ### Noop
Dummy task, used when there is no need to quarantine.