# SE Order flow

## Description

### Things to keep in mind while developing an Order Flow
[This](https://steeleye.atlassian.net/wiki/spaces/IN/pages/328532001/Order+Feeds) confluence page documents 
the points to keep in mind while implementing an order flow.

### Concepts pertaining to an order flow

### Synthetic Order Creation

|❗ The following logic applies to the majority of the Order flows, including the universal Order Blotter, but not to all Order Flows. There are indeed Flows, where the client always sends the OrderStates accompanied by its Orders, thus we do not create Synthetic NEWOs. Synthetic NEWO handling should always be specified on the specs of any Order integration.|
|-|

The flow creates an Order record for each unique OrderState record that we have. 
This is implemented as follows:
- Split the dataframe into Order and OrderState column frame.
- Merge it vertically thereby creating duplicates.
- Remove OrderState with no order status.

Once the aforementioned vertical join is done we have to remove the extraneous 
duplicate records which we do in the following way:
- Filter NEWO which are duplicated while merging. Also 
filter out the Synthetic NEWO which are already present in ES ie has been ingested before.

Example:

For a file with 5 records *(2 order, order state pairs the other just order)*
```
ID1 Order
ID1 OrderState
ID2 Order
ID3 Order
ID3 OrderState
```

The flow would break it down into the following 2 batches provided the batch size is 4. 
So since batch 2 only had an order state record a synthetic NEWO is created as follows:
```
Batch 1:
ID1 Order
ID1 OrderState
ID2 Order
ID3 Order

Batch 2:
ID3 Synthetic Order
ID3 OrderState
```
This ensures that each Order State has an order record. This parity is needed in downstream tasks such as Best Ex.

### Getting Market End of Day Stats *(EODFileEnricher)*
SE's internal `market-data-api` returns the EOD stats for each instrument. The `Close Price` and `Currency` of 
this EOD data is used in the Best Ex OrderVolume computation.

We obtain the EOD data `market-data-api` in the following ways:
- Get all the unique `instrument ids` present in the input file post instrument linkup.
- Call `market-data-api` to fetch the `ric id` associated with each `instrument id`.
- Call `market-data-api` again with the unique `ric ids` to get the EOD data for the instrument.

`market-data-api` is calls are first batched and then invoked asynchronously with exponential retry mechanism.

Sample EOD data:

|Close Ask Price|Close Bid Price|Close Price|Currency|Date    |Exchange Code|High Ask Price|High Bid Price|High Price|Market VWAP|Low Price|Low Ask Price|Low Bid Price|Open Ask Price|Open Bid Price|Open Interest|Open Price|#RIC          |Trade Volume|
|---------------|---------------|-----------|--------|--------|-------------|--------------|--------------|----------|-----------|---------|-------------|-------------|--------------|--------------|-------------|----------|--------------|------------|
|99.6096        |99.2174        |99.2174    |USD     |17/05/22|EJV          |100.0476      |              |99.3471   |           |98.9796  |99.3706      |             |100.0476      |              |             |99.261    |00108WAP5=RRPS|            |
|99.7316        |99.3389        |99.3389    |USD     |18/05/22|EJV          |99.9289       |              |99.5353   |           |98.7537  |99.1436      |             |99.5197       |              |             |99.128    |00108WAP5=RRPS|            |

### Best Execution
Best Execution transaction volume was re-implemented according to the specs mentioned [here](https://steeleye.atlassian.net/wiki/spaces/PRODUCT/pages/2326691865/Data+Ingestion+-+Standard+ETL+s#Notional-Calculations). 
In this new implementation both Order and Order State record's Volume is computed.
