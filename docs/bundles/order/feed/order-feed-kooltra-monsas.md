# Order Feed Kooltra Monsas

Specs: [Order Kooltra Monsas Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2246902789/Order+Monsas+Kooltra+FX+Trades+Options)

This flow is triggered by dropping an S3toS3Copy task in the corresponding TR flow, which copies the input file
from `flows/tr-feed-kooltra-monsas` to `flows/order-feed-kooltra-monsas`. 

It can also be triggered directly by dropping a CSV file into the following path in S3: `flows/order-feed-kooltra-monsas`

It can handle two types of files with different headers:

* Fx Trade files: identified by the word 'trade' in the file name
* Fx Option files: identified by the word 'option' in the file name

## Input file
FxTrade or FxOption `.csv` files

## Output
`Order`, `OrderState` and `QuarantinedOrder` records in ElasticSearch

## Case-insensitive columns
Columns in the input file are case-insensitive. This is done by removing spaces from all columns, and converting them
to upper case.

## Skip logic
Records with Type = 'FXSWAP' and Leg Classification = 'SPOT' are skipped

## Tasks

1. ### ParametersFlowController
This flow can be executed for both a local file or one on S3

2. ### S3DownloadFile
Download CSV file from S3

3. ### LocalFile
Read local CSV file

4. ### CsvFileSplitter
Split CSV file in Batches if the input file exceeds a parametrized threshold

5. ### BatchProducer
Reads each CSV chunk and produces Batches of DataFrames to downstream tasks

6. ### FilterRowsInSourceFile
Filters the rows in the source CSV file by applying the required skip logic   

7. ### PrimaryTransformations
Maps all the required columns to the required target columns

8. ### LinkParties
Links the Party Identifiers to existing records

9. ### LinkInstrument
Links the Instrument Identifiers to existing records in SRP

10. ### InstrumentStrikePriceOverride
Overrides the strike price in the instrument fetched from SRP

11. ### InstrumentExpiryDateOverride
Overrides the expiry date in the instrument fetched from SRP

12. ### AssignMetaParent
Links each OrderState record to the appropriate parent Order record

13. ### AuxiliaryFrameConcatenator
Concatenates all columns from the `PrimaryTransformations`, `LinkParties`, `LinkInstrument`,
`InstrumentStrikePriceOverride`, `InstrumentExpiryDateOverride` and `AssignMetaParent` tasks

14. ### OrderRecords
Filters only Order records into a data frame

15. ### OrderStateRecords
Filters only OrderState records into a data frame

16. ### StripPrefixOrder
Removes the prefix '_order' from all Order records

17. ### StripPrefixOrderState
Removes the prefix '_orderState' from all OrderState records

18. ### VerticalConcatenator
Concatenates Order and OrderState records into a single data frame

19. ### BestExecution
Populates Best Execution fields

20. ### BestExecutionConcatenator
Concatenates the data frames from `VerticalConcatenator` and `BestExecution`

21. ### AssignMeta
Creates the meta fields of the records after record validation

22. ### PostMetaConcatenator
Concatenates the data frames from `BestExecutionConcatenator` and `AssignMeta`

23. ### ElasticBulkTransformer
Creates a file containing all the Order and OrderState records to be ingested into ElasticSearch

24. ### ElasticBulkWriter
Uses the file created by ElasticBulkTransformer to ingest Order and OrderState records into Elasticsearch 

25. ### GenerateOrderKafkaMessages
Creates Order messages for Kafka producer    

26. ### KafkaMessageProducer
Kafka Producer which writes messages into the OrderAnalyticsTopic Kafka topic

27. ### QuarantineCondition
Assesses if any record should be quarantined

28. ### QuarantinedElasticBulkTransformer
Creates a file containing all the QuarantiedOrder records to be ingested into ElasticSearch

29. ### QuarantinedElasticBulkWriter
Uses the file created by QuarantinedElasticBulkTransformer to ingest QuarantiedOrder records into Elasticsearch

30. ### Noop
Dummy task, used when there is no need to quarantine.
