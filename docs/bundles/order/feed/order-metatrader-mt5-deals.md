# Order Metatrader MT5 Deals

Specs: [Metatrader MT5 Deals confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2269807113/Order+rts22Transaction+MetaTrader+5+MT5)

The flow is triggered by dropping a CSV file into the following path in S3: 
`flows/order-metatrader-mt5-deals`

This flow is expected to create `OrderState` records only. `Order(NEWO)` records are 
created in a 
separate bundle 'order-metatrader-mt5-orders'

## Input file
Deals `.csv` file

InstrumentData - a static `.csv` file in s3 path 
"resource/metatrader/mt5/instrument/InstrumentData.csv".

## Output
`OrderState` and `QuarantinedOrder` records in ElasticSearch. Note that this flow
only loads Order (NEWO) records and not OrderStates.

## Skip logic
Ingest only records where `ACTION` is one of ['0', '1', '13', '14'].
   
## Tasks

1. ### ParametersFlowController
This flow can be executed for both a local file or one on S3

2. ### S3DownloadFile
Download CSV file from S3

3. ### LocalFile
Read local CSV file

4. ### CsvFileSplitter
Split CSV file in Batches if the input file exceeds a parametrized threshold

5. ### BatchProducer
Reads each CSV chunk and produces Batches of DataFrames to downstream tasks

6. ### OrderProgressSkipLogic
Applies the required skip logic to the flow

7. ### PrimaryTransformations
Maps all the required columns to the required target columns

8. ### LinkParties
Links the Party Identifiers to existing records

9. ### PartiesFallback
Adds skinny party info where parties are not populated

10. ### PopulateInstruments
Uses InstrumentFallback task to populate instrument data directly from a static s3 
file expected at s3 path "resource/metatrader/mt5/instrument/InstrumentData.csv".

11. ### AssignMetaParent
Links each OrderState record to the appropriate parent Order record

12. ### PrimaryFrameConcatenator
Concatenates all columns from the above tasks.

13. ### BestExecution
Populates Best Execution fields

14. ### BestExecutionConcatenator
Concatenates the data frames from `PrimaryFrameConcatenator` and `BestExecution`

15. ### ModifyBestExecutionResults
Modifies the `BestExecutionVolume` fields based on the Contract Size column.

16. ### AssignMeta
Creates the meta fields of the records after record validation

17. ### PostMetaConcatenator
Concatenates the data frames from `BestExecutionConcatenator` and `AssignMeta`

18. ### ElasticBulkTransformer
Creates a file containing all the Order and OrderState records to be ingested into ElasticSearch

19. ### ElasticBulkWriter
Uses the file created by ElasticBulkTransformer to ingest Order and OrderState records into Elasticsearch 

20. ### GenerateOrderKafkaMessages
Creates Order messages for Kafka producer    

21. ### KafkaMessageProducer
Kafka Producer which writes messages into the OrderAnalyticsTopic Kafka topic

22. ### QuarantineCondition
Assesses if any record should be quarantined

23. ### QuarantinedElasticBulkTransformer
Creates a file containing all the QuarantiedOrder records to be ingested into ElasticSearch

24. ### QuarantinedElasticBulkWriter
Uses the file created by QuarantinedElasticBulkTransformer to ingest QuarantiedOrder records into Elasticsearch

25. ### Noop
Dummy task, used when there is no need to quarantine.
