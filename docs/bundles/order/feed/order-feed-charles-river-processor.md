# Order Feed Charles River Processor

Specs: [Feed Charles River Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2450587649/Order+Charles+River)

This is the processor flow downstream of `order-feed-charles-river-controller` controller flow. The processor logic 
is divided in two flows namely `order-feed-charles-river-order-processor` 
and `order-feed-charles-river-allocation-processor` for orders and allocation flow respectively. 
The two processor flows are majorly the same except for the record creation and transformation logic.

This flow is triggered when the controller task uploads, the CSV batches file to the processor S3 path. 
Which are as follows:
* order: `flow/order-feed-charles-river-order-processor`
* allocation: `flow/order-feed-charles-river-allocation-processor`

It can handle 4 types of files with different headers:

* "Allocations" files: identified by the word `allocations` in the file name
* "Orders" files: identified by the word `orders` in the file name
* "Fill" files: identified by the word `fills` in the file name
* "Place" files: identified by the word `place` in the file name

## Input file
Batched CSV file containing the necessary information to process the data according to either allocation or orders flow. 

## Output
`Order`,  `OrderState` and `QuarantinedOrder` records in ElasticSearch. 
Orders flow produces `NEWO` and `PARF`. Whereas Allocation flow produces `NEWO` and `FILL`.

## Tasks

- ### ParametersFlowController
This flow can be executed for both a local file or one on S3.

- ### S3DownloadFile
Download CSV file from S3.

- ### LocalFile
Read local CSV file.

- ### CsvFileSplitter
Split CSV file in Batches if the input file exceeds a parametrized threshold

- ### BatchProducer
Reads each CSV chunk and produces Batches of DataFrames to downstream tasks.

- ### PrimaryTransformations
Performs the initial mapping for the Order and OrderState fields that are sourced from the original file.

- ### LinkParties
Links the Party Identifiers to existing records.

- ### LinkInstrument
Links the Instrument Identifiers to existing records in SRP.

- ### InstrumentFallback
Adds manually-created instruments where instruments are not populated.

- ### AssignMetaParent
Links each OrderState record to the appropriate parent Order record.

- ### RemoveNewoMetaParent
This task removes the \_\_meta_parent__ field for any NEWO record, as they are not necessary.

- ### AuxiliaryFrameConcatenator
Concatenates all columns from the `PrimaryTransformations`, `LinkParties`, `LinkInstrument`
and `RemoveNewoMetaParent` tasks.

- ### BestExecution
Populates Best Execution fields.

- ### BestExecutionConcatenator
Concatenates the data frames from `VerticalConcatenator` and `BestExecution`.

- ### AssignMeta
Creates the meta fields of the records after record validation.

- ### PostMetaConcatenator
Concatenates the data frames from `BestExecutionConcatenator` and `AssignMeta`.

- ### ElasticBulkTransformer
Creates a file containing all the Order and OrderState records to be ingested into ElasticSearch.

- ### PutIfAbsent
Uses the file created by ElasticBulkTransformer to ingest Order and OrderState records into Elasticsearch.

- ### GenerateOrderKafkaMessages
Creates Order messages for Kafka producer.   

- ### KafkaMessageProducer
Kafka Producer which writes messages into the OrderAnalyticsTopic Kafka topic.

- ### QuarantineCondition
Assesses if any record should be quarantined.

- ### QuarantinedElasticBulkTransformer
Creates a file containing all the QuarantiedOrder records to be ingested into ElasticSearch.

- ### QuarantinedPutIfAbsent
Uses the file created by QuarantinedElasticBulkTransformer to ingest QuarantiedOrder records into Elasticsearch.

- ### Noop
Dummy task, used when there is no need to quarantine.