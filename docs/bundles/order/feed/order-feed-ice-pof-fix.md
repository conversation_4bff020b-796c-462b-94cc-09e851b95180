# Order Feed Ice Pof Fix

Specs: [Order Feed Ice Pof Fix](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2333900853/Order+ICE+POF+FIX)

This flow is triggered by a cloudwatch rule on a daily basis. Based on a prefix and flow args
all the fix files from the given directory (with date if provided in the flow_args) are downloaded
and transformed. The args in flow_args:
"delta_in_days" -> int; if 0 -> download FIX files from day of Flow execution (T);
    if 1 -> download FIX files from day before Flow Execution (T-1), etc...
"prefix" -> str; path where to look for FIX files in the tenant's S3 bucket . In prod, it is ingress/raw/order-feed-ice-pof-fix/


## Input file
This flow isn't triggered by a file, it uses flow_args

## Output
`Order`, `OrderState` and `QuarantinedOrder` records in ElasticSearch

## Skip logic
((`TrdType`.astype('string') != ['K','0']) | `ff_9029`.isnull() ) & (`ff_9022`.fillna('0').astype('int')<=1) -
Only process trades which matches the above condition.

## Tasks

-  ### FetchAndMergeTextFiles
Fetch all the FIX files based on flow_args and merge them into one DataFrame

- ### FixParser
Read the FixFiles and convert them to FixParserResult dataclass

- ### FixParserResultToFrame
Convert FixParserResult to Pandas DataFrame

- ### PrimarySkipLogic
Skips any records that doesn't match the given query

- ### PrimaryTransformations
Maps all the required columns to the required target columns

- ### FetchVDIAndSplitMultiLegTrades
Fetches instruments from `venueDirectInstrument` index in srp and splits the
multileg trades into single legs. And changes the orderid and orderqty as required.

- ### MapVenueAndPriceFieldSingleLeg
Update and populate the venue and price related fields based on the instruments for each order

- ### LinkParties
Links the Party Identifiers to existing records

- ### AssignMetaParent
Links each OrderState record to the appropriate parent Order record

- ### AuxiliaryFrameConcatenator
Concats `AllLegsPrimaryFrameConcatenator` , `LinkParties`, `AssignMetaParent`, `MapVenueAndPriceFieldSingleLeg`

- ### OrderRecords
Filters only Order records into a data frame

- ### OrderStateRecords
Filters only OrderState records into a data frame

- ### StripPrefixOrder
Removes the prefix '_order' from all Order records

- ### StripPrefixOrderState
Removes the prefix '_orderState' from all OrderState records

- ### VerticalConcatenator
Concatenates Order and OrderState records into a single data frame

- ### RemoveInvalidOrderStates
Removes invalid orderstates

- ### RemoveDupNEWO
Remove duplicates NEWO (synthetic NEWO) for the orders which already had a NEWO in the file

- ### BestExecution
Populates Best Execution fields

- ### BestExecutionConcatenator
Concatenates the data frames from `VerticalConcatenator` and `BestExecution`

- ### AssignMeta
Creates the meta fields of the records after record validation

- ### PostMetaConcatenator
Concatenates the data frames from `BestExecutionConcatenator` and `AssignMeta`

- ### ElasticBulkTransformer
Creates a file containing all the Order and OrderState records to be ingested into ElasticSearch

- ### ElasticBulkWriter
Uses the file created by ElasticBulkTransformer to ingest Order and OrderState records into Elasticsearch

- ### GenerateOrderKafkaMessages
Creates Order messages for Kafka producer

- ### KafkaMessageProducer
Kafka Producer which writes messages into the OrderAnalyticsTopic Kafka topic

- ### QuarantineCondition
Assesses if any record should be quarantined

- ### QuarantinedElasticBulkTransformer
Creates a file containing all the QuarantiedOrder records to be ingested into ElasticSearch

- ### QuarantinedElasticBulkWriter
Uses the file created by QuarantinedElasticBulkTransformer to ingest QuarantiedOrder records into Elasticsearch

- ### Noop
Dummy task, used when there is no need to quarantine.
