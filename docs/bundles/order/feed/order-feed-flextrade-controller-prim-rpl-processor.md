# Order Feed Flextrade Controller + PRIM/RPL Processor

Specs: [Order Flextrade Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2473590785/Order+FlexTrade)

The flow is triggered by dropping a CSV file into the following path in S3: `flows/order-feed-flextrade-controller-prim-rpl-processor`.

## Input File
The input file is a single file containing three different headers, and data corresponding to each of the
headers. So essentially, three different types of data are present in the same file.
The 3 types of data are 
- PRIM & RPL - order is placed & order is replaced respectively
- EXE - order is executed
- CXL - orders is cancelled

# Orchestration
This flow is a controller flow, but also doubles as a processor flow for PRIM/RPL records (NEWO records). It extracts
PRIM/RPL, EXE and CXL headers and records are extracted from the consolidated file (the first column indicates what
kind of record a data row corresponds to).
The PRIM and RPL records are processed in this flow, but the CXL and EXE records are written to a file,
and processed in separate flows:
* `order-feed-flextrade-exe-processor`: Subsequent flow which processes EXE records AFTER the PRIM/RPL records are in Elastic
* `order-feed-flextrade-cxl-processor`: Subsequent flow which processes CXL records AFTER the PRIM/RPL records are in Elastic

Note that each record needs to join with other records to get certain data. So, PRIM/RPL records need to join
with EXE records, EXE with PRIM/RPL, CXL with PRIM/RPL and EXE.

As this is the controller flow for the subsequent flows, it needs to make sure that the columns required in
the subsequent flows are written to the respective output files.

## Output
* `Order` and `QuarantinedOrder` records in ElasticSearch for PRIM/RPL records


## Output Files to trigger the subsequent flows
* EXE records are written to S3 to the `flows/order-feed-flextrade-exe-processor` path. Multiple batches
  of EXE records are written into multiple files, as a single file is too large to process without OOM issues.
* CXL records written to S3 to the `flows/order-feed-flextrade-cxl-processor` path


## Skip logic
The skip logic for PRIM/RPL records is based on the file name. The date is extracted from the file name
using a regex (date format: yyyymmdd). If the date is greater than the PRIM/RPL column 'order-time', the
record is skipped. There is no skip logic for the EXE and CXL records.


## Joins
* None of the records (PRIM/RPL, EXE, CXL) are self-contained. They all need information from other records.
* PRIM/RPL records are enriched with columns from EXE records
* EXE records are enriched with columns from PRIM/RPL records 
* CXL records are enriched with columns from both PRIM/RPL records and EXE records

## Tasks

-  ### ParametersFlowController
This flow can be executed for both a local file or one on S3

- ### S3DownloadFile
Download CSV file from S3

- ### LocalFile
Read local CSV file

- ### CreateFlextradeDataFrames
This is where a lot of the hard work happens. 

The consolidated input file is split into its 3 parts. 
Records are joined together so that they have all the required data. Skip logic is applied to the PRIM/RPL 
records based on the date in the file name. 
It returns a dictionary with the type of file as keys and the relevant dataframe as values.
See the task docstring for further details.

- ### ExeFrameExtractor
Extracts the EXE data frame from the dictionary returned by `CreateFlextradeDataFrames`.

- ### ExeExtractPathResult
Writes the EXE data frame to a file, and produces an ExtractPathResult

- ### CsvExeFileSplitter
As there are usually a lot of EXE records, the EXE file created in ExeExtractPathResult is split
into batches

- ### ExeS3FileListFileSplitterResultList
Converts the EXE FileSplitterResultList into an S3 File list so that they can be uploaded to S3.

- ### CxlFrameExtractor
Extracts the CXL data frame from the dictionary returned by `CreateFlextradeDataFrames`.

- ### CxlExtractPathResult
Writes the CXL data frame to a file, and produces an ExtractPathResult

- ### CxlS3FileListFromExtractPathResultList
Converts the CXL ExtractPathResult into an S3 File list so that it can be uploaded to S3.

- ### PrimRplFrameExtractor
Extracts the PRIM/RPL data frame from the dictionary returned by `CreateFlextradeDataFrames`.

- ### InlineFixParserExeUserFixtag
Parses the fix fields present in the exe_userfixtag column. It also prefixes the parsed columns 
with exe_user_fix_tag_.

- ### InlineFixParserPrimRplFixtags
Parses the fix fields present in the prim_rpl_fixtags column. It also prefixes the parsed columns 
with prim_rpl_fix_tag_.

- ### SourceFrameConcatenator
Concatenates the fix columns with the regular columns

- ### PrimRplExtractPathResult
Writes the PRIM/RPL data frame to a file, and produces an ExtractPathResult

- ### CsvPrimRplFileSplitter
Splits CSV file in Batches if the input file exceeds a parametrized threshold

- ### BatchProducer
Reads each CSV chunk and produces Batches of DataFrames to downstream tasks

- ### ExtractBatchFromFrameProducerResult
Extracts the batch from the Batch Producer result

- ### FlextradePrimRplTransformations
Maps all the required columns to the required target columns

- ### LinkParties
Links the Party Identifiers to existing records

- ### LinkInstrument
Links the Instrument Identifiers to existing records in SRP

- ### InstrumentFallback
Adds manually-created instruments where instruments are not populated 
  
- ### PrimaryFrameConcatenator
Concatenates all columns from the `FlextradeCxlTransformations`, `LinkParties`, `LinkInstrument`,
and `AssignMetaParent` tasks

- ### BestExecution
Populates Best Execution fields

- ### BestExecutionConcatenator
Concatenates the data frames from `VerticalConcatenator` and `BestExecution`

- ### AssignMeta
Creates the meta fields of the records after record validation

- ### PostMetaConcatenator
Concatenates the data frames from `BestExecutionConcatenator` and `AssignMeta`

- ### ElasticBulkTransformer
Creates a file containing all the Order and OrderState records to be ingested into ElasticSearch

- ### ElasticBulkWriter
Uses the file created by ElasticBulkTransformer to ingest Order and OrderState records into Elasticsearch 

- ### GenerateOrderKafkaMessages
Creates Order messages for Kafka producer    

- ### KafkaMessageProducer
Kafka Producer which writes messages into the OrderAnalyticsTopic Kafka topic

- ### QuarantineCondition
Assesses if any record should be quarantined

- ### QuarantinedElasticBulkTransformer
Creates a file containing all the QuarantiedOrder records to be ingested into ElasticSearch

- ### QuarantinedElasticBulkWriter
Uses the file created by QuarantinedElasticBulkTransformer to ingest QuarantiedOrder records into Elasticsearch

- ### Noop
Dummy task, used when there is no need to quarantine.

- ### S3UploadExeFile
Uploads the created EXE files to S3 to trigger the EXE flow.

- ### S3UploadCxlFile
Uploads the created CXL file to S3 to trigger the EXE flow.