# Reference Instrument Flow: SE Instrument CFD/SB-Index Futures Transformer

Specs: [SE Instrument CFD/SB-Index Futures Transformer Confluence page](https://steeleye.atlassian.net/wiki/spaces/PRODUCT/pages/2170454019/SteelEye+Instruments+-+CFD+SB+Transformer+-+Index+Futures)

This flow is set as `platform: true` as it's meant to be run on the SRP stack.

### Input
flow arguments `time_difference` or `es_start_time` and `es_end_time`, indicating the time period for which to run the flow against

## OutPut
`SteelEyeInstrument` records in ElasticSearch. It creates two records for each Instrument found in FetchInstruments. One CFD and one SB.

## Tasks

1. ### FlowArgsValidator
Validates the flow args, ensuring a valid time interval is set

2. ### FetchInstruments
Fetches the FirdsInstrument and FcaFirdsInstrument records from SRP for the time interval set

3. ### BatchProducer
Reads each CSV chunk and produces Batches of DataFrames to downstream tasks

4. ### PrimaryTransformations
Performs the initial mapping for the SteelEyeInstrument fields that are sourced from the original file

5. ### CFDRecords
Removes any SB Record field

6. ### SBRecords
Removes any CFD Record field

7. ### StripPrefixCFD
Removes `.__cfd` prefix from columns

8. ### StripPrefixSB
Removes `.__sb` prefix from columns

9. ### VerticalConcatenator
Concatenates CFD and SB records

10. ### AssignMeta
Creates the meta fields of the records after being validated

11. ### PostMetaConcatenator
Concatenates all columns from `VerticalConcatenator` and `AssignMeta`

12. ### ElasticBulkTransformer
Converts the records to be ingested into ElasticSearch

13. ### PutIfAbsent
Writes the records into ElasticSearch