# MyMarket Universal SteelEye Firm Flow

This is a universal flow which ingests records into the MarketCounterparty model
from a CSV file. Duplicates and invalid records are removed (covered in the following
sections), and the remaining records are ingested into Elasticsearch. Additional
fields in the MarketCounterparty records are fetched by linking to the SRP model
LeiRecord.

### Models
<b>MarketCounterparty</b>: records are ingested into this SDP model <br/>
<b>LeiRecord</b>: additional fields are fetched from this SRP model
<b>SinkFileAudit</b>: audit messages and statistics are written into this model

### Duplicate detection
1. Records in the file are marked as duplicates if they have the same LEI and/or the same name as another record.
2. A lookup is done on MarketCounterparty to find records with (i) the same LEI or (ii) the same name
as the records in the input file. Any such records are not ingested

### Skip logic
Records without a name in the input file are dropped.

### Notes about Columns in the Input File
The TradeFileIdentifiers column can be specified multiple times in the input file. The
appropriate target columns are populated by combining values from all these columns.
<br/>
All the columns in the input file are case-insensitive.

### Special Columns in the Input File
The TradeFileIdentifiers column can be specified multiple times in the input file. The
appropriate target columns are populated by combining values from these repeating columns.

### Unique Ids / Unique Props
The uniqueIds and &uniqueProps fields are populated by combining all valid LEIs from the
TradeFileIdentifiers and LEI columns in the input file, and all non-LEI values in
TradeFileIdentifiers. uniqueIds and &uniqueProps should have the appropriate prefixes
('lei' for LEIs and 'account'/'id' for non-LEI values) and should be in lower case.


### Confluence Page
[MyMarket Universal Steeleye Firm Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/**********/Market+SteelEye+Template+-+Firm)