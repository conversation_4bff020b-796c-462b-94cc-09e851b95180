# MyMarket BP Person Processor Flow

This is the second of two BP Person flows and runs after (and is triggered by) the
MyMarket BP Person Controller flow. 
It takes as input a CSV file containing links to two Market files: a BP file, and an auxiliary
KPMG file which is used to fetch email addresses. The target model is Account<PERSON>erson.

All the files are 'full-refresh' files. This means that the client sends data about all their
employees every day. Each employee is identified by a unique 'NTID'. So, after removing duplicate
rows in the files (based on NTID -- the input files have many duplicate rows with 1/2 fields changed)
and joining them together, the flow does the following
- Creates new AccountPerson records for the new NTIDs (not seen in the previous day's files)
- Updates AccountPerson records for NTIDs which have already been seen in the previous day's file. This is
  done using an 'index' operation so existing records are replaced rather than updated.
- Deletes AccountPerson records for any NTIDs from the previous day's file which are not seen in
  the current file. This is done by maintaining 'state info' in S3 -- in a file called 
  state_info/mymarket-bp-person-processor/ntid_list.json. A processed subdirectory in the same directory
  keeps NTIDs corresponding to each day's files. Note that there are also additional 'backup’ files (one per run) created
  in S3 in the foll. path: state_info/mymarket-bp-person-processor/processed/. These files contain
  NTIDs corresponding to each run.

### Models
<b>AccountPerson</b>: records are created in/updated in/deleted from this SDP model
file is 'Employee'. <br/>
<b>SinkFileAudit</b>: audit messages and statistics are written into this model

### Notes about Columns in the Input File
The input CSV file has 2 columns`s3_bpistmrl_file_url` and `s3_kpmgrmrl_file_url`, which have links
to the BP and KPMG files respectively.

### Unique Ids / Unique Props
The uniqueIds and &uniqueProps fields are populated by combining all unique phone numbers (e164-normalised),
imAccount ids and email IDs.

### Confluence Page
[BP Market Person Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/**********/BP+-+Market+Person+Feed)

## Tasks
- #### ParametersFlowController
This flow can be executed for both a local file or one on S3

- #### S3DownloadFile
Downloads CSV file produced by the Controller flow from S3

- #### LocalFile
Reads local Batch CSV file

- #### ReadBatchCSV
Reads the input batch CSV file and creates a data frame with 2 columns: `s3_bpistmrl_file_url` and `s3_kpmgrmrl_file_url`.

- #### ReadAndJoinBpMarketFiles
This task reads the 2 S3 Market files (BP+KPMG) after downloading them based on the S3 links in the
2 columns of the input data frame. It then drops duplicates in both files based on the NTID field.
Finally, it combines the 2 files together to produce a single data frame.

- #### PersonTransformations
Creates all the required Person fields including the uniqueIds field, which is later used to overwrite
the NULL value which the schema assigns to uniqueProps.

- #### PersonTransformations
Creates all the required AccountPerson fields

- #### AssignMeta
Adds meta fields for the AccountPerson model. As AccountPerson has no id props, its uniqueProps will
always be set to null by this task. It generates a uuid for the &id/&key fields.

- #### PrimaryFrameConcatenator
Concatenates the AccountPerson meta fields with the AccountPerson data fields

- #### PopulateUniqueProps
Populate &uniqueProps from the uniqueIds column created in PersonTransformations

- #### FinalFrameConcatenator
Discards the null &uniqueProps field created by AssignMeta and replaces it with the &uniqueProps field
from PopulateUniqueProps

- #### FetchPersonRecordsFromElastic
Fetches AccountPerson records from Elastic to work out if we have to create or update the records. If
an update is needed to an existing record, the &id and &key from the current run (which are both uuids)
are replaced by the &id and &key from the existing record. This task also adds an indicator field to
indicate whether a record has to be created or updated downstream.

- #### CreateRecords
Filters AccountPerson records which need to be inserted into Elasticsearch into a separate data frame

- #### DropCreateIndicator
Drops the indicator column created in FetchPersonRecordsFromElastic from the CreateRecords data frame

- #### ElasticBulkTransformerCreate
Creates an ndjson file containing all the AccountPerson records to be inserted into ElasticSearch

- #### ElasticBulkWriterCreate
Uses the file created by ElasticBulkTransformerCreate to bulk-insert AccountPerson records into Elasticsearch

- #### UpdateRecords
Filters AccountPerson records which need to be updated in Elasticsearch into a separate data frame

- #### DropUpdateIndicator
Drops the indicator column created in FetchPersonRecordsFromElastic from the UpdateRecords data frame

- #### ElasticBulkTransformerIndex
Creates an ndjson file containing all the AccountPerson records to be updated (indexed) into ElasticSearch. index is 
used rather than update so that all existing records are replaced/overwritten.

- #### ElasticBulkWriterIndex
Uses the file created by ElasticBulkTransformerCreate to bulk-index AccountPerson records in Elasticsearch.

- #### GetRecordsForDeletion
Gets a list of all PersonColumns.OFFICIAL_IDENTIFIERS_EMPLOYEE_ID values (in BP terminology, these are 'NTIDs'.
It then compares the NTIDs from the previous day (previous run) with the 
current NTIDs. To do this, it writes a file in S3 (prefix "state_info/mymarket-bp-person-processor/ntid_list.json")
every time the flow runs. Each time, this file is overwritten with the latest run's NTIDs. On the next run,
it fetches this file (which will contain data from the previous run). If there are any NTIDs in the
previous file which are not in the current file, the records corresponding to these NTIDs need to
be deleted.
So, the &id, &hash and &model are fetched for these records, and they are sent to downstream
ElasticBulkTransformer and ElasticBulkDeleter tasks to actually delete the records.

- #### ElasticBulkTransformerUpdate
Creates an ndjson file containing all the AccountPerson records to be deleted from ElasticSearch

- #### ElasticBulkDeleter
Uses the file created by ElasticBulkTransformerCreate to bulk-delete AccountPerson records in Elasticsearch
