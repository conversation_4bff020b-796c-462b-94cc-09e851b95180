# Redbox Voice Flow

This flow processes Redbox metadata and recording files. The metadata and recording files are placed in
the respective S3 folders by the [se-drop-service](https://github.com/steeleye/se-drop-service/blob/master/steeleye_drop_service/service/handlers/drop_handler.py),
through which Redbox send the files to Steeleye. se-drop-service has Redbox-specific logic to get the
right paths for both the metadata and recording files (dates are included in the path as well).

### Specs
[Redbox Voice Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2289729860/Call+Red+Box)

### Input File
CSV file containing S3 links to Redbox JSON Metadata files. 

### Input Paths

Metadata files are dropped into `stream/comms-voice-redbox/YYYY/MM/DD` by se-drop-servcie and get batched
into a Batch CSV file which is written to `flows/comms-voice-redbox`.
The attachment files are stored by se-drop-service in `attachments/stream/comms-voice-redbox/YYYY/MM/DD`

## Output
`Call` and `Attachment` records in ElasticSearch. 

## Legacy handler
This was migrated from a legacy handler: https://github.com/steeleye/feeds-redbox-voice-handler

## Tasks
- #### ParametersFlowController
This flow can be executed for both a local file or one on S3

- #### S3DownloadFile
Downloads Batch CSV file from S3

- #### LocalFile
Reads local Batch CSV file

- #### ReadBatchCSV
Reads the input batch CSV file and creates a data frame with a file_url column

- #### DeriveAttachmentPathFromMetadataPath
Derives the attachment paths from the metadata paths. For Redbox, both the metadata and attachment files
are dropped into date-wise folders.

- #### S3AttachmentMetadata
Uses 'head_object' to get the attachment model fields from the respective S3 files

- #### AttachmentStatic
Used to populate the `__meta_model__` field for assigning meta fields to the Attachment records

- #### AttachmentConcatenator
Combine data frames from S3AttachmentMetadata and AttachmentStatic

- #### AssignAttachmentMeta
Adds meta fields for the Attachment model

- #### AttachmentWithMeta
Concatenates data frames from AssignAttachmentMeta and AttachmentConcatenator

- #### ElasticBulkTransformerAttachment
Creates a file containing all the Attachment records to be ingested into ElasticSearch

- ### PutAttachment
Uses the file created by ElasticBulkTransformer to ingest Attachment records into Elasticsearch 

- #### AddVoiceFilePrefix
Adds the prefix 'voiceFile.' into the Attachment columns so that it can be integrated into the
Call data frame.

- #### JsonBatchCsvDownload
Reads the metadata json files whose links are in the batch csv and creates a data frame with all
the data in the JSON files

- #### CallTransformations
Creates all the Call fields other than the voiceFile fields (which come directly from the attachment)
and hasAttachment/connected

- #### VoiceFileCallConcatenator
Concatenates voiceFile fields to the Call fields

- #### HasAttachmentAndConnected
Populates hasAttachment and connected based on if an Attachment was found or not

- #### PreMetaConcatenator
Consolidates all Call fields together to prepare to send to AssignMeta

- #### AssignCallMeta
Adds meta fields for the Call model

- #### FinalFrameConcatenator
Concatenates the Call meta fields with the Call data fields

- #### ElasticBulkTransformer
Creates a file containing all the Call records to be ingested into ElasticSearch

- #### PutCall
Uses the file created by ElasticBulkTransformer to ingest Call records into Elasticsearch 

- #### FetchTenantConfiguration
Fetches the tenant configuration record to get the contractual limits for transcription

- #### TranscriptionRouter
Triggers Deepgram transcription