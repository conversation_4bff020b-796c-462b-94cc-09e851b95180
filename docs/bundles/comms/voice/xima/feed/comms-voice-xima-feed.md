
# XIMA Voice Feed Flow

This flow processes the XIMA metadata and recording files fetched and placed on s3 by
comms-voice-xima-api flow.

### Specs
[XIMA Call Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2392457217/Call+Xima+IPSolutions+VoIP)

### Input File
csv file containing json metadata and mp3 recording files path. 

## Output
`Call` and `Attachment` records in ElasticSearch. 

## Tasks
1. #### ParametersFlowController
This flow can be executed for both a local file or one on S3

2. #### S3DownloadFile
Download CSV file from S3

3. #### LocalFile
Read local CSV file

4. #### CsvFileSplitter
Split CSV file in Batches if the input file exceeds a parametrized threshold

5. #### BatchProducer
Reads each CSV chunk and produces Batches of DataFrames to downstream tasks

6. #### JsonBatchCsvDownload
Read the metadata json file and converted it into dataframe

7. #### AttachmentFromS3Metadata
Creates Attachment model fields from the S3 recording files

8. #### AttachmentStatic
Used to populate the `__meta_model__` field for assigning meta fields to the Attachment records

9. #### AttachmentConcatenator
Combine data frames from 8. (AttachmentFromS3Metadata) and 9. (AttachmentStatic)

10. #### AssignAttachmentMeta
Adds meta fields for the Attachment model

11. #### AttachmentWithMetaConcatenator
Concatenates data frames from 10. and 11.

12. #### ElasticBulkTransformerAttachment
Creates a file containing all the Attachment records to be ingested into ElasticSearch

13. ### PutAttachment
Uses the file created by ElasticBulkTransformer to ingest Attachment records into Elasticsearch 

14. #### AddVoiceFilePrefix
Adds the prefix 'voiceFile.' into the Attachment columns so that it can be integrated into the
Call data frame.

15. #### CallPrimaryTransformations
Creates all the Call fields other than the voiceFile fields (which come directly from the attachment)

16. #### AssignCallMeta
Adds meta fields for the Call model

17. #### FinalFrameConcatenator
Concatenates the Call meta fields with the Call data fields and the Attachment (voice file) fields

18. #### ElasticBulkTransformer
Creates a file containing all the Call records to be ingested into ElasticSearch

19. ### PutCall
Uses the file created by ElasticBulkTransformer to ingest Call records into Elasticsearch 

20. ### FetchTenantConfiguration
This task will fetch the TenantConfiguration object from the current stack's elasticsearch cluster.

21. ### TranscriptionRouter
Trigger Transcription service based on TenantConfiguration.
