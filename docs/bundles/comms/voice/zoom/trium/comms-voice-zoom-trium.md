# Zoom (Trium) Voice Flow

This flow ingests Zoom Call (meeting) records into Elasticsearch. 
The input file is the recoding file itself with an .m4a extension. 
All the metadata regarding the call is contained within the filename itself. 

### Specs
[Zoom Trium Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2443149313/Call+Zoom+audio+Trium)

### Input File
The input file for this feed is a batch CSV which contains s3 paths of the m4a recoding files.
The m4a files are dropped to `stream/comms-voice-zoom-trium` directory, and a csv with links to these m4a files is created at `flows/comms-voice-zoom-trium` which triggers this flow. 
The recording filename contains all the metadata regarding the call. 

## Output
`Call` records in ElasticSearch 

## Tasks
-  #### ParametersFlowController
This flow can be executed for both a local file or one on S3

- #### S3DownloadFile
Download CSV file from S3

- #### LocalFile
Read local CSV file

- #### ReadBatchCSV
Read Batch CSV containing 2 columns (file_url and realm) and return a DataFrame

- #### S3AttachmentMetadata
Creates a dataframe with attachment metadata from the attachment column having attachment s3 paths

- #### FormatVoiceFile
Adds the prefix 'voiceFile.' into the Attachment columns so that it can be integrated into the
Call data frame.

- #### RegexGetColumnsFromGroups
This task extracts Call metadata from the filenames of the recordings

- #### PrimaryTransformations
Creates all the Call fields other than the voiceFile fields (which come directly from the attachment)

- #### CallAndAttachment
Concatenates data frames from `FormatVoiceFile` and `PrimaryTransformations`

- #### AssignMeta
Adds meta fields for the Call model

- #### FinalFrameConcatenator
Concatenates the Call meta fields (output of `AssignMeta`) amd `CallAndAttachment`

- #### ElasticBulkTransformer
Creates a file containing all the Call records to be ingested into ElasticSearch

- #### PutCall
Uses the file created by ElasticBulkTransformer to ingest Call records into Elasticsearch 

- #### FetchTenantConfiguration
Fetches the tenant configuration record to get the contractual limits for transcription

- #### TranscriptionRouter
Triggers Deepgram transcription

- #### TriggerWaveformFlow
Trigger comms-voice-waveform-generator flow