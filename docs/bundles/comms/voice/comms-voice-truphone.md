# Truphone Voice Flow

This flow ingests Truphone call records into ElasticSearch. The input file is a csv which contains just
two columns. The input file contains the S3 path of the call metadata csv file and S3 path to the zip 
file which contains the recordings for the corresponding calls.  

### Specs
[Truphone Voice Mappings](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2315059213/Call+Feed+-+Truphone)

### Input File
CSV with link to the call metadata (single csv for multiple calls) and link to the zip file consisting of the 
call recordings. 

## Output
`Call` and `Attachment` records in ElasticSearch. 

## Tasks
- #### ParametersFlowController
This flow can be executed for both a local file or one on S3

- #### S3DownloadFile
Download CSV file from S3

- #### LocalFile
Read local CSV file

- #### ReadBatchCSV
Reads the input CSV file

- #### CSVBatchCSVDownloader
Uses the column name as param and downloads the call metadata csv file and converts that into a dataframe. 

- #### UnzipUploadAddAttachmentKeyTruphone
As name suggests, this tasks does the following:
    - Downloads the recordings zip file
    - Unzips the zip files 
    - Uploads each `attachment` to `attachments/comms-voice-truphone`
    - Adds a column with the attachment S3 paths to source_frame 

- #### CreateAttachment
Creates Attachment model fields from the S3 recording files

- #### AttachmentStatic
Used to populate the meta model field for assigning meta fields to the Attachment records

- #### PrimaryFrameConcatenatorAttachment
Combine data frames from 6. and 7.

- #### AssignAttachmentMeta
Adds meta fields for the Attachment model

- #### FinalFrameConcatenatorAttachment
Concatenates data frames from 8. and 9.

- #### ElasticBulkTransformerAttachment
Creates a file containing all the Attachment records to be ingested into ElasticSearch

- #### PutAttachment
Uses the file created by ElasticBulkTransformer to ingest Attachment records into Elasticsearch

- #### PrimaryTransformations
Creates all the Call fields other than the voiceFile fields (which come directly from the attachment)

- #### AssignCallMeta
Adds meta fields for the Call model

- #### FormatVoiceFile
Adds the prefix 'voiceFile.' into the Attachment columns so that it can be integrated into the
Call data frame.

- #### FinalFrameConcatenator
Concatenates the Call meta fields with the Call data fields and the Attachment (voice file) fields

- #### ElasticBulkTransformerCall
Creates a file containing all the Call records to be ingested into ElasticSearch

- #### PutCall
Uses the file created by ElasticBulkTransformer to ingest Call records into Elasticsearch 
