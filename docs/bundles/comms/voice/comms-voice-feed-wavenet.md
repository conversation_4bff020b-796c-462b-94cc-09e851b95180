# Wavenet Voice Flow

This flow ingests Wavenet Calls into Elasticsearch. The input to this flow is a batch CSV
file containing S3 links to WAV (recording) and/or XML (metadata) files.
We can have three different cases (called branches below), all of which are handled in this flow. 
The code branches off in three distinct directions to handle these 3 cases.
<br/>

#### Branch 1: We have rows for both the recording and metadata files in the batch CSV: 
This is the most common case. IMPORTANT: in this case, we have 2 rows for a pair of recording-metadata
files, and we have to process ONLY one of the rows!
The `GetOtherFileUrl` handles this case by discarding the recording file row for cases where
there is already a metadata file with the same name. For this case, we do the complete
attachment and metadata processing, like in all other Voice flows.
   
#### Branch 2: We have both ONLY the metadata file in the batch CSV, and there is no matching recording
When we have only a metadata file in the batch CSV, we look for a matching attachment record in
Elasticsearch (using the fileInfo.location.key as the key). If found, these details are added to the
'voiceFile' field of the Call record. If  no matching Attachment record is found, the call record is ingested
without attachments/voiceFiles

#### Branch 3: We have both ONLY the recording file in the batch CSV, and there is no matching metadata file
When we have only a recording file in the batch CSV, the recording/attachment is ingested into Elasticsearch. 
After it is ingested, we look for a matching Call record in  Elasticsearch (using the sourceKey as the lookup field).
If there are any matches, these Call records are fetched and updated with the attachment details from the current batch.

### Assumption
It is assumed that the recording and metadata will arrive in the SAME folder in stream/comms-voice-feeds-wavenet.
This assumption has to be made, as we have no way to get the corresponding recording file from a metadata file
(or vice versa) if it is present in different sub-folders (e.g. stream/comms-voice-feeds-wavenet/2022/4/20
and stream/comms-voice-feeds-wavenet/2022/4/21).

### Specs
[Wavenet Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/1877573677)

### Input File
Batch CSV file containing S3 links to WAV (recording) and/or XML (metadata) files.  

## Output
`Call` and `Attachment` records in ElasticSearch. Old Call records may be updated in cases where the
attachment comes in a later batch.

## Tasks

Note that when a task has a branch in brackets after the task name, it signifies that it belongs to
one of the 'branches' defined above

- #### ParametersFlowController
This flow can be executed for both a local file or one on S3

- #### S3DownloadFile
Downloads CSV file from S3

- #### LocalFile
Reads local CSV file

- #### ReadBatchCSV
Reads the input CSV file

- #### GetOtherFileUrl
Task which gets the 'other' file url (metadata for recording, and recording for metadata'.
Also, when there are pair files (metadata and recordings) in 2 different rows, the task discards one of the
rows to prevent the same files being processed twice.

- #### RecordingsAndMetadata
Creates Branch 1: Gets rows where we have both the recording and metadata files in the batch CSV

- #### MetadataOnly
Creates Branch 2 : Gets rows where we have both ONLY the metadata file in the current batch CSV.

- #### RecordingsOnly
Creates Branch 3: Gets rows where we have both ONLY the recording file in the current batch CSV

- #### S3AttachmentMetadataStandard (Branch 1)
Gets the S3 Head object metadata for the recording files (file info, location, size etc)

- #### AttachmentStaticStandard (Branch 1)
Used to populate the meta model field for Attachment records

- #### AttachmentConcatenatorStandard (Branch 1)
Concatenates the data frames from the previous 2 tasks

- #### AssignAttachmentMetaStandard (Branch 1)
Adds meta fields for the Attachment model

- #### AttachmentWithMetaConcatenatorStandard (Branch 1)
Concatenates data frames from the previous 2 tasks

- #### ElasticBulkTransformerAttachmentStandard (Branch 1)
Creates a file containing all the Attachment records to be ingested into ElasticSearch

- #### ElasticBulkWriterAttachmentStandard (Branch 1)
Uses the file created by ElasticBulkTransformer to ingest Attachment records into Elasticsearch 

- #### FormatVoiceFileStandard (Branch 1)
Adds the prefix 'voiceFile.' into the Attachment columns so that it can be integrated into the
Call data frame.

- #### XmlBatchCsvDownloaderParserStandard (Branch 1)
Task which downloads all the xml metadata files, parses them, and returns a data frame with the
parsed columns

- #### PrimaryTransformationsStandard (Branch 1)
Creates all the Call fields other than the voiceFile fields (which come directly from the attachment)

- #### CallConcatenatorStandard (Branch 1)
Frame Concatenator which concatenates the voiceFile data from FormatVoiceFileStandard with the rest
of the Call data from PrimaryTransformations

- #### HasAttachmentStandard (Branch 1)
Checks if voiceFile.fileName is not null and populates hasAttachment and connected accordingly

- #### PreMetaCallFrameConcatenatorStandard (Branch 1)
Concatenates data frames from the previous 2 tasks

- #### AssignCallMetaStandard (Branch 1)
Adds meta fields for the Call model

- #### FinalCallFrameConcatenatorStandard (Branch 1)
Concatenates the Call meta fields with the Call (and attachment) fields

- #### ElasticBulkTransformerStandard (Branch 1)
Creates a file containing all the Call records to be ingested into ElasticSearch

- #### ElasticBulkWriterStandard (Branch 1)
Uses the file created by ElasticBulkTransformer to ingest Call records into Elasticsearch 

- #### XmlBatchCsvDownloaderParserMetadataOnly (Branch 2)
Task which downloads all the xml metadata files, parses them, and returns a data frame with the
parsed columns for cases where ONLY the metadata file is present in the batch CSV.

- #### FetchVoiceFilesFromElastic (Branch 2)
For a call recording file, this task checks the Attachment index to find if there is an
Attachment record with the same voice file location key as the 'other' file key in the source frame.
If there is, return the voice file fields in the output data frame with non-null values.
If there isn't, set the same voice file fields with null values.

- #### PrimaryTransformationsMetadataOnly (Branch 2)
Creates all the Call fields other than the voiceFile fields (which come directly from the attachment)

- #### CallConcatenatorMetadataOnly (Branch 2)
Frame Concatenator which concatenates the voiceFile data from FormatVoiceFileStandard with the rest
of the Call data from PrimaryTransformations

- #### HasAttachmentElasticVoiceFiles (Branch 2)
Checks if voiceFile.fileName is not null and populates hasAttachment and connected accordingly

- #### PreMetaCallFrameConcatenatorMetadataOnly (Branch 2)
Concatenates data frames from the previous 2 tasks

- #### AssignCallMetaMetadataOnly (Branch 2)
Adds meta fields for the Call model

- #### FinalCallFrameConcatenatorMetadataOnly (Branch 2)
Concatenates the Call meta fields with the Call (and attachment) fields

- #### ElasticBulkTransformerCallMetadataOnly (Branch 2)
Creates a file containing all the Call records to be ingested into ElasticSearch

- #### ElasticBulkTransformerCallMetadataOnly (Branch 2)
Uses the file created by ElasticBulkTransformer to ingest Call records into Elasticsearch

- #### S3AttachmentMetadataRecordingsOnly (Branch 3)
Gets the S3 Head object metadata for the recording files (file info, location, size etc)

- #### AttachmentStaticRecordingsOnly (Branch 3)
Used to populate the meta model field for Attachment records

- #### AttachmentConcatenatorRecordingsOnly (Branch 3)
Concatenates the data frames from the previous 2 tasks

- #### AssignAttachmentMetaRecordingsOnly (Branch 3)
Adds meta fields for the Attachment model

- #### AttachmentWithMetaConcatenatorRecordingsOnly (Branch 3)
Concatenates data frames from the previous 2 tasks

- #### ElasticBulkTransformerAttachmentRecordingsOnly (Branch 3)
Creates a file containing all the Attachment records to be ingested into ElasticSearch

- #### ElasticBulkWriterAttachmentRecordingsOnly (Branch 3)
Uses the file created by ElasticBulkTransformer to ingest Attachment records into Elasticsearch 

- #### FormatVoiceFileRecordingsOnly (Branch 3)
Adds the prefix 'voiceFile.' into the Attachment columns so that it can be integrated into the
Call data frame.

- #### FetchCallSourceConcatenatorRecordingsOnly (Branch 3)
Concatenates the Voice File recordings with the file urls and other file urls/keys from `RecordingsOnly`

- #### FetchCallRecordsFromElastic (Branch 3)
For attachment/recording files, this task checks the Call index to find if there is a
Call record with the same sourceKey as the 'other' file url in the source frame.
If there is, it fetches the complete Call record (from a scroll) and puts it in the
output data frame. It adds the appropriate voice File details (if not already present)
in the output df, and sets the columns 'hasAttachment' and 'connected' to True.

- #### ElasticBulkTransformerUpdateCalls (Branch 3)
Creates a file containing EXISTING Call records, UPDATED with the voice file details from attachments
in the current batch

- #### ElasticBulkWriterUpdateCalls (Branch 3)
Uses the file created by ElasticBulkTransformer to UPDATE existing Call records with the voice file
details from attachments in the current batch