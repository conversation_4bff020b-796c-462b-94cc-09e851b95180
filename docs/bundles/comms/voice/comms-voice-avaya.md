# Avaya Voice Flow

Initially built for Trium, this flow ingests Avaya Call records into Elasticsearch.
The input file is a tar.gz file which contains both the metadata (html) and recording
(.opus) files. 

### Specs
[Avaya Call Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2265284757/Call+Feed+-+Avaya)

### Input File
tar.gz file containing html metadata file and opus recording files. The HTML file is parsed to
get all the required fields. The name of the recording file is embedded in a button element
in the html file.

## Output
`Call` and `Attachment` records in ElasticSearch. 

## Tasks
1. #### ParametersFlowController
This flow can be executed for both a local file or one on S3

2. #### S3DownloadFile
Download CSV file from S3

3. #### LocalFile
Read local CSV file

4. #### Unzip
Used to unzip the source tar.gz file. The extracted files are stored in a temp directory

5. #### S3FileListFromExtractPathResultList
Create an 'S3 File' list from the temp results list. After this, we have the recordings in
the right format to upload to S3.

5. #### S3UploadRecordings
Actually uploads the recordings to S3.

6. #### AttachmentFromS3Metadata
Creates Attachment model fields from the S3 recording files

7. #### AttachmentStatic
Used to populate the meta model field for assigning meta fields to the Attachment records

8. #### AttachmentConcatenator
Combine data frames from 6. and 7.

9. #### AssignAttachmentMeta
Adds meta fields for the Attachment model

10. #### AttachmentWithMetaConcatenator
Concatenates data frames from 8. and 9.

11. #### ElasticBulkTransformerAttachment
Creates a file containing all the Attachment records to be ingested into ElasticSearch

12. ### PutAttachment
Uses the file created by ElasticBulkTransformer to ingest Attachment records into Elasticsearch 

13. #### ExtractPathResultFromExtractPathResultList
Extracts the html metadata file from the unzipped files so that it can be used in the html parsing task

14. #### PrimaryTransformations
Creates all the Call fields other than the voiceFile fields (which come directly from the attachment)

15. #### AssignCallMeta
Adds meta fields for the Call model

16. #### FormatVoiceFile
Adds the prefix 'voiceFile.' into the Attachment columns so that it can be integrated into the
Call data frame.

17. #### FinalFrameConcatenator
Concatenates the Call meta fields with the Call data fields and the Attachment (voice file) fields

18. #### ElasticBulkTransformerCall
Creates a file containing all the Call records to be ingested into ElasticSearch

19. ### PutCall
Uses the file created by ElasticBulkTransformer to ingest Call records into Elasticsearch 
