# 8x8 Voice API Flow

This flow fetches 8x8 Calls metadata and call recordings from 8x8 API.

### Specs
[8x8 Call Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2333179951/Call+8x8)

### Input
flow arguments `tenant_realm` (tenant bucket) and `config_s3_key` (s3 location of config.json file)

## Output
Uploads json `metadata` files in (ingress/raw/comms/voice/8x8/metadata) and mp3 `recording` files in (ingress/raw/comms/voice/8x8/recordings) on S3. 
Also uploads the batch csv file containing the links for s3 metadata files and s3 recording file on `flow/comms-voice-8x8-feed` on S3.

## Tasks
1. #### FlowArgsValidator
Validates the flow argument sent by EventBridge rule.

2. #### GetConfig
Fetch config.json file content and mapped the values on APIConfig object.

3. #### BearerToken
Create the Voice8x8 object and generate the bearer token for API call.

4. #### GetCallMetadata
It fetches all the metadata from 8x8 API.

5. #### FilterCalls
It filters the new calls by checking existing calls on ES.

6. #### GetCallRecordings
Fetch the call recordings from 8x8 API against each call id

7. #### SplitCallMetadata
Split the list of session into individual and return the list of S3Files

8. #### CreateBatchCSV
Creates the batch CSV file containing the links of metafile and recording file for S3

9. #### S3UploadCallMetadata
Uploads the metadata files on s3

10. #### S3UploadRecordings
Uploads the recording files on s3

11. #### S3UploadBatchCSV
Uploads the batch csv file on s3 which triggers the feed flow
