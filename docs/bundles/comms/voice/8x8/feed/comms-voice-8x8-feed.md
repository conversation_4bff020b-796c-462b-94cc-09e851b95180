# 8x8 Voice Feed Flow

This flow ingests 8x8 Call records into Elasticsearch.
The input file is a csv file which contains both the metadata and recording
files path. 

### Specs
[8x8 Call Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2333179951/Call+8x8)

### Input File
csv file containing json metadata and mp3 recording files path. 

## Output
`Call` and `Attachment` records in ElasticSearch. 

## Tasks
1. #### ParametersFlowController
This flow can be executed for both a local file or one on S3

2. #### S3DownloadFile
Download CSV file from S3

3. #### LocalFile
Read local CSV file

4. #### CsvFileSplitter
Split CSV file in Batches if the input file exceeds a parametrized threshold

5. #### BatchProducer
Reads each CSV chunk and produces Batches of DataFrames to downstream tasks

6. #### JsonBatchCsvDownload
Read the metadata json file and converted it into dataframe

7. #### TransformNestedColumns
Transform dataframe column only for nested json attributes

8. #### AttachmentFromS3Metadata
Creates Attachment model fields from the S3 recording files

9. #### AttachmentStatic
Used to populate the `__meta_model__` field for assigning meta fields to the Attachment records

10. #### AttachmentConcatenator
Combine data frames from 8. (AttachmentFromS3Metadata) and 9. (AttachmentStatic)

11. #### AssignAttachmentMeta
Adds meta fields for the Attachment model

12. #### AttachmentWithMetaConcatenator
Concatenates data frames from 10. and 11.

13. #### ElasticBulkTransformerAttachment
Creates a file containing all the Attachment records to be ingested into ElasticSearch

14. ### PutAttachment
Uses the file created by ElasticBulkTransformer to ingest Attachment records into Elasticsearch 

15. #### AddVoiceFilePrefix
Adds the prefix 'voiceFile.' into the Attachment columns so that it can be integrated into the
Call data frame.

16. #### PrimaryTransformations
Creates all the Call fields other than the voiceFile fields (which come directly from the attachment)

17. #### AssignCallMeta
Adds meta fields for the Call model

18. #### FinalFrameConcatenator
Concatenates the Call meta fields with the Call data fields and the Attachment (voice file) fields

19. #### ElasticBulkTransformer
Creates a file containing all the Call records to be ingested into ElasticSearch

20. ### PutCall
Uses the file created by ElasticBulkTransformer to ingest Call records into Elasticsearch 
