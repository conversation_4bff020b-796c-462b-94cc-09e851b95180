# TR Feed Flow: UnaVista

Specs: [RTS22 UnaVista Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2703294465/RTS22+UnaVista+Handler)

This flow is triggered by a CSV file dropping in s3 `flows/tr-feed-unavista` path.

It expects Report files in CSV format:

## Input
`.csv` file

## OutPut
`RTS22TransaFction` records in ElasticSearch

## Tasks

1. ### ParametersFlowController
This flow can be executed for both a local file or one on S3

2. ### S3DownloadFile
Download CSV file from S3

3. ### LocalFile
Read local CSV file

4. ### CsvFileSplitter
Split CSV file in Batches if the input file exceeds a parametrized threshold

5. ### BatchProducer
Reads each CSV chunk and produces Batches of DataFrames to downstream tasks

6. ### UnaVistaTransformations
Performs the initial mapping for the RTS22Transaction fields that are sourced from the original file

7. ### LinkParties
Links the Party Identifiers to existing records

8. ### LinkInstrument
Links the Instrument Identifiers to existing records

9. ### InstrumentFallback
Instrument details are populated for the instruments that are not found/linked on SRP.

10. ### EligibilityAssessor
Assesses whether a transaction is reportable

11. ### AuxiliaryFrameConcatenator
Concatenates all columns from the `UnaVistaBaerTransformations`, `LinkParties`, `InstrumentOverridesExpiryDate` and `EligibilityAssessor` tasks

12. ### AssignMeta
Creates the meta fields of the records after being validated

13. ### WorkflowStatus
Assigns a status of either `PASSED` or `FAILED` according to the presence of validation errors.

15. ### FinalConcatenator
Concatenates all columns from `AuxiliaryFrameConcatenator`, `AssignMeta` and `WorkflowStatus`

16. ### ElasticBulkTransformer
Converts the records to be ingested into ElasticSearch

17. ### PutIfAbsent
Writes the records into ElasticSearch

18. ### QuarantineCondition
Assesses if any record should be quarantined

19. ### QuarantinedElasticBulkTransformer
Converts the quarantined records to be ingested into ElasticSearch

20. ### QuarantinedPutIfAbsent
Writes the quarantined records into ElasticSearch

21. ### Noop
Dummy task, used when there is no need to quarantine.
