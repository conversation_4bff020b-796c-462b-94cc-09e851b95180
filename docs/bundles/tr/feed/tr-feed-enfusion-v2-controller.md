# TR Feed Enfusion V2 Controller

Specs: [RTS22 Enfusion V2 Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2063139091/RTS22+Enfusion+V2)

This controller flow was created to contextually merge the Allocations file data to the Execution file data
and then trigger an individual AWS Batch job for the processor flow for each of the file chunks. 
(Input data is chunked into batch post merge)

This flow is triggered only when both the `Executions` and `Allocations` file is present in the S3 bucket.

## I/O

The TR Feed Enfusion V2 Controller will execute when the client drops a file in the 
`flows/tr-feed-enfusion-v2-controller` S3 location. Post-processing the resultant chunked files 
will be uploaded to `tr-feed-enfusion-v2-processor`.

## Tasks

- ### MultipleFilesInputController
Checks the S3 location of the incoming file for required file patterns and then creates a 
CSV containing the S3 locations of the files mentioned in the task parameters.

- ### S3DownloadMultipleFiles
Task which reads the files in S3 *(asynchronously)* and then collates it in a dict to be used in downstream tasks.

- ### EnfusionTransactionFrameTransformer
Task specific to the TR Enfusion V2 flow which creates the frame used by downstream tasks. 
We contextually merge the Allocations file data to the Execution file data in this task.
To preserve the 1:1 mapping we take in the first record of each `Order Id` group in the `Allocations` file.
This task also creates a temp column pointing towards the executions file URL which is 
later used as the `sourceKey` for the record. 

- ### CsvFileSplitter
Split CSV file in Batches if the input file exceeds a parametrized threshold.

- ### S3FileListFileSplitterResultList
Auxiliary task which creates a list of S3Files Object from a list of FileSplitterResult 
object produced upstream in CsvFileSplitter. The result is used downstream by S3UploadFile task.

- ### S3UploadFile
Upload the batches to the specific S3 location, thereby triggering the Processor flow.

