# TR Feed Flow: LME

Specs: [RTS22 LME Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2709159950/RTS22+LME)

This flow is triggered when the controller flow drops a CSV file in s3 `flows/tr-feed-lme-processor` path.

It expects Report files in CSV format.

## Input
`.csv` file

## OutPut
`RTS22Transaction` records in ElasticSearch

## Tasks

- ### ParametersFlowController
This flow can be executed for both a local file or one on S3

- ### S3DownloadFile
Download CSV file from S3

- ### LocalFile
Read local CSV file

- ### CsvFileSplitter
Split CSV file in Batches if the input file exceeds a parametrized threshold

- ### BatchProducer
Reads each CSV chunk and produces Batches of DataFrames to downstream tasks

- ### LmeTransformations
Performs the initial mapping for the RTS22Transaction fields that are sourced from the original file

- ### LinkParties
Links the Party Identifiers to existing records

- ### LinkInstrument
Links the Instrument Identifiers to existing records

- ### MapNestedFromInstrument
Maps nested fields in the linked instrument data

- ### MapCommodityDerivativeIndicator
Maps commodity derivative indicator from instrument classification

- ### EligibilityAssessor
Assesses whether a transaction is reportable

- ### AuxiliaryFrameConcatenator
Concatenates all columns from the `LmeTransformations`, 
`LinkParties`, `EligibilityAssessor` and `LinkInstruments` tasks

- ### AssignMeta
Creates the meta fields of the records after being validated

- ### WorkflowStatus
Assigns a status of either `PASSED` or `FAILED` according to the presence of validation errors.

- ### FinalConcatenator
Concatenates all columns from `AuxiliaryFrameConcatenator`, `AssignMeta` and `WorkflowStatus`

- ### ElasticBulkTransformer
Converts the records to be ingested into ElasticSearch

- ### PutIfAbsent
Writes the records into ElasticSearch

- ### QuarantineCondition
Assesses if any record should be quarantined

- ### QuarantinedElasticBulkTransformer
Converts the quarantined records to be ingested into ElasticSearch

- ### QuarantinedPutIfAbsent
Writes the quarantined records into ElasticSearch

- ### Noop
Dummy task, used when there is no need to quarantine.
