# TR Feed Flow: XML to Unavista Converter

Jira page: https://steeleye.atlassian.net/browse/ON-3305

This flow is triggered by a CSV file dropping in s3 `flows/tr-feed-xml-to-unavista-converter/` path.

This is an intermediary flow, that processes the input xml and drops it to be handled by the `tr-feed-unavista` feed.

It expects Report files in XML format:

## Input
`.xml` file

## Tasks

1. ### ParametersFlowController
This flow can be executed for both a local file or one on S3

2. ### S3DownloadFile
Download CSV file from S3

3. ### LocalFile
Read local CSV file

3. ### XMLFileSplitter
Parse single XML file extracted from the multiple embedded XML documents of the .log file

4. ### XmlToUnavistaConverter
Convert input xml to `tr-feed-unavista` format, and `.csv` file.

5. ### S3UploadFile
Upload a single output file to s3, ready to be processed by the `tr-feed-unavista`  handler.