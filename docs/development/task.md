# Swarm Task

This document describes how to create a new task in Swarm tasks

## Task place
 TODO
 
## Task templates

### Transform task 

Params/Resources classes are not mandatory. If they are not needed, please remove
the task class attributes `params_class`/`resources_class`

```python
import pandas as pd

from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

class Params(BaseParams):
    foo: str
    target_column: str

class Resources(BaseResources):
    elastic_key: str

class NewTask(TransformBaseTask):
    params_class = Params
    resources_class = Resources
    
    def execute(
        self, source_frame: pd.DataFrame = None, params: Params=None, resources: Resources=None
    ) -> pd.DataFrame:
        target = pd.DataFrame(index=source_frame.index)

        target[params.target_column] = pd.NA
        
        return target
```

To use this task in the bundle, make sure the bundle `image` version includes that task. Then the config
should be something like:

```yaml
- path: swarm_tasks.path.to.new.task.new_task.py:NewTask
  name: NewTaskNameToReferenceInOtherTasks
  params:
    foo: bar
    target_column: target_column_name
  resources:
    es_key: tenant-data # this should align with the infra resources names
  upstreamTasks:
  - taskName: TaskName # name of the task executed upstream
    mapped: false # or true if mapped
    key: result # this will correspond to the run argument name in the NewTask
```

More than one upstream task can be applied (if required to be executed previous to NewTask).