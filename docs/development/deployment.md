# Swarm Deployments

## Swarm-sdk / swarm-tasks

For swarm-sdk and swarm-tasks, a release should be done and approved 
when it is a valid candidate to be deployed into production. However it is possible 
to have intermediary deployments to test changes in development environments.

Each deployment should be done according to the tag formats [here](#tags). Also, the version 
of the package as well versions of dependencies should be updated in the `pyproject.toml`, 
followed by running `poetry update` and subsequently commit of the `poetry.lock` changes and just then
create the tag. 

> NOTE: the changes in `poetry.lock` have to be committed, otherwise they are not considered in the tag.


#### Production

There are 2 types of deployments that can go to production:

- `release`: main release deployment and should be made from a `/release` branch
- `post-release`: an hot-fix deployment and should be made from a `/hotfix` branch

#### Development

There are 2 types of deployments that can go to development:
 
- `pull-request`: this deployment should be made from `feature/` or `/bugfix` branch and it may happen when a change
needs also to be tested in a development environment.
- `release-candidate`: this deployment should be made from a `/release` branch.

### Tags

A tag is formatted with 2 parts like `v<main version>-<suffix>`, where:

- `main`: it is mandatory and reflects the release version
- `suffix`: it is optional and reflects the nature of the deployment

In terms of the main part, swarm-sdk and swarm-tasks tag format follow a different pattern 
(`<d>` stands for digits):

- `swarm-sdk tag format`: `v<d>.<d>.<d>`, corresponding to `<major>.<minor>.<patch>`. Example: `v3.0.2`.
- `swarm-tasks tag format`: `v<d>.<d>.<d>`, corresponding to `<major>.<minor>.<patch>`. Example: `v3.0.2`

> Every tag should be prefixed with a `v`, otherwise the build will not happen.

> Every release, if the major or minors are updated, the patch version should be 0.

In terms of the suffix part, they can have the following formats (examples considering `swarm-tasks`):

- `post.<d>` this suffix corresponds to a post release (hot-fix) deployment. Example: `v3.0.2.1-post.1`.
- `dev.<PR number><\d>`: this suffix corresponds to a pull request deployment. Example: `v3.0.2.1-dev.451`, 
which corresponds to a dev deployment of PR#45 version 1.
- `rc.<d>`: this suffix corresponds to a release candidate deployment. Example: `v3.0.2.1-rc.1`.

> The tags should be done in the correspondent updated branch using `git tag <tag>`, followed by a 
`git push --tags`.


### Making a release

In order to make a new release in swarm-sdk & swarm-tasks, the next steps should be followed:

1. Create a `/release` branch (with the version) from `master`. Example: `release/v3.0.2`.
2. Change the version of the package in the `pyproject.toml` and run `poetry update` and commit those changes.
3. Update the `CHANGELOG.md`
   1. first run `python update_changelog.py preview` to make sure the changes are correctly formatted.
   2. run `python update_changelog.py generate <tag>` to update the `CHANGELOG.md` with the changes in the release.
   Example: `python update_changelog.py generate v3.0.2`
   3. commit the changes (which should correspond the deletion of the PR yaml files under `/changes` and 
   the update of `CHANGELOG.md`).
4. Push the commits and raise a release PR (the name should be `Release/<version>`. Example: `Release/v3.0.2`.
5. Replace the PR template in the PR description by the new changes in the `CHANGELOG.md` (suggestion: 
go to the PR Files and copy the diff in the `CHANGELOG.md`).
6. After the PR is approved and built successfully in the circleci, MERGE it into `master` (without squash). 
**NOTE: this has to be merge only and not squashed, otherwise we will get conflicts.**
7. Do git tag in master (updated with the merge) for that release (following guidelines [here](#tags)). 
   
> Squash and Merge should be only done when merging from a `feature/`, `bugfix/` or `hotfix/` branches. 

### Making hot-fixes

To make a hot-fix, the steps below should be followed:

1. Create a `/hotfix` branch from `master`.
2. Update the version in `pyproject.toml` with the suffix `.post` (check [tags](#tags)) and run `poetry update`.
Also do Step 3 in [Making a release](#making-a-release).
3. Apply the fixes and raise a PR against `master`.
4. After approval, squash and merge it into `master`.
5. Do a git tag in master (updated with the merge) for that release (following guidelines for hot-fixes [here](#tags)).


## Swarm-Flows deployment

When we deploy a bundle/flow we publish it to either of the below indices in elastic.

## Flows index
This is the index where bundles live that is tenant and environment specific. The `uid` of this
index is the `flow_id` which consists of <<realm:bundle_id>> and is used by the `flow_router` in swarm-sdk
 to get the environment, tenant and flow.
E.G. `flow_id=axis.dev.steeleye.co:tr-universal-steeleye-trade-blotter`

## Bundles index
This is where the bundles live. It is agnostic of tenant and environment. Each bundle's `uid` is the 
id of the bundle. We do not merge previous version's of bundles when publishing a new bundle.
Each bundle ever published is searchable with it's `version` and `id`. 
See the [here](#publish-existing-bundle-to-dev--uat-flow-registry) to publish a specific bundle version
to flows for tenants.


### Bundle & Flow Lifecycle
To publish bundles & flows to the registry please follow the steps below.

- **NOTE**: If you wish to publish anything to the `prod` environment please follow the release-management workflow.
        RM Process [here](https://steeleye.atlassian.net/wiki/spaces/EN/pages/**********/Release+Management+Process)
            
#### Publish Local Bundle to Bundle & Flow Registry for Dev / Uat
- ensure you are on a branch with your git account
- make any local changes to the bundle
- fill out manifest in `swarm/flows/tools/train/manifests` Example: `swarm_flows/tools/train/manifests/EXAMPLE.yaml` 
- leave `version`  blank to publish your local bundle
- `swarm_tasks: version` is required when no bundle version is provided
- if you only wish to publish to the bundle registry, leave tenants blank, otherwise fill in the tenants and the env
    you wish to publish to in flows. See [here](#publish-existing-bundle-to-dev--uat-flow-registry) for more information.
- run the `run_train.py` script
- **NOTE**: make sure you are tunneling into `platform` to access the registry
- **NOTE**: train returns `duplicate` when no material differences detected between composed flow and live, stored flow
- **NOTE**: if it is a new bundle or flow you will be prompted to verify this is what you want

#### Publish Existing Bundle to Dev / Uat Flow Registry
- ensure you are on a branch with your git account
- If you want the latest version of the bundle please use `swarm_flows/tools/360/live_flows.py` and search it's csv
    file output (same directory)
- fill in the `run train manifest` with the following:
    1. `environment` -> the env you want the flow published to
    2. `tenants` -> tenants attached to the bundle (Needed to create a flow)
    3. `updates: bundles: id` -> the bundle id's you wish to update.
    4. `updates: bundles: version` with the version you want promoted to the flow registry
    
    Specify `bundle: all_tenants: true` for a bundle if you wish to publish for all flows in the env specified that 
    have the bundle id, otherwise `bundle: tenants:` with the individual tenants.
        
    Please see `swarm_flows/tools/train/manifests/EXAMPLE.yaml` for an example manifest.
    
- run the `run_train.py` script
- **NOTE**: make sure you are tunneling into `platform` to access the registry
- *NOTE*: train returns `duplicate` when no material differences detected between composed flow and live, stored flow
 
### Publish Batch Image
- Create a Run Configuration
    - To create a batch image in run_train you will need the AWS env-vars set up
    - Add env var for `AWS_PROFILE`, set it to `prod` (only used when publishing to prod)
    - for dev and uat please set the env var to `nonprod`. In a terminal window:
    `export AWS_PROFILE=nonprod` and then run `infra-tools/scripts/pythonaws_credentials.py` with python
    - `run_train` will check for the `image` version of the bundle specified across all stacks for the environment set, 
    if it is not present it will publish the batch image. 
    - You can input the version of the image you wish to publish in the manifest when publishing a local bundle.
    It will write it into you local bundle and publish the updated bundle with it. This will not work when you specify 
        a bundle version as it then it is taken directly from the bundle index.
    swarm_tasks:
      version: 3.5.12-dev.999
  

    **NOTE** Please ensure you have the correct env vars set up to avoid publishing dev image names into prod
