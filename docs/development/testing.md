# Testing

## Python packages

Any changes on swarm-sdk and swarm-tasks should going with the correspondent unittests (if applied).

### Test modules/classes organisation

In swarm tasks, each task should have the correspondent test module, living 
under [`swarm_tasks/tests`](https://github.com/swarmhub/swarm-tasks/tree/develop/tests), 
following the same tree design as in `swarm_tasks` package.

In that module, there should exist a test class for:
- **Task class**: these should have the tests focus on the `execute` and the other 
methods implemented in the task

- **Params/Resources classes (if any)**: Although the Params/Resources classes are pydantic models 
(so we benefit of the validations), we should have a test class for each to test the possible inputs 
to pass to those classes and also to test the raise of errors thrown by the pydantic `@root_validators` 
(if any implemented).

### Transform tasks tests

#### Input Data
In order to test a transform task (which receives a `pd.DataFrame` as `source_frame` attribute)
we should always have tests to test the following generic scenarios:

- The `source_frame` is an empty dataframe where the columns used in the
task are NOT present.
- The `source_frame` is an empty dataframe where the columns used in the
task are present.
- The `source_frame` is a non empty dataframe where some of the values in the columns
used in the task are null.

Those scenarios cover the most common use cases that should handle properly to prevent
the tasks to fail.

#### Transform Base Tasks (to be deprecated)

Tasks that extend TransformBase should be unit tested using the standard test harness (`tests/transform/test_harness.py`). 
The test harness recursively walks the specs directory (`tests/transform/specs`). 
For each yaml spec file within that directory, the harness will execute all 
tests defined within it. The primary aim of this approach is to shorten the 
path to coverage by allowing the task author to focus solely on data inputs 
and outputs alongside permutations of task params. 
