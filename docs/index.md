# Swarm SDK

This library provides an abstraction around the swarm system and underlying ElasticSearch datastore.

## Installation

Add swarm with poetry::

```shell

poetry add swarm

```
    


## Initialisation

The Swarm client needs to be initialised to access Elasticsearch and the kafka flows. Here is an
example::

```python
from elasticsearch import Elasticsearch
from swarm.client.record_handler import RecordHandler

es_client = Elasticsearch(["127.0.0.1"], port=9200)
record_handler = RecordHandler(client=es_client, tenant="foobar")
```



## Primer


## Working with records
--------------------

Swarm supports full CRUD operations to ES. Records are ``dict``, stored in an alias (ElasticSearch named index).

Example of saving a record:

```python
doc = {
   "foo": "bar"
}
try:
   resp = record_handler.create(
       doc=doc,
       alias="foostore",
       model="Foo"
   )
   my_id = resp.id
except RecordHandlerError:
   log.error("argh!")
```

Records can either be retrieved one by one, or searched as a collection.

Example of retrieving a specific record:

```python
doc = record_handler.get(index="foobar", model="Foo", doc_id="doc-123")
```

Updating a record:

```python
try:
  record_handler.update(doc=doc, alias="foostore", model="Foo")
except RecordHandlerError:
  log.error("Failed to update the doc!")
```

Deleting a record:
```python
try:
  record_handler.delete(alias="foostore", model="Foo", doc_id=doc.id)
except RecordHandlerError:
  log.error("Failed to delete the document!")
```