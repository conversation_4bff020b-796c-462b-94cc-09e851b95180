Getting Started
===============

> NOTE: this documentation should be moved into the [SteelEye handbook](https://github.com/steeleye/handbook).

The scope of Swarm (from a code perspective) is covered on 3 repositories:

- [swarm-sdk](https://github.com/swarmhub/swarm-sdk) - It is a python package where all the base code/abstractions live. 
It relies on some of the Prefect classes in terms of flow execution/tasks, 
in addition to the base classes to defines the params and resources for each 
task and the workflow to build a flow from a bundle.

- [swarm-tasks](https://github.com/swarmhub/swarm-tasks) - It is a python package/docker image, where all the tasks 
used in the bundles live. The majority of the tasks are self-independent 
(don’t depend on other tasks). 

- [swarm-flows](https://github.com/steeleye/swarm-flows) - This is where the bundles and the flows live. The bundles 
and flows are merely yaml files, so no code. The only code that exists is in the `/tools` folder. 
- A bundle is tenant & env agnostic and they are stored in `swarm_flows/bundles`.
- Where as a flow is when a bundle has an environment and tenant attached to it.
The tenant and env are taken from an environment var -> `SWARM_LOCAL_FLOW_ID` or on the cloud `FLOW_ID`.

You can think of it like a bundle is a static set of instructions, but when a bundle is run it becomes a flow
as it is attached to a tenant and an environment. They both live in separate indices

The `/tools` directory includes some scripts to help achieve the following:
> run a flow locally -> [docs](./development/setup.md), 
> update a bundle/ flow in the registry -> [docs](./development/deployment.md)

#### Dependencies

For the main python packages, the dependency is simply
```
swarm-sdk -> swarm-tasks
```

In terms of the bundle, it is linked to a specific swarm-tasks image (which is deployed on Amazon ECR 
on a tag of swarm-tasks). For more information about swarm deployments please check [here](./development/deployment.md).