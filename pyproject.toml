[tool.poetry]
name = "swarm-flows"
version = "1.0.0"
description = "Repository for Swarm Flow configurations and test harness"
authors = ["<PERSON> <<EMAIL>>"]

[[tool.poetry.source]]
name = "steeleye-pypi"
url = "https://steeleye.jfrog.io/steeleye/api/pypi/pypi-local/simple/"
priority = "supplemental"

[[tool.poetry.source]]
name = "PyPI"
priority = "primary"

[tool.poetry.dependencies]
click = "^8.0"
GitPython = "^3.1.1"
python = ">=3.9.0,<3.10.0"
pyyaml = "^6.0"
swarm = {path = "../swarm-sdk"}
swarm-tasks = {path = "../swarm-tasks"}
lxml = "^4.5.2"
pre-commit = "^2.8.2"
openpyxl = "^3.0.6"
deepdiff = "^6.3.0"
PyQt5 = "^5.15.9"
rich = "^13.7.1"
typer = "^0.12.3"

[tool.poetry.dev-dependencies]
pytest = "^7.0"
prefect = {extras = ["viz"], version = "0.14.16"}
# docs
mkdocs-material = "^7.3.3"
mkdocstrings = "^0.16.2"
mkdocs-mermaid2-plugin = "^0.5.2"
mkdocs-section-index = "^0.3.2"
mkdocs-git-revision-date-localized-plugin = "^0.10.0"
ansicolors = "1.1.8"


[tool.poetry.scripts]
live_flows = "swarm_flows.tools.360.live_flows:export_live_flows"
run_flow = "swarm_flows.tools.run_flow.run_flow:run_flow"
train = "swarm_flows.tools.train.run_train:run_train"

[build-system]
requires = ["poetry>=0.12"]
build-backend = "poetry.masonry.api"

