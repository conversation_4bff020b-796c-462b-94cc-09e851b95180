[tool.poetry]
name = "swarm-tasks"
version = "6.3.2"
description = "This project will hold Task definitions made to work with the swarmhub sdk."
authors = [
"<PERSON> <<EMAIL>>",
"<PERSON><PERSON><PERSON> <fabio.ram<PERSON><EMAIL>>",
"<PERSON> <<EMAIL>>",
"<PERSON> <<EMAIL>>",
"Puneeth R <<EMAIL>>",
"<PERSON> <<EMAIL>>",
"<PERSON><PERSON> <<EMAIL>>"]
license = "Apache-2.0"
readme = "README.md"
packages = [{ include = "swarm_tasks" }]
include = [
    "swarm_tasks/transform/steeleye/*",
]

[tool.poetry.dependencies]
python = "^3.9.2"

# please be aware that changing pandas version will affect success of some tests. This version is one compatible between
# the market-data-sdk version and the success of tests.
pandas = "~=1.1.0"
numpy = "<1.20" # TODO: this constraint should happen where pandas is a dependency
xmltodict = "^0.13.0"
xlrd = "^1.2.0"
smart_open = "~6.3.0"
filetype = "^1.0.7"
pydub = "^0.24.1"

# steeleye internal
e164-converter = "0.0.8"
se-schema = "5.22.18"
swarm = "6.0.1"
se-elasticsearch = "^3.1.8"
se-trades-tasks = "4.7.7"
se-core-tasks = "4.4.0"
se-elastic-schema = "2.0.0"
pyarrow = ">=14.0.1"

[tool.poetry.dev-dependencies]
asynctest = "^0.13.0"
pre-commit = "==2.17.0"
py = "^1.0.0"
pytest = "^7.0"
pytest-cov = "^3.0.0"
pytest-asyncio = "^0.19"
pytest-html = "^2.1.1"
pytest-lazy-fixture="^0.6.3"
pytest-mock = "^3.2.0"
ElasticMock = "^1.8.0"
elasticsearch = "~7.17.0"
Faker = "^8.7.0"
mock = "^4.0.3"
requests-mock = "1.9.3"
freezegun = "^1.2.2"

# docs
mkdocs-material = "^7.3.3"
mkdocstrings = "^0.18.0"
mkdocs-mermaid2-plugin = "^0.5.2"
mkdocs-section-index = "^0.3.2"
mkdocs-git-revision-date-localized-plugin = "^0.10.0"

[tool.poetry.scripts]
flow_runner = "swarm.flow.runner:main"

[[tool.poetry.source]]
name = "steeleye-pypi"
url = "https://steeleye.jfrog.io/steeleye/api/pypi/pypi-local/simple/"
priority = "supplemental"

[[tool.poetry.source]]
name = "PyPI"
priority = "primary"

[build-system]
build-backend = "poetry.masonry.api"
requires = ["poetry>=1.1.4", "pip>=20.3.3"]
